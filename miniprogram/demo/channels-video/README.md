# 微信小程序视频号功能演示与规则分析

## 📋 目录
- [功能概述](#功能概述)
- [规则分析](#规则分析)
- [技术实现](#技术实现)
- [Demo使用说明](#demo使用说明)
- [常见问题](#常见问题)

## 🎯 功能概述

微信小程序提供了两种打开视频号视频的方式：

### 1. 跳转播放（wx.openChannelsActivity）
- **无主体限制**：任何小程序都可以使用
- **用户体验**：跳转到微信视频号页面观看
- **基础库要求**：2.19.2+

### 2. 内嵌播放（channel-video组件）
- **有主体限制**：需要满足特定的主体关系
- **用户体验**：在小程序内直接播放
- **基础库要求**：2.25.1+（同主体），2.31.1+（非同主体）

## 📊 规则分析

### 主体限制详解

#### 1. 公司主体小程序播放其他账号视频的权限

**跳转播放方式**：
- ✅ **无限制**：公司主体小程序可以跳转播放任何视频号的视频
- ✅ **无需授权**：不需要获得视频号主体的授权
- ✅ **即时生效**：调用接口即可跳转

**内嵌播放方式**：
- ❌ **有严格限制**：不能随意内嵌其他账号的视频
- ✅ **同主体视频**：可以内嵌同公司主体的视频号视频
- ✅ **关联主体视频**：可以内嵌通过微信开放平台关联的主体视频
- ⚠️ **非同主体视频**：需要特殊的feed-token机制

#### 2. 同主体 vs 非同主体播放权限差异

| 对比项 | 同主体视频 | 非同主体视频 |
|--------|------------|--------------|
| **权限要求** | 同主体或关联主体 | 非个人主体小程序 + feed-token |
| **获取方式** | 直接使用finderUserName + feedId | 需要获取feed-token |
| **时效性** | 永久有效 | 24小时有效期 |
| **操作复杂度** | 简单 | 复杂（需要MP平台配置） |
| **播放体验** | 完整功能 | 完整功能 |
| **跳转支持** | 支持无弹窗跳转 | 支持无弹窗跳转 |

#### 3. 必须跳转的场景

以下情况**必须使用跳转方式**：
- 个人主体小程序播放任何非同主体视频
- 公司主体小程序播放非关联主体视频（且未获取feed-token）
- 基础库版本低于2.25.1的环境
- 需要播放纯图片类型的视频号内容

### 技术限制总结

#### 1. 基础库版本要求
```javascript
// 跳转播放
if (compareVersion(SDKVersion, '2.19.2') < 0) {
  // 不支持跳转播放
}

// 同主体内嵌播放
if (compareVersion(SDKVersion, '2.25.1') < 0) {
  // 不支持内嵌播放
}

// 非同主体内嵌播放
if (compareVersion(SDKVersion, '2.31.1') < 0) {
  // 不支持非同主体内嵌播放
}
```

#### 2. 主体关系验证
```javascript
// 主体判断逻辑
const canEmbedVideo = (miniProgramSubject, channelsSubject) => {
  // 1. 同主体
  if (miniProgramSubject === channelsSubject) {
    return true;
  }
  
  // 2. 关联主体（需要微信开放平台配置）
  if (isRelatedSubject(miniProgramSubject, channelsSubject)) {
    return true;
  }
  
  // 3. 非同主体（需要feed-token）
  if (hasFeedToken() && !isPersonalSubject(miniProgramSubject)) {
    return true;
  }
  
  return false;
};
```

#### 3. Feed-token机制限制
- **获取条件**：MP平台开启"获取视频号视频ID权限"
- **时效限制**：24小时有效期
- **操作者限制**：只有开启权限的操作者能获取token
- **主体限制**：仅非个人主体小程序可使用

## 🛠 技术实现

### 跳转播放实现
```javascript
wx.openChannelsActivity({
  finderUserName: 'video_channel_id',
  feedId: 'video_feed_id',
  success: (res) => {
    console.log('跳转成功', res);
  },
  fail: (err) => {
    console.error('跳转失败', err);
  }
});
```

### 内嵌播放实现

#### 同主体视频
```xml
<channel-video 
  finder-user-name="{{finderUserName}}"
  feed-id="{{feedId}}"
  bindload="onVideoLoad"
  binderror="onVideoError">
</channel-video>
```

#### 非同主体视频
```xml
<channel-video 
  feed-token="{{feedToken}}"
  bindload="onVideoLoad"
  binderror="onVideoError">
</channel-video>
```

## 📱 Demo使用说明

### 1. 页面结构
```
miniprogram/demo/channels-video/
├── channels-video.wxml    # 页面结构
├── channels-video.wxss    # 页面样式
├── channels-video.js      # 页面逻辑
├── channels-video.json    # 页面配置
└── README.md             # 说明文档
```

### 2. 功能演示

#### 跳转播放演示
1. 输入视频号ID（finderUserName）
2. 输入视频ID（feedId）
3. 点击"跳转播放视频"按钮
4. 观察跳转效果和错误处理

#### 内嵌播放演示
1. **同主体视频**：输入同主体的视频号ID和视频ID
2. **非同主体视频**：输入有效的feed-token
3. 观察内嵌播放效果和错误处理

### 3. 参数获取方法

#### 获取finderUserName和feedId
1. 登录[视频号助手](https://channels.weixin.qq.com)
2. 首页查看视频号ID（finderUserName）
3. "动态管理"模块复制视频ID（feedId）

#### 获取feed-token
1. MP平台开启"获取视频号视频ID权限"
2. 移动端找到目标视频
3. 复制视频的feed-token（24小时有效）

## ❓ 常见问题

### Q1: 为什么内嵌播放提示"主体不匹配"？
**A**: 检查以下几点：
- 小程序与视频号是否为同主体
- 是否通过微信开放平台建立了关联主体关系
- 非同主体视频是否使用了有效的feed-token

### Q2: feed-token过期怎么办？
**A**: feed-token有24小时有效期，过期后需要：
1. 重新在MP平台开启权限开关
2. 重新在移动端获取新的feed-token

### Q3: 个人主体小程序能否内嵌非同主体视频？
**A**: 不能。个人主体小程序只能：
- 跳转播放任何视频（无限制）
- 内嵌播放同主体视频

### Q4: 如何判断当前小程序的主体类型？
**A**: 在小程序管理后台查看：
- 小程序资料页 → 开发团队 → 查看主体信息
- 个人主体：个人开发者
- 非个人主体：公司、组织等

### Q5: 关联主体如何建立？
**A**: 通过微信开放平台：
1. 注册微信开放平台账号
2. 将小程序绑定到开放平台
3. 申请关联主体关系
4. 等待审核通过

## 🎨 UI设计规范

本Demo遵循项目UI设计规范：
- **主色调**：橙色 #FF7A45
- **辅助色**：浅灰 #F7F8FA，蓝色 #4A90E2
- **字体大小**：30rpx/32rpx/36rpx
- **圆角设计**：12rpx/16rpx
- **阴影效果**：0 4rpx 12rpx rgba(0, 0, 0, 0.05)

## 📈 最佳实践建议

1. **优先使用跳转播放**：简单可靠，无主体限制
2. **谨慎使用内嵌播放**：仅在确保主体关系的情况下使用
3. **做好错误处理**：提供友好的错误提示和降级方案
4. **版本兼容检查**：在使用前检查基础库版本
5. **用户体验优化**：根据业务场景选择合适的播放方式
