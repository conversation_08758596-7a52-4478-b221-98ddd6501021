/**
 * 微信小程序视频号演示页面
 *
 * 功能说明：
 * 1. 跳转播放：通过wx.openChannelsActivity接口跳转到视频号观看视频
 *    - 无主体限制，任何小程序都可以使用
 *    - 用户体验：离开小程序 → 进入完整视频号环境 → 可以上下滑动看其他视频 → 返回小程序
 *    - 注意力分散风险：⚠️ 用户可能被其他视频吸引，不会返回小程序
 *    - 基础库要求：2.19.2+
 *
 * 2. 内嵌播放：通过channel-video组件在小程序内播放视频
 *    - 同主体视频（基础库2.25.1+）：要求小程序与视频号为同主体或关联主体
 *    - 非同主体视频（基础库2.31.1+）：要求非个人主体小程序，需要feed-token
 *    - 用户体验：在小程序内直接播放，无法滑动查看其他视频，专注当前内容
 *    - 注意力集中：✅ 避免用户分散注意力，保持在小程序环境中
 *
 * ⚠️ 常见技术误解澄清：
 * - 错误观点：wx.openChannelsActivity可以弹出定制播放器，限制用户滑动
 * - 正确理解：wx.openChannelsActivity是完整跳转，用户可以自由滑动查看其他视频
 * - 解决注意力分散的正确方案：使用channel-video组件进行内嵌播放
 *
 * 主体限制说明：
 * - 同主体：小程序与视频号的主体完全相同
 * - 关联主体：通过微信开放平台建立关联关系
 * - 非同主体：需要通过feed-token方式，有时效性限制（24小时）
 */

Page({
  data: {
    // 跳转播放参数
    jumpParams: {
      finderUserName: "", // 视频号ID
      feedId: "", // 视频ID
    },

    // 内嵌播放参数
    embedParams: {
      // 同主体视频参数
      sameSubject: {
        finderUserName: "", // 同主体视频号ID
        feedId: "", // 同主体视频ID
        autoplay: false, // 自动播放（仅同主体支持）
        loop: false, // 循环播放
        muted: false, // 静音播放
        objectFitIndex: 0, // 显示模式索引
      },
      // 非同主体视频参数
      differentSubject: {
        feedToken: "", // 非同主体视频token
        autoplay: false, // 自动播放（非同主体强制为false）
        loop: false, // 循环播放
        muted: false, // 静音播放
        objectFitIndex: 0, // 显示模式索引
      },
    },

    // 显示模式选项
    objectFitOptions: [
      { label: "包含(contain)", value: "contain" },
      { label: "填充(fill)", value: "fill" },
      { label: "覆盖(cover)", value: "cover" },
    ],

    // 错误信息
    errorMessage: "",
  },

  /**
   * 页面加载时的处理
   */
  onLoad(options) {
    console.log("视频号演示页面加载", options);

    // 检查基础库版本
    this.checkLibraryVersion();

    // 设置示例数据（仅用于演示）
    this.setExampleData();
  },

  /**
   * 检查基础库版本兼容性
   */
  checkLibraryVersion() {
    const systemInfo = wx.getSystemInfoSync();
    const SDKVersion = systemInfo.SDKVersion;

    console.log("当前基础库版本:", SDKVersion);

    // 检查跳转播放支持（基础库2.19.2+）
    if (this.compareVersion(SDKVersion, "2.19.2") < 0) {
      this.showError(
        "当前基础库版本过低，不支持跳转播放功能，请升级基础库至2.19.2+"
      );
      return;
    }

    // 检查内嵌播放支持（基础库2.25.1+）
    if (this.compareVersion(SDKVersion, "2.25.1") < 0) {
      this.showError("当前基础库版本不支持内嵌播放功能，请升级基础库至2.25.1+");
      return;
    }

    // 检查非同主体内嵌播放支持（基础库2.31.1+）
    if (this.compareVersion(SDKVersion, "2.31.1") < 0) {
      console.warn(
        "当前基础库版本不支持非同主体内嵌播放功能，需要升级至2.31.1+"
      );
    }
  },

  /**
   * 版本号比较
   */
  compareVersion(v1, v2) {
    const v1parts = v1.split(".").map(Number);
    const v2parts = v2.split(".").map(Number);

    for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
      const v1part = v1parts[i] || 0;
      const v2part = v2parts[i] || 0;

      if (v1part > v2part) return 1;
      if (v1part < v2part) return -1;
    }
    return 0;
  },

  /**
   * 设置示例数据（实际使用时请替换为真实数据）
   */
  setExampleData() {
    this.setData({
      jumpParams: {
        finderUserName: "example_finder", // 示例视频号ID
        feedId: "example_feed_id", // 示例视频ID
      },
      embedParams: {
        sameSubject: {
          finderUserName: "same_subject_finder", // 示例同主体视频号ID
          feedId: "same_subject_feed_id", // 示例同主体视频ID
        },
        differentSubject: {
          feedToken: "", // 需要实际获取的feed-token
        },
      },
    });
  },

  /**
   * 输入事件处理
   */
  onFinderUserNameInput(e) {
    this.setData({
      "jumpParams.finderUserName": e.detail.value,
    });
  },

  onFeedIdInput(e) {
    this.setData({
      "jumpParams.feedId": e.detail.value,
    });
  },

  onSameSubjectFinderUserNameInput(e) {
    this.setData({
      "embedParams.sameSubject.finderUserName": e.detail.value,
    });
  },

  onSameSubjectFeedIdInput(e) {
    this.setData({
      "embedParams.sameSubject.feedId": e.detail.value,
    });
  },

  onFeedTokenInput(e) {
    this.setData({
      "embedParams.differentSubject.feedToken": e.detail.value,
    });
  },

  /**
   * 播放控制选项事件处理
   */
  onAutoplayChange(e) {
    this.setData({
      "embedParams.sameSubject.autoplay": e.detail.value,
    });
    console.log("自动播放设置:", e.detail.value);
  },

  onLoopChange(e) {
    this.setData({
      "embedParams.sameSubject.loop": e.detail.value,
    });
    console.log("循环播放设置:", e.detail.value);
  },

  onMutedChange(e) {
    this.setData({
      "embedParams.sameSubject.muted": e.detail.value,
    });
    console.log("静音播放设置:", e.detail.value);
  },

  onObjectFitChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      "embedParams.sameSubject.objectFitIndex": index,
    });
    console.log("显示模式设置:", this.data.objectFitOptions[index]);
  },

  /**
   * 跳转播放视频号视频
   * 特点：
   * - 无主体限制，任何小程序都可以使用
   * - 会跳转到微信视频号页面观看
   * - 用户需要手动返回小程序
   */
  openChannelsVideo() {
    const { finderUserName, feedId } = this.data.jumpParams;

    if (!finderUserName || !feedId) {
      this.showError("请输入视频号ID和视频ID");
      return;
    }

    this.clearError();

    wx.openChannelsActivity({
      finderUserName: finderUserName,
      feedId: feedId,
      success: (res) => {
        console.log("跳转播放成功", res);
        wx.showToast({
          title: "跳转成功",
          icon: "success",
        });
      },
      fail: (err) => {
        console.error("跳转播放失败", err);
        this.showError(`跳转播放失败: ${err.errMsg || "未知错误"}`);

        // 常见错误处理
        if (err.errCode === -1) {
          this.showError("网络错误，请检查网络连接");
        } else if (err.errCode === 40001) {
          this.showError("参数错误，请检查视频号ID和视频ID是否正确");
        } else if (err.errCode === 40013) {
          this.showError("视频不存在或已被删除");
        }
      },
    });
  },

  /**
   * 加载同主体视频
   * 特点：
   * - 要求小程序与视频号为同主体或关联主体
   * - 在小程序内直接播放，用户体验更好
   * - 支持无弹窗跳转到视频号对应视频
   */
  loadSameSubjectVideo() {
    const { finderUserName, feedId } = this.data.embedParams.sameSubject;

    if (!finderUserName || !feedId) {
      this.showError("请输入同主体视频号ID和视频ID");
      return;
    }

    this.clearError();

    // 更新数据触发channel-video组件重新渲染
    this.setData({
      "embedParams.sameSubject.finderUserName": finderUserName,
      "embedParams.sameSubject.feedId": feedId,
    });

    wx.showToast({
      title: "正在加载视频",
      icon: "loading",
    });
  },

  /**
   * 加载非同主体视频
   * 特点：
   * - 要求非个人主体小程序
   * - 需要在MP平台开启"获取视频号视频ID权限"
   * - feed-token有24小时时效限制
   */
  loadDifferentSubjectVideo() {
    const { feedToken } = this.data.embedParams.differentSubject;

    if (!feedToken) {
      this.showError(
        '请输入feed-token\n\n获取方式：\n1. 在MP平台开启"获取视频号视频ID权限"\n2. 在移动端复制视频的feed-token'
      );
      return;
    }

    this.clearError();

    // 更新数据触发channel-video组件重新渲染
    this.setData({
      "embedParams.differentSubject.feedToken": feedToken,
    });

    wx.showToast({
      title: "正在加载视频",
      icon: "loading",
    });
  },

  /**
   * 同主体视频加载成功回调
   */
  onSameSubjectVideoLoad(e) {
    console.log("同主体视频加载成功", e);
    wx.hideToast();
    wx.showToast({
      title: "视频加载成功",
      icon: "success",
    });
  },

  /**
   * 同主体视频加载失败回调
   */
  onSameSubjectVideoError(e) {
    console.error("同主体视频加载失败", e);
    wx.hideToast();

    const errorMsg = this.getVideoErrorMessage(e.detail);
    this.showError(`同主体视频加载失败: ${errorMsg}`);
  },

  /**
   * 非同主体视频加载成功回调
   */
  onDifferentSubjectVideoLoad(e) {
    console.log("非同主体视频加载成功", e);
    wx.hideToast();
    wx.showToast({
      title: "视频加载成功",
      icon: "success",
    });
  },

  /**
   * 非同主体视频加载失败回调
   */
  onDifferentSubjectVideoError(e) {
    console.error("非同主体视频加载失败", e);
    wx.hideToast();

    const errorMsg = this.getVideoErrorMessage(e.detail);
    this.showError(`非同主体视频加载失败: ${errorMsg}`);
  },

  /**
   * 获取视频错误信息
   */
  getVideoErrorMessage(errorDetail) {
    const { errCode, errMsg } = errorDetail || {};

    // 根据错误码返回友好的错误信息
    switch (errCode) {
      case 40001:
        return "参数错误，请检查视频号ID、视频ID或feed-token是否正确";
      case 40013:
        return "视频不存在或已被删除";
      case 40014:
        return "主体不匹配，请确认小程序与视频号为同主体或关联主体";
      case 40015:
        return "feed-token已过期，请重新获取（24小时有效期）";
      case 40016:
        return "小程序无权限播放此视频，请检查主体关系";
      case 48001:
        return "接口功能未授权，请检查小程序权限设置";
      default:
        return errMsg || "未知错误";
    }
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    this.setData({
      errorMessage: message,
    });

    wx.showToast({
      title: "操作失败",
      icon: "error",
    });
  },

  /**
   * 清除错误信息
   */
  clearError() {
    this.setData({
      errorMessage: "",
    });
  },

  /**
   * 页面分享配置
   */
  onShareAppMessage() {
    return {
      title: "微信小程序视频号演示",
      path: "/demo/channels-video/channels-video",
    };
  },
});
