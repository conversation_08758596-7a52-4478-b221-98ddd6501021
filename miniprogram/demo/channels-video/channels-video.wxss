/* 微信小程序视频号演示页面样式 */

.container {
  padding: 30rpx;
  background-color: #F7F8FA;
  min-height: 100vh;
}

/* 页面标题 */
.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF7A45;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 功能说明 */
.description {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.desc-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.desc-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

/* 章节样式 */
.section {
  margin-bottom: 50rpx;
}

.section-title {
  margin-bottom: 30rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 28rpx;
  color: #4A90E2;
  display: block;
}

/* 演示卡片 */
.demo-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  padding: 30rpx;
  color: white;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
}

.card-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  margin-top: 8rpx;
  display: block;
}

.card-content {
  padding: 30rpx;
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

.label {
  font-size: 30rpx;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  background: #FAFAFA;
  box-sizing: border-box;
}

.input:focus {
  border-color: #FF7A45;
  background: white;
}

/* 播放控制选项 */
.control-options {
  margin: 20rpx 0;
  padding: 20rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.option-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.option-row:last-child {
  margin-bottom: 0;
}

.option-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.picker {
  padding: 8rpx 16rpx;
  background: white;
  border-radius: 8rpx;
  border: 2rpx solid #E5E5E5;
  font-size: 26rpx;
  color: #666;
  min-width: 200rpx;
  text-align: center;
}

/* 视频容器 */
.video-container {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 30rpx 0;
  background: #F0F0F0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮样式 */
.demo-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin-top: 20rpx;
}

.demo-btn.primary {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  color: white;
}

.demo-btn.secondary {
  background: #4A90E2;
  color: white;
}

.demo-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 卡片注释 */
.card-note {
  background: #F7F8FA;
  padding: 24rpx 30rpx;
  border-top: 2rpx solid #E5E5E5;
}

.note-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 错误信息 */
.error-section {
  background: #FFF2F0;
  border: 2rpx solid #FFCCC7;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.error-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #FF4D4F;
  margin-bottom: 16rpx;
}

.error-content {
  font-size: 28rpx;
  color: #A8071A;
  line-height: 1.5;
}

/* 技术误解澄清 */
.clarification-section {
  margin-bottom: 50rpx;
}

.clarification-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #FFA940;
}

.clarification-header {
  background: linear-gradient(135deg, #FFA940 0%, #FFBB5C 100%);
  padding: 30rpx;
  color: white;
}

.clarification-title {
  font-size: 30rpx;
  font-weight: bold;
  display: block;
}

.clarification-content {
  padding: 30rpx;
}

.myth-item, .fact-item, .solution-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.myth-item {
  background: #FFF2F0;
  border-left: 6rpx solid #FF4D4F;
}

.fact-item {
  background: #F6FFED;
  border-left: 6rpx solid #52C41A;
}

.solution-item {
  background: #E6F7FF;
  border-left: 6rpx solid #1890FF;
}

.myth-label, .fact-label, .solution-label {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.myth-label {
  color: #FF4D4F;
}

.fact-label {
  color: #52C41A;
}

.solution-label {
  color: #1890FF;
}

.myth-text, .fact-text, .solution-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 功能特性分析 */
.feature-analysis-section {
  margin: 50rpx 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 30rpx;
}

.feature-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #E5E5E5;
}

.feature-header {
  background: linear-gradient(135deg, #4A90E2 0%, #5BA0F2 100%);
  padding: 24rpx 30rpx;
  color: white;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
}

.feature-content {
  padding: 24rpx 30rpx;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-label {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.feature-status {
  font-size: 24rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  text-align: center;
  min-width: 120rpx;
}

.feature-status.supported {
  background: #F6FFED;
  color: #52C41A;
  border: 1rpx solid #B7EB8F;
}

.feature-status.limited {
  background: #FFF7E6;
  color: #FA8C16;
  border: 1rpx solid #FFD591;
}

.feature-status.not-supported {
  background: #FFF2F0;
  color: #FF4D4F;
  border: 1rpx solid #FFCCC7;
}

.feature-status.controversy {
  background: #FFF7E6;
  color: #FA8C16;
  border: 1rpx solid #FFD591;
}

.feature-card.controversy {
  border-left: 8rpx solid #FA8C16;
}

.feature-card.controversy .feature-header {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFE7BA 100%);
}

.feature-status.unknown {
  background: #F0F5FF;
  color: #1890FF;
  border: 1rpx solid #ADC6FF;
}

/* 功能特性分析 */
.feature-analysis-section {
  margin: 50rpx 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 30rpx;
}

.feature-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #E5E5E5;
}

.feature-header {
  background: linear-gradient(135deg, #4A90E2 0%, #5BA0F2 100%);
  padding: 24rpx 30rpx;
  color: white;
}

.feature-title {
  font-size: 28rpx;
  font-weight: bold;
  display: block;
}

.feature-content {
  padding: 24rpx 30rpx;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-label {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.feature-status {
  font-size: 24rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  text-align: center;
  min-width: 120rpx;
}

.feature-status.supported {
  background: #F6FFED;
  color: #52C41A;
  border: 1rpx solid #B7EB8F;
}

.feature-status.limited {
  background: #FFF7E6;
  color: #FA8C16;
  border: 1rpx solid #FFD591;
}

.feature-status.not-supported {
  background: #FFF2F0;
  color: #FF4D4F;
  border: 1rpx solid #FFCCC7;
}

.feature-status.unknown {
  background: #F0F5FF;
  color: #1890FF;
  border: 1rpx solid #ADC6FF;
}

/* 对比表格 */
.comparison-section {
  margin-top: 50rpx;
}

.comparison-table {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.table-header {
  background: #FF7A45;
  color: white;
  display: flex;
  font-weight: bold;
  font-size: 30rpx;
}

.table-row {
  display: flex;
  border-bottom: 2rpx solid #F0F0F0;
  font-size: 28rpx;
}

.table-row:last-child {
  border-bottom: none;
}

.highlight-row {
  background: #FFF7E6;
  font-weight: 500;
}

/* 教育场景应用建议 */
.education-recommendation-section {
  margin: 50rpx 0;
}

.recommendation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 30rpx;
}

.recommendation-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #E5E5E5;
}

.recommendation-card.best {
  border-color: #52C41A;
}

.recommendation-card.good {
  border-color: #1890FF;
}

.recommendation-card.limited {
  border-color: #FA8C16;
}

.recommendation-card.special {
  border-color: #722ED1;
}

.recommendation-header {
  padding: 24rpx 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommendation-card.best .recommendation-header {
  background: linear-gradient(135deg, #52C41A 0%, #73D13D 100%);
}

.recommendation-card.good .recommendation-header {
  background: linear-gradient(135deg, #1890FF 0%, #40A9FF 100%);
}

.recommendation-card.limited .recommendation-header {
  background: linear-gradient(135deg, #FA8C16 0%, #FFA940 100%);
}

.recommendation-card.special .recommendation-header {
  background: linear-gradient(135deg, #722ED1 0%, #9254DE 100%);
}

.recommendation-title {
  font-size: 28rpx;
  font-weight: bold;
}

.recommendation-rating {
  font-size: 24rpx;
}

.recommendation-content {
  padding: 24rpx 30rpx;
}

.recommendation-method {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.method-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
}

.method-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  padding: 4rpx 12rpx;
  background: #F0F5FF;
  border-radius: 8rpx;
}

.recommendation-advantages,
.recommendation-implementation {
  margin-bottom: 20rpx;
}

.advantages-title,
.implementation-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.advantages-item,
.implementation-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.recommendation-summary {
  margin-top: 40rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.summary-title {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  padding: 24rpx 30rpx;
  color: white;
}

.summary-text {
  font-size: 30rpx;
  font-weight: bold;
}

.summary-content {
  padding: 30rpx;
}

.summary-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  display: block;
  margin-bottom: 16rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.highlight {
  color: #FF7A45;
  font-weight: bold;
}

.col-1, .col-2, .col-3 {
  flex: 1;
  padding: 24rpx 20rpx;
  text-align: center;
  border-right: 2rpx solid rgba(255, 255, 255, 0.3);
}

.table-header .col-1,
.table-header .col-2,
.table-header .col-3 {
  border-right: 2rpx solid rgba(255, 255, 255, 0.3);
}

.table-row .col-1,
.table-row .col-2,
.table-row .col-3 {
  border-right: 2rpx solid #F0F0F0;
  color: #333;
}

.col-3 {
  border-right: none !important;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .container {
    padding: 20rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .comparison-table {
    font-size: 26rpx;
  }
  
  .col-1, .col-2, .col-3 {
    padding: 20rpx 16rpx;
  }
}
