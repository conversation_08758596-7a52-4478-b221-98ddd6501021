// 跳跳星球 - 微信小程序主入口
// 重构后使用utils工具函数，遵循MVS规则

const {
  userStorage,
  pageUtils,
  constants,
  errorHandler,
  userActions,
  childrenActions,
  stateManager,
} = require("./utils/index.js");

const { tokenManager } = require("./utils/token-manager.js");
const { httpInjectionManager } = require("./utils/http-injection.js");

App({
  async onLaunch() {
    console.log("跳跳星球启动");

    // 防止Toast循环的强力措施
    this.preventToastLoop();

    // 初始化状态管理器（状态管理器会自动恢复状态）
    console.log("✅ 状态管理器初始化完成");

    // 初始化全局错误处理
    this.initErrorHandler();

    // 🔧 初始化HTTP依赖注入
    this.initHttpInjection();

    // 应用启动时进行静默登录
    await this.silentLogin();

    // 初始化模拟数据（开发阶段）
    this.initMockData();
  },

  // 防止Toast循环的温和措施
  preventToastLoop() {
    const originalShowToast = wx.showToast;
    let toastHistory = new Map(); // 记录Toast历史

    wx.showToast = function (options) {
      const now = Date.now();
      const title = options?.title || "";

      console.log("🍞 Toast调用:", title); // 调试信息

      // 只针对特定的循环错误进行防护
      const isLoopError =
        title.includes("showToast:fail") ||
        title.includes("TypeError: Failed to fetch");

      if (isLoopError) {
        console.warn("🔇 检测到Toast循环错误，已阻止:", title);
        return Promise.resolve();
      }

      // 对于相同内容的Toast，5秒内只显示一次（防抖）
      if (toastHistory.has(title)) {
        const lastTime = toastHistory.get(title);
        if (now - lastTime < 5000) {
          console.log("🔇 相同Toast内容防抖:", title);
          return Promise.resolve();
        }
      }

      toastHistory.set(title, now);

      // 清理过期的历史记录
      if (toastHistory.size > 10) {
        const entries = Array.from(toastHistory.entries());
        entries.forEach(([key, time]) => {
          if (now - time > 30000) {
            // 30秒后清理
            toastHistory.delete(key);
          }
        });
      }

      try {
        console.log("✅ 准备显示Toast:", title); // 调试信息
        return originalShowToast.call(this, options);
      } catch (error) {
        console.error("Toast显示失败:", error);
        // 如果Toast显示失败，不要抛出错误，避免循环
        return Promise.resolve();
      }
    };
  },

  onShow() {
    console.log("小程序显示");
  },

  onHide() {
    console.log("小程序隐藏");
  },

  onError(error) {
    console.error("小程序全局错误:", error);
    // 使用错误处理器处理全局错误
    errorHandler.handle(error, {
      source: "app_global_error",
      timestamp: Date.now(),
    });
  },

  onUnhandledRejection(res) {
    // 只过滤特定的Toast循环错误
    const errorStr = JSON.stringify(res);
    const reasonStr = res.reason?.toString() || "";
    const messageStr = res.reason?.message || "";

    // 只过滤明确的Toast循环错误
    const isToastLoopError =
      errorStr.includes("showToast:fail TypeError: Failed to fetch") ||
      reasonStr.includes("showToast:fail TypeError: Failed to fetch") ||
      messageStr.includes("showToast:fail TypeError: Failed to fetch");

    if (isToastLoopError) {
      console.warn("🔇 检测到Toast循环错误，已过滤");
      return;
    }

    console.error("未处理的Promise拒绝:", res);

    // 正常处理其他错误
    try {
      errorHandler.handle(res.reason, {
        source: "unhandled_promise_rejection",
        promise: res.promise,
        timestamp: Date.now(),
      });
    } catch (handlerError) {
      console.error("错误处理器失败:", handlerError);
    }
  },

  // 初始化错误处理器
  initErrorHandler() {
    // 添加全局错误监听器
    errorHandler.addListener((error) => {
      // 可以在这里添加错误上报逻辑
      console.log("全局错误处理:", error.toLogFormat());

      // 在开发环境显示详细错误信息
      if (constants.APP_CONFIG.DEBUG) {
        console.error("详细错误信息:", error);
      }
    });
  },

  // 静默登录 - 应用启动时自动执行
  async silentLogin() {
    try {
      // 检查是否已有有效的登录态
      const isLoggedIn = userActions.isLoggedIn();
      const token = userActions.getToken();

      if (isLoggedIn && token) {
        // 检查session_key是否有效
        const hasValidSession = await this.checkSession();
        if (hasValidSession) {
          console.log("✅ 登录态有效，无需重新登录");
          return;
        }
      }

      console.log("🔄 开始静默登录...");

      // 获取微信登录凭证
      const { wechat } = require("./utils/index.js");
      const code = await wechat.auth.login();
      console.log("✅ 获取微信登录凭证成功:", code);

      // 调用静默登录API
      const result = await this.callSilentLoginAPI(code);
      console.log("🔍 静默登录API返回数据:", result);

      // 验证返回数据的完整性
      if (!result.id || !result.openid) {
        throw new Error("静默登录返回数据不完整，缺少必要的身份信息");
      }

      // 保存登录状态，安全处理可能缺失的字段
      userActions.login(
        {
          // 基本身份信息（静默登录必有）
          id: result.id,
          openid: result.openid,
          unionid: result.unionid || "",

          // 个人信息（可能为空，需要用户授权）
          nickname: result.nickname || "",
          avatar: result.avatar || "",
          phone: result.phone || "",
          gender: result.gender || 0,
          province: result.province || "",
          city: result.city || "",

          // 账户状态信息（有默认值）
          status: result.status || 1,
          user_type: result.user_type || 1,

          // 🔥 关键：current_child_id（可能为0表示未选择）
          current_child_id: result.current_child_id || 0,

          // 时间信息（可能为空）
          last_login_at: result.last_login_at || "",
          created_at: result.created_at || "",
          updated_at: result.updated_at || "",
        },
        result.token,
        result.refresh_token || ""
      );

      console.log("✅ 用户登录状态已保存");

      // 静默登录成功后同步儿童数据（独立错误处理）
      try {
        await this.syncChildrenDataSilently();
        console.log("✅ 儿童数据同步完成");
      } catch (syncError) {
        console.warn("⚠️ 儿童数据同步失败，但不影响登录:", syncError.message);
        // 儿童数据同步失败不影响登录流程
      }

      console.log("✅ 静默登录成功:", {
        openid: result.openid,
        current_child_id: result.current_child_id || 0,
        is_new_user: result.is_new_user || false,
        has_user_info: !!(result.nickname && result.avatar),
      });
    } catch (error) {
      console.log("⚠️ 静默登录失败:", error.message);
      // 静默登录失败不影响应用使用，用户仍可以浏览基本内容
    }
  },

  // 检查微信session_key是否有效
  async checkSession() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => resolve(true),
        fail: () => resolve(false),
      });
    });
  },

  // 调用静默登录API
  async callSilentLoginAPI(code) {
    try {
      // 动态导入API模块
      const authAPI = require("./apis/auth.js");

      // 调用真实的微信登录API
      const result = await authAPI.wechatLogin({ code });

      console.log("✅ 静默登录API调用成功:", result);

      // 返回完整的登录结果，包含所有用户信息
      return {
        // 基本身份信息
        id: result.user_info?.id || result.userInfo?.id,
        openid: result.user_info?.openid || result.userInfo?.openid,
        unionid: result.user_info?.unionid || result.userInfo?.unionid,
        token: result.access_token || result.accessToken,

        // 用户个人信息
        nickname: result.user_info?.nickname || result.userInfo?.nickname || "",
        avatar: result.user_info?.avatar || result.userInfo?.avatar || "",
        phone: result.user_info?.phone || result.userInfo?.phone || "",
        gender: result.user_info?.gender || result.userInfo?.gender || 0,
        province: result.user_info?.province || result.userInfo?.province || "",
        city: result.user_info?.city || result.userInfo?.city || "",

        // 账户状态信息
        status: result.user_info?.status || result.userInfo?.status || 1,
        user_type:
          result.user_info?.user_type || result.userInfo?.user_type || 1,

        // 🔥 关键字段：当前选择的孩子ID
        current_child_id:
          result.user_info?.current_child_id ||
          result.userInfo?.current_child_id ||
          0,

        // 时间信息
        last_login_at:
          result.user_info?.last_login_at || result.userInfo?.last_login_at,
        created_at: result.user_info?.created_at || result.userInfo?.created_at,
        updated_at: result.user_info?.updated_at || result.userInfo?.updated_at,

        // 新用户标识
        is_new_user: result.is_new_user || false,
      };
    } catch (error) {
      console.error("❌ 静默登录API调用失败:", error);
      throw error;
    }
  },

  // globalData已移除，统一使用状态管理器
  // 如需访问状态，请使用：
  // - userActions.isLoggedIn(), userActions.getUserInfo(), userActions.getToken()
  // - childrenActions.getCurrentChild(), childrenActions.getChildrenList()

  // 初始化模拟数据
  initMockData() {
    const existingChildren = childrenActions.getChildrenList();
    if (!existingChildren || existingChildren.length === 0) {
      // 开发阶段：可以选择是否添加模拟数据
      // 注释掉下面的代码来测试新用户无孩子的情况
      /*
      const mockChildren = [
        {
          id: 1,
          name: "小明",
          age: 6,
          avatar: "👦",
          gender: "male",
        },
        {
          id: 2,
          name: "小红",
          age: 8,
          avatar: "👧",
          gender: "female",
        },
      ];
      childrenActions.setChildrenList(mockChildren);
      childrenActions.setCurrentChild(mockChildren[0]);
      */
    }
  },

  // 获取用户信息
  getUserInfo() {
    // TODO: 调用API获取用户信息
    console.log("获取用户信息");
  },

  // 设置当前选中的孩子（使用状态管理器）
  setCurrentChild(child) {
    childrenActions.setCurrentChild(child);
  },

  // 获取当前选中的孩子（使用状态管理器）
  getCurrentChild() {
    return childrenActions.getCurrentChild();
  },

  // 获取孩子列表（使用状态管理器）
  getChildren() {
    return childrenActions.getChildrenList();
  },

  // 设置孩子列表（使用状态管理器）
  setChildren(children) {
    childrenActions.setChildrenList(children);
  },

  // 添加孩子（使用状态管理器）
  addChild(child) {
    childrenActions.addChild(child);
  },

  // 登录（使用状态管理器）
  login(userInfo, token, refreshToken = "") {
    userActions.login(userInfo, token, refreshToken);
    console.log("✅ 用户登录完成:", userInfo.nickname || userInfo.nickName);

    // 启动token管理器
    tokenManager.start();
  },

  // 登出（使用状态管理器）
  logout() {
    userActions.logout();

    // 停止token管理器
    tokenManager.stop();

    console.log("✅ 用户登出完成");

    // 跳转到登录页
    wx.reLaunch({
      url: constants.PAGES.LOGIN,
    });
  },

  // 显示加载提示 - 使用工具函数
  showLoading(title = "加载中...") {
    pageUtils.showLoading(title);
  },

  // 隐藏加载提示 - 使用工具函数
  hideLoading() {
    pageUtils.hideLoading();
  },

  // 显示成功提示 - 使用工具函数
  showSuccess(title) {
    pageUtils.showSuccess(title);
  },

  // 显示错误提示 - 使用工具函数
  showError(title) {
    pageUtils.showError(title);
  },

  /**
   * 静默同步儿童数据
   * 在静默登录成功后调用，根据current_child_id决定是否同步
   */
  async syncChildrenDataSilently() {
    try {
      console.log("🔄 静默检查是否需要同步儿童数据...");

      // 获取用户信息，检查current_child_id
      const { userActions } = require("./utils/index.js");
      const userInfo = userActions.getUserInfo();

      if (!userInfo) {
        console.log("⚠️ 用户信息不存在，跳过儿童数据同步");
        return;
      }

      const currentChildId = userInfo.current_child_id || 0;
      console.log("🔍 静默检查 current_child_id:", currentChildId);

      if (currentChildId > 0) {
        console.log("✅ current_child_id > 0，开始静默同步儿童数据");
        await this.performSilentChildrenSync();
      } else {
        console.log("ℹ️ current_child_id = 0，用户暂无儿童，跳过同步");
        // 清空儿童相关状态
        const { childrenActions } = require("./utils/index.js");
        childrenActions.setChildrenList([]);
        childrenActions.setCurrentChild(null);
      }
    } catch (error) {
      console.log("⚠️ 静默条件同步儿童数据失败:", error.message);
      // 静默同步失败不影响应用使用
    }
  },

  /**
   * 执行静默儿童数据同步
   */
  async performSilentChildrenSync() {
    try {
      // 动态导入儿童API和状态管理
      const childrenAPI = require("./apis/children.js");
      const { childrenActions, userActions } = require("./utils/index.js");

      // 获取儿童列表
      const childrenList = await childrenAPI.getChildrenList();
      console.log("✅ 静默获取儿童列表成功:", childrenList?.length || 0);

      // 处理API响应数据格式
      const children = Array.isArray(childrenList)
        ? childrenList
        : childrenList?.data || [];

      // 更新儿童列表到状态管理
      childrenActions.setChildrenList(children);

      // 如果有儿童数据，设置当前选中儿童
      if (children && children.length > 0) {
        // 获取用户信息，查看是否有current_child_id
        const userInfo = userActions.getUserInfo();
        let currentChild = null;

        if (
          userInfo &&
          userInfo.current_child_id &&
          userInfo.current_child_id > 0
        ) {
          // 根据current_child_id查找对应的儿童
          currentChild = children.find(
            (child) => child.id === userInfo.current_child_id
          );
        }

        // 如果没有找到对应的儿童，选择第一个
        if (!currentChild) {
          currentChild = children[0];
        }

        // 设置当前选中儿童
        childrenActions.setCurrentChild(currentChild);
        console.log("✅ 静默设置当前儿童:", currentChild.name);
      } else {
        console.log("ℹ️ 用户暂无儿童数据");
        childrenActions.setCurrentChild(null);
      }
    } catch (error) {
      console.log("⚠️ 执行静默儿童数据同步失败:", error.message);
      // 静默同步失败不影响应用使用，可能是因为后端儿童API还未实现
      // 这种情况下用户仍可以正常使用应用的其他功能
    }
  },

  /**
   * 初始化HTTP依赖注入
   * 解决循环依赖问题，为HTTP客户端注入认证相关处理函数
   */
  initHttpInjection() {
    console.log("🔧 开始初始化HTTP依赖注入...");

    try {
      // 动态导入authAPI（避免循环依赖）
      const authAPI = require("./apis/auth.js");

      // 设置Token刷新处理函数
      const tokenRefreshHandler = async (refreshToken) => {
        console.log("🔄 执行注入的Token刷新处理函数");
        return await authAPI.refreshToken(refreshToken);
      };

      // 设置登录重定向处理函数
      const loginRedirectHandler = (reason) => {
        console.log("🔄 执行注入的登录重定向处理函数，原因:", reason);

        // 根据不同原因显示不同提示
        const messages = {
          token_expired: "登录已过期，请重新登录",
          refresh_token_missing: "登录信息丢失，请重新登录",
          refresh_token_expired: "登录已过期，请重新登录",
          refresh_token_invalid: "登录信息无效，请重新登录",
          network_error: "网络异常，请检查网络后重新登录",
          unknown_error: "登录异常，请重新登录",
          token_refresh_handler_missing: "系统配置异常，请重新登录",
        };

        const message = messages[reason] || messages.token_expired;

        // 显示提示
        wx.showToast({
          title: message,
          icon: "none",
          duration: 2000,
        });

        // 延迟跳转，让用户看到提示
        setTimeout(() => {
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const currentRoute = currentPage ? currentPage.route : "";

          // 构建重定向URL，包含原因参数
          const redirectUrl = encodeURIComponent(`/${currentRoute}`);

          wx.reLaunch({
            url: `/pages/login/login?redirectUrl=${redirectUrl}&reason=${reason}`,
          });
        }, 1000);
      };

      // 批量设置处理函数
      httpInjectionManager.setHandlers({
        tokenRefresh: tokenRefreshHandler,
        loginRedirect: loginRedirectHandler,
      });

      console.log("✅ HTTP依赖注入初始化完成");
    } catch (error) {
      console.error("❌ HTTP依赖注入初始化失败:", error);
      // 初始化失败不应该阻止应用启动，但需要记录错误
    }
  },

  // 网络请求封装 - 已重构到utils/request.js
  // 保留此方法以兼容现有代码，但建议直接使用 import { http } from './utils/index.js'
  request(options) {
    console.warn(
      'app.request已废弃，请使用 import { http } from "./utils/index.js"'
    );
    // 为了兼容性，这里可以导入http并调用
    // 但建议页面直接使用新的http工具
    return Promise.reject(new Error("请使用新的http工具函数"));
  },
});
