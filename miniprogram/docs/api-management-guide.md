# API统一管理指南

## 📋 概述

遵循MVS规则，我们实现了统一的API管理系统，采用**方案B（端点内部定义）**架构，提供高内聚、低耦合的模块化设计。

## 🏗️ 架构设计

### 分层结构
```
miniprogram/
├── apis/                    # API模块目录
│   ├── auth.js             # 认证相关API
│   ├── user.js             # 用户管理API
│   ├── children.js         # 孩子管理API
│   ├── checkin.js          # 打卡系统API
│   ├── content.js          # 内容管理API
│   └── index.js            # 统一导出
├── utils/
│   ├── request.js          # HTTP请求客户端
│   ├── constants.js        # 基础常量定义
│   └── index.js           # 工具函数导出
```

### 核心组件
1. **HTTP客户端** (`utils/request.js`) - 底层网络请求封装
2. **API模块** (`apis/*.js`) - 业务API封装，端点内部定义
3. **基础常量** (`utils/constants.js`) - 基础配置常量
4. **统一导出** (`apis/index.js`) - 便捷访问入口

### 方案B架构优势
- ✅ **高内聚**：端点定义与使用在同一文件
- ✅ **低耦合**：修改某个模块不影响其他模块
- ✅ **易维护**：开发时只需关注当前模块
- ✅ **便测试**：每个模块可独立测试

## 🚀 使用方法

### 1. 基础用法

```javascript
// 导入API模块
const { authAPI, userAPI, api } = require('../../utils/index.js');

// 方式1: 直接使用具体API（推荐）
const loginResult = await authAPI.wechatLogin({ code: 'wx_code' });

// 方式2: 使用api对象
const userInfo = await api.user.getCurrentUser();

// 方式3: 导入整个apis模块
const { apis } = require('../../utils/index.js');
const children = await apis.children.getChildrenList();
```

### 2. 模块内部结构

每个API模块都采用统一的结构：

```javascript
// apis/auth.js 示例
const { http } = require('../utils/request.js');
const { API } = require('../utils/constants.js');

// 端点定义 - 模块内部定义
const ENDPOINTS = {
  WECHAT_LOGIN: `${API.BASE_URL}/auth/wechat/login`,
  REFRESH_TOKEN: `${API.BASE_URL}/auth/refresh`
};

const authAPI = {
  async wechatLogin(params) {
    return http.post(ENDPOINTS.WECHAT_LOGIN, params);
  }
};

module.exports = authAPI;
```

### 3. 认证API

```javascript
// 微信登录
const loginData = await authAPI.wechatLogin({
  code: 'wx_login_code',
  encryptedData: 'encrypted_user_info', // 可选
  iv: 'initial_vector' // 可选
});

// 刷新Token
const newToken = await authAPI.refreshToken('refresh_token');

// 登出
await authAPI.logout();

// 验证Token
const isValid = await authAPI.verifyToken('access_token');
```

### 4. 用户API

```javascript
// 获取当前用户信息
const userInfo = await userAPI.getCurrentUser();

// 更新用户信息
const updatedUser = await userAPI.updateUserInfo({
  nickname: '新昵称',
  avatar: 'avatar_url'
});

// 根据ID获取用户（管理员权限）
const user = await userAPI.getUserById(123);

// 上传头像
const avatarResult = await userAPI.uploadAvatar(filePath);

// 绑定手机号
const bindResult = await userAPI.bindPhone({
  encryptedData: 'encrypted_data',
  iv: 'iv_string'
});
```

### 5. 孩子管理API

```javascript
// 获取孩子列表
const children = await childrenAPI.getChildrenList();

// 创建孩子档案
const newChild = await childrenAPI.createChild({
  name: '小明',
  age: 8,
  gender: 1
});

// 更新孩子信息
const updatedChild = await childrenAPI.updateChild(childId, {
  name: '小明明'
});

// 获取孩子详情
const childDetail = await childrenAPI.getChildDetail(childId);

// 获取孩子统计信息
const stats = await childrenAPI.getChildStats(childId);
```

### 5. 打卡API

```javascript
// 创建打卡记录
const checkin = await checkinAPI.createCheckin({
  child_id: 123,
  video_url: 'video_url',
  content: '今天跳了100个'
});

// 获取打卡历史
const history = await checkinAPI.getCheckinHistory({
  child_id: 123,
  page: 1,
  limit: 20
});

// 上传打卡视频
const uploadResult = await checkinAPI.uploadCheckinVideo(filePath, {
  child_id: 123,
  content: '今天的跳绳练习',
  onProgress: (progress) => console.log(progress)
});

// 获取今日打卡状态
const todayStatus = await checkinAPI.getTodayCheckinStatus(childId);

// 获取打卡统计
const stats = await checkinAPI.getCheckinStats(childId, { period: 'month' });
```

### 7. 内容API

```javascript
// 获取视频列表
const videos = await contentAPI.getVideoList({
  category: 'jump_rope',
  page: 1,
  limit: 20
});

// 获取视频详情
const videoDetail = await contentAPI.getVideoDetail(videoId);

// 获取训练营列表
const camps = await contentAPI.getCampList({
  status: 'active',
  age_group: '6-8'
});

// 参加训练营
const joinResult = await contentAPI.joinCamp(campId, childId);

// 搜索内容
const searchResults = await contentAPI.searchContent({
  keyword: '跳绳',
  type: 'video'
});

// 获取推荐内容
const recommendations = await contentAPI.getRecommendations({
  child_id: 123,
  limit: 10
});
```

## 🔧 配置管理

### API基础配置

```javascript
// utils/constants.js - 只保留基础配置
const API = {
  BASE_URL: config.API_BASE_URL, // 从config.js读取
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
};
```

### 端点定义（方案B）

每个API模块内部定义自己的端点：

```javascript
// apis/auth.js - 端点内部定义
const ENDPOINTS = {
  WECHAT_LOGIN: `${API.BASE_URL}/auth/wechat/login`,
  REFRESH_TOKEN: `${API.BASE_URL}/auth/refresh`,
  LOGOUT: `${API.BASE_URL}/auth/logout`
};

// apis/user.js - 端点内部定义
const ENDPOINTS = {
  INFO: `${API.BASE_URL}/user/info`,
  BY_ID: `${API.BASE_URL}/admin/user/:id`
};
```

### 环境配置

```javascript
// utils/config.js
module.exports = {
  // 开发环境
  API_BASE_URL: "http://localhost:8080/api/v1",

  // 生产环境
  // API_BASE_URL: "https://api.kids-platform.com/v1",
};
```

## 🛡️ 错误处理

### 统一错误处理

```javascript
try {
  const result = await authAPI.wechatLogin({ code });
  // 处理成功结果
} catch (error) {
  // 统一错误处理
  console.error('登录失败:', error.message);
  
  // 根据错误类型处理
  if (error.code === 'NETWORK_ERROR') {
    // 网络错误处理
  } else if (error.code === 'AUTH_FAILED') {
    // 认证失败处理
  }
}
```

### 自动重试机制

HTTP客户端内置重试机制：
- 网络错误自动重试
- 服务器错误(5xx)自动重试
- 可配置重试次数和延迟

## 📝 开发规范

### 1. 添加新API模块

```javascript
// 1. 创建新的API模块文件 apis/new-module.js
const { http } = require('../utils/request.js');
const { API } = require('../utils/constants.js');

// 端点定义 - 模块内部定义
const ENDPOINTS = {
  CREATE: `${API.BASE_URL}/new-module`,
  LIST: `${API.BASE_URL}/new-module`,
  UPDATE: `${API.BASE_URL}/new-module/:id`
};

const newModuleAPI = {
  async create(data) {
    return http.post(ENDPOINTS.CREATE, data);
  },

  async getList(params = {}) {
    return http.get(ENDPOINTS.LIST, { params });
  }
};

module.exports = newModuleAPI;

// 2. 在apis/index.js中添加导出
const newModuleAPI = require('./new-module.js');

module.exports = {
  // ... 其他API
  newModuleAPI,
  api: {
    // ... 其他模块
    newModule: newModuleAPI
  }
};
```

### 2. 参数验证

```javascript
const newAPI = {
  async create(data) {
    // 参数验证
    if (!data.name) {
      throw new Error('名称不能为空');
    }
    
    return http.post(API_ENDPOINTS.NEW_MODULE.CREATE, data);
  }
};
```

### 3. 响应数据处理

```javascript
const userAPI = {
  async getCurrentUser() {
    const response = await http.get(API_ENDPOINTS.USER.INFO);
    
    // 数据转换和处理
    return {
      ...response,
      avatar: response.avatar || '/images/default-avatar.png'
    };
  }
};
```

## 🧪 测试建议

### 1. API测试

```javascript
// 测试API调用
describe('authAPI', () => {
  it('should login successfully', async () => {
    const result = await authAPI.wechatLogin({ code: 'test_code' });
    expect(result.access_token).toBeDefined();
  });
});
```

### 2. 错误处理测试

```javascript
it('should handle network error', async () => {
  // 模拟网络错误
  try {
    await authAPI.wechatLogin({ code: 'invalid_code' });
  } catch (error) {
    expect(error.code).toBe('NETWORK_ERROR');
  }
});
```

## 🎯 最佳实践

### 方案B架构优势

1. **高内聚，低耦合**
   - 端点定义与使用在同一文件
   - 修改某个模块不影响其他模块
   - 便于单元测试和维护

2. **开发效率**
   - 开发时只需关注当前模块
   - 减少跨文件引用
   - 更好的代码组织

3. **团队协作**
   - 不同开发者可并行开发不同模块
   - 代码冲突减少
   - 便于代码审查

### 开发规范

1. **统一使用API模块**，避免直接调用HTTP客户端
2. **完整的参数验证**，在API层进行基础验证
3. **统一的错误处理**，提供用户友好的错误信息
4. **详细的日志记录**，记录关键API调用和错误
5. **完整的JSDoc文档**，提供类型提示和使用示例
6. **合理的数据转换**，在API层处理响应数据格式

### 性能优化

1. **缓存策略**，对适合的API添加缓存
2. **请求合并**，避免重复请求
3. **错误重试**，网络错误自动重试
4. **超时控制**，合理设置请求超时

---

**遵循MVS规则，采用方案B架构，构建高质量的API管理系统！** 🎯
