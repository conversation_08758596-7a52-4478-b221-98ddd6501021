# 儿童跳绳学习平台 - 设计系统

## 📋 概述

本设计系统基于微信小程序官方最佳实践，确保无障碍访问和用户体验一致性。

## 🎨 颜色系统

### 主色调
- **主色调**: `#FF7A45` (活力橙) - 体现运动活力
- **功能色**: `#7ED321` (跳绳绿) - 成功、完成状态
- **辅助色**: `#4A90E2` (青蓝色) - 信息提示
- **背景色**: `#F7F8FA` (米白色) - 页面背景

### 文字颜色（符合4.5:1对比度标准）
- **主要文字**: `#1A1A1A` - 对比度 12.6:1
- **次要文字**: `#595959` - 对比度 4.5:1
- **占位文字**: `#8C8C8C` - 对比度 3.2:1
- **禁用文字**: `#BFBFBF` - 对比度 2.3:1

### 状态颜色
- **成功色**: `#52C41A`
- **警告色**: `#FAAD14`
- **错误色**: `#F5222D`

## 📝 字体系统

### 字体大小（实用层级）
- **--font-sm**: 30rpx - 小字体（标签、辅助信息）
- **--font-md**: 32rpx - 标准字体（正文、按钮）
- **--font-lg**: 36rpx - 大字体（标题、重要信息）
- **--font-xl**: 42rpx - 特殊标题（首页训练营标题等）

### 标准字体样式类（推荐使用）
- **text-title-main**: 42rpx + 加粗 - 主标题
- **text-title-section**: 36rpx + 加粗 - 区域标题
- **text-title-card**: 32rpx + 加粗 - 卡片标题
- **text-body**: 32rpx - 正文内容
- **text-caption**: 30rpx - 说明文字
- **text-label**: 30rpx - 标签文字
- **text-button**: 32rpx - 按钮文字

### 行高标准
- **--line-height-sm**: 1.3 - 紧凑行高
- **--line-height-md**: 1.4 - 标准行高
- **--line-height-lg**: 1.5 - 舒适行高

### 字体族
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
```

## 📏 间距系统

### 间距变量
- **--spacing-xs**: 4rpx (2pt)
- **--spacing-sm**: 8rpx (4pt)
- **--spacing-md**: 16rpx (8pt)
- **--spacing-lg**: 24rpx (12pt)
- **--spacing-xl**: 32rpx (16pt)
- **--spacing-xxl**: 48rpx (24pt)

## 🔘 触摸目标

### 最小尺寸（符合44x44pt标准）
- **--touch-target-min**: 88rpx (44pt) - 最小触摸目标
- **--touch-target-comfortable**: 96rpx (48pt) - 舒适触摸目标

## 🎯 圆角系统

- **--radius-sm**: 8rpx
- **--radius-md**: 12rpx
- **--radius-lg**: 16rpx
- **--radius-xl**: 24rpx

## 🔲 组件规范

### 按钮
- 最小高度: 88rpx (44pt)
- 内边距: 16rpx 24rpx
- 圆角: 12rpx
- 字体大小: 32rpx

### 卡片
- 背景: #FFFFFF
- 圆角: 12rpx
- 阴影: 0 4rpx 12rpx rgba(0, 0, 0, 0.05)

## 📱 无障碍设计

### 文字要求
- 所有文字不小于28rpx (14pt)
- 正文文字不小于40rpx (20pt)
- 行间距不少于1.3倍

### 对比度要求
- 普通文字: 4.5:1
- 大文字(36rpx+): 3:1

### 触摸目标
- 最小44x44pt (88rpx)
- 推荐48x48pt (96rpx)

## 🎨 使用示例

### 推荐使用方式（标准样式类）
```xml
<!-- 主标题 -->
<text class="text-title-main">训练营标题</text>

<!-- 区域标题 -->
<text class="text-title-section">更多训练营</text>

<!-- 卡片标题 -->
<text class="text-title-card">卡片标题</text>

<!-- 正文内容 -->
<text class="text-body">这是正文内容描述</text>

<!-- 说明文字 -->
<text class="text-caption">说明信息</text>

<!-- 标签 -->
<text class="text-label">标签</text>
```

## 📁 样式架构重构

### 重构原因
原来的 `app.wxss` 文件有471行代码，包含了所有样式定义，不便于维护。现在按功能模块拆分成多个文件，提高可维护性和可读性。

### 新的文件结构
```
styles/
├── variables.wxss     # 变量定义：颜色、字体、间距、圆角等
├── base.wxss         # 基础样式：重置、文字、工具类、动画
├── layout.wxss       # 布局样式：容器、卡片、flex布局
├── buttons.wxss      # 按钮样式：各种按钮类型和状态
├── spacing.wxss      # 间距样式：margin、padding工具类
├── forms.wxss        # 表单样式：输入框、选择器等
└── emoji-icons.wxss  # 表情图标样式
```

### 导入顺序
在 `app.wxss` 中按以下顺序导入：
1. **variables.wxss** - 变量定义（必须最先导入）
2. **base.wxss** - 基础样式和重置
3. **layout.wxss** - 布局相关样式
4. **buttons.wxss** - 按钮组件样式
5. **spacing.wxss** - 间距工具类
6. **forms.wxss** - 表单组件样式
7. **emoji-icons.wxss** - 图标样式

### 模块化的优势
1. **可维护性**: 每个文件职责单一，便于查找和修改
2. **可读性**: 代码结构清晰，易于理解
3. **可扩展性**: 新增功能样式时，可以独立添加文件
4. **团队协作**: 多人开发时减少冲突

### 文件职责划分
- **variables.wxss**: 只包含CSS变量定义
- **base.wxss**: 基础重置、文字样式、工具类、动画
- **layout.wxss**: 布局相关的容器和flex样式
- **buttons.wxss**: 所有按钮相关样式
- **spacing.wxss**: 所有间距工具类
- **forms.wxss**: 表单元素样式
- **emoji-icons.wxss**: 图标相关样式

### 维护指南
1. **添加新样式**: 确定功能模块，在对应文件中添加
2. **修改现有样式**: 优先修改 `variables.wxss` 中的变量
3. **命名规范**: 使用语义化类名，遵循BEM方法论
4. **使用变量**: 使用CSS变量而不是硬编码值

<!-- 按钮文字 -->
<button class="btn btn-primary text-button">按钮</button>
```

### 传统方式（CSS变量 + 工具类）
```css
/* 字体大小 */
.text-sm, .text-md, .text-lg, .text-xl

/* 颜色 */
.text-primary, .text-secondary, .text-placeholder
.text-success, .text-warning, .text-error

/* 按钮 */
.btn-primary, .btn-secondary, .btn-outline, .btn-ghost
.btn-small, .btn-large, .btn-block

/* 布局 */
.flex, .flex-center, .flex-between
.m-xs, .m-sm, .m-md, .m-lg, .m-xl
.p-xs, .p-sm, .p-md, .p-lg, .p-xl
```

## 🔄 更新日志

### v1.1.0 (当前版本)
- 基于微信官方最佳实践优化字体大小
- 提升文字对比度，符合无障碍标准
- 统一触摸目标尺寸为44pt标准
- 添加行高变量和无障碍友好样式类

### v1.0.0
- 初始设计系统建立
- 基础颜色、字体、间距系统
