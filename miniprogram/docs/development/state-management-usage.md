# 🔄 状态管理系统使用指南

## 📋 概述

本项目实现了基于微信小程序最佳实践的状态管理系统，提供：
- **单一数据源**：集中管理应用状态
- **状态不可变性**：确保状态变化可追踪
- **响应式更新**：状态变化自动同步到页面
- **状态持久化**：自动保存和恢复状态

## 🏗️ 架构设计

### 核心模块
```
utils/
├── state-manager.js    # 状态管理器核心
├── state-actions.js    # 状态操作方法
├── state-mixin.js      # 页面状态绑定
└── index.js           # 统一导出
```

### 状态结构
```javascript
state = {
  user: {
    isLoggedIn: false,
    token: '',
    userInfo: null
  },
  children: {
    currentChild: null,
    childrenList: [],
    loading: false
  },
  business: {
    activeTasks: [],
    recentRecords: [],
    leaderboard: [],
    badges: []
  },
  ui: {
    loading: false,
    error: null,
    toast: null
  },
  app: {
    version: '1.1.0',
    theme: 'light',
    language: 'zh-CN'
  }
}
```

## 🚀 快速开始

### 1. 基础使用

```javascript
// 导入状态管理
const {
  userActions,
  childrenActions,
  createStatePage
} = require('../../utils/index.js');

// 创建状态管理页面
const homePage = createStatePage({
  data: {
    // 页面数据
  },

  onLoad() {
    // 绑定用户状态
    this.bindUserState();
    this.bindChildrenState();
  },

  // 状态变化回调
  onStateChange(path, newValue, oldValue) {
    console.log(`状态变化: ${path}`, newValue);
  }
});

Page(homePage);
```

### 2. 状态操作

```javascript
// 用户操作
userActions.login(token, userInfo);
userActions.logout();
userActions.updateUserInfo(userInfo);

// 孩子管理
childrenActions.setCurrentChild(child);
childrenActions.addChild(child);
childrenActions.updateChild(childId, updates);
```

## 📖 详细使用说明

### 状态绑定

页面可以绑定特定的状态分支，当状态变化时自动更新页面数据：

```javascript
const page = createStatePage({
  onLoad() {
    // 绑定用户状态到页面
    this.bindUserState();

    // 绑定孩子状态到页面
    this.bindChildrenState();

    // 绑定业务状态到页面
    this.bindBusinessState();
  },

  // 状态变化时的回调
  onStateChange(path, newValue, oldValue) {
    if (path === 'user.isLoggedIn') {
      // 登录状态变化处理
      this.handleLoginStateChange(newValue);
    }
  }
});
```

### 状态预设

为常见的页面类型提供了状态预设：

```javascript
// 使用预设创建页面
const page = withStatePreset('userProfile')(createStatePage({
  // 页面配置
}));

// 可用预设
statePresets = {
  userProfile: ['user'],           // 用户资料页
  childManagement: ['children'],   // 孩子管理页
  dashboard: ['user', 'children', 'business'], // 仪表板页
  minimal: []                      // 最小状态页
}
```

## 🔧 高级功能

### 状态持久化

状态管理器自动处理状态的保存和恢复：

```javascript
// 自动保存到本地存储
userActions.login(token, userInfo); // 自动保存用户状态

// 应用启动时自动恢复
stateManager.init(); // 从本地存储恢复状态
```

### 状态监听

可以监听特定状态的变化：

```javascript
// 监听用户登录状态
stateManager.subscribe('user.isLoggedIn', (newValue, oldValue) => {
  if (newValue) {
    console.log('用户已登录');
  } else {
    console.log('用户已登出');
  }
});
```

### 状态验证

内置状态验证确保数据完整性：

```javascript
// 自动验证状态结构
userActions.updateUserInfo({
  nickname: 'test',
  avatar: 'https://example.com/avatar.jpg'
}); // 自动验证用户信息格式
```

## 🎯 最佳实践

### 1. 状态设计原则
- **单一数据源**：所有状态集中管理
- **状态不可变**：通过actions修改状态
- **最小化状态**：只存储必要的状态
- **状态扁平化**：避免深层嵌套

### 2. 页面使用建议
- **按需绑定**：只绑定页面需要的状态
- **及时清理**：页面卸载时清理监听器
- **状态预设**：使用预设简化常见场景
- **错误处理**：处理状态操作的异常情况

### 3. 性能优化
- **批量更新**：合并多个状态变化
- **选择性更新**：只更新变化的部分
- **异步操作**：使用异步actions处理复杂逻辑

## 🐛 常见问题

### Q: 状态没有更新到页面？
A: 检查是否正确绑定了状态，确保调用了对应的bind方法。

### Q: 页面卸载后仍然收到状态更新？
A: 确保在页面onUnload中调用了状态清理方法。

### Q: 状态持久化失败？
A: 检查本地存储权限和存储空间，确保数据格式正确。

## 📚 API参考

### StateManager
- `init()` - 初始化状态管理器
- `getState(path)` - 获取状态
- `setState(path, value)` - 设置状态
- `subscribe(path, callback)` - 监听状态变化
- `unsubscribe(path, callback)` - 取消监听

### UserActions
- `login(token, userInfo)` - 用户登录
- `logout()` - 用户登出
- `updateUserInfo(userInfo)` - 更新用户信息
- `isLoggedIn()` - 检查登录状态

### ChildrenActions
- `setCurrentChild(child)` - 设置当前孩子
- `addChild(child)` - 添加孩子
- `updateChild(childId, updates)` - 更新孩子信息
- `removeChild(childId)` - 删除孩子

### StateMixin
- `createStatePage(config)` - 创建状态管理页面
- `withState(statePaths)` - 状态绑定装饰器
- `withStatePreset(presetName)` - 状态预设装饰器

---

> 📝 **注意**: 本文档描述的是重构后的状态管理系统，确保使用最新版本的utils包。
