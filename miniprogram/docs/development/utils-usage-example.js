// 工具函数使用示例 - 展示如何在页面中使用重构后的utils工具函数
// 遵循MVS规则：统一代码模板，规范使用方式

// 导入需要的工具函数 - 重构后的新导入方式
const { 
  http,           // HTTP请求客户端
  common,         // 通用工具函数
  ui,             // UI交互工具
  navigation,     // 页面导航
  wechat,         // 微信API封装
  format,         // 数据格式化
  validator,      // 数据验证
  storage,        // 存储管理
  constants,      // 常量定义
  userStorage,    // 用户存储
  // 向前兼容的别名
  pageUtils,      // = ui
  utils,          // = common
  validate        // = validator
} = require('../../utils/index.js');

const app = getApp();

// 页面使用示例
Page({
  data: {
    userInfo: null,
    children: [],
    currentChild: null,
    loading: false
  },

  onLoad(options) {
    // 使用ui工具获取页面参数
    const params = ui.getParams(options);
    console.log('页面参数:', params);

    // 设置页面标题
    ui.setTitle('工具函数示例');

    // 初始化页面数据
    this.initPageData();
  },

  async initPageData() {
    try {
      // 显示加载状态
      ui.showLoading('加载中...');

      // 获取用户信息
      await this.loadUserInfo();

      // 获取孩子列表
      await this.loadChildren();

      ui.hideLoading();
      ui.showSuccess('数据加载完成');
    } catch (error) {
      ui.hideLoading();
      ui.showError('数据加载失败');
      console.error('初始化失败:', error);
    }
  },

  async loadUserInfo() {
    try {
      // 使用HTTP客户端发送请求
      const response = await http.get('/api/user/info');
      
      // 使用数据验证
      if (validator.user.isValid(response.data)) {
        this.setData({
          userInfo: response.data
        });
        
        // 保存到本地存储
        userStorage.setUserInfo(response.data);
      } else {
        throw new Error('用户信息格式不正确');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  async loadChildren() {
    try {
      const response = await http.get('/api/children/list');
      
      // 使用数组格式化工具
      const formattedChildren = format.arrayFormat.mapWithIndex(
        response.data,
        (child, index) => ({
          ...child,
          displayName: format.textFormat.truncate(child.name, 10),
          ageText: format.dateFormat.getAge(child.birthday) + '岁'
        })
      );

      this.setData({
        children: formattedChildren,
        currentChild: formattedChildren[0] || null
      });
    } catch (error) {
      console.error('获取孩子列表失败:', error);
      throw error;
    }
  },

  // 防抖搜索示例
  onSearchInput: common.debounce(function(e) {
    const keyword = e.detail.value;
    if (keyword.trim()) {
      this.searchChildren(keyword);
    }
  }, 500),

  async searchChildren(keyword) {
    try {
      ui.showLoading('搜索中...');
      
      const response = await http.get('/api/children/search', {
        keyword: keyword
      });
      
      this.setData({
        children: response.data
      });
      
      ui.hideLoading();
    } catch (error) {
      ui.hideLoading();
      ui.showError('搜索失败');
    }
  },

  // 节流点击示例
  onButtonTap: common.throttle(function() {
    ui.showToast('按钮被点击');
  }, 1000),

  // 微信API使用示例
  async onChooseImage() {
    try {
      // 使用微信媒体API
      const result = await wechat.media.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      if (result.tempFilePaths.length > 0) {
        const filePath = result.tempFilePaths[0];
        
        // 上传图片
        const uploadResult = await http.uploadFile('/api/upload/image', filePath);
        
        ui.showSuccess('图片上传成功');
        console.log('上传结果:', uploadResult);
      }
    } catch (error) {
      ui.showError('图片处理失败');
      console.error('图片处理错误:', error);
    }
  },

  // 导航示例
  onNavigateToDetail() {
    const currentChild = this.data.currentChild;
    if (!currentChild) {
      ui.showToast('请先选择孩子');
      return;
    }

    // 使用导航工具跳转页面
    navigation.navigateTo('/pages/child-detail/child-detail', {
      childId: currentChild.id,
      childName: currentChild.name
    });
  },

  // 智能导航示例
  onNavigateToHome() {
    // 自动判断是否为Tab页面
    navigation.smartNavigate('/pages/home/<USER>');
  },

  // 确认对话框示例
  async onDeleteChild() {
    const confirmed = await ui.showConfirm(
      '确定要删除这个孩子的信息吗？',
      '删除确认'
    );

    if (confirmed) {
      try {
        ui.showLoading('删除中...');
        
        await http.delete(`/api/children/${this.data.currentChild.id}`);
        
        ui.hideLoading();
        ui.showSuccess('删除成功');
        
        // 重新加载数据
        await this.loadChildren();
      } catch (error) {
        ui.hideLoading();
        ui.showError('删除失败');
      }
    }
  },

  // 数据格式化示例
  formatChildInfo(child) {
    return {
      name: format.textFormat.capitalize(child.name),
      age: format.dateFormat.getAge(child.birthday),
      birthday: format.dateFormat.format(child.birthday, 'YYYY-MM-DD'),
      phone: format.phoneFormat.mask(child.phone),
      score: format.numberFormat.toFixed(child.score, 1)
    };
  },

  // 表单验证示例
  onFormSubmit(e) {
    const formData = e.detail.value;
    
    // 验证表单数据
    const errors = [];
    
    if (!validator.text.isRequired(formData.name)) {
      errors.push('姓名不能为空');
    }
    
    if (!validator.phone.isValid(formData.phone)) {
      errors.push('手机号格式不正确');
    }
    
    if (!validator.date.isValid(formData.birthday)) {
      errors.push('生日格式不正确');
    }
    
    if (errors.length > 0) {
      ui.showToast(errors[0]);
      return;
    }
    
    // 提交表单
    this.submitForm(formData);
  },

  async submitForm(formData) {
    try {
      ui.showLoading('提交中...');
      
      const response = await http.post('/api/children/create', formData);
      
      ui.hideLoading();
      ui.showSuccess('提交成功');
      
      // 返回上一页
      navigation.navigateBack();
    } catch (error) {
      ui.hideLoading();
      ui.showError('提交失败');
    }
  },

  // 存储操作示例
  onSaveSettings() {
    const settings = {
      theme: 'light',
      notifications: true,
      language: 'zh-CN'
    };
    
    // 保存用户设置
    userStorage.setUserSettings(settings);
    ui.showSuccess('设置已保存');
  },

  // 页面卸载时清理
  onUnload() {
    // 清理定时器、监听器等
    console.log('页面卸载，清理资源');
  }
});

// 使用说明：
// 1. 重构后的utils包提供了更清晰的模块分工
// 2. 使用新的导入方式可以获得更好的代码提示
// 3. 保留了向前兼容的别名，现有代码可以继续使用
// 4. 建议逐步迁移到新的API，获得更好的功能和性能
