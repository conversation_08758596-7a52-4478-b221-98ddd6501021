// 视频缩略图组件
const { imageManager } = require('../../utils/image-manager');

Component({
  properties: {
    // 缩略图路径
    thumbnail: {
      type: String,
      value: ''
    },
    // 视频类型
    videoType: {
      type: String,
      value: 'course' // 'course' | 'demo'
    },
    // 视频时长
    duration: {
      type: String,
      value: ''
    },
    // 观看次数
    viewCount: {
      type: Number,
      value: 0
    },
    // 是否显示播放按钮
    showPlayButton: {
      type: Boolean,
      value: true
    },
    // 是否显示类型标签
    showTypeBadge: {
      type: Boolean,
      value: true
    },
    // 是否显示观看次数
    showViewCount: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  data: {
    thumbnailPath: '',
    placeholderIcon: '🎥',
    useImageFallback: false
  },

  lifetimes: {
    attached() {
      this.initThumbnail();
    }
  },

  observers: {
    'thumbnail': function(newThumbnail) {
      this.initThumbnail();
    }
  },

  methods: {
    // 初始化缩略图
    initThumbnail() {
      const { thumbnail } = this.data;
      
      if (thumbnail) {
        // 检查是否是我们的图片路径
        if (thumbnail.includes('/images/video-thumb')) {
          // 提取文件名，如 video-thumb1.png -> video-thumb1
          const fileName = thumbnail.split('/').pop().replace('.png', '');
          const placeholderIcon = imageManager.getCSSIcon(fileName);
          
          this.setData({
            thumbnailPath: '', // 不使用图片路径
            placeholderIcon: placeholderIcon,
            useImageFallback: true
          });
        } else {
          // 使用真实的图片路径
          this.setData({
            thumbnailPath: thumbnail,
            useImageFallback: false
          });
        }
      } else {
        // 没有缩略图，使用默认图标
        this.setData({
          thumbnailPath: '',
          placeholderIcon: '🎥',
          useImageFallback: true
        });
      }
    },

    // 图片加载失败处理
    onImageError() {
      console.warn('视频缩略图加载失败，使用CSS图标替代');
      const { thumbnail } = this.data;
      let placeholderIcon = '🎥';
      
      if (thumbnail && thumbnail.includes('/images/video-thumb')) {
        const fileName = thumbnail.split('/').pop().replace('.png', '');
        placeholderIcon = imageManager.getCSSIcon(fileName);
      }
      
      this.setData({
        thumbnailPath: '',
        placeholderIcon: placeholderIcon,
        useImageFallback: true
      });
    },

    // 格式化观看次数
    formatViewCount(count) {
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + '万';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      } else {
        return count.toString();
      }
    }
  }
});
