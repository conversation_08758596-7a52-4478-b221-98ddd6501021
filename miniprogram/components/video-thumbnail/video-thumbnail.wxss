/* 视频缩略图组件样式 */

.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

.thumbnail-background {
  width: 100%;
  height: 100%;
  position: relative;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.course-placeholder {
  background: linear-gradient(135deg, #FF7A45 0%, #FFB366 100%);
}

.demo-placeholder {
  background: linear-gradient(135deg, #4A90E2 0%, #7BB3F0 100%);
}

.placeholder-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}

.placeholder-text {
  font-size: 24rpx;
  opacity: 0.8;
  font-weight: 500;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.play-button:active {
  transform: translate(-50%, -50%) scale(0.9);
  background-color: rgba(0, 0, 0, 0.8);
}

.play-icon {
  color: white;
  font-size: 24rpx;
  margin-left: 4rpx; /* 视觉居中调整 */
}

.video-duration {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  line-height: 1;
}

.video-type-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
  color: white;
}

.video-type-badge.course {
  background-color: var(--primary-color);
}

.video-type-badge.demo {
  background-color: var(--secondary-color);
}

.view-count {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.count-icon {
  font-size: 16rpx;
  margin-right: 4rpx;
}

.count-text {
  line-height: 1;
}

/* 不同尺寸变体 */
.video-thumbnail-container.size-small {
  height: 120rpx;
}

.video-thumbnail-container.size-small .play-button {
  width: 40rpx;
  height: 40rpx;
}

.video-thumbnail-container.size-small .play-icon {
  font-size: 16rpx;
}

.video-thumbnail-container.size-small .placeholder-icon {
  font-size: 32rpx;
}

.video-thumbnail-container.size-small .placeholder-text {
  font-size: 20rpx;
}

.video-thumbnail-container.size-large {
  height: 280rpx;
}

.video-thumbnail-container.size-large .play-button {
  width: 80rpx;
  height: 80rpx;
}

.video-thumbnail-container.size-large .play-icon {
  font-size: 32rpx;
}

.video-thumbnail-container.size-large .placeholder-icon {
  font-size: 64rpx;
}

.video-thumbnail-container.size-large .placeholder-text {
  font-size: 28rpx;
}

/* 悬浮效果 */
.video-thumbnail-container.hover {
  transition: transform 0.2s ease;
}

.video-thumbnail-container.hover:active {
  transform: scale(0.98);
}

/* 圆角变体 */
.video-thumbnail-container.rounded {
  border-radius: 20rpx;
}

/* 阴影效果 */
.video-thumbnail-container.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
