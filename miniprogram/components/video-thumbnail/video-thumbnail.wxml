<!-- 视频缩略图组件 -->
<view class="video-thumbnail-container {{customClass}}" style="{{customStyle}}">
  <!-- 缩略图背景 -->
  <view class="thumbnail-background">
    <!-- 智能图片显示 -->
    <smart-image 
      wx:if="{{thumbnailPath}}"
      src="{{thumbnailPath}}"
      mode="aspectFill"
      custom-class="thumbnail-image"
      bind:error="onImageError"
    ></smart-image>
    
    <!-- CSS图标替代 -->
    <view 
      wx:else
      class="thumbnail-placeholder {{videoType}}-placeholder"
    >
      <view class="placeholder-icon">{{placeholderIcon}}</view>
      <view class="placeholder-text">{{videoType === 'course' ? '课程' : '示范'}}</view>
    </view>
  </view>
  
  <!-- 播放按钮 -->
  <view class="play-button" wx:if="{{showPlayButton}}">
    <view class="play-icon">▶</view>
  </view>
  
  <!-- 视频时长 -->
  <view class="video-duration" wx:if="{{duration}}">{{duration}}</view>
  
  <!-- 视频类型标签 -->
  <view class="video-type-badge {{videoType}}" wx:if="{{showTypeBadge}}">
    {{videoType === 'course' ? '课程' : '示范'}}
  </view>
  
  <!-- 观看次数 -->
  <view class="view-count" wx:if="{{viewCount && showViewCount}}">
    <view class="count-icon">👁</view>
    <text class="count-text">{{formatViewCount(viewCount)}}</text>
  </view>
</view>
