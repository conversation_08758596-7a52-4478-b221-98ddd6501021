// 自定义导航栏组件 - 遵循MVS规则
const app = getApp();
const { childrenActions } = require("../../utils/state-actions.js");
const { avatar, constants } = require("../../utils/index.js");
const { APP_CONFIG } = constants;

Component({
  properties: {
    // 导航栏标题
    title: {
      type: String,
      value: "跳跳星球",
    },
    // 是否显示用户选择器
    showUserSelector: {
      type: Boolean,
      value: true,
    },
    // 是否显示返回按钮
    showBackButton: {
      type: Boolean,
      value: false,
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      value: "#FF7A45",
    },
  },

  data: {
    navbarHeight: 44, // 默认导航栏高度
    statusBarHeight: 20, // 默认状态栏高度
    currentChild: {},
    children: [],
    showChildModal: false,
  },

  lifetimes: {
    attached() {
      this.setNavbarHeight();
      this.loadCurrentChild();
      this.loadChildren();

      // 监听孩子状态变化
      this.currentChildHandler = this.onCurrentChildChange.bind(this);
      this.childrenListHandler = this.onChildrenListChange.bind(this);

      const { stateManager } = require("../../utils/state-manager.js");
      stateManager.subscribe("children.currentChild", this.currentChildHandler);
      stateManager.subscribe("children.childrenList", this.childrenListHandler);
    },

    detached() {
      // 取消监听
      const { stateManager } = require("../../utils/state-manager.js");
      if (this.currentChildHandler) {
        stateManager.unsubscribe(
          "children.currentChild",
          this.currentChildHandler
        );
      }
      if (this.childrenListHandler) {
        stateManager.unsubscribe(
          "children.childrenList",
          this.childrenListHandler
        );
      }
    },
  },

  methods: {
    // 设置导航栏高度（兼容iOS和Android）
    setNavbarHeight() {
      const _this = this;
      wx.getSystemInfo({
        success(res) {
          const ios = !!(res.system.toLowerCase().search("ios") + 1);
          const navbarHeight = res.statusBarHeight + (ios ? 44 : 48);
          _this.setData({
            navbarHeight: navbarHeight,
            statusBarHeight: res.statusBarHeight,
          });

          // 通知父组件导航栏高度
          _this.triggerEvent("navbarHeightChange", {
            navbarHeight: navbarHeight,
            statusBarHeight: res.statusBarHeight,
          });
        },
        fail() {
          console.warn("获取系统信息失败，使用默认导航栏高度");
        },
      });
    },

    // 加载当前选中的孩子
    loadCurrentChild() {
      const currentChild = childrenActions.getCurrentChild();
      if (currentChild) {
        // 处理头像信息
        const avatarInfo = avatar.getAvatarInfo(currentChild.avatar, "child");

        this.setData({
          currentChild: {
            ...currentChild,
            avatar: avatarInfo.url,
            isEmojiAvatar: avatarInfo.isEmoji,
            isDefaultAvatar: avatarInfo.isDefault,
          },
        });
      }
    },

    // 加载孩子列表
    loadChildren() {
      const children = childrenActions.getChildrenList() || [];

      // 处理头像信息
      const childrenWithAvatar = avatar.processAvatarList(
        children,
        "avatar",
        "child"
      );

      this.setData({
        children: childrenWithAvatar,
      });

      // 如果没有选中的孩子且有孩子列表，默认选中第一个
      if (!this.data.currentChild.id && childrenWithAvatar.length > 0) {
        // 使用异步方式选择默认孩子
        this.selectChild({
          currentTarget: { dataset: { child: childrenWithAvatar[0] } },
        }).catch((error) => {
          console.error("默认选择孩子失败:", error);
          // 如果同步失败，至少保证本地状态正确
          childrenActions.setCurrentChild(childrenWithAvatar[0]);
          this.setData({
            currentChild: childrenWithAvatar[0],
          });
        });
      }
    },

    // 显示孩子选择器
    showChildSelector() {
      if (!this.data.showUserSelector) return;

      this.setData({
        showChildModal: true,
      });
    },

    // 隐藏孩子选择器
    hideChildSelector() {
      this.setData({
        showChildModal: false,
      });
    },

    // 阻止事件冒泡
    stopPropagation() {
      // 阻止点击模态框内容时关闭弹窗
    },

    // 选择孩子
    async selectChild(e) {
      const child = e.currentTarget.dataset.child;

      try {
        // 使用新的同步方法选择孩子并同步到服务器
        await childrenActions.selectCurrentChildWithSync(child);

        this.setData({
          currentChild: child,
          showChildModal: false,
        });

        // 通知父组件孩子切换
        this.triggerEvent("childChange", { child });

        wx.showToast({
          title: `已切换到${child.name}`,
          icon: "success",
          duration: 1500,
        });
      } catch (error) {
        console.error("选择孩子失败:", error);

        // 显示错误提示
        wx.showToast({
          title: "切换失败，请重试",
          icon: "error",
          duration: 2000,
        });

        // 关闭弹窗
        this.setData({
          showChildModal: false,
        });
      }
    },

    // 监听当前孩子变化
    onCurrentChildChange(newValue) {
      if (newValue) {
        // 处理头像信息
        const avatarInfo = avatar.getAvatarInfo(newValue.avatar, "child");

        this.setData({
          currentChild: {
            ...newValue,
            avatar: avatarInfo.url,
            isEmojiAvatar: avatarInfo.isEmoji,
            isDefaultAvatar: avatarInfo.isDefault,
          },
        });
      } else {
        this.setData({
          currentChild: {},
        });
      }
    },

    // 监听孩子列表变化
    onChildrenListChange(newValue) {
      const children = newValue || [];

      // 处理头像信息
      const childrenWithAvatar = avatar.processAvatarList(
        children,
        "avatar",
        "child"
      );

      this.setData({
        children: childrenWithAvatar,
      });
    },

    // 跳转到添加孩子页面
    goToAddChild() {
      // 检查孩子数量限制
      if (this.data.children.length >= APP_CONFIG.MAX_CHILDREN) {
        wx.showModal({
          title: "提示",
          content: `最多只能添加${APP_CONFIG.MAX_CHILDREN}个孩子`,
          showCancel: false,
          confirmText: "知道了",
        });
        this.hideChildSelector();
        return;
      }

      this.hideChildSelector();
      wx.navigateTo({
        url: "/pages/child-create/child-create",
      });
    },

    // 返回上一页
    goBack() {
      wx.navigateBack();
    },
  },
});
