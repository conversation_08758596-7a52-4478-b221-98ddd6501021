<!-- 自定义导航栏组件 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content" style="height: {{navbarHeight - statusBarHeight}}px;">
    <!-- 左侧用户选择器 -->
    <view class="user-selector" bindtap="showChildSelector" wx:if="{{showUserSelector}}">
      <text class="user-name">{{currentChild.name || '请选择孩子'}}</text>
      <view class="dropdown-icon">▼</view>
    </view>
    <!-- 左侧返回按钮 -->
    <view class="nav-back" bindtap="goBack" wx:if="{{showBackButton}}">
      <view class="back-icon">‹</view>
    </view>
    <!-- 标题 -->
    <view class="navbar-title">{{title}}</view>
    <!-- 右侧按钮插槽 -->
    <view class="navbar-right">
      <slot name="right"></slot>
    </view>
  </view>
</view>
<!-- 孩子选择弹窗 -->
<view class="child-selector-modal {{showChildModal ? 'show' : ''}}" bindtap="hideChildSelector" wx:if="{{showUserSelector}}">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择孩子</text>
      <view class="modal-close" bindtap="hideChildSelector">×</view>
    </view>
    <view class="modal-body">
      <!-- 孩子列表 -->
      <view class="child-list" wx:if="{{children.length > 0}}">
        <view class="child-item {{currentChild.id === item.id ? 'active' : ''}}" wx:for="{{children}}" wx:key="id" bindtap="selectChild" data-child="{{item}}">
          <view class="child-avatar">
            <image src="{{item.avatar}}" mode="aspectFill"></image>
          </view>
          <view class="child-info">
            <text class="child-name">{{item.name}}</text>
            <text class="child-age">{{item.age}}岁</text>
          </view>
          <view class="child-check" wx:if="{{currentChild.id === item.id}}">✓</view>
        </view>
      </view>
      <!-- 无孩子提示 -->
      <view class="no-child-tip" wx:if="{{children.length === 0}}">
        <view class="tip-icon">👶</view>
        <text class="tip-text">还没有添加孩子信息</text>
        <text class="tip-desc">添加孩子信息后，可以更好地记录成长轨迹</text>
      </view>
      <!-- 添加孩子按钮 -->
      <view class="add-child-btn" bindtap="goToAddChild">
        <text class="add-icon">+</text>
        <text>{{children.length > 0 ? '添加新孩子' : '立即添加孩子'}}</text>
      </view>
    </view>
  </view>
</view>