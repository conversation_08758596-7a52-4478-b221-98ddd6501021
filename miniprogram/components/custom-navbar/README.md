# 自定义导航栏组件 (custom-navbar)

## 功能特性

- ✅ 自动适配iOS和Android设备的导航栏高度
- ✅ 支持用户选择器（孩子切换）
- ✅ 支持返回按钮
- ✅ 支持自定义标题
- ✅ 支持右侧插槽
- ✅ 处理新用户无孩子的情况
- ✅ 完全可复用

## 使用方法

### 1. 在页面JSON中引入组件

```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/custom-navbar"
  }
}
```

### 2. 在WXML中使用

```xml
<!-- 首页样式：显示用户选择器 -->
<custom-navbar 
  title="跳跳星球"
  show-user-selector="{{true}}"
  show-back-button="{{false}}"
  bind:navbarHeightChange="onNavbarHeightChange"
  bind:childChange="onChildChange">
</custom-navbar>

<!-- 详情页样式：显示返回按钮 -->
<custom-navbar 
  title="训练营详情"
  show-user-selector="{{false}}"
  show-back-button="{{true}}"
  bind:navbarHeightChange="onNavbarHeightChange">
  <view slot="right">
    <button>分享</button>
  </view>
</custom-navbar>
```

### 3. 在JS中处理事件

```javascript
Page({
  data: {
    navbarHeight: 44
  },

  // 导航栏高度变化事件
  onNavbarHeightChange(e) {
    this.setData({
      navbarHeight: e.detail.navbarHeight
    });
  },

  // 孩子切换事件
  onChildChange(e) {
    console.log('孩子切换:', e.detail.child);
    // 处理孩子切换后的逻辑
  }
})
```

### 4. 页面内容适配

```xml
<!-- 页面内容需要设置顶部间距 -->
<view class="page-content" style="margin-top: {{navbarHeight}}px;">
  <!-- 页面内容 -->
</view>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '跳跳星球' | 导航栏标题 |
| showUserSelector | Boolean | true | 是否显示用户选择器 |
| showBackButton | Boolean | false | 是否显示返回按钮 |
| backgroundColor | String | '#FF7A45' | 导航栏背景色 |

## 事件说明

| 事件名 | 说明 | 参数 |
|--------|------|------|
| navbarHeightChange | 导航栏高度变化 | {navbarHeight, statusBarHeight} |
| childChange | 孩子切换 | {child} |

## 插槽说明

| 插槽名 | 说明 |
|--------|------|
| right | 右侧内容插槽 |

## 新用户处理

组件会自动处理新用户没有孩子的情况：
- 显示友好的提示信息
- 提供"立即添加孩子"按钮
- 自动跳转到孩子创建页面
