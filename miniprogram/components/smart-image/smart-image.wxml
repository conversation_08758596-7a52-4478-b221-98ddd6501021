<!-- 智能图片组件 - 自动处理图片/CSS图标切换 -->
<view class="smart-image {{customClass}}" style="{{customStyle}}">
  <!-- 图片模式 -->
  <image 
    wx:if="{{resourceType === 'image' && imagePath}}"
    src="{{imagePath}}"
    mode="{{mode}}"
    class="smart-image-img {{imageClass}}"
    style="{{imageStyle}}"
    binderror="onImageError"
    bindload="onImageLoad"
    lazy-load="{{lazyLoad}}"
    show-menu-by-longpress="{{showMenuByLongpress}}"
  ></image>
  
  <!-- CSS图标模式 -->
  <view 
    wx:elif="{{resourceType === 'css' && cssIcon}}"
    class="smart-image-icon {{iconClass}}"
    style="{{iconStyle}}"
  >
    {{cssIcon}}
  </view>
  
  <!-- 加载中状态 -->
  <view 
    wx:elif="{{loading}}"
    class="smart-image-loading {{loadingClass}}"
    style="{{loadingStyle}}"
  >
    <view class="loading-placeholder"></view>
  </view>
  
  <!-- 错误状态 -->
  <view 
    wx:else
    class="smart-image-error {{errorClass}}"
    style="{{errorStyle}}"
  >
    {{fallbackIcon || '❓'}}
  </view>
</view>
