/* 智能图片组件样式 */

.smart-image {
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.smart-image-img {
  width: 100%;
  height: 100%;
  display: block;
}

.smart-image-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: var(--text-secondary);
  background-color: var(--background-color);
  border-radius: 8rpx;
}

.smart-image-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  border-radius: 8rpx;
}

.loading-placeholder {
  width: 60%;
  height: 60%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
}

.smart-image-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-secondary);
  background-color: #f5f5f5;
  border-radius: 8rpx;
  border: 1rpx dashed #ddd;
}

/* 加载动画 */
@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 预设尺寸样式 */
.smart-image.size-small {
  width: 48rpx;
  height: 48rpx;
}

.smart-image.size-medium {
  width: 80rpx;
  height: 80rpx;
}

.smart-image.size-large {
  width: 120rpx;
  height: 120rpx;
}

.smart-image.size-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.smart-image.size-avatar .smart-image-img,
.smart-image.size-avatar .smart-image-icon,
.smart-image.size-avatar .smart-image-loading,
.smart-image.size-avatar .smart-image-error {
  border-radius: 50%;
}

/* 圆形图标 */
.smart-image.round {
  border-radius: 50%;
}

.smart-image.round .smart-image-img,
.smart-image.round .smart-image-icon,
.smart-image.round .smart-image-loading,
.smart-image.round .smart-image-error {
  border-radius: 50%;
}

/* 阴影效果 */
.smart-image.shadow {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 边框效果 */
.smart-image.border {
  border: 1rpx solid var(--border-color);
}

/* 悬浮效果 */
.smart-image.hover {
  transition: transform 0.2s ease;
}

.smart-image.hover:active {
  transform: scale(0.95);
}
