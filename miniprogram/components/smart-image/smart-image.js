// 智能图片组件
const { imageManager } = require('../../utils/image-manager');

Component({
  properties: {
    // 图片名称（在imageManager中定义的key）
    name: {
      type: String,
      value: ''
    },
    // 图片路径（直接指定路径）
    src: {
      type: String,
      value: ''
    },
    // 显示模式：'auto' | 'image' | 'css'
    type: {
      type: String,
      value: 'auto'
    },
    // 图片显示模式
    mode: {
      type: String,
      value: 'aspectFit'
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      value: false
    },
    // 是否显示长按菜单
    showMenuByLongpress: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    // 自定义样式
    customStyle: {
      type: String,
      value: ''
    },
    // 图片样式类
    imageClass: {
      type: String,
      value: ''
    },
    // 图片样式
    imageStyle: {
      type: String,
      value: ''
    },
    // 图标样式类
    iconClass: {
      type: String,
      value: ''
    },
    // 图标样式
    iconStyle: {
      type: String,
      value: ''
    },
    // 加载中样式类
    loadingClass: {
      type: String,
      value: ''
    },
    // 加载中样式
    loadingStyle: {
      type: String,
      value: ''
    },
    // 错误样式类
    errorClass: {
      type: String,
      value: ''
    },
    // 错误样式
    errorStyle: {
      type: String,
      value: ''
    },
    // 备用图标
    fallbackIcon: {
      type: String,
      value: ''
    }
  },

  data: {
    resourceType: 'css', // 'image' | 'css' | 'loading' | 'error'
    imagePath: '',
    cssIcon: '',
    loading: false,
    error: false
  },

  lifetimes: {
    attached() {
      this.initResource();
    }
  },

  observers: {
    'name, src, type': function() {
      this.initResource();
    }
  },

  methods: {
    // 初始化资源
    initResource() {
      const { name, src, type } = this.data;
      
      if (src) {
        // 直接使用src路径
        this.setData({
          resourceType: 'image',
          imagePath: src,
          cssIcon: '',
          loading: false,
          error: false
        });
      } else if (name) {
        // 使用imageManager获取资源
        const resource = imageManager.getResource(name, type);
        
        if (resource.type === 'image') {
          this.setData({
            resourceType: 'image',
            imagePath: resource.path,
            cssIcon: '',
            loading: false,
            error: false
          });
        } else {
          this.setData({
            resourceType: 'css',
            imagePath: '',
            cssIcon: resource.icon,
            loading: false,
            error: false
          });
        }
      } else {
        // 无有效资源
        this.setData({
          resourceType: 'error',
          imagePath: '',
          cssIcon: '',
          loading: false,
          error: true
        });
      }
    },

    // 图片加载成功
    onImageLoad(e) {
      this.triggerEvent('load', e.detail);
    },

    // 图片加载失败
    onImageError(e) {
      console.warn('图片加载失败:', this.data.imagePath);
      
      // 尝试使用CSS图标替代
      const { name } = this.data;
      if (name) {
        const fallbackIcon = imageManager.handleImageError(name);
        this.setData({
          resourceType: 'css',
          imagePath: '',
          cssIcon: fallbackIcon,
          error: false
        });
      } else {
        this.setData({
          resourceType: 'error',
          error: true
        });
      }
      
      this.triggerEvent('error', {
        ...e.detail,
        fallback: true
      });
    }
  }
});
