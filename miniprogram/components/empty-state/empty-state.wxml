<!-- 空状态组件 -->
<view class="empty-state" wx:if="{{show}}">
  <!-- 图标区域 -->
  <view class="empty-icon {{iconClass}}">
    <view class="empty-state-icon {{type}}-icon icon-bounce">
      <!-- 智能显示图片或emoji图标 -->
      <smart-image name="empty-{{type}}" type="css" custom-class="empty-icon-image" icon-class="empty-emoji-icon"></smart-image>
    </view>
  </view>
  <!-- 文字区域 -->
  <view class="empty-content">
    <view class="empty-title">{{title || defaultTitles[type] || '暂无数据'}}</view>
    <view class="empty-description" wx:if="{{description || defaultDescriptions[type]}}">
      {{description || defaultDescriptions[type]}}
    </view>
  </view>
  <!-- 操作按钮 -->
  <view class="empty-actions" wx:if="{{showAction}}">
    <button class="btn btn-primary btn-large" bindtap="onActionTap" wx:if="{{actionText}}">
      {{actionText}}
    </button>
  </view>
</view>