/* 空状态组件样式 */
@import "../../styles/css-icons.wxss";

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  min-height: 400rpx;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-state-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: var(--text-secondary);
  margin: 0 auto;
  position: relative;
}

/* 不同类型的图标颜色 */
.leaderboard-icon {
  background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%);
  color: #FF7A45;
}

.badges-icon {
  background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
  color: #4A90E2;
}

.children-icon {
  background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
  color: #9C27B0;
}

.checkin-icon {
  background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
  color: #4CAF50;
}

.tasks-icon {
  background: linear-gradient(135deg, #FFF8E1 0%, #FFECB3 100%);
  color: #FFC107;
}

.empty-content {
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.empty-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  max-width: 480rpx;
}

.empty-actions {
  width: 100%;
  max-width: 320rpx;
}

/* 动画效果 */
.icon-bounce {
  animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -6rpx, 0);
  }
  70% {
    transform: translate3d(0, -3rpx, 0);
  }
  90% {
    transform: translate3d(0, -1rpx, 0);
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .empty-state {
    padding: 60rpx 32rpx;
    min-height: 320rpx;
  }
  
  .empty-state-icon {
    width: 100rpx;
    height: 100rpx;
    font-size: 50rpx;
  }
  
  .empty-title {
    font-size: 30rpx;
  }
  
  .empty-description {
    font-size: 26rpx;
  }
}
