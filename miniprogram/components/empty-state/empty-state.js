// 空状态组件
const { imageManager } = require("../../utils/image-manager");

Component({
  properties: {
    // 是否显示空状态
    show: {
      type: Boolean,
      value: false,
    },
    // 空状态类型
    type: {
      type: String,
      value: "default",
    },
    // 自定义标题
    title: {
      type: String,
      value: "",
    },
    // 自定义描述
    description: {
      type: String,
      value: "",
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: false,
    },
    // 操作按钮文字
    actionText: {
      type: String,
      value: "",
    },
    // 自定义图标样式类
    iconClass: {
      type: String,
      value: "",
    },
  },

  data: {
    // 默认标题
    defaultTitles: {
      leaderboard: "暂无排行数据",
      badges: "暂无徽章",
      children: "暂无孩子信息",
      checkin: "暂无打卡记录",
      tasks: "暂无任务",
      default: "暂无数据",
    },
    // 默认描述
    defaultDescriptions: {
      leaderboard: "快来完成第一次打卡，登上排行榜吧！",
      badges: "完成更多挑战，获得专属徽章奖励",
      children: "添加孩子信息，开始跳绳挑战之旅",
      checkin: "开始第一次打卡，记录成长足迹",
      tasks: "暂时没有新任务，继续保持运动习惯",
      default: "暂时没有相关数据",
    },
  },

  methods: {
    // 操作按钮点击事件
    onActionTap() {
      this.triggerEvent("action", {
        type: this.data.type,
      });
    },
  },
});
