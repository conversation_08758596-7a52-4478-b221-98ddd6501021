// 测试 request.js 的响应拦截器 URL 显示修复
// 用于验证 API 响应数据的 URL 不再显示为 "unknown"

const { http } = require("../utils/request.js");

/**
 * 测试响应拦截器 URL 显示
 */
async function testResponseInterceptorUrl() {
  console.log("🧪 开始测试响应拦截器 URL 显示修复...");

  try {
    // 测试 GET 请求
    console.log("\n📝 测试 GET 请求:");
    await http.get("/api/test/get", {
      params: { test: "value" }
    });

    // 测试 POST 请求
    console.log("\n📝 测试 POST 请求:");
    await http.post("/api/test/post", {
      name: "test",
      value: 123
    });

    // 测试带认证的请求
    console.log("\n📝 测试带认证的请求:");
    await http.get("/api/user/profile");

    console.log("\n✅ 所有测试完成！请检查控制台日志中的 URL 是否正确显示。");

  } catch (error) {
    console.log("\n⚠️ 测试过程中遇到错误（这是正常的，因为测试服务器可能未启动）:");
    console.log("错误信息:", error.message);
    console.log("✅ 重要的是检查错误日志中的 URL 是否正确显示而不是 'unknown'");
  }
}

/**
 * 测试不同类型的响应处理
 */
async function testDifferentResponseTypes() {
  console.log("\n🧪 测试不同类型的响应处理...");

  // 模拟成功响应
  const mockSuccessResponse = {
    statusCode: 200,
    data: {
      code: 0,
      message: "success",
      data: {
        id: 1,
        name: "test user"
      }
    },
    config: {
      method: "GET",
      url: "http://localhost:8080/api/user/profile",
      data: {},
      header: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      }
    }
  };

  // 模拟业务错误响应
  const mockBusinessErrorResponse = {
    statusCode: 200,
    data: {
      code: 1001,
      message: "用户不存在",
      data: null
    },
    config: {
      method: "GET",
      url: "http://localhost:8080/api/user/999",
      data: {},
      header: {
        "Content-Type": "application/json"
      }
    }
  };

  // 模拟认证错误响应
  const mockAuthErrorResponse = {
    statusCode: 200,
    data: {
      code: 401,
      message: "token已过期",
      data: null
    },
    config: {
      method: "GET",
      url: "http://localhost:8080/api/user/profile",
      data: {},
      header: {
        "Content-Type": "application/json",
        "Authorization": "Bearer expired-token"
      }
    }
  };

  console.log("\n📝 测试成功响应处理:");
  try {
    const successHandler = http.interceptors.response.handlers[0].fulfilled;
    const result = await successHandler(mockSuccessResponse);
    console.log("✅ 成功响应处理正常，返回数据:", result);
  } catch (error) {
    console.log("❌ 成功响应处理异常:", error.message);
  }

  console.log("\n📝 测试业务错误响应处理:");
  try {
    const successHandler = http.interceptors.response.handlers[0].fulfilled;
    await successHandler(mockBusinessErrorResponse);
    console.log("❌ 业务错误响应应该抛出异常");
  } catch (error) {
    console.log("✅ 业务错误响应处理正常，错误信息:", error.message);
  }

  console.log("\n📝 测试认证错误响应处理:");
  try {
    const successHandler = http.interceptors.response.handlers[0].fulfilled;
    await successHandler(mockAuthErrorResponse);
    console.log("❌ 认证错误响应应该抛出异常");
  } catch (error) {
    console.log("✅ 认证错误响应处理正常，错误信息:", error.message);
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log("🚀 开始运行 request.js 响应拦截器测试...");
  console.log("=" * 50);

  await testResponseInterceptorUrl();
  await testDifferentResponseTypes();

  console.log("\n" + "=" * 50);
  console.log("🎉 所有测试运行完成！");
  console.log("\n📋 检查要点:");
  console.log("1. 控制台日志中的 URL 应该显示完整的请求地址，而不是 'unknown'");
  console.log("2. 请求方法（GET/POST等）应该正确显示");
  console.log("3. 响应状态码应该正确显示");
  console.log("4. 错误处理时也应该包含正确的请求信息");
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testResponseInterceptorUrl,
  testDifferentResponseTypes,
  runAllTests
};
