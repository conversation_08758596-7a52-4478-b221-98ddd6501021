// 数据格式转换工具函数 - 前后端数据格式统一
// 遵循MVS规则：统一代码模板，纯函数设计

/**
 * 数据格式转换工具函数集合
 * 处理前后端数据格式差异，确保数据一致性
 */
const dataConverter = {
  /**
   * 性别映射配置
   */
  GENDER_MAP: {
    // 前端 -> 后端
    TO_BACKEND: {
      male: 1,
      female: 2,
      unknown: 0,
      "": 0,
    },
    // 后端 -> 前端
    TO_FRONTEND: {
      1: "male",
      2: "female",
      0: "unknown",
    },
  },

  /**
   * 将前端性别格式转换为后端格式
   * @param {string} frontendGender 前端性别格式 'male' | 'female' | 'unknown' | ''
   * @returns {number} 后端性别格式 0 | 1 | 2
   * @example
   * const backendGender = dataConverter.convertGenderToBackend('male'); // 返回 1
   */
  convertGenderToBackend(frontendGender) {
    if (typeof frontendGender !== "string") {
      return 0; // 默认未知
    }

    const gender = frontendGender.toLowerCase().trim();
    return this.GENDER_MAP.TO_BACKEND[gender] ?? 0;
  },

  /**
   * 将后端性别格式转换为前端格式
   * @param {number} backendGender 后端性别格式 0 | 1 | 2
   * @returns {string} 前端性别格式 'male' | 'female' | 'unknown'
   * @example
   * const frontendGender = dataConverter.convertGenderToFrontend(1); // 返回 'male'
   */
  convertGenderToFrontend(backendGender) {
    if (typeof backendGender !== "number") {
      return "unknown";
    }

    return this.GENDER_MAP.TO_FRONTEND[backendGender] ?? "unknown";
  },

  /**
   * 将前端日期字符串转换为后端格式
   * @param {string} dateString 前端日期字符串 'YYYY-MM-DD'
   * @returns {string} 后端日期格式
   * @example
   * const backendDate = dataConverter.convertDateToBackend('2015-06-15');
   */
  convertDateToBackend(dateString) {
    if (!dateString || typeof dateString !== "string") {
      return "";
    }

    // 验证日期格式 YYYY-MM-DD
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
      return "";
    }

    // 验证日期有效性
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return "";
    }

    return dateString;
  },

  /**
   * 将后端日期格式转换为前端格式
   * @param {string|Date} backendDate 后端日期格式
   * @returns {string} 前端日期字符串 'YYYY-MM-DD'
   * @example
   * const frontendDate = dataConverter.convertDateToFrontend('2015-06-15T00:00:00Z');
   */
  convertDateToFrontend(backendDate) {
    if (!backendDate) {
      return "";
    }

    let date;
    if (typeof backendDate === "string") {
      date = new Date(backendDate);
    } else if (backendDate instanceof Date) {
      date = backendDate;
    } else {
      return "";
    }

    if (isNaN(date.getTime())) {
      return "";
    }

    // 格式化为 YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  },

  /**
   * 计算年龄
   * @param {string|Date} birthDate 出生日期
   * @returns {number} 年龄
   * @example
   * const age = dataConverter.calculateAge('2015-06-15'); // 返回计算出的年龄
   */
  calculateAge(birthDate) {
    if (!birthDate) {
      return 0;
    }

    let date;
    if (typeof birthDate === "string") {
      date = new Date(birthDate);
    } else if (birthDate instanceof Date) {
      date = birthDate;
    } else {
      return 0;
    }

    if (isNaN(date.getTime())) {
      return 0;
    }

    const today = new Date();
    let age = today.getFullYear() - date.getFullYear();
    const monthDiff = today.getMonth() - date.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < date.getDate())
    ) {
      age--;
    }

    return Math.max(0, age);
  },

  /**
   * 将前端孩子数据转换为后端创建请求格式
   * @param {Object} frontendData 前端孩子数据
   * @returns {Object} 后端创建请求格式
   * @example
   * const backendData = dataConverter.convertChildToBackendCreate(frontendChildData);
   */
  convertChildToBackendCreate(frontendData) {
    if (!frontendData || typeof frontendData !== "object") {
      return {};
    }

    return {
      name: frontendData.name || "",
      nickname: frontendData.nickname || "",
      gender: this.convertGenderToBackend(frontendData.gender),
      birth_date: this.convertDateToBackend(
        frontendData.birthday || frontendData.birth_date
      ),
      relation: frontendData.relation || "家长",
    };
  },

  /**
   * 将前端孩子数据转换为后端更新请求格式
   * @param {Object} frontendData 前端孩子数据
   * @returns {Object} 后端更新请求格式
   * @example
   * const backendData = dataConverter.convertChildToBackendUpdate(frontendChildData);
   */
  convertChildToBackendUpdate(frontendData) {
    if (!frontendData || typeof frontendData !== "object") {
      return {};
    }

    const updateData = {};

    // 基本信息字段
    if (frontendData.name !== undefined) {
      updateData.name = frontendData.name;
    }
    if (frontendData.nickname !== undefined) {
      updateData.nickname = frontendData.nickname;
    }
    if (frontendData.gender !== undefined) {
      updateData.gender = this.convertGenderToBackend(frontendData.gender);
    }
    if (
      frontendData.birthday !== undefined ||
      frontendData.birth_date !== undefined
    ) {
      updateData.birth_date = this.convertDateToBackend(
        frontendData.birthday || frontendData.birth_date
      );
    }

    // 学校信息字段
    if (frontendData.school !== undefined) {
      updateData.school = frontendData.school || "";
    }
    if (frontendData.grade !== undefined) {
      updateData.grade = frontendData.grade || "";
    }
    if (frontendData.province !== undefined) {
      updateData.province = frontendData.province || "";
    }
    if (frontendData.city !== undefined) {
      updateData.city = frontendData.city || "";
    }

    // 头像字段处理
    if (frontendData.avatar !== undefined) {
      // 如果是emoji头像，直接使用
      if (frontendData.isEmojiAvatar) {
        updateData.avatar = frontendData.avatar;
      } else if (
        frontendData.avatar &&
        frontendData.avatar.startsWith("http")
      ) {
        // 如果是URL，需要验证格式
        updateData.avatar = frontendData.avatar;
      } else {
        // 其他情况设为空字符串，让后端使用默认头像
        updateData.avatar = "";
      }
    }

    // 必需的偏好设置字段（使用默认值确保验证通过）
    if (frontendData.preferred_difficulty !== undefined) {
      updateData.preferred_difficulty = this.validateDifficultyLevel(
        frontendData.preferred_difficulty
      );
    } else {
      updateData.preferred_difficulty = 1; // 默认新手级别
    }

    if (frontendData.privacy_level !== undefined) {
      updateData.privacy_level = this.validatePrivacyLevel(
        frontendData.privacy_level
      );
    } else {
      updateData.privacy_level = 2; // 默认好友可见
    }

    if (frontendData.show_in_leaderboard !== undefined) {
      updateData.show_in_leaderboard = frontendData.show_in_leaderboard ? 1 : 0;
    } else {
      updateData.show_in_leaderboard = 1; // 默认参与排行榜
    }

    return updateData;
  },

  /**
   * 验证难度级别
   * @param {*} level 难度级别
   * @returns {number} 有效的难度级别 1-5
   */
  validateDifficultyLevel(level) {
    const numLevel = parseInt(level);
    if (numLevel >= 1 && numLevel <= 5) {
      return numLevel;
    }
    return 1; // 默认新手级别
  },

  /**
   * 验证隐私级别
   * @param {*} level 隐私级别
   * @returns {number} 有效的隐私级别 1-3
   */
  validatePrivacyLevel(level) {
    const numLevel = parseInt(level);
    if (numLevel >= 1 && numLevel <= 3) {
      return numLevel;
    }
    return 2; // 默认好友可见
  },

  /**
   * 将后端孩子数据转换为前端显示格式
   * @param {Object} backendData 后端孩子数据
   * @returns {Object} 前端显示格式
   * @example
   * const frontendData = dataConverter.convertChildToFrontend(backendChildData);
   */
  convertChildToFrontend(backendData) {
    if (!backendData || typeof backendData !== "object") {
      return {};
    }

    const frontendData = {
      ...backendData,
      gender: this.convertGenderToFrontend(backendData.gender),
      birthday: this.convertDateToFrontend(backendData.birth_date),
      age: backendData.age || this.calculateAge(backendData.birth_date),

      // 其他统计字段
      bestScore1Min: backendData.best_score_1min || 0,
      bestScoreContinuous: backendData.best_score_continuous || 0,
      skillLevel: backendData.skill_level || 1,
      lastCheckinDate: this.convertDateToFrontend(
        backendData.last_checkin_date
      ),

      // 偏好设置字段
      preferredDifficulty: backendData.preferred_difficulty || 1,
      privacyLevel: backendData.privacy_level || 2,
      showInLeaderboard: backendData.show_in_leaderboard || 1,
      learningGoals: backendData.learning_goals || "",
    };

    // 清理不需要的后端字段
    delete frontendData.birth_date;
    delete frontendData.best_score_1min;
    delete frontendData.best_score_continuous;
    delete frontendData.skill_level;
    delete frontendData.last_checkin_date;
    delete frontendData.preferred_difficulty;
    delete frontendData.privacy_level;
    delete frontendData.show_in_leaderboard;
    delete frontendData.learning_goals;

    return frontendData;
  },

  /**
   * 批量转换后端孩子列表为前端格式
   * @param {Array} backendList 后端孩子列表
   * @returns {Array} 前端格式列表
   * @example
   * const frontendList = dataConverter.convertChildrenListToFrontend(backendChildrenList);
   */
  convertChildrenListToFrontend(backendList) {
    if (!Array.isArray(backendList)) {
      return [];
    }

    return backendList.map((child) => this.convertChildToFrontend(child));
  },
};

module.exports = dataConverter;
