// HTTP依赖注入管理器
// 解决循环依赖问题，提供清晰的依赖注入接口

/**
 * HTTP依赖注入管理器
 * 用于管理HTTP客户端的外部依赖，避免循环依赖问题
 */
class HttpInjectionManager {
  constructor() {
    // 注入的处理函数
    this.handlers = {
      tokenRefresh: null,        // Token刷新处理函数
      loginRedirect: null,       // 登录重定向处理函数
      errorHandler: null,        // 自定义错误处理函数
    };
    
    // 注入状态
    this.isInitialized = false;
  }

  /**
   * 设置Token刷新处理函数
   * @param {Function} handler Token刷新函数
   * @example
   * injectionManager.setTokenRefreshHandler(async (refreshToken) => {
   *   return await authAPI.refreshToken(refreshToken);
   * });
   */
  setTokenRefreshHandler(handler) {
    if (typeof handler !== 'function') {
      throw new Error('Token刷新处理函数必须是一个函数');
    }
    this.handlers.tokenRefresh = handler;
    console.log('✅ Token刷新处理函数已注入');
  }

  /**
   * 设置登录重定向处理函数
   * @param {Function} handler 登录重定向函数
   * @example
   * injectionManager.setLoginRedirectHandler((reason) => {
   *   wx.navigateTo({ url: `/pages/login/login?reason=${reason}` });
   * });
   */
  setLoginRedirectHandler(handler) {
    if (typeof handler !== 'function') {
      throw new Error('登录重定向处理函数必须是一个函数');
    }
    this.handlers.loginRedirect = handler;
    console.log('✅ 登录重定向处理函数已注入');
  }

  /**
   * 设置自定义错误处理函数
   * @param {Function} handler 错误处理函数
   */
  setErrorHandler(handler) {
    if (typeof handler !== 'function') {
      throw new Error('错误处理函数必须是一个函数');
    }
    this.handlers.errorHandler = handler;
    console.log('✅ 自定义错误处理函数已注入');
  }

  /**
   * 批量设置处理函数
   * @param {Object} handlers 处理函数对象
   * @param {Function} handlers.tokenRefresh Token刷新函数
   * @param {Function} handlers.loginRedirect 登录重定向函数
   * @param {Function} handlers.errorHandler 错误处理函数
   */
  setHandlers(handlers) {
    if (handlers.tokenRefresh) {
      this.setTokenRefreshHandler(handlers.tokenRefresh);
    }
    if (handlers.loginRedirect) {
      this.setLoginRedirectHandler(handlers.loginRedirect);
    }
    if (handlers.errorHandler) {
      this.setErrorHandler(handlers.errorHandler);
    }
    
    this.isInitialized = true;
    console.log('✅ HTTP依赖注入初始化完成');
  }

  /**
   * 获取Token刷新处理函数
   * @returns {Function|null} Token刷新函数
   */
  getTokenRefreshHandler() {
    return this.handlers.tokenRefresh;
  }

  /**
   * 获取登录重定向处理函数
   * @returns {Function|null} 登录重定向函数
   */
  getLoginRedirectHandler() {
    return this.handlers.loginRedirect;
  }

  /**
   * 获取错误处理函数
   * @returns {Function|null} 错误处理函数
   */
  getErrorHandler() {
    return this.handlers.errorHandler;
  }

  /**
   * 检查是否已初始化
   * @returns {boolean} 是否已初始化
   */
  isReady() {
    return this.isInitialized && this.handlers.tokenRefresh && this.handlers.loginRedirect;
  }

  /**
   * 重置所有处理函数
   */
  reset() {
    this.handlers = {
      tokenRefresh: null,
      loginRedirect: null,
      errorHandler: null,
    };
    this.isInitialized = false;
    console.log('🔄 HTTP依赖注入已重置');
  }
}

// 创建单例实例
const httpInjectionManager = new HttpInjectionManager();

module.exports = {
  HttpInjectionManager,
  httpInjectionManager,
};
