// Token管理器 - 提供token预刷新和生命周期管理
// 遵循MVS规则：统一代码模板，完善错误处理

const { userStorage } = require("./storage.js");
const { userActions } = require("./state-actions.js");
const authAPI = require("../apis/auth.js");
const { createError, ERROR_CODES } = require("./error.js");

/**
 * Token管理器类
 */
class TokenManager {
  constructor() {
    this.refreshTimer = null;
    this.isRefreshing = false;
    this.preRefreshThreshold = 5 * 60 * 1000; // 5分钟前预刷新
  }

  /**
   * 启动token管理
   */
  start() {
    console.log("🔄 启动Token管理器");
    this._scheduleRefresh();
  }

  /**
   * 停止token管理
   */
  stop() {
    console.log("⏹️ 停止Token管理器");
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * 检查token是否需要刷新
   * @returns {boolean} 是否需要刷新
   */
  shouldRefresh() {
    const token = userStorage.getToken();
    if (!token) {
      return false;
    }

    try {
      // 解析JWT token获取过期时间
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiresAt = payload.exp * 1000; // 转换为毫秒
      const now = Date.now();
      
      // 如果在预刷新阈值内，则需要刷新
      return (expiresAt - now) <= this.preRefreshThreshold;
    } catch (error) {
      console.error("❌ Token解析失败:", error);
      return true; // 解析失败则认为需要刷新
    }
  }

  /**
   * 预刷新token
   */
  async preRefresh() {
    if (this.isRefreshing) {
      console.log("🔄 Token正在刷新中，跳过预刷新");
      return;
    }

    const refreshToken = userStorage.getRefreshToken();
    if (!refreshToken) {
      console.log("🔄 没有refresh token，跳过预刷新");
      return;
    }

    try {
      this.isRefreshing = true;
      console.log("🔄 开始预刷新token");

      const tokenData = await authAPI.refreshToken(refreshToken);
      
      // 更新存储的token
      userStorage.setToken(tokenData.access_token);
      userStorage.setRefreshToken(tokenData.refresh_token);
      
      // 更新状态管理中的token
      const userInfo = userActions.getUserInfo();
      if (userInfo) {
        userActions.login(userInfo, tokenData.access_token, tokenData.refresh_token);
      }

      console.log("✅ Token预刷新成功");
      
      // 重新调度下次刷新
      this._scheduleRefresh();
      
    } catch (error) {
      console.error("❌ Token预刷新失败:", error);
      
      // 预刷新失败不影响用户体验，等待正常的401触发刷新
      this._scheduleRefresh();
      
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 调度下次刷新
   * @private
   */
  _scheduleRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    const token = userStorage.getToken();
    if (!token) {
      return;
    }

    try {
      // 解析JWT token获取过期时间
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiresAt = payload.exp * 1000; // 转换为毫秒
      const now = Date.now();
      
      // 计算预刷新时间点
      const refreshTime = expiresAt - this.preRefreshThreshold;
      const delay = Math.max(0, refreshTime - now);
      
      console.log(`⏰ 调度Token预刷新，${Math.round(delay / 1000)}秒后执行`);
      
      this.refreshTimer = setTimeout(() => {
        this.preRefresh();
      }, delay);
      
    } catch (error) {
      console.error("❌ Token调度失败:", error);
      
      // 解析失败，使用默认间隔重试
      this.refreshTimer = setTimeout(() => {
        this.preRefresh();
      }, 60000); // 1分钟后重试
    }
  }

  /**
   * 手动刷新token
   */
  async manualRefresh() {
    return this.preRefresh();
  }

  /**
   * 获取token剩余时间（秒）
   * @returns {number} 剩余时间，-1表示无效token
   */
  getTokenRemainingTime() {
    const token = userStorage.getToken();
    if (!token) {
      return -1;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiresAt = payload.exp * 1000;
      const now = Date.now();
      
      return Math.max(0, Math.floor((expiresAt - now) / 1000));
    } catch (error) {
      return -1;
    }
  }
}

// 创建全局实例
const tokenManager = new TokenManager();

module.exports = {
  TokenManager,
  tokenManager,
};
