// 头像处理工具函数 - 统一头像显示逻辑
// 遵循MVS规则：统一代码模板，纯函数设计

/**
 * 头像处理工具函数集合
 * 提供用户和孩子头像的统一处理逻辑
 */
const avatar = {
  /**
   * 默认头像配置
   */
  DEFAULT_AVATARS: {
    USER: "/images/avatar_default.png", // 用户默认头像
    CHILD: "/images/avatar_default.png", // 孩子默认头像（统一使用同一个）
  },

  /**
   * 获取用户头像URL
   * @param {string} avatarUrl 用户头像URL
   * @returns {string} 处理后的头像URL
   * @example
   * const userAvatar = avatar.getUserAvatar(user.avatar);
   */
  getUserAvatar(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === "") {
      return this.DEFAULT_AVATARS.USER;
    }
    return avatarUrl;
  },

  /**
   * 获取孩子头像URL
   * @param {string} avatarUrl 孩子头像URL
   * @returns {string} 处理后的头像URL
   * @example
   * const childAvatar = avatar.getChildAvatar(child.avatar);
   */
  getChildAvatar(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === "") {
      return this.DEFAULT_AVATARS.CHILD;
    }
    return avatarUrl;
  },

  /**
   * 检查是否为默认头像
   * @param {string} avatarUrl 头像URL
   * @param {string} type 头像类型 'user' | 'child'
   * @returns {boolean} 是否为默认头像
   * @example
   * const isDefault = avatar.isDefaultAvatar(user.avatar, 'user');
   */
  isDefaultAvatar(avatarUrl, type = "user") {
    if (!avatarUrl || avatarUrl.trim() === "") {
      return true;
    }

    const defaultAvatar =
      type === "child" ? this.DEFAULT_AVATARS.CHILD : this.DEFAULT_AVATARS.USER;

    return avatarUrl === defaultAvatar;
  },

  /**
   * 检查是否为emoji头像（兼容现有逻辑）
   * @param {string} avatarUrl 头像URL
   * @returns {boolean} 是否为emoji头像
   * @example
   * const isEmoji = avatar.isEmojiAvatar(child.avatar);
   */
  isEmojiAvatar(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === "") {
      return false;
    }

    // 检查是否为emoji字符
    const emojiRegex =
      /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    return emojiRegex.test(avatarUrl);
  },

  /**
   * 获取头像显示信息
   * @param {string} avatarUrl 头像URL
   * @param {string} type 头像类型 'user' | 'child'
   * @returns {Object} 头像显示信息
   * @example
   * const avatarInfo = avatar.getAvatarInfo(child.avatar, 'child');
   * // 返回: { url: '...', isDefault: true, isEmoji: false, type: 'child' }
   */
  getAvatarInfo(avatarUrl, type = "user") {
    const processedUrl =
      type === "child"
        ? this.getChildAvatar(avatarUrl)
        : this.getUserAvatar(avatarUrl);

    return {
      url: processedUrl,
      isDefault: this.isDefaultAvatar(avatarUrl, type),
      isEmoji: this.isEmojiAvatar(avatarUrl),
      type: type,
    };
  },

  /**
   * 批量处理头像列表
   * @param {Array} items 包含头像的对象列表
   * @param {string} avatarField 头像字段名，默认为'avatar'
   * @param {string} type 头像类型 'user' | 'child'
   * @returns {Array} 处理后的对象列表
   * @example
   * const processedChildren = avatar.processAvatarList(children, 'avatar', 'child');
   */
  processAvatarList(items, avatarField = "avatar", type = "child") {
    if (!Array.isArray(items)) {
      return [];
    }

    return items.map((item) => {
      const avatarInfo = this.getAvatarInfo(item[avatarField], type);
      return {
        ...item,
        [avatarField]: avatarInfo.url,
        isEmojiAvatar: avatarInfo.isEmoji,
        isDefaultAvatar: avatarInfo.isDefault,
      };
    });
  },
};

module.exports = avatar;
