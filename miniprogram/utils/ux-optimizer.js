// 用户体验优化器
// 提供分场景的用户体验策略和友好的错误提示

/**
 * 用户体验优化器
 * 根据不同场景提供最佳的用户体验策略
 */
class UXOptimizer {
  constructor() {
    // 场景配置
    this.scenarios = {
      // 关键操作：支付、提交重要数据等
      critical: {
        showLoading: true,
        showRetryDialog: true,
        autoRetry: false,
        maxWaitTime: 30000,
        userGuidance: 'detailed'
      },
      // 浏览操作：查看内容、列表等
      browsing: {
        showLoading: false,
        showRetryDialog: false,
        autoRetry: true,
        maxWaitTime: 10000,
        userGuidance: 'minimal'
      },
      // 数据提交：表单提交、设置保存等
      submission: {
        showLoading: true,
        showRetryDialog: true,
        autoRetry: true,
        maxWaitTime: 20000,
        userGuidance: 'moderate'
      },
      // 后台操作：数据同步、预加载等
      background: {
        showLoading: false,
        showRetryDialog: false,
        autoRetry: true,
        maxWaitTime: 5000,
        userGuidance: 'none'
      }
    };

    // 当前活跃的操作
    this.activeOperations = new Map();
  }

  /**
   * 开始操作优化
   * @param {string} operationId 操作ID
   * @param {string} scenario 场景类型
   * @param {Object} options 额外选项
   * @returns {Object} 操作控制器
   */
  startOperation(operationId, scenario = 'browsing', options = {}) {
    const config = { ...this.scenarios[scenario], ...options };
    
    const operation = {
      id: operationId,
      scenario,
      config,
      startTime: Date.now(),
      status: 'running',
      retryCount: 0,
      userNotified: false
    };

    this.activeOperations.set(operationId, operation);

    console.log("🎯 开始操作优化:", {
      operationId,
      scenario,
      config: config
    });

    // 根据配置显示加载状态
    if (config.showLoading) {
      this._showLoading(operationId, scenario);
    }

    return {
      updateProgress: (progress) => this._updateProgress(operationId, progress),
      showRetry: (error) => this._showRetryOption(operationId, error),
      complete: (result) => this._completeOperation(operationId, result),
      fail: (error) => this._failOperation(operationId, error)
    };
  }

  /**
   * 显示加载状态
   */
  _showLoading(operationId, scenario) {
    const messages = {
      critical: "处理中，请稍候...",
      browsing: "加载中...",
      submission: "提交中，请稍候...",
      background: ""
    };

    const message = messages[scenario];
    if (message) {
      wx.showLoading({
        title: message,
        mask: true
      });
    }
  }

  /**
   * 更新进度
   */
  _updateProgress(operationId, progress) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) return;

    operation.progress = progress;
    
    // 根据场景决定是否显示详细进度
    if (operation.config.userGuidance === 'detailed' && progress.message) {
      wx.showLoading({
        title: progress.message,
        mask: true
      });
    }
  }

  /**
   * 显示重试选项
   */
  async _showRetryOption(operationId, error) {
    const operation = this.activeOperations.get(operationId);
    if (!operation || !operation.config.showRetryDialog) {
      return false; // 不显示重试对话框
    }

    const retryMessages = this._getRetryMessages(error, operation.scenario);
    
    return new Promise((resolve) => {
      wx.showModal({
        title: retryMessages.title,
        content: retryMessages.content,
        showCancel: true,
        cancelText: "取消",
        confirmText: "重试",
        success: (res) => {
          if (res.confirm) {
            operation.retryCount++;
            console.log(`🔄 用户选择重试 (第${operation.retryCount}次)`, operationId);
            resolve(true);
          } else {
            console.log("❌ 用户取消重试", operationId);
            resolve(false);
          }
        }
      });
    });
  }

  /**
   * 获取重试消息
   */
  _getRetryMessages(error, scenario) {
    const baseMessages = {
      critical: {
        title: "操作失败",
        content: "重要操作执行失败，是否重试？"
      },
      browsing: {
        title: "加载失败", 
        content: "内容加载失败，是否重试？"
      },
      submission: {
        title: "提交失败",
        content: "数据提交失败，是否重试？"
      },
      background: {
        title: "同步失败",
        content: "后台同步失败，是否重试？"
      }
    };

    let messages = baseMessages[scenario] || baseMessages.browsing;

    // 根据错误类型定制消息
    if (error?.message?.includes('network')) {
      messages = {
        title: "网络异常",
        content: "网络连接异常，请检查网络后重试"
      };
    } else if (error?.message?.includes('timeout')) {
      messages = {
        title: "请求超时",
        content: "请求超时，可能是网络较慢，是否重试？"
      };
    }

    return messages;
  }

  /**
   * 完成操作
   */
  _completeOperation(operationId, result) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) return;

    operation.status = 'completed';
    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;

    console.log("✅ 操作完成:", {
      operationId,
      duration: operation.duration,
      retryCount: operation.retryCount
    });

    // 隐藏加载状态
    if (operation.config.showLoading) {
      wx.hideLoading();
    }

    // 显示成功提示（根据场景）
    this._showSuccessMessage(operation, result);

    // 清理操作记录
    setTimeout(() => {
      this.activeOperations.delete(operationId);
    }, 5000);
  }

  /**
   * 操作失败
   */
  _failOperation(operationId, error) {
    const operation = this.activeOperations.get(operationId);
    if (!operation) return;

    operation.status = 'failed';
    operation.endTime = Date.now();
    operation.duration = operation.endTime - operation.startTime;
    operation.error = error;

    console.log("❌ 操作失败:", {
      operationId,
      duration: operation.duration,
      retryCount: operation.retryCount,
      error: error.message
    });

    // 隐藏加载状态
    if (operation.config.showLoading) {
      wx.hideLoading();
    }

    // 显示错误提示（根据场景和用户指导级别）
    this._showErrorMessage(operation, error);

    // 清理操作记录
    setTimeout(() => {
      this.activeOperations.delete(operationId);
    }, 10000);
  }

  /**
   * 显示成功消息
   */
  _showSuccessMessage(operation, result) {
    if (operation.config.userGuidance === 'none') return;

    const messages = {
      critical: "操作成功完成",
      browsing: "", // 浏览操作通常不需要成功提示
      submission: "提交成功",
      background: ""
    };

    const message = messages[operation.scenario];
    if (message) {
      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
    }
  }

  /**
   * 显示错误消息
   */
  _showErrorMessage(operation, error) {
    if (operation.config.userGuidance === 'none') return;

    let title = "操作失败";
    let icon = 'none';

    // 根据错误类型和场景定制消息
    if (error?.message?.includes('network')) {
      title = "网络连接异常";
    } else if (error?.message?.includes('timeout')) {
      title = "请求超时";
    } else if (error?.message?.includes('token')) {
      title = "登录已过期";
    } else {
      const scenarioMessages = {
        critical: "重要操作失败",
        browsing: "加载失败",
        submission: "提交失败", 
        background: "同步失败"
      };
      title = scenarioMessages[operation.scenario] || title;
    }

    // 根据用户指导级别决定显示方式
    if (operation.config.userGuidance === 'detailed') {
      wx.showModal({
        title: title,
        content: error.message || "请稍后重试",
        showCancel: false,
        confirmText: "确定"
      });
    } else {
      wx.showToast({
        title: title,
        icon: icon,
        duration: 3000
      });
    }
  }

  /**
   * 获取操作统计
   */
  getOperationStats() {
    const operations = Array.from(this.activeOperations.values());
    
    return {
      active: operations.filter(op => op.status === 'running').length,
      completed: operations.filter(op => op.status === 'completed').length,
      failed: operations.filter(op => op.status === 'failed').length,
      total: operations.length,
      details: operations
    };
  }

  /**
   * 清理过期操作
   */
  cleanup(maxAge = 300000) { // 5分钟
    const now = Date.now();
    for (const [id, operation] of this.activeOperations.entries()) {
      if (operation.endTime && (now - operation.endTime) > maxAge) {
        this.activeOperations.delete(id);
      }
    }
  }
}

// 创建单例实例
const uxOptimizer = new UXOptimizer();

module.exports = {
  UXOptimizer,
  uxOptimizer,
};
