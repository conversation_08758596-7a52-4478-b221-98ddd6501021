// 状态管理混入 - 页面状态绑定
// 提供页面级别的状态管理功能，自动同步状态到页面数据
// 遵循微信小程序最佳实践

const {
  stateManager,
  getState,
  setState,
  subscribe,
} = require("./state-manager.js");
const {
  userActions,
  childrenActions,
  businessActions,
  uiActions,
} = require("./state-actions.js");

/**
 * 状态管理混入
 * 为页面提供状态管理功能
 */
const stateMixin = {
  /**
   * 页面状态订阅列表
   */
  _stateSubscriptions: [],

  /**
   * 绑定状态到页面数据
   * @param {Object} stateBindings 状态绑定配置
   *
   * 示例：
   * this.bindState({
   *   'user.isLoggedIn': 'isLoggedIn',
   *   'children.currentChild': 'currentChild',
   *   'children.childrenList': 'children'
   * })
   */
  bindState(stateBindings) {
    if (!stateBindings || typeof stateBindings !== "object") {
      console.warn("bindState: stateBindings must be an object");
      return;
    }

    // 确保订阅列表存在
    if (!this._stateSubscriptions) {
      this._stateSubscriptions = [];
    }

    Object.keys(stateBindings).forEach((statePath) => {
      const dataKey = stateBindings[statePath];

      // 初始化页面数据
      const initialValue = getState(statePath);
      if (initialValue !== null && initialValue !== undefined) {
        this.setData({
          [dataKey]: initialValue,
        });
      }

      // 订阅状态变化
      const unsubscribe = subscribe(statePath, (newValue, oldValue) => {
        if (newValue !== oldValue) {
          this.setData({
            [dataKey]: newValue,
          });

          // 触发状态变化回调
          if (typeof this.onStateChange === "function") {
            this.onStateChange(statePath, newValue, oldValue);
          }
        }
      });

      // 保存取消订阅函数
      this._stateSubscriptions.push(unsubscribe);
    });
  },

  /**
   * 绑定用户状态
   * 快捷方法，绑定常用的用户状态
   */
  bindUserState() {
    this.bindState({
      "user.isLoggedIn": "isLoggedIn",
      "user.userInfo": "userInfo",
      "user.token": "token",
    });
  },

  /**
   * 绑定孩子状态
   * 快捷方法，绑定常用的孩子状态
   */
  bindChildrenState() {
    this.bindState({
      "children.currentChild": "currentChild",
      "children.childrenList": "children",
      "children.loading": "childrenLoading",
    });
  },

  /**
   * 绑定业务数据状态
   * 快捷方法，绑定常用的业务数据状态
   */
  bindBusinessState() {
    this.bindState({
      "business.activeTasks": "activeTasks",
      "business.recentRecords": "recentRecords",
      "business.leaderboard": "leaderboard",
      "business.badges": "badges",
    });
  },

  /**
   * 绑定UI状态
   * 快捷方法，绑定常用的UI状态
   */
  bindUIState() {
    this.bindState({
      "ui.loading": "globalLoading",
      "ui.error": "globalError",
      "ui.toast": "globalToast",
    });
  },

  /**
   * 获取状态值
   * @param {string} path 状态路径
   * @returns {any} 状态值
   */
  getState(path) {
    return getState(path);
  },

  /**
   * 设置状态值
   * @param {string} path 状态路径
   * @param {any} value 新值
   * @param {Object} options 选项
   */
  setState(path, value, options) {
    return setState(path, value, options);
  },

  /**
   * 订阅状态变化
   * @param {string} path 状态路径
   * @param {Function} callback 回调函数
   * @returns {Function} 取消订阅函数
   */
  subscribeState(path, callback) {
    const unsubscribe = subscribe(path, callback);

    // 确保订阅列表存在
    if (!this._stateSubscriptions) {
      this._stateSubscriptions = [];
    }

    this._stateSubscriptions.push(unsubscribe);
    return unsubscribe;
  },

  /**
   * 页面卸载时清理订阅
   */
  onUnload() {
    // 清理所有状态订阅
    if (this._stateSubscriptions && Array.isArray(this._stateSubscriptions)) {
      this._stateSubscriptions.forEach((unsubscribe) => {
        if (typeof unsubscribe === "function") {
          unsubscribe();
        }
      });
      this._stateSubscriptions = [];
    }

    // 调用原始的onUnload方法
    if (this._originalOnUnload) {
      this._originalOnUnload.call(this);
    }
  },
};

/**
 * 创建带状态管理的页面
 * @param {Object} pageConfig 页面配置
 * @returns {Object} 增强后的页面配置
 */
function createStatePage(pageConfig) {
  // 保存原始的onUnload方法
  if (pageConfig.onUnload) {
    pageConfig._originalOnUnload = pageConfig.onUnload;
  }

  // 合并状态管理混入
  const enhancedConfig = {
    ...stateMixin,
    ...pageConfig,

    // 初始化状态订阅列表
    _stateSubscriptions: [],

    // 提供状态操作方法的快捷访问
    userActions,
    childrenActions,
    businessActions,
    uiActions,
  };

  return enhancedConfig;
}

/**
 * 状态管理装饰器
 * 用于快速为页面添加状态管理功能
 */
function withState(stateBindings = {}) {
  return function (pageConfig) {
    const enhancedConfig = createStatePage(pageConfig);

    // 在页面加载时自动绑定状态
    const originalOnLoad = enhancedConfig.onLoad;
    enhancedConfig.onLoad = function (options) {
      // 绑定状态
      if (Object.keys(stateBindings).length > 0) {
        this.bindState(stateBindings);
      }

      // 调用原始的onLoad方法
      if (originalOnLoad) {
        originalOnLoad.call(this, options);
      }
    };

    return enhancedConfig;
  };
}

/**
 * 常用状态绑定预设
 */
const statePresets = {
  // 用户页面预设
  user: {
    "user.isLoggedIn": "isLoggedIn",
    "user.userInfo": "userInfo",
  },

  // 孩子管理页面预设
  children: {
    "children.currentChild": "currentChild",
    "children.childrenList": "children",
    "children.loading": "childrenLoading",
  },

  // 业务数据页面预设
  business: {
    "business.activeTasks": "activeTasks",
    "business.recentRecords": "recentRecords",
  },

  // 完整预设（包含所有常用状态）
  full: {
    "user.isLoggedIn": "isLoggedIn",
    "user.userInfo": "userInfo",
    "children.currentChild": "currentChild",
    "children.childrenList": "children",
    "children.loading": "childrenLoading",
    "business.activeTasks": "activeTasks",
    "business.recentRecords": "recentRecords",
    "ui.loading": "globalLoading",
    "ui.error": "globalError",
  },
};

/**
 * 使用预设创建状态页面
 * @param {string} presetName 预设名称
 * @param {Object} additionalBindings 额外的状态绑定
 */
function withStatePreset(presetName, additionalBindings = {}) {
  const preset = statePresets[presetName] || {};
  const bindings = { ...preset, ...additionalBindings };
  return withState(bindings);
}

// 导出状态管理混入和工具函数
module.exports = {
  stateMixin,
  createStatePage,
  withState,
  withStatePreset,
  statePresets,

  // 便捷访问状态操作
  userActions,
  childrenActions,
  businessActions,
  uiActions,
  getState,
  setState,
};
