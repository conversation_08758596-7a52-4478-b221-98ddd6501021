// 页面导航工具 - 统一的页面跳转和参数处理
// 遵循MVS规则：统一代码模板，规范导航行为

/**
 * 页面导航工具函数
 */
const navigation = {
  /**
   * 导航到页面
   * @param {string} url 页面路径
   * @param {object} params 页面参数
   * @param {object} options 导航选项
   */
  navigateTo(url, params = {}, options = {}) {
    const query = Object.keys(params)
      .filter((key) => params[key] !== null && params[key] !== undefined)
      .map((key) => `${key}=${encodeURIComponent(params[key])}`)
      .join("&");

    const fullUrl = query ? `${url}?${query}` : url;

    wx.navigateTo({
      url: fullUrl,
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  },

  /**
   * 重定向到页面
   * @param {string} url 页面路径
   * @param {object} params 页面参数
   * @param {object} options 导航选项
   */
  redirectTo(url, params = {}, options = {}) {
    const query = Object.keys(params)
      .filter((key) => params[key] !== null && params[key] !== undefined)
      .map((key) => `${key}=${encodeURIComponent(params[key])}`)
      .join("&");

    const fullUrl = query ? `${url}?${query}` : url;

    wx.redirectTo({
      url: fullUrl,
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  },

  /**
   * 切换到Tab页面
   * @param {string} url Tab页面路径
   * @param {object} options 导航选项
   */
  switchTab(url, options = {}) {
    wx.switchTab({
      url,
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  },

  /**
   * 返回上一页
   * @param {number} delta 返回层数
   * @param {object} options 导航选项
   */
  navigateBack(delta = 1, options = {}) {
    wx.navigateBack({
      delta,
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  },

  /**
   * 重新启动到指定页面
   * @param {string} url 页面路径
   * @param {object} params 页面参数
   * @param {object} options 导航选项
   */
  reLaunch(url, params = {}, options = {}) {
    const query = Object.keys(params)
      .filter((key) => params[key] !== null && params[key] !== undefined)
      .map((key) => `${key}=${encodeURIComponent(params[key])}`)
      .join("&");

    const fullUrl = query ? `${url}?${query}` : url;

    wx.reLaunch({
      url: fullUrl,
      success: options.success,
      fail: options.fail,
      complete: options.complete,
    });
  },

  /**
   * 获取当前页面栈
   * @returns {Array} 页面栈
   */
  getCurrentPages() {
    return getCurrentPages();
  },

  /**
   * 获取当前页面
   * @returns {object|null} 当前页面对象
   */
  getCurrentPage() {
    const pages = getCurrentPages();
    return pages.length > 0 ? pages[pages.length - 1] : null;
  },

  /**
   * 获取上一页面
   * @returns {object|null} 上一页面对象
   */
  getPreviousPage() {
    const pages = getCurrentPages();
    return pages.length > 1 ? pages[pages.length - 2] : null;
  },

  /**
   * 检查是否可以返回
   * @returns {boolean} 是否可以返回
   */
  canGoBack() {
    return getCurrentPages().length > 1;
  },

  /**
   * 安全返回（如果不能返回则跳转到首页）
   * @param {string} fallbackUrl 备用页面路径
   * @param {object} options 导航选项
   */
  safeGoBack(fallbackUrl = "/pages/home/<USER>", options = {}) {
    if (this.canGoBack()) {
      this.navigateBack(1, options);
    } else {
      this.switchTab(fallbackUrl, options);
    }
  },

  /**
   * 带确认的返回
   * @param {string} message 确认消息
   * @param {string} title 确认标题
   * @param {object} options 导航选项
   * @returns {Promise<boolean>} 是否执行了返回
   */
  async confirmGoBack(
    message = "确定要离开当前页面吗？",
    title = "提示",
    options = {}
  ) {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content: message,
        success: (res) => {
          if (res.confirm) {
            this.navigateBack(1, options);
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail: () => resolve(false),
      });
    });
  },

  /**
   * 解析URL参数
   * @param {string} url 完整URL
   * @returns {object} 解析后的参数对象
   */
  parseUrlParams(url) {
    const params = {};
    const queryIndex = url.indexOf("?");

    if (queryIndex === -1) return params;

    const queryString = url.substring(queryIndex + 1);
    const pairs = queryString.split("&");

    pairs.forEach((pair) => {
      const [key, value] = pair.split("=");
      if (key) {
        try {
          params[decodeURIComponent(key)] = decodeURIComponent(value || "");
        } catch (e) {
          params[key] = value || "";
        }
      }
    });

    return params;
  },

  /**
   * 构建URL
   * @param {string} path 页面路径
   * @param {object} params 参数对象
   * @returns {string} 完整URL
   */
  buildUrl(path, params = {}) {
    const query = Object.keys(params)
      .filter((key) => params[key] !== null && params[key] !== undefined)
      .map((key) => `${key}=${encodeURIComponent(params[key])}`)
      .join("&");

    return query ? `${path}?${query}` : path;
  },

  /**
   * 预加载页面
   * @param {string} url 页面路径
   */
  preloadPage(url) {
    if (wx.preloadPage) {
      wx.preloadPage({ url });
    }
  },

  /**
   * 获取页面路由信息
   * @returns {object} 路由信息
   */
  getRouteInfo() {
    const currentPage = this.getCurrentPage();
    if (!currentPage) return null;

    return {
      route: currentPage.route,
      options: currentPage.options || {},
      params: this.parseUrlParams(
        currentPage.route +
          "?" +
          Object.keys(currentPage.options || {})
            .map((key) => `${key}=${currentPage.options[key]}`)
            .join("&")
      ),
    };
  },

  /**
   * 检查是否为Tab页面
   * @param {string} url 页面路径
   * @returns {boolean} 是否为Tab页面
   */
  isTabPage(url) {
    // 这里应该根据app.json中的tabBar配置来判断
    // 暂时硬编码常见的Tab页面
    const tabPages = [
      "/pages/home/<USER>",
      "/pages/growth/growth",
      "/pages/profile/profile",
    ];

    return tabPages.some((tabPage) => url.startsWith(tabPage));
  },

  /**
   * 智能导航（自动选择navigateTo或switchTab）
   * @param {string} url 页面路径
   * @param {object} params 页面参数
   * @param {object} options 导航选项
   */
  smartNavigate(url, params = {}, options = {}) {
    if (this.isTabPage(url)) {
      this.switchTab(url, options);
    } else {
      this.navigateTo(url, params, options);
    }
  },

  /**
   * 跳转到登录页面（保存当前页面信息用于登录后跳转）
   * @param {object} options 跳转选项
   * @param {string} options.redirectUrl 登录成功后跳转的目标页面
   * @param {object} options.params 目标页面的参数
   * @param {string} options.from 来源页面标识
   * @param {object} options.loginOptions 登录页面的导航选项
   */
  toLogin(options = {}) {
    const currentPage = this.getCurrentPage();
    const currentRoute = currentPage ? currentPage.route : "";

    // 构建登录页面参数
    const loginParams = {
      from: options.from || currentRoute,
      redirectUrl: options.redirectUrl || "",
      ...options.params,
    };

    // 如果没有指定redirectUrl，尝试从当前页面获取
    if (!loginParams.redirectUrl && currentPage) {
      loginParams.redirectUrl = `/${currentPage.route}`;

      // 添加当前页面的参数
      if (currentPage.options) {
        Object.assign(loginParams, currentPage.options);
      }
    }

    console.log("跳转到登录页面，参数:", loginParams);

    // 跳转到登录页面
    this.navigateTo("/pages/login/login", loginParams, options.loginOptions);
  },

  /**
   * 需要登录时的智能跳转
   * @param {Function} checkLogin 检查登录状态的函数
   * @param {object} options 跳转选项（同toLogin）
   * @returns {boolean} 是否需要跳转到登录页面
   */
  requireLogin(checkLogin, options = {}) {
    if (typeof checkLogin === "function" && !checkLogin()) {
      this.toLogin(options);
      return true;
    }
    return false;
  },
};

// CommonJS导出
module.exports = navigation;
