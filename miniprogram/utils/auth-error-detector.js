// JWT认证错误检测工具
// 提供多层次的认证错误检测机制

const { ERROR_CODES } = require("./constants.js");

/**
 * JWT认证错误检测器
 * 支持HTTP状态码、业务错误码、错误消息多层检测
 */
class AuthErrorDetector {
  constructor() {
    // JWT相关错误码定义
    this.JWT_ERROR_CODES = [10601, 10602, 10603, 10604, 10605, 10606];
    
    // 认证相关的HTTP状态码
    this.AUTH_HTTP_CODES = [401, 403];
    
    // 认证相关的错误消息关键词
    this.AUTH_MESSAGES = [
      'token无效',
      'token已过期', 
      '登录已过期',
      'unauthorized',
      'token expired',
      'invalid token',
      'access denied',
      'authentication failed',
      'login required'
    ];
  }

  /**
   * 检查是否为认证相关错误
   * @param {Object} error 错误对象
   * @param {Object} response 响应对象
   * @returns {Object} 检测结果
   */
  detectAuthError(error, response) {
    const result = {
      isAuthError: false,
      errorType: null,
      errorCode: null,
      httpStatus: null,
      message: null,
      confidence: 0, // 置信度 0-100
      details: {}
    };

    // 1. HTTP状态码检查（最高优先级）
    const httpCheck = this._checkHttpStatus(error, response);
    if (httpCheck.isAuthError) {
      Object.assign(result, httpCheck);
      result.confidence = 95;
      console.log("🔍 HTTP状态码检测到认证错误:", result);
      return result;
    }

    // 2. 业务错误码检查（高优先级）
    const businessCheck = this._checkBusinessCode(error, response);
    if (businessCheck.isAuthError) {
      Object.assign(result, businessCheck);
      result.confidence = 90;
      console.log("🔍 业务错误码检测到认证错误:", result);
      return result;
    }

    // 3. 错误消息检查（中等优先级）
    const messageCheck = this._checkErrorMessage(error, response);
    if (messageCheck.isAuthError) {
      Object.assign(result, messageCheck);
      result.confidence = 70;
      console.log("🔍 错误消息检测到认证错误:", result);
      return result;
    }

    // 4. 综合判断（低优先级）
    const contextCheck = this._checkContext(error, response);
    if (contextCheck.isAuthError) {
      Object.assign(result, contextCheck);
      result.confidence = 50;
      console.log("🔍 上下文检测到可能的认证错误:", result);
      return result;
    }

    console.log("🔍 未检测到认证错误");
    return result;
  }

  /**
   * 检查HTTP状态码
   */
  _checkHttpStatus(error, response) {
    const httpStatus = error?.statusCode || response?.statusCode;
    
    if (this.AUTH_HTTP_CODES.includes(httpStatus)) {
      return {
        isAuthError: true,
        errorType: 'http_status',
        httpStatus: httpStatus,
        message: httpStatus === 401 ? '未授权访问' : '访问被禁止',
        details: { source: 'http_status_code' }
      };
    }
    
    return { isAuthError: false };
  }

  /**
   * 检查业务错误码
   */
  _checkBusinessCode(error, response) {
    const businessCode = response?.data?.code || error?.response?.data?.code;
    
    if (businessCode && this.JWT_ERROR_CODES.includes(businessCode)) {
      return {
        isAuthError: true,
        errorType: 'business_code',
        errorCode: businessCode,
        message: response?.data?.message || error?.response?.data?.message || 'JWT认证失败',
        details: { 
          source: 'business_error_code',
          originalCode: businessCode
        }
      };
    }
    
    return { isAuthError: false };
  }

  /**
   * 检查错误消息
   */
  _checkErrorMessage(error, response) {
    const messages = [
      error?.message,
      response?.data?.message,
      error?.response?.data?.message,
      response?.errMsg
    ].filter(Boolean);

    for (const message of messages) {
      const lowerMessage = message.toLowerCase();
      const matchedKeyword = this.AUTH_MESSAGES.find(keyword => 
        lowerMessage.includes(keyword.toLowerCase())
      );
      
      if (matchedKeyword) {
        return {
          isAuthError: true,
          errorType: 'error_message',
          message: message,
          details: { 
            source: 'error_message',
            matchedKeyword: matchedKeyword,
            originalMessage: message
          }
        };
      }
    }
    
    return { isAuthError: false };
  }

  /**
   * 检查上下文信息
   */
  _checkContext(error, response) {
    // 检查请求URL是否为需要认证的接口
    const url = error?.config?.url || response?.config?.url || '';
    const needsAuth = this._isAuthRequiredUrl(url);
    
    // 检查是否有token相关的头部信息
    const hasAuthHeader = this._hasAuthHeader(error, response);
    
    if (needsAuth && !hasAuthHeader) {
      return {
        isAuthError: true,
        errorType: 'context',
        message: '需要认证的接口缺少认证信息',
        details: { 
          source: 'context_analysis',
          url: url,
          needsAuth: needsAuth,
          hasAuthHeader: hasAuthHeader
        }
      };
    }
    
    return { isAuthError: false };
  }

  /**
   * 判断URL是否需要认证
   */
  _isAuthRequiredUrl(url) {
    const authRequiredPaths = [
      '/api/v1/user',
      '/api/v1/children',
      '/api/v1/growth',
      '/api/v1/checkin',
      '/api/v1/content/user'
    ];
    
    return authRequiredPaths.some(path => url.includes(path));
  }

  /**
   * 检查是否有认证头部
   */
  _hasAuthHeader(error, response) {
    const headers = error?.config?.header || response?.config?.header || {};
    return !!headers.Authorization;
  }

  /**
   * 获取错误的严重程度
   */
  getErrorSeverity(detectionResult) {
    if (!detectionResult.isAuthError) {
      return 'none';
    }
    
    if (detectionResult.confidence >= 90) {
      return 'critical';
    } else if (detectionResult.confidence >= 70) {
      return 'high';
    } else if (detectionResult.confidence >= 50) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * 获取建议的处理方式
   */
  getRecommendedAction(detectionResult) {
    if (!detectionResult.isAuthError) {
      return 'none';
    }
    
    const severity = this.getErrorSeverity(detectionResult);
    
    switch (severity) {
      case 'critical':
        return 'immediate_refresh'; // 立即尝试刷新token
      case 'high':
        return 'refresh_with_retry'; // 刷新token并重试
      case 'medium':
        return 'silent_refresh'; // 静默刷新
      case 'low':
        return 'monitor'; // 监控但不立即处理
      default:
        return 'none';
    }
  }
}

// 创建单例实例
const authErrorDetector = new AuthErrorDetector();

module.exports = {
  AuthErrorDetector,
  authErrorDetector,
};
