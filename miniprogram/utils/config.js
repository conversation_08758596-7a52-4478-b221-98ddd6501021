const ENV = "development"; // development | production

const CONFIG = {
  development: {
    API_BASE_URL: "http://localhost:8080/api/v1",
    DEBUG: true,
    // 开发模式下是否启用网络请求（设为false可以使用模拟数据）
    ENABLE_NETWORK: true, // 如果API无法连接，可以临时设为false
    // 网络请求失败时是否使用模拟数据
    USE_MOCK_ON_ERROR: true,
  },
  production: {
    API_BASE_URL: "https://your-production-api.com/api/v1",
    DEBUG: false,
    ENABLE_NETWORK: true,
    USE_MOCK_ON_ERROR: false,
  },
};

module.exports = CONFIG[ENV];
