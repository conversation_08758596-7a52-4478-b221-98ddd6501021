// 通用工具函数 - 纯函数工具集合
// 遵循MVS规则：统一代码模板，无副作用的纯函数

/**
 * 通用工具函数集合
 */
const common = {
  /**
   * 防抖函数
   * @param {Function} func 要防抖的函数
   * @param {number} wait 等待时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, wait = 300) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   * @param {Function} func 要节流的函数
   * @param {number} limit 限制时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  throttle(func, limit = 300) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  },

  /**
   * 深拷贝
   * @param {any} obj 要拷贝的对象
   * @returns {any} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map((item) => this.deepClone(item));
    if (typeof obj === "object") {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  /**
   * 生成随机字符串
   * @param {number} length 字符串长度
   * @returns {string} 随机字符串
   */
  randomString(length = 8) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * 生成UUID
   * @returns {string} UUID字符串
   */
  uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * 延迟执行
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  },

  /**
   * 安全的JSON字符串化
   * @param {any} obj 要字符串化的对象
   * @param {number} space 缩进空格数
   * @returns {string} JSON字符串
   */
  safeJsonStringify(obj, space = 0) {
    try {
      return JSON.stringify(obj, null, space);
    } catch (error) {
      console.error('JSON stringify failed:', error);
      return '{}';
    }
  },

  /**
   * 安全的JSON解析
   * @param {string} str 要解析的JSON字符串
   * @param {any} defaultValue 解析失败时的默认值
   * @returns {any} 解析后的对象
   */
  safeJsonParse(str, defaultValue = null) {
    try {
      return JSON.parse(str);
    } catch (error) {
      console.error('JSON parse failed:', error);
      return defaultValue;
    }
  },

  /**
   * 检查是否为空值
   * @param {any} value 要检查的值
   * @returns {boolean} 是否为空
   */
  isEmpty(value) {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  },

  /**
   * 获取对象深层属性值
   * @param {object} obj 对象
   * @param {string} path 属性路径，如 'user.profile.name'
   * @param {any} defaultValue 默认值
   * @returns {any} 属性值
   */
  get(obj, path, defaultValue = undefined) {
    const keys = path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return defaultValue;
      }
      result = result[key];
    }
    
    return result !== undefined ? result : defaultValue;
  },

  /**
   * 设置对象深层属性值
   * @param {object} obj 对象
   * @param {string} path 属性路径
   * @param {any} value 要设置的值
   */
  set(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = obj;
    
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
  },

  /**
   * 数组去重
   * @param {Array} arr 数组
   * @param {string|Function} key 去重键或函数
   * @returns {Array} 去重后的数组
   */
  unique(arr, key) {
    if (!Array.isArray(arr)) return [];
    
    if (key) {
      const seen = new Set();
      return arr.filter(item => {
        const value = typeof key === 'function' ? key(item) : item[key];
        if (seen.has(value)) return false;
        seen.add(value);
        return true;
      });
    }
    
    return [...new Set(arr)];
  },

  /**
   * 数组分组
   * @param {Array} arr 数组
   * @param {string|Function} key 分组键或函数
   * @returns {object} 分组后的对象
   */
  groupBy(arr, key) {
    if (!Array.isArray(arr)) return {};
    
    return arr.reduce((groups, item) => {
      const groupKey = typeof key === 'function' ? key(item) : item[key];
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {});
  }
};

// CommonJS导出
module.exports = common;
