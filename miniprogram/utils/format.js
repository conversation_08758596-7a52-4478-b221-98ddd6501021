// 数据格式化工具 - 统一数据格式化处理
// 遵循MVS规则：统一代码模板，便于维护

/**
 * 日期时间格式化
 */
const dateFormat = {
  /**
   * 格式化日期
   * @param {Date|string|number} date 日期
   * @param {string} format 格式字符串
   * @returns {string} 格式化后的日期
   */
  format(date, format = "YYYY-MM-DD") {
    if (!date) return "";

    const d = new Date(date);
    if (isNaN(d.getTime())) return "";

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    const hour = String(d.getHours()).padStart(2, "0");
    const minute = String(d.getMinutes()).padStart(2, "0");
    const second = String(d.getSeconds()).padStart(2, "0");

    return format
      .replace("YYYY", year)
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hour)
      .replace("mm", minute)
      .replace("ss", second);
  },

  /**
   * 相对时间格式化
   * @param {Date|string|number} date 日期
   * @returns {string} 相对时间描述
   */
  relative(date) {
    if (!date) return "";

    const d = new Date(date);
    if (isNaN(d.getTime())) return "";

    const now = new Date();
    const diff = now.getTime() - d.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (seconds < 60) return "刚刚";
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return this.format(date, "MM-DD");
  },

  /**
   * 友好的日期显示
   * @param {Date|string|number} date 日期
   * @returns {string} 友好的日期描述
   */
  friendly(date) {
    if (!date) return "";

    const d = new Date(date);
    if (isNaN(d.getTime())) return "";

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const targetDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());
    const diffDays = Math.floor((targetDate - today) / (24 * 60 * 60 * 1000));

    if (diffDays === 0) return "今天";
    if (diffDays === 1) return "明天";
    if (diffDays === -1) return "昨天";
    if (diffDays > 1 && diffDays <= 7) return `${diffDays}天后`;
    if (diffDays < -1 && diffDays >= -7) return `${Math.abs(diffDays)}天前`;

    return this.format(date, "MM-DD");
  },
};

/**
 * 数字格式化
 */
const numberFormat = {
  /**
   * 格式化数字（添加千分位分隔符）
   * @param {number} num 数字
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的数字
   */
  format(num, decimals = 0) {
    if (num === null || num === undefined || isNaN(num)) return "0";

    return Number(num).toLocaleString("zh-CN", {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  },

  /**
   * 格式化大数字（K, M, B）
   * @param {number} num 数字
   * @returns {string} 格式化后的数字
   */
  abbreviate(num) {
    if (num === null || num === undefined || isNaN(num)) return "0";

    const absNum = Math.abs(num);
    const sign = num < 0 ? "-" : "";

    if (absNum >= 1000000000) {
      return sign + (absNum / 1000000000).toFixed(1) + "B";
    }
    if (absNum >= 1000000) {
      return sign + (absNum / 1000000).toFixed(1) + "M";
    }
    if (absNum >= 1000) {
      return sign + (absNum / 1000).toFixed(1) + "K";
    }

    return num.toString();
  },

  /**
   * 格式化百分比
   * @param {number} num 数字（0-1之间）
   * @param {number} decimals 小数位数
   * @returns {string} 百分比字符串
   */
  percentage(num, decimals = 1) {
    if (num === null || num === undefined || isNaN(num)) return "0%";

    return (num * 100).toFixed(decimals) + "%";
  },

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的文件大小
   */
  fileSize(bytes) {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },
};

/**
 * 文本格式化
 */
const textFormat = {
  /**
   * 截断文本
   * @param {string} text 文本
   * @param {number} length 最大长度
   * @param {string} suffix 后缀
   * @returns {string} 截断后的文本
   */
  truncate(text, length = 50, suffix = "...") {
    if (!text) return "";
    if (text.length <= length) return text;

    return text.substring(0, length) + suffix;
  },

  /**
   * 首字母大写
   * @param {string} text 文本
   * @returns {string} 首字母大写的文本
   */
  capitalize(text) {
    if (!text) return "";
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },

  /**
   * 驼峰命名转换
   * @param {string} text 文本
   * @returns {string} 驼峰命名的文本
   */
  camelCase(text) {
    if (!text) return "";
    return text.replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ""));
  },

  /**
   * 下划线命名转换
   * @param {string} text 文本
   * @returns {string} 下划线命名的文本
   */
  snakeCase(text) {
    if (!text) return "";
    return text
      .replace(/([A-Z])/g, "_$1")
      .toLowerCase()
      .replace(/^_/, "");
  },

  /**
   * 隐藏敏感信息
   * @param {string} text 文本
   * @param {number} start 开始位置
   * @param {number} end 结束位置
   * @param {string} mask 掩码字符
   * @returns {string} 隐藏后的文本
   */
  mask(text, start = 3, end = 4, mask = "*") {
    if (!text || text.length <= start + end) return text;

    const maskLength = text.length - start - end;
    const maskStr = mask.repeat(maskLength);

    return (
      text.substring(0, start) + maskStr + text.substring(text.length - end)
    );
  },
};

/**
 * 手机号格式化
 */
const phoneFormat = {
  /**
   * 格式化手机号（添加空格）
   * @param {string} phone 手机号
   * @returns {string} 格式化后的手机号
   */
  format(phone) {
    if (!phone) return "";
    const cleaned = phone.replace(/\D/g, "");

    if (cleaned.length === 11) {
      return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, "$1 $2 $3");
    }

    return phone;
  },

  /**
   * 隐藏手机号中间4位
   * @param {string} phone 手机号
   * @returns {string} 隐藏后的手机号
   */
  hide(phone) {
    if (!phone) return "";
    return textFormat.mask(phone, 3, 4, "*");
  },
};

/**
 * 金额格式化
 */
const moneyFormat = {
  /**
   * 格式化金额
   * @param {number} amount 金额
   * @param {string} currency 货币符号
   * @param {number} decimals 小数位数
   * @returns {string} 格式化后的金额
   */
  format(amount, currency = "¥", decimals = 2) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return currency + "0.00";
    }

    return currency + numberFormat.format(amount, decimals);
  },

  /**
   * 格式化积分
   * @param {number} points 积分
   * @returns {string} 格式化后的积分
   */
  points(points) {
    if (points === null || points === undefined || isNaN(points)) {
      return "0积分";
    }

    return numberFormat.abbreviate(points) + "积分";
  },
};

/**
 * URL参数格式化
 */
const urlFormat = {
  /**
   * 对象转URL参数
   * @param {object} params 参数对象
   * @returns {string} URL参数字符串
   */
  stringify(params) {
    if (!params || typeof params !== "object") return "";

    return Object.keys(params)
      .filter((key) => params[key] !== null && params[key] !== undefined)
      .map(
        (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
      )
      .join("&");
  },

  /**
   * URL参数转对象
   * @param {string} search 参数字符串
   * @returns {object} 参数对象
   */
  parse(search) {
    if (!search) return {};

    const params = {};
    const searchParams = search.startsWith("?") ? search.slice(1) : search;

    searchParams.split("&").forEach((param) => {
      const [key, value] = param.split("=");
      if (key) {
        params[decodeURIComponent(key)] = decodeURIComponent(value || "");
      }
    });

    return params;
  },
};

/**
 * 数组格式化
 */
const arrayFormat = {
  /**
   * 数组去重
   * @param {Array} arr 数组
   * @param {string} key 对象数组的去重键
   * @returns {Array} 去重后的数组
   */
  unique(arr, key) {
    if (!Array.isArray(arr)) return [];

    if (key) {
      const seen = new Set();
      return arr.filter((item) => {
        const value = item[key];
        if (seen.has(value)) return false;
        seen.add(value);
        return true;
      });
    }

    return [...new Set(arr)];
  },

  /**
   * 数组分组
   * @param {Array} arr 数组
   * @param {string|function} key 分组键或函数
   * @returns {object} 分组后的对象
   */
  groupBy(arr, key) {
    if (!Array.isArray(arr)) return {};

    return arr.reduce((groups, item) => {
      const groupKey = typeof key === "function" ? key(item) : item[key];
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {});
  },
};

// 导出所有格式化工具
module.exports = {
  dateFormat,
  numberFormat,
  textFormat,
  phoneFormat,
  moneyFormat,
  urlFormat,
  arrayFormat,
};
