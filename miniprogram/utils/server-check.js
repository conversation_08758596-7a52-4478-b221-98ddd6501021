// API服务器状态检查工具
// 避免循环报错，提供友好的服务器状态提示

const { APP_CONFIG } = require('./constants.js');

class ServerChecker {
  constructor() {
    this.isServerOnline = false;
    this.lastCheckTime = 0;
    this.checkInterval = 30000; // 30秒检查一次
    this.hasShownOfflineWarning = false;
  }

  /**
   * 检查服务器状态
   */
  async checkServerStatus() {
    const now = Date.now();
    
    // 避免频繁检查
    if (now - this.lastCheckTime < this.checkInterval) {
      return this.isServerOnline;
    }

    this.lastCheckTime = now;

    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${APP_CONFIG.API_BASE_URL}/health`,
          method: 'GET',
          timeout: 3000,
          success: resolve,
          fail: reject
        });
      });

      this.isServerOnline = response.statusCode === 200;
      
      if (this.isServerOnline) {
        this.hasShownOfflineWarning = false;
        console.log('✅ API服务器在线');
      }
      
    } catch (error) {
      this.isServerOnline = false;
      
      // 只显示一次离线警告
      if (!this.hasShownOfflineWarning) {
        this.hasShownOfflineWarning = true;
        console.warn('⚠️ API服务器离线，请启动后端服务器');
        
        // 在开发环境显示友好提示
        if (APP_CONFIG.DEBUG) {
          wx.showModal({
            title: '开发提示',
            content: 'API服务器未启动，请在终端运行后端服务器\n\n部分功能可能无法正常使用',
            showCancel: false,
            confirmText: '知道了'
          });
        }
      }
    }

    return this.isServerOnline;
  }

  /**
   * 获取服务器状态
   */
  getServerStatus() {
    return {
      isOnline: this.isServerOnline,
      lastCheckTime: this.lastCheckTime
    };
  }

  /**
   * 重置警告状态
   */
  resetWarningState() {
    this.hasShownOfflineWarning = false;
  }
}

// 创建全局实例
const serverChecker = new ServerChecker();

// 导出
module.exports = {
  ServerChecker,
  serverChecker,
  
  // 便捷方法
  checkServer: () => serverChecker.checkServerStatus(),
  isServerOnline: () => serverChecker.isServerOnline,
  getServerStatus: () => serverChecker.getServerStatus()
};
