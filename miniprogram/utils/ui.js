// UI交互工具 - 统一的用户界面交互函数
// 遵循MVS规则：统一代码模板，一致的用户体验

/**
 * 加载状态管理器
 */
class LoadingManager {
  constructor() {
    this.loadingCount = 0;
    this.loadingTitle = "加载中...";
  }

  /**
   * 显示加载状态
   * @param {string} title 加载提示文字
   */
  show(title = this.loadingTitle) {
    if (this.loadingCount === 0) {
      wx.showLoading({
        title,
        mask: true,
      });
    }
    this.loadingCount++;
  }

  /**
   * 隐藏加载状态
   */
  hide() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      wx.hideLoading();
    }
  }

  /**
   * 强制隐藏加载状态
   */
  forceHide() {
    this.loadingCount = 0;
    wx.hideLoading();
  }
}

// 创建全局加载管理器实例
const loadingManager = new LoadingManager();

/**
 * UI交互工具函数
 */
const ui = {
  /**
   * 获取页面参数
   * @param {object} options 页面options参数
   * @returns {object} 解析后的参数对象
   */
  getParams(options) {
    const params = {};
    for (const key in options) {
      if (options.hasOwnProperty(key)) {
        try {
          params[key] = JSON.parse(decodeURIComponent(options[key]));
        } catch (e) {
          params[key] = decodeURIComponent(options[key]);
        }
      }
    }
    return params;
  },

  /**
   * 设置页面标题
   * @param {string} title 页面标题
   */
  setTitle(title) {
    wx.setNavigationBarTitle({ title });
  },

  /**
   * 显示加载提示
   * @param {string} title 加载提示文字
   */
  showLoading(title = "加载中...") {
    loadingManager.show(title);
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    loadingManager.hide();
  },

  /**
   * 强制隐藏所有加载提示
   */
  forceHideLoading() {
    loadingManager.forceHide();
  },

  /**
   * 显示成功提示
   * @param {string} title 提示文字
   * @param {number} duration 显示时长
   */
  showSuccess(title, duration = 2000) {
    wx.showToast({
      title,
      icon: "success",
      duration,
    });
  },

  /**
   * 显示错误提示
   * @param {string} title 提示文字
   * @param {number} duration 显示时长
   */
  showError(title, duration = 2000) {
    wx.showToast({
      title,
      icon: "error",
      duration,
    });
  },

  /**
   * 显示普通提示
   * @param {string} title 提示文字
   * @param {number} duration 显示时长
   */
  showToast(title, duration = 2000) {
    wx.showToast({
      title,
      icon: "none",
      duration,
    });
  },

  /**
   * 显示确认对话框
   * @param {string} content 对话框内容
   * @param {string} title 对话框标题
   * @param {object} options 其他选项
   * @returns {Promise<boolean>} 用户是否确认
   */
  showConfirm(content, title = "提示", options = {}) {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        confirmText: options.confirmText || "确定",
        cancelText: options.cancelText || "取消",
        confirmColor: options.confirmColor || "#FF7A45",
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false),
      });
    });
  },

  /**
   * 显示操作菜单
   * @param {Array<string>} itemList 菜单项列表
   * @param {object} options 其他选项
   * @returns {Promise<number>} 用户选择的索引，-1表示取消
   */
  showActionSheet(itemList, options = {}) {
    return new Promise((resolve) => {
      wx.showActionSheet({
        itemList,
        itemColor: options.itemColor || "#000000",
        success: (res) => resolve(res.tapIndex),
        fail: () => resolve(-1),
      });
    });
  },

  /**
   * 显示输入框
   * @param {string} title 输入框标题
   * @param {object} options 其他选项
   * @returns {Promise<string|null>} 用户输入的内容，null表示取消
   */
  showInput(title, options = {}) {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content: options.content || "",
        editable: true,
        placeholderText: options.placeholder || "请输入",
        confirmText: options.confirmText || "确定",
        cancelText: options.cancelText || "取消",
        success: (res) => {
          if (res.confirm) {
            resolve(res.content || "");
          } else {
            resolve(null);
          }
        },
        fail: () => resolve(null),
      });
    });
  },

  /**
   * 显示下拉刷新
   */
  showPullDownRefresh() {
    wx.startPullDownRefresh();
  },

  /**
   * 停止下拉刷新
   */
  stopPullDownRefresh() {
    wx.stopPullDownRefresh();
  },

  /**
   * 显示页面加载状态
   * @param {boolean} show 是否显示
   */
  showPageLoading(show = true) {
    if (show) {
      wx.showNavigationBarLoading();
    } else {
      wx.hideNavigationBarLoading();
    }
  },

  /**
   * 设置导航栏颜色
   * @param {string} frontColor 前景颜色
   * @param {string} backgroundColor 背景颜色
   */
  setNavigationBarColor(frontColor = "#ffffff", backgroundColor = "#FF7A45") {
    wx.setNavigationBarColor({
      frontColor,
      backgroundColor,
    });
  },

  /**
   * 显示分享菜单
   * @param {object} options 分享选项
   */
  showShareMenu(options = {}) {
    wx.showShareMenu({
      withShareTicket: options.withShareTicket || false,
      menus: options.menus || ["shareAppMessage", "shareTimeline"],
    });
  },

  /**
   * 隐藏分享菜单
   */
  hideShareMenu() {
    wx.hideShareMenu();
  },

  /**
   * 设置页面背景色
   * @param {string} backgroundColor 背景颜色
   * @param {string} backgroundColorTop 顶部背景颜色
   * @param {string} backgroundColorBottom 底部背景颜色
   */
  setBackgroundColor(backgroundColor, backgroundColorTop, backgroundColorBottom) {
    wx.setBackgroundColor({
      backgroundColor: backgroundColor || "#ffffff",
      backgroundColorTop: backgroundColorTop || backgroundColor || "#ffffff",
      backgroundColorBottom: backgroundColorBottom || backgroundColor || "#ffffff",
    });
  },

  /**
   * 震动反馈
   * @param {string} type 震动类型：light, medium, heavy
   */
  vibrateShort(type = "medium") {
    if (wx.vibrateShort) {
      wx.vibrateShort({ type });
    }
  },

  /**
   * 长震动
   */
  vibrateLong() {
    if (wx.vibrateLong) {
      wx.vibrateLong();
    }
  }
};

// 导出加载管理器和UI工具
module.exports = {
  ...ui,
  LoadingManager,
  loadingManager
};
