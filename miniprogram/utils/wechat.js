// 微信API封装 - 统一微信小程序API调用
// 遵循MVS规则：统一代码模板，完善错误处理

const { WECHAT, ERROR_CODES } = require("./constants.js");
const { handleWechatError, createError } = require("./error.js");

/**
 * 微信登录相关API
 */
const auth = {
  /**
   * 微信登录
   * @returns {Promise<string>} 登录凭证code
   */
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            resolve(res.code);
          } else {
            reject(
              createError(ERROR_CODES.WECHAT_LOGIN_FAILED, "获取登录凭证失败")
            );
          }
        },
        fail: (error) => {
          reject(handleWechatError(error, "wx.login"));
        },
      });
    });
  },

  /**
   * 获取用户信息
   * @returns {Promise<object>} 用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: "用于完善用户资料",
        success: (res) => {
          resolve(res.userInfo);
        },
        fail: (error) => {
          reject(handleWechatError(error, "wx.getUserProfile"));
        },
      });
    });
  },

  /**
   * 检查登录状态
   * @returns {Promise<boolean>} 是否有效
   */
  checkSession() {
    return new Promise((resolve) => {
      wx.checkSession({
        success: () => resolve(true),
        fail: () => resolve(false),
      });
    });
  },
};

/**
 * 权限相关API
 */
const permission = {
  /**
   * 获取用户授权设置
   * @returns {Promise<object>} 授权设置
   */
  getSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => resolve(res.authSetting),
        fail: (error) => reject(handleWechatError(error, "wx.getSetting")),
      });
    });
  },

  /**
   * 打开授权设置页面
   * @returns {Promise<object>} 授权结果
   */
  openSetting() {
    return new Promise((resolve, reject) => {
      wx.openSetting({
        success: (res) => resolve(res.authSetting),
        fail: (error) => reject(handleWechatError(error, "wx.openSetting")),
      });
    });
  },

  /**
   * 请求用户授权
   * @param {string} scope 权限范围
   * @returns {Promise<boolean>} 是否授权成功
   */
  async authorize(scope) {
    try {
      const settings = await this.getSetting();

      // 如果已经授权，直接返回true
      if (settings[scope]) {
        return true;
      }

      // 如果明确拒绝过，引导用户到设置页面
      if (settings[scope] === false) {
        const result = await wx.showModal({
          title: "需要授权",
          content: "请在设置中开启相关权限",
          confirmText: "去设置",
          cancelText: "取消",
        });

        if (result.confirm) {
          const newSettings = await this.openSetting();
          return !!newSettings[scope];
        }
        return false;
      }

      // 首次请求授权
      return new Promise((resolve) => {
        wx.authorize({
          scope,
          success: () => resolve(true),
          fail: () => resolve(false),
        });
      });
    } catch (error) {
      console.error("Authorization error:", error);
      return false;
    }
  },
};

/**
 * 媒体相关API
 */
const media = {
  /**
   * 选择图片
   * @param {object} options 选择选项
   * @returns {Promise<Array>} 图片路径数组
   */
  chooseImage(options = {}) {
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: options.count || 1,
        sizeType: options.sizeType || ["original", "compressed"],
        sourceType: options.sourceType || ["album", "camera"],
        success: (res) => resolve(res.tempFilePaths),
        fail: (error) => reject(handleWechatError(error, "wx.chooseImage")),
      });
    });
  },

  /**
   * 预览图片
   * @param {Array} urls 图片URL数组
   * @param {number} current 当前显示图片索引
   */
  previewImage(urls, current = 0) {
    wx.previewImage({
      urls,
      current: typeof current === "number" ? urls[current] : current,
    });
  },

  /**
   * 保存图片到相册
   * @param {string} filePath 图片路径
   * @returns {Promise<void>}
   */
  async saveImageToPhotosAlbum(filePath) {
    // 检查权限
    const hasPermission = await permission.authorize(WECHAT.ALBUM_SCOPE);
    if (!hasPermission) {
      throw createError(
        ERROR_CODES.WECHAT_AUTH_DENIED,
        "需要相册权限才能保存图片"
      );
    }

    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          wx.showToast({
            title: "保存成功",
            icon: "success",
          });
          resolve();
        },
        fail: (error) =>
          reject(handleWechatError(error, "wx.saveImageToPhotosAlbum")),
      });
    });
  },
};

/**
 * 位置相关API
 */
const location = {
  /**
   * 获取当前位置
   * @param {string} type 坐标类型
   * @returns {Promise<object>} 位置信息
   */
  async getLocation(type = "wgs84") {
    // 检查权限
    const hasPermission = await permission.authorize(WECHAT.LOCATION_SCOPE);
    if (!hasPermission) {
      throw createError(ERROR_CODES.WECHAT_AUTH_DENIED, "需要位置权限");
    }

    return new Promise((resolve, reject) => {
      wx.getLocation({
        type,
        success: (res) => resolve(res),
        fail: (error) => reject(handleWechatError(error, "wx.getLocation")),
      });
    });
  },

  /**
   * 选择位置
   * @returns {Promise<object>} 选择的位置信息
   */
  chooseLocation() {
    return new Promise((resolve, reject) => {
      wx.chooseLocation({
        success: (res) => resolve(res),
        fail: (error) => reject(handleWechatError(error, "wx.chooseLocation")),
      });
    });
  },
};

/**
 * 分享相关API
 */
const share = {
  /**
   * 分享到朋友圈
   * @param {object} options 分享选项
   */
  shareTimeline(options = {}) {
    return {
      title: options.title || "跳跳星球",
      path: options.path || "/pages/home/<USER>",
      imageUrl: options.imageUrl || "",
    };
  },

  /**
   * 分享给朋友
   * @param {object} options 分享选项
   */
  shareAppMessage(options = {}) {
    return {
      title: options.title || "跳跳星球",
      path: options.path || "/pages/home/<USER>",
      imageUrl: options.imageUrl || "",
    };
  },
};

/**
 * 系统相关API
 */
const system = {
  /**
   * 获取系统信息
   * @returns {Promise<object>} 系统信息
   */
  getSystemInfo() {
    return new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: (res) => resolve(res),
        fail: (error) => reject(handleWechatError(error, "wx.getSystemInfo")),
      });
    });
  },

  /**
   * 获取网络类型
   * @returns {Promise<string>} 网络类型
   */
  getNetworkType() {
    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => resolve(res.networkType),
        fail: (error) => reject(handleWechatError(error, "wx.getNetworkType")),
      });
    });
  },

  /**
   * 设置剪贴板内容
   * @param {string} data 要复制的内容
   * @returns {Promise<void>}
   */
  setClipboardData(data) {
    return new Promise((resolve, reject) => {
      wx.setClipboardData({
        data,
        success: () => {
          wx.showToast({
            title: "复制成功",
            icon: "success",
          });
          resolve();
        },
        fail: (error) =>
          reject(handleWechatError(error, "wx.setClipboardData")),
      });
    });
  },
};

// 导航功能已移至专用的navigation.js模块

// 导出所有模块（移除navigation，使用专用的navigation.js）
module.exports = {
  auth,
  permission,
  media,
  location,
  share,
  system,
};
