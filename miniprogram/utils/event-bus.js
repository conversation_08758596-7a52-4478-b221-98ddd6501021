// 全局事件总线
// 用于页面间通信和状态同步

class EventBus {
  constructor() {
    this.events = new Map();
    this.storage_prefix = 'event_bus_';
  }

  /**
   * 监听事件
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   */
  on(eventName, callback) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, []);
    }
    this.events.get(eventName).push(callback);
    console.log(`📡 监听事件: ${eventName}`);
  }

  /**
   * 移除事件监听
   * @param {string} eventName 事件名称
   * @param {function} callback 回调函数
   */
  off(eventName, callback) {
    if (this.events.has(eventName)) {
      const callbacks = this.events.get(eventName);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
        console.log(`📡 移除事件监听: ${eventName}`);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  emit(eventName, data) {
    console.log(`📡 触发事件: ${eventName}`, data);
    
    // 触发内存中的监听器
    if (this.events.has(eventName)) {
      const callbacks = this.events.get(eventName);
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件回调执行失败: ${eventName}`, error);
        }
      });
    }

    // 同时保存到存储中，供其他页面检查
    this.saveEventToStorage(eventName, data);
  }

  /**
   * 保存事件到存储
   * @param {string} eventName 事件名称
   * @param {any} data 事件数据
   */
  saveEventToStorage(eventName, data) {
    const eventData = {
      name: eventName,
      data: data,
      timestamp: Date.now()
    };

    try {
      wx.setStorageSync(`${this.storage_prefix}${eventName}`, eventData);
    } catch (error) {
      console.error('保存事件到存储失败:', error);
    }
  }

  /**
   * 从存储中检查事件
   * @param {string} eventName 事件名称
   * @param {number} maxAge 最大有效期（毫秒）
   */
  checkEventFromStorage(eventName, maxAge = 5 * 60 * 1000) {
    try {
      const eventData = wx.getStorageSync(`${this.storage_prefix}${eventName}`);
      if (eventData && eventData.timestamp) {
        const age = Date.now() - eventData.timestamp;
        if (age <= maxAge) {
          return eventData;
        } else {
          // 事件过期，清除
          wx.removeStorageSync(`${this.storage_prefix}${eventName}`);
        }
      }
    } catch (error) {
      console.error('从存储检查事件失败:', error);
    }
    return null;
  }

  /**
   * 清除存储中的事件
   * @param {string} eventName 事件名称
   */
  clearEventFromStorage(eventName) {
    try {
      wx.removeStorageSync(`${this.storage_prefix}${eventName}`);
      console.log(`📡 清除存储事件: ${eventName}`);
    } catch (error) {
      console.error('清除存储事件失败:', error);
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    this.events.clear();
    console.log('📡 清除所有事件监听器');
  }

  /**
   * 获取所有事件名称
   */
  getEventNames() {
    return Array.from(this.events.keys());
  }
}

// 创建全局实例
const eventBus = new EventBus();

// 定义常用事件名称
const EVENT_NAMES = {
  CHECKIN_SUCCESS: 'checkin_success',
  CHECKIN_FAILED: 'checkin_failed',
  DATA_REFRESH: 'data_refresh',
  USER_LOGIN: 'user_login',
  CHILD_CHANGED: 'child_changed'
};

module.exports = {
  EventBus,
  eventBus,
  EVENT_NAMES
};
