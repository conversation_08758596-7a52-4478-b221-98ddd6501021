// 加载状态管理器
// 处理网络请求的加载状态，包括超时、重试等

class LoadingManager {
  constructor() {
    this.loadingStates = new Map(); // 存储各个页面的加载状态
    this.defaultTimeout = 15000; // 默认超时时间15秒
    this.retryCount = 3; // 默认重试次数
  }

  /**
   * 开始加载
   * @param {string} key 加载标识
   * @param {Object} options 配置选项
   */
  startLoading(key, options = {}) {
    const config = {
      timeout: options.timeout || this.defaultTimeout,
      showLoading: options.showLoading !== false,
      title: options.title || '加载中...',
      mask: options.mask !== false,
      onTimeout: options.onTimeout,
      onError: options.onError
    };

    // 显示加载提示
    if (config.showLoading) {
      wx.showLoading({
        title: config.title,
        mask: config.mask
      });
    }

    // 设置超时处理
    const timeoutId = setTimeout(() => {
      this.handleTimeout(key, config);
    }, config.timeout);

    // 存储加载状态
    this.loadingStates.set(key, {
      config,
      timeoutId,
      startTime: Date.now()
    });

    console.log(`🔄 开始加载: ${key}`);
  }

  /**
   * 结束加载
   * @param {string} key 加载标识
   * @param {boolean} success 是否成功
   */
  endLoading(key, success = true) {
    const state = this.loadingStates.get(key);
    if (!state) return;

    // 清除超时定时器
    clearTimeout(state.timeoutId);

    // 隐藏加载提示
    if (state.config.showLoading) {
      wx.hideLoading();
    }

    // 计算加载时间
    const duration = Date.now() - state.startTime;
    console.log(`✅ 加载完成: ${key}, 耗时: ${duration}ms, 成功: ${success}`);

    // 清除状态
    this.loadingStates.delete(key);
  }

  /**
   * 处理超时
   * @param {string} key 加载标识
   * @param {Object} config 配置
   */
  handleTimeout(key, config) {
    console.warn(`⏰ 加载超时: ${key}`);
    
    // 隐藏加载提示
    if (config.showLoading) {
      wx.hideLoading();
    }

    // 显示超时处理选项
    wx.showModal({
      title: '网络连接超时',
      content: '网络连接较慢，请选择下一步操作',
      cancelText: '重试',
      confirmText: '离线浏览',
      success: (res) => {
        if (res.confirm) {
          // 用户选择离线浏览
          this.handleOfflineMode(key);
        } else if (res.cancel) {
          // 用户选择重试
          this.handleRetry(key, config);
        }
      }
    });

    // 调用自定义超时处理
    if (config.onTimeout) {
      config.onTimeout(key);
    }

    // 清除状态
    this.loadingStates.delete(key);
  }

  /**
   * 处理重试
   * @param {string} key 加载标识
   * @param {Object} config 配置
   */
  handleRetry(key, config) {
    console.log(`🔄 重试加载: ${key}`);
    
    // 重新开始加载
    this.startLoading(key, config);
    
    // 触发重试回调
    if (config.onRetry) {
      config.onRetry(key);
    }
  }

  /**
   * 处理离线模式
   * @param {string} key 加载标识
   */
  handleOfflineMode(key) {
    console.log(`📱 进入离线模式: ${key}`);
    
    wx.showToast({
      title: '已切换到离线模式',
      icon: 'none',
      duration: 2000
    });

    // 可以在这里加载缓存数据
    this.loadCachedData(key);
  }

  /**
   * 加载缓存数据
   * @param {string} key 加载标识
   */
  loadCachedData(key) {
    // 这里可以实现缓存数据的加载逻辑
    console.log(`📦 加载缓存数据: ${key}`);
  }

  /**
   * 检查是否正在加载
   * @param {string} key 加载标识
   */
  isLoading(key) {
    return this.loadingStates.has(key);
  }

  /**
   * 获取所有加载状态
   */
  getAllLoadingStates() {
    return Array.from(this.loadingStates.keys());
  }

  /**
   * 清除所有加载状态
   */
  clearAllLoading() {
    this.loadingStates.forEach((state, key) => {
      this.endLoading(key, false);
    });
  }
}

// 创建全局实例
const loadingManager = new LoadingManager();

module.exports = {
  LoadingManager,
  loadingManager,
  
  // 便捷方法
  startLoading: (key, options) => loadingManager.startLoading(key, options),
  endLoading: (key, success) => loadingManager.endLoading(key, success),
  isLoading: (key) => loadingManager.isLoading(key)
};
