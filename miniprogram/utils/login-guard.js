// 登录守卫 - 为关键页面提供登录检查功能
// 确保页面功能正常使用，实现渐进式登录策略

// 避免循环依赖，直接导入需要的模块
const { userActions } = require("./state-actions.js");
const wechat = require("./wechat.js");
const constants = require("./constants.js");
const ui = require("./ui.js");

/**
 * 登录守卫混入
 * 为页面提供登录检查和自动登录功能
 */
const loginGuard = {
  /**
   * 确保有登录态
   * @param {Object} options 配置选项
   * @param {boolean} options.required 是否必须登录（默认false）
   * @param {boolean} options.needUserInfo 是否需要用户信息（默认false）
   * @param {boolean} options.silent 是否静默处理（默认true）
   * @returns {Promise<boolean>} 是否有有效登录态
   */
  async ensureLogin(options = {}) {
    const { required = false, needUserInfo = false, silent = true } = options;

    try {
      // 1. 检查当前登录状态
      const isLoggedIn = userActions.isLoggedIn();
      const userInfo = userActions.getUserInfo();

      // 2. 如果已登录且满足要求，直接返回
      if (isLoggedIn) {
        if (!needUserInfo || (userInfo && userInfo.nickname)) {
          return true;
        }
      }

      // 3. 尝试静默登录
      if (!isLoggedIn) {
        if (!silent) {
          ui.showLoading("登录中...");
        }

        try {
          const app = getApp();
          await app.silentLogin();
        } catch (silentLoginError) {
          console.log("静默登录失败:", silentLoginError);
        }

        if (!silent) {
          ui.hideLoading();
        }

        // 检查静默登录是否成功
        if (userActions.isLoggedIn()) {
          console.log("✅ 静默登录成功");

          // 重新检查用户信息要求
          const updatedUserInfo = userActions.getUserInfo();
          if (!needUserInfo || (updatedUserInfo && updatedUserInfo.nickname)) {
            return true;
          } else {
            console.log("⚠️ 静默登录成功但缺少用户信息");
            // 继续执行后续逻辑，跳转到登录页面
          }
        }
      }

      // 4. 静默登录失败，根据配置处理
      if (required) {
        // 必须登录，跳转到登录页面
        if (!silent) {
          ui.showToast("需要登录才能使用此功能");
        }

        setTimeout(() => {
          // 获取当前页面信息用于登录后跳转
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          let redirectUrl = "";

          if (currentPage) {
            redirectUrl = `/${currentPage.route}`;
            // 添加当前页面参数
            if (
              currentPage.options &&
              Object.keys(currentPage.options).length > 0
            ) {
              const params = Object.keys(currentPage.options)
                .map(
                  (key) =>
                    `${key}=${encodeURIComponent(currentPage.options[key])}`
                )
                .join("&");
              redirectUrl += `?${params}`;
            }
          }

          const loginUrl = redirectUrl
            ? `${constants.PAGES.LOGIN}?redirectUrl=${encodeURIComponent(
                redirectUrl
              )}&from=${currentPage?.route || "unknown"}`
            : constants.PAGES.LOGIN;

          console.log("登录守卫跳转到登录页面:", loginUrl);

          wx.navigateTo({
            url: loginUrl,
          });
        }, 1000);

        return false;
      } else {
        // 非必须登录，允许继续使用
        if (!silent) {
          console.log("⚠️ 未登录，但允许继续使用");
        }
        return false;
      }
    } catch (error) {
      console.error("登录检查失败:", error);

      if (required) {
        ui.showError("登录失败，请重试");
        return false;
      }

      return false;
    }
  },

  /**
   * 检查是否需要用户信息
   * @param {boolean} showPrompt 是否显示提示
   * @returns {boolean} 是否有用户信息
   */
  checkUserInfo(showPrompt = false) {
    const userInfo = userActions.getUserInfo();
    const hasUserInfo = userInfo && userInfo.nickname;

    if (!hasUserInfo && showPrompt) {
      this.promptForUserInfo();
    }

    return hasUserInfo;
  },

  /**
   * 提示用户完善信息
   */
  promptForUserInfo() {
    wx.showModal({
      title: "完善用户信息",
      content: "为了更好的体验，建议您完善用户信息",
      confirmText: "去完善",
      cancelText: "稍后再说",
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: constants.PAGES.LOGIN,
          });
        }
      },
    });
  },

  /**
   * 获取用户信息（在当前页面）
   * @returns {Promise<boolean>} 是否成功获取
   */
  async getUserProfileInPage() {
    try {
      // 直接使用已导入的wechat模块
      const userInfo = await wechat.auth.getUserProfile();

      // 更新用户信息
      userActions.updateUserInfo({
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        country: userInfo.country,
        language: userInfo.language,
      });

      ui.showSuccess("用户信息获取成功");
      return true;
    } catch (error) {
      console.error("获取用户信息失败:", error);

      if (error.message.includes("auth deny")) {
        ui.showToast("您拒绝了授权");
      } else {
        ui.showError("获取用户信息失败");
      }

      return false;
    }
  },

  /**
   * 登录状态变化处理
   */
  onLoginStateChange(isLoggedIn, userInfo) {
    console.log("登录状态变化:", isLoggedIn, userInfo?.nickname);

    // 可以在这里处理登录状态变化后的逻辑
    // 比如重新加载数据、更新UI等
    if (typeof this.onLoginChange === "function") {
      this.onLoginChange(isLoggedIn, userInfo);
    }
  },
};

/**
 * 创建带登录守卫的页面
 * @param {Object} pageConfig 页面配置
 * @param {Object} guardOptions 守卫配置
 * @returns {Object} 增强后的页面配置
 */
function createPageWithLoginGuard(pageConfig, guardOptions = {}) {
  const {
    autoCheck = true, // 是否自动检查登录
    required = false, // 是否必须登录
    needUserInfo = false, // 是否需要用户信息
    silent = true, // 是否静默处理
  } = guardOptions;

  // 合并登录守卫混入
  const enhancedConfig = {
    ...loginGuard,
    ...pageConfig,

    // 保存原始的onLoad方法
    _originalOnLoad: pageConfig.onLoad,

    // 增强的onLoad方法
    async onLoad(options) {
      // 自动检查登录
      if (autoCheck) {
        await this.ensureLogin({
          required,
          needUserInfo,
          silent,
        });
      }

      // 调用原始的onLoad方法
      if (this._originalOnLoad) {
        this._originalOnLoad.call(this, options);
      }
    },
  };

  return enhancedConfig;
}

/**
 * 登录守卫装饰器
 * @param {Object} guardOptions 守卫配置
 */
function withLoginGuard(guardOptions = {}) {
  return function (pageConfig) {
    return createPageWithLoginGuard(pageConfig, guardOptions);
  };
}

/**
 * 常用的登录守卫预设
 */
const loginGuardPresets = {
  // 基础检查：不要求登录，静默检查
  basic: {
    autoCheck: true,
    required: false,
    needUserInfo: false,
    silent: true,
  },

  // 需要登录：必须有登录态
  requireLogin: {
    autoCheck: true,
    required: true,
    needUserInfo: false,
    silent: false,
  },

  // 需要用户信息：必须有完整用户信息
  requireUserInfo: {
    autoCheck: true,
    required: true,
    needUserInfo: true,
    silent: false,
  },

  // 个人中心：需要用户信息，但可以在页面内获取
  profile: {
    autoCheck: true,
    required: true,
    needUserInfo: false,
    silent: false,
  },
};

/**
 * 使用预设创建登录守卫
 * @param {string} presetName 预设名称
 * @param {Object} additionalOptions 额外配置
 */
function withLoginGuardPreset(presetName, additionalOptions = {}) {
  const preset = loginGuardPresets[presetName] || loginGuardPresets.basic;
  const options = { ...preset, ...additionalOptions };
  return withLoginGuard(options);
}

// 导出登录守卫相关功能
module.exports = {
  loginGuard,
  createPageWithLoginGuard,
  withLoginGuard,
  withLoginGuardPreset,
  loginGuardPresets,
};
