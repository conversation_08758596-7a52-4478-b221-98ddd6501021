// 常量定义 - 统一管理所有常量
// 遵循MVS规则：统一代码模板，便于维护
const config = require("./config.js");

// API相关常量
const API = {
  BASE_URL: config.API_BASE_URL,
  TIMEOUT: 10000, // 请求超时时间
  RETRY_COUNT: 3, // 重试次数
  RETRY_DELAY: 1000, // 重试延迟
};

// 存储键名常量 - 统一管理避免重复和错误
const STORAGE_KEYS = {
  TOKEN: "token",
  REFRESH_TOKEN: "refreshToken",
  USER_INFO: "userInfo",
  CURRENT_CHILD: "currentChild",
  CHILDREN_LIST: "childrenList",
  USER_SETTINGS: "userSettings",
  CACHE_PREFIX: "cache_",
  LAST_LOGIN_TIME: "lastLoginTime",
};

// 错误码常量
const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT_ERROR: "TIMEOUT_ERROR",
  SERVER_ERROR: "SERVER_ERROR",

  // 业务错误
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  NOT_FOUND: "NOT_FOUND",
  VALIDATION_ERROR: "VALIDATION_ERROR",

  // 微信相关错误
  WECHAT_LOGIN_FAILED: "WECHAT_LOGIN_FAILED",
  WECHAT_AUTH_DENIED: "WECHAT_AUTH_DENIED",

  // 本地存储错误
  STORAGE_ERROR: "STORAGE_ERROR",
  STORAGE_QUOTA_EXCEEDED: "STORAGE_QUOTA_EXCEEDED",
};

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: "网络连接失败，请检查网络设置",
  [ERROR_CODES.TIMEOUT_ERROR]: "请求超时，请稍后重试",
  [ERROR_CODES.SERVER_ERROR]: "服务器繁忙，请稍后重试",
  [ERROR_CODES.UNAUTHORIZED]: "登录已过期，请重新登录",
  [ERROR_CODES.FORBIDDEN]: "权限不足，无法访问",
  [ERROR_CODES.NOT_FOUND]: "请求的资源不存在",
  [ERROR_CODES.VALIDATION_ERROR]: "输入信息有误，请检查后重试",
  [ERROR_CODES.WECHAT_LOGIN_FAILED]: "微信登录失败，请重试",
  [ERROR_CODES.WECHAT_AUTH_DENIED]: "用户拒绝授权",
  [ERROR_CODES.STORAGE_ERROR]: "数据存储失败",
  [ERROR_CODES.STORAGE_QUOTA_EXCEEDED]: "存储空间不足",
};

// 页面路径常量
const PAGES = {
  LOGIN: "/pages/login/login",
  HOME: "/pages/home/<USER>",
  GROWTH: "/pages/growth/growth",
  PROFILE: "/pages/profile/profile",
  CAMP_DETAIL: "/pages/camp-detail/camp-detail",
  CHECKIN: "/pages/checkin/checkin",
  CHECKIN_SUCCESS: "/pages/checkin-success/checkin-success",
  VIDEO_PLAY: "/pages/video-play/video-play",
  VIDEO_LIST: "/pages/video-list/video-list",
  LEADERBOARD: "/pages/leaderboard/leaderboard",
  BADGES: "/pages/badges/badges",
  SHARE_CARD: "/pages/share-card/share-card",
  CHILD_MANAGE: "/pages/child-manage/child-manage",
  CHILD_CREATE: "/pages/child-create/child-create",
  SETTINGS: "/pages/settings/settings",
};

// 缓存相关常量
const CACHE = {
  DEFAULT_EXPIRE: 30 * 60 * 1000, // 30分钟
  LONG_EXPIRE: 24 * 60 * 60 * 1000, // 24小时
  SHORT_EXPIRE: 5 * 60 * 1000, // 5分钟
  MONTH_EXPIRE: 30 * 24 * 60 * 60 * 1000, // 30天（1个月）
};

// 验证规则常量
const VALIDATION = {
  CHILD_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 20,
    PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/, // 中文、英文、数字、空格
  },
  CHILD_AGE: {
    MIN: 3,
    MAX: 18,
  },
  NOTE_MAX_LENGTH: 200,
};

// 微信相关常量
const WECHAT = {
  LOGIN_SCOPE: "scope.userInfo",
  LOCATION_SCOPE: "scope.userLocation",
  CAMERA_SCOPE: "scope.camera",
  ALBUM_SCOPE: "scope.writePhotosAlbum",
};

// 应用配置常量
const APP_CONFIG = {
  VERSION: "1.0.0",
  DEBUG: false, // 生产环境设为false
  LOG_LEVEL: "info", // debug, info, warn, error
  MAX_CHILDREN: 3, // 最多添加孩子数量
  MAX_UPLOAD_SIZE: 10 * 1024 * 1024, // 10MB
};

// 默认值常量
const DEFAULTS = {
  CHILD_AVATAR: "👶",
  USER_AVATAR: "👤",
  PAGE_SIZE: 20,
  LOADING_TEXT: "加载中...",
  EMPTY_TEXT: "暂无数据",
};

// 事件名称常量
const EVENTS = {
  LOGIN_SUCCESS: "loginSuccess",
  LOGIN_FAILED: "loginFailed",
  LOGOUT: "logout",
  CHILD_CHANGED: "childChanged",
  DATA_UPDATED: "dataUpdated",
};

// 导出所有常量
module.exports = {
  API,
  STORAGE_KEYS,
  ERROR_CODES,
  ERROR_MESSAGES,
  PAGES,
  CACHE,
  VALIDATION,
  WECHAT,
  APP_CONFIG,
  DEFAULTS,
  EVENTS,
};
