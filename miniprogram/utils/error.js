// 错误处理工具 - 统一错误处理机制 (CommonJS版本)
// 遵循MVS规则：统一代码模板，完善错误处理

const { ERROR_CODES, ERROR_MESSAGES, APP_CONFIG } = require("./constants.js");

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(code, message, originalError = null, context = {}) {
    super(message);
    this.name = "AppError";
    this.code = code;
    this.originalError = originalError;
    this.context = context;
    this.timestamp = Date.now();
  }

  /**
   * 转换为用户友好的消息
   */
  toUserMessage() {
    return ERROR_MESSAGES[this.code] || this.message || "操作失败，请稍后重试";
  }

  /**
   * 转换为日志格式
   */
  toLogFormat() {
    return {
      code: this.code,
      message: this.message,
      originalError: this.originalError?.message,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }
}

/**
 * 创建错误实例
 */
function createError(code, message, originalError = null, context = {}) {
  return new AppError(code, message, originalError, context);
}

/**
 * 错误处理器
 */
class ErrorHandler {
  constructor() {
    this.errorListeners = [];
  }

  addListener(listener) {
    this.errorListeners.push(listener);
  }

  removeListener(listener) {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  handle(error, context = {}) {
    let appError;

    try {
      if (error instanceof AppError) {
        appError = error;
      } else {
        appError = this._convertToAppError(error, context);
      }

      this._logError(appError);

      this.errorListeners.forEach((listener) => {
        try {
          listener(appError);
        } catch (e) {
          console.error("Error listener failed:", e);
        }
      });

      this._showUserMessage(appError);
      return appError;
    } catch (handlerError) {
      // 如果处理过程中出错，静默处理
      console.error("错误处理器内部错误:", handlerError);
      return null;
    }
  }

  _convertToAppError(error, context) {
    let code = ERROR_CODES.SERVER_ERROR;
    let message = error.message;

    if (error.message?.includes("timeout")) {
      code = ERROR_CODES.TIMEOUT_ERROR;
    } else if (error.message?.includes("network")) {
      code = ERROR_CODES.NETWORK_ERROR;
    } else if (error.message?.includes("unauthorized")) {
      code = ERROR_CODES.UNAUTHORIZED;
    }

    return createError(code, message, error, context);
  }

  _logError(error) {
    const logData = error.toLogFormat();
    if (APP_CONFIG.DEBUG) {
      console.error("App Error:", logData);
    }
  }

  _showUserMessage(error) {
    const message = error.toUserMessage();

    // 添加错误显示的防抖机制，避免循环报错
    if (this._shouldShowError(error)) {
      try {
        if (error.code === ERROR_CODES.UNAUTHORIZED) {
          wx.showModal({
            title: "登录过期",
            content: message,
            showCancel: false,
            confirmText: "重新登录",
            success: () => {
              wx.reLaunch({
                url: "/pages/login/login",
              });
            },
          });
        } else if (error.code === ERROR_CODES.NETWORK_ERROR) {
          // 网络错误显示友好提示，但有防抖机制
          wx.showToast({
            title: "🌐 网络连接异常，请稍后重试",
            icon: "none",
            duration: 4000, // 增加显示时间
            mask: true, // 添加遮罩，确保显示
          });
        } else {
          wx.showToast({
            title: message,
            icon: "none",
            duration: 2000,
          });
        }
      } catch (toastError) {
        // 如果Toast显示失败，只在控制台输出
        console.error("显示错误消息失败:", toastError);
        console.error("原始错误:", message);
      }
    }
  }

  _shouldShowError(error) {
    // 防抖机制：同类型错误在短时间内只显示一次
    const now = Date.now();
    const key = `${error.code}_${error.message}`;

    if (!this._lastErrorTime) {
      this._lastErrorTime = {};
    }

    if (this._lastErrorTime[key] && now - this._lastErrorTime[key] < 5000) {
      return false; // 5秒内不重复显示同类错误
    }

    this._lastErrorTime[key] = now;
    return true;
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

/**
 * 网络错误处理
 */
function handleNetworkError(error, context = {}) {
  let code = ERROR_CODES.NETWORK_ERROR;
  let message = "网络请求失败";

  if (error.statusCode) {
    switch (error.statusCode) {
      case 401:
        code = ERROR_CODES.UNAUTHORIZED;
        break;
      case 403:
        code = ERROR_CODES.FORBIDDEN;
        break;
      case 404:
        code = ERROR_CODES.NOT_FOUND;
        break;
      case 500:
      case 502:
      case 503:
        code = ERROR_CODES.SERVER_ERROR;
        break;
      default:
        code = ERROR_CODES.NETWORK_ERROR;
    }
  }

  const appError = createError(code, message, error, context);
  return errorHandler.handle(appError);
}

/**
 * 微信API错误处理
 */
function handleWechatError(error, apiName = "") {
  let code = ERROR_CODES.WECHAT_LOGIN_FAILED;
  let message = "微信操作失败";

  if (error.errMsg?.includes("auth deny")) {
    code = ERROR_CODES.WECHAT_AUTH_DENIED;
    message = "用户拒绝授权";
  }

  const context = { apiName, errMsg: error.errMsg };
  const appError = createError(code, message, error, context);
  return errorHandler.handle(appError);
}

// CommonJS导出
module.exports = errorHandler;
module.exports.AppError = AppError;
module.exports.createError = createError;
module.exports.ErrorHandler = ErrorHandler;
module.exports.errorHandler = errorHandler;
module.exports.handleNetworkError = handleNetworkError;
module.exports.handleWechatError = handleWechatError;
