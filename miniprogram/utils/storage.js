// 本地存储封装 - 统一管理本地存储操作
// 遵循MVS规则：统一代码模板，基础测试覆盖

const { STORAGE_KEYS, ERROR_CODES, CACHE } = require("./constants.js");
const { createError } = require("./error.js");

/**
 * 本地存储管理器
 * 提供统一的存储接口，支持缓存过期、数据验证等功能
 */
class StorageManager {
  constructor() {
    this.prefix = "kids_platform_";
  }

  /**
   * 生成存储键名
   * @param {string} key 原始键名
   * @returns {string} 带前缀的键名
   */
  _getKey(key) {
    return this.prefix + key;
  }

  /**
   * 设置存储数据
   * @param {string} key 存储键名
   * @param {any} value 存储值
   * @param {number} expire 过期时间（毫秒），0表示永不过期
   * @returns {boolean} 是否成功
   */
  set(key, value, expire = 0) {
    try {
      const data = {
        value,
        timestamp: Date.now(),
        expire: expire > 0 ? Date.now() + expire : 0,
      };

      wx.setStorageSync(this._getKey(key), JSON.stringify(data));
      return true;
    } catch (error) {
      console.error("Storage set error:", error);
      throw createError(ERROR_CODES.STORAGE_ERROR, "数据存储失败", error);
    }
  }

  /**
   * 获取存储数据
   * @param {string} key 存储键名
   * @param {any} defaultValue 默认值
   * @returns {any} 存储的值或默认值
   */
  get(key, defaultValue = null) {
    try {
      const dataStr = wx.getStorageSync(this._getKey(key));
      if (!dataStr) {
        return defaultValue;
      }

      const data = JSON.parse(dataStr);

      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key);
        return defaultValue;
      }

      return data.value;
    } catch (error) {
      console.error("Storage get error:", error);
      return defaultValue;
    }
  }

  /**
   * 删除存储数据
   * @param {string} key 存储键名
   * @returns {boolean} 是否成功
   */
  remove(key) {
    try {
      wx.removeStorageSync(this._getKey(key));
      return true;
    } catch (error) {
      console.error("Storage remove error:", error);
      return false;
    }
  }

  /**
   * 清空所有存储数据
   * @returns {boolean} 是否成功
   */
  clear() {
    try {
      // 获取所有键名，只清除带前缀的
      const info = wx.getStorageInfoSync();
      const keys = info.keys.filter((key) => key.startsWith(this.prefix));

      keys.forEach((key) => {
        wx.removeStorageSync(key);
      });

      return true;
    } catch (error) {
      console.error("Storage clear error:", error);
      return false;
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key 存储键名
   * @returns {boolean} 是否存在
   */
  has(key) {
    try {
      const dataStr = wx.getStorageSync(this._getKey(key));
      if (!dataStr) return false;

      const data = JSON.parse(dataStr);

      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key);
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取存储信息
   * @returns {object} 存储信息
   */
  getInfo() {
    try {
      const info = wx.getStorageInfoSync();
      const ourKeys = info.keys.filter((key) => key.startsWith(this.prefix));

      return {
        keys: ourKeys,
        currentSize: info.currentSize,
        limitSize: info.limitSize,
        keyCount: ourKeys.length,
      };
    } catch (error) {
      console.error("Storage getInfo error:", error);
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0,
        keyCount: 0,
      };
    }
  }
}

// 创建存储管理器实例
const storage = new StorageManager();

// 便捷方法 - 用户相关存储
const userStorage = {
  // 用户信息
  setUserInfo(userInfo) {
    return storage.set(STORAGE_KEYS.USER_INFO, userInfo, CACHE.MONTH_EXPIRE);
  },

  getUserInfo() {
    return storage.get(STORAGE_KEYS.USER_INFO);
  },

  // 登录令牌
  setToken(token) {
    return storage.set(STORAGE_KEYS.TOKEN, token, CACHE.MONTH_EXPIRE);
  },

  getToken() {
    return storage.get(STORAGE_KEYS.TOKEN);
  },

  // 刷新令牌
  setRefreshToken(refreshToken) {
    return storage.set(
      STORAGE_KEYS.REFRESH_TOKEN,
      refreshToken,
      CACHE.MONTH_EXPIRE
    );
  },

  getRefreshToken() {
    return storage.get(STORAGE_KEYS.REFRESH_TOKEN);
  },

  // 当前选中的孩子
  setCurrentChild(child) {
    return storage.set(STORAGE_KEYS.CURRENT_CHILD, child, CACHE.MONTH_EXPIRE);
  },

  // 获取当前选中的孩子
  getCurrentChild() {
    return storage.get(STORAGE_KEYS.CURRENT_CHILD, {});
  },

  // 孩子列表
  setChildrenList(children) {
    return storage.set(
      STORAGE_KEYS.CHILDREN_LIST,
      children,
      CACHE.MONTH_EXPIRE
    );
  },

  getChildrenList() {
    return storage.get(STORAGE_KEYS.CHILDREN_LIST, []);
  },

  // 用户设置
  setUserSettings(settings) {
    return storage.set(STORAGE_KEYS.USER_SETTINGS, settings, CACHE.LONG_EXPIRE);
  },

  getUserSettings() {
    return storage.get(STORAGE_KEYS.USER_SETTINGS, {});
  },

  // 清除用户相关数据
  clearUserData() {
    storage.remove(STORAGE_KEYS.TOKEN);
    storage.remove(STORAGE_KEYS.REFRESH_TOKEN);
    storage.remove(STORAGE_KEYS.USER_INFO);
    storage.remove(STORAGE_KEYS.CURRENT_CHILD);
    storage.remove(STORAGE_KEYS.CHILDREN_LIST);
  },
};

// 缓存相关方法
const cacheStorage = {
  // 设置缓存
  setCache(key, data, expire = CACHE.DEFAULT_EXPIRE) {
    return storage.set(STORAGE_KEYS.CACHE_PREFIX + key, data, expire);
  },

  // 获取缓存
  getCache(key, defaultValue = null) {
    return storage.get(STORAGE_KEYS.CACHE_PREFIX + key, defaultValue);
  },

  // 删除缓存
  removeCache(key) {
    return storage.remove(STORAGE_KEYS.CACHE_PREFIX + key);
  },

  // 清空所有缓存
  clearCache() {
    const info = storage.getInfo();
    const cacheKeys = info.keys.filter((key) =>
      key.includes(STORAGE_KEYS.CACHE_PREFIX)
    );

    cacheKeys.forEach((key) => {
      const originalKey = key
        .replace(storage.prefix, "")
        .replace(STORAGE_KEYS.CACHE_PREFIX, "");
      storage.remove(STORAGE_KEYS.CACHE_PREFIX + originalKey);
    });
  },
};

// 儿童数据缓存相关方法 - 支持按儿童ID分别缓存
const childDataStorage = {
  /**
   * 生成儿童数据缓存键
   * @param {number} childId 儿童ID
   * @param {string} dataType 数据类型
   * @returns {string} 缓存键
   */
  _getChildDataKey(childId, dataType) {
    return `child_${childId}_${dataType}`;
  },

  /**
   * 设置儿童任务数据
   * @param {number} childId 儿童ID
   * @param {Array} tasks 任务列表
   * @param {number} expire 过期时间
   */
  setChildTasks(childId, tasks, expire = CACHE.DEFAULT_EXPIRE) {
    const key = this._getChildDataKey(childId, "tasks");
    return cacheStorage.setCache(key, tasks, expire);
  },

  /**
   * 获取儿童任务数据
   * @param {number} childId 儿童ID
   * @returns {Array} 任务列表
   */
  getChildTasks(childId) {
    const key = this._getChildDataKey(childId, "tasks");
    return cacheStorage.getCache(key, []);
  },

  /**
   * 设置儿童记录数据
   * @param {number} childId 儿童ID
   * @param {Array} records 记录列表
   * @param {number} expire 过期时间
   */
  setChildRecords(childId, records, expire = CACHE.DEFAULT_EXPIRE) {
    const key = this._getChildDataKey(childId, "records");
    return cacheStorage.setCache(key, records, expire);
  },

  /**
   * 获取儿童记录数据
   * @param {number} childId 儿童ID
   * @returns {Array} 记录列表
   */
  getChildRecords(childId) {
    const key = this._getChildDataKey(childId, "records");
    return cacheStorage.getCache(key, []);
  },

  /**
   * 设置儿童徽章数据
   * @param {number} childId 儿童ID
   * @param {Array} badges 徽章列表
   * @param {number} expire 过期时间
   */
  setChildBadges(childId, badges, expire = CACHE.LONG_EXPIRE) {
    const key = this._getChildDataKey(childId, "badges");
    return cacheStorage.setCache(key, badges, expire);
  },

  /**
   * 获取儿童徽章数据
   * @param {number} childId 儿童ID
   * @returns {Array} 徽章列表
   */
  getChildBadges(childId) {
    const key = this._getChildDataKey(childId, "badges");
    return cacheStorage.getCache(key, []);
  },

  /**
   * 设置儿童积分数据
   * @param {number} childId 儿童ID
   * @param {Object} points 积分信息
   * @param {number} expire 过期时间
   */
  setChildPoints(childId, points, expire = CACHE.SHORT_EXPIRE) {
    const key = this._getChildDataKey(childId, "points");
    return cacheStorage.setCache(key, points, expire);
  },

  /**
   * 获取儿童积分数据
   * @param {number} childId 儿童ID
   * @returns {Object} 积分信息
   */
  getChildPoints(childId) {
    const key = this._getChildDataKey(childId, "points");
    return cacheStorage.getCache(key, { total: 0, available: 0 });
  },

  /**
   * 设置儿童统计数据
   * @param {number} childId 儿童ID
   * @param {Object} stats 统计信息
   * @param {number} expire 过期时间
   */
  setChildStats(childId, stats, expire = CACHE.DEFAULT_EXPIRE) {
    const key = this._getChildDataKey(childId, "stats");
    return cacheStorage.setCache(key, stats, expire);
  },

  /**
   * 获取儿童统计数据
   * @param {number} childId 儿童ID
   * @returns {Object} 统计信息
   */
  getChildStats(childId) {
    const key = this._getChildDataKey(childId, "stats");
    return cacheStorage.getCache(key, {});
  },

  /**
   * 清除指定儿童的所有缓存数据
   * @param {number} childId 儿童ID
   */
  clearChildData(childId) {
    const dataTypes = ["tasks", "records", "badges", "points", "stats"];
    dataTypes.forEach((dataType) => {
      const key = this._getChildDataKey(childId, dataType);
      cacheStorage.removeCache(key);
    });
  },

  /**
   * 清除所有儿童的缓存数据
   */
  clearAllChildData() {
    const info = storage.getInfo();
    const childDataKeys = info.keys.filter(
      (key) => key.includes("child_") && key.includes("_")
    );

    childDataKeys.forEach((key) => {
      const originalKey = key
        .replace(storage.prefix, "")
        .replace(STORAGE_KEYS.CACHE_PREFIX, "");
      cacheStorage.removeCache(originalKey);
    });
  },

  /**
   * 获取儿童的所有缓存数据类型
   * @param {number} childId 儿童ID
   * @returns {Array} 已缓存的数据类型列表
   */
  getChildCachedDataTypes(childId) {
    const info = storage.getInfo();
    const prefix = `${storage.prefix}${STORAGE_KEYS.CACHE_PREFIX}child_${childId}_`;

    return info.keys
      .filter((key) => key.startsWith(prefix))
      .map((key) => key.replace(prefix, ""));
  },
};

// 导出主要接口
module.exports = storage;
module.exports.storage = storage;
module.exports.userStorage = userStorage;
module.exports.cacheStorage = cacheStorage;
module.exports.childDataStorage = childDataStorage;
