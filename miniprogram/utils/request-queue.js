// 并发请求队列管理器
// 优化token刷新期间的并发请求处理

/**
 * 请求队列管理器
 * 处理token刷新期间的并发请求，避免重复刷新和请求丢失
 */
class RequestQueueManager {
  constructor() {
    // 队列状态
    this.isTokenRefreshing = false;
    this.refreshPromise = null;
    
    // 等待队列
    this.pendingRequests = [];
    
    // 队列统计
    this.stats = {
      totalQueued: 0,
      totalProcessed: 0,
      totalFailed: 0,
      maxQueueSize: 0,
      averageWaitTime: 0
    };
  }

  /**
   * 添加请求到队列
   * @param {Function} requestFunction 请求函数
   * @param {Object} config 请求配置
   * @param {Object} options 队列选项
   * @returns {Promise} 请求结果
   */
  async enqueue(requestFunction, config, options = {}) {
    const requestId = this._generateRequestId(config);
    const queueItem = {
      id: requestId,
      requestFunction,
      config,
      options,
      enqueuedAt: Date.now(),
      priority: options.priority || 'normal',
      timeout: options.timeout || 30000
    };

    console.log("📥 请求加入队列:", {
      requestId,
      url: config.url,
      method: config.method,
      priority: queueItem.priority,
      queueSize: this.pendingRequests.length
    });

    return new Promise((resolve, reject) => {
      queueItem.resolve = resolve;
      queueItem.reject = reject;
      
      // 根据优先级插入队列
      this._insertByPriority(queueItem);
      
      // 更新统计
      this.stats.totalQueued++;
      this.stats.maxQueueSize = Math.max(this.stats.maxQueueSize, this.pendingRequests.length);
      
      // 设置超时处理
      queueItem.timeoutId = setTimeout(() => {
        this._timeoutRequest(requestId);
      }, queueItem.timeout);
      
      // 如果没有在刷新token，立即处理
      if (!this.isTokenRefreshing) {
        this._processQueue();
      }
    });
  }

  /**
   * 开始token刷新
   * @param {Promise} refreshPromise token刷新Promise
   */
  startTokenRefresh(refreshPromise) {
    if (this.isTokenRefreshing) {
      console.log("🔄 Token刷新已在进行中");
      return this.refreshPromise;
    }

    console.log("🔄 开始Token刷新，暂停队列处理");
    this.isTokenRefreshing = true;
    this.refreshPromise = refreshPromise;

    // 监听刷新完成
    refreshPromise
      .then((result) => {
        console.log("✅ Token刷新成功，恢复队列处理");
        this._onTokenRefreshSuccess(result);
      })
      .catch((error) => {
        console.error("❌ Token刷新失败，处理队列失败");
        this._onTokenRefreshFailure(error);
      })
      .finally(() => {
        this.isTokenRefreshing = false;
        this.refreshPromise = null;
      });

    return refreshPromise;
  }

  /**
   * 根据优先级插入队列
   */
  _insertByPriority(queueItem) {
    const priorities = { high: 3, normal: 2, low: 1 };
    const itemPriority = priorities[queueItem.priority] || 2;
    
    let insertIndex = this.pendingRequests.length;
    
    // 找到合适的插入位置
    for (let i = 0; i < this.pendingRequests.length; i++) {
      const existingPriority = priorities[this.pendingRequests[i].priority] || 2;
      if (itemPriority > existingPriority) {
        insertIndex = i;
        break;
      }
    }
    
    this.pendingRequests.splice(insertIndex, 0, queueItem);
  }

  /**
   * 处理队列
   */
  async _processQueue() {
    if (this.isTokenRefreshing || this.pendingRequests.length === 0) {
      return;
    }

    console.log(`🔄 开始处理队列，共${this.pendingRequests.length}个请求`);

    // 批量处理请求（避免过多并发）
    const batchSize = 5;
    const batches = this._createBatches(this.pendingRequests.splice(0), batchSize);

    for (const batch of batches) {
      await this._processBatch(batch);
    }

    console.log("✅ 队列处理完成");
  }

  /**
   * 创建批次
   */
  _createBatches(requests, batchSize) {
    const batches = [];
    for (let i = 0; i < requests.length; i += batchSize) {
      batches.push(requests.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 处理批次
   */
  async _processBatch(batch) {
    const promises = batch.map(queueItem => this._executeRequest(queueItem));
    await Promise.allSettled(promises);
  }

  /**
   * 执行单个请求
   */
  async _executeRequest(queueItem) {
    const startTime = Date.now();
    
    try {
      // 清除超时定时器
      if (queueItem.timeoutId) {
        clearTimeout(queueItem.timeoutId);
      }

      console.log("🚀 执行队列请求:", {
        requestId: queueItem.id,
        waitTime: startTime - queueItem.enqueuedAt
      });

      const result = await queueItem.requestFunction(queueItem.config);
      
      // 更新统计
      this.stats.totalProcessed++;
      this._updateAverageWaitTime(startTime - queueItem.enqueuedAt);
      
      queueItem.resolve(result);
      
    } catch (error) {
      console.error("❌ 队列请求执行失败:", {
        requestId: queueItem.id,
        error: error.message
      });
      
      this.stats.totalFailed++;
      queueItem.reject(error);
    }
  }

  /**
   * Token刷新成功处理
   */
  _onTokenRefreshSuccess(tokenData) {
    console.log(`🔄 Token刷新成功，更新${this.pendingRequests.length}个待处理请求的token`);
    
    // 更新所有待处理请求的Authorization头
    this.pendingRequests.forEach(queueItem => {
      if (queueItem.config.header) {
        queueItem.config.header.Authorization = `Bearer ${tokenData.access_token}`;
      } else {
        queueItem.config.header = {
          Authorization: `Bearer ${tokenData.access_token}`
        };
      }
    });

    // 继续处理队列
    this._processQueue();
  }

  /**
   * Token刷新失败处理
   */
  _onTokenRefreshFailure(error) {
    console.error(`❌ Token刷新失败，拒绝${this.pendingRequests.length}个待处理请求`);
    
    // 拒绝所有待处理请求
    const requests = this.pendingRequests.splice(0);
    requests.forEach(queueItem => {
      if (queueItem.timeoutId) {
        clearTimeout(queueItem.timeoutId);
      }
      
      this.stats.totalFailed++;
      queueItem.reject(error);
    });
  }

  /**
   * 请求超时处理
   */
  _timeoutRequest(requestId) {
    const index = this.pendingRequests.findIndex(item => item.id === requestId);
    if (index === -1) return;

    const queueItem = this.pendingRequests.splice(index, 1)[0];
    
    console.error("⏰ 队列请求超时:", {
      requestId,
      waitTime: Date.now() - queueItem.enqueuedAt
    });

    this.stats.totalFailed++;
    queueItem.reject(new Error('Request timeout in queue'));
  }

  /**
   * 生成请求ID
   */
  _generateRequestId(config) {
    const url = config.url || 'unknown';
    const method = config.method || 'GET';
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 5);
    return `${method}_${url.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}_${random}`;
  }

  /**
   * 更新平均等待时间
   */
  _updateAverageWaitTime(waitTime) {
    const totalProcessed = this.stats.totalProcessed;
    const currentAverage = this.stats.averageWaitTime;
    
    // 计算新的平均值
    this.stats.averageWaitTime = 
      (currentAverage * (totalProcessed - 1) + waitTime) / totalProcessed;
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      isTokenRefreshing: this.isTokenRefreshing,
      pendingCount: this.pendingRequests.length,
      stats: { ...this.stats },
      pendingRequests: this.pendingRequests.map(item => ({
        id: item.id,
        url: item.config.url,
        method: item.config.method,
        priority: item.priority,
        waitTime: Date.now() - item.enqueuedAt
      }))
    };
  }

  /**
   * 清空队列（紧急情况使用）
   */
  clearQueue(reason = 'manual_clear') {
    console.log(`🧹 清空队列，原因: ${reason}，影响${this.pendingRequests.length}个请求`);
    
    const requests = this.pendingRequests.splice(0);
    requests.forEach(queueItem => {
      if (queueItem.timeoutId) {
        clearTimeout(queueItem.timeoutId);
      }
      
      this.stats.totalFailed++;
      queueItem.reject(new Error(`Queue cleared: ${reason}`));
    });
  }

  /**
   * 重置统计
   */
  resetStats() {
    this.stats = {
      totalQueued: 0,
      totalProcessed: 0,
      totalFailed: 0,
      maxQueueSize: 0,
      averageWaitTime: 0
    };
  }
}

// 创建单例实例
const requestQueueManager = new RequestQueueManager();

module.exports = {
  RequestQueueManager,
  requestQueueManager,
};
