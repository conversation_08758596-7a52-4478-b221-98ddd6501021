// 数据验证工具 - 统一数据验证处理
// 遵循MVS规则：统一代码模板，完善验证逻辑

import { VALIDATION, ERROR_CODES } from './constants.js'
import { createError } from './error.js'

/**
 * 验证结果类
 */
export class ValidationResult {
  constructor(isValid = true, errors = []) {
    this.isValid = isValid
    this.errors = errors
  }

  /**
   * 添加错误
   * @param {string} field 字段名
   * @param {string} message 错误消息
   */
  addError(field, message) {
    this.isValid = false
    this.errors.push({ field, message })
  }

  /**
   * 获取第一个错误消息
   * @returns {string} 错误消息
   */
  getFirstError() {
    return this.errors.length > 0 ? this.errors[0].message : ''
  }

  /**
   * 获取指定字段的错误
   * @param {string} field 字段名
   * @returns {Array} 错误数组
   */
  getFieldErrors(field) {
    return this.errors.filter(error => error.field === field)
  }
}

/**
 * 基础验证器
 */
export class Validator {
  constructor() {
    this.rules = {}
  }

  /**
   * 添加验证规则
   * @param {string} field 字段名
   * @param {Array} rules 规则数组
   */
  addRule(field, rules) {
    this.rules[field] = rules
    return this
  }

  /**
   * 验证数据
   * @param {object} data 要验证的数据
   * @returns {ValidationResult} 验证结果
   */
  validate(data) {
    const result = new ValidationResult()

    for (const [field, rules] of Object.entries(this.rules)) {
      const value = data[field]
      
      for (const rule of rules) {
        if (!rule.validate(value, data)) {
          result.addError(field, rule.message)
          if (rule.stopOnFail) break
        }
      }
    }

    return result
  }
}

/**
 * 验证规则类
 */
export class ValidationRule {
  constructor(validate, message, stopOnFail = false) {
    this.validate = validate
    this.message = message
    this.stopOnFail = stopOnFail
  }
}

/**
 * 常用验证规则
 */
export const rules = {
  /**
   * 必填验证
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  required(message = '此字段为必填项') {
    return new ValidationRule(
      (value) => value !== null && value !== undefined && value !== '',
      message,
      true
    )
  },

  /**
   * 最小长度验证
   * @param {number} min 最小长度
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  minLength(min, message = `最少需要${min}个字符`) {
    return new ValidationRule(
      (value) => !value || value.toString().length >= min,
      message
    )
  },

  /**
   * 最大长度验证
   * @param {number} max 最大长度
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  maxLength(max, message = `最多允许${max}个字符`) {
    return new ValidationRule(
      (value) => !value || value.toString().length <= max,
      message
    )
  },

  /**
   * 正则表达式验证
   * @param {RegExp} pattern 正则表达式
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  pattern(pattern, message = '格式不正确') {
    return new ValidationRule(
      (value) => !value || pattern.test(value),
      message
    )
  },

  /**
   * 数值范围验证
   * @param {number} min 最小值
   * @param {number} max 最大值
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  range(min, max, message = `值必须在${min}到${max}之间`) {
    return new ValidationRule(
      (value) => {
        if (!value) return true
        const num = Number(value)
        return !isNaN(num) && num >= min && num <= max
      },
      message
    )
  },

  /**
   * 数字验证
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  numeric(message = '必须是数字') {
    return new ValidationRule(
      (value) => !value || !isNaN(Number(value)),
      message
    )
  },

  /**
   * 邮箱验证
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  email(message = '邮箱格式不正确') {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return new ValidationRule(
      (value) => !value || emailPattern.test(value),
      message
    )
  },

  /**
   * 手机号验证
   * @param {string} message 错误消息
   * @returns {ValidationRule} 验证规则
   */
  phone(message = '手机号格式不正确') {
    const phonePattern = /^1[3-9]\d{9}$/
    return new ValidationRule(
      (value) => !value || phonePattern.test(value),
      message
    )
  }
}

/**
 * 孩子信息验证器
 */
export function createChildValidator() {
  return new Validator()
    .addRule('name', [
      rules.required('请输入孩子姓名'),
      rules.minLength(VALIDATION.CHILD_NAME.MIN_LENGTH, '姓名至少需要1个字符'),
      rules.maxLength(VALIDATION.CHILD_NAME.MAX_LENGTH, '姓名最多20个字符'),
      rules.pattern(VALIDATION.CHILD_NAME.PATTERN, '姓名只能包含中文、英文、数字和空格')
    ])
    .addRule('age', [
      rules.required('请选择孩子年龄'),
      rules.numeric('年龄必须是数字'),
      rules.range(VALIDATION.CHILD_AGE.MIN, VALIDATION.CHILD_AGE.MAX, 
        `年龄必须在${VALIDATION.CHILD_AGE.MIN}到${VALIDATION.CHILD_AGE.MAX}岁之间`)
    ])
    .addRule('gender', [
      rules.required('请选择孩子性别')
    ])
}

/**
 * 打卡备注验证器
 */
export function createCheckinValidator() {
  return new Validator()
    .addRule('note', [
      rules.maxLength(VALIDATION.NOTE_MAX_LENGTH, `备注最多${VALIDATION.NOTE_MAX_LENGTH}个字符`)
    ])
    .addRule('videoId', [
      rules.required('请输入视频号ID')
    ])
}

/**
 * 用户设置验证器
 */
export function createSettingsValidator() {
  return new Validator()
    .addRule('nickname', [
      rules.maxLength(20, '昵称最多20个字符')
    ])
    .addRule('phone', [
      rules.phone('手机号格式不正确')
    ])
}

/**
 * 快速验证函数
 */
export const validate = {
  /**
   * 验证孩子信息
   * @param {object} childData 孩子数据
   * @returns {ValidationResult} 验证结果
   */
  child(childData) {
    const validator = createChildValidator()
    return validator.validate(childData)
  },

  /**
   * 验证打卡信息
   * @param {object} checkinData 打卡数据
   * @returns {ValidationResult} 验证结果
   */
  checkin(checkinData) {
    const validator = createCheckinValidator()
    return validator.validate(checkinData)
  },

  /**
   * 验证用户设置
   * @param {object} settingsData 设置数据
   * @returns {ValidationResult} 验证结果
   */
  settings(settingsData) {
    const validator = createSettingsValidator()
    return validator.validate(settingsData)
  },

  /**
   * 验证并抛出错误
   * @param {ValidationResult} result 验证结果
   * @throws {AppError} 验证失败时抛出错误
   */
  throwIfInvalid(result) {
    if (!result.isValid) {
      throw createError(
        ERROR_CODES.VALIDATION_ERROR,
        result.getFirstError(),
        null,
        { errors: result.errors }
      )
    }
  }
}

/**
 * 表单验证装饰器
 * @param {function} validator 验证器函数
 * @returns {function} 装饰器函数
 */
export function withValidation(validator) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function(...args) {
      const data = args[0] // 假设第一个参数是要验证的数据
      const result = validator(data)
      
      if (!result.isValid) {
        throw createError(
          ERROR_CODES.VALIDATION_ERROR,
          result.getFirstError(),
          null,
          { errors: result.errors }
        )
      }

      return originalMethod.apply(this, args)
    }

    return descriptor
  }
}

// 导出验证工具
export default {
  Validator,
  ValidationRule,
  ValidationResult,
  rules,
  validate,
  createChildValidator,
  createCheckinValidator,
  createSettingsValidator,
  withValidation
}
