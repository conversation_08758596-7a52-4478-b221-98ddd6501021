// 图片资源管理工具
class ImageManager {
  constructor() {
    // 图片资源映射
    this.imageMap = {
      // 应用Logo
      "app-logo": "/images/app-logo.png",
      "app-logo-white": "/images/app-logo-white.png",

      // TabBar图标
      "tab-home": "/images/tab-home.png",
      "tab-home-active": "/images/tab-home-active.png",
      "tab-growth": "/images/tab-growth.png",
      "tab-growth-active": "/images/tab-growth-active.png",
      "tab-profile": "/images/tab-profile.png",
      "tab-profile-active": "/images/tab-profile-active.png",

      // 功能图标
      "icon-calendar": "/images/icon-calendar.png",
      "icon-statistics": "/images/icon-statistics.png",
      "icon-trophy": "/images/icon-trophy.png",
      "icon-star": "/images/icon-star.png",
      "icon-heart": "/images/icon-heart.png",

      // 分享相关
      "mini-qr-code": "/images/mini-qr-code.png",
    };

    // CSS图标映射（用于空状态等）
    this.cssIconMap = {
      "empty-leaderboard": "📊",
      "empty-badges": "🏆",
      "empty-children": "👶",
      "empty-checkin": "📅",
      "empty-tasks": "✅",
      "default-avatar": "👤",
      "default-child-avatar": "👶",
      // 视频缩略图替代
      "video-thumb1": "🎥",
      "video-thumb2": "📹",
      "video-thumb3": "🎬",
      "video-thumb4": "🎞️",
      "video-thumb5": "📺",
      "video-thumb6": "🎦",
      "video-thumb7": "🎥",
      "video-thumb8": "📹",
    };
  }

  /**
   * 获取图片路径
   * @param {string} name 图片名称
   * @returns {string} 图片路径
   */
  getImagePath(name) {
    return this.imageMap[name] || "";
  }

  /**
   * 获取CSS图标
   * @param {string} name 图标名称
   * @returns {string} emoji图标
   */
  getCSSIcon(name) {
    return this.cssIconMap[name] || "📝";
  }

  /**
   * 检查是否有对应的图片文件
   * @param {string} name 图片名称
   * @returns {boolean} 是否存在
   */
  hasImage(name) {
    return !!this.imageMap[name];
  }

  /**
   * 获取图片或CSS图标
   * @param {string} name 名称
   * @param {string} type 类型：'image' | 'css' | 'auto'
   * @returns {object} {type, path, icon}
   */
  getResource(name, type = "auto") {
    if (type === "image" || (type === "auto" && this.hasImage(name))) {
      return {
        type: "image",
        path: this.getImagePath(name),
        icon: null,
      };
    } else {
      return {
        type: "css",
        path: null,
        icon: this.getCSSIcon(name),
      };
    }
  }

  /**
   * 图片加载错误处理
   * @param {string} name 图片名称
   * @returns {string} 备用图标
   */
  handleImageError(name) {
    console.warn(`图片加载失败: ${name}, 使用CSS图标替代`);
    return this.getCSSIcon(name);
  }

  /**
   * 获取所有可用图片列表
   * @returns {Array} 图片列表
   */
  getAvailableImages() {
    return Object.keys(this.imageMap);
  }

  /**
   * 获取所有CSS图标列表
   * @returns {Array} CSS图标列表
   */
  getAvailableCSSIcons() {
    return Object.keys(this.cssIconMap);
  }
}

// 创建全局实例
const imageManager = new ImageManager();

// 导出
module.exports = {
  ImageManager,
  imageManager,
};

// 如果在小程序环境中，挂载到全局
if (typeof getApp === "function") {
  try {
    const app = getApp();
    if (app) {
      app.imageManager = imageManager;
    }
  } catch (e) {
    // 忽略错误，可能是在初始化阶段
  }
}
