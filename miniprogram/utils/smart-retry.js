// 智能重试机制
// 支持网络异常检测、指数退避重试、认证错误区分处理

const { authErrorDetector } = require("./auth-error-detector.js");

/**
 * 智能重试管理器
 * 区分网络问题和认证问题，提供不同的重试策略
 */
class SmartRetryManager {
  constructor() {
    // 重试配置
    this.config = {
      maxRetries: 3,
      baseDelay: 1000, // 基础延迟1秒
      maxDelay: 10000, // 最大延迟10秒
      backoffFactor: 2, // 指数退避因子
      networkTimeoutThreshold: 5000, // 网络超时阈值
    };
    
    // 重试统计
    this.retryStats = new Map();
  }

  /**
   * 判断是否应该重试
   * @param {Object} error 错误对象
   * @param {Object} response 响应对象
   * @param {number} attemptCount 当前尝试次数
   * @returns {Object} 重试决策
   */
  shouldRetry(error, response, attemptCount = 0) {
    const decision = {
      shouldRetry: false,
      retryType: null,
      delay: 0,
      reason: null,
      maxRetries: this.config.maxRetries
    };

    // 超过最大重试次数
    if (attemptCount >= this.config.maxRetries) {
      decision.reason = "exceeded_max_retries";
      return decision;
    }

    // 检查错误类型
    const errorAnalysis = this._analyzeError(error, response);
    
    switch (errorAnalysis.type) {
      case 'network':
        decision.shouldRetry = true;
        decision.retryType = 'network';
        decision.delay = this._calculateDelay(attemptCount);
        decision.reason = "network_error_retry";
        break;
        
      case 'timeout':
        decision.shouldRetry = true;
        decision.retryType = 'timeout';
        decision.delay = this._calculateDelay(attemptCount);
        decision.reason = "timeout_retry";
        break;
        
      case 'server_error':
        // 5xx错误可以重试，但次数较少
        if (attemptCount < 2) {
          decision.shouldRetry = true;
          decision.retryType = 'server_error';
          decision.delay = this._calculateDelay(attemptCount);
          decision.reason = "server_error_retry";
        } else {
          decision.reason = "server_error_max_retries";
        }
        break;
        
      case 'auth':
        // 认证错误不应该通过简单重试解决
        decision.shouldRetry = false;
        decision.reason = "auth_error_no_retry";
        break;
        
      case 'client_error':
        // 4xx客户端错误（除401/403）通常不应重试
        decision.shouldRetry = false;
        decision.reason = "client_error_no_retry";
        break;
        
      default:
        decision.reason = "unknown_error_no_retry";
    }

    console.log("🔄 重试决策:", {
      attemptCount,
      errorType: errorAnalysis.type,
      shouldRetry: decision.shouldRetry,
      delay: decision.delay,
      reason: decision.reason
    });

    return decision;
  }

  /**
   * 分析错误类型
   */
  _analyzeError(error, response) {
    // 检查是否为认证错误
    const authDetection = authErrorDetector.detectAuthError(error, response);
    if (authDetection.isAuthError) {
      return { type: 'auth', details: authDetection };
    }

    // 检查网络错误
    if (this._isNetworkError(error)) {
      return { type: 'network', details: { originalError: error } };
    }

    // 检查超时错误
    if (this._isTimeoutError(error)) {
      return { type: 'timeout', details: { originalError: error } };
    }

    // 检查HTTP状态码
    const statusCode = error?.statusCode || response?.statusCode;
    if (statusCode) {
      if (statusCode >= 500) {
        return { type: 'server_error', details: { statusCode } };
      } else if (statusCode >= 400) {
        return { type: 'client_error', details: { statusCode } };
      }
    }

    return { type: 'unknown', details: { error, response } };
  }

  /**
   * 检查是否为网络错误
   */
  _isNetworkError(error) {
    const networkErrorIndicators = [
      'network error',
      'connection failed',
      'request failed',
      'no internet',
      'dns error',
      'connection timeout'
    ];

    const errorMessage = (error?.message || error?.errMsg || '').toLowerCase();
    return networkErrorIndicators.some(indicator => 
      errorMessage.includes(indicator)
    );
  }

  /**
   * 检查是否为超时错误
   */
  _isTimeoutError(error) {
    const timeoutIndicators = [
      'timeout',
      'time out',
      'request timeout',
      'response timeout'
    ];

    const errorMessage = (error?.message || error?.errMsg || '').toLowerCase();
    return timeoutIndicators.some(indicator => 
      errorMessage.includes(indicator)
    );
  }

  /**
   * 计算重试延迟（指数退避）
   */
  _calculateDelay(attemptCount) {
    const delay = Math.min(
      this.config.baseDelay * Math.pow(this.config.backoffFactor, attemptCount),
      this.config.maxDelay
    );
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.3 * delay;
    return Math.floor(delay + jitter);
  }

  /**
   * 执行重试
   * @param {Function} requestFunction 请求函数
   * @param {Object} requestConfig 请求配置
   * @param {number} attemptCount 当前尝试次数
   * @returns {Promise} 请求结果
   */
  async executeWithRetry(requestFunction, requestConfig, attemptCount = 0) {
    const requestId = this._generateRequestId(requestConfig);
    
    try {
      console.log(`🔄 执行请求 (尝试 ${attemptCount + 1})`, {
        requestId,
        url: requestConfig.url,
        method: requestConfig.method
      });

      const result = await requestFunction(requestConfig);
      
      // 成功时清除重试统计
      this.retryStats.delete(requestId);
      
      console.log("✅ 请求成功", { requestId, attemptCount: attemptCount + 1 });
      return result;
      
    } catch (error) {
      console.log("❌ 请求失败", { 
        requestId, 
        attemptCount: attemptCount + 1,
        error: error.message 
      });

      const retryDecision = this.shouldRetry(error, error.response, attemptCount);
      
      if (retryDecision.shouldRetry) {
        // 更新重试统计
        this._updateRetryStats(requestId, attemptCount + 1, retryDecision);
        
        console.log(`🔄 将在 ${retryDecision.delay}ms 后重试`, {
          requestId,
          nextAttempt: attemptCount + 2,
          reason: retryDecision.reason
        });

        // 等待指定延迟
        await this._delay(retryDecision.delay);
        
        // 递归重试
        return this.executeWithRetry(requestFunction, requestConfig, attemptCount + 1);
      } else {
        // 不重试，记录最终失败
        this._updateRetryStats(requestId, attemptCount + 1, retryDecision, true);
        
        console.log("❌ 请求最终失败", {
          requestId,
          totalAttempts: attemptCount + 1,
          reason: retryDecision.reason
        });
        
        throw error;
      }
    }
  }

  /**
   * 生成请求ID
   */
  _generateRequestId(config) {
    const url = config.url || 'unknown';
    const method = config.method || 'GET';
    const timestamp = Date.now();
    return `${method}_${url.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}`;
  }

  /**
   * 更新重试统计
   */
  _updateRetryStats(requestId, attemptCount, decision, isFinal = false) {
    if (!this.retryStats.has(requestId)) {
      this.retryStats.set(requestId, {
        attempts: [],
        startTime: Date.now(),
        endTime: null,
        finalResult: null
      });
    }

    const stats = this.retryStats.get(requestId);
    stats.attempts.push({
      attemptNumber: attemptCount,
      timestamp: Date.now(),
      decision: decision
    });

    if (isFinal) {
      stats.endTime = Date.now();
      stats.finalResult = decision.shouldRetry ? 'retry_exhausted' : 'failed';
    }
  }

  /**
   * 延迟函数
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取重试统计
   */
  getRetryStats() {
    const stats = Array.from(this.retryStats.entries()).map(([id, data]) => ({
      requestId: id,
      ...data,
      duration: (data.endTime || Date.now()) - data.startTime,
      totalAttempts: data.attempts.length
    }));

    return {
      activeRequests: stats.filter(s => !s.endTime).length,
      completedRequests: stats.filter(s => s.endTime).length,
      totalRequests: stats.length,
      details: stats
    };
  }

  /**
   * 清理过期的统计数据
   */
  cleanupStats(maxAge = 300000) { // 5分钟
    const now = Date.now();
    for (const [id, stats] of this.retryStats.entries()) {
      if (stats.endTime && (now - stats.endTime) > maxAge) {
        this.retryStats.delete(id);
      }
    }
  }
}

// 创建单例实例
const smartRetryManager = new SmartRetryManager();

module.exports = {
  SmartRetryManager,
  smartRetryManager,
};
