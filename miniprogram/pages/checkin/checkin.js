// 打卡页面
const {
  childrenActions,
  startLoading,
  endLoading,
  isLoading,
} = require("../../utils/index.js");

const checkinAPI = require("../../apis/checkin.js");
const app = getApp();

Page({
  data: {
    taskInfo: {},
    campId: null,
    currentChild: null,
    videoId: "",
    checkinNote: "",
    practiceDuration: 10, // 练习时长（分钟）
    jumpCount1min: 0, // 1分钟跳绳个数
    jumpCountContinuous: 0, // 连续跳绳个数
    feelingScore: 8, // 感受评分 1-10
    rewardPoints: 20,
    willGetBadge: false,
    nextBadge: {},
    canSubmit: false,
    isSubmitting: false,
    showHelpModal: false,
  },

  onLoad(options) {
    console.log("打卡页面加载", options);
    this.initPage(options);
  },

  // 初始化页面
  initPage(options) {
    const task_id = options.task_id || 1; // 从URL参数获取，但内部使用snake_case
    const camp_id = options.camp_id || 1; // 从URL参数获取，但内部使用snake_case

    // 获取当前选择的孩子
    const currentChild = childrenActions.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      campId: camp_id,
      currentChild: currentChild,
    });

    this.loadTaskInfo(task_id, camp_id);
    this.checkRewards();
  },

  // 加载任务信息
  loadTaskInfo(taskId, campId) {
    // 模拟数据，实际应该从API获取
    const taskInfo = {
      id: taskId,
      camp_id: campId, // 使用snake_case字段名
      campTitle: "《零基础21天跳绳挑战营》",
      checkin_count: 7, // 已打卡次数
      totalCount: 21,
      todayTask: "连续跳绳50个",
      taskDescription:
        "保持稳定的节奏，连续完成50个跳绳动作，注意手腕发力和脚步协调。",
    };

    this.setData({
      taskInfo: taskInfo,
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: `第${taskInfo.checkin_count + 1}次打卡`,
    });
  },

  // 检查奖励信息
  checkRewards() {
    const checkinCount = this.data.taskInfo.checkin_count + 1;

    // 检查是否会获得勋章
    let willGetBadge = false;
    let nextBadge = {};

    if (checkinCount === 3) {
      willGetBadge = true;
      nextBadge = { name: "初学者", icon: "🌟" };
    } else if (checkinCount === 7) {
      willGetBadge = true;
      nextBadge = { name: "坚持之星", icon: "⭐" };
    } else if (checkinCount === 15) {
      willGetBadge = true;
      nextBadge = { name: "跳绳达人", icon: "🏅" };
    } else if (checkinCount === 21) {
      willGetBadge = true;
      nextBadge = { name: "挑战完成者", icon: "🏆" };
    }

    this.setData({
      willGetBadge: willGetBadge,
      nextBadge: nextBadge,
    });
  },

  // 视频ID输入
  onVideoIdInput(e) {
    const videoId = e.detail.value.trim();
    this.setData({
      videoId: videoId,
      canSubmit: videoId.length > 0,
    });
  },

  // 打卡备注输入
  onCheckinNoteInput(e) {
    const checkinNote = e.detail.value;
    this.setData({
      checkinNote: checkinNote,
    });
  },

  // 提交打卡
  async submitCheckin() {
    if (!this.data.canSubmit || this.data.isSubmitting) return;

    const {
      videoId,
      checkinNote,
      taskInfo,
      campId,
      currentChild,
      practiceDuration,
      jumpCount1min,
      jumpCountContinuous,
      feelingScore,
    } = this.data;

    // 简单验证
    if (!videoId) {
      wx.showToast({
        title: "请输入视频号链接",
        icon: "none",
      });
      return;
    }

    if (!currentChild || !currentChild.id) {
      wx.showToast({
        title: "孩子信息缺失",
        icon: "none",
      });
      return;
    }

    const loadingKey = "submit-checkin";

    try {
      startLoading(loadingKey, {
        title: "提交打卡中...",
        timeout: 15000,
      });

      this.setData({
        isSubmitting: true,
      });

      console.log("🏃‍♂️ 开始提交打卡记录");

      // 构建打卡数据
      const checkinData = {
        child_id: currentChild.id,
        camp_id: campId,
        practice_duration: practiceDuration,
        jump_count_1min: jumpCount1min,
        jump_count_continuous: jumpCountContinuous,
        feeling_text: checkinNote || "今日训练完成",
        feeling_score: feelingScore,
        photos: [videoId], // 暂时将视频ID作为照片URL
      };

      // 调用API提交打卡
      const response = await checkinAPI.createCheckin(checkinData);

      console.log("✅ 打卡提交成功:", response);

      endLoading(loadingKey, true);

      this.setData({
        isSubmitting: false,
      });

      // 跳转到打卡成功页面
      const params = {
        checkin_id: response.data?.id || Date.now(),
        count: taskInfo.checkin_count + 1,
        total: taskInfo.totalCount,
        points: response.data?.points_earned || this.data.rewardPoints,
        taskDescription: `🌐 API提交: ${taskInfo.todayTask}`,
        dataSource: "api",
      };

      if (this.data.willGetBadge) {
        params.newBadge = JSON.stringify(this.data.nextBadge);
      }

      const queryString = Object.keys(params)
        .map((key) => `${key}=${encodeURIComponent(params[key])}`)
        .join("&");

      wx.redirectTo({
        url: `/pages/checkin-success/checkin-success?${queryString}`,
      });
    } catch (error) {
      console.error("❌ 打卡提交失败:", error);

      endLoading(loadingKey, false);

      this.setData({
        isSubmitting: false,
      });

      wx.showToast({
        title: "打卡失败，请重试",
        icon: "none",
      });
    }

    // 实际的API调用代码示例：
    /*
    app.request({
      url: '/api/checkin/submit',
      method: 'POST',
      data: {
        task_id: taskInfo.id,
        videoId: videoId,
        note: checkinNote
      }
    }).then(res => {
      this.setData({
        isSubmitting: false
      })

      // 跳转到打卡成功页面
      wx.redirectTo({
        url: `/pages/checkin-success/checkin-success?checkinId=${res.data.id}`
      })
    }).catch(err => {
      this.setData({
        isSubmitting: false
      })

      app.showError(err.message || '打卡失败')
    })
    */
  },

  // 显示帮助
  showHelp() {
    this.setData({
      showHelpModal: true,
    });
  },

  // 隐藏帮助
  hideHelp() {
    this.setData({
      showHelpModal: false,
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  },

  // 分享
  onShareAppMessage() {
    const task = this.data.taskInfo;
    return {
      title: `我正在进行${task.campTitle}的第${task.checkin_count + 1}次打卡！`,
      path: "/pages/home/<USER>",
      image_url: "/images/share-checkin.png",
    };
  },

  onShareTimeline() {
    return {
      title: "跳跳星球 - 坚持打卡，健康成长",
      image_url: "/images/share-checkin.png",
    };
  },
});
