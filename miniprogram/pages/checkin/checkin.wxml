<!-- 打卡页面 -->
<view class="page-container">
    <!-- 顶部任务信息 -->
    <view class="task-header">
        <view class="task-info">
            <text class="task-title">{{taskInfo.campTitle}}</text>
            <text class="task-subtitle">第{{taskInfo.checkinCount + 1}}次打卡</text>
        </view>
        <view class="task-progress">
            <view class="progress-circle">
                <text class="progress-text">{{taskInfo.checkinCount}}/{{taskInfo.totalCount}}</text>
            </view>
        </view>
    </view>
    <!-- 今日任务 -->
    <view class="today-task-section">
        <view class="section-title">
            <text class="emoji">🎯</text>
            <text>今日任务</text>
        </view>
        <view class="task-card">
            <view class="task-icon">
                <view class="task-icon-emoji">📋</view>
            </view>
            <view class="task-content">
                <text class="task-name">{{taskInfo.todayTask}}</text>
                <text class="task-description">{{taskInfo.taskDescription}}</text>
            </view>
        </view>
    </view>
    <!-- 打卡说明 -->
    <view class="checkin-guide-section">
        <view class="section-title">
            <text class="emoji">📱</text>
            <text>打卡方式</text>
        </view>
        <view class="guide-steps">
            <view class="step-item">
                <view class="step-number">1</view>
                <view class="step-content">
                    <text class="step-title">录制视频</text>
                    <text class="step-desc">完成今日任务后，录制跳绳视频</text>
                </view>
            </view>
            <view class="step-item">
                <view class="step-number">2</view>
                <view class="step-content">
                    <text class="step-title">发布到视频号</text>
                    <text class="step-desc">将视频发布到微信视频号</text>
                </view>
            </view>
            <view class="step-item">
                <view class="step-number">3</view>
                <view class="step-content">
                    <text class="step-title">提交视频号ID</text>
                    <text class="step-desc">复制视频号链接，在下方提交</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 视频号ID输入 -->
    <view class="checkin-form-section">
        <view class="section-title">
            <text class="emoji">✅</text>
            <text>提交打卡</text>
        </view>
        <view class="form-card">
            <view class="input-group">
                <text class="input-label">视频号链接或ID</text>
                <input class="input-field" placeholder="请粘贴视频号链接或输入视频ID" value="{{videoId}}" bindinput="onVideoIdInput" maxlength="200" />
                <text class="input-hint">示例：https://channels.weixin.qq.com/...</text>
            </view>
            <view class="input-group">
                <text class="input-label">完成情况 (可选)</text>
                <textarea class="textarea-field" placeholder="分享一下今天的训练感受吧~" value="{{checkinNote}}" bindinput="onCheckinNoteInput" maxlength="200" show-confirm-bar="{{false}}" />
                <text class="input-hint">{{checkinNote.length}}/200字</text>
            </view>
        </view>
    </view>
    <!-- 激励信息 -->
    <view class="motivation-section">
        <view class="motivation-card">
            <view class="motivation-icon">🎉</view>
            <view class="motivation-content">
                <text class="motivation-title">坚持就是胜利！</text>
                <text class="motivation-text">完成打卡可获得 {{rewardPoints}} 积分</text>
                <text class="motivation-text" wx:if="{{willGetBadge}}">
                    还将获得"{{nextBadge.name}}"勋章
                </text>
            </view>
        </view>
    </view>
    <!-- 底部提交按钮 -->
    <view class="bottom-action">
        <button class="btn btn-primary btn-large btn-block checkin-btn" bindtap="submitCheckin" disabled="{{!canSubmit || isSubmitting}}">
            {{isSubmitting ? '提交中...' : '完成打卡'}}
        </button>
        <view class="help-section">
            <text class="help-text">遇到问题？</text>
            <text class="help-link" bindtap="showHelp">查看帮助</text>
        </view>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 帮助弹窗 -->
<view class="help-modal {{showHelpModal ? 'show' : ''}}" bindtap="hideHelp">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">打卡帮助</text>
            <view class="modal-close" bindtap="hideHelp">×</view>
        </view>
        <view class="modal-body">
            <view class="help-content">
                <view class="help-item">
                    <text class="help-question">Q: 如何获取视频号链接？</text>
                    <text class="help-answer">A: 在视频号中点击分享按钮，选择"复制链接"即可。</text>
                </view>
                <view class="help-item">
                    <text class="help-question">Q: 视频有什么要求？</text>
                    <text class="help-answer">A: 视频需要清晰展示跳绳过程，时长建议30秒以上。</text>
                </view>
                <view class="help-item">
                    <text class="help-question">Q: 打卡失败怎么办？</text>
                    <text class="help-answer">A: 请检查网络连接和视频链接是否正确，或联系客服。</text>
                </view>
            </view>
        </view>
    </view>
</view>