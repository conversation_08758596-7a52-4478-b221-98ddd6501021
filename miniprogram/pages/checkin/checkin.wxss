/* 打卡页面样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
}

/* 顶部任务信息 */
.task-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #E6692D 100%);
  padding: var(--spacing-lg) var(--spacing-md);
  padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top));
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.task-info {
  flex: 1;
}

.task-title {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: var(--spacing-xs);
}

.task-subtitle {
  font-size: var(--font-md);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.task-progress {
  
}

.progress-circle {
  width: 100rpx;
  height: 100rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  /* backdrop-filter: blur(10rpx); */
}

.progress-text {
  font-size: var(--font-sm);
  font-weight: 600;
  color: #FFFFFF;
}

/* 内容区域通用样式 */
.today-task-section,
.checkin-guide-section,
.checkin-form-section,
.motivation-section {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-title .emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 今日任务卡片 */
.task-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.task-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: var(--spacing-md);
  background-color: #FFF2E8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-icon image {
  width: 48rpx;
  height: 48rpx;
}

.task-content {
  flex: 1;
}

.task-name {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-description {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 打卡说明步骤 */
.guide-steps {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.step-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid #F5F5F5;
}

.step-item:last-child {
  border-bottom: none;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: var(--primary-color);
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-md);
  font-weight: 600;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.step-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 表单区域 */
.form-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-label {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.input-field,
.textarea-field {
  width: 100%;
  padding: var(--spacing-md);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: var(--font-md);
  color: var(--text-primary);
  background-color: #FAFAFA;
  box-sizing: border-box;
}

/* 微信小程序不支持:focus伪类
.input-field:focus,
.textarea-field:focus {
  border-color: var(--primary-color);
  background-color: #FFFFFF;
}
*/

.textarea-field {
  min-height: 120rpx;
  /* resize: none; */
}

.input-hint {
  display: block;
  font-size: var(--font-xs);
  color: var(--text-placeholder);
  margin-top: var(--spacing-xs);
}

/* 激励信息 */
.motivation-card {
  background: linear-gradient(135deg, #FFF2E8 0%, #FFE8E0 100%);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  border: 2rpx solid #FFD6CC;
}

.motivation-icon {
  font-size: var(--font-xxl);
  margin-right: var(--spacing-md);
}

.motivation-content {
  flex: 1;
}

.motivation-title {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.motivation-text {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-xs);
}

.motivation-text:last-child {
  margin-bottom: 0;
}

/* 底部操作区域 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: var(--spacing-md);
  padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
  border-top: 2rpx solid var(--border-color);
  z-index: 100;
}

.checkin-btn {
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 122, 69, 0.3);
  margin-bottom: var(--spacing-md);
}

.checkin-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 122, 69, 0.3);
}

.checkin-btn:disabled {
  opacity: 0.5;
  transform: none;
  box-shadow: none;
}

.help-section {
  text-align: center;
}

.help-text {
  font-size: var(--font-sm);
  color: var(--text-placeholder);
  margin-right: var(--spacing-sm);
}

.help-link {
  font-size: var(--font-sm);
  color: var(--info-color);
  text-decoration: underline;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}

/* 帮助弹窗 */
.help-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.help-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.help-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  background-color: #F5F5F5;
  border-radius: 50%;
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.help-content {
  
}

.help-item {
  margin-bottom: var(--spacing-lg);
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-question {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.help-answer {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}
