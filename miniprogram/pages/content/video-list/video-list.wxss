/* 视频列表页样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 顶部信息 */
.header-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #E6692D 100%);
  padding: var(--spacing-lg) var(--spacing-md);
  padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top));
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.page-title {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: var(--spacing-xs);
}

.video-count {
  font-size: var(--font-sm);
  color: rgba(255, 255, 255, 0.9);
}

/* 筛选标签 */
.filter-section {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.filter-tabs {
  display: flex;
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.filter-tab.active {
  background-color: var(--primary-color);
}

.filter-tab.active .tab-text,
.filter-tab.active .tab-count {
  color: #FFFFFF;
}

.tab-text {
  display: block;
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.tab-count {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* 视频列表 */
.video-list-section {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.video-list {
  
}

.video-item {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  transition: transform 0.3s ease;
}

/* 视频缩略图组件样式 */
.video-item-thumbnail {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.video-item:active {
  transform: scale(0.98);
}

/* 以下样式已由 video-thumbnail 组件处理，保留作为参考 */
/*
.video-thumbnail {
  position: relative;
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.video-thumbnail image {
  width: 100%;
  height: 100%;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-lg);
}

.video-duration {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.7);
  color: #FFFFFF;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
}

.video-type-badge {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 500;
}

.video-type-badge.course {
  background-color: var(--info-color);
  color: #FFFFFF;
}
*/

.video-type-badge.demo {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.video-info {
  flex: 1;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.video-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-description {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.meta-icon {
  font-size: var(--font-xs);
}

.meta-text {
  font-size: var(--font-xs);
  color: var(--text-placeholder);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-xxl);
}

.empty-icon {
  font-size: var(--font-xxl);
  margin-bottom: var(--spacing-md);
}

.empty-text {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

/* 底部提示 */
.bottom-tip {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.tip-content {
  background-color: #F7F8FA;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tip-icon {
  font-size: var(--font-md);
}

.tip-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  flex: 1;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}
