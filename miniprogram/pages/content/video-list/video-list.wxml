<!-- 视频列表页 -->
<view class="page-container">
    <!-- 顶部信息 -->
    <view class="header-section">
        <text class="page-title">{{campTitle}} - 教学视频</text>
        <text class="video-count">共{{totalVideos}}个视频</text>
    </view>
    <!-- 筛选标签 -->
    <view class="filter-section">
        <view class="filter-tabs">
            <view class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}" bindtap="switchFilter" data-filter="all">
                <text class="tab-text">全部</text>
                <text class="tab-count">({{allVideos.length}})</text>
            </view>
            <view class="filter-tab {{currentFilter === 'course' ? 'active' : ''}}" bindtap="switchFilter" data-filter="course">
                <text class="tab-text">课程视频</text>
                <text class="tab-count">({{courseVideos.length}})</text>
            </view>
            <view class="filter-tab {{currentFilter === 'demo' ? 'active' : ''}}" bindtap="switchFilter" data-filter="demo">
                <text class="tab-text">示范视频</text>
                <text class="tab-count">({{demoVideos.length}})</text>
            </view>
        </view>
    </view>
    <!-- 视频列表 -->
    <view class="video-list-section">
        <view class="video-list">
            <view class="video-item" wx:for="{{displayVideos}}" wx:key="id" bindtap="playVideo" data-video="{{item}}">
                <video-thumbnail thumbnail="{{item.thumbnail}}" video-type="{{item.type}}" duration="{{item.duration}}" view-count="{{item.viewCount}}" show-play-button="{{true}}" show-type-badge="{{true}}" show-view-count="{{false}}" custom-class="video-item-thumbnail"></video-thumbnail>
                <view class="video-info">
                    <text class="video-title">{{item.title}}</text>
                    <text class="video-description">{{item.description}}</text>
                    <view class="video-meta">
                        <view class="meta-item">
                            <text class="meta-icon">👨‍🏫</text>
                            <text class="meta-text">{{item.instructor}}</text>
                        </view>
                        <view class="meta-item">
                            <text class="meta-icon">👀</text>
                            <text class="meta-text">{{item.viewCount}}次观看</text>
                        </view>
                        <view class="meta-item" wx:if="{{item.type === 'course'}}">
                            <text class="meta-icon">🎯</text>
                            <text class="meta-text">学习后可打卡</text>
                        </view>
                        <view class="meta-item" wx:else>
                            <text class="meta-icon">👁️</text>
                            <text class="meta-text">随时参考观看</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{displayVideos.length === 0}}">
            <view class="empty-icon">📹</view>
            <text class="empty-text">暂无{{filterText}}视频</text>
        </view>
    </view>
    <!-- 底部提示 -->
    <view class="bottom-tip">
        <view class="tip-content">
            <text class="tip-icon">💡</text>
            <text class="tip-text">课程视频：学习后可打卡练习 | 示范视频：随时参考观看</text>
        </view>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>