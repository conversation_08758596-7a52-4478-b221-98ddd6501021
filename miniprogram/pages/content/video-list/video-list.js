// 视频列表页
const app = getApp();

Page({
  data: {
    camp_id: null,
    campTitle: "",
    currentFilter: "all",
    allVideos: [],
    courseVideos: [],
    demoVideos: [],
    displayVideos: [],
    totalVideos: 0,
    filterText: "",
  },

  onLoad(options) {
    console.log("视频列表页加载", options);
    this.initPage(options);
  },

  // 初始化页面
  initPage(options) {
    const campId = parseInt(options.camp_id) || 1;
    const type = options.type || "all";

    this.setData({
      camp_id: campId,
      currentFilter: type,
    });

    this.loadCampInfo(campId);
    this.loadVideoList(campId);
  },

  // 加载训练营信息
  loadCampInfo(campId) {
    // 模拟数据，实际应该从API获取
    const campInfo = {
      1: { title: "《零基础21天跳绳挑战营》" },
      2: { title: "《暑假跳绳追高计划》" },
    };

    const camp = campInfo[campId] || campInfo[1];
    this.setData({
      campTitle: camp.title,
    });
  },

  // 加载视频列表
  loadVideoList(campId) {
    // 模拟视频数据，实际应该从API获取
    const videoData = {
      1: [
        {
          id: 1,
          title: "第1课：跳绳基础姿势",
          description: "学习正确的握绳方法和基本站姿",
          thumbnail: "video-thumb1", // 使用imageManager中的CSS图标
          duration: "2:30",
          type: "course",
          instructor: "教练小王",
          view_count: 2345,
        },
        {
          id: 2,
          title: "第2课：手腕发力技巧",
          description: "掌握正确的手腕转动方法",
          thumbnail: "video-thumb2", // 使用imageManager中的CSS图标
          duration: "3:15",
          type: "course",
          instructor: "教练小王",
          view_count: 1876,
        },
        {
          id: 3,
          title: "常见错误纠正",
          description: "避免初学者常犯的5个错误",
          thumbnail: "video-thumb3", // 使用imageManager中的CSS图标
          duration: "1:45",
          type: "demo",
          instructor: "教练小王",
          view_count: 3421,
        },
        {
          id: 4,
          title: "第3课：连续跳绳练习",
          description: "从单次跳跃到连续跳绳的过渡",
          thumbnail: "video-thumb4", // 使用imageManager中的CSS图标
          duration: "4:20",
          type: "course",
          instructor: "教练小王",
          view_count: 1654,
        },
        {
          id: 5,
          title: "节奏感训练",
          description: "培养跳绳的节奏感和协调性",
          thumbnail: "video-thumb5", // 使用imageManager中的CSS图标
          duration: "3:00",
          type: "demo",
          instructor: "教练小王",
          view_count: 987,
        },
        {
          id: 6,
          title: "第4课：挑战50个",
          description: "挑战连续跳绳50个的目标",
          thumbnail: "video-thumb6", // 使用imageManager中的CSS图标
          duration: "5:30",
          type: "course",
          instructor: "教练小王",
          view_count: 1234,
        },
      ],
      2: [
        {
          id: 7,
          title: "科学跳绳方法",
          description: "结合身高增长的科学跳绳训练",
          thumbnail: "video-thumb7", // 使用imageManager中的CSS图标
          duration: "5:00",
          type: "course",
          instructor: "教练小李",
          view_count: 1567,
        },
        {
          id: 8,
          title: "营养搭配指导",
          description: "跳绳训练期间的营养补充建议",
          thumbnail: "video-thumb8", // 使用imageManager中的CSS图标
          duration: "3:30",
          type: "course",
          instructor: "营养师小张",
          view_count: 892,
        },
      ],
    };

    const allVideos = videoData[campId] || [];
    const courseVideos = allVideos.filter((video) => video.type === "course");
    const demoVideos = allVideos.filter((video) => video.type === "demo");

    this.setData({
      allVideos: allVideos,
      courseVideos: courseVideos,
      demoVideos: demoVideos,
      totalVideos: allVideos.length,
    });

    // 根据当前筛选条件显示视频
    this.updateDisplayVideos();
  },

  // 切换筛选条件
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      currentFilter: filter,
    });

    this.updateDisplayVideos();
  },

  // 更新显示的视频列表
  updateDisplayVideos() {
    const { currentFilter, allVideos, courseVideos, demoVideos } = this.data;
    let displayVideos = [];
    let filterText = "";

    switch (currentFilter) {
      case "all":
        displayVideos = allVideos;
        filterText = "";
        break;
      case "course":
        displayVideos = courseVideos;
        filterText = "课程";
        break;
      case "demo":
        displayVideos = demoVideos;
        filterText = "示范";
        break;
    }

    this.setData({
      displayVideos: displayVideos,
      filterText: filterText,
    });
  },

  // 播放视频
  playVideo(e) {
    const video = e.currentTarget.dataset.video;
    wx.navigateTo({
      url: `/pages/video-play/video-play?id=${video.id}&campId=${this.data.camp_id}`,
    });
  },

  // 分享
  onShareAppMessage() {
    const camp = this.data.campTitle;
    return {
      title: `${camp} - 专业教学视频`,
      path: `/pages/video-list/video-list?campId=${this.data.camp_id}`,
      image_url: "/images/share-video-list.png",
    };
  },

  onShareTimeline() {
    const camp = this.data.campTitle;
    return {
      title: `${camp} - 跳跳星球`,
      image_url: "/images/share-video-list.png",
    };
  },
});
