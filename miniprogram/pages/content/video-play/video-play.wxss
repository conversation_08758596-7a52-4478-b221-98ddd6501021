/* 视频播放页样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 视频播放区域 */
.video-player-section {
  background-color: #000000;
  position: relative;
}

.video-player {
  width: 100%;
  height: 420rpx;
}

/* 视频信息区域 */
.video-info-section {
  padding: var(--spacing-lg) var(--spacing-md);
  background-color: #FFFFFF;
  margin-bottom: var(--spacing-md);
}

.video-header {
  margin-bottom: var(--spacing-lg);
}

.video-title {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: var(--spacing-md);
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.meta-icon {
  font-size: var(--font-sm);
}

.meta-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.video-type-tag {
  
}

.type-tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 500;
}

.type-tag.course {
  background-color: #E6F7FF;
  color: var(--info-color);
}

.type-tag.demo {
  background-color: #FFF2E8;
  color: var(--primary-color);
}

/* 视频描述 */
.video-description {
  
}

.description-title {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.description-content {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.key-points {
  background-color: #F7F8FA;
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
}

.points-title {
  display: block;
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.point-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.point-item:last-child {
  margin-bottom: 0;
}

.point-bullet {
  color: var(--primary-color);
  font-weight: 600;
  margin-right: var(--spacing-sm);
  margin-top: 2rpx;
}

.point-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  flex: 1;
}

/* 相关视频区域 */
.related-videos-section {
  padding: 0 var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-title .emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 文字列表样式 */
.video-text-list {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.video-text-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1rpx solid #F0F0F0;
  transition: background-color 0.2s ease;
}

.video-text-item:last-child {
  border-bottom: none;
}

.video-text-item:active {
  background-color: #F8F9FA;
}

.video-number {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--primary-color);
  width: 60rpx;
  flex-shrink: 0;
}

.video-content {
  flex: 1;
  margin-left: var(--spacing-md);
  margin-right: var(--spacing-md);
}

.video-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.video-title {
  font-size: var(--font-md);
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
  margin-right: var(--spacing-sm);
}

.video-duration {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.video-meta-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.video-type {
  font-size: var(--font-xs);
  padding: 4rpx 12rpx;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.video-type.course {
  background-color: #E6F7FF;
  color: var(--info-color);
}

.video-type.demo {
  background-color: #FFF2E8;
  color: var(--primary-color);
}

.video-instructor {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.play-arrow {
  font-size: var(--font-lg);
  color: var(--primary-color);
  flex-shrink: 0;
}

/* 查看更多按钮 */
.view-more-text-item {
  padding: var(--spacing-md);
  border-top: 1rpx solid #F0F0F0;
  background-color: #FAFAFA;
}

.view-more-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
}

.view-more-text {
  font-size: var(--font-md);
  color: var(--primary-color);
  font-weight: 500;
}

.view-more-arrow {
  font-size: var(--font-md);
  color: var(--primary-color);
}



/* 操作按钮区域 */
.action-section {
  padding: var(--spacing-md);
  background-color: #FFFFFF;
  margin-bottom: var(--spacing-lg);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  min-height: 88rpx;
}

.btn-icon {
  font-size: var(--font-lg);
}

.btn-text {
  font-size: var(--font-sm);
  font-weight: 500;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}
