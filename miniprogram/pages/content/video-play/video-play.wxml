<!-- 视频播放页 -->
<view class="page-container">
    <!-- 视频播放区域 -->
    <view class="video-player-section">
        <video class="video-player" src="{{videoInfo.videoUrl}}" poster="{{videoInfo.thumbnail}}" controls="{{true}}" autoplay="{{false}}" loop="{{false}}" muted="{{false}}" show-center-play-btn="{{true}}" show-play-btn="{{true}}" show-fullscreen-btn="{{true}}" bindplay="onVideoPlay" bindpause="onVideoPause" bindended="onVideoEnded" binderror="onVideoError"></video>
    </view>
    <!-- 视频信息区域 -->
    <view class="video-info-section">
        <view class="video-header">
            <text class="video-title">{{videoInfo.title}}</text>
            <view class="video-meta">
                <view class="meta-item">
                    <text class="meta-icon">👨‍🏫</text>
                    <text class="meta-text">{{videoInfo.instructor}}</text>
                </view>
                <view class="meta-item">
                    <text class="meta-icon">📅</text>
                    <text class="meta-text">{{videoInfo.publishDate}}</text>
                </view>
                <view class="meta-item">
                    <text class="meta-icon">👀</text>
                    <text class="meta-text">{{videoInfo.viewCount}}次观看</text>
                </view>
            </view>
            <view class="video-type-tag">
                <text class="type-tag {{videoInfo.type}}">
                    {{videoInfo.type === 'course' ? '课程视频' : '示范视频'}}
                </text>
            </view>
        </view>
        <view class="video-description" wx:if="{{videoInfo.description}}">
            <text class="description-title">📖 视频介绍</text>
            <text class="description-content">{{videoInfo.description}}</text>
            <view class="key-points" wx:if="{{videoInfo.keyPoints && videoInfo.keyPoints.length > 0}}">
                <text class="points-title">重点内容：</text>
                <view class="point-item" wx:for="{{videoInfo.keyPoints}}" wx:key="*this">
                    <text class="point-bullet">•</text>
                    <text class="point-text">{{item}}</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 相关视频区域 - 文字列表形式 -->
    <view class="related-videos-section" wx:if="{{relatedVideos.length > 0}}">
        <view class="section-title">
            <text class="emoji">📹</text>
            <text>相关视频 (共{{totalRelatedCount}}个)</text>
        </view>
        <!-- 文字列表形式 -->
        <view class="video-text-list">
            <!-- 相关视频项 -->
            <view class="video-text-item" wx:for="{{relatedVideos}}" wx:key="id" wx:if="{{index < 5}}" bindtap="playRelatedVideo" data-video="{{item}}">
                <view class="video-number">
                    {{index + 1 < 10 ? '0' + (index + 1) : index + 1}}.
                </view>
                <view class="video-content">
                    <view class="video-title-row">
                        <text class="video-title">{{item.title}}</text>
                        <text class="video-duration">{{item.duration}}</text>
                    </view>
                    <view class="video-meta-row">
                        <text class="video-type {{item.type}}">
                            {{item.type === 'course' ? '课程视频' : '示范视频'}}
                        </text>
                        <text class="video-instructor" wx:if="{{item.instructor}}">
                            {{item.instructor}}
                        </text>
                    </view>
                </view>
                <view class="play-arrow">▶</view>
            </view>
            <!-- 查看更多按钮 -->
            <view class="view-more-text-item" wx:if="{{totalRelatedCount > 5}}" bindtap="goToVideoList">
                <view class="view-more-content">
                    <text class="view-more-text">查看全部 {{totalRelatedCount}} 个视频</text>
                    <view class="view-more-arrow">→</view>
                </view>
            </view>
        </view>
    </view>
    <!-- 操作按钮区域 -->
    <view class="action-section">
        <view class="action-buttons">
            <button class="btn btn-outline action-btn" bindtap="collectVideo">
                <text class="btn-icon">{{videoInfo.isCollected ? '❤️' : '🤍'}}</text>
                <text class="btn-text">{{videoInfo.isCollected ? '已收藏' : '收藏'}}</text>
            </button>
            <button class="btn btn-primary action-btn" bindtap="startPractice" wx:if="{{videoInfo.type === 'course'}}">
                <text class="btn-icon">🎯</text>
                <text class="btn-text">开始练习</text>
            </button>
            <button class="btn btn-secondary action-btn" bindtap="shareVideo">
                <text class="btn-icon">📤</text>
                <text class="btn-text">分享视频</text>
            </button>
        </view>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>