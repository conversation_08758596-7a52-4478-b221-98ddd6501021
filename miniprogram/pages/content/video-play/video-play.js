// 视频播放页
const app = getApp();

Page({
  data: {
    videoId: null,
    camp_id: null,
    videoInfo: {},
    relatedVideos: [],
    totalRelatedCount: 0,
  },

  onLoad(options) {
    console.log("视频播放页加载", options);
    this.initPage(options);
  },

  // 初始化页面
  initPage(options) {
    const videoId = parseInt(options.id) || 1;
    const campId = parseInt(options.camp_id) || 1;

    this.setData({
      videoId: videoId,
      camp_id: campId,
    });

    this.loadVideoInfo(videoId);
    this.loadRelatedVideos(videoId, campId);
  },

  // 加载视频信息
  loadVideoInfo(videoId) {
    // 模拟数据，实际应该从API获取
    const videoData = {
      1: {
        id: 1,
        title: "基础姿势教学",
        video_url: "https://example.com/video1.mp4",
        thumbnail: "/images/video-thumb1.png",
        duration: "2:30",
        type: "demo",
        instructor: "教练小王",
        publishDate: "2024-12-30",
        view_count: 2345,
        isCollected: false,
        description:
          "本视频详细讲解跳绳的基础姿势，包括正确的握绳方法、站姿和基本动作要领。",
        keyPoints: [
          "正确的握绳方法",
          "标准的站姿要求",
          "手腕发力技巧",
          "脚步协调要点",
        ],
      },
      2: {
        id: 2,
        title: "手腕发力技巧",
        video_url: "https://example.com/video2.mp4",
        thumbnail: "/images/video-thumb2.png",
        duration: "3:15",
        type: "course",
        instructor: "教练小王",
        publishDate: "2024-12-29",
        view_count: 1876,
        isCollected: true,
        description: "掌握正确的手腕转动方法，是连续跳绳的关键技巧。",
        keyPoints: [
          "手腕转动的正确方法",
          "避免用手臂发力",
          "保持手腕灵活性",
          "练习节奏控制",
        ],
      },
    };

    const videoInfo = videoData[videoId] || videoData[1];
    this.setData({
      videoInfo: videoInfo,
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: videoInfo.title,
    });
  },

  // 加载相关视频
  loadRelatedVideos(currentVideoId, campId) {
    // 模拟相关视频数据
    const allVideos = [
      {
        id: 1,
        title: "基础姿势教学",
        thumbnail: "/images/video-thumb1.png",
        duration: "2:30",
        type: "demo",
        instructor: "教练小李",
      },
      {
        id: 2,
        title: "手腕发力技巧",
        thumbnail: "/images/video-thumb2.png",
        duration: "3:15",
        type: "course",
        instructor: "教练小王",
      },
      {
        id: 3,
        title: "连续跳绳练习",
        thumbnail: "/images/video-thumb3.png",
        duration: "4:20",
        type: "course",
        instructor: "教练小张",
      },
      {
        id: 4,
        title: "常见错误纠正",
        thumbnail: "/images/video-thumb4.png",
        duration: "1:45",
        type: "demo",
        instructor: "教练小李",
      },
      {
        id: 5,
        title: "节奏感训练",
        thumbnail: "/images/video-thumb5.png",
        duration: "3:00",
        type: "course",
        instructor: "教练小陈",
      },
      {
        id: 6,
        title: "进阶技巧展示",
        thumbnail: "/images/video-thumb6.png",
        duration: "2:45",
        type: "demo",
        instructor: "教练小王",
      },
    ];

    // 过滤掉当前视频
    const relatedVideos = allVideos.filter(
      (video) => video.id !== currentVideoId
    );

    this.setData({
      relatedVideos: relatedVideos,
      totalRelatedCount: relatedVideos.length,
    });
  },

  // 播放相关视频
  playRelatedVideo(e) {
    const video = e.currentTarget.dataset.video;

    // 重新加载当前页面，切换到新视频
    wx.redirectTo({
      url: `/pages/video-play/video-play?id=${video.id}&campId=${this.data.camp_id}`,
    });
  },

  // 跳转到视频列表
  goToVideoList() {
    wx.navigateTo({
      url: `/pages/video-list/video-list?campId=${this.data.camp_id}&type=related`,
    });
  },

  // 收藏视频
  collectVideo() {
    const isCollected = !this.data.videoInfo.isCollected;

    this.setData({
      "videoInfo.isCollected": isCollected,
    });

    app.showSuccess(isCollected ? "已收藏" : "已取消收藏");
  },

  // 开始练习
  startPractice() {
    const currentChild = app.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/checkin/checkin?taskId=1&campId=${this.data.camp_id}`,
    });
  },

  // 分享视频
  shareVideo() {
    wx.showShareMenu({
      withShareTicket: true,
    });
  },

  // 视频播放事件
  onVideoPlay() {
    console.log("视频开始播放");
  },

  onVideoPause() {
    console.log("视频暂停");
  },

  onVideoEnded() {
    console.log("视频播放结束");
  },

  onVideoError(e) {
    console.error("视频播放错误", e);
    wx.showToast({
      title: "视频加载失败",
      icon: "error",
    });
  },

  // 分享
  onShareAppMessage() {
    const video = this.data.videoInfo;
    return {
      title: `${video.title} - 专业跳绳教学`,
      path: `/pages/video-play/video-play?id=${video.id}&campId=${this.data.camp_id}`,
      image_url: video.thumbnail,
    };
  },

  onShareTimeline() {
    const video = this.data.videoInfo;
    return {
      title: `${video.title} - 跳跳星球`,
      image_url: video.thumbnail,
    };
  },
});
