<!-- 我的页面 - 个人管理中心 -->
<view class="page">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <view class="user-avatar" bindtap="editUserInfo">
        <view class="default-user-avatar" wx:if="{{!userInfo.avatar}}">👤</view>
        <image wx:else src="{{userInfo.avatar}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || '微信用户'}}</text>
        <text class="user-id">ID: {{userInfo.id || '000000'}}</text>
      </view>
    </view>
  </view>
  <!-- 当前孩子信息 -->
  <view class="weui-cells__title" wx:if="{{currentChild.id}}">当前孩子</view>
  <view class="weui-cells weui-cells_after-title" wx:if="{{currentChild.id}}">
    <navigator url="/pages/auth/child-manage/child-manage" class="weui-cell weui-cell_access" hover-class="weui-cell_active">
      <view class="weui-cell__hd">
        <view class="child-avatar">
          <view class="default-child-avatar" wx:if="{{!currentChild.avatar || currentChild.isEmojiAvatar}}">
            {{currentChild.avatar || '👶'}}
          </view>
          <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
        </view>
      </view>
      <view class="weui-cell__bd">
        <view class="child-info">
          <text class="child-name">{{currentChild.name}}</text>
          <text class="child-age">{{currentChild.age}}岁</text>
        </view>
      </view>
      <!-- <view class="weui-cell__ft">
        <view class="child-stats">
          <text class="stat-text">
            {{currentChild.totalCheckins || 0}}次打卡 · {{currentChild.totalPoints || 0}}积分
          </text>
        </view>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view> -->
    </navigator>
  </view>
  <!-- 无孩子状态 -->
  <block wx:else>
    <view class="weui-cells__title">孩子管理</view>
    <view class="weui-cells weui-cells_after-title">
      <navigator url="/pages/child-create/child-create" class="weui-cell weui-cell_access" hover-class="weui-cell_active">
        <view class="weui-cell__hd">
          <view class="no-child-icon">👶</view>
        </view>
        <view class="weui-cell__bd">
          <text>还没有添加孩子信息</text>
        </view>
        <view class="weui-cell__ft">
          <text class="weui-cell__ft_in-access">点击添加</text>
        </view>
        <view class="weui-cell__ft weui-cell__ft_in-access"></view>
      </navigator>
    </view>
  </block>
  <!-- 快捷功能
  <view class="weui-cells__title">⚡ 快捷功能</view>
  <view class="weui-cells weui-cells_after-title">
    <navigator url="/pages/child-manage/child-manage" class="weui-cell weui-cell_access" hover-class="weui-cell_active">
      <view class="weui-cell__hd">
        <view class="function-icon">👶</view>
      </view>
      <view class="weui-cell__bd">
        <view>孩子管理</view>
        <view class="weui-cell__desc">管理孩子档案信息</view>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </navigator>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="goToBadges">
      <view class="weui-cell__hd">
        <view class="function-icon">🏅</view>
      </view>
      <view class="weui-cell__bd">
        <view>我的勋章</view>
        <view class="weui-cell__desc">查看获得的成就勋章</view>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="goToInvite">
      <view class="weui-cell__hd">
        <view class="function-icon">👥</view>
      </view>
      <view class="weui-cell__bd">
        <view>邀请好友</view>
        <view class="weui-cell__desc">邀请好友一起运动</view>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="goToShareCard">
      <view class="weui-cell__hd">
        <view class="function-icon">🎨</view>
      </view>
      <view class="weui-cell__bd">
        <view>分享卡片</view>
        <view class="weui-cell__desc">生成精美的成就卡片</view>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
  </view> -->
  <!-- 设置 -->
  <view class="weui-cells__title">⚙️ 设置</view>
  <view class="weui-cells weui-cells_after-title">
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="goToChildManage">
      <view class="weui-cell__hd">
        <view class="function-icon">👶</view>
      </view>
      <view class="weui-cell__bd">孩子管理</view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="goToSettings">
      <view class="weui-cell__hd">
        <view class="function-icon">⚙️</view>
      </view>
      <view class="weui-cell__bd">通用设置</view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="showAbout">
      <view class="weui-cell__hd">
        <view class="function-icon">ℹ️</view>
      </view>
      <view class="weui-cell__bd">关于我们</view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="showFeedback">
      <view class="weui-cell__hd">
        <view class="function-icon">💬</view>
      </view>
      <view class="weui-cell__bd">意见反馈</view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="showHelp">
      <view class="weui-cell__hd">
        <view class="function-icon">❓</view>
      </view>
      <view class="weui-cell__bd">帮助中心</view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>