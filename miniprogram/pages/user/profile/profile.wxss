/* 我的页面样式 - WeUI风格 */

.page {
  min-height: 100vh;
  background-color: var(--weui-BG-1);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 用户信息卡片 - 优化版 */
.user-card {
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 50%, #4A90E2 100%);
  padding: 50rpx 30rpx 40rpx;
  margin: 20rpx 30rpx 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
  position: relative;
  overflow: hidden;
}

/* 添加装饰性背景元素 */
.user-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.user-card::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  z-index: 0;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  margin-right: 30rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.4);
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.default-user-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: rgba(255, 255, 255, 0.9);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 42rpx;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.user-id {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.15);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
}

/* 当前孩子信息卡片优化 */
.weui-cells_after-title {
  margin: 0 30rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  background: #FFFFFF;
}

.weui-cell {
  position: relative;
  background: linear-gradient(135deg, #F7F8FA 0%, #FFFFFF 100%);
  border-radius: 16rpx;
  margin: 0;
  transition: all 0.3s ease;
}

.weui-cell:active {
  background: linear-gradient(135deg, #F0F1F3 0%, #F7F8FA 100%);
  transform: scale(0.98);
}

/* 孩子头像优化 */
.child-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  overflow: hidden;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  border: 3rpx solid rgba(255, 122, 69, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.2);
  position: relative;
}

.child-avatar image {
  width: 100%;
  height: 100%;
}

.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 孩子信息优化 */
.child-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.child-name {
  font-size: 36rpx;
  color: #2C3E50;
  font-weight: 600;
  margin-bottom: 6rpx;
  letter-spacing: 0.5rpx;
}

.child-age {
  font-size: 28rpx;
  color: #7F8C8D;
  background: rgba(255, 122, 69, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  font-weight: 500;
  width: fit-content;
}

/* 统计信息优化 */
.child-stats {
  text-align: right;
  padding: 8rpx 0;
}

.stat-text {
  font-size: 26rpx;
  color: #95A5A6;
  background: rgba(74, 144, 226, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 功能图标优化 */
.function-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #F7F8FA 0%, #E8EAED 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 122, 69, 0.1);
  transition: all 0.3s ease;
}

.no-child-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  margin-right: 24rpx;
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  border-radius: 16rpx;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

/* 页面标题优化 */
.weui-cells__title {
  color: #2C3E50 !important;
  font-weight: 600 !important;
  font-size: 32rpx !important;
  margin: 40rpx 30rpx 20rpx !important;
  position: relative;
}

.weui-cells__title::before {
  content: '';
  position: absolute;
  left: -8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  border-radius: 3rpx;
}

/* 设置区域卡片优化 */
.weui-cells {
  margin: 0 30rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.weui-cell {
  background: #FFFFFF;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.weui-cell:active {
  background: #F7F8FA;
  transform: scale(0.98);
}

.weui-cell__bd {
  color: #2C3E50 !important;
  font-weight: 500 !important;
  font-size: 32rpx !important;
}

/* WeUI 单元格描述文字 */
.weui-cell__desc {
  font-size: 28rpx;
  color: var(--weui-FG-1);
  line-height: 1.4;
  margin-top: 4rpx;
}

/* 按钮区域 */
.weui-btn-area {
  margin: 60rpx 30rpx 30rpx;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
