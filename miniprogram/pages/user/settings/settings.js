// 设置页面
const { userActions } = require("../../../utils/index.js");

Page({
  data: {
    // 主题色
    primaryColor: "#FF7A45",

    // 用户信息
    userInfo: {
      nickname: "微信用户", // 使用snake_case，与后端API一致
      avatar: "👤", // 使用snake_case，与后端API一致
      id: "000001",
    },

    // 设置项
    settings: {
      // 通知设置
      checkinReminder: true,
      achievementNotification: true,
      leaderboardUpdate: false,

      // 隐私设置
      publicLeaderboard: true,
      friendsView: true,

      // 功能设置
      reminderTime: "每天 18:00",
      weeklyGoal: 5,
      autoShare: false,
    },

    // 时间选择器
    showTimePicker: false,
    selectedTime: "18:00",

    // 目标选择器
    showGoalPicker: false,
    selectedGoal: 5,
    goalOptions: [3, 4, 5, 6, 7],
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
    this.loadSettings();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    // 从状态管理器获取用户信息
    const userInfo = userActions.getUserInfo();
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
      });
    }
  },

  /**
   * 加载设置
   */
  loadSettings() {
    // 从本地存储加载设置
    try {
      const settings = wx.getStorageSync("userSettings");
      if (settings) {
        this.setData({
          settings: { ...this.data.settings, ...settings },
        });
      }
    } catch (error) {
      console.error("加载设置失败:", error);
    }
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      wx.setStorageSync("userSettings", this.data.settings);
    } catch (error) {
      console.error("保存设置失败:", error);
    }
  },

  /**
   * 打卡提醒开关
   */
  onCheckinReminderChange(e) {
    this.setData({
      "settings.checkinReminder": e.detail.value,
    });
    this.saveSettings();

    if (e.detail.value) {
      this.requestNotificationPermission();
    }
  },

  /**
   * 成就通知开关
   */
  onAchievementNotificationChange(e) {
    this.setData({
      "settings.achievementNotification": e.detail.value,
    });
    this.saveSettings();
  },

  /**
   * 排行榜更新开关
   */
  onLeaderboardUpdateChange(e) {
    this.setData({
      "settings.leaderboardUpdate": e.detail.value,
    });
    this.saveSettings();
  },

  /**
   * 公开排行榜开关
   */
  onPublicLeaderboardChange(e) {
    this.setData({
      "settings.publicLeaderboard": e.detail.value,
    });
    this.saveSettings();
  },

  /**
   * 好友查看开关
   */
  onFriendsViewChange(e) {
    this.setData({
      "settings.friendsView": e.detail.value,
    });
    this.saveSettings();
  },

  /**
   * 自动分享开关
   */
  onAutoShareChange(e) {
    this.setData({
      "settings.autoShare": e.detail.value,
    });
    this.saveSettings();
  },

  /**
   * 请求通知权限
   */
  requestNotificationPermission() {
    wx.requestSubscribeMessage({
      tmplIds: ["template_id_1", "template_id_2"], // 替换为实际的模板ID
      success: (res) => {
        console.log("订阅消息成功:", res);
      },
      fail: (err) => {
        console.error("订阅消息失败:", err);
      },
    });
  },

  /**
   * 设置提醒时间
   */
  setReminderTime() {
    this.setData({
      showTimePicker: true,
      selectedTime: this.data.settings.reminderTime.split(" ")[1] || "18:00",
    });
  },

  /**
   * 隐藏时间选择器
   */
  hideTimePicker() {
    this.setData({ showTimePicker: false });
  },

  /**
   * 时间变化
   */
  onTimeChange(e) {
    this.setData({
      selectedTime: e.detail.value,
    });
  },

  /**
   * 确认时间
   */
  confirmTime() {
    const reminderTime = `每天 ${this.data.selectedTime}`;
    this.setData({
      "settings.reminderTime": reminderTime,
      showTimePicker: false,
    });
    this.saveSettings();

    wx.showToast({
      title: "设置成功",
      icon: "success",
    });
  },

  /**
   * 设置周目标
   */
  setWeeklyGoal() {
    this.setData({
      showGoalPicker: true,
      selectedGoal: this.data.settings.weeklyGoal,
    });
  },

  /**
   * 隐藏目标选择器
   */
  hideGoalPicker() {
    this.setData({ showGoalPicker: false });
  },

  /**
   * 选择目标
   */
  selectGoal(e) {
    const goal = e.currentTarget.dataset.goal;
    this.setData({ selectedGoal: goal });
  },

  /**
   * 确认目标
   */
  confirmGoal() {
    this.setData({
      "settings.weeklyGoal": this.data.selectedGoal,
      showGoalPicker: false,
    });
    this.saveSettings();

    wx.showToast({
      title: "设置成功",
      icon: "success",
    });
  },

  /**
   * 导出数据
   */
  exportData() {
    wx.showLoading({
      title: "导出中...",
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: "导出完成",
        content: "数据已导出到微信文件，可在微信中查看和分享",
        showCancel: false,
      });
    }, 2000);
  },

  /**
   * 清除缓存
   */
  clearCache() {
    wx.showModal({
      title: "确认清除",
      content: "清除缓存后，部分数据需要重新加载，确定要清除吗？",
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: "清除中...",
          });

          // 清除缓存
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: "清除成功",
              icon: "success",
            });
          }, 1500);
        }
      },
    });
  },

  /**
   * 同步数据
   */
  syncData() {
    wx.showLoading({
      title: "同步中...",
    });

    // 模拟同步过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: "同步成功",
        icon: "success",
      });
    }, 2000);
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    wx.showLoading({
      title: "检查中...",
    });

    // 检查小程序更新
    const updateManager = wx.getUpdateManager();

    updateManager.onCheckForUpdate((res) => {
      wx.hideLoading();
      if (res.hasUpdate) {
        wx.showModal({
          title: "发现新版本",
          content: "发现新版本，是否立即更新？",
          success: (modalRes) => {
            if (modalRes.confirm) {
              updateManager.onUpdateReady(() => {
                updateManager.applyUpdate();
              });
            }
          },
        });
      } else {
        wx.showToast({
          title: "已是最新版本",
          icon: "success",
        });
      }
    });

    updateManager.onUpdateFailed(() => {
      wx.hideLoading();
      wx.showToast({
        title: "检查更新失败",
        icon: "none",
      });
    });
  },

  /**
   * 显示帮助
   */
  showHelp() {
    wx.navigateTo({
      url: "/pages/help/help",
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacy() {
    wx.navigateTo({
      url: "/pages/privacy/privacy",
    });
  },

  /**
   * 联系我们
   */
  contactUs() {
    wx.showActionSheet({
      itemList: ["在线客服", "意见反馈", "电话客服"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 打开客服会话
            wx.openCustomerServiceChat({
              extInfo: { url: "https://work.weixin.qq.com/..." },
              corpId: "corpId",
              success: () => {
                console.log("打开客服会话成功");
              },
            });
            break;
          case 1:
            // 跳转到反馈页面
            wx.navigateTo({
              url: "/pages/feedback/feedback",
            });
            break;
          case 2:
            // 拨打客服电话
            wx.makePhoneCall({
              phoneNumber: "************",
            });
            break;
        }
      },
    });
  },

  /**
   * 退出登录
   */
  logout() {
    wx.showModal({
      title: "确认退出",
      content: "退出登录后需要重新登录，确定要退出吗？",
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          wx.clearStorageSync();

          // 跳转到登录页
          wx.reLaunch({
            url: "/pages/login/login",
          });
        }
      },
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },
});
