/* 设置页面样式 */

.page-container {
  min-height: 100vh;
  background: var(--background-color);
  padding-bottom: var(--spacing-xxl);
}

/* 设置页面样式 */

/* 用户信息区域 */
.user-info-section {
  display: flex;
  align-items: center;
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), #FF9A6B);
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: var(--spacing-lg);
}

.user-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.default-user-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  font-size: 60rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.user-name {
  font-size: var(--font-lg);
  font-weight: 600;
}

.user-id {
  font-size: var(--font-sm);
  opacity: 0.9;
}

.user-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg);
  /* backdrop-filter: blur(10rpx); */
}

.badge-text {
  font-size: var(--font-sm);
  font-weight: 600;
}

/* 设置区域 */
.settings-section {
  margin: 0 var(--spacing-lg) var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.settings-list {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background: var(--background-color);
}

.setting-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.setting-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.setting-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.setting-arrow {
  font-size: var(--font-lg);
  color: var(--text-placeholder);
  margin-left: var(--spacing-md);
}

/* 退出登录区域 */
.logout-section {
  margin: var(--spacing-xl) var(--spacing-lg) 0;
}

.logout-btn {
  height: 88rpx;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--error-color);
  border-color: var(--error-color);
}

.logout-btn:active {
  background: rgba(245, 34, 45, 0.1);
}

/* 弹窗样式 */
.time-picker-modal, .goal-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.time-picker-modal.show, .goal-picker-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  margin: var(--spacing-lg);
  max-width: 600rpx;
  width: 100%;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.time-picker-modal.show .modal-content, .goal-picker-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  border-radius: 50%;
  background: var(--background-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

/* 时间选择器 */
.time-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xl);
  background: var(--background-color);
  border-radius: var(--radius-lg);
  text-align: center;
}

.time-text {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.time-tip {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 目标选择器 */
.goal-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.goal-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--radius-lg);
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
}

.goal-option.active {
  background: rgba(255, 122, 69, 0.1);
  border-color: var(--primary-color);
}

.goal-number {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.goal-unit {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.modal-footer {
  display: flex;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.modal-footer .btn {
  flex: 1;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + var(--spacing-lg));
}
