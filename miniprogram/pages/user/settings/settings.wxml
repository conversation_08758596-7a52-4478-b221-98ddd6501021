<!-- 设置页面 -->
<view class="page-container">
    <!-- 用户信息区域 -->
    <view class="user-info-section">
        <view class="user-avatar">
            <view class="default-user-avatar" wx:if="{{!userInfo.avatarUrl}}">👤</view>
            <image wx:else src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        </view>
        <view class="user-details">
            <text class="user-name">{{userInfo.nickName || '微信用户'}}</text>
            <text class="user-id">ID: {{userInfo.id || '000001'}}</text>
        </view>
        <view class="user-badge">
            <text class="badge-text">VIP</text>
        </view>
    </view>
    <!-- 通知设置 -->
    <view class="settings-section">
        <view class="section-title">
            <text class="emoji">🔔</text>
            <text>通知设置</text>
        </view>
        <view class="settings-list">
            <view class="setting-item">
                <view class="setting-info">
                    <text class="setting-title">打卡提醒</text>
                    <text class="setting-desc">每日定时提醒孩子打卡</text>
                </view>
                <switch checked="{{settings.checkinReminder}}" bindchange="onCheckinReminderChange" color="{{primaryColor}}" />
            </view>
            <view class="setting-item">
                <view class="setting-info">
                    <text class="setting-title">成就通知</text>
                    <text class="setting-desc">获得新徽章时推送通知</text>
                </view>
                <switch checked="{{settings.achievementNotification}}" bindchange="onAchievementNotificationChange" color="{{primaryColor}}" />
            </view>
            <view class="setting-item">
                <view class="setting-info">
                    <text class="setting-title">排行榜更新</text>
                    <text class="setting-desc">排行榜变化时通知</text>
                </view>
                <switch checked="{{settings.leaderboardUpdate}}" bindchange="onLeaderboardUpdateChange" color="{{primaryColor}}" />
            </view>
        </view>
    </view>
    <!-- 隐私设置 -->
    <view class="settings-section">
        <view class="section-title">
            <text class="emoji">🔒</text>
            <text>隐私设置</text>
        </view>
        <view class="settings-list">
            <view class="setting-item">
                <view class="setting-info">
                    <text class="setting-title">公开排行榜</text>
                    <text class="setting-desc">在排行榜中显示孩子信息</text>
                </view>
                <switch checked="{{settings.publicLeaderboard}}" bindchange="onPublicLeaderboardChange" color="{{primaryColor}}" />
            </view>
            <view class="setting-item">
                <view class="setting-info">
                    <text class="setting-title">允许好友查看</text>
                    <text class="setting-desc">好友可以查看孩子的运动记录</text>
                </view>
                <switch checked="{{settings.friendsView}}" bindchange="onFriendsViewChange" color="{{primaryColor}}" />
            </view>
        </view>
    </view>
    <!-- 功能设置 -->
    <view class="settings-section">
        <view class="section-title">
            <text class="emoji">⚙️</text>
            <text>功能设置</text>
        </view>
        <view class="settings-list">
            <view class="setting-item" bindtap="setReminderTime">
                <view class="setting-info">
                    <text class="setting-title">提醒时间</text>
                    <text class="setting-desc">{{settings.reminderTime || '每天 18:00'}}</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="setWeeklyGoal">
                <view class="setting-info">
                    <text class="setting-title">周目标</text>
                    <text class="setting-desc">每周打卡 {{settings.weeklyGoal || 5}} 次</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="setAutoShare">
                <view class="setting-info">
                    <text class="setting-title">自动分享</text>
                    <text class="setting-desc">{{settings.autoShare ? '已开启' : '已关闭'}}</text>
                </view>
                <switch checked="{{settings.autoShare}}" bindchange="onAutoShareChange" color="{{primaryColor}}" />
            </view>
        </view>
    </view>
    <!-- 数据管理 -->
    <view class="settings-section">
        <view class="section-title">
            <text class="emoji">📊</text>
            <text>数据管理</text>
        </view>
        <view class="settings-list">
            <view class="setting-item" bindtap="exportData">
                <view class="setting-info">
                    <text class="setting-title">导出数据</text>
                    <text class="setting-desc">导出所有运动记录</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="clearCache">
                <view class="setting-info">
                    <text class="setting-title">清除缓存</text>
                    <text class="setting-desc">清理本地缓存数据</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="syncData">
                <view class="setting-info">
                    <text class="setting-title">同步数据</text>
                    <text class="setting-desc">与云端数据同步</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
        </view>
    </view>
    <!-- 关于应用 -->
    <view class="settings-section">
        <view class="section-title">
            <text class="emoji">ℹ️</text>
            <text>关于应用</text>
        </view>
        <view class="settings-list">
            <view class="setting-item" bindtap="checkUpdate">
                <view class="setting-info">
                    <text class="setting-title">检查更新</text>
                    <text class="setting-desc">当前版本 v1.0.0</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="showHelp">
                <view class="setting-info">
                    <text class="setting-title">使用帮助</text>
                    <text class="setting-desc">查看使用说明</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="showPrivacy">
                <view class="setting-info">
                    <text class="setting-title">隐私政策</text>
                    <text class="setting-desc">了解隐私保护</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
            <view class="setting-item" bindtap="contactUs">
                <view class="setting-info">
                    <text class="setting-title">联系我们</text>
                    <text class="setting-desc">意见反馈与客服</text>
                </view>
                <view class="setting-arrow">></view>
            </view>
        </view>
    </view>
    <!-- 退出登录 -->
    <view class="logout-section">
        <button class="btn btn-outline btn-full logout-btn" bindtap="logout">退出登录</button>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 提醒时间选择弹窗 -->
<view class="time-picker-modal {{showTimePicker ? 'show' : ''}}" bindtap="hideTimePicker">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">设置提醒时间</text>
            <view class="modal-close" bindtap="hideTimePicker">×</view>
        </view>
        <view class="modal-body">
            <picker mode="time" value="{{selectedTime}}" bindchange="onTimeChange">
                <view class="time-display">
                    <text class="time-text">{{selectedTime || '18:00'}}</text>
                    <text class="time-tip">点击选择时间</text>
                </view>
            </picker>
        </view>
        <view class="modal-footer">
            <button class="btn btn-outline" bindtap="hideTimePicker">取消</button>
            <button class="btn btn-primary" bindtap="confirmTime">确定</button>
        </view>
    </view>
</view>
<!-- 周目标设置弹窗 -->
<view class="goal-picker-modal {{showGoalPicker ? 'show' : ''}}" bindtap="hideGoalPicker">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">设置周目标</text>
            <view class="modal-close" bindtap="hideGoalPicker">×</view>
        </view>
        <view class="modal-body">
            <view class="goal-options">
                <view class="goal-option {{item === selectedGoal ? 'active' : ''}}" wx:for="{{goalOptions}}" wx:key="*this" bindtap="selectGoal" data-goal="{{item}}">
                    <text class="goal-number">{{item}}</text>
                    <text class="goal-unit">次/周</text>
                </view>
            </view>
        </view>
        <view class="modal-footer">
            <button class="btn btn-outline" bindtap="hideGoalPicker">取消</button>
            <button class="btn btn-primary" bindtap="confirmGoal">确定</button>
        </view>
    </view>
</view>