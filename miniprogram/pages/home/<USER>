/* 首页样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
}





/* 首页特定样式 */

/* 训练营卡片区域 */
.camps-container {
  padding: 0 var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.camp-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(255, 122, 69, 0.15);
  transition: transform 0.3s ease;
}

.camp-card:active {
  transform: scale(0.98);
}

.camp-illustration {
  height: 320rpx;
  background: linear-gradient(135deg, #FFE8E0 0%, #FFF5F2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.camp-emoji {
  font-size: 200rpx;
  line-height: 1;
}

.camp-content {
  padding: var(--spacing-lg);
}

.camp-title {
  /* 使用标准主标题样式类 text-title-main */
  margin-bottom: 8rpx;
}

.camp-subtitle {
  /* 使用标准正文样式类 text-body */
  margin-bottom: 16rpx;
}

.camp-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.tag {
  /* 使用标准标签样式类 text-label */
  background-color: #FFF2E8;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.camp-action {
  display: flex;
  justify-content: flex-end;
}

/* 图片背景风格的训练营 */
.image-camps-container {
  padding: var(--spacing-lg) var(--spacing-md) 0;
}

.image-camp-card {
  margin-bottom: var(--spacing-lg);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.image-camp-card:active {
  transform: scale(0.98);
}

.image-camp-background {
  height: 420rpx;
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: var(--radius-lg);
}

.image-camp-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #FFFFFF;
  border-radius: var(--radius-lg);
}

.image-camp-tags {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.image-tag {
  background-color: var(--primary-color);
  color: #FFFFFF;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 122, 69, 0.3);
}

.image-camp-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.image-camp-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.image-camp-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-xs);
  line-height: 1.4;
}

.image-camp-participants {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-lg);
}

.image-camp-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  backdrop-filter: blur(10rpx);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #FFFFFF;
}

.image-camp-action {
  text-align: center;
}

.action-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.action-text::before {
  content: "✏️";
  font-size: 24rpx;
}

/* 区块标题样式 */
.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  padding: 0 var(--spacing-md);
  margin: var(--spacing-xxl) 0 var(--spacing-lg) 0;
}

/* 即将推出区域 */
.coming-soon-container {
  padding: 0 var(--spacing-md);
  margin-top: var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.coming-soon-card {
  flex: 1;
  min-width: 200rpx;
  margin-bottom: var(--spacing-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.coming-soon-background {
  height: 180rpx;
  position: relative;
  background-size: cover;
  background-position: center;
  border-radius: var(--radius-lg);
}

.coming-soon-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.6) 100%);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  border-radius: var(--radius-lg);
}

.coming-soon-content {
  text-align: center;
}

.coming-soon-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: var(--spacing-xs);
}

.coming-soon-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-xs);
}

.coming-soon-status {
  font-size: 24rpx;
  color: #FFFFFF;
  background-color: rgba(255, 122, 69, 0.8);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-top: var(--spacing-xs);
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}


