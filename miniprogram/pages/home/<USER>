// 首页 - 训练营选择中心 - 使用状态管理和登录守卫
const {
  childrenActions,
  createStatePage,
  withLoginGuardPreset,
  startLoading,
  endLoading,
  isLoading,
} = require("../../utils/index.js");

const contentAPI = require("../../apis/content.js");
const app = getApp();

// 使用状态管理和登录守卫创建页面
const homePage = withLoginGuardPreset("basic")(
  createStatePage({
    data: {
      navbarHeight: 44, // 默认导航栏高度
      // 状态管理会自动绑定这些数据
      currentChild: null,
      children: [],
      activeCamps: [],
      // 图片背景风格的训练营（从API加载）
      imageCamps: [],
      comingSoonCamps: [
        {
          id: 101, // 使用更大的ID避免与真实数据冲突
          title: "冲刺200/分钟",
          subtitle: "即将推出",
          backgroundImage:
            "linear-gradient(135deg, rgba(232, 229, 229, 0.9), rgba(227, 222, 222, 0.8))",
        },
        {
          id: 102,
          title: "进阶150/分钟",
          subtitle: "即将推出",
          backgroundImage:
            "linear-gradient(135deg, rgba(232, 229, 229, 0.9), rgba(227, 222, 222, 0.8))",
        },
        {
          id: 103,
          title: "花样跳绳",
          subtitle: "即将推出",
          backgroundImage:
            "linear-gradient(135deg, rgba(232, 229, 229, 0.9), rgba(227, 222, 222, 0.8))",
        },
      ],
    },

    onLoad() {
      console.log("首页加载");

      // 绑定孩子状态到页面数据
      this.bindChildrenState();

      this.initPage();
    },

    onShow() {
      console.log("首页显示");
      this.refreshData();
    },

    // 状态变化回调
    onStateChange(path, newValue, oldValue) {
      console.log(`首页状态变化: ${path}`, newValue);

      // 当前孩子变化时可以做一些处理
      if (path === "children.currentChild") {
        console.log("当前孩子变化:", newValue?.name);
      }
    },

    // 登录状态变化回调
    onLoginChange(isLoggedIn, userInfo) {
      console.log("首页 - 登录状态变化:", isLoggedIn);
      if (isLoggedIn) {
        // 登录成功后可以加载个性化内容
        this.loadPersonalizedContent();
      }
    },

    // 初始化页面
    initPage() {
      // 组件会自动处理导航栏高度和孩子数据
    },

    // 刷新数据
    async refreshData() {
      const loadingKey = "home-refresh";

      // 如果已经在加载中，跳过
      if (isLoading(loadingKey)) {
        console.log("首页数据正在加载中，跳过重复请求");
        return;
      }

      try {
        // 开始加载
        startLoading(loadingKey, {
          title: "加载首页数据...",
          timeout: 15000,
          onTimeout: () => {
            console.log("首页数据加载超时");
            this.handleLoadTimeout();
          },
          onRetry: () => {
            console.log("重试加载首页数据");
            this.refreshData();
          },
        });

        console.log("🔄 开始刷新首页数据");

        // 模拟网络请求（这里应该是真实的API调用）
        await this.loadHomeData();

        // 加载成功
        endLoading(loadingKey, true);
        console.log("✅ 首页数据加载成功");
      } catch (error) {
        // 加载失败
        endLoading(loadingKey, false);
        console.error("❌ 首页数据加载失败:", error);

        // 显示错误提示
        wx.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 加载首页数据（直接API调用）
    async loadHomeData() {
      try {
        console.log("🏕️ 开始加载训练营列表");

        // 直接调用API
        const response = await contentAPI.getCampList({
          page: 1,
          limit: 20,
        });

        console.log(
          "✅ 训练营列表加载成功，数据条数:",
          response?.list?.length || 0
        );

        // 更新页面数据
        if (response && response.list && response.list.length > 0) {
          // 转换数据格式为页面所需
          const camps = response.list.map((camp) => {
            // 安全解析tags
            let tags = [];
            try {
              tags = camp.tags ? JSON.parse(camp.tags) : [];
            } catch (error) {
              console.warn("解析tags失败:", camp.tags);
              tags = [];
            }

            return {
              id: camp.id,
              title: camp.title || "未知训练营",
              subtitle: camp.subtitle || "",
              participants: `${camp.total_participants || 0}人参与`,
              backgroundImage:
                camp.background_color ||
                "linear-gradient(135deg, rgba(255, 122, 69, 0.9), rgba(255, 140, 105, 0.8))", // 默认橙色
              tags: tags,
              duration: `${camp.duration_days || 0}天`,
              frequency: `${camp.daily_minutes || 0}分钟`,
              courses: camp.price > 0 ? `¥${camp.price}` : "完全免费",
              rating: camp.average_rating || 0,
              status: camp.status,
            };
          });

          this.setData({
            activeCamps: camps,
            imageCamps: camps,
            lastUpdateTime: Date.now(),
          });

          console.log("📱 页面数据已更新，显示", camps.length, "个训练营");
        } else {
          console.warn("⚠️ API返回数据为空或格式不正确");
        }

        return response;
      } catch (error) {
        console.error("❌ 加载训练营列表失败:", error);

        // 显示错误提示
        wx.showToast({
          title: "网络连接失败",
          icon: "none",
          duration: 2000,
        });

        throw error;
      }
    },

    // 处理加载超时
    handleLoadTimeout() {
      console.log("处理首页加载超时");
      // 可以在这里加载缓存数据或显示离线内容
      this.loadCachedData();
    },

    // 加载缓存数据
    loadCachedData() {
      console.log("📦 加载首页缓存数据");
      wx.showToast({
        title: "已切换到离线模式",
        icon: "none",
        duration: 2000,
      });
      // 这里可以加载本地缓存的数据
    },

    // 加载个性化内容
    loadPersonalizedContent() {
      const currentChild = childrenActions.getCurrentChild();
      if (currentChild) {
        console.log("为孩子加载个性化内容:", currentChild.name);
        // 这里可以根据孩子的年龄、水平等加载推荐的训练营
      }
    },

    // 导航栏高度变化事件
    onNavbarHeightChange(e) {
      this.setData({
        navbarHeight: e.detail.navbarHeight,
      });
    },

    // 孩子切换事件
    onChildChange(e) {
      console.log("孩子切换:", e.detail.child);
      // 可以在这里处理孩子切换后的逻辑
    },

    // 跳转到训练营详情
    goToCampDetail(e) {
      const camp = e.currentTarget.dataset.camp;

      // 检查是否有选中的孩子
      if (!this.data.currentChild || !this.data.currentChild.id) {
        wx.showToast({
          title: "请先选择孩子",
          icon: "none",
        });
        return;
      }

      wx.navigateTo({
        url: `/pages/camp-detail/camp-detail?id=${camp.id}`,
      });
    },

    // 下拉刷新
    onPullDownRefresh() {
      this.refreshData();
      setTimeout(() => {
        wx.stopPullDownRefresh();
      }, 1000);
    },

    // 强制刷新数据
    async forceRefresh() {
      try {
        wx.showLoading({ title: "刷新中...", mask: true });
        await this.loadHomeData();
        wx.hideLoading();
        wx.showToast({
          title: "刷新成功",
          icon: "success",
          duration: 1500,
        });
      } catch (error) {
        wx.hideLoading();
        wx.showToast({
          title: "刷新失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 分享
    onShareAppMessage() {
      return {
        title: "跳跳星球 - 让孩子爱上运动",
        path: "/pages/home/<USER>",
        image_url: "🏠", // 使用emoji替代图片
      };
    },

    onShareTimeline() {
      return {
        title: "跳跳星球 - 让孩子爱上运动",
        image_url: "🏠", // 使用emoji替代图片
      };
    },
  })
);

// 创建页面实例
Page(homePage);
