<!-- 首页 - 训练营选择中心 -->
<view class="page-container">
  <!-- 自定义导航栏 -->
  <custom-navbar title="跳跳星球" show-user-selector="{{true}}" show-back-button="{{false}}" bind:navbarHeightChange="onNavbarHeightChange" bind:childChange="onChildChange"></custom-navbar>
  <!-- 主要训练营列表 -->
  <!-- <view class="camps-container" style="margin-top: {{navbarHeight}}px;">
    <view class="camp-card" wx:for="{{activeCamps}}" wx:key="id" bindtap="goToCampDetail" data-camp="{{item}}">
      <view class="camp-illustration">
        <view class="camp-emoji">{{item.illustration}}</view>
      </view>
      <view class="camp-content">
        <view class="camp-title text-title-main">{{item.title}}</view>
        <view class="camp-subtitle text-body">{{item.subtitle}}</view>
        <view class="camp-tags">
          <text class="tag text-label" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>
        <view class="camp-action">
          <button class="btn btn-primary btn-small">立即查看 ▶</button>
        </view>
      </view>
    </view>
  </view> -->
  <!-- 图片背景风格的训练营 -->
  <view class="image-camps-container" style="margin-top: {{navbarHeight + 10}}px;">
    <view class="image-camp-card" wx:for="{{imageCamps}}" wx:key="id" bindtap="goToCampDetail" data-camp="{{item}}">
      <view class="image-camp-background" style="background: {{item.backgroundImage}};">
        <view class="image-camp-overlay">
          <view class="image-camp-tags">
            <text class="image-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
          <view class="image-camp-content">
            <view class="image-camp-title">{{item.title}}</view>
            <view class="image-camp-subtitle">{{item.subtitle}}</view>
            <view class="image-camp-participants">{{item.participants}}</view>
          </view>
          <view class="image-camp-stats">
            <view class="stat-item">
              <view class="stat-label">计划时长</view>
              <view class="stat-value">{{item.duration}}</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">每天训练</view>
              <view class="stat-value">{{item.frequency}}</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">配套课程</view>
              <view class="stat-value">{{item.courses}}</view>
            </view>
          </view>
          <!-- <view class="image-camp-action">
            <text class="action-text">评估身体现状，为你定制训练方案</text>
          </view> -->
        </view>
      </view>
    </view>
  </view>
  <!-- 即将推出的训练营 -->
  <view class="section-title">即将推出</view>
  <view class="coming-soon-container">
    <view class="coming-soon-card" wx:for="{{comingSoonCamps}}" wx:key="id">
      <view class="coming-soon-background" style="background: {{item.backgroundImage}};">
        <view class="coming-soon-overlay">
          <view class="coming-soon-content">
            <view class="coming-soon-title">{{item.title}}</view>
            <view class="coming-soon-subtitle">{{item.subtitle}}</view>
            <!-- <view class="coming-soon-status">敬请期待</view> -->
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>