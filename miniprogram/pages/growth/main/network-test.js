// 网络错误测试页面
const { http } = require('../../../utils/index.js');

Page({
  data: {
    testResults: []
  },

  onLoad() {
    console.log('网络测试页面加载');
  },

  // 测试网络请求
  async testNetworkRequest() {
    console.log('开始测试网络请求...');
    
    try {
      const result = await http.get('/api/test');
      console.log('网络请求成功:', result);
      
      this.addTestResult('网络请求成功', 'success');
    } catch (error) {
      console.error('网络请求失败:', error);
      
      this.addTestResult(`网络请求失败: ${error.message}`, 'error');
    }
  },

  // 添加测试结果
  addTestResult(message, type) {
    const results = this.data.testResults;
    results.push({
      id: Date.now(),
      message,
      type,
      time: new Date().toLocaleTimeString()
    });
    
    this.setData({
      testResults: results
    });
  },

  // 清空测试结果
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  // 手动触发错误
  triggerError() {
    try {
      throw new Error('这是一个测试错误');
    } catch (error) {
      console.error('手动触发的错误:', error);
      
      wx.showToast({
        title: '测试错误已触发',
        icon: 'none'
      });
    }
  }
});
