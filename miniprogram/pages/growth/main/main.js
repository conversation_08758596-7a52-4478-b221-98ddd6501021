// 成长主页重构版 - 基于V2-A简洁任务导向型
const {
  startLoading,
  endLoading,
  isLoading,
  userActions,
  childrenActions,
} = require("../../../utils/index.js");

const contentAPI = require("../../../apis/content.js");
const userAPI = require("../../../apis/user.js");

Page({
  data: {
    // 导航栏高度
    navbarHeight: 44,

    // 当前Tab
    currentTab: 0,

    // 是否有活跃的训练营
    hasActiveCamps: true,

    // 用户统计信息（从API获取）
    userStats: {
      totalPoints: 0,
      currentLevel: 0,
      streakDays: 0,
      completedContracts: 0,
    },

    // 训练营列表（从API获取）
    campList: [],

    // 契约列表（从API获取）
    contractsList: [],
    activeContractsCount: 0,

    // 勋章数据（从API获取）
    unlockedMedals: 0,
    totalMedals: 0,
    medalsList: [],

    // 成长轨迹记录（从API获取）
    growthTrackList: [],

    // 数据加载状态
    isLoadingGrowthData: false,
    isLoadingContracts: false,
    isLoadingMedals: false,
  },

  onLoad(options) {
    console.log("成长主页重构版加载");
    // 检查登录状态和孩子状态
    this.checkLoginAndChildStatus();
  },

  /**
   * 导航栏高度变化
   */
  onNavbarHeightChange(e) {
    const navbarHeight = e.detail.navbarHeight;
    this.setData({ navbarHeight });
    console.log("导航栏高度:", navbarHeight);
  },

  /**
   * 孩子切换
   */
  onChildChange(e) {
    const childInfo = e.detail;
    console.log("孩子切换:", childInfo);
    // 重新加载页面数据
    this.loadPageData();
  },

  /**
   * 检查登录状态和孩子状态
   */
  checkLoginAndChildStatus() {
    // 使用状态管理器检查登录状态
    if (!userActions.isLoggedIn()) {
      console.log("用户未登录，跳转到登录页");
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    // 使用状态管理器检查孩子状态
    const currentChild = childrenActions.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      console.log("未选择孩子，跳转到孩子管理页");
      wx.redirectTo({
        url: "/pages/auth/child-manage/child-manage",
      });
      return;
    }

    console.log("登录状态和孩子状态正常");
    this.loadPageData();
  },

  /**
   * 加载页面数据
   */
  async loadPageData() {
    const currentChild = childrenActions.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      console.log("没有选择的孩子，无法加载数据");
      return;
    }

    // 并行加载训练营数据和成长数据
    await Promise.all([this.loadCampData(), this.loadGrowthData()]);
  },

  /**
   * 加载训练营数据
   */
  async loadCampData() {
    const loadingKey = "load-camp-data";

    try {
      if (!isLoading(loadingKey)) {
        startLoading(loadingKey, {
          title: "加载训练营数据...",
          timeout: 10000,
        });
      }

      console.log("🌐 开始加载用户训练营数据");

      // 调用API获取用户参与的训练营列表
      const response = await contentAPI.getUserCamps();

      console.log("✅ 用户训练营数据加载成功:", response);

      let campList = [];

      if (response && response && Array.isArray(response)) {
        // 转换API数据为页面需要的格式
        campList = response.map((camp) => ({
          id: camp.camp_id,
          title: camp.title,
          subtitle: camp.subtitle || "专业指导训练",
          currentDay: camp.total_checkins || 0,
          totalDays: camp.duration_days || 21,
          progressPercent: Math.round(
            ((camp.total_checkins || 0) / (camp.duration_days || 21)) * 100
          ),
          streakDays: camp.consecutive_days || 0,
          totalPoints: camp.total_points || 0,
          todayStatus:
            camp.today_status === "completed" ? "completed" : "pending",
          hasContract: false, // 暂时设为false，后续可以关联契约数据
        }));
      }

      // 更新页面数据
      this.setData({
        campList: campList,
        hasActiveCamps: campList.length > 0,
      });

      endLoading(loadingKey, true);
    } catch (error) {
      console.error("❌ 加载用户训练营数据失败:", error);
      endLoading(loadingKey, false);

      // 显示错误提示
      wx.showToast({
        title: "加载训练营数据失败",
        icon: "none",
        duration: 3000,
      });

      // 设置空状态
      this.setData({
        campList: [],
        hasActiveCamps: false,
      });
    }
  },

  /**
   * 加载成长数据（个人荣誉、勋章、成长轨迹）
   */
  async loadGrowthData() {
    const loadingKey = "load-growth-data";

    try {
      this.setData({ isLoadingGrowthData: true });

      if (!isLoading(loadingKey)) {
        startLoading(loadingKey, {
          title: "加载成长数据...",
          timeout: 10000,
        });
      }

      console.log("📈 开始加载成长数据");

      // 调用API获取成长页面完整数据
      const response = await userAPI.getGrowthPageData();

      console.log("✅ 成长数据加载成功:", response);

      if (response) {
        const data = response;

        // 更新用户统计信息
        const userStats = {
          totalPoints: data.user_stats?.total_points || 0,
          currentLevel: data.user_stats?.current_level || 0,
          streakDays: data.user_stats?.streak_days || 0,
          completedContracts: data.user_stats?.completed_contracts || 0,
        };

        // 更新契约列表数据
        const contractsList = (data.contract_list || []).map((contract) => ({
          ...contract,
          start_date_formatted: this.formatDate(contract.start_date),
          target_date_formatted: this.formatDate(contract.target_date),
        }));
        const activeContractsCount = contractsList.filter(
          (contract) => contract.contract_status === 1
        ).length;

        // 更新勋章数据
        let medalsList = data.medals_list || [];

        // 对勋章进行排序：已解锁的在前，然后按照sort_order排序
        medalsList.sort((a, b) => {
          // 首先按解锁状态排序（已解锁的在前）
          if (a.unlocked !== b.unlocked) {
            return b.unlocked - a.unlocked;
          }
          // 然后按照sort_order排序（数值越大越靠前）
          return (b.sort_order || 0) - (a.sort_order || 0);
        });

        const unlockedMedals = medalsList.filter(
          (medal) => medal.unlocked
        ).length;
        const totalMedals = medalsList.length;

        // 更新成长轨迹数据
        const growthTrackList = data.growth_track || [];

        // 更新页面数据
        this.setData({
          userStats,
          contractsList,
          activeContractsCount,
          medalsList,
          unlockedMedals,
          totalMedals,
          growthTrackList,
        });
      }

      endLoading(loadingKey, true);
      this.setData({ isLoadingGrowthData: false });
    } catch (error) {
      console.error("❌ 加载成长数据失败:", error);
      endLoading(loadingKey, false);
      this.setData({ isLoadingGrowthData: false });

      // 显示错误提示
      wx.showToast({
        title: "加载成长数据失败",
        icon: "none",
        duration: 3000,
      });

      // 设置空状态
      this.setData({
        userStats: {
          totalPoints: 0,
          currentLevel: 0,
          streakDays: 0,
          completedContracts: 0,
        },
        medalsList: [],
        unlockedMedals: 0,
        totalMedals: 0,
        growthTrackList: [],
      });
    }
  },

  /**
   * 切换Tab
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab,
    });
  },

  /**
   * 跳转到训练营详情页面
   */
  goToCampDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/growth/detail/detail?camp_id=${camp.id}&from=main&status=${camp.todayStatus}`,
    });
  },

  /**
   * 跳转到荣誉契约
   */
  goToContract(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.showToast({
      title: "跳转到契约详情页",
      icon: "none",
    });
  },

  /**
   * 创建契约
   */
  createContract(e) {
    const camp = e.currentTarget.dataset.camp;

    wx.showModal({
      title: "创建荣誉契约",
      content: `为"${camp.title}"创建家庭荣誉契约，设定奖励和见证人？`,
      confirmText: "创建",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "跳转到契约创建页",
            icon: "none",
          });
        }
      },
    });
  },

  /**
   * 查看打卡详情（已打卡状态）
   */
  viewCheckinDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/growth/detail/detail?campId=${camp.id}&from=main&status=completed`,
    });
  },

  /**
   * 查看排行榜 - 直接跳转到排行榜
   */
  viewRanking(e) {
    const camp = e.currentTarget.dataset.camp;

    // 直接跳转到排行榜
    wx.navigateTo({
      url: `/pages/social/leaderboard/leaderboard?type=weekly&campId=${camp.id}`,
    });
  },

  /**
   * 查看契约
   */
  viewContract(e) {
    const camp = e.currentTarget.dataset.camp;

    if (camp.hasContract) {
      // 如果有契约，跳转到契约详情
      wx.navigateTo({
        url: `/pages/demo/contract-v4/contract-v4?campId=${
          camp.id
        }&contractId=${camp.contract?.id || 1}`,
      });
    } else {
      // 如果没有契约，提示创建
      wx.showModal({
        title: "创建荣誉契约",
        content: `为"${camp.title}"创建家庭荣誉契约，设定奖励和见证人？`,
        confirmText: "创建",
        success: (res) => {
          if (res.confirm) {
            // 跳转到契约页面，自动打开创建弹窗
            wx.navigateTo({
              url: `/pages/demo/contract-v4/contract-v4?campId=${camp.id}&action=create`,
            });
          }
        },
      });
    }
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: "/pages/index/index",
    });
  },

  /**
   * 查看勋章详情
   */
  viewMedalDetail(e) {
    const medal = e.currentTarget.dataset.medal;

    let title = `${medal.icon} ${medal.name}`;
    let content = "";

    if (medal.unlocked) {
      // 已解锁勋章
      content = `🎉 恭喜获得"${medal.name}"勋章！\n\n`;
      content += `📝 ${medal.description || medal.condition_description}\n`;
      content += `🏆 等级：${medal.level_name || "普通"}\n`;
      content += `💰 奖励：${medal.points_reward || 0}积分\n`;
      if (medal.unlocked_at) {
        content += `⏰ 获得时间：${medal.unlocked_at}`;
      }
      if (medal.is_contract_medal) {
        content += `\n\n✨ 这是一个珍贵的契约勋章！`;
      }
    } else {
      // 未解锁勋章
      content = `🎯 ${medal.description || medal.condition_description}\n\n`;
      content += `📊 当前进度：${medal.progress || 0}/${medal.target || 0}\n`;
      content += `🏆 等级：${medal.level_name || "普通"}\n`;
      content += `💰 完成奖励：${medal.points_reward || 0}积分\n`;

      const remaining = (medal.target || 0) - (medal.progress || 0);
      if (remaining > 0) {
        content += `\n💪 还需要：${remaining}次`;
      } else {
        content += `\n🎉 即将解锁！`;
      }
    }

    wx.showModal({
      title: title,
      content: content,
      showCancel: medal.unlocked,
      cancelText: "分享",
      confirmText: medal.unlocked ? "知道了" : "继续努力",
      success: (res) => {
        if (res.cancel && medal.unlocked) {
          // 分享单个勋章
          this.shareSingleMedal(medal);
        }
      },
    });
  },

  /**
   * 分享所有勋章
   */
  shareAllMedals() {
    const posterData = {
      type: "medal",
      medals: this.data.medalsList.filter((m) => m.unlocked),
      totalMedals: this.data.totalMedals,
      unlockedMedals: this.data.unlockedMedals,
    };

    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=medal&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享单个勋章
   */
  shareSingleMedal(medal) {
    const posterData = {
      type: "single_medal",
      medal: medal,
    };

    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=single_medal&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 创建新契约
   */
  createNewContract() {
    // 跳转到契约创建页面
    wx.navigateTo({
      url: `/pages/demo/contract-v4/contract-v4?action=create`,
    });
  },

  /**
   * 查看契约详情
   */
  viewContractDetail(e) {
    const contract = e.currentTarget.dataset.contract;

    // 跳转到契约详情页面
    wx.navigateTo({
      url: `/pages/demo/contract-v4/contract-v4?contractId=${contract.id}`,
    });
  },

  /**
   * 格式化日期，只显示年月日
   */
  formatDate(dateString) {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("日期格式化失败:", error);
      return dateString;
    }
  },

  /**
   * 分享单个勋章
   */
  shareMedal(e) {
    const medal = e.currentTarget.dataset.medal;

    const posterData = {
      type: "medal",
      medal: medal,
      singleMedal: true,
    };

    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=medal&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享成长海报 - 根据不同类型分享不同海报
   */
  shareGrowthPoster(e) {
    const track = e.currentTarget.dataset.track;

    // 根据记录类型跳转到对应的海报页面
    const posterTypeMap = {
      checkin: "打卡海报",
      honor: "荣誉海报",
      award: "获奖海报",
      contract: "契约海报",
    };

    const posterType = track.type;
    const posterName = posterTypeMap[posterType];

    if (!posterName) {
      wx.showToast({
        title: "暂不支持该类型分享",
        icon: "none",
      });
      return;
    }

    const url = `/pages/social/share-poster/share-poster?type=${posterType}&data=${encodeURIComponent(
      JSON.stringify(track)
    )}`;

    console.log(`分享${posterName}:`, track);
    wx.navigateTo({
      url: url,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log("下拉刷新成长页面数据");
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: "我的成长训练营进度，一起来挑战吧！",
      path: "/pages/demo/growth-v4-main/growth-v4-main",
      image_url: "/images/share_growth_v4.jpg",
    };
  },
});
