/* 成长V4 - 打卡详情页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 训练营信息区 */
.camp-info-section {
  background-color: #FFFFFF;
  margin: 32rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.camp-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.camp-basic-info {
  flex: 1;
}

.camp-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 16rpx;
}

.camp-time-info {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 24rpx;
  color: #999999;
}

.time-value {
  font-size: 24rpx;
  color: #FF7A45;
  font-weight: 500;
}

.camp-progress-circle {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: conic-gradient(#FFB366 0deg, #FFB366 240deg, #F5F5F5 240deg);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
}

.progress-inner {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #FF9A56;
}

/* 详细信息展开按钮 */
.info-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  margin: 16rpx 0;
  background-color: #F7F8FA;
  border-radius: 12rpx;
  cursor: pointer;
}

.toggle-text {
  font-size: 24rpx;
  color: #666666;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666666;
  transition: transform 0.3s ease;
  line-height: 1;
  display: inline-block;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 详细信息区域 */
.camp-details {
  margin-top: 16rpx;
}

/* 统计信息 */
.camp-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 契约信息 */
.contract-info {
  background: linear-gradient(135deg, #F8FAFE, #E8F4FD);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #E8F4FD;
}

.contract-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.contract-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #4A90E2;
}

.contract-status {
  font-size: 20rpx;
  color: #52C41A;
  background-color: #E8F5E8;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.contract-content {
  
}

.contract-reward {
  font-size: 26rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.contract-witnesses {
  display: flex;
  align-items: center;
}

.witnesses-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 12rpx;
}

.witnesses-list {
  display: flex;
  gap: 8rpx;
}

.witness-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
}

/* 通用区块标题 */
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 32rpx 32rpx 24rpx;
}

.title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

/* 相关视频区 */
.videos-section {
  margin-bottom: 32rpx;
}

.video-count {
  font-size: 24rpx;
  color: #666666;
}

/* 相关视频区域 */
.videos-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.videos-section .section-title {
  margin: 0;
  padding: 32rpx 32rpx 16rpx;
}

.video-scroll-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.scroll-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.4);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: normal;
  z-index: 15;
  opacity: 0.8;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.scroll-indicator.left {
  left: 10rpx;
}

.scroll-indicator.right {
  right: 10rpx;
}

.video-text-list {
  white-space: nowrap;
  padding: 0 16rpx 16rpx 16rpx;
  flex: 1;
}

.video-text-item {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  padding: 16rpx 4rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  vertical-align: top;
  transition: all 0.3s ease;
  position: relative;
}

.video-text-item:last-child {
  margin-right: 0;
}

.video-text-item.playing {
  background: linear-gradient(135deg, #fff7f0 0%, #ffe8d6 100%);
  border-color: #ff6b35;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.2);
}

.video-text-item:active {
  transform: scale(0.98);
}

.video-line-1 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.video-line-2 {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.playing-icon {
  font-size: 18rpx;
  margin-right: 6rpx;
  animation: pulse 2s infinite;
  flex-shrink: 0;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.video-number {
  font-size: 22rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 6rpx;
  flex-shrink: 0;
}

.video-text-item.playing .video-number {
  color: #ff6b35;
}

.video-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-text-item.playing .video-title {
  color: #ff6b35;
}

.video-separator {
  font-size: 20rpx;
  color: #999;
  margin-right: 6rpx;
  flex-shrink: 0;
}

.video-text-item.playing .video-separator {
  color: #ff8c42;
}

.video-subtitle {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-text-item.playing .video-subtitle {
  color: #ff8c42;
}

/* 日历区 */
.calendar-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.calendar-section .section-title {
  margin: 0;
  padding: 32rpx 32rpx 16rpx;
}

.calendar-content {
  padding: 0 32rpx 32rpx;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
  line-height: 1;
}

.calendar-controls:hover {
  background-color: #F7F8FA;
}

.calendar-controls:active {
  background-color: #F0F0F0;
}

.expand-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1;
}

.makeup-info {
  font-size: 22rpx;
  color: #FF7A45;
  background-color: #FFF2ED;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}



/* 周视图 */
.week-calendar {
  
}

.week-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.week-nav-btn {
  font-size: 32rpx;
  color: #FF7A45;
  padding: 8rpx 16rpx;
}

.week-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.week-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.week-day {
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.week-day.completed {
  background-color: #E8F5E8;
}

.week-day.makeup {
  background-color: #E6F7FF;
}

.week-day.today {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.week-day.pending {
  background-color: #FFF2ED;
}

.week-day.missed {
  background-color: #FFF2F0;
}

.week-day.can-makeup {
  background-color: #E8F4FD;
  border: 2rpx solid #4A90E2;
}

.week-day.selected {
  border: 3rpx solid #FF7A45 !important;
  box-shadow: 0 0 0 2rpx rgba(255, 122, 69, 0.3);
  background-color: rgba(255, 122, 69, 0.1) !important;
  transform: scale(1.05);
}

.week-day:active {
  transform: scale(0.95);
}

.day-name {
  font-size: 20rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.day-status {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.status-icon {
  font-weight: bold;
}

.status-icon.completed {
  color: #52C41A;
}

.status-icon.makeup {
  color: #1890FF;
}

.status-icon.today {
  color: #FF7A45;
  font-weight: bold;
}

.status-icon.pending {
  color: #FF7A45;
}

.status-icon.missed {
  color: #FF4D4F;
}

.status-icon.future {
  color: #D9D9D9;
}

.day-date {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 月视图 */
.month-calendar {
  
}

.month-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.month-nav-btn {
  font-size: 32rpx;
  color: #FF7A45;
  padding: 8rpx 16rpx;
}

.month-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.calendar-grid {
  
}

.weekday-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 16rpx;
}

.weekday-name {
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  padding: 8rpx;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.calendar-day.completed {
  background-color: #E8F5E8;
}

.calendar-day.makeup {
  background-color: #E6F7FF;
}

.calendar-day.today {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.calendar-day.pending {
  background-color: #FFF2ED;
}

.calendar-day.missed {
  background-color: #FFF2F0;
}

.calendar-day.can-makeup {
  background-color: #E8F4FD;
  border: 2rpx solid #4A90E2;
}

.calendar-day.selected {
  border: 3rpx solid #FF7A45 !important;
  box-shadow: 0 0 0 2rpx rgba(255, 122, 69, 0.3);
  background-color: rgba(255, 122, 69, 0.1) !important;
  transform: scale(1.05);
}

.calendar-day:active {
  transform: scale(0.95);
}

.day-number {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.day-indicator {
  font-size: 20rpx;
}

.day-indicator .status-icon {
  font-size: 20rpx;
}

/* 奖励区 */
.rewards-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.rewards-section .section-title {
  margin: 0;
  padding: 32rpx 32rpx 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.rewards-section .title-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rewards-section .rewards-count {
  font-size: 24rpx;
  color: #FF7A45;
  background-color: #FFF2ED;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.rewards-section .rewards-toggle {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rewards-section .toggle-text {
  font-size: 28rpx;
  color: #666666;
}

.rewards-section .toggle-icon {
  font-size: 24rpx;
  color: #666666;
  transition: transform 0.3s ease;
}

.rewards-section .toggle-icon.expanded {
  transform: rotate(180deg);
}

.rewards-content {
  animation: fadeIn 0.3s ease;
}

.rewards-scroll {
  white-space: nowrap;
}

.rewards-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 32rpx 0 0;
}

.reward-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  min-width: 120rpx;
  flex-shrink: 0;
}

.reward-item.claimed {
  background-color: #E8F5E8;
}

.reward-item.available {
  background-color: #FFFFFF;
}

.reward-item.locked {
  opacity: 0.5;
}

.reward-day {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.reward-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: block;
}

.reward-name {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.reward-status-badge {
  font-size: 20rpx;
  color: #666666;
}

.rewards-summary {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #666666;
}

/* 底部按钮 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E5E5E5;
  z-index: 100;
}

.checkin-btn {
  width: 100%;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.checkin-btn.pending {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
}

.checkin-btn.completed {
  background-color: #E8F5E8;
  color: #52C41A;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.rewards-modal, .makeup-modal {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 80%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  font-size: 32rpx;
  color: #666666;
}

.modal-content {
  max-height: 600rpx;
}

/* 奖励网格 */
.rewards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 32rpx;
}



/* 补卡弹窗 */
.makeup-modal .modal-content {
  padding: 32rpx;
  text-align: center;
}

.makeup-date {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.makeup-desc {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 16rpx;
}

.makeup-cost {
  font-size: 24rpx;
  color: #FF7A45;
  display: block;
  margin-bottom: 32rpx;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.modal-btn.cancel {
  background-color: #F0F0F0;
  color: #666666;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
}

/* 标准日历布局样式 */
.standard-calendar {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
}

.standard-calendar .weekday-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.standard-calendar .weekday-title {
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.standard-calendar .calendar-body {
  /* 日历主体样式 */
}

.standard-calendar .calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.standard-calendar .calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  position: relative;
  transition: all 0.2s ease;
  min-height: 80rpx;
  padding: 4rpx;
}

/* 日期数字样式 */
.standard-calendar .day-number {
  font-size: 22rpx;
  font-weight: 400;
  margin-bottom: 4rpx;
  color: #666666;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.standard-calendar .day-number.other-month {
  color: #CCCCCC;
}

/* 状态样式 */
.standard-calendar .day-status {
  font-size: 24rpx;
  line-height: 1;
}

.standard-calendar .status-icon {
  font-size: 24rpx;
  font-weight: bold;
  display: inline-block;
}

/* 打卡状态背景色 */
.standard-calendar .calendar-day.pending {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.standard-calendar .calendar-day.completed {
  background-color: #E8F5E8;
}

.standard-calendar .calendar-day.makeup {
  background-color: #E8F4FD;
  border: 2rpx solid #4A90E2;
}

.standard-calendar .calendar-day.missed {
  background-color: #FFF2F0;
}

.standard-calendar .calendar-day.blank {
  background-color: #F5F5F5;
}

.standard-calendar .calendar-day.future {
  background-color: #FAFAFA;
}

/* 今天特殊样式 */
.standard-calendar .calendar-day.today {
  border: 3rpx solid #FF7A45 !important;
  box-shadow: 0 0 0 2rpx rgba(255, 122, 69, 0.2);
}

/* 禁用状态 */
.standard-calendar .calendar-day.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 点击效果 */
.standard-calendar .calendar-day:active {
  transform: scale(0.95);
}

/* 状态图标颜色 */
.standard-calendar .status-icon.pending {
  color: #FF7A45;
}

.standard-calendar .status-icon.completed {
  color: #52C41A;
}

.standard-calendar .status-icon.makeup {
  color: #4A90E2;
}

.standard-calendar .status-icon.missed {
  color: #FF4D4F;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
