/* 打卡表单页面样式 - 优化版 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 数据源信息显示 */
.data-source-info {
  margin: 20rpx 32rpx;
  padding: 20rpx 24rpx;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 12rpx;
  text-align: center;
}

.data-source-text {
  font-size: 24rpx;
  color: #0369a1;
  font-weight: 500;
}

/* 顶部导航栏 */
.nav-header {
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
}

.nav-back {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 36rpx;
  color: #333333;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.nav-placeholder {
  width: 80rpx;
}

/* 顶部训练营信息 */
.camp-header {
  background-color: #FFFFFF;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5E5;
}

.camp-info {
  flex: 1;
  position: relative;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-day {
  font-size: 28rpx;
  color: #666666;
}

/* 打卡类型标识 */
.checkin-type-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff7a45;
  border-radius: 20rpx;
  padding: 6rpx 12rpx;
}

.badge-text {
  font-size: 22rpx;
  color: white;
  font-weight: bold;
}

.camp-progress {
  text-align: right;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 4rpx;
}

/* 打卡日期显示 */
.checkin-date {
  font-size: 24rpx;
  color: #999999;
  display: block;
}

/* 打卡表单 */
.checkin-form {
  padding: 32rpx;
}

.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.required {
  color: #FF4D4F;
  font-size: 32rpx;
}

.optional {
  color: #999999;
  font-size: 24rpx;
}

/* 练习时长选择器 */
.duration-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.duration-btn {
  background-color: #FFFFFF;
  border: 2rpx solid #E5E5E5;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  transition: all 0.3s ease;
}

.duration-btn.active {
  background-color: #FF7A45;
  border-color: #FF7A45;
  color: #FFFFFF;
}

/* 自定义时长 */
.custom-duration {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 24rpx;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  border: 2rpx solid #E5E5E5;
}

.custom-label {
  font-size: 28rpx;
  color: #333333;
}

.custom-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  border: 1rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
}

.custom-unit {
  font-size: 28rpx;
  color: #666666;
}

.custom-toggle {
  margin-top: 16rpx;
  text-align: center;
}

.toggle-text {
  font-size: 26rpx;
  color: #FF7A45;
}

/* 成绩输入 */
.score-inputs {
  display: flex;
  gap: 24rpx;
}

.score-item {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.score-label {
  font-size: 28rpx;
  color: #333333;
  white-space: nowrap;
}

.score-input {
  flex: 1;
  font-size: 28rpx;
  text-align: center;
  border: none;
  background-color: #F7F8FA;
  border-radius: 8rpx;
  padding: 16rpx;
}

.score-unit {
  font-size: 24rpx;
  color: #666666;
}

/* 训练感受输入 */
.feeling-input {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  height: 80rpx;
  border: none;
  box-sizing: border-box;
}

.char-count {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin-top: 16rpx;
  display: block;
}

/* 照片上传 */
.photo-upload {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
}

.photo-list {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.photo-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}

.add-photo-btn {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #E5E5E5;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F7F8FA;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  color: #999999;
}

.add-text {
  font-size: 24rpx;
  color: #999999;
}

.photo-tip {
  font-size: 24rpx;
  color: #999999;
  text-align: center;
}

/* 激励信息 */
.motivation-section {
  padding: 0 32rpx 32rpx;
}

.motivation-card {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  color: #FFFFFF;
}

.motivation-icon {
  font-size: 64rpx;
  margin-right: 24rpx;
}

.motivation-content {
  flex: 1;
}

.motivation-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.motivation-text {
  font-size: 28rpx;
  display: block;
  margin-bottom: 4rpx;
  opacity: 0.9;
}

/* 底部提交按钮 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E5E5E5;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.submit-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.submit-btn[disabled] {
  opacity: 0.6;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.success-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  text-align: center;
}

.success-animation {
  margin-bottom: 32rpx;
}

.success-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 获得奖励 */
.rewards-earned {
  margin-bottom: 32rpx;
}

.rewards-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  display: block;
}

.reward-items {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}

.reward-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reward-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #333333;
}

/* 契约进度更新 */
.contract-update {
  background-color: #FFF2ED;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.contract-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF7A45;
  margin-bottom: 16rpx;
  display: block;
}

.contract-progress {
  margin-bottom: 16rpx;
}

.contract-progress .progress-text {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  display: block;
}

.progress-bar {
  height: 8rpx;
  background-color: #E5E5E5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.contract-message {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.action-btn.secondary {
  background-color: #F7F8FA;
  color: #666666;
}

.action-btn.primary {
  background-color: #FF7A45;
  color: #FFFFFF;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
