/* 孩子管理页面样式 */

.page-container {
  min-height: 100vh;
  background: var(--background-color);
  padding-bottom: var(--spacing-xxl);
}

/* 顶部操作区域 */
.top-actions {
  padding: var(--spacing-lg);
  background: white;
  border-bottom: 2rpx solid var(--border-color);
}

.add-child-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  background: var(--primary-color);
  border-radius: var(--radius-lg);
  color: white;
}

.add-icon {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  margin: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
}

.emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 当前选中的孩子 */
.current-child-section {
  margin-bottom: var(--spacing-xl);
}

.current-child-card {
  margin: 0 var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), #ff9a6b);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
}

.child-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-lg);
}

.child-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  font-size: 72rpx;
  color: rgba(255, 255, 255, 0.8);
}

.current-badge {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  background: white;
  color: var(--primary-color);
  font-size: var(--font-xs);
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.child-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.child-name {
  font-size: var(--font-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.child-age,
.child-birthday {
  font-size: var(--font-md);
  opacity: 0.9;
}



/* 当前孩子快捷操作 */
.current-child-actions {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
}

.quick-edit-btn {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.quick-edit-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.edit-icon {
  font-size: var(--font-md);
  margin-right: var(--spacing-xs);
}

.edit-text {
  font-size: var(--font-sm);
  color: white;
}

.child-nickname {
  font-size: var(--font-md);
  opacity: 0.9;
  margin-top: var(--spacing-xs);
}

/* 孩子列表 */
.children-list-section {
  margin-bottom: var(--spacing-xl);
}

.children-list {
  margin: 0 var(--spacing-lg);
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.child-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  /* 确保子元素的层级正确 */
  z-index: 1;
}

.child-item:hover {
  background-color: rgba(255, 122, 69, 0.02);
}

.child-item:last-child {
  border-bottom: none;
}

.child-item.current {
  background: linear-gradient(
    135deg,
    rgba(255, 122, 69, 0.05),
    rgba(255, 122, 69, 0.1)
  );
  border-left: 8rpx solid var(--primary-color);
}

.child-left {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

/* 点击提示 */
.tap-hint {
  position: absolute;
  top: 50%;
  right: -120rpx;
  transform: translateY(-50%);
  background: rgba(255, 122, 69, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10;
  white-space: nowrap;
}

.child-item:hover .tap-hint {
  opacity: 1;
}

.hint-text {
  font-size: 24rpx;
  font-weight: 500;
}

.child-avatar-small {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  margin-right: var(--spacing-lg);
}

.child-avatar-small image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid var(--border-color);
}

.default-child-avatar-small {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  border-radius: 50%;
  border: 3rpx solid var(--border-color);
  font-size: 48rpx;
  color: var(--text-secondary);
}

.current-indicator {
  position: absolute;
  bottom: -5rpx;
  right: -5rpx;
  width: 32rpx;
  height: 32rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xs);
  font-weight: 600;
}

.child-info-small {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.child-name-small {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.child-details,
.child-progress {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-top: 4rpx;
}

.child-progress {
  color: var(--primary-color);
  font-weight: 500;
}

.child-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  z-index: 99;
  /* 确保右侧操作区域不被遮挡 */
  pointer-events: auto;
}



.child-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
}

.score-number {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
}

.score-label {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  text-align: center;
}

.empty-illustration {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: var(--spacing-xl);
}

.empty-illustration image {
  width: 100%;
  height: 100%;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* 管理功能区域 */
.management-section {
  margin-bottom: var(--spacing-xl);
}

.management-actions {
  margin: 0 var(--spacing-lg);
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.management-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.management-item:last-child {
  border-bottom: none;
}

.management-item:active {
  background: var(--background-color);
}

.management-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color);
  border-radius: var(--radius-md);
  margin-right: var(--spacing-lg);
}

.management-icon image {
  width: 48rpx;
  height: 48rpx;
}

.management-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.management-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.management-subtitle {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.management-arrow {
  font-size: var(--font-lg);
  color: var(--text-placeholder);
}



/* 底部安全距离 */
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + var(--spacing-lg));
}
