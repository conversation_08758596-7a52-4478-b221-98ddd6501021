<!-- 孩子管理页面 -->
<view class="page-container">
    <!-- 顶部添加按钮 -->
    <view class="top-actions">
        <view class="add-child-btn" bindtap="goToAddChild">
            <view class="add-icon">+</view>
            <text>添加孩子</text>
        </view>
    </view>
    <!-- 当前选中的孩子 -->
    <view class="current-child-section" wx:if="{{currentChild.id}}">
        <view class="section-title">
            <text class="emoji">👑</text>
            <text>当前选中的孩子</text>
        </view>
        <view class="current-child-card">
            <view class="child-avatar">
                <view class="default-child-avatar" wx:if="{{currentChild.isEmojiAvatar}}">
                    {{currentChild.avatar}}
                </view>
                <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
                <view class="current-badge">当前</view>
            </view>
            <view class="child-info">
                <text class="child-name">{{currentChild.name}}</text>
                <text class="child-age">
                    {{currentChild.age}}岁 · {{currentChild.gender === 'male' ? '男孩' : '女孩'}}
                </text>
                <text class="child-birthday">生日：{{currentChild.birthday}}</text>
                <text class="child-nickname" wx:if="{{currentChild.nickname}}">
                    昵称：{{currentChild.nickname}}
                </text>
            </view>
            <!-- 当前孩子编辑按钮 -->
            <view class="current-child-actions">
                <view class="quick-edit-btn" bindtap="editCurrentChild">
                    <text class="edit-icon">✏️</text>
                    <text class="edit-text">编辑信息</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 所有孩子列表 -->
    <view class="children-list-section">
        <view class="section-title">
            <text class="emoji">👨‍👩‍👧‍👦</text>
            <text>所有孩子 ({{children.length}})</text>
        </view>
        <!-- 孩子列表 -->
        <view class="children-list" wx:if="{{children.length > 0}}">
            <view class="child-item {{item.id === currentChild.id ? 'current' : ''}}" wx:for="{{children}}" wx:key="id" bindtap="selectChild" data-child="{{item}}">
                <view class="child-left">
                    <view class="child-avatar-small">
                        <view class="default-child-avatar-small" wx:if="{{item.isEmojiAvatar}}">
                            {{item.avatar}}
                        </view>
                        <image wx:else src="{{item.avatar}}" mode="aspectFill"></image>
                        <view class="current-indicator" wx:if="{{item.id === currentChild.id}}">
                            ✓
                        </view>
                    </view>
                    <view class="child-info-small">
                        <text class="child-name-small">{{item.name}}</text>
                        <text class="child-details">
                            {{item.age}}岁 · {{item.gender === 'male' ? '男孩' : '女孩'}}
                        </text>
                    </view>
                    <!-- 点击提示 -->
                    <view class="tap-hint" wx:if="{{item.id !== currentChild.id}}">
                        <text class="hint-text">点击切换</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" wx:else>
            <view class="empty-illustration">
                <view class="empty-state-emoji">📭</view>
            </view>
            <view class="empty-content">
                <text class="empty-title">👶 还没有添加孩子</text>
                <text class="empty-subtitle">添加孩子信息，开始跳绳学习之旅吧！</text>
                <button class="btn btn-primary" bindtap="goToAddChild">添加第一个孩子</button>
            </view>
        </view>
    </view>
    <!-- 管理功能区域 -->
    <view class="management-section" wx:if="{{children.length > 0}}">
        <view class="section-title">
            <text class="emoji">⚙️</text>
            <text>管理功能</text>
        </view>
        <view class="management-actions">
            <view class="management-item" bindtap="goToAddChild">
                <view class="management-icon">
                    <view class="add-child-icon">👶➕</view>
                </view>
                <view class="management-content">
                    <text class="management-title">添加新孩子</text>
                    <text class="management-subtitle">为家庭添加更多孩子</text>
                </view>
                <view class="management-arrow">></view>
            </view>
        </view>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>