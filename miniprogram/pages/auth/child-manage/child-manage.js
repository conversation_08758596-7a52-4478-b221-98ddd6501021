// 孩子管理页面 - 遵循MVS规则

// 引入工具模块
const {
  childrenAPI,
  avatar,
  dataConverter,
  constants,
  userActions,
  childrenActions,
} = require("../../../utils/index.js");
const { APP_CONFIG } = constants;

Page({
  data: {
    // 当前选中的孩子
    currentChild: null,

    // 所有孩子列表
    children: [],

    // 加载状态
    loading: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadCurrentChild();
    this.loadChildrenList();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示时刷新所有数据，确保从编辑页面返回时数据是最新的
    this.refreshAllData();
  },

  /**
   * 刷新所有数据
   */
  async refreshAllData() {
    try {
      // 并行加载当前孩子和孩子列表，提高加载效率
      await Promise.all([this.loadCurrentChild(), this.loadChildrenList()]);

      console.log("✅ 页面数据刷新完成");
    } catch (error) {
      console.error("❌ 页面数据刷新失败:", error);
      // 即使刷新失败，也不影响用户操作
    }
  },

  /**
   * 加载当前孩子信息
   */
  async loadCurrentChild() {
    try {
      // 调用API获取当前选择的孩子
      const response = await childrenAPI.getCurrentChild();
      const currentChild = response.data || response;

      if (currentChild) {
        // 转换后端数据为前端格式
        const frontendChild =
          dataConverter.convertChildToFrontend(currentChild);
        // 处理头像信息
        const avatarInfo = avatar.getAvatarInfo(frontendChild.avatar, "child");

        const processedChild = {
          ...frontendChild,
          avatar: avatarInfo.url,
          isEmojiAvatar: avatarInfo.isEmoji,
          isDefaultAvatar: avatarInfo.isDefault,
        };

        this.setData({
          currentChild: processedChild,
        });

        // 同步到状态管理器
        childrenActions.setCurrentChild(processedChild);

        console.log("✅ 当前孩子信息加载成功:", processedChild.name);
      } else {
        // 如果没有当前孩子，清空显示
        this.setData({
          currentChild: null,
        });
        childrenActions.setCurrentChild(null);
        console.log("ℹ️ 当前没有选中的孩子");
      }
    } catch (error) {
      console.error("❌ 加载当前孩子失败:", error);

      // 如果API调用失败，尝试从状态管理器获取
      const currentChild = childrenActions.getCurrentChild();
      if (currentChild) {
        const avatarInfo = avatar.getAvatarInfo(currentChild.avatar, "child");

        this.setData({
          currentChild: {
            ...currentChild,
            avatar: avatarInfo.url,
            isEmojiAvatar: avatarInfo.isEmoji,
            isDefaultAvatar: avatarInfo.isDefault,
          },
        });
        console.log("✅ 从缓存加载当前孩子信息:", currentChild.name);
      } else {
        // 完全没有数据时的处理
        this.setData({
          currentChild: null,
        });
        console.log("⚠️ 无法获取当前孩子信息");
      }
    }
  },

  /**
   * 加载孩子列表
   */
  async loadChildrenList() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 调用真实API获取孩子列表
      const response = await childrenAPI.getChildrenList();
      const children = response.data || response;

      // 转换后端数据为前端格式并处理头像
      const processedChildren =
        dataConverter.convertChildrenListToFrontend(children);
      const childrenWithAvatar = avatar.processAvatarList(
        processedChildren,
        "avatar",
        "child"
      );

      this.setData({
        children: childrenWithAvatar,
      });
    } catch (error) {
      console.error("加载孩子列表失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 选择孩子
   */
  async selectChild(e) {
    const child = e.currentTarget.dataset.child;

    try {
      // 调用API选择当前孩子
      await childrenAPI.selectCurrentChild(child.id);

      // 更新当前选中的孩子（头像信息已经在列表中处理过了）
      this.setData({
        currentChild: child,
      });

      // 使用状态管理器更新当前孩子
      childrenActions.setCurrentChild(child);

      wx.showToast({
        title: `已切换到${child.name}`,
        icon: "success",
      });
    } catch (error) {
      console.error("选择孩子失败:", error);
      wx.showToast({
        title: "切换失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 编辑当前孩子信息
   */
  editCurrentChild() {
    if (!this.data.currentChild || !this.data.currentChild.id) {
      wx.showToast({
        title: "当前没有选中的孩子",
        icon: "none",
      });
      return;
    }

    console.log("🔧 编辑当前孩子信息:", this.data.currentChild);

    // 确保当前孩子信息同步到状态管理器
    childrenActions.setCurrentChild(this.data.currentChild);

    wx.navigateTo({
      url: `/pages/auth/child-create/child-create?mode=edit&child_id=${this.data.currentChild.id}`,
      fail: (error) => {
        console.error("❌ 跳转编辑页面失败:", error);
        wx.showToast({
          title: "跳转失败，请重试",
          icon: "none",
        });
      },
    });
  },

  /**
   * 跳转到添加孩子页面
   */
  goToAddChild() {
    // 检查孩子数量限制
    if (this.data.children.length >= APP_CONFIG.MAX_CHILDREN) {
      wx.showModal({
        title: "提示",
        content: `最多只能添加${APP_CONFIG.MAX_CHILDREN}个孩子`,
        showCancel: false,
        confirmText: "知道了",
      });
      return;
    }

    wx.navigateTo({
      url: "/pages/auth/child-create/child-create?mode=add",
    });
  },

  /**
   * 切换账号
   */
  switchAccount() {
    wx.showModal({
      title: "切换账号",
      content: "确定要退出当前账号吗？",
      success: (res) => {
        if (res.confirm) {
          // 清除本地数据
          wx.clearStorageSync();

          // 跳转到登录页
          wx.reLaunch({
            url: "/pages/login/login",
          });
        }
      },
    });
  },
});
