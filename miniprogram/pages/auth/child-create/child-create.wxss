/* 添加/编辑孩子页面样式 - WeUI风格 */

.page {
  min-height: 100vh;
  background-color: var(--weui-BG-1);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 头像展示区域 */
.avatar-section {
  background-color: var(--weui-BG-2);
  padding: 60rpx 30rpx 40rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.child-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: var(--weui-BG-1);
  border: 4rpx solid var(--weui-FG-3);
}

.avatar-desc {
  font-size: 28rpx;
  color: var(--weui-FG-1);
  line-height: 1.4;
}

/* WeUI 表单自定义样式 */
.weui-label {
  font-size: 34rpx;
  color: var(--weui-FG-0);
  font-weight: 400;
}

.required {
  color: var(--weui-RED);
  margin-left: 4rpx;
}

/* 性别选择器 */
.gender-selector {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.gender-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid var(--weui-FG-3);
  border-radius: 16rpx;
  background-color: var(--weui-BG-2);
  transition: all 0.3s ease;
}

.gender-option.selected {
  border-color: var(--weui-BRAND);
  background-color: rgba(7, 193, 96, 0.1);
}

.gender-emoji {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.gender-text {
  font-size: 28rpx;
  color: var(--weui-FG-0);
  font-weight: 500;
}

.gender-option.selected .gender-text {
  color: var(--weui-BRAND);
}

/* 日期选择器 */
.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.date-selected {
  color: var(--weui-FG-0);
}

.date-placeholder {
  color: var(--weui-FG-2);
}

/* 年龄显示 */
.age-text {
  color: var(--weui-FG-0);
  font-size: 34rpx;
  margin-right: 10rpx;
}

.age-note {
  color: var(--weui-FG-1);
  font-size: 28rpx;
}

/* 按钮区域 */
.weui-btn-area {
  margin: 60rpx 30rpx 30rpx;
}

.weui-btn {
  margin-bottom: 20rpx;
}

.weui-btn:last-child {
  margin-bottom: 0;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
