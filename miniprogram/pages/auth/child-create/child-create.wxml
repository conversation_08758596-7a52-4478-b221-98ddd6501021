<!-- 添加/编辑孩子页面 -->
<view class="page">
  <!-- 头像展示区域 -->
  <view class="avatar-section">
    <view class="avatar-container">
      <image class="child-avatar" src="{{childAvatar}}" mode="aspectFill"></image>
    </view>
    <view class="avatar-desc">系统将自动为孩子分配默认头像</view>
  </view>
  <!-- 基本信息表单 -->
  <view class="weui-cells__title">基本信息</view>
  <view class="weui-cells weui-cells_after-title">
    <!-- 姓名输入 -->
    <view class="weui-cell weui-cell_input">
      <view class="weui-cell__hd">
        <view class="weui-label">
          姓名
          <text class="required">*</text>
        </view>
      </view>
      <view class="weui-cell__bd">
        <input class="weui-input" type="text" placeholder="请输入孩子的姓名" value="{{formData.name}}" bindinput="onNameInput" maxlength="20" />
      </view>
    </view>
    <!-- 性别选择 -->
    <view class="weui-cell">
      <view class="weui-cell__hd">
        <view class="weui-label">
          性别
          <text class="required">*</text>
        </view>
      </view>
      <view class="weui-cell__bd">
        <view class="gender-selector">
          <view class="gender-option {{formData.gender != 'female' ? 'selected' : ''}}" bindtap="selectGender" data-gender="male">
            <text class="gender-emoji">👦</text>
            <text class="gender-text">男孩</text>
          </view>
          <view class="gender-option {{formData.gender === 'female' ? 'selected' : ''}}" bindtap="selectGender" data-gender="female">
            <text class="gender-emoji">👧</text>
            <text class="gender-text">女孩</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 昵称输入 -->
    <view class="weui-cell weui-cell_input">
      <view class="weui-cell__hd">
        <view class="weui-label">昵称</view>
      </view>
      <view class="weui-cell__bd">
        <input class="weui-input" type="text" placeholder="给孩子起个爱的昵称" value="{{formData.nickname}}" bindinput="onNicknameInput" maxlength="15" />
      </view>
    </view>
    <!-- 生日选择 -->
    <view class="weui-cell weui-cell_access" hover-class="weui-cell_active">
      <view class="weui-cell__hd">
        <view class="weui-label">生日</view>
      </view>
      <view class="weui-cell__bd">
        <picker mode="date" value="{{formData.birthday}}" bindchange="onBirthdayChange" end="{{today}}">
          <view class="date-picker">
            <text class="{{formData.birthday ? 'date-selected' : 'date-placeholder'}}">
              {{formData.birthday || '请选择生日'}}
            </text>
          </view>
        </picker>
      </view>
      <view class="weui-cell__ft weui-cell__ft_in-access"></view>
    </view>
    <!-- 年龄显示 -->
    <view class="weui-cell" wx:if="{{formData.birthday}}">
      <view class="weui-cell__hd">
        <view class="weui-label">年龄</view>
      </view>
      <view class="weui-cell__bd">
        <text class="age-text">{{calculatedAge}}岁</text>
        <text class="age-note">（根据生日自动计算）</text>
      </view>
    </view>
  </view>
  <!-- 底部操作按钮 -->
  <view class="weui-btn-area">
    <button class="weui-btn weui-btn_primary" bindtap="saveChild" disabled="{{!canSave}}">
      {{mode === 'edit' ? '保存修改' : '添加孩子'}}
    </button>
    <button class="weui-btn weui-btn_default" bindtap="goBack">取消</button>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>