/* 登录页面样式 - 参考grew_up风格 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF7A45 0%, #FF9A6B 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* Logo区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 80rpx;
  margin-bottom: 100rpx;
}

.logo-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 功能介绍区域 */
.features-section {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 120rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.feature-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 登录区域 */
.login-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: #FFFFFF;
  color: #FF7A45;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.login-btn.loading {
  background: rgba(255, 255, 255, 0.8);
  color: rgba(255, 122, 69, 0.6);
}

.login-btn:not(.loading):active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.privacy-notice {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.5;
}

.privacy-notice .link {
  color: #FFFFFF;
  text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
  margin-top: auto;
  opacity: 0.3;
}

.decoration-text {
  font-size: 60rpx;
  color: rgba(255, 255, 255, 0.3);
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.loading-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333333;
}
