<!-- 微信登录页 -->
<view class="login-container">
  <!-- 顶部Logo区域 -->
  <view class="logo-section">
    <view class="logo-placeholder">📱</view>
    <text class="app-name">跳跳星球</text>
    <text class="app-slogan">你的每一次进步 都值得喝彩</text>
  </view>
  <!-- 功能介绍区域 -->
  <view class="features-section">
    <view class="feature-item">
      <view class="feature-icon">🏃</view>
      <text class="feature-text">跳绳打卡</text>
    </view>
    <view class="feature-item">
      <view class="feature-icon">👥</view>
      <text class="feature-text">社团学习</text>
    </view>
    <view class="feature-item">
      <view class="feature-icon">🏆</view>
      <text class="feature-text">成长勋章</text>
    </view>
  </view>
  <!-- 登录按钮区域 -->
  <view class="login-section">
    <button class="login-btn {{isLoading ? 'loading' : ''}}" bindtap="onWechatLogin" disabled="{{isLoading}}">
      <text wx:if="{{!isLoading}}">微信一键登录</text>
      <text wx:else>登录中...</text>
    </button>
    <view class="privacy-notice">
      <text>登录即表示同意</text>
      <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      <text>和</text>
      <text class="link" bindtap="showUserAgreement">《用户协议》</text>
    </view>
  </view>
  <!-- 底部装饰 -->
  <view class="bottom-decoration">
    <text class="decoration-text">🌟</text>
  </view>
</view>
<!-- 加载遮罩 -->
<view class="loading-mask" wx:if="{{isLoading}}">
  <view class="loading-content">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">正在登录...</text>
  </view>
</view>