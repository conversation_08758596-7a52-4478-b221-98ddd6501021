// 微信登录页 - 使用状态管理系统
const {
  http,
  wechat,
  navigation,
  pageUtils,
  constants,
  errorHandler,
  userActions,
  childrenActions,
  createStatePage,
  authAPI,
  userAPI,
} = require("../../utils/index.js");

const app = getApp();

// 使用状态管理创建页面
const loginPage = createStatePage({
  data: {
    isLoading: false,
    // 跳转参数保存
    redirectInfo: null,
  },

  onLoad(options) {
    console.log("登录页面加载", options);

    // 保存跳转参数
    this.saveRedirectInfo(options);

    // 检查是否已经登录且有完整用户信息
    const userInfo = userActions.getUserInfo();
    if (userActions.isLoggedIn() && userInfo && userInfo.nickname) {
      console.log("用户已登录且有完整信息，直接跳转");
      this.performRedirect();
      return;
    }

    // 数据埋点
    if (app.track) {
      app.track("page_view", {
        page_name: "login",
        source: options.source || "direct",
      });
    }
  },

  onShow() {
    // 重置加载状态
    this.setData({
      isLoading: false,
    });
  },

  /**
   * 保存跳转参数信息
   */
  saveRedirectInfo(options) {
    const redirectInfo = {
      // 来源页面路径
      from: options.from || "",
      // 目标页面路径 - 需要解码
      redirectUrl: options.redirectUrl
        ? decodeURIComponent(options.redirectUrl)
        : "",
      // 页面参数
      params: {},
      // 是否为tabBar页面
      isTabBar: false,
    };

    console.log("🔍 原始参数:", options);
    console.log("🔍 解码后的redirectUrl:", redirectInfo.redirectUrl);

    // 解析并保存所有参数（除了系统参数）
    Object.keys(options).forEach((key) => {
      if (!["from", "redirectUrl"].includes(key)) {
        redirectInfo.params[key] = options[key];
      }
    });

    // 判断目标页面是否为tabBar页面
    if (redirectInfo.redirectUrl) {
      const tabBarPages = [
        "/pages/home/<USER>",
        "/pages/growth/growth",
        "/pages/profile/profile",
      ];

      // 提取URL的路径部分（去掉参数）
      const urlPath = redirectInfo.redirectUrl.split("?")[0];
      redirectInfo.isTabBar = tabBarPages.includes(urlPath);

      console.log("TabBar页面判断:", {
        originalRedirectUrl: options.redirectUrl,
        decodedRedirectUrl: redirectInfo.redirectUrl,
        urlPath: urlPath,
        isTabBar: redirectInfo.isTabBar,
      });
    }

    this.setData({ redirectInfo });
    console.log("保存跳转信息:", redirectInfo);
  },

  /**
   * 微信一键登录 - 采用参考项目方式但保持我们的架构
   */
  async onWechatLogin() {
    console.log("开始微信一键登录");

    if (this.data.isLoading) return;

    this.setData({ isLoading: true });
    pageUtils.showLoading("登录中...");

    // 数据埋点
    if (app.track) {
      app.track("login_attempt", {
        method: "wechat",
      });
    }

    try {
      // 1. 先尝试获取用户信息（必须在用户点击事件中直接调用）
      let userInfo = null;
      try {
        console.log("📱 尝试获取用户信息...");
        userInfo = await wechat.auth.getUserProfile();
        console.log("✅ 用户信息获取成功:", userInfo);
      } catch (error) {
        console.log("⚠️ 用户拒绝授权或获取失败，继续基础登录:", error.message);
      }

      // 2. 执行登录流程
      await this.performFullLogin(userInfo);
    } catch (error) {
      console.error("登录失败:", error);
      this.handleLoginError(error.message || "登录失败，请重试");
    }
  },

  /**
   * 执行完整登录流程 - 保持我们的架构优势
   * @param {Object} userInfo 已获取的用户信息（可选）
   */
  async performFullLogin(userInfo = null) {
    console.log("🚀 开始完整登录流程");

    // 1. 获取微信登录code
    const code = await this.getWechatCode();
    console.log("📱 获取微信code成功:", code);

    // 2. 调用后端登录API
    console.log("🌐 准备调用登录API...");
    const loginData = await this.callLoginAPI(code, userInfo);

    // 3. 处理登录成功
    console.log("✅ 登录流程完成，处理登录成功");
    this.handleLoginSuccess(loginData, userInfo);
  },

  /**
   * 获取微信登录code
   */
  async getWechatCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log("获取微信code成功:", res.code);
            resolve(res.code);
          } else {
            reject(new Error("获取登录凭证失败"));
          }
        },
        fail: (err) => {
          console.error("微信登录失败:", err);
          reject(new Error("微信登录失败"));
        },
      });
    });
  },

  /**
   * 调用后端登录API
   */
  async callLoginAPI(code, userInfo) {
    console.log("🌐 调用真实登录API");
    console.log("  - 微信code:", code);
    console.log("  - 用户信息:", userInfo);

    try {
      // 准备登录参数
      const loginParams = {
        code: code,
        // 如果有用户信息，可以传递加密数据（暂时不实现）
        encryptedData: "",
        iv: "",
      };

      // 调用统一的认证API
      const loginData = await authAPI.wechatLogin(loginParams);

      console.log("✅ 登录API调用成功:", loginData);
      return loginData;
    } catch (error) {
      console.error("❌ 登录API调用失败:", error);

      // 开发阶段：如果API调用失败，使用模拟数据
      console.log("🔧 API调用失败，使用模拟数据（开发阶段）");

      // 生成标准的JWT格式Token（用于开发测试）
      const mockJwtToken = this.generateMockJwtToken();

      const mockLoginData = {
        access_token: mockJwtToken,
        user_info: {
          id: Date.now(),
          openid: "mock_openid_" + Date.now(),
          unionid: "mock_unionid_" + Date.now(),
          nickname:
            userInfo?.nickName || "跳跳用户" + Math.floor(Math.random() * 1000),
          avatar: userInfo?.avatarUrl || "",
          phone: "",
          gender: userInfo?.gender || 0,
          city: userInfo?.city || "",
          province: userInfo?.province || "",
          // 移除数据库中不存在的字段 country
        },
        is_new_user: true,
      };

      console.log("✅ 使用模拟登录数据:", mockLoginData);
      return mockLoginData;
    }
  },

  /**
   * 生成模拟JWT Token（开发用）
   */
  generateMockJwtToken() {
    // JWT Header
    const header = {
      alg: "HS256",
      typ: "JWT",
    };

    // JWT Payload
    const payload = {
      sub: "mock_user_" + Date.now(),
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24小时后过期
      openid: "mock_openid_" + Date.now(),
      unionid: "mock_unionid_" + Date.now(),
    };

    // 简单的Base64编码（仅用于开发测试）
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));
    const signature = "mock_signature_" + Date.now().toString(36);

    // 组合成标准JWT格式
    const jwtToken = `${encodedHeader}.${encodedPayload}.${signature}`;

    console.log("🔧 生成模拟JWT Token:", jwtToken.substring(0, 50) + "...");
    return jwtToken;
  },

  /**
   * 处理登录成功
   */
  async handleLoginSuccess(loginData, userInfo) {
    console.log("登录成功:", loginData);

    // 准备用户数据 - 适配不同的API响应格式
    let userData = loginData.user || loginData.user_info || {};
    let token = loginData.token || loginData.access_token || "";
    let refreshToken = loginData.refresh_token || "";

    // 如果获取到了微信用户信息，更新用户资料
    if (userInfo) {
      userData = {
        ...userData,
        nickname: userInfo.nickName, // 微信API返回nickName，映射到nickname
        avatar: userInfo.avatarUrl, // 微信API返回avatarUrl，映射到avatar
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        // 移除数据库中不存在的字段：country, language
      };

      // 异步更新用户资料到后端
      this.updateUserProfile(userInfo);
    }

    // 使用正确的userActions方法保存登录信息
    userActions.login(userData, token, refreshToken);

    // 数据埋点
    if (app.track) {
      app.track("login_success", {
        method: "wechat",
        user_id: userData.id,
        is_new_user: loginData.is_new_user || false,
      });
    }

    // 登录成功后根据current_child_id决定是否同步儿童数据
    await this.conditionalSyncChildrenData(userData);

    pageUtils.hideLoading();
    pageUtils.showSuccess("登录成功");

    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      this.performRedirect();
    }, 1500);
  },

  /**
   * 处理登录失败
   */
  handleLoginError(errorMessage) {
    console.error("登录失败:", errorMessage);

    this.setData({ isLoading: false });
    pageUtils.hideLoading();
    pageUtils.showError(errorMessage);

    // 数据埋点
    if (app.track) {
      app.track("login_failed", {
        method: "wechat",
        error: errorMessage,
      });
    }
  },

  /**
   * 更新用户资料到后端
   */
  async updateUserProfile(userInfo) {
    try {
      const userData = userActions.getUserInfo();
      if (!userData || !userData.id) {
        console.log("⚠️ 用户数据不完整，跳过资料更新");
        return;
      }

      // 使用统一的用户API更新资料
      await userAPI.updateUserInfo({
        nickname: userInfo.nickName, // 微信API返回nickName，映射到nickname
        avatar: userInfo.avatarUrl, // 微信API返回avatarUrl，映射到avatar
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        // 移除数据库中不存在的字段：country
      });

      console.log("✅ 用户资料更新成功");
    } catch (error) {
      console.error("❌ 用户资料更新失败:", error);
      // 不影响登录流程，只记录错误
    }
  },

  /**
   * 执行智能跳转
   */
  performRedirect() {
    const { redirectInfo } = this.data;

    console.log("🚀 开始执行跳转逻辑");
    console.log("📋 跳转信息:", redirectInfo);

    // 如果没有跳转信息，默认跳转到首页
    if (!redirectInfo || !redirectInfo.redirectUrl) {
      console.log("❌ 无跳转信息，跳转到首页");
      navigation.switchTab(constants.PAGES.HOME);
      return;
    }

    const { redirectUrl, params, isTabBar } = redirectInfo;

    // 构建完整的跳转URL
    let targetUrl = redirectUrl;

    // 添加参数
    if (params && Object.keys(params).length > 0) {
      const paramString = Object.keys(params)
        .map((key) => `${key}=${encodeURIComponent(params[key])}`)
        .join("&");
      targetUrl += (targetUrl.includes("?") ? "&" : "?") + paramString;
    }

    console.log("🎯 执行跳转:", {
      originalUrl: redirectUrl,
      targetUrl,
      isTabBar,
      params,
    });

    try {
      if (isTabBar) {
        // TabBar页面使用switchTab，不能带参数
        console.log("📱 使用switchTab跳转到TabBar页面:", redirectUrl);
        navigation.switchTab(redirectUrl);
      } else {
        // 普通页面使用redirectTo，可以带参数
        console.log("📄 使用redirectTo跳转到普通页面:", targetUrl);
        navigation.redirectTo(targetUrl);
      }
    } catch (error) {
      console.error("❌ 跳转失败，回退到首页:", error);
      navigation.switchTab(constants.PAGES.HOME);
    }
  },

  // 显示用户协议
  showUserAgreement() {
    wx.showModal({
      title: "用户协议",
      content: "这里是用户协议的内容...",
      showCancel: false,
      confirmText: "我知道了",
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    wx.showModal({
      title: "隐私政策",
      content: "这里是隐私政策的内容...",
      showCancel: false,
      confirmText: "我知道了",
    });
  },

  /**
   * 根据用户current_child_id条件同步儿童数据
   * 只有当current_child_id > 0时才同步儿童数据
   * @param {Object} userData 用户数据
   */
  async conditionalSyncChildrenData(userData) {
    try {
      console.log("🔄 检查是否需要同步儿童数据...");
      console.log("📋 用户数据:", userData);

      // 检查current_child_id字段
      const currentChildId = userData.current_child_id || 0;
      console.log("🔍 current_child_id:", currentChildId);

      if (currentChildId > 0) {
        console.log("✅ current_child_id > 0，开始同步儿童数据");
        await this.syncChildrenData();
      } else {
        console.log("ℹ️ current_child_id = 0，用户暂无儿童，跳过同步");
        // 清空儿童相关状态
        childrenActions.setChildrenList([]);
        childrenActions.setCurrentChild(null);
      }
    } catch (error) {
      console.error("❌ 条件同步儿童数据失败:", error);
      // 同步失败不影响登录流程，只记录错误
    }
  },

  /**
   * 同步儿童数据
   * 获取用户的儿童列表，并设置当前选中儿童
   */
  async syncChildrenData() {
    try {
      console.log("🔄 开始同步儿童数据...");

      // 获取儿童列表
      const childrenAPI = require("../../apis/children.js");
      const childrenList = await childrenAPI.getChildrenList();

      console.log("✅ 获取儿童列表成功:", childrenList);

      // 更新儿童列表到状态管理
      childrenActions.setChildrenList(childrenList || []);

      // 如果有儿童数据，设置当前选中儿童
      if (childrenList && childrenList.length > 0) {
        // 获取用户信息，查看是否有current_child_id
        const userInfo = userActions.getUserInfo();
        let currentChild = null;

        if (
          userInfo &&
          userInfo.current_child_id &&
          userInfo.current_child_id > 0
        ) {
          // 根据current_child_id查找对应的儿童
          currentChild = childrenList.find(
            (child) => child.id === userInfo.current_child_id
          );
        }

        // 如果没有找到对应的儿童，选择第一个
        if (!currentChild) {
          currentChild = childrenList[0];
        }

        // 设置当前选中儿童
        childrenActions.setCurrentChild(currentChild);
        console.log("✅ 设置当前儿童:", currentChild.name);
      } else {
        console.log("ℹ️ 用户暂无儿童数据");
        childrenActions.setCurrentChild(null);
      }
    } catch (error) {
      console.error("❌ 同步儿童数据失败:", error);
      // 同步失败不影响登录流程，只记录错误
      // 用户可以稍后手动刷新或在儿童管理页面重新获取
    }
  },
});

// 创建页面实例
Page(loginPage);
