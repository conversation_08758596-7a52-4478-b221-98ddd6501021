<!-- 图片集成测试页面 -->
<view class="page-container">
  <view class="test-section">
    <view class="section-title">📱 应用Logo测试</view>
    <view class="test-grid">
      <view class="test-item">
        <smart-image name="app-logo" custom-class="size-large"></smart-image>
        <text class="test-label">彩色Logo</text>
      </view>
      <view class="test-item">
        <smart-image name="app-logo-white" custom-class="size-large" custom-style="background: #333; border-radius: 8rpx;"></smart-image>
        <text class="test-label">白色Logo</text>
      </view>
    </view>
  </view>
  <view class="test-section">
    <view class="section-title">🎯 功能图标测试</view>
    <view class="test-grid">
      <view class="test-item">
        <smart-image name="icon-calendar" custom-class="size-medium"></smart-image>
        <text class="test-label">日历</text>
      </view>
      <view class="test-item">
        <smart-image name="icon-statistics" custom-class="size-medium"></smart-image>
        <text class="test-label">统计</text>
      </view>
      <view class="test-item">
        <smart-image name="icon-trophy" custom-class="size-medium"></smart-image>
        <text class="test-label">奖杯</text>
      </view>
      <view class="test-item">
        <smart-image name="icon-star" custom-class="size-medium"></smart-image>
        <text class="test-label">星星</text>
      </view>
      <view class="test-item">
        <smart-image name="icon-heart" custom-class="size-medium"></smart-image>
        <text class="test-label">爱心</text>
      </view>
    </view>
  </view>
  <view class="test-section">
    <view class="section-title">📱 TabBar图标测试</view>
    <view class="test-grid">
      <view class="test-item">
        <smart-image name="tab-home" custom-class="size-medium"></smart-image>
        <text class="test-label">首页</text>
      </view>
      <view class="test-item">
        <smart-image name="tab-home-active" custom-class="size-medium"></smart-image>
        <text class="test-label">首页(选中)</text>
      </view>
      <view class="test-item">
        <smart-image name="tab-growth" custom-class="size-medium"></smart-image>
        <text class="test-label">成长</text>
      </view>
      <view class="test-item">
        <smart-image name="tab-growth-active" custom-class="size-medium"></smart-image>
        <text class="test-label">成长(选中)</text>
      </view>
      <view class="test-item">
        <smart-image name="tab-profile" custom-class="size-medium"></smart-image>
        <text class="test-label">我的</text>
      </view>
      <view class="test-item">
        <smart-image name="tab-profile-active" custom-class="size-medium"></smart-image>
        <text class="test-label">我的(选中)</text>
      </view>
    </view>
  </view>
  <view class="test-section">
    <view class="section-title">📋 空状态组件测试</view>
    <view class="empty-test-container">
      <empty-state show="{{true}}" type="leaderboard" showAction="{{false}}"></empty-state>
    </view>
    <view class="empty-test-container">
      <empty-state show="{{true}}" type="badges" showAction="{{false}}"></empty-state>
    </view>
    <view class="empty-test-container">
      <empty-state show="{{true}}" type="children" showAction="{{false}}"></empty-state>
    </view>
  </view>
  <view class="test-section">
    <view class="section-title">📱 分享相关测试</view>
    <view class="test-grid">
      <view class="test-item">
        <smart-image name="mini-qr-code" custom-class="size-large"></smart-image>
        <text class="test-label">小程序码</text>
      </view>
    </view>
  </view>
  <view class="test-section">
    <view class="section-title">❌ 错误处理测试</view>
    <view class="test-grid">
      <view class="test-item">
        <smart-image name="non-existent-image" custom-class="size-medium"></smart-image>
        <text class="test-label">不存在的图片名称</text>
      </view>
      <view class="test-item">
        <smart-image name="" custom-class="size-medium" fallbackIcon="❓"></smart-image>
        <text class="test-label">空图片路径</text>
      </view>
    </view>
  </view>
</view>