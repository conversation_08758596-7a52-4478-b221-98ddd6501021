/* 图片集成测试页面样式 */
@import "../../styles/css-icons.wxss";

.page-container {
  padding: 32rpx;
  background-color: var(--background-color);
  min-height: 100vh;
}

.test-section {
  margin-bottom: 48rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
  gap: 24rpx;
}

.test-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: 12rpx;
  background-color: var(--background-color);
  border: 1rpx solid var(--border-color);
  transition: all 0.2s ease;
}

.test-item:active {
  transform: scale(0.95);
  background-color: #f0f0f0;
}

.test-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 12rpx;
  text-align: center;
  line-height: 1.2;
}

.empty-test-container {
  margin-bottom: 32rpx;
  border: 1rpx dashed var(--border-color);
  border-radius: 12rpx;
  background-color: var(--background-color);
}

.empty-test-container:last-child {
  margin-bottom: 0;
}

/* 尺寸样式覆盖 */
.size-small {
  width: 48rpx !important;
  height: 48rpx !important;
}

.size-medium {
  width: 80rpx !important;
  height: 80rpx !important;
}

.size-large {
  width: 120rpx !important;
  height: 120rpx !important;
}

/* 特殊样式 */
.test-item .smart-image {
  flex-shrink: 0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .test-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
  }
  
  .test-item {
    padding: 12rpx;
  }
  
  .test-label {
    font-size: 22rpx;
  }
}
