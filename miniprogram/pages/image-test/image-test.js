// 图片集成测试页面
const { imageManager } = require('../../utils/image-manager');

Page({
  data: {
    
  },

  onLoad() {
    console.log('图片集成测试页面加载');
    console.log('可用图片列表:', imageManager.getAvailableImages());
    console.log('可用CSS图标列表:', imageManager.getAvailableCSSIcons());
  },

  onShow() {
    // 测试图片管理器功能
    this.testImageManager();
  },

  // 测试图片管理器
  testImageManager() {
    console.log('=== 图片管理器测试 ===');
    
    // 测试获取图片路径
    console.log('app-logo路径:', imageManager.getImagePath('app-logo'));
    console.log('icon-calendar路径:', imageManager.getImagePath('icon-calendar'));
    
    // 测试获取CSS图标
    console.log('empty-leaderboard图标:', imageManager.getCSSIcon('empty-leaderboard'));
    console.log('default-avatar图标:', imageManager.getCSSIcon('default-avatar'));
    
    // 测试资源获取
    console.log('app-logo资源:', imageManager.getResource('app-logo'));
    console.log('empty-badges资源:', imageManager.getResource('empty-badges'));
    console.log('不存在的资源:', imageManager.getResource('non-existent'));
  }
});
