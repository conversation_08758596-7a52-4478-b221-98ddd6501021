// 分享海报生成页面 V4版本
const app = getApp();

Page({
  data: {
    // 选择的海报类型
    selectedType: "",

    // 海报配置
    posterConfig: {},

    // 画布设置
    canvasWidth: 300,
    canvasHeight: 400,

    // 模板相关
    templates: [],
    currentTemplate: "template1",
    showTemplateModal: false,

    // 自定义设置
    customText: "",
    showQRCode: true,
    showWatermark: true,

    // 状态控制
    isGenerating: false,
    showPreviewModal: false,
    previewImageUrl: "",

    // 海报数据
    posterData: {},
  },

  onLoad(options) {
    console.log("分享海报页面加载", options);
    this.initPage(options);
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    // 从参数中获取海报类型和数据
    const type = options.type;
    const data = options.data
      ? JSON.parse(decodeURIComponent(options.data))
      : {};

    if (type) {
      this.selectType({ currentTarget: { dataset: { type } } });
      this.setData({
        posterData: data,
      });
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: "生成分享海报",
    });
  },

  /**
   * 选择海报类型
   */
  selectType(e) {
    const type = e.currentTarget.dataset.type;
    const config = this.getPosterConfig(type);
    const templates = this.getTemplates(type);

    this.setData({
      selectedType: type,
      posterConfig: config,
      templates: templates,
      currentTemplate: templates[0]?.id || "template1",
      customText: config.defaultText || "",
    });

    // 生成海报
    this.generatePoster();
  },

  /**
   * 获取海报配置
   */
  getPosterConfig(type) {
    const configs = {
      checkin: {
        title: "打卡成就海报",
        allowTextEdit: true,
        allowWatermark: true,
        textPlaceholder: "今天又完成了一次打卡，坚持就是胜利！",
        defaultText: "今天又完成了一次打卡，坚持就是胜利！",
      },
      medal: {
        title: "勋章获得海报",
        allowTextEdit: true,
        allowWatermark: true,
        textPlaceholder: "获得新勋章啦，继续加油！",
        defaultText: "获得新勋章啦，继续加油！",
      },
      ranking: {
        title: "排行榜成绩海报",
        allowTextEdit: true,
        allowWatermark: true,
        textPlaceholder: "看看我的排行榜成绩，一起来挑战吧！",
        defaultText: "看看我的排行榜成绩，一起来挑战吧！",
      },
      contract: {
        title: "契约达成海报",
        allowTextEdit: true,
        allowWatermark: true,
        textPlaceholder: "家庭荣誉契约达成，我们做到了！",
        defaultText: "家庭荣誉契约达成，我们做到了！",
      },
      progress: {
        title: "成长进度海报",
        allowTextEdit: true,
        allowWatermark: true,
        textPlaceholder: "看看我的成长进度，每天都在进步！",
        defaultText: "看看我的成长进度，每天都在进步！",
      },
    };

    return configs[type] || configs.checkin;
  },

  /**
   * 获取模板列表
   */
  getTemplates(type) {
    const baseTemplates = [
      {
        id: "template1",
        name: "活力橙",
        description: "温暖活力的橙色主题",
        bgColor: "linear-gradient(135deg, #FF7A45 0%, #FF9A56 100%)",
      },
      {
        id: "template2",
        name: "温馨粉",
        description: "温馨可爱的粉色主题",
        bgColor: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      },
      {
        id: "template3",
        name: "清新蓝",
        description: "清新自然的蓝色系设计",
        bgColor: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      },
      {
        id: "template4",
        name: "自然绿",
        description: "自然清新的绿色风格",
        bgColor: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      },
    ];

    return baseTemplates;
  },

  /**
   * 返回类型选择
   */
  backToTypes() {
    this.setData({
      selectedType: "",
      posterConfig: {},
      customText: "",
      isGenerating: false,
    });
  },

  /**
   * 显示模板选择
   */
  showTemplates() {
    this.setData({
      showTemplateModal: true,
    });
  },

  /**
   * 隐藏模板选择
   */
  hideTemplates() {
    this.setData({
      showTemplateModal: false,
    });
  },

  /**
   * 选择模板
   */
  selectTemplate(e) {
    const template = e.currentTarget.dataset.template;
    this.setData({
      currentTemplate: template.id,
      showTemplateModal: false,
    });

    // 重新生成海报
    this.generatePoster();
  },

  /**
   * 自定义文案输入
   */
  onCustomTextInput(e) {
    this.setData({
      customText: e.detail.value,
    });
  },

  /**
   * 二维码开关
   */
  onQRCodeToggle(e) {
    this.setData({
      showQRCode: e.detail.value,
    });
  },

  /**
   * 水印开关
   */
  onWatermarkToggle(e) {
    this.setData({
      showWatermark: e.detail.value,
    });
  },

  /**
   * 生成海报
   */
  generatePoster() {
    this.setData({
      isGenerating: true,
    });

    // 模拟生成过程
    setTimeout(() => {
      this.drawPoster();
    }, 500);
  },

  /**
   * 绘制海报
   */
  drawPoster() {
    const ctx = wx.createCanvasContext("posterCanvas", this);
    const {
      canvasWidth,
      canvasHeight,
      currentTemplate,
      customText,
      showQRCode,
      showWatermark,
    } = this.data;

    // 获取当前模板
    const template = this.data.templates.find((t) => t.id === currentTemplate);

    // 绘制背景 - 温暖的橙色渐变
    const gradient = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight);
    gradient.addColorStop(0, "#FF7A45");
    gradient.addColorStop(1, "#FF9A56");
    ctx.setFillStyle(gradient);
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 绘制内容区域
    ctx.setFillStyle("#FFFFFF");
    ctx.setGlobalAlpha(0.95);
    ctx.fillRect(20, 40, canvasWidth - 40, canvasHeight - 80);
    ctx.setGlobalAlpha(1);

    // 绘制标题
    ctx.setFillStyle("#333333");
    ctx.setFontSize(24);
    ctx.setTextAlign("center");
    ctx.fillText(this.data.posterConfig.title, canvasWidth / 2, 80);

    // 绘制自定义文案
    if (customText) {
      ctx.setFillStyle("#666666");
      ctx.setFontSize(16);
      ctx.fillText(customText, canvasWidth / 2, 120);
    }

    // 绘制主要内容（根据类型不同）
    this.drawMainContent(ctx);

    // 绘制二维码
    if (showQRCode) {
      this.drawQRCode(ctx, canvasWidth, canvasHeight);
    }

    // 绘制水印
    if (showWatermark) {
      ctx.setFillStyle("#CCCCCC");
      ctx.setFontSize(12);
      ctx.setTextAlign("left");
      ctx.fillText("跳跳星球", 30, canvasHeight - 25);
    }

    ctx.draw(false, () => {
      this.setData({
        isGenerating: false,
      });
    });
  },

  /**
   * 绘制主要内容
   */
  drawMainContent(ctx) {
    const { selectedType, posterData, canvasWidth } = this.data;

    switch (selectedType) {
      case "checkin":
        this.drawCheckinContent(ctx);
        break;
      case "medal":
        this.drawMedalContent(ctx);
        break;
      case "ranking":
        this.drawRankingContent(ctx);
        break;
      case "contract":
        this.drawContractContent(ctx);
        break;
      case "progress":
        this.drawProgressContent(ctx);
        break;
    }
  },

  /**
   * 绘制打卡内容
   */
  drawCheckinContent(ctx) {
    const { canvasWidth } = this.data;

    // 绘制打卡图标
    ctx.setFillStyle("#FF7A45");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("✅", canvasWidth / 2, 200);

    // 绘制积分信息
    ctx.setFillStyle("#333333");
    ctx.setFontSize(20);
    ctx.fillText("获得积分：25分", canvasWidth / 2, 240);

    // 绘制连续天数
    ctx.setFillStyle("#666666");
    ctx.setFontSize(16);
    ctx.fillText("连续打卡：8天", canvasWidth / 2, 270);
  },

  /**
   * 绘制勋章内容
   */
  drawMedalContent(ctx) {
    const { canvasWidth } = this.data;

    // 绘制勋章图标
    ctx.setFillStyle("#FFD700");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("🏅", canvasWidth / 2, 200);

    // 绘制勋章名称
    ctx.setFillStyle("#333333");
    ctx.setFontSize(20);
    ctx.fillText("坚持一周勋章", canvasWidth / 2, 240);
  },

  /**
   * 绘制排行榜内容
   */
  drawRankingContent(ctx) {
    const { canvasWidth } = this.data;

    // 绘制排名图标
    ctx.setFillStyle("#FF7A45");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("🏆", canvasWidth / 2, 200);

    // 绘制排名信息
    ctx.setFillStyle("#333333");
    ctx.setFontSize(20);
    ctx.fillText("排名：第5名", canvasWidth / 2, 240);

    // 绘制积分
    ctx.setFillStyle("#666666");
    ctx.setFontSize(16);
    ctx.fillText("总积分：285分", canvasWidth / 2, 270);
  },

  /**
   * 绘制契约内容
   */
  drawContractContent(ctx) {
    const { canvasWidth } = this.data;

    // 绘制契约图标
    ctx.setFillStyle("#4A90E2");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("🤝", canvasWidth / 2, 200);

    // 绘制契约信息
    ctx.setFillStyle("#333333");
    ctx.setFontSize(20);
    ctx.fillText("21天挑战契约达成", canvasWidth / 2, 240);
  },

  /**
   * 绘制进度内容
   */
  drawProgressContent(ctx) {
    const { canvasWidth } = this.data;

    // 绘制进度图标
    ctx.setFillStyle("#52C41A");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("📊", canvasWidth / 2, 200);

    // 绘制进度信息
    ctx.setFillStyle("#333333");
    ctx.setFontSize(20);
    ctx.fillText("训练营进度：38%", canvasWidth / 2, 240);

    // 绘制天数
    ctx.setFillStyle("#666666");
    ctx.setFontSize(16);
    ctx.fillText("8/21天", canvasWidth / 2, 270);
  },

  /**
   * 重新生成海报
   */
  regeneratePoster() {
    this.generatePoster();
  },

  /**
   * 预览海报
   */
  previewPoster() {
    wx.canvasToTempFilePath(
      {
        canvasId: "posterCanvas",
        success: (res) => {
          this.setData({
            previewImageUrl: res.tempFilePath,
            showPreviewModal: true,
          });
        },
        fail: (err) => {
          console.error("生成预览图失败", err);
          wx.showToast({
            title: "预览失败",
            icon: "none",
          });
        },
      },
      this
    );
  },

  /**
   * 隐藏预览
   */
  hidePreview() {
    this.setData({
      showPreviewModal: false,
    });
  },

  /**
   * 确认海报
   */
  confirmPoster() {
    this.hidePreview();
    this.savePoster();
  },

  /**
   * 保存海报
   */
  savePoster() {
    wx.canvasToTempFilePath(
      {
        canvasId: "posterCanvas",
        success: (res) => {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: "保存成功",
                icon: "success",
              });
            },
            fail: () => {
              wx.showToast({
                title: "保存失败",
                icon: "none",
              });
            },
          });
        },
        fail: (err) => {
          console.error("生成图片失败", err);
          wx.showToast({
            title: "生成失败",
            icon: "none",
          });
        },
      },
      this
    );
  },

  /**
   * 分享给好友
   */
  sharePoster() {
    wx.showToast({
      title: "分享给好友",
      icon: "success",
    });
  },

  /**
   * 分享到朋友圈
   */
  shareToMoments() {
    this.savePoster();
    wx.showToast({
      title: "已保存，可在朋友圈分享",
      icon: "success",
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 绘制二维码
   */
  drawQRCode(ctx, canvasWidth, canvasHeight) {
    const qrSize = 60;
    const qrX = canvasWidth - 80;
    const qrY = canvasHeight - 80;

    // 绘制二维码背景
    ctx.setFillStyle("#FFFFFF");
    ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10);

    // 绘制二维码边框
    ctx.setStrokeStyle("#E5E5E5");
    ctx.setLineWidth(1);
    ctx.strokeRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10);

    try {
      // 绘制二维码图片
      ctx.drawImage("/images/qr_code.png", qrX, qrY, qrSize, qrSize);

      // 绘制提示文字
      ctx.setFillStyle("#333333");
      ctx.setFontSize(12);
      ctx.setTextAlign("center");
      ctx.fillText("扫码体验", qrX + qrSize / 2, qrY + qrSize + 15);
    } catch (error) {
      console.error("绘制二维码失败:", error);
      // 如果图片加载失败，绘制占位符
      ctx.setFillStyle("#F0F0F0");
      ctx.fillRect(qrX, qrY, qrSize, qrSize);
      ctx.setFillStyle("#999999");
      ctx.setFontSize(10);
      ctx.setTextAlign("center");
      ctx.fillText("二维码", qrX + qrSize / 2, qrY + qrSize / 2);
      ctx.fillText("扫码体验", qrX + qrSize / 2, qrY + qrSize + 15);
    }
  },
});
