/* 简化版分享海报页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
}

.poster-content {
  padding: 32rpx;
}

/* 颜色选择器 */
.color-selector {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex-shrink: 0;
}

.color-options {
  display: flex;
  gap: 24rpx;
  flex: 1;
}

.color-item {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.color-item.active {
  transform: scale(1.2);
}

.color-item.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: bold;
}

/* 海报预览区 */
.poster-preview {
  margin-bottom: 32rpx;
}

.poster-template {
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

/* 海报头部 */
.poster-header {
  padding: 40rpx 32rpx;
  color: #FFFFFF;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.child-info {
  flex: 1;
}

.child-name {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.checkin-date {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.checkin-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
}

.badge-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 海报主体 */
.poster-body {
  padding: 40rpx 32rpx;
}

.camp-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.camp-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
  display: block;
}

/* 打卡统计 */
.checkin-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 48rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

/* 鼓励语 */
.encourage-text {
  background-color: #FFF2ED;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
}

.encourage-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 进度信息 */
.progress-info {
  margin-bottom: 20rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

.progress-bar {
  height: 16rpx;
  background-color: #E5E5E5;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

/* 海报底部 */
.poster-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: #F8F9FA;
  border-top: 1rpx solid #E5E5E5;
}

.app-info {
  flex: 1;
}

.app-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.app-slogan {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.qr-code {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 0;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.save-btn {
  background: #4A90E2;
  color: #FFFFFF;
}

.save-btn:active {
  background: #3A7BC8;
  transform: scale(0.98);
}

.share-btn {
  background: #52C41A;
  color: #FFFFFF;
}

.share-btn:active {
  background: #389E0D;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}
