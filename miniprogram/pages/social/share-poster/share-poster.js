// 简化版分享海报页面
Page({
  data: {
    // 导航栏高度
    navbarHeight: 44,

    // 当前选择的颜色
    currentColor: "orange",

    // 颜色主题配置
    colorThemes: {
      orange: {
        primary: "#FF7A45",
        gradient: "linear-gradient(135deg, #FF7A45 0%, #FF9A56 100%)",
      },
      pink: {
        primary: "#FF6B9D",
        gradient: "linear-gradient(135deg, #FF6B9D 0%, #FF8FB3 100%)",
      },
      blue: {
        primary: "#4A90E2",
        gradient: "linear-gradient(135deg, #4A90E2 0%, #6BA3F0 100%)",
      },
      green: {
        primary: "#52C41A",
        gradient: "linear-gradient(135deg, #52C41A 0%, #73D13D 100%)",
      },
    },

    // 海报数据
    posterData: {
      child: {
        name: "小明",
        avatar: "/images/avatar_child_default.png",
      },
      checkin: {
        date: "2024年1月21日",
        day: 8,
        duration: 15,
        score: 120,
        points: 25,
      },
      camp: {
        title: "21天跳绳挑战",
        subtitle: "从零基础到连续跳绳300个",
        currentDay: 8,
        totalDays: 21,
        progressPercent: 38,
      },
    },

    // 随机鼓励语
    encourageMessage: "坚持就是胜利，继续加油！",
  },

  onLoad(options) {
    console.log("简化版海报页面加载", options);

    // 解析传入的数据
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data));
        this.updatePosterData(data);
      } catch (error) {
        console.error("解析海报数据失败:", error);
      }
    }

    // 生成随机鼓励语
    this.generateEncourageMessage();
  },

  /**
   * 导航栏高度变化
   */
  onNavbarHeightChange(e) {
    this.setData({
      navbarHeight: e.detail.navbarHeight,
    });
  },

  /**
   * 选择颜色
   */
  selectColor(e) {
    const color = e.currentTarget.dataset.color;
    this.setData({
      currentColor: color,
    });
  },

  /**
   * 更新海报数据
   */
  updatePosterData(data) {
    const posterData = { ...this.data.posterData };

    if (data.type === "checkin") {
      posterData.checkin = {
        ...posterData.checkin,
        day: data.currentDay || posterData.checkin.day,
        date: this.formatDate(data.checkinTime) || posterData.checkin.date,
      };

      posterData.camp = {
        ...posterData.camp,
        title: data.campTitle || posterData.camp.title,
        currentDay: data.currentDay || posterData.camp.currentDay,
        totalDays: data.totalDays || posterData.camp.totalDays,
        progressPercent:
          Math.round((data.currentDay / data.totalDays) * 100) ||
          posterData.camp.progressPercent,
      };
    }

    this.setData({ posterData });
  },

  /**
   * 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return null;
    const date = new Date(dateString);
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  },

  /**
   * 生成随机鼓励语
   */
  generateEncourageMessage() {
    const messages = [
      "坚持就是胜利，继续加油！",
      "每一次努力都值得被记录！",
      "小小的坚持，大大的成长！",
      "今天又进步了一点点！",
      "你的努力，爸爸妈妈都看在眼里！",
      "棒棒的表现，为你点赞！",
      "每一天的坚持都在积累成功！",
      "相信自己，你是最棒的！",
      "成长路上，每一步都算数！",
      "今天的你比昨天更优秀！",
    ];

    const randomIndex = Math.floor(Math.random() * messages.length);
    this.setData({
      encourageMessage: messages[randomIndex],
    });
  },

  /**
   * 保存到相册
   */
  saveToAlbum() {
    wx.showLoading({
      title: "保存中...",
    });

    // 模拟保存过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: "保存成功",
        icon: "success",
      });
    }, 1500);
  },

  /**
   * 分享给好友
   */
  shareToFriends() {
    wx.showActionSheet({
      itemList: ["分享到微信", "分享到朋友圈", "复制链接"],
      success: (res) => {
        const actions = ["微信好友", "朋友圈", "复制链接"];
        wx.showToast({
          title: `分享到${actions[res.tapIndex]}`,
          icon: "success",
        });
      },
    });
  },
});
