<!-- 简化版分享海报页面 -->
<view class="page-container">
  <!-- 自定义导航栏 -->
  <custom-navbar title="分享海报" show-back-button="{{true}}" bind:navbarHeightChange="onNavbarHeightChange"></custom-navbar>
  <!-- 海报内容区 -->
  <view class="poster-content" style="margin-top: {{navbarHeight}}px;">
    <!-- 海报预览区 -->
    <view class="poster-preview">
      <!-- 打卡海报模板 -->
      <view class="poster-template checkin-poster">
        <view class="poster-header" style="background: {{colorThemes[currentColor].gradient}};">
          <view class="header-content">
            <image class="child-avatar" src="{{posterData.child.avatar}}" mode="aspectFill" />
            <view class="child-info">
              <text class="child-name">{{posterData.child.name}}</text>
              <text class="checkin-date">{{posterData.checkin.date}}</text>
            </view>
            <view class="checkin-badge">
              <text class="badge-text">第{{posterData.checkin.day}}天</text>
            </view>
          </view>
        </view>
        <view class="poster-body">
          <view class="camp-info">
            <text class="camp-title">{{posterData.camp.title}}</text>
            <text class="camp-subtitle">{{posterData.camp.subtitle}}</text>
          </view>
          <view class="checkin-stats">
            <view class="stat-item">
              <text class="stat-value">{{posterData.checkin.duration}}</text>
              <text class="stat-label">练习时长(分钟)</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{posterData.checkin.score}}</text>
              <text class="stat-label">跳绳成绩(个)</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{posterData.checkin.points}}</text>
              <text class="stat-label">获得积分</text>
            </view>
          </view>
          <view class="encourage-text">
            <text class="encourage-content">{{encourageMessage}}</text>
          </view>
          <view class="progress-info">
            <view class="progress-header">
              <text class="progress-title">训练营进度</text>
              <text class="progress-text">
                {{posterData.camp.currentDay}}/{{posterData.camp.totalDays}} 天 ({{posterData.camp.progressPercent}}%)
              </text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{posterData.camp.progressPercent}}%; background: {{colorThemes[currentColor].primary}};"></view>
            </view>
          </view>
        </view>
        <view class="poster-footer">
          <view class="app-info">
            <text class="app-name">成长伙伴</text>
            <text class="app-slogan">陪伴孩子快乐成长</text>
          </view>
          <image class="qr-code" src="/images/qr_code.png" mode="aspectFit" />
        </view>
      </view>
    </view>
    <!-- 颜色选择器 -->
    <view class="color-selector">
      <text class="selector-title">选择颜色</text>
      <view class="color-options">
        <view class="color-item {{currentColor === 'orange' ? 'active' : ''}}" style="background: #FF7A45;" bindtap="selectColor" data-color="orange"></view>
        <view class="color-item {{currentColor === 'pink' ? 'active' : ''}}" style="background: #FF6B9D;" bindtap="selectColor" data-color="pink"></view>
        <view class="color-item {{currentColor === 'blue' ? 'active' : ''}}" style="background: #4A90E2;" bindtap="selectColor" data-color="blue"></view>
        <view class="color-item {{currentColor === 'green' ? 'active' : ''}}" style="background: #52C41A;" bindtap="selectColor" data-color="green"></view>
      </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn save-btn" bindtap="saveToAlbum">
        <text class="btn-icon">📱</text>
        <text class="btn-text">保存到相册</text>
      </button>
      <button class="action-btn share-btn" bindtap="shareToFriends">
        <text class="btn-icon">💬</text>
        <text class="btn-text">分享给好友</text>
      </button>
    </view>
  </view>
</view>