/* 成就徽章页面样式 */

.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--background-color) 25%);
  padding-bottom: var(--spacing-xxl);
}

/* 成就徽章页面样式 */

/* 当前孩子信息 */
.child-info-section {
  display: flex;
  align-items: center;
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-xl);
  /* backdrop-filter: blur(10rpx); */
}

.child-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: var(--spacing-lg);
}

.child-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.child-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.child-name {
  font-size: var(--font-lg);
  font-weight: 600;
  color: white;
}

.child-progress {
  font-size: var(--font-md);
  color: rgba(255, 255, 255, 0.9);
}

.achievement-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: var(--font-xl);
  font-weight: 700;
  color: white;
}

.stat-label {
  font-size: var(--font-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* 分类标签 */
.category-tabs {
  display: flex;
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-md) 0;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.tab-item text {
  font-size: var(--font-md);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.tab-item.active {
  background: white;
}

.tab-item.active text {
  color: var(--primary-color);
  font-weight: 600;
}

/* 徽章容器 */
.badges-container {
  margin: 0 var(--spacing-lg);
}

/* 最新获得的徽章 */
.latest-badge-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.latest-badge-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.15);
  position: relative;
  overflow: hidden;
}

.badge-glow {
  position: relative;
  margin-right: var(--spacing-lg);
}

.badge-glow::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background: conic-gradient(from 0deg, #FFD700, #FF7A45, #FFD700);
  border-radius: 50%;
  animation: rotate 3s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.badge-icon-large {
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.badge-emoji {
  font-size: 80rpx;
}

.badge-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.badge-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.badge-desc {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.4;
}

.badge-time {
  font-size: var(--font-sm);
  color: var(--text-placeholder);
}

.share-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
}

.share-btn image {
  width: 40rpx;
  height: 40rpx;
  
}

/* 徽章网格 */
.badges-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
}

.badge-item.earned {
  background: linear-gradient(135deg, rgba(255, 122, 69, 0.1), rgba(255, 215, 0, 0.1));
}

.badge-item.locked {
  background: var(--background-color);
}

.badge-item:active {
  transform: scale(0.95);
}

.badge-icon {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.badge-emoji {
  font-size: 60rpx;
  transition: all 0.3s ease;
}

.badge-emoji.grayscale {
  opacity: 0.3;
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon {
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.8);
}

.badge-name {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.badge-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: var(--border-color);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

.earned-time {
  font-size: var(--font-xs);
  color: var(--text-placeholder);
  text-align: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  text-align: center;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.empty-illustration {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: var(--spacing-xl);
}

.empty-illustration image {
  width: 100%;
  height: 100%;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* 徽章详情弹窗 */
.badge-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.badge-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  margin: var(--spacing-lg);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.badge-detail-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.badge-icon-detail {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-emoji-detail {
  font-size: 80rpx;
}

.badge-emoji-detail.grayscale {
  opacity: 0.3;
}

.lock-overlay-detail {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lock-icon-detail {
  width: 48rpx;
  height: 48rpx;
  
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  border-radius: 50%;
  background: var(--background-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.badge-title-detail {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: block;
}

.badge-desc-detail {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  display: block;
}

/* 已获得状态 */
.earned-info {
  margin-bottom: var(--spacing-lg);
}

.earned-badge {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: rgba(82, 196, 26, 0.1);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
}

.earned-icon {
  font-size: var(--font-lg);
  margin-right: var(--spacing-md);
}

.earned-text {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--success-color);
}

.earned-time-detail {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 未获得状态 */
.locked-info {
  margin-bottom: var(--spacing-lg);
}

.locked-badge {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: rgba(153, 153, 153, 0.1);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.locked-icon {
  font-size: var(--font-lg);
  margin-right: var(--spacing-md);
}

.locked-text {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-secondary);
}

.unlock-condition {
  margin-bottom: var(--spacing-lg);
}

.condition-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.condition-text {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.5;
}

.progress-info {
  margin-top: var(--spacing-lg);
}

.progress-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-md);
}

.progress-bar-detail {
  width: 100%;
  height: 16rpx;
  background: var(--border-color);
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill-detail {
  height: 100%;
  background: var(--primary-color);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text-detail {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 徽章奖励 */
.badge-rewards {
  margin-top: var(--spacing-lg);
}

.rewards-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-md);
}

.rewards-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.reward-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--radius-md);
}

.reward-icon {
  font-size: var(--font-md);
  margin-right: var(--spacing-md);
}

.reward-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
}

.modal-footer {
  display: flex;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.modal-footer .btn {
  flex: 1;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + var(--spacing-lg));
}
