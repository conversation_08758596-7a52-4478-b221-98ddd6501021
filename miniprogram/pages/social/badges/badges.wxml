<!-- 成就徽章页面 -->
<view class="page-container">
    <!-- 当前孩子信息 -->
    <view class="child-info-section">
        <view class="child-avatar">
            <smart-image name="default-child-avatar" src="{{currentChild.avatar}}" mode="aspectFill" custom-class="avatar-image"></smart-image>
        </view>
        <view class="child-details">
            <text class="child-name">{{currentChild.name}}</text>
            <text class="child-progress">已获得 {{earnedBadges.length}} 个徽章</text>
        </view>
        <view class="achievement-stats">
            <view class="stat-item">
                <text class="stat-number">{{totalPoints}}</text>
                <text class="stat-label">总积分</text>
            </view>
            <view class="stat-item">
                <text class="stat-number">{{earnedBadges.length}}</text>
                <text class="stat-label">徽章数</text>
            </view>
        </view>
    </view>
    <!-- 分类标签 -->
    <view class="category-tabs">
        <view class="tab-item {{currentCategory === 'all' ? 'active' : ''}}" bindtap="switchCategory" data-category="all">
            <text>全部</text>
        </view>
        <view class="tab-item {{currentCategory === 'earned' ? 'active' : ''}}" bindtap="switchCategory" data-category="earned">
            <text>已获得</text>
        </view>
        <view class="tab-item {{currentCategory === 'locked' ? 'active' : ''}}" bindtap="switchCategory" data-category="locked">
            <text>未获得</text>
        </view>
    </view>
    <!-- 徽章列表 -->
    <view class="badges-container">
        <!-- 最新获得的徽章 -->
        <view class="latest-badge-section" wx:if="{{latestBadge && currentCategory === 'all'}}">
            <view class="section-title">
                <text class="emoji">🎉</text>
                <text>最新获得</text>
            </view>
            <view class="latest-badge-card" bindtap="showBadgeDetail" data-badge="{{latestBadge}}">
                <view class="badge-glow">
                    <view class="badge-icon-large">
                        <text class="badge-emoji">{{latestBadge.icon}}</text>
                    </view>
                </view>
                <view class="badge-info">
                    <text class="badge-title">{{latestBadge.title}}</text>
                    <text class="badge-desc">{{latestBadge.description}}</text>
                    <text class="badge-time">{{latestBadge.earnedTime}}</text>
                </view>
                <view class="share-btn" bindtap="shareBadge" data-badge="{{latestBadge}}" catchtap="stopPropagation">
                    <view class="share-icon">📤</view>
                </view>
            </view>
        </view>
        <!-- 徽章网格 -->
        <view class="badges-grid">
            <view class="badge-item {{item.earned ? 'earned' : 'locked'}}" wx:for="{{filteredBadges}}" wx:key="id" bindtap="showBadgeDetail" data-badge="{{item}}">
                <view class="badge-icon">
                    <text class="badge-emoji {{item.earned ? '' : 'grayscale'}}">
                        {{item.icon}}
                    </text>
                    <view class="lock-overlay" wx:if="{{!item.earned}}">
                        <view class="lock-icon">🔒</view>
                    </view>
                </view>
                <text class="badge-name">{{item.title}}</text>
                <view class="badge-progress" wx:if="{{!item.earned && item.progress}}">
                    <view class="progress-bar">
                        <view class="progress-fill" style="width: {{item.progress.percentage}}%"></view>
                    </view>
                    <text class="progress-text">
                        {{item.progress.current}}/{{item.progress.total}}
                    </text>
                </view>
                <text class="earned-time" wx:if="{{item.earned}}">{{item.earnedTime}}</text>
            </view>
        </view>
        <!-- 空状态组件 -->
        <empty-state show="{{filteredBadges.length === 0}}" type="badges" title="{{currentCategory === 'earned' ? '还没有获得徽章' : '暂无相关徽章'}}" description="{{currentCategory === 'earned' ? '快去打卡赚取积分，解锁更多徽章吧！' : '继续努力，更多徽章等你来解锁！'}}" showAction="{{currentCategory === 'earned'}}" actionText="去打卡" bind:action="goToGrowth"></empty-state>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 徽章详情弹窗 -->
<view class="badge-detail-modal {{showDetailModal ? 'show' : ''}}" bindtap="hideDetailModal">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <view class="badge-icon-detail">
                <text class="badge-emoji-detail {{selectedBadge.earned ? '' : 'grayscale'}}">
                    {{selectedBadge.icon}}
                </text>
                <view class="lock-overlay-detail" wx:if="{{!selectedBadge.earned}}">
                    <view class="lock-icon-detail">🔒</view>
                </view>
            </view>
            <view class="modal-close" bindtap="hideDetailModal">×</view>
        </view>
        <view class="modal-body">
            <text class="badge-title-detail">{{selectedBadge.title}}</text>
            <text class="badge-desc-detail">{{selectedBadge.description}}</text>
            <!-- 已获得状态 -->
            <view class="earned-info" wx:if="{{selectedBadge.earned}}">
                <view class="earned-badge">
                    <text class="earned-icon">✅</text>
                    <text class="earned-text">已获得</text>
                </view>
                <text class="earned-time-detail">获得时间：{{selectedBadge.earnedTime}}</text>
            </view>
            <!-- 未获得状态 -->
            <view class="locked-info" wx:else>
                <view class="locked-badge">
                    <text class="locked-icon">🔒</text>
                    <text class="locked-text">未获得</text>
                </view>
                <view class="unlock-condition">
                    <text class="condition-title">解锁条件：</text>
                    <text class="condition-text">{{selectedBadge.condition}}</text>
                </view>
                <view class="progress-info" wx:if="{{selectedBadge.progress}}">
                    <text class="progress-title">当前进度：</text>
                    <view class="progress-bar-detail">
                        <view class="progress-fill-detail" style="width: {{selectedBadge.progress.percentage}}%"></view>
                    </view>
                    <text class="progress-text-detail">
                        {{selectedBadge.progress.current}}/{{selectedBadge.progress.total}}
            ({{selectedBadge.progress.percentage}}%)
                    </text>
                </view>
            </view>
            <!-- 徽章奖励 -->
            <view class="badge-rewards" wx:if="{{selectedBadge.rewards}}">
                <text class="rewards-title">徽章奖励：</text>
                <view class="rewards-list">
                    <view class="reward-item" wx:for="{{selectedBadge.rewards}}" wx:key="type">
                        <text class="reward-icon">{{item.icon}}</text>
                        <text class="reward-text">{{item.text}}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="modal-footer" wx:if="{{selectedBadge.earned}}">
            <button class="btn btn-outline" bindtap="hideDetailModal">关闭</button>
            <button class="btn btn-primary" bindtap="shareBadge" data-badge="{{selectedBadge}}">
                分享徽章
            </button>
        </view>
    </view>
</view>