// 成就徽章页面
const { childrenActions } = require("../../../utils/index.js");

Page({
  data: {
    // 当前孩子信息
    currentChild: {
      id: 1,
      name: "小明",
      avatar: "", // 使用空字符串，让smart-image组件处理
    },

    // 总积分
    totalPoints: 380,

    // 当前分类
    currentCategory: "all",

    // 所有徽章数据
    allBadges: [],

    // 已获得的徽章
    earnedBadges: [],

    // 过滤后的徽章
    filteredBadges: [],

    // 最新获得的徽章
    latestBadge: null,

    // 徽章详情弹窗
    showDetailModal: false,
    selectedBadge: {},

    // 加载状态
    loading: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadCurrentChild();
    this.loadBadgesData();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.loadBadgesData();
  },

  /**
   * 加载当前孩子信息
   */
  loadCurrentChild() {
    // 从状态管理器获取当前选中的孩子
    const currentChild = childrenActions.getCurrentChild();
    if (currentChild) {
      this.setData({
        currentChild: currentChild,
        totalPoints: currentChild.totalPoints || 380,
      });
    }
  },

  /**
   * 加载徽章数据
   */
  async loadBadgesData() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 模拟API调用
      const badgesData = this.getMockBadgesData();

      // 分离已获得和未获得的徽章
      const earnedBadges = badgesData.filter((badge) => badge.earned);
      const latestBadge =
        earnedBadges.length > 0 ? earnedBadges[earnedBadges.length - 1] : null;

      this.setData({
        allBadges: badgesData,
        earnedBadges: earnedBadges,
        latestBadge: latestBadge,
      });

      // 应用当前分类过滤
      this.filterBadges();
    } catch (error) {
      console.error("加载徽章数据失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取模拟徽章数据
   */
  getMockBadgesData() {
    return [
      {
        id: 1,
        title: "初学者",
        description: "完成第一次跳绳打卡",
        icon: "🌱",
        earned: true,
        earnedTime: "2024-01-15",
        condition: "完成第一次跳绳打卡",
        rewards: [
          { type: "points", icon: "⭐", text: "+10 积分" },
          { type: "title", icon: "🏷️", text: "初学者称号" },
        ],
      },
      {
        id: 2,
        title: "坚持者",
        description: "连续打卡7天",
        icon: "🔥",
        earned: true,
        earnedTime: "2024-01-22",
        condition: "连续打卡7天",
        rewards: [
          { type: "points", icon: "⭐", text: "+50 积分" },
          { type: "multiplier", icon: "✨", text: "积分倍数 x1.2" },
        ],
      },
      {
        id: 3,
        title: "百次达人",
        description: "累计跳绳100次",
        icon: "💯",
        earned: true,
        earnedTime: "2024-02-01",
        condition: "累计跳绳100次",
        rewards: [
          { type: "points", icon: "⭐", text: "+100 积分" },
          { type: "badge", icon: "🏆", text: "专属头像框" },
        ],
      },
      {
        id: 4,
        title: "速度之星",
        description: "单次跳绳超过200次",
        icon: "⚡",
        earned: false,
        condition: "单次跳绳超过200次",
        progress: {
          current: 150,
          total: 200,
          percentage: 75,
        },
        rewards: [
          { type: "points", icon: "⭐", text: "+80 积分" },
          { type: "title", icon: "🏷️", text: "速度之星称号" },
        ],
      },
      {
        id: 5,
        title: "月度冠军",
        description: "单月打卡25天",
        icon: "👑",
        earned: false,
        condition: "单月打卡25天",
        progress: {
          current: 18,
          total: 25,
          percentage: 72,
        },
        rewards: [
          { type: "points", icon: "⭐", text: "+200 积分" },
          { type: "crown", icon: "👑", text: "月度冠军皇冠" },
        ],
      },
      {
        id: 6,
        title: "技巧大师",
        description: "掌握5种跳绳技巧",
        icon: "🎯",
        earned: false,
        condition: "掌握5种跳绳技巧",
        progress: {
          current: 2,
          total: 5,
          percentage: 40,
        },
        rewards: [
          { type: "points", icon: "⭐", text: "+150 积分" },
          { type: "skill", icon: "🎨", text: "解锁花式跳绳" },
        ],
      },
      {
        id: 7,
        title: "分享达人",
        description: "分享打卡视频10次",
        icon: "📱",
        earned: false,
        condition: "分享打卡视频10次",
        progress: {
          current: 6,
          total: 10,
          percentage: 60,
        },
        rewards: [
          { type: "points", icon: "⭐", text: "+60 积分" },
          { type: "social", icon: "👥", text: "社交达人标识" },
        ],
      },
      {
        id: 8,
        title: "年度传奇",
        description: "全年打卡300天",
        icon: "🏆",
        earned: false,
        condition: "全年打卡300天",
        progress: {
          current: 45,
          total: 300,
          percentage: 15,
        },
        rewards: [
          { type: "points", icon: "⭐", text: "+500 积分" },
          { type: "legend", icon: "🌟", text: "传奇称号" },
          { type: "special", icon: "🎁", text: "神秘礼品" },
        ],
      },
    ];
  },

  /**
   * 切换分类
   */
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;
    if (category === this.data.currentCategory) return;

    this.setData({ currentCategory: category });
    this.filterBadges();
  },

  /**
   * 过滤徽章
   */
  filterBadges() {
    const { allBadges, currentCategory } = this.data;
    let filteredBadges = [];

    switch (currentCategory) {
      case "earned":
        filteredBadges = allBadges.filter((badge) => badge.earned);
        break;
      case "locked":
        filteredBadges = allBadges.filter((badge) => !badge.earned);
        break;
      default:
        filteredBadges = allBadges;
        break;
    }

    this.setData({ filteredBadges });
  },

  /**
   * 显示徽章详情
   */
  showBadgeDetail(e) {
    const badge = e.currentTarget.dataset.badge;
    this.setData({
      selectedBadge: badge,
      showDetailModal: true,
    });
  },

  /**
   * 隐藏徽章详情
   */
  hideDetailModal() {
    this.setData({
      showDetailModal: false,
      selectedBadge: {},
    });
  },

  /**
   * 分享徽章
   */
  shareBadge(e) {
    const badge = e.currentTarget.dataset.badge;

    // 生成分享内容
    const shareContent = this.generateShareContent(badge);

    wx.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage", "shareTimeline"],
    });

    wx.showToast({
      title: "准备分享徽章",
      icon: "success",
    });
  },

  /**
   * 生成分享内容
   */
  generateShareContent(badge) {
    return {
      title: `我获得了"${badge.title}"徽章！`,
      desc: badge.description,
      path: `/pages/badges/badges?badgeId=${badge.id}`,
      image_url: "/images/share-badge.png",
    };
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 跳转到成长页面
   */
  goToGrowth() {
    wx.switchTab({
      url: "/pages/growth/growth",
    });
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    const { currentChild, earnedBadges } = this.data;

    return {
      title: `${currentChild.name}已经获得了${earnedBadges.length}个跳绳徽章！`,
      desc: "一起来跳绳，收集更多成就徽章吧！",
      path: "/pages/badges/badges",
      image_url: "/images/share-badges.png",
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { currentChild, earnedBadges } = this.data;

    return {
      title: `${currentChild.name}的跳绳成就：已获得${earnedBadges.length}个徽章！`,
      query: "",
      image_url: "/images/share-timeline-badges.png",
    };
  },
});
