// 排行榜页面 V4版本 - 多维度排行榜
const app = getApp();

Page({
  data: {
    // 当前用户信息
    currentChild: {
      id: 1,
      name: "小明",
      avatar: "👦",
      isEmojiAvatar: true,
      age: 8,
    },

    // 我的排名信息
    myRank: {
      rank: 5,
      value: 285,
      unit: "分",
      trend: 2,
      trendText: "上升2名",
    },

    // 排行榜数据
    leaderboardData: [],
    totalCount: 0,

    // 用户详情弹窗
    showUserDetail: false,
    selectedUser: {},
  },

  onLoad(options) {
    console.log("排行榜页面加载", options);
    this.initPage(options);
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadLeaderboardData();
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    // 加载数据
    this.loadLeaderboardData();

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: "排行榜",
    });
  },

  /**
   * 加载排行榜数据
   */
  loadLeaderboardData() {
    wx.showLoading({
      title: "加载中...",
    });

    // 模拟API调用
    setTimeout(() => {
      const mockData = this.generateMockData();

      this.setData({
        leaderboardData: mockData.list,
        totalCount: mockData.total,
        myRank: mockData.myRank,
      });

      wx.hideLoading();
    }, 800);
  },

  /**
   * 生成模拟数据 - 本周排行榜
   */
  generateMockData() {
    // 基础用户数据
    const baseData = [
      { id: 1, name: "小红", age: 7, avatar: "👧", isEmojiAvatar: true },
      { id: 2, name: "小刚", age: 8, avatar: "👦", isEmojiAvatar: true },
      { id: 3, name: "小丽", age: 6, avatar: "👧", isEmojiAvatar: true },
      {
        id: 4,
        name: "小明",
        age: 8,
        avatar: "👦",
        isEmojiAvatar: true,
        isCurrentChild: true,
      },
      { id: 5, name: "小华", age: 7, avatar: "👦", isEmojiAvatar: true },
      { id: 6, name: "小芳", age: 9, avatar: "👧", isEmojiAvatar: true },
      { id: 7, name: "小强", age: 8, avatar: "👦", isEmojiAvatar: true },
      { id: 8, name: "小美", age: 7, avatar: "👧", isEmojiAvatar: true },
    ];

    // 生成本周排行榜数据
    const list = baseData.map((item, index) => ({
      ...item,
      value: 150 - index * 15,
      unit: "分",
      trend: Math.floor(Math.random() * 5) - 2,
      extra: `${7 - Math.floor(index / 2)}次`,
      totalPoints: 500 - index * 50,
      streakDays: 20 - index,
      bestScore: 120 - index * 5,
    }));

    const myRank = {
      rank: 5,
      value: 90,
      unit: "分",
      trend: 2,
      trendText: "上升2名",
    };

    return {
      list: list,
      total: list.length,
      myRank: myRank,
    };
  },

  /**
   * 分享我的排名
   */
  shareMyRank() {
    const posterData = {
      type: "ranking",
      rankType: "weekly",
      myRank: this.data.myRank,
      currentChild: this.data.currentChild,
      rankingTitle: "排行榜",
    };

    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=ranking&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享排行榜
   */
  shareLeaderboard() {
    wx.showLoading({
      title: "生成海报中...",
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.showActionSheet({
        itemList: ["保存到相册", "分享到朋友圈", "分享给好友"],
        success: (res) => {
          const actions = ["保存成功", "分享到朋友圈", "分享给好友"];
          wx.showToast({
            title: `排行榜${actions[res.tapIndex]}`,
            icon: "success",
          });
        },
      });
    }, 1500);
  },

  /**
   * 查看用户详情
   */
  viewUserDetail(e) {
    const user = e.currentTarget.dataset.user;
    this.setData({
      selectedUser: user,
      showUserDetail: true,
    });
  },

  /**
   * 隐藏用户详情
   */
  hideUserDetail() {
    this.setData({
      showUserDetail: false,
    });
  },

  /**
   * 发起挑战
   */
  challengeUser() {
    const user = this.data.selectedUser;
    wx.showModal({
      title: "发起挑战",
      content: `向${user.name}发起跳绳挑战，比比谁更厉害！`,
      confirmText: "发起",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "挑战已发起",
            icon: "success",
          });
          this.hideUserDetail();
        }
      },
    });
  },

  /**
   * 去打卡
   */
  goToCheckin() {
    wx.navigateTo({
      url: "/pages/demo/checkin-form-v4/checkin-form-v4",
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadLeaderboardData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: "快来看看排行榜，一起来挑战吧！",
      path: "/pages/social/leaderboard/leaderboard?type=weekly",
      image_url: "/images/share-leaderboard.png",
    };
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },
});
