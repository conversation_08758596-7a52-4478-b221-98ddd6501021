/* 排行榜页面样式 */

.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--background-color) 30%);
  padding-bottom: var(--spacing-xxl);
}

/* 排行榜页面样式 */

/* 时间切换标签 */
.time-tabs {
  display: flex;
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-md) 0;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-item text {
  font-size: var(--font-lg);
  color: rgba(255, 255, 255, 0.8);
  font-weight: normal;
  line-height: var(--line-height-sm);
}

.tab-item.active {
  background: white;
}

.tab-item.active text {
  color: var(--primary-color);
  font-weight: normal;
}

/* 我的排名卡片 */
.my-rank-card {
  margin: 0 var(--spacing-lg) var(--spacing-lg);
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.15);
}

.rank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.rank-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.invite-tip {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FFE066, #FF7A45);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
}

.tip-text {
  font-size: var(--font-xs);
  color: white;
  font-weight: 500;
  margin-right: var(--spacing-xs);
}

.tip-icon {
  font-size: var(--font-sm);
}

.rank-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-number {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-sm);
}

.rank-text {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.rank-value {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 var(--spacing-xs);
}

.rank-details {
  display: flex;
  flex-direction: column;
}

.child-name {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.rank-score {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.rank-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.rank-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 6rpx solid var(--primary-color);
}

.rank-badge {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 排行榜列表 */
.leaderboard-list {
  margin: 0 var(--spacing-lg);
}

.list-header {
  margin-bottom: var(--spacing-lg);
}

.list-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.list-subtitle {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 前三名特殊展示 */
.top-three {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin-bottom: var(--spacing-xl);
  padding: 0 var(--spacing-lg);
}

.top-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.top-item.first {
  order: 2;
  margin: 0 var(--spacing-md);
}

.top-item.second {
  order: 1;
}

.top-item.third {
  order: 3;
}

.top-avatar {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.top-item.first .top-avatar {
  width: 140rpx;
  height: 140rpx;
}

.top-item.second .top-avatar,
.top-item.third .top-avatar {
  width: 120rpx;
  height: 120rpx;
}

.top-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 6rpx solid #FFD700;
}

.top-item.second .top-avatar image {
  border-color: #C0C0C0;
}

.top-item.third .top-avatar image {
  border-color: #CD7F32;
}

.top-badge {
  position: absolute;
  bottom: -10rpx;
  right: -10rpx;
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.crown {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-xl);
}

.top-name {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  text-align: center;
}

.top-score {
  font-size: var(--font-sm);
  color: var(--primary-color);
  font-weight: 600;
}

/* 其他排名列表 */
.rank-list {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.rank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank-item.current-child {
  background: linear-gradient(135deg, rgba(255, 122, 69, 0.05), rgba(255, 122, 69, 0.1));
  border-left: 8rpx solid var(--primary-color);
}

.rank-left {
  display: flex;
  align-items: center;
}

.rank-number {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--primary-color);
  width: 80rpx;
  text-align: center;
}

.rank-avatar-small {
  width: 80rpx;
  height: 80rpx;
  margin: 0 var(--spacing-lg);
}

.rank-avatar-small image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid var(--border-color);
}

.rank-info-small {
  display: flex;
  flex-direction: column;
}

.rank-name {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.rank-age {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.rank-right {
  display: flex;
  align-items: center;
}

.rank-score-small {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin-right: var(--spacing-md);
}

.rank-trend {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-icon {
  font-size: var(--font-md);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  text-align: center;
}

.empty-illustration {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: var(--spacing-xl);
}

.empty-illustration image {
  width: 100%;
  height: 100%;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* 邀请弹窗 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.invite-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  margin: var(--spacing-lg);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.invite-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  border-radius: 50%;
  background: var(--background-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.invite-illustration {
  width: 200rpx;
  height: 150rpx;
  margin: 0 auto var(--spacing-lg);
}

.invite-illustration image {
  width: 100%;
  height: 100%;
}

.invite-content {
  text-align: center;
}

.invite-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: block;
}

.invite-description {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  display: block;
}

.invite-benefits {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--radius-md);
}

.benefit-icon {
  font-size: var(--font-lg);
  margin-right: var(--spacing-md);
}

.benefit-text {
  font-size: var(--font-md);
  color: var(--text-primary);
  font-weight: 500;
}

.modal-footer {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + var(--spacing-lg));
}

/* 默认头像样式 */
.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  border-radius: 50%;
  font-size: 60rpx;
  color: var(--text-secondary);
}
