<!-- 排行榜页面 -->
<view class="page-container">
    <!-- 时间切换标签 -->
    <view class="time-tabs">
        <view class="tab-item {{currentTab === 'week' ? 'active' : ''}}" bindtap="switchTab" data-tab="week">
            <text>本周</text>
        </view>
        <view class="tab-item {{currentTab === 'month' ? 'active' : ''}}" bindtap="switchTab" data-tab="month">
            <text>本月</text>
        </view>
    </view>
    <!-- 我的排名卡片 -->
    <view class="my-rank-card">
        <view class="rank-header">
            <text class="rank-title">我的排名</text>
            <view class="invite-tip" bindtap="showInviteModal">
                <text class="tip-text">邀请好友积分翻倍</text>
                <text class="tip-icon">💡</text>
            </view>
        </view>
        <view class="rank-content">
            <view class="rank-info">
                <view class="rank-number">
                    <text class="rank-text">第</text>
                    <text class="rank-value">{{myRank.rank || '--'}}</text>
                    <text class="rank-text">名</text>
                </view>
                <view class="rank-details">
                    <text class="child-name">{{currentChild.name}}</text>
                    <text class="rank-score">{{myRank.score || 0}}分</text>
                </view>
            </view>
            <view class="rank-avatar">
                <view class="default-child-avatar" wx:if="{{!currentChild.avatar || currentChild.isEmojiAvatar}}">
                    {{currentChild.avatar || '👶'}}
                </view>
                <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
                <view class="rank-badge" wx:if="{{myRank.rank <= 3}}">
                    <text wx:if="{{myRank.rank === 1}}">🥇</text>
                    <text wx:elif="{{myRank.rank === 2}}">🥈</text>
                    <text wx:elif="{{myRank.rank === 3}}">🥉</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 排行榜列表 -->
    <view class="leaderboard-list">
        <view class="list-header">
            <text class="list-title">🏆 {{currentTab === 'week' ? '本周' : '本月'}}排行榜</text>
            <text class="list-subtitle">共{{totalCount}}位小朋友参与</text>
        </view>
        <!-- 前三名特殊展示 -->
        <view class="top-three" wx:if="{{leaderboardData.length >= 3}}">
            <!-- 第二名 -->
            <view class="top-item second">
                <view class="top-avatar">
                    <view class="default-child-avatar" wx:if="{{!leaderboardData[1].avatar || leaderboardData[1].isEmojiAvatar}}">
                        {{leaderboardData[1].avatar || '👶'}}
                    </view>
                    <image wx:else src="{{leaderboardData[1].avatar}}" mode="aspectFill"></image>
                    <view class="top-badge">🥈</view>
                </view>
                <text class="top-name">{{leaderboardData[1].name}}</text>
                <text class="top-score">{{leaderboardData[1].score}}分</text>
            </view>
            <!-- 第一名 -->
            <view class="top-item first">
                <view class="top-avatar">
                    <view class="default-child-avatar" wx:if="{{!leaderboardData[0].avatar || leaderboardData[0].isEmojiAvatar}}">
                        {{leaderboardData[0].avatar || '👶'}}
                    </view>
                    <image wx:else src="{{leaderboardData[0].avatar}}" mode="aspectFill"></image>
                    <view class="top-badge">🥇</view>
                    <view class="crown">👑</view>
                </view>
                <text class="top-name">{{leaderboardData[0].name}}</text>
                <text class="top-score">{{leaderboardData[0].score}}分</text>
            </view>
            <!-- 第三名 -->
            <view class="top-item third">
                <view class="top-avatar">
                    <view class="default-child-avatar" wx:if="{{!leaderboardData[2].avatar || leaderboardData[2].isEmojiAvatar}}">
                        {{leaderboardData[2].avatar || '👶'}}
                    </view>
                    <image wx:else src="{{leaderboardData[2].avatar}}" mode="aspectFill"></image>
                    <view class="top-badge">🥉</view>
                </view>
                <text class="top-name">{{leaderboardData[2].name}}</text>
                <text class="top-score">{{leaderboardData[2].score}}分</text>
            </view>
        </view>
        <!-- 其他排名列表 -->
        <view class="rank-list">
            <view class="rank-item {{item.isCurrentChild ? 'current-child' : ''}}" wx:for="{{leaderboardData}}" wx:for-index="index" wx:key="id" wx:if="{{index >= 3}}">
                <view class="rank-left">
                    <text class="rank-number">{{index + 1}}</text>
                    <view class="rank-avatar-small">
                        <view class="default-child-avatar" wx:if="{{!item.avatar || item.isEmojiAvatar}}">
                            {{item.avatar || '👶'}}
                        </view>
                        <image wx:else src="{{item.avatar}}" mode="aspectFill"></image>
                    </view>
                    <view class="rank-info-small">
                        <text class="rank-name">{{item.name}}</text>
                        <text class="rank-age">{{item.age}}岁</text>
                    </view>
                </view>
                <view class="rank-right">
                    <text class="rank-score-small">{{item.score}}分</text>
                    <view class="rank-trend" wx:if="{{item.trend}}">
                        <text class="trend-icon" wx:if="{{item.trend > 0}}">📈</text>
                        <text class="trend-icon" wx:elif="{{item.trend < 0}}">📉</text>
                        <text class="trend-icon" wx:else>➖</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <!-- 空状态组件 -->
    <empty-state show="{{leaderboardData.length === 0}}" type="leaderboard" showAction="{{true}}" actionText="去打卡" bind:action="goToGrowth"></empty-state>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 邀请提示弹窗 -->
<view class="invite-modal {{showInviteModal ? 'show' : ''}}" bindtap="hideInviteModal">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">邀请好友积分翻倍</text>
            <view class="modal-close" bindtap="hideInviteModal">×</view>
        </view>
        <view class="modal-body">
            <view class="invite-illustration">
                <view class="invite-bonus-emoji">🎁</view>
            </view>
            <view class="invite-content">
                <text class="invite-title">💰 邀请好友，积分翻倍！</text>
                <text class="invite-description">邀请好友加入训练营，你们的打卡积分都将翻倍！
          一起努力，一起进步！</text>
                <view class="invite-benefits">
                    <view class="benefit-item">
                        <text class="benefit-icon">🎯</text>
                        <text class="benefit-text">打卡积分翻倍</text>
                    </view>
                    <view class="benefit-item">
                        <text class="benefit-icon">🏆</text>
                        <text class="benefit-text">排行榜冲刺更快</text>
                    </view>
                    <view class="benefit-item">
                        <text class="benefit-icon">👥</text>
                        <text class="benefit-text">和好友一起进步</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="modal-footer">
            <button class="btn btn-primary btn-full" bindtap="goToInvite">立即邀请</button>
        </view>
    </view>
</view>