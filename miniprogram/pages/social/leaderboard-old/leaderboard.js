// 排行榜页面
const { childrenActions } = require("../../../utils/index.js");

// 工具函数：检测是否为emoji头像
function isEmojiAvatar(avatar) {
  if (!avatar) return false;
  // 检测是否为emoji字符（简单检测：长度较短且不包含http/https）
  return (
    avatar.length <= 4 && !avatar.startsWith("http") && !avatar.startsWith("/")
  );
}

Page({
  data: {
    // 当前选中的时间标签
    currentTab: "week",

    // 当前孩子信息
    currentChild: {
      id: 1,
      name: "小明",
      avatar: "👤",
      age: 8,
    },

    // 我的排名信息
    myRank: {
      rank: 5,
      score: 280,
    },

    // 排行榜数据
    leaderboardData: [],

    // 总参与人数
    totalCount: 0,

    // 邀请弹窗显示状态
    showInviteModal: false,

    // 加载状态
    loading: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadCurrentChild();
    this.loadLeaderboardData();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.loadLeaderboardData();
  },

  /**
   * 加载当前孩子信息
   */
  loadCurrentChild() {
    // 从状态管理器获取当前选中的孩子
    const currentChild = childrenActions.getCurrentChild();
    if (currentChild) {
      const childData = { ...currentChild };
      // 标记是否为emoji头像
      childData.isEmojiAvatar = isEmojiAvatar(childData.avatar);
      this.setData({
        currentChild: childData,
      });
    }
  },

  /**
   * 加载排行榜数据
   */
  async loadLeaderboardData() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      // 模拟API调用
      const mockData = this.getMockLeaderboardData();

      // 标记当前孩子和emoji头像
      const processedData = mockData.map((item) => ({
        ...item,
        isCurrentChild: item.id === this.data.currentChild.id,
        isEmojiAvatar: isEmojiAvatar(item.avatar),
      }));

      // 找到当前孩子的排名
      const myRankIndex = processedData.findIndex(
        (item) => item.isCurrentChild
      );
      const myRank =
        myRankIndex >= 0
          ? {
              rank: myRankIndex + 1,
              score: processedData[myRankIndex].score,
            }
          : { rank: null, score: 0 };

      this.setData({
        leaderboardData: processedData,
        totalCount: processedData.length,
        myRank: myRank,
      });
    } catch (error) {
      console.error("加载排行榜数据失败:", error);
      wx.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取模拟排行榜数据
   */
  getMockLeaderboardData() {
    const mockData = [
      {
        id: 2,
        name: "小红",
        age: 7,
        score: 520,
        avatar: "👤",
        trend: 1,
      },
      {
        id: 3,
        name: "小刚",
        age: 9,
        score: 480,
        avatar: "👤",
        trend: -1,
      },
      {
        id: 4,
        name: "小丽",
        age: 8,
        score: 450,
        avatar: "👤",
        trend: 0,
      },
      {
        id: 5,
        name: "小华",
        age: 7,
        score: 420,
        avatar: "👤",
        trend: 1,
      },
      {
        id: 1,
        name: this.data.currentChild.name,
        age: this.data.currentChild.age,
        score: 380,
        avatar: this.data.currentChild.avatar,
        trend: 1,
      },
      {
        id: 6,
        name: "小芳",
        age: 9,
        score: 350,
        avatar: "👤",
        trend: -1,
      },
      {
        id: 7,
        name: "小强",
        age: 8,
        score: 320,
        avatar: "👤",
        trend: 0,
      },
      {
        id: 8,
        name: "小美",
        age: 7,
        score: 290,
        avatar: "👤",
        trend: 1,
      },
      {
        id: 9,
        name: "小亮",
        age: 9,
        score: 260,
        avatar: "👤",
        trend: -1,
      },
      {
        id: 10,
        name: "小雨",
        age: 8,
        score: 230,
        avatar: "👤",
        trend: 0,
      },
    ];

    // 根据当前标签过滤数据（这里简化处理，实际应该调用不同的API）
    return mockData.sort((a, b) => b.score - a.score);
  },

  /**
   * 切换时间标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;

    this.setData({ currentTab: tab });
    this.loadLeaderboardData();
  },

  /**
   * 显示邀请弹窗
   */
  showInviteModal() {
    this.setData({ showInviteModal: true });
  },

  /**
   * 隐藏邀请弹窗
   */
  hideInviteModal() {
    this.setData({ showInviteModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  /**
   * 跳转到成长页面
   */
  goToGrowth() {
    wx.switchTab({
      url: "/pages/growth/growth",
    });
  },

  /**
   * 跳转到邀请页面
   */
  goToInvite() {
    this.hideInviteModal();
    // 这里应该跳转到邀请页面或显示邀请功能
    wx.showToast({
      title: "邀请功能开发中",
      icon: "none",
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadLeaderboardData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },
});
