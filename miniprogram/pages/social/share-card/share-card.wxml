<!-- 分享卡片生成页面 -->
<view class="page-container">
    <!-- 顶部导航 -->
    <view class="header">
        <view class="nav-back" bindtap="goBack">
            <view class="back-icon">←</view>
        </view>
        <text class="page-title">分享卡片</text>
        <view class="nav-placeholder"></view>
    </view>
    <!-- 卡片预览区域 -->
    <view class="card-preview-section">
        <view class="preview-title">
            <text class="emoji">🎨</text>
            <text>卡片预览</text>
        </view>
        <!-- 分享卡片 -->
        <view class="share-card" id="shareCard">
            <!-- 卡片背景 -->
            <view class="card-background" style="background: {{selectedTemplate.background}}">
                <!-- 装饰元素 -->
                <view class="decoration-elements">
                    <view class="decoration decoration-1" wx:if="{{selectedTemplate.decorations}}"></view>
                    <view class="decoration decoration-2" wx:if="{{selectedTemplate.decorations}}"></view>
                    <view class="decoration decoration-3" wx:if="{{selectedTemplate.decorations}}"></view>
                </view>
                <!-- 卡片内容 -->
                <view class="card-content">
                    <!-- 头部信息 -->
                    <view class="card-header">
                        <view class="child-info">
                            <view class="child-avatar">
                                <view class="default-child-avatar" wx:if="{{!currentChild.avatar}}">
                                    👶
                                </view>
                                <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
                            </view>
                            <view class="child-details">
                                <text class="child-name">{{currentChild.name}}</text>
                                <text class="child-age">{{currentChild.age}}岁</text>
                            </view>
                        </view>
                        <view class="app-logo">
                            <smart-image name="app-logo" custom-class="size-medium"></smart-image>
                        </view>
                    </view>
                    <!-- 主要成就 -->
                    <view class="achievement-main">
                        <text class="achievement-title">{{shareData.title}}</text>
                        <text class="achievement-subtitle">{{shareData.subtitle}}</text>
                        <view class="achievement-highlight">
                            <text class="highlight-number">{{shareData.mainNumber}}</text>
                            <text class="highlight-unit">{{shareData.mainUnit}}</text>
                        </view>
                    </view>
                    <!-- 统计数据 -->
                    <view class="stats-grid">
                        <view class="stat-item" wx:for="{{shareData.stats}}" wx:key="label">
                            <text class="stat-number">{{item.value}}</text>
                            <text class="stat-label">{{item.label}}</text>
                        </view>
                    </view>
                    <!-- 底部信息 -->
                    <view class="card-footer">
                        <text class="share-text">{{shareData.shareText}}</text>
                        <view class="qr-code">
                            <view class="qr-code-emoji">📱</view>
                            <text class="qr-text">扫码加入</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <!-- 模板选择 -->
    <view class="template-section">
        <view class="section-title">
            <text class="emoji">🎭</text>
            <text>选择模板</text>
        </view>
        <scroll-view class="template-list" scroll-x="true" show-scrollbar="{{false}}">
            <view class="template-item {{item.id === selectedTemplate.id ? 'active' : ''}}" wx:for="{{templates}}" wx:key="id" bindtap="selectTemplate" data-template="{{item}}">
                <view class="template-preview" style="background: {{item.background}}">
                    <view class="template-name">{{item.name}}</view>
                </view>
            </view>
        </scroll-view>
    </view>
    <!-- 分享文案选择 -->
    <view class="content-section">
        <view class="section-title">
            <text class="emoji">✍️</text>
            <text>分享文案</text>
        </view>
        <view class="content-options">
            <view class="content-item {{item.id === selectedContent.id ? 'active' : ''}}" wx:for="{{contentOptions}}" wx:key="id" bindtap="selectContent" data-content="{{item}}">
                <view class="content-preview">
                    <text class="content-title">{{item.title}}</text>
                    <text class="content-desc">{{item.description}}</text>
                </view>
                <view class="content-check" wx:if="{{item.id === selectedContent.id}}">✓</view>
            </view>
        </view>
    </view>
    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
        <button class="btn btn-outline" bindtap="previewCard">预览卡片</button>
        <button class="btn btn-primary" bindtap="generateCard" disabled="{{generating}}">
            {{generating ? '生成中...' : '生成分享'}}
        </button>
    </view>
    <!-- 底部安全距离 -->
    <view class="safe-area-bottom"></view>
</view>
<!-- 预览弹窗 -->
<view class="preview-modal {{showPreview ? 'show' : ''}}" bindtap="hidePreview">
    <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
            <text class="modal-title">卡片预览</text>
            <view class="modal-close" bindtap="hidePreview">×</view>
        </view>
        <view class="modal-body">
            <view class="preview-card">
                <!-- 这里复制上面的分享卡片结构 -->
                <view class="card-background" style="background: {{selectedTemplate.background}}">
                    <view class="card-content">
                        <view class="card-header">
                            <view class="child-info">
                                <view class="child-avatar">
                                    <image src="{{currentChild.avatar || '/images/default-child-avatar.png'}}" mode="aspectFill"></image>
                                </view>
                                <view class="child-details">
                                    <text class="child-name">{{currentChild.name}}</text>
                                    <text class="child-age">{{currentChild.age}}岁</text>
                                </view>
                            </view>
                            <view class="app-logo">
                                <smart-image name="app-logo-white" custom-class="size-medium"></smart-image>
                            </view>
                        </view>
                        <view class="achievement-main">
                            <text class="achievement-title">{{shareData.title}}</text>
                            <text class="achievement-subtitle">{{shareData.subtitle}}</text>
                            <view class="achievement-highlight">
                                <text class="highlight-number">{{shareData.mainNumber}}</text>
                                <text class="highlight-unit">{{shareData.mainUnit}}</text>
                            </view>
                        </view>
                        <view class="stats-grid">
                            <view class="stat-item" wx:for="{{shareData.stats}}" wx:key="label">
                                <text class="stat-number">{{item.value}}</text>
                                <text class="stat-label">{{item.label}}</text>
                            </view>
                        </view>
                        <view class="card-footer">
                            <text class="share-text">{{shareData.shareText}}</text>
                            <view class="qr-code">
                                <view class="qr-code-emoji">📱</view>
                                <text class="qr-text">扫码加入</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="modal-footer">
            <button class="btn btn-outline" bindtap="hidePreview">关闭</button>
            <button class="btn btn-primary" bindtap="saveToAlbum">保存到相册</button>
        </view>
    </view>
</view>