/* 分享卡片生成页面样式 */

.page-container {
  min-height: 100vh;
  background: var(--background-color);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top));
  background: white;
  border-bottom: 2rpx solid var(--border-color);
}

.nav-back {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--background-color);
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: bold;
}

.page-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.nav-placeholder {
  width: 80rpx;
}

/* 区域标题 */
.section-title, .preview-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child, .preview-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 卡片预览区域 */
.card-preview-section {
  padding: var(--spacing-lg);
}

.share-card {
  width: 100%;
  aspect-ratio: 3/4;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.card-background {
  width: 100%;
  height: 100%;
  position: relative;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
}

/* 装饰元素 */
.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* pointer-events: none; */
}

.decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.decoration-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -100rpx;
}

.decoration-2 {
  width: 150rpx;
  height: 150rpx;
  bottom: 100rpx;
  left: -75rpx;
}

.decoration-3 {
  width: 100rpx;
  height: 100rpx;
  top: 200rpx;
  right: 50rpx;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  color: white;
  position: relative;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}

.child-info {
  display: flex;
  align-items: center;
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: var(--spacing-md);
}

.child-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.8);
}

.child-details {
  display: flex;
  flex-direction: column;
}

.child-name {
  font-size: var(--font-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.child-age {
  font-size: var(--font-md);
  opacity: 0.9;
}

.app-logo {
  width: 120rpx;
  height: 60rpx;
}

.app-logo-text {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 主要成就 */
.achievement-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin: var(--spacing-xl) 0;
}

.achievement-title {
  font-size: var(--font-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  line-height: 1.3;
}

.achievement-subtitle {
  font-size: var(--font-md);
  opacity: 0.9;
  margin-bottom: var(--spacing-xl);
}

.achievement-highlight {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.highlight-number {
  font-size: 120rpx;
  font-weight: 900;
  line-height: 1;
  margin-right: var(--spacing-md);
}

.highlight-unit {
  font-size: var(--font-xl);
  font-weight: 600;
  opacity: 0.9;
}

/* 统计数据 */
.stats-grid {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: var(--font-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-sm);
  opacity: 0.8;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.share-text {
  font-size: var(--font-md);
  font-weight: 500;
  opacity: 0.9;
  flex: 1;
}

.qr-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.qr-code image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: var(--spacing-xs);
}

.qr-text {
  font-size: var(--font-xs);
  opacity: 0.8;
}

/* 模板选择 */
.template-section {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.template-list {
  white-space: nowrap;
}

.template-item {
  display: inline-block;
  margin-right: var(--spacing-md);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.template-item.active {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.template-preview {
  width: 120rpx;
  height: 160rpx;
  display: flex;
  align-items: flex-end;
  padding: var(--spacing-md);
  position: relative;
}

.template-name {
  font-size: var(--font-xs);
  color: white;
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 分享文案选择 */
.content-section {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.content-options {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
  transition: background-color 0.3s ease;
}

.content-item:last-child {
  border-bottom: none;
}

.content-item.active {
  background: rgba(255, 122, 69, 0.1);
}

.content-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.content-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.content-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.content-check {
  width: 48rpx;
  height: 48rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-md);
  font-weight: 600;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: var(--spacing-lg);
  padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
  border-top: 2rpx solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
  z-index: 100;
}

.bottom-actions .btn {
  flex: 1;
  height: 88rpx;
  font-size: var(--font-md);
  font-weight: 600;
}

/* 预览弹窗 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.preview-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  margin: var(--spacing-lg);
  max-width: 500rpx;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.preview-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  border-radius: 50%;
  background: var(--background-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.preview-card {
  width: 100%;
  aspect-ratio: 3/4;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.15);
}

.preview-card .card-background {
  padding: var(--spacing-lg);
}

.preview-card .child-avatar {
  width: 60rpx;
  height: 60rpx;
}

.preview-card .child-name {
  font-size: var(--font-md);
}

.preview-card .child-age {
  font-size: var(--font-sm);
}

.preview-card .app-logo {
  width: 80rpx;
  height: 40rpx;
}

.preview-card .achievement-title {
  font-size: var(--font-lg);
}

.preview-card .achievement-subtitle {
  font-size: var(--font-sm);
}

.preview-card .highlight-number {
  font-size: 80rpx;
}

.preview-card .highlight-unit {
  font-size: var(--font-lg);
}

.preview-card .stat-number {
  font-size: var(--font-md);
}

.preview-card .stat-label {
  font-size: var(--font-xs);
}

.preview-card .share-text {
  font-size: var(--font-sm);
}

.preview-card .qr-code image {
  width: 60rpx;
  height: 60rpx;
}

.preview-card .qr-text {
  font-size: 20rpx;
}

.modal-footer {
  display: flex;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.modal-footer .btn {
  flex: 1;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(120rpx + env(safe-area-inset-bottom));
}
