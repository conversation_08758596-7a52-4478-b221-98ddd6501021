// 分享卡片生成页面
const { childrenActions } = require("../../../utils/index.js");

Page({
  data: {
    // 当前孩子信息
    currentChild: {
      id: 1,
      name: "小明",
      age: 8,
      avatar: "/images/default-child-avatar.png",
    },

    // 分享数据
    shareData: {
      title: "跳绳小达人",
      subtitle: "坚持运动，健康成长",
      mainNumber: "25",
      mainUnit: "天",
      shareText: "一起来跳绳吧！",
      stats: [
        { label: "总打卡", value: "25" },
        { label: "总积分", value: "380" },
        { label: "连续天数", value: "7" },
      ],
    },

    // 模板选项
    templates: [
      {
        id: 1,
        name: "活力橙",
        background: "linear-gradient(135deg, #FF7A45, #FF9A6B)",
        decorations: true,
      },
      {
        id: 2,
        name: "清新蓝",
        background: "linear-gradient(135deg, #4A90E2, #7BB3F0)",
        decorations: true,
      },
      {
        id: 3,
        name: "温暖粉",
        background: "linear-gradient(135deg, #FF6B9D, #FF8FB3)",
        decorations: true,
      },
      {
        id: 4,
        name: "自然绿",
        background: "linear-gradient(135deg, #52C41A, #73D13D)",
        decorations: true,
      },
      {
        id: 5,
        name: "优雅紫",
        background: "linear-gradient(135deg, #722ED1, #9254DE)",
        decorations: true,
      },
    ],

    // 选中的模板
    selectedTemplate: {},

    // 文案选项
    contentOptions: [
      {
        id: 1,
        title: "坚持打卡",
        description: "强调持续运动的重要性",
        template: {
          title: "跳绳小达人",
          subtitle: "坚持运动，健康成长",
          shareText: "一起来跳绳，养成运动好习惯！",
        },
      },
      {
        id: 2,
        title: "成就展示",
        description: "突出孩子的运动成就",
        template: {
          title: "运动小明星",
          subtitle: "用汗水书写成长",
          shareText: "看我的跳绳成就，你也来挑战吧！",
        },
      },
      {
        id: 3,
        title: "邀请好友",
        description: "邀请更多朋友一起运动",
        template: {
          title: "跳绳大挑战",
          subtitle: "和朋友一起运动更有趣",
          shareText: "邀请你一起加入跳绳训练营！",
        },
      },
      {
        id: 4,
        title: "健康生活",
        description: "倡导健康的生活方式",
        template: {
          title: "健康小卫士",
          subtitle: "运动让生活更美好",
          shareText: "每天跳绳，拥抱健康生活！",
        },
      },
    ],

    // 选中的文案
    selectedContent: {},

    // 预览弹窗
    showPreview: false,

    // 生成状态
    generating: false,
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.loadCurrentChild();
    this.initializeDefaults();
    this.generateShareData();
  },

  /**
   * 加载当前孩子信息
   */
  loadCurrentChild() {
    // 从状态管理器获取当前选中的孩子
    const currentChild = childrenActions.getCurrentChild();
    if (currentChild) {
      this.setData({
        currentChild: currentChild,
      });
    }
  },

  /**
   * 初始化默认选项
   */
  initializeDefaults() {
    const defaultTemplate = this.data.templates[0];
    const defaultContent = this.data.contentOptions[0];

    this.setData({
      selectedTemplate: defaultTemplate,
      selectedContent: defaultContent,
    });
  },

  /**
   * 生成分享数据
   */
  generateShareData() {
    const { currentChild, selectedContent } = this.data;

    // 模拟从API获取孩子的统计数据
    const stats = this.getChildStats(currentChild.id);

    // 根据选中的文案模板生成分享数据
    const shareData = {
      ...selectedContent.template,
      mainNumber: stats.totalCheckins.toString(),
      mainUnit: "天",
      stats: [
        { label: "总打卡", value: stats.totalCheckins.toString() },
        { label: "总积分", value: stats.totalPoints.toString() },
        { label: "连续天数", value: stats.currentStreak.toString() },
      ],
    };

    this.setData({ shareData });
  },

  /**
   * 获取孩子统计数据
   */
  getChildStats(childId) {
    // 模拟API调用，实际应该从服务器获取
    return {
      totalCheckins: 25,
      totalPoints: 380,
      currentStreak: 7,
      totalBadges: 3,
      weeklyAverage: 5.2,
    };
  },

  /**
   * 选择模板
   */
  selectTemplate(e) {
    const template = e.currentTarget.dataset.template;
    this.setData({ selectedTemplate: template });
  },

  /**
   * 选择文案
   */
  selectContent(e) {
    const content = e.currentTarget.dataset.content;
    this.setData({ selectedContent: content });

    // 重新生成分享数据
    this.generateShareData();
  },

  /**
   * 预览卡片
   */
  previewCard() {
    this.setData({ showPreview: true });
  },

  /**
   * 隐藏预览
   */
  hidePreview() {
    this.setData({ showPreview: false });
  },

  /**
   * 生成分享卡片
   */
  async generateCard() {
    if (this.data.generating) return;

    this.setData({ generating: true });

    try {
      // 使用canvas生成图片
      const imagePath = await this.generateCardImage();

      // 显示分享选项
      this.showShareOptions(imagePath);
    } catch (error) {
      console.error("生成卡片失败:", error);
      wx.showToast({
        title: "生成失败，请重试",
        icon: "none",
      });
    } finally {
      this.setData({ generating: false });
    }
  },

  /**
   * 生成卡片图片
   */
  generateCardImage() {
    return new Promise((resolve, reject) => {
      // 创建canvas上下文
      const query = wx.createSelectorQuery();
      query
        .select("#shareCard")
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res[0]) {
            reject(new Error("获取canvas失败"));
            return;
          }

          // 模拟生成过程
          setTimeout(() => {
            // 实际项目中这里应该使用canvas绘制卡片
            // 然后调用canvas.toTempFilePath()生成图片
            const tempFilePath = "/temp/share-card.png";
            resolve(tempFilePath);
          }, 2000);
        });
    });
  },

  /**
   * 显示分享选项
   */
  showShareOptions(imagePath) {
    wx.showActionSheet({
      itemList: ["保存到相册", "分享给好友", "分享到朋友圈"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.saveToAlbum(imagePath);
            break;
          case 1:
            this.shareToFriend(imagePath);
            break;
          case 2:
            this.shareToTimeline(imagePath);
            break;
        }
      },
    });
  },

  /**
   * 保存到相册
   */
  async saveToAlbum(imagePath) {
    try {
      // 请求保存权限
      const authResult = await wx.authorize({
        scope: "scope.writePhotosAlbum",
      });

      // 保存图片到相册
      await wx.saveImageToPhotosAlbum({
        filePath: imagePath || "/temp/share-card.png",
      });

      wx.showToast({
        title: "保存成功",
        icon: "success",
      });
    } catch (error) {
      if (error.errMsg.includes("auth deny")) {
        wx.showModal({
          title: "提示",
          content: "需要您授权保存图片到相册",
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          },
        });
      } else {
        wx.showToast({
          title: "保存失败",
          icon: "none",
        });
      }
    }
  },

  /**
   * 分享给好友
   */
  shareToFriend(imagePath) {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage"],
    });

    wx.showToast({
      title: "准备分享给好友",
      icon: "success",
    });
  },

  /**
   * 分享到朋友圈
   */
  shareToTimeline(imagePath) {
    // 触发朋友圈分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ["shareTimeline"],
    });

    wx.showToast({
      title: "准备分享到朋友圈",
      icon: "success",
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 分享给好友
   */
  onShareAppMessage() {
    const { currentChild, shareData } = this.data;

    return {
      title: `${currentChild.name}的跳绳成就卡片`,
      desc: shareData.shareText,
      path: "/pages/share-card/share-card",
      image_url: "/images/share-card-preview.png",
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { currentChild, shareData } = this.data;

    return {
      title: `${currentChild.name}${shareData.title} - ${shareData.shareText}`,
      query: "",
      image_url: "/images/share-timeline-card.png",
    };
  },
});
