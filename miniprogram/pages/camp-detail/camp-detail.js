// Demo版本D：极简版 - 逻辑文件
const contentAPI = require("../../apis/content.js");
const {
  childrenActions,
  withLoginGuardPreset,
  startLoading,
  endLoading,
  isLoading,
} = require("../../utils/index.js");

Page({
  data: {
    campId: null,
    campDetail: null,
    isLoading: false,
    currentChild: null,
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: "让孩子从不会跳到连续100个",
      heroNumber: "0 → 100个",
      heroText: "21天技能飞跃",
      keyBenefits: [
        [
          { icon: "🎯", text: "掌握跳绳技能" },
          { icon: "💪", text: "养成运动习惯" },
        ],
        [
          { icon: "📈", text: "体育成绩提升" },
          { icon: "😊", text: "建立自信心" },
        ],
      ],
      painSolution: '解决"不会教、没方法、没效果"的困扰',
      featuredVideo: {
        id: 1,
        title: "跳绳基础到进阶完整教程",
        desc: "专业教练示范，家长也能教",
        thumbnail: "/images/app-logo.png",
        duration: "完整教程",
      },
      videoList: [
        {
          id: 1,
          title: "基础动作教学",
          subtitle: "正确握绳姿势与站立方法",
          duration: "3:20",
          isPlaying: false,
        },
        {
          id: 2,
          title: "基础动作教学",
          subtitle: "正确握绳姿势与站立方法",
          duration: "3:20",
          isPlaying: false,
        },
        {
          id: 3,
          title: "节奏训练技巧",
          subtitle: "找到适合的跳绳节奏感",
          duration: "4:15",
          isPlaying: true, // 当前正在播放
        },
        {
          id: 4,
          title: "连续跳绳练习",
          subtitle: "从几个到几十个的突破",
          duration: "5:30",
          isPlaying: false,
        },
        {
          id: 5,
          title: "技巧提升训练",
          subtitle: "常见错误纠正与进阶",
          duration: "6:45",
          isPlaying: false,
        },
      ],
      currentVideoId: 3, // 当前选中的视频ID
      steps: [
        {
          icon: "📱",
          text: "看视频练习15分钟",
        },
        {
          icon: "✅",
          text: "打卡记录成长",
        },
      ],
      promises: [{ text: "可补打卡" }, { text: "有指导" }, { text: "有答疑" }],
    },
    canScrollLeft: false,
    canScrollRight: true,
  },

  onLoad: function (options) {
    console.log("训练营详情页加载，参数:", options);

    // 获取训练营ID
    if (options.id) {
      this.setData({
        campId: parseInt(options.id),
      });

      // 加载训练营详情
      this.loadCampDetail();
    } else {
      wx.showToast({
        title: "训练营ID缺失",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }

    // 获取当前选择的孩子
    const currentChild = childrenActions.getCurrentChild();
    this.setData({
      currentChild: currentChild,
    });

    // 初始化滚动状态
    this.checkScrollStatus();
  },

  // 加载训练营详情（直接API调用）
  async loadCampDetail() {
    const loadingKey = "camp-detail";

    if (isLoading(loadingKey)) {
      return;
    }

    try {
      startLoading(loadingKey, {
        title: "加载训练营详情...",
        timeout: 10000,
      });

      console.log("🏕️ 开始加载训练营详情，ID:", this.data.campId);
      console.log("🔧 API配置检查:", {
        baseURL: contentAPI.BASE_URL || "未定义",
        campId: this.data.campId,
        timestamp: new Date().toISOString(),
      });

      // 直接调用API
      const response = await contentAPI.getCampDetail(this.data.campId);

      console.log("✅ 训练营详情加载成功:", response);
      console.log("📊 响应数据详情:", {
        hasData: !!response,
        dataKeys: response ? Object.keys(response) : [],
        title: response?.title,
        subtitle: response?.subtitle,
      });

      if (response && response) {
        // 更新页面数据
        this.setData({
          campDetail: response,
          // 更新任务信息，保持原有的UI结构
          "taskInfo.title": response.title || this.data.taskInfo.title,
          "taskInfo.subtitle": response.subtitle || this.data.taskInfo.subtitle,
          "taskInfo.heroNumber":
            response.hero_number || this.data.taskInfo.heroNumber,
          "taskInfo.heroText":
            response.hero_text || this.data.taskInfo.heroText,
          "taskInfo.keyBenefits": this.parseKeyBenefits(
            response.key_benefits_list || response.key_benefits
          ),
          "taskInfo.painSolution":
            response.pain_solution || this.data.taskInfo.painSolution,
        });

        // 如果有视频集合数据，更新视频列表和特色视频
        if (response.video_collection && response.video_collection.videos) {
          const videos = response.video_collection.videos;
          this.setData({
            "taskInfo.videoList": this.parseVideoList(videos),
          });

          // 更新特色视频（使用第一个视频）
          if (videos.length > 0) {
            const featuredVideo = videos[0];
            this.setData({
              "taskInfo.featuredVideo": {
                id: featuredVideo.id,
                title: featuredVideo.title || "专业教学视频",
                desc:
                  featuredVideo.custom_subtitle ||
                  featuredVideo.description ||
                  "专业教练示范，家长也能教",
                thumbnail: featuredVideo.thumbnail || "/images/app-logo.png",
                duration:
                  this.formatDuration(featuredVideo.duration) || "完整教程",
              },
            });
          }
        }
      }

      endLoading(loadingKey, true);
    } catch (error) {
      console.error("❌ 训练营详情加载失败:", error);
      console.error("🔍 错误详情:", {
        message: error.message,
        statusCode: error.statusCode,
        errorType: typeof error,
        stack: error.stack,
      });

      endLoading(loadingKey, false);

      // 显示更详细的错误信息（开发环境）
      const errorMsg = error.message?.includes("localhost")
        ? "无法连接到API服务器，请确保后端服务已启动"
        : "网络连接失败，请重试";

      wx.showToast({
        title: errorMsg,
        icon: "none",
        duration: 3000,
      });

      // 开发环境下显示更多调试信息
      console.log("🔧 调试信息:", {
        campId: this.data.campId,
        currentData: this.data.taskInfo,
        timestamp: new Date().toISOString(),
      });
    }
  },

  // 解析核心收益点
  parseKeyBenefits(keyBenefitsList) {
    if (!keyBenefitsList || !Array.isArray(keyBenefitsList)) {
      return this.data.taskInfo.keyBenefits;
    }

    // 预定义图标数组
    const icons = ["🎯", "💪", "📈", "😊", "🏆", "⭐", "🎉", "💯"];

    // 将一维数组转换为二维数组，每行2个
    const benefits = [];
    for (let i = 0; i < keyBenefitsList.length; i += 2) {
      const row = [];
      if (keyBenefitsList[i]) {
        row.push({
          icon: icons[i] || "🎯",
          text: keyBenefitsList[i],
        });
      }
      if (keyBenefitsList[i + 1]) {
        row.push({
          icon: icons[i + 1] || "💪",
          text: keyBenefitsList[i + 1],
        });
      }
      if (row.length > 0) {
        benefits.push(row);
      }
    }

    return benefits.length > 0 ? benefits : this.data.taskInfo.keyBenefits;
  },

  // 解析视频列表
  parseVideoList(videos) {
    if (!videos || !Array.isArray(videos)) {
      return this.data.taskInfo.videoList;
    }

    return videos.map((video, index) => ({
      id: video.id || index + 1,
      title: video.title || `视频 ${index + 1}`,
      subtitle: video.custom_subtitle || video.description || "训练视频",
      duration: this.formatDuration(video.duration) || "未知",
      isPlaying: false,
    }));
  },

  // 格式化视频时长（秒转换为分:秒格式）
  formatDuration(seconds) {
    if (!seconds || seconds <= 0) {
      return null;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  },

  onReady: function () {
    // 页面渲染完成后检查滚动状态
    this.checkScrollStatus();

    // 添加调试按钮（开发环境）
    console.log("🔧 页面就绪，当前数据:", {
      campId: this.data.campId,
      campDetail: this.data.campDetail,
      taskInfo: this.data.taskInfo,
    });
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const videoId = e.currentTarget.dataset.videoId;
    let video;

    if (videoId == 1) {
      video = this.data.taskInfo.featuredVideo;
    } else {
      video = this.data.taskInfo.videoList.find((v) => v.id == videoId);
    }

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${
        video.subtitle || video.desc || "专业教学视频"
      }`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 选择视频事件
  onVideoSelect: function (e) {
    const videoId = parseInt(e.currentTarget.dataset.videoId);

    // 更新当前选中的视频ID
    this.setData({
      "taskInfo.currentVideoId": videoId,
    });

    // 更新视频列表中的播放状态
    const videoList = this.data.taskInfo.videoList.map((video) => ({
      ...video,
      isPlaying: video.id === videoId,
    }));

    this.setData({
      "taskInfo.videoList": videoList,
    });

    // 可以在这里添加其他逻辑，比如自动播放等
    console.log("选中视频:", videoId);
  },

  // 视频列表滚动事件
  onVideoListScroll: function (e) {
    const { scrollLeft, scrollWidth } = e.detail;
    const query = wx.createSelectorQuery().in(this);
    query.select(".video-text-list").boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const containerWidth = res[0].width;

        this.setData({
          canScrollLeft: scrollLeft > 10, // 滚动超过10px才显示左侧指示器
          canScrollRight: scrollLeft < scrollWidth - containerWidth - 10, // 距离右边界超过10px才显示右侧指示器
        });
      }
    });
  },

  // 检查滚动状态
  checkScrollStatus: function () {
    setTimeout(() => {
      const query = wx.createSelectorQuery().in(this);
      query.select(".video-text-list").boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          const containerWidth = res[0].width;
          const videoCount = this.data.taskInfo.videoList.length;
          const itemWidth = 260; // 每个视频项的宽度(包含margin)
          const totalWidth = videoCount * itemWidth;

          this.setData({
            canScrollLeft: false, // 初始状态不显示左侧指示器
            canScrollRight: totalWidth > containerWidth, // 只有内容超出容器宽度才显示右侧指示器
          });
        }
      });
    }, 100); // 延迟100ms确保DOM渲染完成
  },

  // 加入任务事件
  async onJoinTask() {
    // 检查是否有选中的孩子
    if (!this.data.currentChild || !this.data.currentChild.id) {
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      return;
    }

    // 检查是否有训练营详情
    if (!this.data.campId) {
      wx.showToast({
        title: "训练营信息缺失",
        icon: "none",
      });
      return;
    }

    const campTitle = this.data.campDetail?.title || this.data.taskInfo.title;

    wx.showModal({
      title: "立即开始挑战",
      content: `${campTitle}\n专业指导，科学训练，见证成长`,
      confirmText: "立即开始",
      cancelText: "再考虑",
      success: async (res) => {
        if (res.confirm) {
          await this.joinCamp();
        }
      },
    });
  },

  // 参与训练营
  async joinCamp() {
    const loadingKey = "join-camp";

    try {
      startLoading(loadingKey, {
        title: "正在参与训练营...",
        timeout: 10000,
      });

      console.log(
        "🏕️ 开始参与训练营，ID:",
        this.data.campId,
        "孩子ID:",
        this.data.currentChild.id
      );

      // 调用API参与训练营
      const response = await contentAPI.joinCamp(
        this.data.campId,
        this.data.currentChild.id
      );

      console.log("✅ 参与训练营成功:", response);

      endLoading(loadingKey, true);

      if (response && response && response.success) {
        // 显示加入成功
        wx.showToast({
          title: "挑战开始！",
          icon: "success",
          duration: 2000,
        });

        // 跳转到成长页面
        setTimeout(() => {
          wx.switchTab({
            url: "/pages/growth/main/main",
          });
        }, 2000);
      } else {
        wx.showToast({
          title: response?.data?.message || "参与失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("❌ 参与训练营失败:", error);
      endLoading(loadingKey, false);

      wx.showToast({
        title: "参与失败，请重试",
        icon: "none",
      });
    }
  },

  // 加入微信训练交流群
  onJoinWechatGroup: function () {
    wx.showModal({
      title: "加入专属训练交流群",
      content:
        "添加微信：jump_helper_2024\n\n备注「21天跳绳」，即可加入专属训练交流群，获得：\n\n🎁 个性化训练计划\n👥 1000+家长交流经验\n⏰ 每日训练提醒\n📊 专业进度分析",
      confirmText: "复制微信号",
      cancelText: "稍后再说",
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: "jump_helper_2024",
            success: () => {
              wx.showToast({
                title: "微信号已复制",
                icon: "success",
                duration: 2000,
              });

              // 提示用户下一步操作
              setTimeout(() => {
                wx.showModal({
                  title: "下一步",
                  content: "请打开微信，添加好友，粘贴微信号并备注「21天跳绳」",
                  showCancel: false,
                  confirmText: "知道了",
                });
              }, 2500);
            },
          });
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "21天跳绳养成计划 - 专业指导，21天见效果",
      desc: "让孩子从不会跳到连续100个，专业教练指导",
      path: "/pages/camp-detail/camp-detail",
      image_url: "/images/share-poster.jpg", // 如果有分享图片的话
    };
  },
});
