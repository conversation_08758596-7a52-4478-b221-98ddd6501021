<!-- Demo版本D：极简版 - 任务详情页面 -->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="task-title">{{taskInfo.title}}</view>
    <view class="task-subtitle">{{taskInfo.subtitle}}</view>
    <view class="header-highlight">
      <text class="highlight-text">专业教练指导 · 科学训练方法</text>
    </view>
  </view>
  <!-- 核心价值区域 (35%) -->
  <view class="core-value-section">
    <view class="value-hero">
      <view class="hero-number">{{taskInfo.heroNumber}}</view>
      <view class="hero-text">{{taskInfo.heroText}}</view>
    </view>
    <view class="key-benefits">
      <view class="benefit-row" wx:for="{{taskInfo.keyBenefits}}" wx:key="index">
        <view class="benefit-simple" wx:for="{{item}}" wx:for-item="benefit" wx:key="text">
          <text class="benefit-icon">{{benefit.icon}}</text>
          <text class="benefit-text">{{benefit.text}}</text>
        </view>
      </view>
    </view>
    <view class="pain-solution">
      <view class="pain-text">{{taskInfo.painSolution}}</view>
    </view>
  </view>
  <!-- 关键视频区域 (20%) -->
  <view class="video-section-minimal">
    <!-- <view class="section-title-minimal">
      <text class="icon">📺</text>
      <text>专业教学视频</text>
    </view> -->
    <view class="featured-video-minimal" bindtap="onVideoTap" data-video-id="{{taskInfo.featuredVideo.id}}">
      <view class="video-thumbnail-featured">
        <image src="{{taskInfo.featuredVideo.thumbnail}}" mode="aspectFill"></image>
        <view class="play-icon-featured">▶️</view>
        <view class="video-duration">{{taskInfo.featuredVideo.duration}}</view>
      </view>
      <view class="video-info-featured">
        <view class="video-title-featured">{{taskInfo.featuredVideo.title}}</view>
        <view class="video-desc-featured">{{taskInfo.featuredVideo.desc}}</view>
      </view>
    </view>
    <!-- 文字列表形式的视频列表 -->
    <view class="video-text-list-container">
      <view class="video-scroll-wrapper">
        <view class="scroll-indicator left" wx:if="{{canScrollLeft}}">‹</view>
        <scroll-view class="video-text-list" scroll-x="true" show-scrollbar="false" bindscroll="onVideoListScroll">
          <view class="video-text-item {{item.isPlaying ? 'playing' : ''}}" wx:for="{{taskInfo.videoList}}" wx:key="id" bindtap="onVideoSelect" data-video-id="{{item.id}}">
            <view class="video-line-1">
              <text class="playing-icon" wx:if="{{item.isPlaying}}">🎵</text>
              <text class="video-number">{{index + 1 < 10 ? '0' + (index + 1) : index + 1}}</text>
              <text class="video-title">{{item.title}}</text>
            </view>
            <view class="video-line-2">
              <text class="video-separator">-</text>
              <text class="video-subtitle">{{item.subtitle}}</text>
            </view>
          </view>
        </scroll-view>
        <view class="scroll-indicator right" wx:if="{{canScrollRight}}">›</view>
      </view>
      <!-- <view class="video-indicator">
        <text class="icon">📚</text>
        <text>{{taskInfo.videoList.length}}个系统教学视频，循序渐进</text>
      </view> -->
    </view>
  </view>
  <!-- 简化操作说明区域 (30%) -->
  <view class="simple-operation-section">
    <view class="section-title-minimal">
      <text class="icon">⚡</text>
      <text>超简单，每天2步，20分钟</text>
    </view>
    <view class="steps-minimal">
      <view class="step-minimal">
        <view class="step-icon-large">📱</view>
        <view class="step-text-large">看视频练习15分钟</view>
      </view>
      <view class="step-arrow-large">→</view>
      <view class="step-minimal">
        <view class="step-icon-large">✅</view>
        <view class="step-text-large">打卡记录成长</view>
      </view>
    </view>
    <view class="time-promise">
      <!-- <view class="promise-highlight">每天仅需20分钟</view> -->
      <view class="promise-details">
        <text class="promise-item">✓ 可补打卡</text>
        <text class="promise-item">✓ 有指导</text>
        <text class="promise-item">✓ 有答疑</text>
      </view>
    </view>
    <view class="guarantee-simple">
      <text class="icon">🛡️</text>
      <text>完全免费，随时可退，专业指导</text>
    </view>
  </view>
  <!-- 社交功能按钮 - 极简版 -->
  <view class="social-buttons-minimal">
    <button class="social-btn share-btn" open-type="share">
      <text class="btn-icon">📤</text>
      <text>分享好友</text>
    </button>
    <button class="social-btn wechat-btn" bindtap="onJoinWechatGroup">
      <text class="btn-icon">💬</text>
      <text>加入专属训练交流群</text>
    </button>
  </view>
  <!-- 大行动按钮区域 (15%) -->
  <view class="action-section-large">
    <view class="urgency-banner">
      <text class="urgency-icon">🔥</text>
      <text class="urgency-text">限时免费，立即开始</text>
    </view>
    <button class="join-button-large" bindtap="onJoinTask">立即参加挑战</button>
    <view class="final-promise">
      <text>专业教练指导 · 科学训练方法 · 快速见效果</text>
    </view>
  </view>
</view>