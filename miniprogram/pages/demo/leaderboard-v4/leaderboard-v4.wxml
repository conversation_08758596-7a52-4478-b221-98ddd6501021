<!-- 排行榜页面 V4版本 - 多维度排行榜 -->
<view class="page-container">
  <!-- 排行榜标题 -->
  <view class="ranking-header">
    <view class="header-icon">📅</view>
    <view class="header-info">
      <text class="header-title">排行榜</text>
      <text class="header-subtitle">本周积分排名</text>
    </view>
  </view>
  <!-- 我的排名卡片 -->
  <view class="my-rank-card">
    <view class="rank-header">
      <text class="rank-title">我的排名</text>
      <view class="rank-type-info">
        <text class="type-desc">本周获得的积分</text>
      </view>
    </view>
    <view class="rank-content">
      <view class="rank-info">
        <view class="rank-number">
          <text class="rank-text">第</text>
          <text class="rank-value">{{myRank.rank || '--'}}</text>
          <text class="rank-text">名</text>
        </view>
        <view class="rank-details">
          <text class="child-name">{{currentChild.name}}</text>
          <text class="rank-score">{{myRank.value || 0}}{{myRank.unit}}</text>
          <view class="rank-trend" wx:if="{{myRank.trend !== undefined}}">
            <text class="trend-icon" wx:if="{{myRank.trend > 0}}">📈</text>
            <text class="trend-icon" wx:elif="{{myRank.trend < 0}}">📉</text>
            <text class="trend-icon" wx:else>➖</text>
            <text class="trend-text">{{myRank.trendText}}</text>
          </view>
        </view>
      </view>
      <view class="rank-avatar">
        <view class="default-child-avatar" wx:if="{{!currentChild.avatar || currentChild.isEmojiAvatar}}">
          {{currentChild.avatar || '👶'}}
        </view>
        <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
        <view class="rank-badge" wx:if="{{myRank.rank <= 3}}">
          <text wx:if="{{myRank.rank === 1}}">🥇</text>
          <text wx:elif="{{myRank.rank === 2}}">🥈</text>
          <text wx:elif="{{myRank.rank === 3}}">🥉</text>
        </view>
      </view>
    </view>
    <!-- 分享按钮 -->
    <view class="rank-actions">
      <button class="share-rank-btn" bindtap="shareMyRank">
        <text class="share-icon">📸</text>
        <text class="share-text">分享我的排名</text>
      </button>
    </view>
  </view>
  <!-- 排行榜列表 -->
  <view class="leaderboard-list">
    <view class="list-header">
      <text class="list-title">🏆 排行榜</text>
      <text class="list-subtitle">共{{totalCount}}位小朋友参与</text>
      <button class="share-list-btn" bindtap="shareLeaderboard">
        <text class="share-icon">📊</text>
        <text class="share-text">分享榜单</text>
      </button>
    </view>
    <!-- 前三名特殊展示 -->
    <view class="top-three" wx:if="{{leaderboardData.length >= 3}}">
      <!-- 第二名 -->
      <view class="top-item second" bindtap="viewUserDetail" data-user="{{leaderboardData[1]}}">
        <view class="top-avatar">
          <view class="default-child-avatar" wx:if="{{!leaderboardData[1].avatar || leaderboardData[1].isEmojiAvatar}}">
            {{leaderboardData[1].avatar || '👶'}}
          </view>
          <image wx:else src="{{leaderboardData[1].avatar}}" mode="aspectFill"></image>
          <view class="top-badge">🥈</view>
        </view>
        <text class="top-name">{{leaderboardData[1].name}}</text>
        <text class="top-score">{{leaderboardData[1].value}}{{leaderboardData[1].unit}}</text>
        <view class="top-extra" wx:if="{{leaderboardData[1].extra}}">
          <text class="extra-text">{{leaderboardData[1].extra}}</text>
        </view>
      </view>
      <!-- 第一名 -->
      <view class="top-item first" bindtap="viewUserDetail" data-user="{{leaderboardData[0]}}">
        <view class="top-avatar">
          <view class="default-child-avatar" wx:if="{{!leaderboardData[0].avatar || leaderboardData[0].isEmojiAvatar}}">
            {{leaderboardData[0].avatar || '👶'}}
          </view>
          <image wx:else src="{{leaderboardData[0].avatar}}" mode="aspectFill"></image>
          <view class="top-badge">🥇</view>
          <view class="crown">👑</view>
        </view>
        <text class="top-name">{{leaderboardData[0].name}}</text>
        <text class="top-score">{{leaderboardData[0].value}}{{leaderboardData[0].unit}}</text>
        <view class="top-extra" wx:if="{{leaderboardData[0].extra}}">
          <text class="extra-text">{{leaderboardData[0].extra}}</text>
        </view>
      </view>
      <!-- 第三名 -->
      <view class="top-item third" bindtap="viewUserDetail" data-user="{{leaderboardData[2]}}">
        <view class="top-avatar">
          <view class="default-child-avatar" wx:if="{{!leaderboardData[2].avatar || leaderboardData[2].isEmojiAvatar}}">
            {{leaderboardData[2].avatar || '👶'}}
          </view>
          <image wx:else src="{{leaderboardData[2].avatar}}" mode="aspectFill"></image>
          <view class="top-badge">🥉</view>
        </view>
        <text class="top-name">{{leaderboardData[2].name}}</text>
        <text class="top-score">{{leaderboardData[2].value}}{{leaderboardData[2].unit}}</text>
        <view class="top-extra" wx:if="{{leaderboardData[2].extra}}">
          <text class="extra-text">{{leaderboardData[2].extra}}</text>
        </view>
      </view>
    </view>
    <!-- 其他排名列表 -->
    <view class="rank-list">
      <view class="rank-item {{item.isCurrentChild ? 'current-child' : ''}}" wx:for="{{leaderboardData}}" wx:for-index="index" wx:key="id" wx:if="{{index >= 3}}" bindtap="viewUserDetail" data-user="{{item}}">
        <view class="rank-left">
          <text class="rank-number">{{index + 1}}</text>
          <view class="rank-avatar-small">
            <view class="default-child-avatar" wx:if="{{!item.avatar || item.isEmojiAvatar}}">
              {{item.avatar || '👶'}}
            </view>
            <image wx:else src="{{item.avatar}}" mode="aspectFill"></image>
          </view>
          <view class="rank-info-small">
            <text class="rank-name">{{item.name}}</text>
            <text class="rank-age">{{item.age}}岁</text>
          </view>
        </view>
        <view class="rank-right">
          <text class="rank-score-small">{{item.value}}{{item.unit}}</text>
          <view class="rank-trend" wx:if="{{item.trend !== undefined}}">
            <text class="trend-icon" wx:if="{{item.trend > 0}}">📈</text>
            <text class="trend-icon" wx:elif="{{item.trend < 0}}">📉</text>
            <text class="trend-icon" wx:else>➖</text>
          </view>
          <view class="rank-extra" wx:if="{{item.extra}}">
            <text class="extra-text">{{item.extra}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 排行榜说明 -->
  <view class="ranking-info">
    <view class="info-card">
      <text class="info-title">📋 排行榜说明</text>
      <text class="info-content">根据本周获得的积分进行排名，每周一重置。包括打卡积分、任务奖励、勋章奖励等。</text>
    </view>
  </view>
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{leaderboardData.length === 0}}">
    <view class="empty-icon">📊</view>
    <text class="empty-title">暂无排行数据</text>
    <text class="empty-desc">快去打卡，成为第一名吧！</text>
    <button class="empty-action-btn" bindtap="goToCheckin">立即打卡</button>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
<!-- 用户详情弹窗 -->
<view class="user-detail-modal {{showUserDetail ? 'show' : ''}}" bindtap="hideUserDetail">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{selectedUser.name}}的成绩</text>
      <view class="modal-close" bindtap="hideUserDetail">×</view>
    </view>
    <view class="modal-body">
      <view class="user-avatar-large">
        <view class="default-child-avatar" wx:if="{{!selectedUser.avatar || selectedUser.isEmojiAvatar}}">
          {{selectedUser.avatar || '👶'}}
        </view>
        <image wx:else src="{{selectedUser.avatar}}" mode="aspectFill"></image>
      </view>
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-label">总积分</text>
          <text class="stat-value">{{selectedUser.totalPoints}}分</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">连续天数</text>
          <text class="stat-value">{{selectedUser.streakDays}}天</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">最佳成绩</text>
          <text class="stat-value">{{selectedUser.bestScore}}个/分钟</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideUserDetail">关闭</button>
      <button class="btn btn-primary" bindtap="challengeUser">发起挑战</button>
    </view>
  </view>
</view>