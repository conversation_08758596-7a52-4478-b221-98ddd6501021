/* 排行榜页面 V4版本样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 0 32rpx 120rpx;
}

/* 排行榜标题 */
.ranking-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
}

.header-icon {
  font-size: 48rpx;
}

.header-info {
  flex: 1;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 我的排名卡片 */
.my-rank-card {
  background: linear-gradient(135deg, #4A90E2, #5BA0F2);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
}

.rank-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.rank-title {
  font-size: 28rpx;
  font-weight: 600;
}

.rank-type-info {
  
}

.type-desc {
  font-size: 20rpx;
  opacity: 0.9;
}

.rank-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.rank-info {
  flex: 1;
}

.rank-number {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  margin-bottom: 12rpx;
}

.rank-text {
  font-size: 24rpx;
}

.rank-value {
  font-size: 48rpx;
  font-weight: 700;
}

.rank-details {
  
}

.child-name {
  font-size: 26rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 4rpx;
}

.rank-score {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

.rank-trend {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.trend-icon {
  font-size: 16rpx;
}

.trend-text {
  font-size: 20rpx;
  opacity: 0.9;
}

.rank-avatar {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  background: linear-gradient(135deg, #FFE0B2, #FFCC80);
}

.rank-avatar image {
  width: 100%;
  height: 100%;
}

.rank-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.rank-actions {
  text-align: center;
}

.share-rank-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.share-rank-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.share-icon {
  font-size: 20rpx;
}

.share-text {
  font-size: 22rpx;
}

/* 排行榜列表 */
.leaderboard-list {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  flex-wrap: wrap;
  gap: 16rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.list-subtitle {
  font-size: 24rpx;
  color: #666666;
}

.share-list-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.share-list-btn:active {
  background-color: #357ABD;
  transform: scale(0.95);
}

/* 前三名展示 */
.top-three {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 24rpx;
  margin-bottom: 48rpx;
  padding: 32rpx 0;
}

.top-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.top-item:active {
  transform: scale(0.95);
}

.top-item.first {
  order: 2;
}

.top-item.second {
  order: 1;
}

.top-item.third {
  order: 3;
}

.top-avatar {
  position: relative;
  margin-bottom: 16rpx;
}

.top-item.first .top-avatar {
  width: 120rpx;
  height: 120rpx;
}

.top-item.second .top-avatar,
.top-item.third .top-avatar {
  width: 100rpx;
  height: 100rpx;
}

.top-avatar .default-child-avatar,
.top-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.top-avatar .default-child-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FFE0B2, #FFCC80);
}

.top-item.first .default-child-avatar {
  font-size: 64rpx;
}

.top-item.second .default-child-avatar,
.top-item.third .default-child-avatar {
  font-size: 48rpx;
}

.top-badge {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.crown {
  position: absolute;
  top: -16rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
}

.top-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.top-score {
  font-size: 28rpx;
  font-weight: 700;
  color: #FF7A45;
  margin-bottom: 4rpx;
}

.top-extra {
  
}

.extra-text {
  font-size: 20rpx;
  color: #999999;
}

/* 其他排名列表 */
.rank-list {
  
}

.rank-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
  transition: all 0.3s ease;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank-item:active {
  background-color: #F7F8FA;
  transform: scale(0.98);
}

.rank-item.current-child {
  background: linear-gradient(135deg, #FFF2ED, #FFFFFF);
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  border: 2rpx solid #FF7A45;
  margin: 8rpx 0;
}

.rank-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.rank-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  min-width: 48rpx;
  text-align: center;
}

.rank-avatar-small {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  overflow: hidden;
}

.rank-avatar-small .default-child-avatar,
.rank-avatar-small image {
  width: 100%;
  height: 100%;
}

.rank-avatar-small .default-child-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: linear-gradient(135deg, #FFE0B2, #FFCC80);
}

.rank-info-small {
  
}

.rank-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.rank-age {
  font-size: 20rpx;
  color: #999999;
}

.rank-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.rank-score-small {
  font-size: 26rpx;
  font-weight: 600;
  color: #FF7A45;
}

.rank-extra {
  
}

/* 排行榜说明 */
.ranking-info {
  margin-bottom: 32rpx;
}

.info-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.info-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.info-content {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
  display: block;
}

.empty-action-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.empty-action-btn:active {
  transform: scale(0.95);
}

/* 用户详情弹窗 */
.user-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.user-detail-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: #F0F0F0;
}

.modal-body {
  padding: 32rpx;
  text-align: center;
}

.user-avatar-large {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 32rpx;
  border: 4rpx solid #F0F0F0;
}

.user-avatar-large .default-child-avatar,
.user-avatar-large image {
  width: 100%;
  height: 100%;
}

.user-avatar-large .default-child-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  background: linear-gradient(135deg, #FFE0B2, #FFCC80);
}

.user-stats {
  display: flex;
  gap: 32rpx;
  justify-content: center;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 22rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF7A45;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.modal-footer .btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn.btn-outline {
  background-color: #F7F8FA;
  color: #666666;
  border: 1rpx solid #E5E5E5;
}

.btn.btn-outline:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.btn.btn-primary {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.btn.btn-primary:active {
  transform: scale(0.98);
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 120rpx;
}
