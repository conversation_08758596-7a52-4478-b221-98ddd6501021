// Demo页面总导航
Page({
  data: {},

  onLoad: function (options) {
    console.log("Demo总导航页面加载");
  },

  /**
   * 导航到指定Demo页面
   */
  navigateToDemo: function (e) {
    const path = e.currentTarget.dataset.path;
    console.log("准备跳转到:", path);

    wx.navigateTo({
      url: path,
      success: () => {
        console.log("成功导航到Demo页面:", path);
      },
      fail: (err) => {
        console.error("导航失败:", err);
        wx.showToast({
          title: "页面跳转失败",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function () {
    return {
      title: "功能设计方案展示 - 训练营相关功能",
      path: "/pages/demo/demo-index/demo-index",
    };
  },
});
