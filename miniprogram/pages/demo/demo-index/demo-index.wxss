/* Demo页面总导航样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding-bottom: var(--spacing-lg);
}

.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #E6692D 100%);
  padding: var(--spacing-xl) var(--spacing-lg);
  padding-top: calc(var(--spacing-xl) + env(safe-area-inset-top));
  text-align: center;
  color: white;
  margin-bottom: var(--spacing-lg);
}

.title {
  font-size: var(--font-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.subtitle {
  font-size: var(--font-md);
  opacity: 0.9;
}

/* 分区样式 */
.section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.section-icon {
  font-size: var(--font-xl);
  margin-right: var(--spacing-sm);
}

.section-name {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.demo-list {
  padding: 0 var(--spacing-lg);
}

.demo-item {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.demo-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.demo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.demo-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.demo-tag {
  color: #FFFFFF;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-xs);
  font-weight: 600;
}

.demo-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
  background-color: #F8F9FA;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

.demo-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.feature {
  font-size: var(--font-xs);
  color: var(--text-primary);
  background-color: #FFF2E8;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  border: 1rpx solid #FFD6CC;
}

.footer {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.note {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  background-color: #FFFFFF;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  line-height: 1.5;
}
