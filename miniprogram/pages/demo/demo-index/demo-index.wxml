<!-- Demo页面总导航 -->
<view class="page-container">
    <view class="header">
        <view class="title">功能设计方案展示</view>
        <view class="subtitle">训练营相关功能的多种设计方案对比</view>
    </view>
    <!-- 成长页面方案 -->
    <view class="section">
        <view class="section-title">
            <text class="section-icon">🌱</text>
            <text class="section-name">成长页面功能</text>
        </view>
        <view class="demo-list">
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/growth-v4-main/growth-v4-main">
                <view class="demo-header">
                    <view class="demo-title">成长主页重构版 V4</view>
                    <view class="demo-tag" style="background: #FF6B35;">最新</view>
                </view>
                <view class="demo-desc">基于V2-A简洁任务导向型重构，家庭荣誉契约与任务合并，多维度排行榜设计</view>
                <view class="demo-features">
                    <text class="feature">✓ 契约任务合并</text>
                    <text class="feature">✓ 多维度排行榜</text>
                    <text class="feature">✓ 卡片式布局</text>
                </view>
            </view>
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/growth-v2-index/growth-v2-index">
                <view class="demo-header">
                    <view class="demo-title">成长栏目重构方案 V2</view>
                    <view class="demo-tag" style="background: #FF6B35;">完整</view>
                </view>
                <view class="demo-desc">4套完整的成长栏目设计方案，深度融入家庭荣誉契约功能</view>
                <view class="demo-features">
                    <text class="feature">✓ 家庭荣誉契约</text>
                    <text class="feature">✓ 完整页面体系</text>
                    <text class="feature">✓ 4种设计风格</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 任务详情页面方案 -->
    <view class="section">
        <view class="section-title">
            <text class="section-icon">📋</text>
            <text class="section-name">任务详情页面</text>
        </view>
        <view class="demo-list">
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/task-detail-demo-b/task-detail-demo-b">
                <view class="demo-header">
                    <view class="demo-title">版本B：价值主张优先型</view>
                    <view class="demo-tag" style="background: #FF7A45;">推荐</view>
                </view>
                <view class="demo-desc">详细价值主张(40%) + 视频展示(25%) + 操作说明(20%) + 行动按钮(15%)</view>
                <view class="demo-features">
                    <text class="feature">✓ 突出孩子能力提升</text>
                    <text class="feature">✓ 视频作为支撑</text>
                    <text class="feature">✓ 橙色主题</text>
                </view>
            </view>
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/task-detail-demo-a/task-detail-demo-a">
                <view class="demo-header">
                    <view class="demo-title">版本A：视频重点突出型</view>
                    <view class="demo-tag" style="background: #4A90E2;">专业</view>
                </view>
                <view class="demo-desc">简短价值主张(20%) + 视频展示(40%) + 操作说明(25%) + 行动按钮(15%)</view>
                <view class="demo-features">
                    <text class="feature">✓ 视频作为主要卖点</text>
                    <text class="feature">✓ 突出专业教练</text>
                    <text class="feature">✓ 蓝色主题</text>
                </view>
            </view>
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/task-detail-demo-c/task-detail-demo-c">
                <view class="demo-header">
                    <view class="demo-title">版本C：平衡展示型</view>
                    <view class="demo-tag" style="background: #6A4C93;">全面</view>
                </view>
                <view class="demo-desc">价值主张(30%) + 视频展示(30%) + 操作说明(25%) + 行动按钮(15%)</view>
                <view class="demo-features">
                    <text class="feature">✓ 各元素平衡展示</text>
                    <text class="feature">✓ 信息全面</text>
                    <text class="feature">✓ 紫色主题</text>
                </view>
            </view>
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/task-detail-demo-d/task-detail-demo-d">
                <view class="demo-header">
                    <view class="demo-title">版本D：极简版（优化版）</view>
                    <view class="demo-tag" style="background: #FF6B35;">最新</view>
                </view>
                <view class="demo-desc">核心价值(35%) + 关键视频(20%) + 简化操作说明(30%) + 大行动按钮(15%)</view>
                <view class="demo-features">
                    <text class="feature">✓ 视频可滑动</text>
                    <text class="feature">✓ 步骤简化为2步</text>
                    <text class="feature">✓ 橙红色主题</text>
                </view>
            </view>
        </view>
    </view>
    <!-- 测试工具 -->
    <view class="section">
        <view class="section-title">
            <text class="section-icon">🔧</text>
            <text class="section-name">测试工具</text>
        </view>
        <view class="demo-list">
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/checkin-test/checkin-test">
                <view class="demo-header">
                    <view class="demo-title">打卡功能测试</view>
                    <view class="demo-tag" style="background: #17A2B8;">测试</view>
                </view>
                <view class="demo-desc">快速测试所有打卡功能页面，检查页面跳转和功能</view>
                <view class="demo-features">
                    <text class="feature">✓ 快速测试</text>
                    <text class="feature">✓ 错误检查</text>
                    <text class="feature">✓ 功能验证</text>
                </view>
            </view>
            <view class="demo-item" bindtap="navigateToDemo" data-path="/pages/demo/image-test/image-test">
                <view class="demo-header">
                    <view class="demo-title">图片路径测试</view>
                    <view class="demo-tag" style="background: #6C757D;">工具</view>
                </view>
                <view class="demo-desc">验证所有demo页面使用的图片资源是否能正常加载</view>
                <view class="demo-features">
                    <text class="feature">✓ 图片验证</text>
                    <text class="feature">✓ 路径检查</text>
                    <text class="feature">✓ 加载状态</text>
                </view>
            </view>
        </view>
    </view>
    <view class="footer">
        <view class="note">💡 点击任意版本查看详细效果</view>
        <view class="note">🎯 每个版本都有完整的交互演示</view>
        <view class="note">🔧 测试工具可以帮助验证功能正常性</view>
    </view>
</view>