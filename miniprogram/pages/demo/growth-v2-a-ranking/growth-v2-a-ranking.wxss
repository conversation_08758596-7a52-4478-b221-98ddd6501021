/* 方案A - 排行榜页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部训练营信息 */
.camp-header {
  background-color: #FFFFFF;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5E5;
}

.camp-info {
  flex: 1;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.camp-stats {
  text-align: right;
}

.stats-text {
  font-size: 24rpx;
  color: #999999;
}

/* 我的排名区块 */
.my-ranking-section {
  padding: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.my-ranking-card {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 16rpx;
  padding: 32rpx;
  color: #FFFFFF;
}

.ranking-info {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.rank-number {
  margin-right: 24rpx;
}

.rank-text {
  font-size: 36rpx;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 3rpx solid #FFFFFF;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.user-score {
  font-size: 28rpx;
  opacity: 0.9;
}

.ranking-stats {
  display: flex;
  gap: 48rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 排行榜类型切换 */
.ranking-tabs {
  display: flex;
  background-color: #FFFFFF;
  margin: 0 32rpx;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #FF7A45;
  color: #FFFFFF;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 排行榜列表 */
.ranking-list-section {
  padding: 32rpx;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.ranking-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.ranking-item.top-three {
  border: 2rpx solid #FFD700;
  background: linear-gradient(135deg, #FFFBE6, #FFFFFF);
}

.rank-badge {
  width: 60rpx;
  text-align: center;
  margin-right: 24rpx;
}

.rank-icon {
  font-size: 48rpx;
}

.rank-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #666666;
}

.ranking-item .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #E5E5E5;
}

.ranking-item .user-info {
  flex: 1;
}

.ranking-item .user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #666666;
}

.ranking-item .user-score {
  text-align: right;
}

.score-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 4rpx;
}

.score-label {
  font-size: 24rpx;
  color: #666666;
}

/* 契约排行项目 */
.contract-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border-left: 6rpx solid #FFD700;
}

.contract-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contract-item .user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.contract-item .user-info {
  flex: 1;
}

.contract-item .user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.contract-title {
  font-size: 24rpx;
  color: #666666;
}

.contract-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.contract-status.active {
  background-color: #FFF2ED;
  color: #FF7A45;
}

.contract-status.completed {
  background-color: #E8F5E8;
  color: #52C41A;
}

.contract-status.failed {
  background-color: #FFF2F0;
  color: #FF4D4F;
}

.contract-progress {
  margin-bottom: 16rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

.progress-percent {
  font-size: 24rpx;
  color: #FF7A45;
  font-weight: 600;
}

.progress-bar {
  height: 6rpx;
  background-color: #E5E5E5;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.contract-reward {
  display: flex;
  align-items: center;
}

.reward-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 激励信息 */
.motivation-section {
  padding: 0 32rpx 32rpx;
}

.motivation-card {
  background: linear-gradient(135deg, #4A90E2, #6BA3E8);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  color: #FFFFFF;
}

.motivation-icon {
  font-size: 64rpx;
  margin-right: 24rpx;
}

.motivation-content {
  flex: 1;
}

.motivation-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.motivation-text {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
