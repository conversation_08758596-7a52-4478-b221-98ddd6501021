<!-- 方案A - 排行榜页面 -->
<view class="page-container">
  <!-- 顶部训练营信息 -->
  <view class="camp-header">
    <view class="camp-info">
      <text class="camp-title">{{campInfo.title}}</text>
      <text class="camp-subtitle">今日排行榜</text>
    </view>
    <view class="camp-stats">
      <text class="stats-text">共 {{totalParticipants}} 人参与</text>
    </view>
  </view>

  <!-- 我的排名 -->
  <view class="my-ranking-section">
    <view class="section-title">
      <text class="title-icon">🏆</text>
      <text class="title-text">我的排名</text>
    </view>
    <view class="my-ranking-card">
      <view class="ranking-info">
        <view class="rank-number">
          <text class="rank-text">第{{myRanking.rank}}名</text>
        </view>
        <view class="user-info">
          <image class="user-avatar" src="{{myRanking.avatar}}" mode="aspectFill" />
          <view class="user-details">
            <text class="user-name">{{myRanking.name}}</text>
            <text class="user-score">{{myRanking.score}} 分</text>
          </view>
        </view>
      </view>
      <view class="ranking-stats">
        <view class="stat-item">
          <text class="stat-value">{{myRanking.todayScore}}</text>
          <text class="stat-label">今日得分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{myRanking.streak}}</text>
          <text class="stat-label">连续天数</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 排行榜类型切换 -->
  <view class="ranking-tabs">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">今日排行</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">总排行</text>
    </view>
    <view class="tab-item {{currentTab === 2 ? 'active' : ''}}" bindtap="switchTab" data-tab="2">
      <text class="tab-text">契约榜</text>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="ranking-list-section">
    <!-- 今日排行 -->
    <view class="ranking-list" wx:if="{{currentTab === 0}}">
      <view class="ranking-item {{item.rank <= 3 ? 'top-three' : ''}}" wx:for="{{todayRanking}}" wx:key="id">
        <view class="rank-badge">
          <text wx:if="{{item.rank === 1}}" class="rank-icon">🥇</text>
          <text wx:elif="{{item.rank === 2}}" class="rank-icon">🥈</text>
          <text wx:elif="{{item.rank === 3}}" class="rank-icon">🥉</text>
          <text wx:else class="rank-number">{{item.rank}}</text>
        </view>
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="user-desc">今日得分 {{item.todayScore}} 分</text>
        </view>
        <view class="user-score">
          <text class="score-value">{{item.score}}</text>
          <text class="score-label">总分</text>
        </view>
      </view>
    </view>

    <!-- 总排行 -->
    <view class="ranking-list" wx:if="{{currentTab === 1}}">
      <view class="ranking-item {{item.rank <= 3 ? 'top-three' : ''}}" wx:for="{{totalRanking}}" wx:key="id">
        <view class="rank-badge">
          <text wx:if="{{item.rank === 1}}" class="rank-icon">🥇</text>
          <text wx:elif="{{item.rank === 2}}" class="rank-icon">🥈</text>
          <text wx:elif="{{item.rank === 3}}" class="rank-icon">🥉</text>
          <text wx:else class="rank-number">{{item.rank}}</text>
        </view>
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="user-desc">连续 {{item.streak}} 天</text>
        </view>
        <view class="user-score">
          <text class="score-value">{{item.totalScore}}</text>
          <text class="score-label">总分</text>
        </view>
      </view>
    </view>

    <!-- 契约榜 -->
    <view class="ranking-list" wx:if="{{currentTab === 2}}">
      <view class="contract-item" wx:for="{{contractRanking}}" wx:key="id">
        <view class="contract-header">
          <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
          <view class="user-info">
            <text class="user-name">{{item.name}}</text>
            <text class="contract-title">{{item.contractTitle}}</text>
          </view>
          <view class="contract-status {{item.status}}">
            <text wx:if="{{item.status === 'active'}}">进行中</text>
            <text wx:elif="{{item.status === 'completed'}}">已完成</text>
            <text wx:else>已失败</text>
          </view>
        </view>
        <view class="contract-progress">
          <view class="progress-info">
            <text class="progress-text">{{item.currentDay}}/{{item.totalDays}} 天</text>
            <text class="progress-percent">{{item.progressPercent}}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progressPercent}}%"></view>
          </view>
        </view>
        <view class="contract-reward">
          <text class="reward-label">约定奖励：</text>
          <text class="reward-text">{{item.reward}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 激励信息 -->
  <view class="motivation-section">
    <view class="motivation-card">
      <view class="motivation-icon">🎯</view>
      <view class="motivation-content">
        <text class="motivation-title">继续加油！</text>
        <text class="motivation-text" wx:if="{{currentTab === 0}}">今日还有机会提升排名</text>
        <text class="motivation-text" wx:elif="{{currentTab === 1}}">坚持打卡，冲击更高排名</text>
        <text class="motivation-text" wx:else>完成契约，获得专属荣誉</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!hasData}}" class="empty-state">
    <view class="empty-icon">📊</view>
    <text class="empty-title">暂无排行数据</text>
    <text class="empty-desc">完成打卡后即可查看排行榜</text>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
