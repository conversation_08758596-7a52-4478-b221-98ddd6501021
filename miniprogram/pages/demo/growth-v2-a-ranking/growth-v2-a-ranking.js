// 方案A - 排行榜页面
Page({
  data: {
    // 训练营信息
    campInfo: {
      title: "21天跳绳养成计划",
      subtitle: "今日排行榜",
    },

    // 总参与人数
    totalParticipants: 156,

    // 当前Tab
    currentTab: 0,

    // 是否有数据
    hasData: true,

    // 我的排名
    myRanking: {
      rank: 8,
      name: "小明",
      avatar: "/images/avatar_default.png",
      score: 285,
      todayScore: 25,
      streak: 12,
    },

    // 今日排行
    todayRanking: [
      {
        id: 1,
        rank: 1,
        name: "小红",
        avatar: "/images/avatar_default.png",
        score: 320,
        todayScore: 30,
        streak: 14,
      },
      {
        id: 2,
        rank: 2,
        name: "小刚",
        avatar: "/images/avatar_default.png",
        score: 315,
        todayScore: 28,
        streak: 13,
      },
      {
        id: 3,
        rank: 3,
        name: "小丽",
        avatar: "/images/avatar_default.png",
        score: 310,
        todayScore: 27,
        streak: 15,
      },
      {
        id: 4,
        rank: 4,
        name: "小华",
        avatar: "/images/avatar_default.png",
        score: 305,
        todayScore: 26,
        streak: 11,
      },
      {
        id: 5,
        rank: 5,
        name: "小军",
        avatar: "/images/avatar_default.png",
        score: 300,
        todayScore: 25,
        streak: 12,
      },
      {
        id: 6,
        rank: 6,
        name: "小芳",
        avatar: "/images/avatar_default.png",
        score: 295,
        todayScore: 24,
        streak: 10,
      },
      {
        id: 7,
        rank: 7,
        name: "小强",
        avatar: "/images/avatar_default.png",
        score: 290,
        todayScore: 23,
        streak: 9,
      },
      {
        id: 8,
        rank: 8,
        name: "小明",
        avatar: "/images/avatar_default.png",
        score: 285,
        todayScore: 25,
        streak: 12,
      },
      {
        id: 9,
        rank: 9,
        name: "小雨",
        avatar: "/images/avatar_default.png",
        score: 280,
        todayScore: 22,
        streak: 8,
      },
      {
        id: 10,
        rank: 10,
        name: "小雪",
        avatar: "/images/avatar_default.png",
        score: 275,
        todayScore: 21,
        streak: 7,
      },
    ],

    // 总排行
    totalRanking: [
      {
        id: 1,
        rank: 1,
        name: "小红",
        avatar: "/images/avatar_default.png",
        totalScore: 420,
        streak: 21,
      },
      {
        id: 2,
        rank: 2,
        name: "小刚",
        avatar: "/images/avatar_default.png",
        totalScore: 415,
        streak: 20,
      },
      {
        id: 3,
        rank: 3,
        name: "小丽",
        avatar: "/images/avatar_default.png",
        totalScore: 410,
        streak: 21,
      },
      {
        id: 4,
        rank: 4,
        name: "小华",
        avatar: "/images/avatar_default.png",
        totalScore: 405,
        streak: 19,
      },
      {
        id: 5,
        rank: 5,
        name: "小军",
        avatar: "/images/avatar_default.png",
        totalScore: 400,
        streak: 18,
      },
      {
        id: 6,
        rank: 6,
        name: "小芳",
        avatar: "/images/avatar_default.png",
        totalScore: 395,
        streak: 17,
      },
      {
        id: 7,
        rank: 7,
        name: "小强",
        avatar: "/images/avatar_default.png",
        totalScore: 390,
        streak: 16,
      },
      {
        id: 8,
        rank: 8,
        name: "小明",
        avatar: "/images/avatar_default.png",
        totalScore: 385,
        streak: 15,
      },
      {
        id: 9,
        rank: 9,
        name: "小雨",
        avatar: "/images/avatar_default.png",
        totalScore: 380,
        streak: 14,
      },
      {
        id: 10,
        rank: 10,
        name: "小雪",
        avatar: "/images/avatar_default.png",
        totalScore: 375,
        streak: 13,
      },
    ],

    // 契约排行
    contractRanking: [
      {
        id: 1,
        name: "小红",
        avatar: "/images/avatar_default.png",
        contractTitle: "21天跳绳挑战契约",
        status: "completed",
        currentDay: 21,
        totalDays: 21,
        progressPercent: 100,
        reward: "一次游乐园之旅",
      },
      {
        id: 2,
        name: "小刚",
        avatar: "/images/avatar_default.png",
        contractTitle: "21天跳绳挑战契约",
        status: "active",
        currentDay: 18,
        totalDays: 21,
        progressPercent: 86,
        reward: "一套新玩具",
      },
      {
        id: 3,
        name: "小丽",
        avatar: "/images/avatar_default.png",
        contractTitle: "21天跳绳挑战契约",
        status: "active",
        currentDay: 16,
        totalDays: 21,
        progressPercent: 76,
        reward: "一次科技馆之旅",
      },
      {
        id: 4,
        name: "小华",
        avatar: "/images/avatar_default.png",
        contractTitle: "14天阅读契约",
        status: "active",
        currentDay: 10,
        totalDays: 14,
        progressPercent: 71,
        reward: "一套新书籍",
      },
      {
        id: 5,
        name: "小明",
        avatar: "/images/avatar_default.png",
        contractTitle: "21天跳绳挑战契约",
        status: "active",
        currentDay: 14,
        totalDays: 21,
        progressPercent: 67,
        reward: "一次科技馆之旅",
      },
      {
        id: 6,
        name: "小军",
        avatar: "/images/avatar_default.png",
        contractTitle: "30天数学契约",
        status: "failed",
        currentDay: 8,
        totalDays: 30,
        progressPercent: 27,
        reward: "一个新书包",
      },
    ],
  },

  onLoad(options) {
    console.log("方案A排行榜页面加载");
    const campId = options.campId;
    if (campId) {
      this.loadRankingData(campId);
    }
  },

  /**
   * 加载排行榜数据
   */
  loadRankingData(campId) {
    // 模拟加载排行榜数据
    console.log("加载排行榜数据:", campId);
  },

  /**
   * 切换Tab
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    // 模拟刷新数据
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: "刷新成功",
        icon: "success",
      });
    }, 1000);
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: "快来看看训练营排行榜！",
      path: "/pages/demo/growth-v2-a-ranking/growth-v2-a-ranking",
      imageUrl: "/images/share_ranking.jpg",
    };
  },
});
