// 成长主页重构版 - 基于V2-A简洁任务导向型
Page({
  data: {
    // 当前Tab
    currentTab: 0,

    // 是否有活跃的训练营
    hasActiveCamps: true,

    // 用户统计信息
    userStats: {
      totalPoints: 2580,
      currentLevel: 8,
      streakDays: 15,
      completedContracts: 3,
    },

    // 训练营列表
    campList: [
      {
        id: 1,
        title: "21天跳绳挑战",
        subtitle: "从零基础到连续跳绳300个",
        currentDay: 14,
        totalDays: 21,
        progressPercent: 67,
        streakDays: 12,
        totalPoints: 1680,
        todayStatus: "pending", // pending, completed
        hasContract: true,
        contract: {
          status: "进行中",
          reward: "新跳绳 + 运动鞋",
          witness: "妈妈、爸爸",
        },
      },
      {
        id: 2,
        title: "30天阅读习惯",
        subtitle: "每天阅读30分钟，培养阅读兴趣",
        currentDay: 8,
        totalDays: 30,
        progressPercent: 27,
        streakDays: 6,
        totalPoints: 480,
        todayStatus: "completed",
        hasContract: false,
      },
      {
        id: 3,
        title: "数学思维训练",
        subtitle: "14天数学逻辑思维提升",
        currentDay: 3,
        totalDays: 14,
        progressPercent: 21,
        streakDays: 3,
        totalPoints: 420,
        todayStatus: "pending",
        hasContract: true,
        contract: {
          status: "进行中",
          reward: "新的数学练习册",
          witnesses: [{ id: 1, avatar: "/images/avatar_default.png" }],
        },
      },
    ],

    // 勋章数据
    unlockedMedals: 8,
    totalMedals: 15,
    medalsList: [
      {
        id: 1,
        icon: "🏃‍♂️",
        name: "运动达人",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 2,
        icon: "📚",
        name: "阅读之星",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 3,
        icon: "🏆",
        name: "契约守护者",
        unlocked: true,
        isContractMedal: true,
      },
      {
        id: 4,
        icon: "⚡",
        name: "连续打卡7天",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 5,
        icon: "🔥",
        name: "连续打卡21天",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 6,
        icon: "💎",
        name: "完美契约",
        unlocked: false,
        isContractMedal: true,
      },
    ],

    // 成长轨迹
    growthTimeline: [
      {
        id: 1,
        date: "今天",
        title: "完成阅读打卡",
        description: "阅读《小王子》30分钟，记录读后感",
        icon: "📚",
      },
      {
        id: 2,
        date: "昨天",
        title: "跳绳训练突破",
        description: "连续跳绳150个，创个人新纪录",
        icon: "🏃‍♂️",
      },
      {
        id: 3,
        date: "3天前",
        title: "获得新勋章",
        description: '获得"契约守护者"勋章',
        icon: "🏆",
      },
    ],
  },

  onLoad(options) {
    console.log("成长主页重构版加载");
  },

  /**
   * 切换Tab
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab,
    });
  },

  /**
   * 跳转到训练营详情
   */
  goToCampDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/demo/growth-v4-detail/growth-v4-detail?campId=${camp.id}`,
    });
  },

  /**
   * 跳转到训练营详情页面
   */
  goToCampDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/demo/growth-v4-detail/growth-v4-detail?campId=${camp.id}&from=main&status=${camp.todayStatus}`,
    });
  },

  /**
   * 跳转到荣誉契约
   */
  goToContract(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.showToast({
      title: "跳转到契约详情页",
      icon: "none",
    });
  },

  /**
   * 创建契约
   */
  createContract(e) {
    const camp = e.currentTarget.dataset.camp;

    wx.showModal({
      title: "创建荣誉契约",
      content: `为"${camp.title}"创建家庭荣誉契约，设定奖励和见证人？`,
      confirmText: "创建",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "跳转到契约创建页",
            icon: "none",
          });
        }
      },
    });
  },

  /**
   * 查看打卡详情（已打卡状态）
   */
  viewCheckinDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/demo/growth-v4-detail/growth-v4-detail?campId=${camp.id}&from=main&status=completed`,
    });
  },

  /**
   * 查看排行榜 - 直接跳转到排行榜
   */
  viewRanking(e) {
    const camp = e.currentTarget.dataset.camp;

    // 直接跳转到排行榜
    wx.navigateTo({
      url: `/pages/demo/leaderboard-v4/leaderboard-v4?type=weekly&campId=${camp.id}`,
    });
  },

  /**
   * 查看契约
   */
  viewContract(e) {
    const camp = e.currentTarget.dataset.camp;

    if (camp.hasContract) {
      // 如果有契约，跳转到契约详情
      wx.navigateTo({
        url: `/pages/demo/contract-v4/contract-v4?campId=${
          camp.id
        }&contractId=${camp.contract?.id || 1}`,
      });
    } else {
      // 如果没有契约，提示创建
      wx.showModal({
        title: "创建荣誉契约",
        content: `为"${camp.title}"创建家庭荣誉契约，设定奖励和见证人？`,
        confirmText: "创建",
        success: (res) => {
          if (res.confirm) {
            // 跳转到契约页面，自动打开创建弹窗
            wx.navigateTo({
              url: `/pages/demo/contract-v4/contract-v4?campId=${camp.id}&action=create`,
            });
          }
        },
      });
    }
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: "/pages/index/index",
    });
  },

  /**
   * 查看勋章详情
   */
  viewMedalDetail(e) {
    const medal = e.currentTarget.dataset.medal;

    if (!medal.unlocked) {
      wx.showToast({
        title: "勋章未解锁",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: medal.name,
      content: `恭喜获得"${medal.name}"勋章！${
        medal.isContractMedal ? "这是一个珍贵的契约勋章。" : ""
      }`,
      showCancel: false,
      confirmText: "知道了",
    });
  },

  /**
   * 分享所有勋章
   */
  shareAllMedals() {
    const posterData = {
      type: "medal",
      medals: this.data.medalsList.filter((m) => m.unlocked),
      totalMedals: this.data.totalMedals,
      unlockedMedals: this.data.unlockedMedals,
    };

    wx.navigateTo({
      url: `/pages/demo/share-poster-v4/share-poster-v4?type=medal&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享单个勋章
   */
  shareMedal(e) {
    const medal = e.currentTarget.dataset.medal;

    const posterData = {
      type: "medal",
      medal: medal,
      singleMedal: true,
    };

    wx.navigateTo({
      url: `/pages/demo/share-poster-v4/share-poster-v4?type=medal&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享成长轨迹
   */
  shareGrowthTimeline() {
    const posterData = {
      type: "progress",
      timeline: this.data.growthTimeline,
      totalPoints: this.data.totalPoints,
      level: this.data.currentLevel,
    };

    wx.navigateTo({
      url: `/pages/demo/share-poster-v4/share-poster-v4?type=progress&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 分享单个成长轨迹项目
   */
  shareTimelineItem(e) {
    const timeline = e.currentTarget.dataset.timeline;

    wx.showLoading({
      title: "生成海报中...",
    });

    // 模拟生成单个成就海报
    setTimeout(() => {
      wx.hideLoading();
      wx.showActionSheet({
        itemList: ["保存到相册", "分享到朋友圈", "分享给好友"],
        success: (res) => {
          const actions = ["保存成功", "分享到朋友圈", "分享给好友"];
          wx.showToast({
            title: `${timeline.title}${actions[res.tapIndex]}`,
            icon: "success",
          });
        },
      });
    }, 1000);
  },

  /**
   * 查看成长轨迹详情
   */
  viewTimelineDetail(e) {
    const timeline = e.currentTarget.dataset.timeline;

    wx.showModal({
      title: timeline.title,
      content: `${timeline.date}\n${timeline.description}`,
      showCancel: false,
      confirmText: "知道了",
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: "我的成长训练营进度，一起来挑战吧！",
      path: "/pages/demo/growth-v4-main/growth-v4-main",
      imageUrl: "/images/share_growth_v4.jpg",
    };
  },
});
