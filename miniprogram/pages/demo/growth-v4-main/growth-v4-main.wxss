/* 成长主页重构版样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部Tab导航 */
.tab-nav {
  background-color: #FFFFFF;
  display: flex;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}

.tab-item.active {
  color: #FF7A45;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF7A45;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.tab-item.active .tab-text {
  color: #FF7A45;
  font-weight: 600;
}

/* Tab内容 */
.tab-content {
  padding: 0 32rpx;
}

/* 通用区块标题 */
.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 32rpx 0 24rpx;
}

.title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.camps-count, .medals-count {
  font-size: 24rpx;
  color: #666666;
}

/* 训练营列表 */
.camps-section {
  margin-bottom: 48rpx;
}

.camp-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 训练营卡片 */
.camp-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.camp-card:active {
  transform: scale(0.98);
  border-color: #FF7A45;
}

/* 训练营头部 */
.camp-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.camp-info {
  flex: 1;
  cursor: pointer;
}

.camp-info:active {
  opacity: 0.7;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.status-symbol {
  font-size: 24rpx;
  line-height: 1;
  display: block;
  text-align: center;
  vertical-align: middle;
}

.status-symbol.completed {
  /* emoji 使用系统颜色 */
}

.status-symbol.pending {
  /* emoji 使用系统颜色 */
}

.ranking-link {
  padding: 8rpx 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.ranking-link:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  transform: scale(0.98);
}

.ranking-text {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.ranking-link:active .ranking-text {
  color: #4A90E2;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}





/* 进度信息 */
.camp-progress {
  margin-bottom: 20rpx;
}

.progress-data {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.data-item {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.data-separator {
  font-size: 24rpx;
  color: #CCCCCC;
  font-weight: 400;
}

/* 家庭荣誉契约详情 */
.contract-detail {
  background: linear-gradient(135deg, #F8FAFE, #E8F4FD);
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #E8F4FD;
}

.contract-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.contract-icon {
  font-size: 20rpx;
}

.contract-text {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: 600;
}

.contract-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.contract-reward,
.contract-witness {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

/* 操作按钮区 - 打卡与契约并排 */
.action-buttons-row {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.checkin-btn-flex {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 18rpx 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary-soft.checkin-btn-flex {
  background: linear-gradient(135deg, #FF9A6B, #FFB88A);
  color: #FFFFFF;
  box-shadow: 0 2rpx 12rpx rgba(255, 154, 107, 0.2);
}

.btn-success-soft.checkin-btn-flex {
  background: linear-gradient(135deg, #73D13D, #95DE64);
  color: #FFFFFF;
  box-shadow: 0 2rpx 12rpx rgba(115, 209, 61, 0.2);
}

.checkin-btn-flex:active {
  transform: scale(0.98);
}

.checkin-btn-flex .btn-icon {
  font-size: 28rpx;
}

.checkin-btn-flex .btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

.btn-outline-soft.contract-btn-flex {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 18rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: 1rpx solid #E5E5E5;
  background-color: #F7F8FA;
  color: #666666;
  transition: all 0.3s ease;
}

.btn-outline-soft.contract-btn-flex:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
  transform: scale(0.98);
}

.contract-btn-flex .btn-icon {
  font-size: 22rpx;
}

.contract-btn-flex .btn-text {
  font-size: 24rpx;
  font-weight: 500;
}



/* 个人荣誉总览 */
.honor-overview-section {
  margin-bottom: 48rpx;
}

.honor-stats {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 勋章墙 */
.medals-section {
  margin-bottom: 48rpx;
}

.medals-grid {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.medal-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.medal-item.unlocked {
  background: linear-gradient(135deg, #FFF2ED, #FFFFFF);
  border: 2rpx solid #FF7A45;
}

.medal-item.locked {
  background-color: #F7F8FA;
  opacity: 0.5;
}

.medal-item:active {
  transform: scale(0.95);
}

.medal-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}

.medal-name {
  font-size: 22rpx;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.medal-special {
  
}

.special-label {
  font-size: 18rpx;
  color: #FF7A45;
  background-color: #FFF2ED;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

/* 成长轨迹 */
.growth-timeline-section {
  margin-bottom: 48rpx;
}

.timeline-list {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.timeline-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.timeline-item:last-child {
  border-bottom: none;
}

.timeline-date {
  font-size: 20rpx;
  color: #999999;
  width: 80rpx;
  margin-right: 20rpx;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.timeline-icon {
  font-size: 32rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
  margin-bottom: 48rpx;
}

.empty-action-btn {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  padding: 20rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

/* 分享功能样式 */
.share-all-btn, .share-timeline-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 122, 69, 0.3);
}

.share-all-btn:active, .share-timeline-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.share-icon {
  font-size: 18rpx;
}

.share-text {
  font-size: 20rpx;
}

.medal-content {
  padding: 16rpx;
  width: 100%;
}

.medal-share-btn {
  width: 100%;
  padding: 8rpx 12rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 0 0 14rpx 14rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.medal-share-btn:active {
  background-color: #357ABD;
  transform: scale(0.98);
}

.timeline-main {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx 0;
}

.timeline-share-btn {
  padding: 8rpx 16rpx;
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-left: 16rpx;
}

.timeline-share-btn:active {
  background-color: #357ABD;
  transform: scale(0.95);
}

/* 确保section-title是相对定位 */
.section-title {
  position: relative;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
