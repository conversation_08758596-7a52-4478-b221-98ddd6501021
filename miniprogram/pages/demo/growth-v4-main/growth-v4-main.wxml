<!-- 成长主页重构版 - 基于V2-A简洁任务导向型 -->
<view class="page-container">
  <!-- 顶部导航Tab -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">任务</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">成长</text>
    </view>
  </view>
  <!-- 任务Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <!-- 训练营列表 -->
    <view class="camps-section">
      <view class="section-title">
        <text class="title-icon">📚</text>
        <text class="title-text">我的训练营</text>
        <text class="camps-count">{{campList.length}}个进行中</text>
      </view>
      <!-- 训练营卡片列表 -->
      <view class="camp-list">
        <view class="camp-card" wx:for="{{campList}}" wx:key="id">
          <!-- 训练营基本信息 -->
          <view class="camp-header">
            <view class="camp-info" bindtap="goToCampDetail" data-camp="{{item}}">
              <text class="camp-title">{{item.title}}</text>
              <text class="camp-subtitle">{{item.subtitle}}</text>
            </view>
            <view class="header-right">
              <view class="status-icon">
                <text class="status-symbol completed" wx:if="{{item.todayStatus === 'completed'}}">
                  ✅
                </text>
                <text class="status-symbol pending" wx:else>⭕</text>
              </view>
              <view class="ranking-link" bindtap="viewRanking" data-camp="{{item}}">
                <text class="ranking-text">排行榜</text>
              </view>
            </view>
          </view>
          <!-- 打卡进度 -->
          <view class="camp-progress">
            <view class="progress-data">
              <text class="data-item">第{{item.currentDay}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">连续{{item.streakDays}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">{{item.totalPoints}}积分</text>
              <text class="data-separator">•</text>
              <text class="data-item">完成{{item.progressPercent}}%</text>
            </view>
          </view>
          <!-- 家庭荣誉契约详情（如果有） -->
          <view class="contract-detail" wx:if="{{item.hasContract}}">
            <view class="contract-title">
              <text class="contract-icon">🏆</text>
              <text class="contract-text">家庭荣誉契约</text>
            </view>
            <view class="contract-info">
              <text class="contract-reward">🎁 {{item.contract.reward}}</text>
              <text class="contract-witness">👥 {{item.contract.witness}}</text>
            </view>
          </view>
          <!-- 操作按钮区 - 打卡与契约并排 -->
          <view class="action-buttons-row">
            <button class="btn btn-primary-soft checkin-btn-flex" bindtap="goToCampDetail" data-camp="{{item}}" wx:if="{{item.todayStatus === 'pending'}}">
              <text class="btn-icon">⚡</text>
              <text class="btn-text">立即打卡</text>
            </button>
            <button class="btn btn-success-soft checkin-btn-flex" bindtap="goToCampDetail" data-camp="{{item}}" wx:else>
              <text class="btn-icon">✅</text>
              <text class="btn-text">今日已打卡</text>
            </button>
            <button class="btn btn-outline-soft contract-btn-flex" bindtap="viewContract" data-camp="{{item}}">
              <text class="btn-icon">🤝</text>
              <text class="btn-text">{{item.hasContract ? '查看契约' : '添加契约'}}</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 成长Tab内容（去掉奖励中心） -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <!-- 个人荣誉总览 -->
    <view class="honor-overview-section">
      <view class="section-title">
        <text class="title-icon">🌟</text>
        <text class="title-text">个人荣誉</text>
      </view>
      <view class="honor-stats">
        <view class="stat-item">
          <text class="stat-value">{{userStats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.currentLevel}}</text>
          <text class="stat-label">当前等级</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.streakDays}}</text>
          <text class="stat-label">最长连续</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.completedContracts}}</text>
          <text class="stat-label">完成契约</text>
        </view>
      </view>
    </view>
    <!-- 勋章墙 -->
    <view class="medals-section">
      <view class="section-title">
        <text class="title-icon">🏅</text>
        <text class="title-text">勋章墙</text>
        <text class="medals-count">{{unlockedMedals}}/{{totalMedals}}</text>
        <button class="share-all-btn" bindtap="shareAllMedals">
          <text class="share-icon">📸</text>
          <text class="share-text">分享勋章墙</text>
        </button>
      </view>
      <view class="medals-grid">
        <view class="medal-item {{item.unlocked ? 'unlocked' : 'locked'}}" wx:for="{{medalsList}}" wx:key="id">
          <view class="medal-content" bindtap="viewMedalDetail" data-medal="{{item}}">
            <view class="medal-icon">{{item.icon}}</view>
            <text class="medal-name">{{item.name}}</text>
            <view class="medal-special" wx:if="{{item.isContractMedal}}">
              <text class="special-label">契约勋章</text>
            </view>
          </view>
          <button class="medal-share-btn" wx:if="{{item.unlocked}}" bindtap="shareMedal" data-medal="{{item}}" catchtap="true">
            分享
          </button>
        </view>
      </view>
    </view>
    <!-- 成长轨迹 -->
    <view class="growth-timeline-section">
      <view class="section-title">
        <text class="title-icon">📈</text>
        <text class="title-text">成长轨迹</text>
        <button class="share-timeline-btn" bindtap="shareGrowthTimeline">
          <text class="share-icon">📊</text>
          <text class="share-text">分享成长记录</text>
        </button>
      </view>
      <view class="timeline-list">
        <view class="timeline-item" wx:for="{{growthTimeline}}" wx:key="id">
          <view class="timeline-main" bindtap="viewTimelineDetail" data-timeline="{{item}}">
            <view class="timeline-date">{{item.date}}</view>
            <view class="timeline-content">
              <text class="timeline-title">{{item.title}}</text>
              <text class="timeline-desc">{{item.description}}</text>
            </view>
            <view class="timeline-icon">{{item.icon}}</view>
          </view>
          <button class="timeline-share-btn" bindtap="shareTimelineItem" data-timeline="{{item}}" catchtap="true">
            分享
          </button>
        </view>
      </view>
    </view>
  </view>
  <!-- 空状态 -->
  <view wx:if="{{!hasActiveCamps}}" class="empty-state">
    <view class="empty-icon">📭</view>
    <text class="empty-title">还没有参加训练营</text>
    <text class="empty-desc">去首页选择适合的训练营开始学习吧！</text>
    <button class="empty-action-btn" bindtap="goToHome">选择训练营</button>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>