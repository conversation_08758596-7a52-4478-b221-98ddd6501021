// 图片路径测试页面
Page({
  data: {
    imageStatus: {},
    imageStatusText: {},
    successCount: 0,
    errorCount: 0,
    totalCount: 8
  },

  onLoad() {
    console.log('图片路径测试页面加载');
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    const imageName = e.currentTarget.dataset.name;
    console.log('图片加载成功:', imageName);
    
    this.setData({
      [`imageStatus.${imageName}`]: 'success',
      [`imageStatusText.${imageName}`]: '✅ 加载成功',
      successCount: this.data.successCount + 1
    });
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const imageName = e.currentTarget.dataset.name;
    console.error('图片加载失败:', imageName, e.detail);
    
    this.setData({
      [`imageStatus.${imageName}`]: 'error',
      [`imageStatusText.${imageName}`]: '❌ 加载失败',
      errorCount: this.data.errorCount + 1
    });
  }
});
