/* 图片路径测试页面样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding: var(--spacing-lg);
}

.test-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.test-title {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.test-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.test-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.image-item {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.test-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  background-color: #F5F5F5;
}

.test-image.small {
  width: 80rpx;
  height: 80rpx;
}

.image-name {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  word-break: break-all;
}

.image-status {
  font-size: var(--font-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.image-status.success {
  background-color: #E8F5E8;
  color: var(--success-color);
}

.image-status.error {
  background-color: #FFEBEE;
  color: var(--error-color);
}

.image-status.loading {
  background-color: #F5F5F5;
  color: var(--text-placeholder);
}

.test-summary {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.summary-title {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.summary-text {
  font-size: var(--font-md);
  color: var(--text-secondary);
}
