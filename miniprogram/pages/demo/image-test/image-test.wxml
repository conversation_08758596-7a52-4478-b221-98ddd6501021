<!-- 图片路径测试页面 -->
<view class="page-container">
  <view class="test-header">
    <text class="test-title">图片路径测试</text>
    <text class="test-subtitle">验证所有demo页面使用的图片是否能正常加载</text>
  </view>
  <view class="image-tests">
    <view class="test-section">
      <text class="section-title">头像图片测试</text>
      <view class="image-grid">
        <view class="image-item">
          <image class="test-image" src="/images/avatar_default.png" mode="aspectFill" binderror="onImageError" bindload="onImageLoad" data-name="avatar_default.png" />
          <text class="image-name">avatar_default.png</text>
          <text class="image-status {{imageStatus['avatar_default.png'] || 'loading'}}">
            {{imageStatusText['avatar_default.png'] || '加载中...'}}
          </text>
        </view>
        <view class="image-item">
          <image class="test-image" src="/images/avatar_default.png" mode="aspectFill" binderror="onImageError" bindload="onImageLoad" data-name="avatar_default.png" />
          <text class="image-name">avatar_default.png (child)</text>
          <text class="image-status {{imageStatus['avatar_default.png'] || 'loading'}}">
            {{imageStatusText['avatar_default.png'] || '加载中...'}}
          </text>
        </view>
      </view>
    </view>
    <view class="test-section">
      <text class="section-title">Logo图片测试</text>
      <view class="image-grid">
        <view class="image-item">
          <image class="test-image" src="/images/app-logo.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="app-logo.png" />
          <text class="image-name">app-logo.png</text>
          <text class="image-status {{imageStatus['app-logo.png'] || 'loading'}}">
            {{imageStatusText['app-logo.png'] || '加载中...'}}
          </text>
        </view>
        <view class="image-item">
          <image class="test-image" src="/images/app-logo-white.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="app-logo-white.png" />
          <text class="image-name">app-logo-white.png</text>
          <text class="image-status {{imageStatus['app-logo-white.png'] || 'loading'}}">
            {{imageStatusText['app-logo-white.png'] || '加载中...'}}
          </text>
        </view>
      </view>
    </view>
    <view class="test-section">
      <text class="section-title">图标图片测试</text>
      <view class="image-grid">
        <view class="image-item">
          <image class="test-image small" src="/images/icon-calendar.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="icon-calendar.png" />
          <text class="image-name">icon-calendar.png</text>
          <text class="image-status {{imageStatus['icon-calendar.png'] || 'loading'}}">
            {{imageStatusText['icon-calendar.png'] || '加载中...'}}
          </text>
        </view>
        <view class="image-item">
          <image class="test-image small" src="/images/icon-heart.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="icon-heart.png" />
          <text class="image-name">icon-heart.png</text>
          <text class="image-status {{imageStatus['icon-heart.png'] || 'loading'}}">
            {{imageStatusText['icon-heart.png'] || '加载中...'}}
          </text>
        </view>
        <view class="image-item">
          <image class="test-image small" src="/images/icon-star.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="icon-star.png" />
          <text class="image-name">icon-star.png</text>
          <text class="image-status {{imageStatus['icon-star.png'] || 'loading'}}">
            {{imageStatusText['icon-star.png'] || '加载中...'}}
          </text>
        </view>
        <view class="image-item">
          <image class="test-image small" src="/images/icon-trophy.png" mode="aspectFit" binderror="onImageError" bindload="onImageLoad" data-name="icon-trophy.png" />
          <text class="image-name">icon-trophy.png</text>
          <text class="image-status {{imageStatus['icon-trophy.png'] || 'loading'}}">
            {{imageStatusText['icon-trophy.png'] || '加载中...'}}
          </text>
        </view>
      </view>
    </view>
    <!-- 错误测试 -->
    <view class="test-section">
      <text class="section-title">错误路径测试（应该失败）</text>
      <view class="image-grid">
        <view class="image-item">
          <image class="test-image" src="/images/avatar-demo.png" mode="aspectFill" binderror="onImageError" bindload="onImageLoad" data-name="avatar-demo.png" />
          <text class="image-name">avatar-demo.png (不存在)</text>
          <text class="image-status {{imageStatus['avatar-demo.png'] || 'loading'}}">
            {{imageStatusText['avatar-demo.png'] || '加载中...'}}
          </text>
        </view>
      </view>
    </view>
  </view>
  <view class="test-summary">
    <text class="summary-title">测试结果汇总</text>
    <text class="summary-text">成功: {{successCount}} | 失败: {{errorCount}} | 总计: {{totalCount}}</text>
  </view>
</view>