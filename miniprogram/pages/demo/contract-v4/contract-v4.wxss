/* 家庭荣誉契约页面 V4版本样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 0 32rpx 120rpx;
}

/* 契约状态头部 */
.contract-header {
  margin: 32rpx 0;
}

.header-bg {
  background: linear-gradient(135deg, #4A90E2, #5BA0F2);
  border-radius: 24rpx;
  padding: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.contract-icon {
  font-size: 48rpx;
}

.contract-info {
  flex: 1;
}

.contract-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.contract-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.contract-status {
  
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-badge.active {
  background-color: #52C41A;
  border-color: #52C41A;
}

.status-badge.completed {
  background-color: #FFD700;
  border-color: #FFD700;
  color: #333333;
}

.status-badge.pending_award {
  background-color: #FF7A45;
  border-color: #FF7A45;
}

.status-text {
  color: inherit;
}

/* 契约详情卡片 */
.contract-details-card {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.details-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.edit-btn:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.edit-icon {
  font-size: 18rpx;
}

.details-content {
  
}

.detail-item {
  margin-bottom: 32rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.label-icon {
  font-size: 24rpx;
}

.label-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}

.item-content {
  padding-left: 36rpx;
}

.content-text {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 见证人列表 */
.witnesses-list {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
}

.witness-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120rpx;
}

.witness-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #4A90E2;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.witness-avatar image {
  width: 100%;
  height: 100%;
}

.witness-name {
  font-size: 22rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.witness-role {
  font-size: 18rpx;
  color: #999999;
}

/* 进度追踪卡片 */
.progress-card {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.progress-percentage {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF7A45;
}

.progress-bar {
  height: 12rpx;
  background-color: #F0F0F0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF7A45, #FF9A6B);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF7A45;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #666666;
}

.stat-divider {
  font-size: 24rpx;
  color: #CCCCCC;
}

/* 最近打卡记录 */
.recent-checkins {
  
}

.checkins-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.checkins-list {
  display: flex;
  gap: 12rpx;
  justify-content: center;
}

.checkin-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12rpx 8rpx;
  border-radius: 12rpx;
  min-width: 64rpx;
  transition: all 0.3s ease;
}

.checkin-day.completed {
  background-color: #E8F5E8;
  border: 1rpx solid #52C41A;
}

.checkin-day.missed {
  background-color: #FFF2F0;
  border: 1rpx solid #FF4D4F;
}

.checkin-day.pending {
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
}

.day-number {
  font-size: 20rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4rpx;
}

.day-status {
  font-size: 16rpx;
}

/* 契约完成庆祝 */
.completion-card {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  color: #333333;
  box-shadow: 0 8rpx 32rpx rgba(255, 215, 0, 0.3);
}

.completion-content {
  margin-bottom: 32rpx;
}

.completion-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.completion-title {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
}

.completion-subtitle {
  font-size: 24rpx;
  display: block;
  margin-bottom: 16rpx;
}

.completion-date {
  font-size: 20rpx;
  opacity: 0.8;
}

.completion-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.share-btn {
  background-color: #4A90E2;
  color: #FFFFFF;
}

.certificate-btn {
  background-color: #52C41A;
  color: #FFFFFF;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 24rpx;
}

/* 家长授勋区域 */
.award-section {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
}

.award-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.award-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.award-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.award-content {
  
}

.award-summary {
  text-align: center;
  margin-bottom: 32rpx;
}

.summary-text {
  font-size: 26rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.summary-details {
  font-size: 22rpx;
  opacity: 0.9;
}

.award-actions {
  display: flex;
  gap: 16rpx;
}

.award-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.confirm-btn {
  background-color: #52C41A;
  color: #FFFFFF;
}

.delay-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.award-btn:active {
  transform: scale(0.95);
}

/* 操作按钮区域 */
.action-buttons {
  margin-bottom: 32rpx;
}

.main-action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx 32rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.create-btn {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(255, 122, 69, 0.3);
}

.invite-btn {
  background: linear-gradient(135deg, #4A90E2, #5BA0F2);
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

.main-action-btn:active {
  transform: scale(0.98);
}

.secondary-action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.secondary-action-btn:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

/* 契约说明 */
.contract-info-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.info-header {
  margin-bottom: 16rpx;
}

.info-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.info-content {
  
}

.info-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.6;
}

/* 创建契约弹窗 */
.create-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.create-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.create-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: #F0F0F0;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  min-height: 80rpx;
  padding: 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333333;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333333;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999999;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.modal-footer .btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn.btn-outline {
  background-color: #F7F8FA;
  color: #666666;
  border: 1rpx solid #E5E5E5;
}

.btn.btn-outline:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.btn.btn-primary {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.btn.btn-primary:active {
  transform: scale(0.98);
}

/* 授勋弹窗 */
.award-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.award-modal.show {
  opacity: 1;
  visibility: visible;
}

.award-content {
  text-align: center;
  padding: 48rpx 32rpx;
}

.award-animation {
  position: relative;
  margin-bottom: 32rpx;
}

.medal-icon {
  font-size: 120rpx;
  animation: bounce 1s ease-in-out infinite alternate;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-20rpx); }
}

.sparkles {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: translateX(-50%) scale(0.8); }
  50% { opacity: 1; transform: translateX(-50%) scale(1.2); }
}

.award-text {
  margin-bottom: 32rpx;
}

.award-main-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #FFD700;
  display: block;
  margin-bottom: 12rpx;
}

.award-sub-text {
  font-size: 24rpx;
  color: #FFFFFF;
}

.award-modal-footer {

}

.award-modal-footer .btn {
  padding: 16rpx 48rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 120rpx;
}
