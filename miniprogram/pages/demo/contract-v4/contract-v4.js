// 家庭荣誉契约页面 V4版本
const app = getApp();

Page({
  data: {
    // 契约数据
    contractData: {},

    // 状态文本映射
    statusTexts: {
      active: "进行中",
      completed: "已完成",
      pending_award: "待授勋",
      cancelled: "已取消",
    },

    // 当前儿童信息
    currentChild: {
      id: 1,
      name: "小明",
      avatar: "👦",
    },

    // 用户角色
    isParent: true,

    // 最近打卡记录
    recentCheckins: [],

    // 创建契约相关
    showCreateModal: false,
    newContract: {
      goal: "",
      reward: "",
      daysIndex: 1,
    },
    dayOptions: ["7", "14", "21", "30"],

    // 授勋相关
    showAwardModal: false,
  },

  onLoad(options) {
    console.log("家庭荣誉契约页面加载", options);
    this.initPage(options);
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadContractData();
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    const contractId = options.contractId;
    const campId = options.campId;
    const action = options.action;

    // 加载契约数据
    this.loadContractData(contractId, campId);

    // 如果是创建动作，自动打开创建弹窗
    if (action === "create") {
      setTimeout(() => {
        this.createNewContract();
      }, 500);
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: "家庭荣誉契约",
    });
  },

  /**
   * 加载契约数据
   */
  loadContractData(contractId, campId) {
    // 模拟加载契约数据
    const mockContractData = {
      id: contractId || 1,
      title: "21天跳绳挑战",
      goal: "连续21天完成跳绳打卡，每天至少练习30分钟",
      reward: "新的专业跳绳和一双心仪的运动鞋",
      status: "active", // active, completed, pending_award, cancelled
      signDate: "2024年1月15日",
      totalDays: 21,
      completedDays: 8,
      streakDays: 8,
      progressPercent: Math.round((8 / 21) * 100),
      witnesses: [
        {
          id: 1,
          name: "妈妈",
          role: "主要见证人",
          avatar: "",
        },
        {
          id: 2,
          name: "爸爸",
          role: "见证人",
          avatar: "",
        },
      ],
    };

    // 生成最近打卡记录
    const recentCheckins = this.generateRecentCheckins(
      mockContractData.completedDays
    );

    this.setData({
      contractData: mockContractData,
      recentCheckins: recentCheckins,
    });
  },

  /**
   * 生成最近打卡记录
   */
  generateRecentCheckins(completedDays) {
    const checkins = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      let status = "pending";
      if (i < completedDays && i < 7) {
        status = Math.random() > 0.1 ? "completed" : "missed";
      } else if (i >= completedDays) {
        status = "pending";
      }

      checkins.push({
        date: date.toISOString().split("T")[0],
        day: date.getDate(),
        status: status,
      });
    }

    return checkins;
  },

  /**
   * 编辑契约
   */
  editContract() {
    wx.showActionSheet({
      itemList: ["修改目标", "修改奖励", "添加见证人", "暂停契约"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.editGoal();
            break;
          case 1:
            this.editReward();
            break;
          case 2:
            this.inviteWitnesses();
            break;
          case 3:
            this.pauseContract();
            break;
        }
      },
    });
  },

  /**
   * 修改目标
   */
  editGoal() {
    wx.showModal({
      title: "修改挑战目标",
      editable: true,
      placeholderText: this.data.contractData.goal,
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            "contractData.goal": res.content,
          });
          wx.showToast({
            title: "目标已更新",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 修改奖励
   */
  editReward() {
    wx.showModal({
      title: "修改约定奖励",
      editable: true,
      placeholderText: this.data.contractData.reward,
      success: (res) => {
        if (res.confirm && res.content) {
          this.setData({
            "contractData.reward": res.content,
          });
          wx.showToast({
            title: "奖励已更新",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 邀请见证人
   */
  inviteWitnesses() {
    wx.showModal({
      title: "邀请见证人",
      content: "通过微信邀请家人成为契约见证人，共同监督挑战进度",
      confirmText: "发送邀请",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "邀请已发送",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 暂停契约
   */
  pauseContract() {
    wx.showModal({
      title: "暂停契约",
      content: "确定要暂停当前契约吗？暂停后可以重新激活。",
      confirmText: "暂停",
      confirmColor: "#FF7A45",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            "contractData.status": "paused",
          });
          wx.showToast({
            title: "契约已暂停",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 分享完成成就
   */
  shareCompletion() {
    const posterData = {
      type: "contract",
      contract: this.data.contractData,
      child: this.data.currentChild,
    };

    wx.navigateTo({
      url: `/pages/demo/share-poster-v4/share-poster-v4?type=contract&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 查看证书
   */
  viewCertificate() {
    wx.showToast({
      title: "跳转到证书页面",
      icon: "none",
    });
  },

  /**
   * 确认授勋
   */
  confirmAward() {
    wx.showModal({
      title: "确认授勋",
      content: `确认为${this.data.currentChild.name}授予"坚持挑战者"荣誉勋章吗？`,
      confirmText: "确认授勋",
      success: (res) => {
        if (res.confirm) {
          this.setData({
            showAwardModal: true,
          });
        }
      },
    });
  },

  /**
   * 延迟授勋
   */
  delayAward() {
    wx.showToast({
      title: "已设置提醒",
      icon: "success",
    });
  },

  /**
   * 完成授勋
   */
  completeAward() {
    this.setData({
      showAwardModal: false,
      "contractData.status": "completed",
      "contractData.completionDate": new Date().toLocaleDateString(),
    });

    wx.showToast({
      title: "授勋完成",
      icon: "success",
    });
  },

  /**
   * 隐藏授勋弹窗
   */
  hideAwardModal() {
    this.setData({
      showAwardModal: false,
    });
  },

  /**
   * 创建新契约
   */
  createNewContract() {
    this.setData({
      showCreateModal: true,
      newContract: {
        goal: "",
        reward: "",
        daysIndex: 1,
      },
    });
  },

  /**
   * 隐藏创建弹窗
   */
  hideCreateModal() {
    this.setData({
      showCreateModal: false,
    });
  },

  /**
   * 目标输入
   */
  onGoalInput(e) {
    this.setData({
      "newContract.goal": e.detail.value,
    });
  },

  /**
   * 奖励输入
   */
  onRewardInput(e) {
    this.setData({
      "newContract.reward": e.detail.value,
    });
  },

  /**
   * 天数选择
   */
  onDaysChange(e) {
    this.setData({
      "newContract.daysIndex": e.detail.value,
    });
  },

  /**
   * 确认创建契约
   */
  confirmCreate() {
    const { goal, reward, daysIndex } = this.data.newContract;
    const days = this.data.dayOptions[daysIndex];

    if (!goal.trim()) {
      wx.showToast({
        title: "请填写挑战目标",
        icon: "none",
      });
      return;
    }

    if (!reward.trim()) {
      wx.showToast({
        title: "请填写约定奖励",
        icon: "none",
      });
      return;
    }

    // 创建新契约
    const newContractData = {
      id: Date.now(),
      title: `${days}天挑战`,
      goal: goal,
      reward: reward,
      status: "active",
      signDate: new Date().toLocaleDateString(),
      totalDays: parseInt(days),
      completedDays: 0,
      streakDays: 0,
      progressPercent: 0,
      witnesses: [
        {
          id: 1,
          name: "妈妈",
          role: "主要见证人",
          avatar: "",
        },
      ],
    };

    this.setData({
      contractData: newContractData,
      showCreateModal: false,
      recentCheckins: this.generateRecentCheckins(0),
    });

    wx.showToast({
      title: "契约创建成功",
      icon: "success",
    });
  },

  /**
   * 查看历史契约
   */
  viewHistory() {
    wx.showToast({
      title: "跳转到历史契约页面",
      icon: "none",
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadContractData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const { contractData, currentChild } = this.data;
    return {
      title: `${currentChild.name}的家庭荣誉契约，一起来见证吧！`,
      path: `/pages/demo/contract-v4/contract-v4?contractId=${contractData.id}`,
      imageUrl: "/images/share-contract.png",
    };
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },
});
