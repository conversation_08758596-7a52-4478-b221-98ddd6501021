<!-- 家庭荣誉契约页面 V4版本 -->
<view class="page-container">
  <!-- 契约状态头部 -->
  <view class="contract-header">
    <view class="header-bg">
      <view class="header-content">
        <view class="contract-icon">🏆</view>
        <view class="contract-info">
          <text class="contract-title">家庭荣誉契约</text>
          <text class="contract-subtitle">{{contractData.title || '21天跳绳挑战'}}</text>
        </view>
        <view class="contract-status">
          <view class="status-badge {{contractData.status}}">
            <text class="status-text">{{statusTexts[contractData.status]}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 契约详情卡片 -->
  <view class="contract-details-card">
    <view class="details-header">
      <text class="details-title">契约详情</text>
      <button class="edit-btn" bindtap="editContract" wx:if="{{contractData.status === 'active'}}">
        <text class="edit-icon">✏️</text>
        <text class="edit-text">编辑</text>
      </button>
    </view>
    
    <view class="details-content">
      <!-- 目标设定 -->
      <view class="detail-item">
        <view class="item-label">
          <text class="label-icon">🎯</text>
          <text class="label-text">挑战目标</text>
        </view>
        <view class="item-content">
          <text class="content-text">{{contractData.goal || '连续21天完成跳绳打卡'}}</text>
        </view>
      </view>
      
      <!-- 奖励约定 -->
      <view class="detail-item">
        <view class="item-label">
          <text class="label-icon">🎁</text>
          <text class="label-text">约定奖励</text>
        </view>
        <view class="item-content">
          <text class="content-text">{{contractData.reward || '新的跳绳和运动鞋'}}</text>
        </view>
      </view>
      
      <!-- 见证人 -->
      <view class="detail-item">
        <view class="item-label">
          <text class="label-icon">👥</text>
          <text class="label-text">见证人</text>
        </view>
        <view class="item-content">
          <view class="witnesses-list">
            <view class="witness-item" wx:for="{{contractData.witnesses}}" wx:key="id">
              <view class="witness-avatar">
                <image src="{{item.avatar}}" mode="aspectFill" wx:if="{{item.avatar}}" />
                <text wx:else>{{item.name.charAt(0)}}</text>
              </view>
              <text class="witness-name">{{item.name}}</text>
              <text class="witness-role">{{item.role}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 签约时间 -->
      <view class="detail-item">
        <view class="item-label">
          <text class="label-icon">📅</text>
          <text class="label-text">签约时间</text>
        </view>
        <view class="item-content">
          <text class="content-text">{{contractData.signDate || '2024年1月15日'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 进度追踪卡片 -->
  <view class="progress-card" wx:if="{{contractData.status === 'active'}}">
    <view class="progress-header">
      <text class="progress-title">挑战进度</text>
      <text class="progress-percentage">{{contractData.progressPercent || 38}}%</text>
    </view>
    
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{contractData.progressPercent || 38}}%;"></view>
    </view>
    
    <view class="progress-stats">
      <view class="stat-item">
        <text class="stat-value">{{contractData.completedDays || 8}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-divider">/</view>
      <view class="stat-item">
        <text class="stat-value">{{contractData.totalDays || 21}}</text>
        <text class="stat-label">总天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{contractData.streakDays || 8}}</text>
        <text class="stat-label">连续天数</text>
      </view>
    </view>
    
    <!-- 最近打卡记录 -->
    <view class="recent-checkins">
      <text class="checkins-title">最近打卡</text>
      <view class="checkins-list">
        <view class="checkin-day {{item.status}}" 
              wx:for="{{recentCheckins}}" 
              wx:key="date">
          <text class="day-number">{{item.day}}</text>
          <text class="day-status">{{item.status === 'completed' ? '✅' : item.status === 'missed' ? '❌' : '⭕'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 契约完成庆祝 -->
  <view class="completion-card" wx:if="{{contractData.status === 'completed'}}">
    <view class="completion-content">
      <view class="completion-icon">🎉</view>
      <text class="completion-title">契约达成！</text>
      <text class="completion-subtitle">恭喜完成21天挑战</text>
      <text class="completion-date">完成时间：{{contractData.completionDate}}</text>
    </view>
    
    <view class="completion-actions">
      <button class="action-btn share-btn" bindtap="shareCompletion">
        <text class="btn-icon">📸</text>
        <text class="btn-text">分享成就</text>
      </button>
      <button class="action-btn certificate-btn" bindtap="viewCertificate">
        <text class="btn-icon">🏆</text>
        <text class="btn-text">查看证书</text>
      </button>
    </view>
  </view>

  <!-- 家长授勋区域 -->
  <view class="award-section" wx:if="{{contractData.status === 'pending_award' && isParent}}">
    <view class="award-header">
      <text class="award-title">🏅 授勋仪式</text>
      <text class="award-subtitle">孩子已完成挑战，请确认授勋</text>
    </view>
    
    <view class="award-content">
      <view class="award-summary">
        <text class="summary-text">{{currentChild.name}}已成功完成21天跳绳挑战</text>
        <text class="summary-details">连续打卡{{contractData.completedDays}}天，表现优秀！</text>
      </view>
      
      <view class="award-actions">
        <button class="award-btn confirm-btn" bindtap="confirmAward">
          <text class="btn-icon">✅</text>
          <text class="btn-text">确认授勋</text>
        </button>
        <button class="award-btn delay-btn" bindtap="delayAward">
          <text class="btn-icon">⏰</text>
          <text class="btn-text">稍后授勋</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-buttons">
    <!-- 创建新契约 -->
    <button class="main-action-btn create-btn" 
            bindtap="createNewContract" 
            wx:if="{{!contractData.id || contractData.status === 'completed'}}">
      <text class="btn-icon">➕</text>
      <text class="btn-text">创建新契约</text>
    </button>
    
    <!-- 邀请见证人 -->
    <button class="main-action-btn invite-btn" 
            bindtap="inviteWitnesses" 
            wx:if="{{contractData.status === 'active'}}">
      <text class="btn-icon">👥</text>
      <text class="btn-text">邀请见证人</text>
    </button>
    
    <!-- 查看历史契约 -->
    <button class="secondary-action-btn history-btn" bindtap="viewHistory">
      <text class="btn-icon">📋</text>
      <text class="btn-text">历史契约</text>
    </button>
  </view>

  <!-- 契约说明 -->
  <view class="contract-info-card">
    <view class="info-header">
      <text class="info-title">💡 契约说明</text>
    </view>
    <view class="info-content">
      <text class="info-text">家庭荣誉契约是亲子共同制定的挑战目标，通过设定明确的奖励和见证人，让孩子在家长的支持下完成挑战，增强家庭凝聚力和孩子的成就感。</text>
    </view>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 创建契约弹窗 -->
<view class="create-modal {{showCreateModal ? 'show' : ''}}" bindtap="hideCreateModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">创建荣誉契约</text>
      <view class="modal-close" bindtap="hideCreateModal">×</view>
    </view>
    <view class="modal-body">
      <view class="form-section">
        <text class="form-label">挑战目标</text>
        <textarea class="form-input" 
                  placeholder="例如：连续21天完成跳绳打卡"
                  value="{{newContract.goal}}"
                  bindinput="onGoalInput"
                  maxlength="100" />
      </view>
      
      <view class="form-section">
        <text class="form-label">约定奖励</text>
        <textarea class="form-input" 
                  placeholder="例如：新的跳绳和运动鞋"
                  value="{{newContract.reward}}"
                  bindinput="onRewardInput"
                  maxlength="100" />
      </view>
      
      <view class="form-section">
        <text class="form-label">挑战天数</text>
        <picker mode="selector" 
                range="{{dayOptions}}" 
                value="{{newContract.daysIndex}}"
                bindchange="onDaysChange">
          <view class="picker-display">
            <text>{{dayOptions[newContract.daysIndex]}}天</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hideCreateModal">取消</button>
      <button class="btn btn-primary" bindtap="confirmCreate">创建契约</button>
    </view>
  </view>
</view>

<!-- 授勋确认弹窗 -->
<view class="award-modal {{showAwardModal ? 'show' : ''}}" bindtap="hideAwardModal">
  <view class="modal-content award-content" catchtap="stopPropagation">
    <view class="award-animation">
      <view class="medal-icon">🏅</view>
      <view class="sparkles">✨</view>
    </view>
    <view class="award-text">
      <text class="award-main-text">授勋成功！</text>
      <text class="award-sub-text">{{currentChild.name}}获得了"坚持挑战者"荣誉勋章</text>
    </view>
    <view class="award-modal-footer">
      <button class="btn btn-primary" bindtap="completeAward">完成授勋</button>
    </view>
  </view>
</view>
