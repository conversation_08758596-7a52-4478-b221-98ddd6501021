/* Demo版本D：极简版 - 样式文件 */

.container {
  padding: 0;
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  padding: 50rpx 40rpx 60rpx;
  text-align: center;
  color: white;
  position: relative;
}

.task-title {
  font-size: 52rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.task-subtitle {
  font-size: 36rpx;
  margin-bottom: 24rpx;
  opacity: 0.95;
}

.header-highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  display: inline-block;
}

.highlight-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 通用区域样式 */
.core-value-section,
.video-section-minimal,
.simple-operation-section,
.action-section-large {
  background: white;
  margin: 30rpx 40rpx;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.section-title-minimal {
  display: flex;
  align-items: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  justify-content: center;
}

.section-title-minimal .icon {
  margin-right: 16rpx;
  font-size: 44rpx;
}

/* 核心价值区域 (35%) */
.core-value-section {
  margin-top: -40rpx;
  z-index: 10;
  position: relative;
  text-align: center;
}

.value-hero {
  margin-bottom: 50rpx;
}

.hero-number {
  font-size: 80rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.2);
}

.hero-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
}

.key-benefits {
  margin-bottom: 40rpx;
}

.benefit-row {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.benefit-simple {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff7f0;
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ff6b35;
}

.benefit-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.benefit-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.pain-solution {
  background: linear-gradient(135deg, #fff7f0 0%, #ffe8d6 100%);
  padding: 30rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ff6b35;
}

.pain-text {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
  text-align: center;
}

/* 关键视频区域 (20%) */
.featured-video-minimal {
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: #f8f9fa;
  border: 3rpx solid #ff6b35;
}

.video-thumbnail-featured {
  position: relative;
  height: 320rpx;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail-featured image {
  width: 100%;
  height: 100%;
}

.play-icon-featured {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80rpx;
  color: white;
  text-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
}

.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.video-info-featured {
  padding: 30rpx;
  text-align: center;
}

.video-title-featured {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.video-desc-featured {
  font-size: 28rpx;
  color: #666;
}

/* 文字列表形式的视频列表 */
.video-text-list-container {
  margin-top: 30rpx;
}

.video-scroll-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.video-scroll-wrapper::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 40rpx;
  background: linear-gradient(to right, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 5;
  pointer-events: none;
}

.video-scroll-wrapper::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40rpx;
  background: linear-gradient(to left, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 5;
  pointer-events: none;
}

.scroll-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.4);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: normal;
  z-index: 15;
  opacity: 0.8;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.scroll-indicator.left {
  left: 10rpx;
}

.scroll-indicator.right {
  right: 10rpx;
}

.video-text-list {
  white-space: nowrap;
  padding: 20rpx 2rpx;
  flex: 1;
}

.video-text-item {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  padding: 16rpx 4rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  vertical-align: top;
  transition: all 0.3s ease;
  position: relative;
}

.video-text-item:last-child {
  margin-right: 0;
}

.video-text-item.playing {
  background: linear-gradient(135deg, #fff7f0 0%, #ffe8d6 100%);
  border-color: #ff6b35;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.2);
}

.video-text-item:active {
  transform: scale(0.98);
}

.video-line-1 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.video-line-2 {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.playing-icon {
  font-size: 18rpx;
  margin-right: 6rpx;
  animation: pulse 2s infinite;
  flex-shrink: 0;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.video-number {
  font-size: 22rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-right: 6rpx;
  flex-shrink: 0;
}

.video-text-item.playing .video-number {
  color: #ff6b35;
}

.video-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-text-item.playing .video-title {
  color: #ff6b35;
}

.video-separator {
  font-size: 20rpx;
  color: #999;
  margin-right: 6rpx;
  flex-shrink: 0;
}

.video-text-item.playing .video-separator {
  color: #ff8c42;
}

.video-subtitle {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-text-item.playing .video-subtitle {
  color: #ff8c42;
}

.video-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ff6b35;
  background: #fff7f0;
  padding: 24rpx;
  border-radius: 16rpx;
  font-weight: 600;
  margin-top: 20rpx;
}

.video-indicator .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 简化操作说明区域 (30%) */
.steps-minimal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50rpx;
}

.step-minimal {
  flex: 1;
  text-align: center;
}

.step-icon-large {
  font-size: 64rpx;
  margin-bottom: 20rpx;
}

.step-text-large {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.step-arrow-large {
  font-size: 40rpx;
  color: #ff6b35;
  margin: 0 20rpx;
  font-weight: bold;
}

.time-promise {
  text-align: center;
  margin-bottom: 40rpx;
}

.promise-highlight {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 20rpx;
}

.promise-details {
  display: flex;
  justify-content: space-around;
  background: #fff7f0;
  padding: 20rpx;
  border-radius: 16rpx;
}

.promise-item {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.guarantee-simple {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
  font-weight: 500;
}

.guarantee-simple .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
  color: #ff6b35;
}

/* 社交功能按钮 - 极简版 */
.social-buttons-minimal {
  margin: 20rpx 40rpx;
  display: flex;
  gap: 20rpx;
}

.social-btn {
  flex: 1;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.social-btn:active {
  transform: scale(0.96);
}

.social-btn .btn-icon {
  font-size: 24rpx;
}

/* 分享按钮样式 */
.share-btn {
  border-color: #ff6b35;
  color: #ff6b35;
}

.share-btn:active {
  background: #fff7f0;
}

/* 微信群按钮样式 */
.wechat-btn {
  border-color: #4CAF50;
  color: #4CAF50;
}

.wechat-btn:active {
  background: #f1f8e9;
}

/* 大行动按钮区域 (15%) */
.action-section-large {
  text-align: center;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  color: white;
  margin-bottom: 0;
  border-radius: 24rpx 24rpx 0 0;
}

.urgency-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.urgency-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.urgency-text {
  font-size: 40rpx;
  font-weight: bold;
}

.join-button-large {
  background: white;
  color: #ff6b35;
  font-size: 42rpx;
  font-weight: bold;
  padding: 40rpx 60rpx;
  border-radius: 60rpx;
  border: none;
  margin-bottom: 30rpx;
  box-shadow: 0 12rpx 32rpx rgba(255, 107, 53, 0.3);
  transform: scale(1);
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 500rpx;
}

.join-button-large:active {
  transform: scale(0.96);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
}

.final-promise {
  font-size: 28rpx;
  opacity: 0.95;
  font-weight: 500;
  line-height: 1.4;
}
