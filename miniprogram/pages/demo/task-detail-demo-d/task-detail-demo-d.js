// Demo版本D：极简版 - 逻辑文件
Page({
  data: {
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: "让孩子从不会跳到连续100个",
      heroNumber: "0 → 100个",
      heroText: "21天技能飞跃",
      keyBenefits: [
        [
          { icon: "🎯", text: "掌握跳绳技能" },
          { icon: "💪", text: "养成运动习惯" },
        ],
        [
          { icon: "📈", text: "体育成绩提升" },
          { icon: "😊", text: "建立自信心" },
        ],
      ],
      painSolution: '解决"不会教、没方法、没效果"的困扰',
      featuredVideo: {
        id: 1,
        title: "跳绳基础到进阶完整教程",
        desc: "专业教练示范，家长也能教",
        thumbnail: "/images/app-logo.png",
        duration: "完整教程",
      },
      videoList: [
        {
          id: 1,
          title: "基础动作教学",
          subtitle: "正确握绳姿势与站立方法",
          duration: "3:20",
          isPlaying: false,
        },
        {
          id: 2,
          title: "基础动作教学",
          subtitle: "正确握绳姿势与站立方法",
          duration: "3:20",
          isPlaying: false,
        },
        {
          id: 3,
          title: "节奏训练技巧",
          subtitle: "找到适合的跳绳节奏感",
          duration: "4:15",
          isPlaying: true, // 当前正在播放
        },
        {
          id: 4,
          title: "连续跳绳练习",
          subtitle: "从几个到几十个的突破",
          duration: "5:30",
          isPlaying: false,
        },
        {
          id: 5,
          title: "技巧提升训练",
          subtitle: "常见错误纠正与进阶",
          duration: "6:45",
          isPlaying: false,
        },
        {
          id: 6,
          title: "综合测试",
          subtitle: "21天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 7,
          title: "综合测试",
          subtitle: "21天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 8,
          title: "综合测试",
          subtitle: "21天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 9,
          title: "综合测试",
          subtitle: "21天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 10,
          title: "综合测试",
          subtitle: "10天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 11,
          title: "综合测试",
          subtitle: "11天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
        {
          id: 12,
          title: "综合测试",
          subtitle: "12天成果检验",
          duration: "2:50",
          isPlaying: false,
        },
      ],
      currentVideoId: 3, // 当前选中的视频ID
      steps: [
        {
          icon: "📱",
          text: "看视频练习15分钟",
        },
        {
          icon: "✅",
          text: "打卡记录成长",
        },
      ],
      promises: [{ text: "可补打卡" }, { text: "有指导" }, { text: "有答疑" }],
    },
    canScrollLeft: false,
    canScrollRight: true,
  },

  onLoad: function (options) {
    console.log("Demo版本D：极简版页面加载");
    // 初始化滚动状态
    this.checkScrollStatus();
  },

  onReady: function () {
    // 页面渲染完成后检查滚动状态
    this.checkScrollStatus();
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const videoId = e.currentTarget.dataset.videoId;
    let video;

    if (videoId == 1) {
      video = this.data.taskInfo.featuredVideo;
    } else {
      video = this.data.taskInfo.videoList.find((v) => v.id == videoId);
    }

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${
        video.subtitle || video.desc || "专业教学视频"
      }`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 选择视频事件
  onVideoSelect: function (e) {
    const videoId = parseInt(e.currentTarget.dataset.videoId);

    // 更新当前选中的视频ID
    this.setData({
      "taskInfo.currentVideoId": videoId,
    });

    // 更新视频列表中的播放状态
    const videoList = this.data.taskInfo.videoList.map((video) => ({
      ...video,
      isPlaying: video.id === videoId,
    }));

    this.setData({
      "taskInfo.videoList": videoList,
    });

    // 可以在这里添加其他逻辑，比如自动播放等
    console.log("选中视频:", videoId);
  },

  // 视频列表滚动事件
  onVideoListScroll: function (e) {
    const { scrollLeft, scrollWidth } = e.detail;
    const query = wx.createSelectorQuery().in(this);
    query.select(".video-text-list").boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const containerWidth = res[0].width;

        this.setData({
          canScrollLeft: scrollLeft > 10, // 滚动超过10px才显示左侧指示器
          canScrollRight: scrollLeft < scrollWidth - containerWidth - 10, // 距离右边界超过10px才显示右侧指示器
        });
      }
    });
  },

  // 检查滚动状态
  checkScrollStatus: function () {
    setTimeout(() => {
      const query = wx.createSelectorQuery().in(this);
      query.select(".video-text-list").boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          const containerWidth = res[0].width;
          const videoCount = this.data.taskInfo.videoList.length;
          const itemWidth = 260; // 每个视频项的宽度(包含margin)
          const totalWidth = videoCount * itemWidth;

          this.setData({
            canScrollLeft: false, // 初始状态不显示左侧指示器
            canScrollRight: totalWidth > containerWidth, // 只有内容超出容器宽度才显示右侧指示器
          });
        }
      });
    }, 100); // 延迟100ms确保DOM渲染完成
  },

  // 加入任务事件
  onJoinTask: function () {
    wx.showModal({
      title: "立即开始挑战",
      content: "21天跳绳养成计划\n专业指导，科学训练，21天见效果",
      confirmText: "立即开始",
      cancelText: "再考虑",
      success: (res) => {
        if (res.confirm) {
          // 显示加入成功
          wx.showToast({
            title: "挑战开始！",
            icon: "success",
            duration: 2000,
          });

          // 模拟跳转流程
          setTimeout(() => {
            wx.showModal({
              title: "开始第一天",
              content: "现在开始观看教学视频，学习正确方法？",
              confirmText: "开始学习",
              cancelText: "稍后",
              success: (res2) => {
                if (res2.confirm) {
                  wx.showToast({
                    title: "即将播放教学视频",
                    icon: "none",
                    duration: 2000,
                  });
                }
              },
            });
          }, 2500);
        }
      },
    });
  },

  // 加入微信训练交流群
  onJoinWechatGroup: function () {
    wx.showModal({
      title: "加入专属训练交流群",
      content:
        "添加微信：jump_helper_2024\n\n备注「21天跳绳」，即可加入专属训练交流群，获得：\n\n🎁 个性化训练计划\n👥 1000+家长交流经验\n⏰ 每日训练提醒\n📊 专业进度分析",
      confirmText: "复制微信号",
      cancelText: "稍后再说",
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: "jump_helper_2024",
            success: () => {
              wx.showToast({
                title: "微信号已复制",
                icon: "success",
                duration: 2000,
              });

              // 提示用户下一步操作
              setTimeout(() => {
                wx.showModal({
                  title: "下一步",
                  content: "请打开微信，添加好友，粘贴微信号并备注「21天跳绳」",
                  showCancel: false,
                  confirmText: "知道了",
                });
              }, 2500);
            },
          });
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "21天跳绳养成计划 - 专业指导，21天见效果",
      desc: "让孩子从不会跳到连续100个，专业教练指导",
      path: "/pages/task-detail-demo-d/task-detail-demo-d",
      imageUrl: "/images/share-poster.jpg", // 如果有分享图片的话
    };
  },
});
