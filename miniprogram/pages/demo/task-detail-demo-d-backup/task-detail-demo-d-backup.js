// Demo版本D：极简版 - 逻辑文件
Page({
  data: {
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: "让孩子从不会跳到连续100个",
      heroNumber: "0 → 100个",
      heroText: "21天技能飞跃",
      keyBenefits: [
        [
          { icon: "🎯", text: "掌握跳绳技能" },
          { icon: "💪", text: "养成运动习惯" },
        ],
        [
          { icon: "📈", text: "体育成绩提升" },
          { icon: "😊", text: "建立自信心" },
        ],
      ],
      painSolution: '解决"不会教、没方法、没效果"的困扰',
      featuredVideo: {
        id: 1,
        title: "跳绳基础到进阶完整教程",
        desc: "专业教练示范，家长也能教",
        thumbnail: "/images/app-logo.png",
        duration: "完整教程",
      },
      steps: [
        {
          icon: "📱",
          text: "看视频学方法",
        },
        {
          icon: "🏃‍♂️",
          text: "陪孩子练15分钟",
        },
        {
          icon: "✅",
          text: "打卡记录进步",
        },
      ],
      promises: [{ text: "可补打卡" }, { text: "有指导" }, { text: "有答疑" }],
    },
  },

  onLoad: function (options) {
    console.log("Demo版本D：极简版页面加载");
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const video = this.data.taskInfo.featuredVideo;

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${video.desc}`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 加入任务事件
  onJoinTask: function () {
    wx.showModal({
      title: "立即开始挑战",
      content: "21天跳绳养成计划\n专业指导，科学训练，21天见效果",
      confirmText: "立即开始",
      cancelText: "再考虑",
      success: (res) => {
        if (res.confirm) {
          // 显示加入成功
          wx.showToast({
            title: "挑战开始！",
            icon: "success",
            duration: 2000,
          });

          // 模拟跳转流程
          setTimeout(() => {
            wx.showModal({
              title: "开始第一天",
              content: "现在开始观看教学视频，学习正确方法？",
              confirmText: "开始学习",
              cancelText: "稍后",
              success: (res2) => {
                if (res2.confirm) {
                  wx.showToast({
                    title: "即将播放教学视频",
                    icon: "none",
                    duration: 2000,
                  });
                }
              },
            });
          }, 2500);
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "21天跳绳养成计划 - 专业指导，21天见效果",
      path: "/pages/task-detail-demo-d/task-detail-demo-d",
    };
  },
});
