<!-- 分享海报生成页面 V4版本 -->
<view class="page-container">
  <!-- 海报类型选择 -->
  <view class="poster-types" wx:if="{{!selectedType}}">
    <view class="types-header">
      <text class="types-title">选择海报类型</text>
      <text class="types-subtitle">为你的成就制作专属海报</text>
    </view>
    
    <view class="type-list">
      <view class="type-item" bindtap="selectType" data-type="checkin">
        <view class="type-icon">📝</view>
        <view class="type-info">
          <text class="type-name">打卡成就海报</text>
          <text class="type-desc">展示今日打卡成果和积分奖励</text>
        </view>
        <view class="type-arrow">→</view>
      </view>
      
      <view class="type-item" bindtap="selectType" data-type="medal">
        <view class="type-icon">🏅</view>
        <view class="type-info">
          <text class="type-name">勋章获得海报</text>
          <text class="type-desc">庆祝获得新勋章的荣耀时刻</text>
        </view>
        <view class="type-arrow">→</view>
      </view>
      
      <view class="type-item" bindtap="selectType" data-type="ranking">
        <view class="type-icon">🏆</view>
        <view class="type-info">
          <text class="type-name">排行榜成绩海报</text>
          <text class="type-desc">分享你在排行榜上的优秀表现</text>
        </view>
        <view class="type-arrow">→</view>
      </view>
      
      <view class="type-item" bindtap="selectType" data-type="contract">
        <view class="type-icon">🤝</view>
        <view class="type-info">
          <text class="type-name">契约达成海报</text>
          <text class="type-desc">记录家庭荣誉契约的完成时刻</text>
        </view>
        <view class="type-arrow">→</view>
      </view>
      
      <view class="type-item" bindtap="selectType" data-type="progress">
        <view class="type-icon">📊</view>
        <view class="type-info">
          <text class="type-name">成长进度海报</text>
          <text class="type-desc">展示训练营的整体进度和成就</text>
        </view>
        <view class="type-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 海报预览和编辑 -->
  <view class="poster-editor" wx:if="{{selectedType}}">
    <!-- 顶部操作栏 -->
    <view class="editor-header">
      <view class="header-left">
        <button class="back-btn" bindtap="backToTypes">
          <text class="back-icon">←</text>
          <text class="back-text">重新选择</text>
        </button>
      </view>
      <view class="header-center">
        <text class="editor-title">{{posterConfig.title}}</text>
      </view>
      <view class="header-right">
        <button class="template-btn" bindtap="showTemplates">
          <text class="template-icon">🎨</text>
          <text class="template-text">模板</text>
        </button>
      </view>
    </view>

    <!-- 海报预览区域 -->
    <view class="poster-preview">
      <view class="preview-container">
        <!-- 海报画布 -->
        <canvas class="poster-canvas" 
                canvas-id="posterCanvas" 
                style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"></canvas>
        
        <!-- 加载状态 -->
        <view class="canvas-loading" wx:if="{{isGenerating}}">
          <view class="loading-spinner"></view>
          <text class="loading-text">生成海报中...</text>
        </view>
      </view>
      
      <!-- 预览操作 -->
      <view class="preview-actions">
        <button class="preview-btn" bindtap="previewPoster">
          <text class="btn-icon">👀</text>
          <text class="btn-text">预览</text>
        </button>
        <button class="refresh-btn" bindtap="regeneratePoster">
          <text class="btn-icon">🔄</text>
          <text class="btn-text">重新生成</text>
        </button>
      </view>
    </view>

    <!-- 自定义选项 -->
    <view class="customization-panel">
      <view class="panel-title">
        <text class="title-icon">⚙️</text>
        <text class="title-text">自定义设置</text>
      </view>
      
      <!-- 模板选择 -->
      <view class="custom-section">
        <text class="section-label">海报模板</text>
        <scroll-view class="template-scroll" scroll-x="true" show-scrollbar="false">
          <view class="template-item {{item.id === currentTemplate ? 'active' : ''}}" 
                wx:for="{{templates}}" 
                wx:key="id"
                bindtap="selectTemplate" 
                data-template="{{item}}">
            <view class="template-preview" style="background: {{item.bgColor}};">
              <text class="template-name">{{item.name}}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 文字自定义 -->
      <view class="custom-section" wx:if="{{posterConfig.allowTextEdit}}">
        <text class="section-label">个性文案</text>
        <textarea class="custom-text-input"
                  placeholder="{{posterConfig.textPlaceholder}}"
                  value="{{customText}}"
                  bindinput="onCustomTextInput"
                  maxlength="50"
                  show-confirm-bar="{{false}}" />
        <view class="text-count">{{customText.length}}/50</view>
      </view>
      
      <!-- 显示选项 -->
      <view class="custom-section">
        <text class="section-label">显示选项</text>
        <view class="option-list">
          <view class="option-item">
            <text class="option-label">显示二维码</text>
            <switch class="option-switch" 
                    checked="{{showQRCode}}" 
                    bindchange="onQRCodeToggle" />
          </view>
          <view class="option-item" wx:if="{{posterConfig.allowWatermark}}">
            <text class="option-label">添加水印</text>
            <switch class="option-switch" 
                    checked="{{showWatermark}}" 
                    bindchange="onWatermarkToggle" />
          </view>
        </view>
      </view>
    </view>

    <!-- 分享操作 -->
    <view class="share-actions">
      <button class="action-btn save-btn" bindtap="savePoster">
        <text class="btn-icon">💾</text>
        <text class="btn-text">保存到相册</text>
      </button>
      <button class="action-btn share-btn" bindtap="sharePoster">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享给好友</text>
      </button>
      <button class="action-btn moments-btn" bindtap="shareToMoments">
        <text class="btn-icon">📱</text>
        <text class="btn-text">分享到朋友圈</text>
      </button>
    </view>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 模板选择弹窗 -->
<view class="template-modal {{showTemplateModal ? 'show' : ''}}" bindtap="hideTemplates">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择海报模板</text>
      <view class="modal-close" bindtap="hideTemplates">×</view>
    </view>
    <view class="modal-body">
      <view class="template-grid">
        <view class="template-card {{item.id === currentTemplate ? 'active' : ''}}" 
              wx:for="{{templates}}" 
              wx:key="id"
              bindtap="selectTemplate" 
              data-template="{{item}}">
          <view class="card-preview" style="background: {{item.bgColor}};">
            <text class="card-name">{{item.name}}</text>
          </view>
          <view class="card-info">
            <text class="card-title">{{item.name}}</text>
            <text class="card-desc">{{item.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 预览弹窗 -->
<view class="preview-modal {{showPreviewModal ? 'show' : ''}}" bindtap="hidePreview">
  <view class="modal-content large" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">海报预览</text>
      <view class="modal-close" bindtap="hidePreview">×</view>
    </view>
    <view class="modal-body">
      <view class="preview-image-container">
        <image class="preview-image" 
               src="{{previewImageUrl}}" 
               mode="aspectFit" 
               wx:if="{{previewImageUrl}}" />
        <view class="preview-placeholder" wx:else>
          <text class="placeholder-text">预览生成中...</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="btn btn-outline" bindtap="hidePreview">关闭</button>
      <button class="btn btn-primary" bindtap="confirmPoster">确认使用</button>
    </view>
  </view>
</view>
