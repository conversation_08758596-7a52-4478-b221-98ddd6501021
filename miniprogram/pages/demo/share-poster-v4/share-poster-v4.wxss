/* 分享海报生成页面 V4版本样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 0 32rpx 120rpx;
}

/* 海报类型选择 */
.poster-types {
  padding: 32rpx 0;
}

.types-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.types-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
}

.types-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.type-list {
  
}

.type-item {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.type-item:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.type-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.type-arrow {
  font-size: 24rpx;
  color: #CCCCCC;
}

/* 海报编辑器 */
.poster-editor {
  padding: 32rpx 0;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 16rpx;
}

.header-left,
.header-right {
  width: 120rpx;
}

.header-center {
  flex: 1;
  text-align: center;
}

.back-btn,
.template-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 22rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.back-btn:active,
.template-btn:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.back-icon,
.template-icon {
  font-size: 20rpx;
}

.editor-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

/* 海报预览区域 */
.poster-preview {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.preview-container {
  position: relative;
  text-align: center;
  margin-bottom: 24rpx;
}

.poster-canvas {
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  background-color: #FFFFFF;
}

.canvas-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #F0F0F0;
  border-top: 4rpx solid #FF7A45;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #666666;
}

.preview-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.preview-btn,
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.preview-btn:active,
.refresh-btn:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.btn-icon {
  font-size: 20rpx;
}

/* 自定义面板 */
.customization-panel {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.title-icon {
  font-size: 28rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.custom-section {
  margin-bottom: 32rpx;
}

.custom-section:last-child {
  margin-bottom: 0;
}

.section-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

/* 模板滚动 */
.template-scroll {
  white-space: nowrap;
}

.template-item {
  display: inline-block;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.template-item:active {
  transform: scale(0.95);
}

.template-preview {
  width: 120rpx;
  height: 160rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #E5E5E5;
  transition: all 0.3s ease;
}

.template-item.active .template-preview {
  border-color: #FF7A45;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.template-name {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: 500;
}

/* 文字输入 */
.custom-text-input {
  width: 100%;
  min-height: 80rpx;
  padding: 16rpx;
  background-color: #F7F8FA;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333333;
}

.text-count {
  text-align: right;
  font-size: 20rpx;
  color: #999999;
  margin-top: 8rpx;
}

/* 选项列表 */
.option-list {
  
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-label {
  font-size: 26rpx;
  color: #333333;
}

.option-switch {
  transform: scale(0.8);
}

/* 分享操作 */
.share-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.save-btn {
  background: linear-gradient(135deg, #4A90E2, #5BA0F2);
  color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.share-btn {
  background: linear-gradient(135deg, #52C41A, #73D13D);
  color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
}

.moments-btn {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn .btn-icon {
  font-size: 28rpx;
}

.action-btn .btn-text {
  font-size: 22rpx;
}

/* 模板弹窗 */
.template-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.template-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.template-modal.show .modal-content {
  transform: scale(1);
}

.modal-content.large {
  max-width: 90%;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: #F0F0F0;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.template-card {
  border: 2rpx solid #E5E5E5;
  border-radius: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.template-card:active {
  transform: scale(0.95);
}

.template-card.active {
  border-color: #FF7A45;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.card-preview {
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-name {
  font-size: 24rpx;
  color: #FFFFFF;
  font-weight: 600;
}

.card-info {
  padding: 16rpx;
}

.card-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.card-desc {
  font-size: 20rpx;
  color: #666666;
}

/* 预览弹窗 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.preview-modal.show {
  opacity: 1;
  visibility: visible;
}

.preview-image-container {
  text-align: center;
  margin-bottom: 24rpx;
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  border-radius: 12rpx;
}

.preview-placeholder {
  width: 300rpx;
  height: 400rpx;
  background-color: #F0F0F0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999999;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F0F0F0;
}

.modal-footer .btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn.btn-outline {
  background-color: #F7F8FA;
  color: #666666;
  border: 1rpx solid #E5E5E5;
}

.btn.btn-outline:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.btn.btn-primary {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  color: #FFFFFF;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255, 122, 69, 0.3);
}

.btn.btn-primary:active {
  transform: scale(0.98);
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 120rpx;
}
