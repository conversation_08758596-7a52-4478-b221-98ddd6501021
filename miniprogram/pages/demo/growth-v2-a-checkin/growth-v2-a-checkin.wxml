<!-- 打卡表单页面 - 优化版 -->
<view class="page-container">
  <!-- 顶部导航栏 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="nav-title">今日打卡</view>
    <view class="nav-placeholder"></view>
  </view>
  <!-- 训练营信息 -->
  <view class="camp-header">
    <view class="camp-info">
      <text class="camp-title">🏃 {{campInfo.title}}</text>
      <text class="camp-day">第 {{campInfo.currentDay}} 天</text>
    </view>
    <view class="camp-progress">
      <text class="progress-text">{{campInfo.currentDay}}/{{campInfo.totalDays}}</text>
    </view>
  </view>
  <!-- 打卡表单 -->
  <view class="checkin-form">
    <!-- 练习时长 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-icon">⏰</text>
        <text class="title-text">练习时长</text>
        <text class="required">*</text>
      </view>
      <view class="duration-selector">
        <view class="duration-btn {{duration === 5 ? 'active' : ''}}" bindtap="selectDuration" data-duration="5">
          5分钟
        </view>
        <view class="duration-btn {{duration === 10 ? 'active' : ''}}" bindtap="selectDuration" data-duration="10">
          10分钟
        </view>
        <view class="duration-btn {{duration === 15 ? 'active' : ''}}" bindtap="selectDuration" data-duration="15">
          15分钟
        </view>
        <view class="duration-btn {{duration === 20 ? 'active' : ''}}" bindtap="selectDuration" data-duration="20">
          20分钟
        </view>
        <view class="duration-btn {{duration === 30 ? 'active' : ''}}" bindtap="selectDuration" data-duration="30">
          30分钟
        </view>
        <view class="duration-btn {{duration === 45 ? 'active' : ''}}" bindtap="selectDuration" data-duration="45">
          45分钟
        </view>
      </view>
      <!-- 自定义时长 -->
      <view class="custom-duration" wx:if="{{showCustomDuration}}">
        <text class="custom-label">其他:</text>
        <input class="custom-input" type="number" placeholder="分钟" value="{{customDuration}}" bindinput="onCustomDurationInput" />
        <text class="custom-unit">分钟</text>
      </view>
      <view class="custom-toggle" bindtap="toggleCustomDuration">
        <text class="toggle-text">{{showCustomDuration ? '收起' : '其他时长'}}</text>
      </view>
    </view>
    <!-- 跳绳成绩 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-icon">🏃‍♂️</text>
        <text class="title-text">跳绳成绩</text>
      </view>
      <view class="score-inputs">
        <view class="score-item">
          <text class="score-label">1分钟跳绳:</text>
          <input class="score-input" type="number" placeholder="个数" value="{{score1min}}" bindinput="onScore1minInput" />
          <text class="score-unit">个</text>
        </view>
        <view class="score-item">
          <text class="score-label">连续跳绳:</text>
          <input class="score-input" type="number" placeholder="个数" value="{{scoreContinuous}}" bindinput="onScoreContinuousInput" />
          <text class="score-unit">个</text>
        </view>
      </view>
    </view>
    <!-- 训练感受 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-icon">💭</text>
        <text class="title-text">训练感受</text>
      </view>
      <input class="feeling-input" placeholder="今天感觉怎么样？" value="{{feeling}}" bindinput="onFeelingInput" maxlength="100" />
      <text class="char-count">{{feeling.length}}/100</text>
    </view>
    <!-- 照片上传 -->
    <view class="form-section">
      <view class="section-title">
        <text class="title-icon">📷</text>
        <text class="title-text">训练照片</text>
        <text class="optional">选填</text>
      </view>
      <view class="photo-upload">
        <view class="photo-list">
          <image class="photo-item" src="{{item}}" wx:for="{{photos}}" wx:key="index" bindtap="previewPhoto" data-index="{{index}}" mode="aspectFill" />
          <view class="add-photo-btn" wx:if="{{photos.length < 3}}" bindtap="addPhoto">
            <text class="add-icon">📷</text>
            <text class="add-text">添加照片</text>
          </view>
        </view>
        <text class="photo-tip">最多可上传3张照片</text>
      </view>
    </view>
  </view>
  <!-- 激励信息 -->
  <view class="motivation-section">
    <view class="motivation-card">
      <view class="motivation-icon">🎉</view>
      <view class="motivation-content">
        <text class="motivation-title">坚持就是胜利！</text>
        <text class="motivation-text">完成打卡可获得 {{rewardPoints}} 积分</text>
      </view>
    </view>
  </view>
  <!-- 底部完成按钮 -->
  <view class="bottom-action">
    <button class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" bindtap="submitCheckin" disabled="{{!canSubmit || isSubmitting}}">
      {{isSubmitting ? '提交中...' : '完成打卡'}}
    </button>
  </view>
  <!-- 打卡成功弹窗 -->
  <view class="success-modal {{showSuccessModal ? 'show' : ''}}" wx:if="{{showSuccessModal}}">
    <view class="modal-content">
      <view class="success-animation">
        <text class="success-icon">🎉</text>
        <text class="success-title">打卡成功！</text>
      </view>
      <!-- 获得奖励 -->
      <view class="rewards-earned">
        <text class="rewards-title">获得奖励</text>
        <view class="reward-items">
          <view class="reward-item">
            <text class="reward-icon">🪙</text>
            <text class="reward-text">+{{rewardPoints}} 积分</text>
          </view>
          <view class="reward-item" wx:if="{{earnedMedal}}">
            <text class="reward-icon">🏅</text>
            <text class="reward-text">{{earnedMedal.name}}</text>
          </view>
        </view>
      </view>
      <!-- 契约进度更新 -->
      <view class="contract-update" wx:if="{{hasContract}}">
        <text class="contract-title">家庭荣誉契约</text>
        <view class="contract-progress">
          <text class="progress-text">
            {{contractProgress.current}}/{{contractProgress.total}} 天
          </text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{contractProgress.percent}}%"></view>
          </view>
        </view>
        <text class="contract-message" wx:if="{{contractProgress.isCompleted}}">
          🎊 契约完成！等待家长授勋
        </text>
        <text class="contract-message" wx:else>
          💪 继续加油，距离完成还有 {{contractProgress.remaining}} 天
        </text>
      </view>
      <!-- 操作按钮 -->
      <view class="modal-actions">
        <button class="action-btn secondary" bindtap="closeSuccessModal">继续训练</button>
        <button class="action-btn primary" bindtap="generatePoster">生成海报</button>
      </view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>