// 方案A - 打卡表单页面
Page({
  data: {
    // 训练营信息
    campInfo: {
      id: 1,
      title: "21天跳绳养成计划",
      currentDay: 14,
      totalDays: 21,
    },

    // 表单数据
    duration: 15, // 练习时长
    showCustomDuration: false, // 是否显示自定义时长
    customDuration: "", // 自定义时长
    score1min: "", // 1分钟跳绳成绩
    scoreContinuous: "", // 连续跳绳成绩
    feeling: "", // 训练感受
    photos: [], // 训练照片

    // 奖励积分
    rewardPoints: 25,

    // 是否有契约
    hasContract: true,

    // 契约进度
    contractProgress: {
      current: 14,
      total: 21,
      percent: 67,
      remaining: 7,
      isCompleted: false,
    },

    // 表单验证
    canSubmit: true,
    isSubmitting: false,

    // 成功弹窗
    showSuccessModal: false,
    earnedMedal: null, // 获得的勋章
  },

  onLoad(options) {
    console.log("方案A打卡表单页面加载");
    const campId = options.campId;
    if (campId) {
      this.loadCampInfo(campId);
    }
    this.validateForm();
  },

  /**
   * 加载训练营信息
   */
  loadCampInfo(campId) {
    // 模拟加载训练营信息
    console.log("加载训练营信息:", campId);
  },

  /**
   * 选择练习时长
   */
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    this.setData({
      duration,
      showCustomDuration: false, // 选择预设时长时隐藏自定义输入
    });
    this.validateForm();
  },

  /**
   * 切换自定义时长显示
   */
  toggleCustomDuration() {
    this.setData({
      showCustomDuration: !this.data.showCustomDuration,
    });
  },

  /**
   * 自定义时长输入
   */
  onCustomDurationInput(e) {
    const customDuration = e.detail.value;
    this.setData({
      customDuration,
      duration: customDuration ? parseInt(customDuration) : 0,
    });
    this.validateForm();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 1分钟跳绳成绩输入
   */
  onScore1minInput(e) {
    this.setData({
      score1min: e.detail.value,
    });
  },

  /**
   * 连续跳绳成绩输入
   */
  onScoreContinuousInput(e) {
    this.setData({
      scoreContinuous: e.detail.value,
    });
  },

  /**
   * 训练感受输入
   */
  onFeelingInput(e) {
    this.setData({
      feeling: e.detail.value,
    });
  },

  /**
   * 添加照片
   */
  addPhoto() {
    const that = this;
    wx.chooseImage({
      count: 3 - this.data.photos.length,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const photos = [...this.data.photos, ...res.tempFilePaths];
        that.setData({ photos });
      },
    });
  },

  /**
   * 预览照片
   */
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.photos[index],
      urls: this.data.photos,
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { duration } = this.data;
    const canSubmit = duration > 0;
    this.setData({ canSubmit });
  },

  /**
   * 提交打卡
   */
  submitCheckin() {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return;
    }

    this.setData({ isSubmitting: true });

    // 模拟提交打卡数据
    const checkinData = {
      campId: this.data.campInfo.id,
      duration: this.data.duration,
      score1min: this.data.score1min,
      scoreContinuous: this.data.scoreContinuous,
      feeling: this.data.feeling,
      photos: this.data.photos,
      timestamp: new Date().getTime(),
    };

    console.log("提交打卡数据:", checkinData);

    // 模拟网络请求
    setTimeout(() => {
      this.handleCheckinSuccess();
    }, 1500);
  },

  /**
   * 处理打卡成功
   */
  handleCheckinSuccess() {
    // 保存打卡状态
    const today = new Date().toDateString();
    wx.setStorageSync("lastCheckinDate_growth_v2_a", today);

    // 更新契约进度
    let contractProgress = { ...this.data.contractProgress };
    if (this.data.hasContract) {
      contractProgress.current += 1;
      contractProgress.percent = Math.round(
        (contractProgress.current / contractProgress.total) * 100
      );
      contractProgress.remaining =
        contractProgress.total - contractProgress.current;
      contractProgress.isCompleted =
        contractProgress.current >= contractProgress.total;
    }

    // 检查是否获得勋章
    let earnedMedal = null;
    if (contractProgress.current === 7) {
      earnedMedal = { name: "坚持一周", icon: "🏅" };
    } else if (contractProgress.current === 14) {
      earnedMedal = { name: "坚持两周", icon: "🏆" };
    } else if (contractProgress.isCompleted) {
      earnedMedal = { name: "契约守护者", icon: "👑" };
    }

    this.setData({
      isSubmitting: false,
      showSuccessModal: true,
      contractProgress,
      earnedMedal,
    });

    // 触发页面刷新
    const pages = getCurrentPages();
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
      if (prevPage.route.includes("growth-v2-a-detail")) {
        prevPage.setData({
          todayStatus: "completed",
        });
      }
    }
  },

  /**
   * 关闭成功弹窗
   */
  closeSuccessModal() {
    this.setData({
      showSuccessModal: false,
    });

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 300);
  },

  /**
   * 生成海报
   */
  generatePoster() {
    this.setData({
      showSuccessModal: false,
    });

    // 跳转到海报页面
    wx.navigateTo({
      url: `/pages/demo/growth-v2-a-poster/growth-v2-a-poster?type=checkin&campId=${this.data.campInfo.id}&day=${this.data.campInfo.currentDay}`,
    });
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    // 清理定时器等资源
  },
});
