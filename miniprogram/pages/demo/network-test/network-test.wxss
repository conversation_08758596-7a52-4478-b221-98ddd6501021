/* 网络连接测试页面样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
  padding: var(--spacing-lg);
}

.test-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.test-title {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.test-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

/* 连接状态 */
.connection-status {
  margin-bottom: var(--spacing-xl);
}

.status-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid transparent;
}

.status-card.connected {
  border-left-color: var(--success-color);
  background: linear-gradient(135deg, #F6FFED 0%, #FFFFFF 100%);
}

.status-card.connecting {
  border-left-color: var(--primary-color);
  background: linear-gradient(135deg, #FFF2E8 0%, #FFFFFF 100%);
}

.status-card.error {
  border-left-color: var(--error-color);
  background: linear-gradient(135deg, #FFEBEE 0%, #FFFFFF 100%);
}

.status-icon {
  font-size: var(--font-xxl);
  margin-right: var(--spacing-md);
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 测试按钮 */
.test-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.test-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-lg);
  font-size: var(--font-md);
  font-weight: 600;
  border: none;
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.test-btn.secondary {
  background-color: #FFFFFF;
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.test-btn[disabled] {
  background-color: #F5F5F5;
  color: var(--text-placeholder);
}

/* 服务器信息 */
.server-info {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.info-value {
  font-size: var(--font-md);
  color: var(--text-primary);
  font-weight: 600;
}

/* 测试结果 */
.test-results {
  margin-bottom: var(--spacing-xl);
}

.results-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.result-item {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid transparent;
}

.result-item.success {
  border-left-color: var(--success-color);
}

.result-item.error {
  border-left-color: var(--error-color);
}

.result-time {
  font-size: var(--font-xs);
  color: var(--text-placeholder);
  margin-right: var(--spacing-md);
  min-width: 80rpx;
}

.result-content {
  flex: 1;
}

.result-title {
  display: block;
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.result-message {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  line-height: 1.4;
}

.result-status {
  font-size: var(--font-md);
}

/* 故障排除 */
.troubleshooting {
  background-color: #F0F9FF;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1rpx solid #E0F2FE;
}

.trouble-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--info-color);
  margin-bottom: var(--spacing-md);
}

.trouble-steps {
  
}

.trouble-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.trouble-step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--info-color);
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-sm);
  font-weight: 600;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.step-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
  line-height: 1.5;
  padding-top: var(--spacing-xs);
}
