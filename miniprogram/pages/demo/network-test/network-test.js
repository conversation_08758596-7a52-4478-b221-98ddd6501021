// 网络连接测试页面
const config = require('../../../utils/config');
const request = require('../../../utils/request');

Page({
  data: {
    // 连接状态
    connectionStatus: 'unknown', // unknown | connecting | connected | error
    statusTitle: '未知状态',
    statusDesc: '点击测试连接按钮检查服务器状态',
    
    // 服务器信息
    apiBaseUrl: config.API_BASE_URL,
    environment: config.DEBUG ? 'development' : 'production',
    debugMode: config.DEBUG,
    
    // 测试状态
    isLoading: false,
    
    // 测试结果
    testResults: []
  },

  onLoad(options) {
    console.log('网络测试页面加载');
    // 页面加载时自动测试一次连接
    this.testConnection();
  },

  /**
   * 测试连接
   */
  async testConnection() {
    this.setData({
      isLoading: true,
      connectionStatus: 'connecting',
      statusTitle: '连接中...',
      statusDesc: '正在测试与API服务器的连接'
    });

    try {
      // 简单的连接测试
      const response = await request.get('/health');
      
      this.setData({
        connectionStatus: 'connected',
        statusTitle: '连接成功',
        statusDesc: `服务器响应正常，延迟: ${Date.now() - this.startTime}ms`
      });

      this.addTestResult('连接测试', '成功连接到API服务器', 'success');
      
    } catch (error) {
      console.error('连接测试失败:', error);
      
      this.setData({
        connectionStatus: 'error',
        statusTitle: '连接失败',
        statusDesc: error.message || '无法连接到API服务器'
      });

      this.addTestResult('连接测试', error.message || '连接失败', 'error');
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  /**
   * 健康检查
   */
  async testHealthCheck() {
    this.setData({
      isLoading: true
    });

    try {
      const startTime = Date.now();
      const response = await request.get('/health');
      const endTime = Date.now();
      
      const message = `服务器健康状态正常，响应时间: ${endTime - startTime}ms`;
      this.addTestResult('健康检查', message, 'success');
      
      wx.showToast({
        title: '健康检查通过',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('健康检查失败:', error);
      
      this.addTestResult('健康检查', error.message || '健康检查失败', 'error');
      
      wx.showToast({
        title: '健康检查失败',
        icon: 'error'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(title, message, status) {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    
    const newResult = {
      timestamp: Date.now(),
      time: time,
      title: title,
      message: message,
      status: status
    };
    
    const results = [newResult, ...this.data.testResults.slice(0, 9)]; // 保留最近10条记录
    
    this.setData({
      testResults: results
    });
  }
});
