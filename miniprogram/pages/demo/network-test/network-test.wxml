<!-- 网络连接测试页面 -->
<view class="page-container">
  <view class="test-header">
    <text class="test-title">网络连接测试</text>
    <text class="test-subtitle">检查API服务器连接状态</text>
  </view>

  <view class="connection-status">
    <view class="status-card {{connectionStatus}}">
      <view class="status-icon">
        <text wx:if="{{connectionStatus === 'connected'}}">✅</text>
        <text wx:elif="{{connectionStatus === 'connecting'}}">🔄</text>
        <text wx:else="{{connectionStatus === 'error'}}">❌</text>
      </view>
      <view class="status-info">
        <text class="status-title">{{statusTitle}}</text>
        <text class="status-desc">{{statusDesc}}</text>
      </view>
    </view>
  </view>

  <view class="test-actions">
    <button class="test-btn" bindtap="testConnection" disabled="{{isLoading}}">
      {{isLoading ? '测试中...' : '测试连接'}}
    </button>
    <button class="test-btn secondary" bindtap="testHealthCheck" disabled="{{isLoading}}">
      健康检查
    </button>
  </view>

  <view class="server-info">
    <view class="info-title">服务器信息</view>
    <view class="info-item">
      <text class="info-label">API地址:</text>
      <text class="info-value">{{apiBaseUrl}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">环境:</text>
      <text class="info-value">{{environment}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">调试模式:</text>
      <text class="info-value">{{debugMode ? '开启' : '关闭'}}</text>
    </view>
  </view>

  <view class="test-results" wx:if="{{testResults.length > 0}}">
    <view class="results-title">测试结果</view>
    <view class="result-item {{item.status}}" wx:for="{{testResults}}" wx:key="timestamp">
      <view class="result-time">{{item.time}}</view>
      <view class="result-content">
        <text class="result-title">{{item.title}}</text>
        <text class="result-message">{{item.message}}</text>
      </view>
      <view class="result-status">
        <text wx:if="{{item.status === 'success'}}">✅</text>
        <text wx:else>❌</text>
      </view>
    </view>
  </view>

  <view class="troubleshooting">
    <view class="trouble-title">故障排除</view>
    <view class="trouble-steps">
      <view class="trouble-step">
        <text class="step-number">1</text>
        <text class="step-text">确保API服务器已启动 (make start SERVICE=api)</text>
      </view>
      <view class="trouble-step">
        <text class="step-number">2</text>
        <text class="step-text">检查服务器状态 (make status)</text>
      </view>
      <view class="trouble-step">
        <text class="step-number">3</text>
        <text class="step-text">确认端口8080未被占用</text>
      </view>
      <view class="trouble-step">
        <text class="step-number">4</text>
        <text class="step-text">检查微信开发工具网络设置</text>
      </view>
    </view>
  </view>
</view>
