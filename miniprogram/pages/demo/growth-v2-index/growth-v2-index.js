// 成长栏目重构方案导航页面
Page({
  data: {
    // 页面数据
  },

  onLoad(options) {
    console.log("成长栏目重构方案导航页面加载");
  },

  /**
   * 跳转到指定方案
   */
  goToScheme(e) {
    const scheme = e.currentTarget.dataset.scheme;

    // 根据方案跳转到对应的主页面
    const schemePages = {
      a: "/pages/demo/growth-v2-a-main/growth-v2-a-main",
    };

    const targetPage = schemePages[scheme];

    if (targetPage) {
      wx.navigateTo({
        url: targetPage,
      });
    } else {
      wx.showToast({
        title: "页面不存在",
        icon: "none",
      });
    }
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: "成长栏目重构方案 - 简洁任务导向型",
      path: "/pages/demo/growth-v2-index/growth-v2-index",
      imageUrl: "/images/share_growth_v2.jpg",
    };
  },
});
