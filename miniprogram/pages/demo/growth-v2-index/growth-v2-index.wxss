/* 成长栏目重构方案导航页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666666;
}

/* 方案列表 */
.schemes-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.scheme-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.scheme-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.scheme-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.scheme-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-right: 16rpx;
}

.scheme-badge.scheme-a {
  background-color: #FF7A45;
}

.scheme-badge.scheme-b {
  background-color: #4A90E2;
}

.scheme-badge.scheme-c {
  background-color: #52C41A;
}

.scheme-badge.scheme-d {
  background-color: #722ED1;
}

.scheme-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.scheme-content {
  margin-bottom: 32rpx;
}

.scheme-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  display: block;
}

.scheme-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag {
  background-color: #F7F8FA;
  color: #666666;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid #E5E5E5;
}

.scheme-preview {
  border-top: 1rpx solid #F0F0F0;
  padding-top: 24rpx;
}

.preview-screens {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}

.screen-item {
  background-color: #F7F8FA;
  color: #666666;
  font-size: 24rpx;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  text-align: center;
  border: 1rpx solid #E5E5E5;
}

/* 设计说明 */
.design-notes {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.notes-header {
  margin-bottom: 24rpx;
}

.notes-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.notes-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.note-item {
  padding: 24rpx;
  background-color: #F7F8FA;
  border-radius: 16rpx;
  border-left: 6rpx solid #FF7A45;
}

.note-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.note-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
}

/* 功能对比 */
.comparison-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.comparison-header {
  margin-bottom: 24rpx;
}

.comparison-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.comparison-table {
  display: flex;
  flex-direction: column;
  border: 1rpx solid #E5E5E5;
  border-radius: 12rpx;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 1.2fr 1fr 1fr 1fr 1fr;
  background-color: #F7F8FA;
}

.table-row {
  display: grid;
  grid-template-columns: 1.2fr 1fr 1fr 1fr 1fr;
  border-top: 1rpx solid #E5E5E5;
}

.header-cell {
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  border-right: 1rpx solid #E5E5E5;
}

.header-cell:last-child {
  border-right: none;
}

.cell {
  padding: 20rpx 16rpx;
  font-size: 24rpx;
  color: #666666;
  text-align: center;
  border-right: 1rpx solid #E5E5E5;
}

.cell:first-child {
  font-weight: 500;
  color: #333333;
  text-align: left;
}

.cell:last-child {
  border-right: none;
}

/* 底部提示 */
.bottom-tips {
  margin-bottom: 32rpx;
}

.tip-card {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  align-items: flex-start;
  color: #FFFFFF;
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
}

.tip-desc {
  font-size: 28rpx;
  line-height: 1.6;
  opacity: 0.9;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
