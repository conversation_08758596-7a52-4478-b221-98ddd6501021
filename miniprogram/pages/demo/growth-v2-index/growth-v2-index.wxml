<!-- 成长栏目重构方案导航页面 -->
<view class="page-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">成长栏目重构方案</text>
    <text class="page-subtitle">简洁任务导向型完整设计方案</text>
  </view>
  <!-- 方案列表 -->
  <view class="schemes-list">
    <!-- 方案A -->
    <view class="scheme-card" bindtap="goToScheme" data-scheme="a">
      <view class="scheme-header">
        <view class="scheme-badge scheme-a">方案A</view>
        <text class="scheme-title">简洁任务导向型</text>
      </view>
      <view class="scheme-content">
        <text class="scheme-desc">极简设计，突出任务完成和契约进度，大卡片布局，信息层次清晰</text>
        <view class="scheme-features">
          <text class="feature-tag">✨ 家庭荣誉契约置顶</text>
          <text class="feature-tag">🎯 一键操作流程</text>
          <text class="feature-tag">📊 清晰进度展示</text>
        </view>
      </view>
      <view class="scheme-preview">
        <view class="preview-screens">
          <view class="screen-item">📱 成长主页</view>
          <view class="screen-item">📋 打卡详情</view>
          <view class="screen-item">✍️ 打卡表单</view>
          <view class="screen-item">🏆 排行榜</view>
          <view class="screen-item">🤝 荣誉契约</view>
          <view class="screen-item">🎨 分享海报</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 设计说明 -->
  <view class="design-notes">
    <view class="notes-header">
      <text class="notes-title">🎨 设计说明</text>
    </view>
    <view class="notes-content">
      <view class="note-item">
        <text class="note-title">家庭荣誉契约融入</text>
        <text class="note-desc">深度融入了家庭荣誉契约功能，通过简洁任务导向的设计风格展现契约的情感价值</text>
      </view>
      <view class="note-item">
        <text class="note-title">完整页面体系</text>
        <text class="note-desc">包含6个完整页面：成长主页(tab结构)、打卡详情、打卡表单、排行榜、荣誉契约、分享海报</text>
      </view>
      <view class="note-item">
        <text class="note-title">统一设计规范</text>
        <text class="note-desc">遵循项目统一的色彩规范：主色橙色#FF7A45，辅助色青蓝#4A90E2，背景色米白#F7F8FA</text>
      </view>
      <view class="note-item">
        <text class="note-title">MVS架构规则</text>
        <text class="note-desc">严格遵循Model-View-Service架构规则，模块化组件设计，统一的数据模拟和状态管理</text>
      </view>
    </view>
  </view>
  <!-- 底部提示 -->
  <view class="bottom-tips">
    <view class="tip-card">
      <text class="tip-icon">💡</text>
      <view class="tip-content">
        <text class="tip-title">使用提示</text>
        <text class="tip-desc">点击方案卡片可以体验完整的页面流程，包含完整的交互功能和模拟数据</text>
      </view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>