<!-- 方案A - 打卡详情页面 -->
<view class="page-container">
  <!-- 训练营信息区 -->
  <view class="camp-info-section">
    <view class="camp-header">
      <view class="camp-basic-info">
        <text class="camp-title">{{campInfo.title}}</text>
        <text class="camp-subtitle">{{campInfo.subtitle}}</text>
      </view>
      <view class="camp-progress-circle">
        <view class="progress-inner">
          <text class="progress-text">{{campInfo.progressPercent}}%</text>
        </view>
      </view>
    </view>
    <view class="camp-stats">
      <view class="stat-item">
        <text class="stat-value">{{campInfo.currentDay}}</text>
        <text class="stat-label">当前天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{campInfo.totalDays}}</text>
        <text class="stat-label">总天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{campInfo.streakDays}}</text>
        <text class="stat-label">连续天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{campInfo.totalPoints}}</text>
        <text class="stat-label">获得积分</text>
      </view>
    </view>
  </view>

  <!-- 家庭荣誉契约卡片 -->
  <view class="contract-section" wx:if="{{hasContract}}">
    <view class="section-title">
      <text class="title-icon">🏆</text>
      <text class="title-text">家庭荣誉契约</text>
      <text class="contract-status">{{contractInfo.status}}</text>
    </view>
    <view class="contract-card" bindtap="goToContract">
      <view class="contract-header">
        <view class="contract-info">
          <text class="contract-title">{{contractInfo.title}}</text>
          <text class="contract-desc">{{contractInfo.description}}</text>
        </view>
        <view class="contract-progress">
          <text class="progress-text">{{contractInfo.currentDay}}/{{contractInfo.totalDays}}</text>
        </view>
      </view>
      <view class="contract-reward">
        <text class="reward-label">约定奖励：</text>
        <text class="reward-text">{{contractInfo.reward}}</text>
      </view>
      <view class="contract-progress-bar">
        <view class="progress-bg">
          <view class="progress-fill" style="width: {{contractInfo.progress}}%"></view>
        </view>
        <text class="progress-percent">{{contractInfo.progress}}%</text>
      </view>
    </view>
  </view>

  <!-- 相关视频列表 -->
  <view class="videos-section">
    <view class="section-title" bindtap="toggleVideos">
      <text class="title-icon">📹</text>
      <text class="title-text">相关视频</text>
      <text class="toggle-icon">{{videosExpanded ? '▲' : '▼'}}</text>
    </view>
    <view class="videos-list" wx:if="{{videosExpanded}}">
      <view class="video-item" wx:for="{{videosList}}" wx:key="id" bindtap="playVideo" data-video="{{item}}">
        <image class="video-thumb" src="{{item.thumbnail}}" mode="aspectFill" />
        <view class="video-info">
          <text class="video-title">{{item.title}}</text>
          <text class="video-duration">{{item.duration}}</text>
        </view>
        <view class="video-status">
          <text wx:if="{{item.watched}}">✅</text>
          <text wx:else>▶️</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 打卡进度日历 -->
  <view class="calendar-section">
    <view class="section-title" bindtap="toggleCalendar">
      <text class="title-icon">📅</text>
      <text class="title-text">打卡日历</text>
      <text class="toggle-icon">{{calendarExpanded ? '▲' : '▼'}}</text>
    </view>
    <view class="calendar-container">
      <!-- 当前周显示 -->
      <view class="week-view" wx:if="{{!calendarExpanded}}">
        <view class="week-day {{item.status}}" wx:for="{{currentWeek}}" wx:key="date">
          <text class="day-name">{{item.dayName}}</text>
          <view class="day-status">
            <text wx:if="{{item.status === 'completed'}}">✅</text>
            <text wx:elif="{{item.status === 'today'}}">📅</text>
            <text wx:elif="{{item.status === 'future'}}">⭕</text>
            <text wx:else>❌</text>
          </view>
          <text class="day-date">{{item.date}}</text>
        </view>
      </view>
      <!-- 完整日历显示 -->
      <view class="full-calendar" wx:if="{{calendarExpanded}}">
        <view class="calendar-month" wx:for="{{calendarData}}" wx:key="month">
          <text class="month-title">{{item.monthName}}</text>
          <view class="calendar-grid">
            <view class="calendar-day {{day.status}}" wx:for="{{item.days}}" wx:for-item="day" wx:key="date">
              <text class="day-number">{{day.day}}</text>
              <view class="day-indicator">
                <text wx:if="{{day.status === 'completed'}}">✅</text>
                <text wx:elif="{{day.status === 'today'}}">📅</text>
                <text wx:elif="{{day.status === 'missed'}}">❌</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 每日奖励 -->
  <view class="rewards-section">
    <view class="section-title">
      <text class="title-icon">🎁</text>
      <text class="title-text">每日奖励</text>
    </view>
    <scroll-view class="rewards-scroll" scroll-x="true" show-scrollbar="false">
      <view class="rewards-list">
        <view class="reward-item {{item.status}}" wx:for="{{dailyRewards}}" wx:key="day" bindtap="claimReward" data-reward="{{item}}">
          <view class="reward-day">第{{item.day}}天</view>
          <view class="reward-icon">{{item.icon}}</view>
          <text class="reward-name">{{item.name}}</text>
          <view class="reward-status-badge">
            <text wx:if="{{item.status === 'claimed'}}">已领取</text>
            <text wx:elif="{{item.status === 'available'}}">可领取</text>
            <text wx:else>未解锁</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 补卡信息 -->
  <view class="makeup-section" wx:if="{{hasMissedDays}}">
    <view class="section-title">
      <text class="title-icon">🔄</text>
      <text class="title-text">补卡机会</text>
    </view>
    <view class="makeup-info">
      <text class="makeup-text">你还有 {{makeupChances}} 次补卡机会</text>
      <button class="makeup-btn" bindtap="showMakeupOptions">立即补卡</button>
    </view>
  </view>

  <!-- 底部打卡按钮 -->
  <view class="bottom-action">
    <button class="checkin-btn {{todayStatus}}" bindtap="goToCheckin" disabled="{{todayStatus === 'completed'}}">
      <text wx:if="{{todayStatus === 'completed'}}">✅ 今日已打卡</text>
      <text wx:elif="{{todayStatus === 'pending'}}">📝 立即打卡</text>
      <text wx:else>❌ 今日已错过</text>
    </button>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
