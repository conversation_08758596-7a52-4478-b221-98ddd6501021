// 方案A - 打卡详情页面
Page({
  data: {
    // 训练营信息
    campInfo: {
      id: 1,
      title: '21天跳绳养成计划',
      subtitle: '基础跳绳技能训练',
      currentDay: 14,
      totalDays: 21,
      progressPercent: 67,
      streakDays: 12,
      totalPoints: 420
    },

    // 今日状态
    todayStatus: 'pending', // pending, completed, missed

    // 是否有契约
    hasContract: true,

    // 契约信息
    contractInfo: {
      title: '21天跳绳挑战契约',
      description: '连续打卡21天，掌握跳绳技能',
      status: '进行中',
      currentDay: 14,
      totalDays: 21,
      progress: 67,
      reward: '一次科技馆之旅'
    },

    // 视频展开状态
    videosExpanded: false,

    // 相关视频列表
    videosList: [
      { id: 1, title: '跳绳基础动作教学', duration: '5:30', thumbnail: '/images/video_thumb1.jpg', watched: true },
      { id: 2, title: '跳绳节奏训练', duration: '8:15', thumbnail: '/images/video_thumb2.jpg', watched: true },
      { id: 3, title: '花样跳绳技巧', duration: '6:45', thumbnail: '/images/video_thumb3.jpg', watched: false },
      { id: 4, title: '跳绳常见错误纠正', duration: '4:20', thumbnail: '/images/video_thumb4.jpg', watched: false }
    ],

    // 日历展开状态
    calendarExpanded: false,

    // 当前周数据
    currentWeek: [
      { dayName: '周一', date: '15', status: 'completed' },
      { dayName: '周二', date: '16', status: 'completed' },
      { dayName: '周三', date: '17', status: 'completed' },
      { dayName: '周四', date: '18', status: 'missed' },
      { dayName: '周五', date: '19', status: 'completed' },
      { dayName: '周六', date: '20', status: 'completed' },
      { dayName: '周日', date: '21', status: 'today' }
    ],

    // 完整日历数据
    calendarData: [
      {
        monthName: '2024年1月',
        days: [
          { day: 1, date: '2024-01-01', status: 'completed' },
          { day: 2, date: '2024-01-02', status: 'completed' },
          { day: 3, date: '2024-01-03', status: 'missed' },
          { day: 4, date: '2024-01-04', status: 'completed' },
          { day: 5, date: '2024-01-05', status: 'completed' },
          { day: 6, date: '2024-01-06', status: 'completed' },
          { day: 7, date: '2024-01-07', status: 'completed' },
          { day: 8, date: '2024-01-08', status: 'completed' },
          { day: 9, date: '2024-01-09', status: 'completed' },
          { day: 10, date: '2024-01-10', status: 'completed' },
          { day: 11, date: '2024-01-11', status: 'completed' },
          { day: 12, date: '2024-01-12', status: 'completed' },
          { day: 13, date: '2024-01-13', status: 'completed' },
          { day: 14, date: '2024-01-14', status: 'completed' },
          { day: 15, date: '2024-01-15', status: 'completed' },
          { day: 16, date: '2024-01-16', status: 'completed' },
          { day: 17, date: '2024-01-17', status: 'completed' },
          { day: 18, date: '2024-01-18', status: 'missed' },
          { day: 19, date: '2024-01-19', status: 'completed' },
          { day: 20, date: '2024-01-20', status: 'completed' },
          { day: 21, date: '2024-01-21', status: 'today' }
        ]
      }
    ],

    // 每日奖励
    dailyRewards: [
      { day: 1, icon: '🪙', name: '金币', status: 'claimed' },
      { day: 2, icon: '💎', name: '宝石', status: 'claimed' },
      { day: 3, icon: '🎁', name: '礼包', status: 'claimed' },
      { day: 4, icon: '🏆', name: '奖杯', status: 'claimed' },
      { day: 5, icon: '⭐', name: '星星', status: 'claimed' },
      { day: 6, icon: '🎖️', name: '勋章', status: 'claimed' },
      { day: 7, icon: '👑', name: '皇冠', status: 'claimed' },
      { day: 8, icon: '🔥', name: '火焰', status: 'claimed' },
      { day: 9, icon: '💪', name: '力量', status: 'claimed' },
      { day: 10, icon: '🌟', name: '闪星', status: 'claimed' },
      { day: 11, icon: '🎯', name: '目标', status: 'claimed' },
      { day: 12, icon: '🚀', name: '火箭', status: 'claimed' },
      { day: 13, icon: '💫', name: '流星', status: 'claimed' },
      { day: 14, icon: '🏅', name: '奖牌', status: 'claimed' },
      { day: 15, icon: '🎊', name: '彩带', status: 'available' },
      { day: 16, icon: '🎉', name: '庆祝', status: 'locked' },
      { day: 17, icon: '🏆', name: '冠军', status: 'locked' },
      { day: 18, icon: '👑', name: '王者', status: 'locked' },
      { day: 19, icon: '💎', name: '钻石', status: 'locked' },
      { day: 20, icon: '🌈', name: '彩虹', status: 'locked' },
      { day: 21, icon: '🎁', name: '大奖', status: 'locked' }
    ],

    // 补卡信息
    hasMissedDays: true,
    makeupChances: 2
  },

  onLoad(options) {
    console.log('方案A打卡详情页面加载');
    const campId = options.campId;
    if (campId) {
      this.loadCampDetail(campId);
    }
    this.checkTodayStatus();
  },

  /**
   * 加载训练营详情
   */
  loadCampDetail(campId) {
    // 模拟加载训练营详情数据
    console.log('加载训练营详情:', campId);
  },

  /**
   * 检查今日状态
   */
  checkTodayStatus() {
    const today = new Date().toDateString();
    const lastCheckinDate = wx.getStorageSync('lastCheckinDate_growth_v2_a') || '';
    
    if (lastCheckinDate === today) {
      this.setData({
        todayStatus: 'completed'
      });
    }
  },

  /**
   * 切换视频展开状态
   */
  toggleVideos() {
    this.setData({
      videosExpanded: !this.data.videosExpanded
    });
  },

  /**
   * 播放视频
   */
  playVideo(e) {
    const video = e.currentTarget.dataset.video;
    wx.showModal({
      title: video.title,
      content: '播放视频功能',
      showCancel: false
    });
  },

  /**
   * 切换日历展开状态
   */
  toggleCalendar() {
    this.setData({
      calendarExpanded: !this.data.calendarExpanded
    });
  },

  /**
   * 领取奖励
   */
  claimReward(e) {
    const reward = e.currentTarget.dataset.reward;
    if (reward.status === 'available') {
      wx.showModal({
        title: '领取奖励',
        content: `确定要领取第${reward.day}天的奖励"${reward.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            // 更新奖励状态
            const dailyRewards = this.data.dailyRewards;
            const index = dailyRewards.findIndex(item => item.day === reward.day);
            if (index !== -1) {
              dailyRewards[index].status = 'claimed';
              this.setData({ dailyRewards });
              wx.showToast({
                title: '奖励领取成功！',
                icon: 'success'
              });
            }
          }
        }
      });
    } else if (reward.status === 'locked') {
      wx.showToast({
        title: '奖励尚未解锁',
        icon: 'none'
      });
    } else {
      wx.showToast({
        title: '奖励已领取',
        icon: 'none'
      });
    }
  },

  /**
   * 显示补卡选项
   */
  showMakeupOptions() {
    wx.showActionSheet({
      itemList: ['补昨天的卡', '补前天的卡', '查看更多补卡选项'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.makeupCheckin('yesterday');
        } else if (res.tapIndex === 1) {
          this.makeupCheckin('dayBeforeYesterday');
        } else if (res.tapIndex === 2) {
          wx.showModal({
            title: '补卡功能',
            content: '更多补卡选项开发中...',
            showCancel: false
          });
        }
      }
    });
  },

  /**
   * 补卡
   */
  makeupCheckin(day) {
    if (this.data.makeupChances > 0) {
      wx.showModal({
        title: '确认补卡',
        content: '确定要使用一次补卡机会吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({
              makeupChances: this.data.makeupChances - 1
            });
            wx.showToast({
              title: '补卡成功！',
              icon: 'success'
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '补卡机会已用完',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到契约页面
   */
  goToContract() {
    wx.navigateTo({
      url: '/pages/demo/growth-v2-a-contract/growth-v2-a-contract'
    });
  },

  /**
   * 跳转到打卡页面
   */
  goToCheckin() {
    if (this.data.todayStatus === 'pending') {
      wx.navigateTo({
        url: `/pages/demo/growth-v2-a-checkin/growth-v2-a-checkin?campId=${this.data.campInfo.id}`
      });
    } else if (this.data.todayStatus === 'missed') {
      wx.showModal({
        title: '今日已错过',
        content: '今日打卡时间已过，是否使用补卡机会？',
        success: (res) => {
          if (res.confirm) {
            this.showMakeupOptions();
          }
        }
      });
    }
  }
});
