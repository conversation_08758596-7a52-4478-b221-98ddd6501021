/* 方案A - 打卡详情页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: 200rpx;
}

/* 训练营信息区 */
.camp-info-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.camp-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.camp-basic-info {
  flex: 1;
  margin-right: 24rpx;
}

.camp-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 12rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.camp-progress-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: conic-gradient(#FF7A45 0deg, #FF7A45 var(--progress-deg, 240deg), #E5E5E5 var(--progress-deg, 240deg));
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-inner {
  width: 80rpx;
  height: 80rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #FF7A45;
}

.camp-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 通用区块样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666666;
}

/* 家庭荣誉契约区块 */
.contract-section {
  margin-bottom: 32rpx;
}

.contract-status {
  background-color: #FF7A45;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.contract-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #FFD700;
}

.contract-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.contract-info {
  flex: 1;
}

.contract-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.contract-desc {
  font-size: 28rpx;
  color: #666666;
}

.contract-progress {
  text-align: right;
}

.contract-progress .progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF7A45;
}

.contract-reward {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.reward-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 16rpx;
}

.reward-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.contract-progress-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bg {
  flex: 1;
  height: 8rpx;
  background-color: #E5E5E5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

/* 相关视频列表 */
.videos-section {
  margin-bottom: 32rpx;
}

.videos-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.video-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.video-item:last-child {
  border-bottom: none;
}

.video-thumb {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
}

.video-info {
  flex: 1;
}

.video-title {
  font-size: 28rpx;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.video-duration {
  font-size: 24rpx;
  color: #666666;
}

.video-status {
  font-size: 32rpx;
}

/* 打卡进度日历 */
.calendar-section {
  margin-bottom: 32rpx;
}

.calendar-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

/* 当前周视图 */
.week-view {
  display: flex;
  justify-content: space-between;
  gap: 8rpx;
}

.week-day {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
}

.week-day.completed {
  background-color: #E8F5E8;
}

.week-day.today {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.week-day.missed {
  background-color: #FFF2F0;
}

.day-name {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.day-status {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.day-date {
  font-size: 24rpx;
  color: #666666;
}

/* 完整日历视图 */
.full-calendar {
  max-height: 600rpx;
  overflow-y: auto;
}

.calendar-month {
  margin-bottom: 32rpx;
}

.month-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  position: relative;
}

.calendar-day.completed {
  background-color: #E8F5E8;
}

.calendar-day.today {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.calendar-day.missed {
  background-color: #FFF2F0;
}

.day-number {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 4rpx;
}

.day-indicator {
  font-size: 20rpx;
}

/* 每日奖励 */
.rewards-section {
  margin-bottom: 32rpx;
}

.rewards-scroll {
  white-space: nowrap;
}

.rewards-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 32rpx 0 0;
}

.reward-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  min-width: 120rpx;
  flex-shrink: 0;
}

.reward-item.claimed {
  background-color: #E8F5E8;
}

.reward-item.available {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.reward-item.locked {
  opacity: 0.5;
}

.reward-day {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.reward-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: block;
}

.reward-name {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.reward-status-badge {
  font-size: 20rpx;
  color: #666666;
}

/* 补卡信息 */
.makeup-section {
  margin-bottom: 32rpx;
}

.makeup-info {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.makeup-text {
  font-size: 28rpx;
  color: #333333;
}

.makeup-btn {
  background-color: #4A90E2;
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

/* 底部打卡按钮 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E5E5E5;
  z-index: 100;
}

.checkin-btn {
  width: 100%;
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.checkin-btn.completed {
  background-color: #52C41A;
}

.checkin-btn.missed {
  background-color: #FF4D4F;
}

.checkin-btn[disabled] {
  opacity: 0.6;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
