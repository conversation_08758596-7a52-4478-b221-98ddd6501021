// Demo版本A：视频重点突出型 - 逻辑文件
Page({
  data: {
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: "专业教练指导，让孩子轻松掌握跳绳技能",
      quickBenefits: [
        {
          number: "0→100个",
          desc: "21天技能飞跃",
        },
        {
          number: "15分钟",
          desc: "每天轻松练习",
        },
        {
          number: "专业指导",
          desc: "教练亲自示范",
        },
      ],
      featuredVideo: {
        id: 1,
        title: "跳绳基础完整教程",
        subtitle: "从零基础到连续100个，专业教练全程指导",
        thumbnail: "/images/app-logo.png",
        duration: "45分钟",
        students: "2847人学习",
        rating: "4.9分",
      },
      courseVideos: [
        {
          id: 2,
          title: "第1课：基础动作教学",
          subtitle: "正确姿势和握绳方法",
          thumbnail: "/images/app-logo.png",
          duration: "5分钟",
        },
        {
          id: 3,
          title: "第2课：节奏训练",
          subtitle: "找到适合的跳绳节奏",
          thumbnail: "/images/app-logo.png",
          duration: "8分钟",
        },
        {
          id: 4,
          title: "第3课：连续跳练习",
          subtitle: "从几个到几十个的突破",
          thumbnail: "/images/app-logo.png",
          duration: "10分钟",
        },
        {
          id: 5,
          title: "第4课：技巧提升",
          subtitle: "常见问题解决方法",
          thumbnail: "/images/app-logo.png",
          duration: "12分钟",
        },
      ],
      coach: {
        name: "李教练",
        title: "专业体育教练 · 10年教学经验",
        desc: "已帮助3000+孩子掌握跳绳技能",
        avatar: "/images/avatar_default.png",
      },
      steps: [
        {
          icon: "📺",
          title: "观看教学视频",
          desc: "学习正确方法",
        },
        {
          icon: "🏃‍♂️",
          title: "指导孩子练习",
          desc: "按视频方法教学",
        },
        {
          icon: "📝",
          title: "记录打卡进步",
          desc: "分享成长喜悦",
        },
      ],
      promises: [
        "视频可反复观看，学会为止",
        "专业教练在线答疑指导",
        "妈妈群经验分享交流",
      ],
      results: [
        {
          icon: "🎯",
          text: "连续跳100个",
        },
        {
          icon: "💪",
          text: "主动要求练习",
        },
        {
          icon: "📈",
          text: "体育成绩提升",
        },
        {
          icon: "😊",
          text: "自信心增强",
        },
      ],
    },
  },

  onLoad: function (options) {
    console.log("Demo版本A：视频重点突出型页面加载");
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const videoId = e.currentTarget.dataset.videoId;
    let video;

    if (videoId == 1) {
      video = this.data.taskInfo.featuredVideo;
    } else {
      video = this.data.taskInfo.courseVideos.find((v) => v.id == videoId);
    }

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${video.subtitle}`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 加入任务事件
  onJoinTask: function () {
    wx.showModal({
      title: "确认参加挑战",
      content: '确定要参加"21天跳绳养成计划"吗？\n将先为您播放教学视频',
      confirmText: "立即观看",
      cancelText: "再想想",
      success: (res) => {
        if (res.confirm) {
          // 先播放主要教学视频
          wx.showToast({
            title: "即将播放教学视频",
            icon: "none",
            duration: 2000,
          });

          // 然后提示加入成功
          setTimeout(() => {
            wx.showToast({
              title: "成功加入挑战！",
              icon: "success",
              duration: 2000,
            });
          }, 2500);
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "专业教练教跳绳，21天让孩子掌握技能",
      path: "/pages/task-detail-demo-a/task-detail-demo-a",
    };
  },
});
