/* Demo版本A：视频重点突出型 - 样式文件 */

.container {
  padding: 0;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #4a90e2 0%, #6ba3f0 100%);
  padding: 40rpx 30rpx 50rpx;
  text-align: center;
  color: white;
}

.task-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.task-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
}

/* 通用区域样式 */
.value-section,
.video-section-main,
.operation-section,
.result-section,
.action-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.section-title .icon {
  margin-right: 16rpx;
  font-size: 40rpx;
}

/* 简短价值主张区域 (20%) */
.value-section {
  margin-top: -30rpx;
  z-index: 10;
  position: relative;
}

.quick-benefits {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.benefit-highlight {
  flex: 1;
  text-align: center;
  background: linear-gradient(135deg, #fff7f0 0%, #ffe8d6 100%);
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  border: 2rpx solid #ff7a45;
}

.big-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff7a45;
  margin-bottom: 12rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

/* 专业教学视频区域 (40% - 重点) */
.video-section-main {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  border: 2rpx solid #4a90e2;
}

/* 主推视频 */
.featured-video {
  margin-bottom: 40rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: white;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.15);
}

.video-thumbnail-large {
  position: relative;
  height: 400rpx;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail-large image {
  width: 100%;
  height: 100%;
}

.play-icon-large {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 80rpx;
  color: white;
  text-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
}

.video-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff7a45;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.video-info-main {
  padding: 30rpx;
}

.video-title-main {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.video-subtitle-main {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.video-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #4a90e2;
  font-weight: 500;
}

/* 系列课程 */
.course-series {
  margin-bottom: 40rpx;
}

.series-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.videos-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.video-item-compact {
  display: flex;
  align-items: center;
  background: white;
  padding: 20rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
}

.video-item-compact:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.video-thumbnail-small {
  position: relative;
  width: 120rpx;
  height: 80rpx;
  background: #ddd;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.video-thumbnail-small image {
  width: 100%;
  height: 100%;
}

.play-icon-small {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.video-info-compact {
  flex: 1;
}

.video-title-compact {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.video-subtitle-compact {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.video-duration {
  font-size: 22rpx;
  color: #4a90e2;
  font-weight: 500;
}

/* 教练介绍 */
.coach-intro {
  display: flex;
  align-items: center;
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #e9ecef;
}

.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.coach-avatar image {
  width: 100%;
  height: 100%;
}

.coach-info {
  flex: 1;
}

.coach-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.coach-title {
  font-size: 26rpx;
  color: #4a90e2;
  margin-bottom: 8rpx;
}

.coach-desc {
  font-size: 24rpx;
  color: #666;
}

.video-highlight {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #4a90e2;
  background: white;
  padding: 25rpx;
  border-radius: 12rpx;
  font-weight: 500;
  text-align: center;
}

.video-highlight .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 操作说明区域 (25%) */
.steps-simple {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.step-simple {
  flex: 1;
  text-align: center;
}

.step-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.step-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 22rpx;
  color: #666;
}

.step-arrow {
  font-size: 32rpx;
  color: #4a90e2;
  margin: 0 20rpx;
}

.time-commitment {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.time-commitment .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.support-promise {
  background: #fff7f0;
  padding: 25rpx;
  border-radius: 12rpx;
}

.promise-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.promise-item:last-child {
  margin-bottom: 0;
}

.promise-item .icon {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #ff7a45;
}

/* 成果展示区域 */
.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.result-item {
  text-align: center;
  background: #f8f9fa;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
}

.result-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.result-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 行动召唤区域 (15%) */
.action-section {
  text-align: center;
  background: linear-gradient(135deg, #4a90e2 0%, #6ba3f0 100%);
  color: white;
}

.urgency-info {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.urgency-info .icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.join-button-main {
  background: white;
  color: #4a90e2;
  font-size: 36rpx;
  font-weight: bold;
  padding: 30rpx 40rpx;
  border-radius: 50rpx;
  border: none;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

.join-button-main:active {
  transform: scale(0.98);
}

.guarantee {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  opacity: 0.9;
}

.guarantee .icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}
