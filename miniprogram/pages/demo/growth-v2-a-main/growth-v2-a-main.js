// 方案A - 简洁任务导向型成长主页面
Page({
  data: {
    // 当前Tab
    currentTab: 0,

    // 是否有活跃训练营
    hasActiveCamps: true,

    // 是否有活跃契约
    hasActiveContract: true,

    // 家庭荣誉契约信息
    contractInfo: {
      title: "21天跳绳挑战契约",
      description: "连续打卡21天，掌握跳绳技能",
      progress: 67,
      currentDay: 14,
      totalDays: 21,
      reward: "一次科技馆之旅",
      witnesses: [
        { id: 1, avatar: "/images/avatar_default.png" },
        { id: 2, avatar: "/images/avatar_default.png" },
      ],
    },

    // 训练营列表
    campList: [
      {
        id: 1,
        title: "21天跳绳养成计划",
        subtitle: "基础跳绳技能训练",
        currentDay: 14,
        totalDays: 21,
        progressPercent: 67,
        todayStatus: "completed",
        hasContract: true,
      },
      {
        id: 2,
        title: "30天阅读习惯",
        subtitle: "培养每日阅读好习惯",
        currentDay: 8,
        totalDays: 30,
        progressPercent: 27,
        todayStatus: "pending",
        hasContract: false,
      },
      {
        id: 3,
        title: "14天数学思维",
        subtitle: "逻辑思维能力提升",
        currentDay: 5,
        totalDays: 14,
        progressPercent: 36,
        todayStatus: "missed",
        hasContract: false,
      },
    ],

    // 用户统计数据
    userStats: {
      totalPoints: 1285,
      currentLevel: 8,
      streakDays: 14,
      completedContracts: 2,
    },

    // 勋章数据
    unlockedMedals: 5,
    totalMedals: 12,
    medalsList: [
      {
        id: 1,
        name: "新手上路",
        icon: "🌟",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 2,
        name: "坚持不懈",
        icon: "💪",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 3,
        name: "契约守护者",
        icon: "🏆",
        unlocked: true,
        isContractMedal: true,
      },
      {
        id: 4,
        name: "跳绳达人",
        icon: "🏃‍♂️",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 5,
        name: "阅读之星",
        icon: "📚",
        unlocked: true,
        isContractMedal: false,
      },
      {
        id: 6,
        name: "数学天才",
        icon: "🧮",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 7,
        name: "全能选手",
        icon: "🎯",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 8,
        name: "契约大师",
        icon: "👑",
        unlocked: false,
        isContractMedal: true,
      },
      {
        id: 9,
        name: "社交达人",
        icon: "👥",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 10,
        name: "进步之星",
        icon: "⭐",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 11,
        name: "完美主义",
        icon: "💎",
        unlocked: false,
        isContractMedal: false,
      },
      {
        id: 12,
        name: "传奇英雄",
        icon: "🚀",
        unlocked: false,
        isContractMedal: false,
      },
    ],

    // 奖励列表
    rewardsList: [
      {
        id: 1,
        name: "每日签到奖励",
        description: "连续签到7天",
        icon: "🎁",
        status: "available",
      },
      {
        id: 2,
        name: "跳绳成就奖励",
        description: "完成跳绳训练营",
        icon: "🏆",
        status: "claimed",
      },
      {
        id: 3,
        name: "契约完成奖励",
        description: "完成第一个家庭契约",
        icon: "💎",
        status: "claimed",
      },
      {
        id: 4,
        name: "阅读进步奖励",
        description: "阅读训练营进度50%",
        icon: "📚",
        status: "locked",
      },
      {
        id: 5,
        name: "全勤奖励",
        description: "本月全勤打卡",
        icon: "⭐",
        status: "locked",
      },
    ],
  },

  onLoad(options) {
    console.log("方案A成长主页面加载");
    this.checkTodayStatus();
  },

  /**
   * 检查今日状态
   */
  checkTodayStatus() {
    const today = new Date().toDateString();
    const lastCheckDate = wx.getStorageSync("lastCheckDate_growth_v2_a") || "";

    if (lastCheckDate !== today) {
      // 可以在这里更新今日状态
      wx.setStorageSync("lastCheckDate_growth_v2_a", today);
    }
  },

  /**
   * 切换Tab
   */
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab,
    });
  },

  /**
   * 跳转到契约页面
   */
  goToContract() {
    wx.navigateTo({
      url: "/pages/demo/growth-v2-a-contract/growth-v2-a-contract",
    });
  },

  /**
   * 跳转到训练营详情
   */
  goToCampDetail(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/demo/growth-v2-a-detail/growth-v2-a-detail?campId=${camp.id}`,
    });
  },

  /**
   * 跳转到排行榜
   */
  goToRanking(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.navigateTo({
      url: `/pages/demo/growth-v2-a-ranking/growth-v2-a-ranking?campId=${camp.id}`,
    });
  },

  /**
   * 创建契约
   */
  createContract(e) {
    const camp = e.currentTarget.dataset.camp;
    wx.showModal({
      title: "创建家庭荣誉契约",
      content: `要为"${camp.title}"创建家庭荣誉契约吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/pages/demo/growth-v2-a-contract/growth-v2-a-contract?action=create&campId=${camp.id}`,
          });
        }
      },
    });
  },

  /**
   * 查看勋章详情
   */
  viewMedalDetail(e) {
    const medal = e.currentTarget.dataset.medal;
    wx.showModal({
      title: medal.name,
      content: medal.unlocked
        ? "恭喜你获得了这个勋章！"
        : "继续努力，即可解锁此勋章",
      showCancel: false,
    });
  },

  /**
   * 领取奖励
   */
  claimReward(e) {
    const reward = e.currentTarget.dataset.reward;
    if (reward.status === "available") {
      wx.showModal({
        title: "领取奖励",
        content: `确定要领取"${reward.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            // 更新奖励状态
            const rewardsList = this.data.rewardsList;
            const index = rewardsList.findIndex(
              (item) => item.id === reward.id
            );
            if (index !== -1) {
              rewardsList[index].status = "claimed";
              this.setData({ rewardsList });
              wx.showToast({
                title: "奖励领取成功！",
                icon: "success",
              });
            }
          }
        },
      });
    } else if (reward.status === "locked") {
      wx.showToast({
        title: "奖励尚未解锁",
        icon: "none",
      });
    } else {
      wx.showToast({
        title: "奖励已领取",
        icon: "none",
      });
    }
  },

  /**
   * 快速操作
   */
  goToHistory() {
    wx.showToast({
      title: "跳转到历史记录",
      icon: "none",
    });
  },

  goToAllRankings() {
    wx.navigateTo({
      url: "/pages/demo/growth-v2-a-ranking/growth-v2-a-ranking",
    });
  },

  goToSettings() {
    wx.showToast({
      title: "跳转到设置页面",
      icon: "none",
    });
  },

  shareProgress() {
    wx.navigateTo({
      url: "/pages/demo/growth-v2-a-poster/growth-v2-a-poster",
    });
  },

  /**
   * 跳转到首页
   */
  goToHome() {
    wx.switchTab({
      url: "/pages/index/index",
    });
  },
});
