<!-- 方案A - 简洁任务导向型成长主页面 -->
<view class="page-container">
  <!-- 顶部导航Tab -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">任务</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">成长</text>
    </view>
  </view>

  <!-- 任务Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <!-- 家庭荣誉契约卡片 -->
    <view class="contract-section" wx:if="{{hasActiveContract}}">
      <view class="section-title">
        <text class="title-icon">🏆</text>
        <text class="title-text">家庭荣誉契约</text>
        <text class="contract-status">进行中</text>
      </view>
      <view class="contract-card" bindtap="goToContract">
        <view class="contract-header">
          <view class="contract-info">
            <text class="contract-title">{{contractInfo.title}}</text>
            <text class="contract-desc">{{contractInfo.description}}</text>
          </view>
          <view class="contract-progress-circle">
            <view class="progress-inner">
              <text class="progress-text">{{contractInfo.progress}}%</text>
            </view>
          </view>
        </view>
        <view class="contract-details">
          <view class="contract-reward">
            <text class="reward-label">约定奖励：</text>
            <text class="reward-text">{{contractInfo.reward}}</text>
          </view>
          <view class="contract-witnesses">
            <text class="witnesses-label">见证人：</text>
            <view class="witnesses-list">
              <image class="witness-avatar" src="{{item.avatar}}" wx:for="{{contractInfo.witnesses}}" wx:key="id" />
            </view>
          </view>
        </view>
        <view class="contract-progress-bar">
          <view class="progress-bg">
            <view class="progress-fill" style="width: {{contractInfo.progress}}%"></view>
          </view>
          <text class="progress-desc">{{contractInfo.currentDay}}/{{contractInfo.totalDays}}天</text>
        </view>
      </view>
    </view>

    <!-- 训练营列表 -->
    <view class="camps-section">
      <view class="section-title">
        <text class="title-icon">📚</text>
        <text class="title-text">我的训练营</text>
      </view>
      <view class="camp-list">
        <view class="camp-item" wx:for="{{campList}}" wx:key="id" bindtap="goToCampDetail" data-camp="{{item}}">
          <view class="camp-header">
            <view class="camp-info">
              <text class="camp-title">{{item.title}}</text>
              <text class="camp-subtitle">{{item.subtitle}}</text>
            </view>
            <view class="camp-status">
              <view class="status-badge {{item.todayStatus}}">
                <text wx:if="{{item.todayStatus === 'completed'}}">✅ 已打卡</text>
                <text wx:elif="{{item.todayStatus === 'pending'}}">⭕ 待打卡</text>
                <text wx:else="{{item.todayStatus === 'missed'}}">❌ 已错过</text>
              </view>
            </view>
          </view>
          <view class="camp-progress">
            <view class="progress-info">
              <text class="progress-text">{{item.currentDay}}/{{item.totalDays}}天</text>
              <text class="progress-percent">{{item.progressPercent}}%</text>
            </view>
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.progressPercent}}%"></view>
            </view>
          </view>
          <view class="camp-actions">
            <view class="action-btn" bindtap="goToRanking" data-camp="{{item}}" catchtap="true">
              <text class="action-icon">📊</text>
              <text class="action-text">排行榜</text>
            </view>
            <view class="action-btn contract-btn" wx:if="{{item.hasContract}}" bindtap="goToContract" catchtap="true">
              <text class="action-icon">🏆</text>
              <text class="action-text">荣誉契约</text>
            </view>
            <view class="action-btn create-contract-btn" wx:else bindtap="createContract" data-camp="{{item}}" catchtap="true">
              <text class="action-icon">➕</text>
              <text class="action-text">创建契约</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions-section">
      <view class="section-title">
        <text class="title-icon">⚡</text>
        <text class="title-text">快速操作</text>
      </view>
      <view class="quick-actions-grid">
        <view class="quick-action-item" bindtap="goToHistory">
          <view class="action-icon">📊</view>
          <text class="action-text">历史记录</text>
        </view>
        <view class="quick-action-item" bindtap="goToAllRankings">
          <view class="action-icon">🏆</view>
          <text class="action-text">全部排行</text>
        </view>
        <view class="quick-action-item" bindtap="goToSettings">
          <view class="action-icon">⚙️</view>
          <text class="action-text">设置</text>
        </view>
        <view class="quick-action-item" bindtap="shareProgress">
          <view class="action-icon">📤</view>
          <text class="action-text">分享</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成长Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <!-- 个人荣誉总览 -->
    <view class="honor-overview-section">
      <view class="section-title">
        <text class="title-icon">🌟</text>
        <text class="title-text">个人荣誉</text>
      </view>
      <view class="honor-stats">
        <view class="stat-item">
          <text class="stat-value">{{userStats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.currentLevel}}</text>
          <text class="stat-label">当前等级</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.streakDays}}</text>
          <text class="stat-label">连续天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.completedContracts}}</text>
          <text class="stat-label">完成契约</text>
        </view>
      </view>
    </view>

    <!-- 勋章墙 -->
    <view class="medals-section">
      <view class="section-title">
        <text class="title-icon">🏅</text>
        <text class="title-text">勋章墙</text>
        <text class="medals-count">{{unlockedMedals}}/{{totalMedals}}</text>
      </view>
      <view class="medals-grid">
        <view class="medal-item {{item.unlocked ? 'unlocked' : 'locked'}}" wx:for="{{medalsList}}" wx:key="id" bindtap="viewMedalDetail" data-medal="{{item}}">
          <view class="medal-icon">{{item.icon}}</view>
          <text class="medal-name">{{item.name}}</text>
          <view class="medal-special" wx:if="{{item.isContractMedal}}">
            <text class="special-label">契约勋章</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖励中心 -->
    <view class="rewards-section">
      <view class="section-title">
        <text class="title-icon">🎁</text>
        <text class="title-text">奖励中心</text>
      </view>
      <view class="rewards-list">
        <view class="reward-item {{item.status}}" wx:for="{{rewardsList}}" wx:key="id" bindtap="claimReward" data-reward="{{item}}">
          <view class="reward-icon">{{item.icon}}</view>
          <view class="reward-info">
            <text class="reward-name">{{item.name}}</text>
            <text class="reward-desc">{{item.description}}</text>
          </view>
          <view class="reward-status">
            <text wx:if="{{item.status === 'available'}}">可领取</text>
            <text wx:elif="{{item.status === 'claimed'}}">已领取</text>
            <text wx:else>未解锁</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!hasActiveCamps}}" class="empty-state">
    <view class="empty-icon">📭</view>
    <text class="empty-title">还没有参加训练营</text>
    <text class="empty-desc">去首页选择适合的训练营开始学习吧！</text>
    <button class="empty-action-btn" bindtap="goToHome">选择训练营</button>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
