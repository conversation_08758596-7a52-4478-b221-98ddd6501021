/* 方案A - 简洁任务导向型成长主页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* Tab导航 */
.tab-nav {
  display: flex;
  background-color: #FFFFFF;
  border-bottom: 2rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}

.tab-item.active {
  color: #FF7A45;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #FF7A45;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* Tab内容 */
.tab-content {
  padding: 32rpx;
}

/* 通用区块标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
}

/* 家庭荣誉契约区块 */
.contract-section {
  margin-bottom: 48rpx;
}

.contract-status {
  background-color: #FF7A45;
  color: #FFFFFF;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.contract-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #FFD700;
}

.contract-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.contract-info {
  flex: 1;
  margin-right: 24rpx;
}

.contract-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.contract-desc {
  font-size: 28rpx;
  color: #666666;
}

.contract-progress-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: conic-gradient(#FF7A45 0deg, #FF7A45 var(--progress-deg, 240deg), #E5E5E5 var(--progress-deg, 240deg));
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-inner {
  width: 80rpx;
  height: 80rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #FF7A45;
}

.contract-details {
  margin-bottom: 24rpx;
}

.contract-reward, .contract-witnesses {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.reward-label, .witnesses-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 16rpx;
}

.reward-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.witnesses-list {
  display: flex;
  gap: 8rpx;
}

.witness-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #FFD700;
}

.contract-progress-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bg {
  flex: 1;
  height: 8rpx;
  background-color: #E5E5E5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-desc {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

/* 训练营列表 */
.camps-section {
  margin-bottom: 48rpx;
}

.camp-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.camp-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.camp-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.camp-info {
  flex: 1;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.status-badge.completed {
  background-color: #E8F5E8;
  color: #52C41A;
}

.status-badge.pending {
  background-color: #FFF7E6;
  color: #FA8C16;
}

.status-badge.missed {
  background-color: #FFF2F0;
  color: #FF4D4F;
}

.camp-progress {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #666666;
}

.progress-percent {
  font-size: 28rpx;
  color: #FF7A45;
  font-weight: 600;
}

.camp-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 12rpx;
  border: 2rpx solid #E5E5E5;
  background-color: #FFFFFF;
}

.action-btn.contract-btn {
  border-color: #FFD700;
  background-color: #FFFBE6;
}

.action-btn.create-contract-btn {
  border-color: #FF7A45;
  background-color: #FFF2ED;
}

.action-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 28rpx;
  color: #333333;
}

/* 快速操作 */
.quick-actions-section {
  margin-bottom: 48rpx;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.quick-action-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.quick-action-item .action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.quick-action-item .action-text {
  font-size: 24rpx;
  color: #666666;
}

/* 个人荣誉总览 */
.honor-overview-section {
  margin-bottom: 48rpx;
}

.honor-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

/* 勋章墙 */
.medals-section {
  margin-bottom: 48rpx;
}

.medals-count {
  font-size: 28rpx;
  color: #666666;
}

.medals-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.medal-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.medal-item.locked {
  opacity: 0.5;
}

.medal-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}

.medal-name {
  font-size: 24rpx;
  color: #333333;
}

.medal-special {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.special-label {
  background-color: #FFD700;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 奖励中心 */
.rewards-section {
  margin-bottom: 48rpx;
}

.rewards-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reward-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.reward-item.locked {
  opacity: 0.6;
}

.reward-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.reward-desc {
  font-size: 24rpx;
  color: #666666;
}

.reward-status {
  font-size: 24rpx;
  color: #666666;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  display: block;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.empty-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
  display: block;
}

.empty-action-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
