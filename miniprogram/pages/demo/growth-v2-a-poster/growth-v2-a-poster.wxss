/* 方案A - 分享海报页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 海报类型切换 */
.poster-tabs {
  display: flex;
  background-color: #FFFFFF;
  margin: 32rpx;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #FF7A45;
  color: #FFFFFF;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 海报预览区 */
.poster-preview {
  margin: 0 32rpx 32rpx;
  position: relative;
}

.poster-canvas {
  position: absolute;
  top: -9999rpx;
  left: -9999rpx;
  opacity: 0;
}

.poster-template {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: scale(0.8);
  transform-origin: top center;
}

/* 打卡海报样式 */
.poster-header {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 48rpx 32rpx;
  display: flex;
  align-items: center;
  color: #FFFFFF;
}

.child-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 4rpx solid #FFFFFF;
}

.child-info {
  flex: 1;
}

.child-name {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.checkin-date {
  font-size: 28rpx;
  opacity: 0.9;
}

.checkin-badge {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
}

.badge-text {
  font-size: 32rpx;
  font-weight: 600;
}

.poster-body {
  padding: 48rpx 32rpx;
}

.camp-info {
  text-align: center;
  margin-bottom: 48rpx;
}

.camp-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 28rpx;
  color: #666666;
}

.checkin-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 48rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF7A45;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.checkin-feeling {
  background-color: #FFF2ED;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
}

.feeling-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.feeling-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.progress-info {
  text-align: center;
}

.progress-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.progress-bar {
  height: 16rpx;
  background-color: #E5E5E5;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

.poster-footer {
  background-color: #F7F8FA;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-info {
  flex: 1;
}

.app-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.app-slogan {
  font-size: 24rpx;
  color: #666666;
}

.qr-code {
  width: 120rpx;
  height: 120rpx;
}

/* 成就海报样式 */
.achievement-poster {
  background: linear-gradient(135deg, #4A90E2, #6BA3E8);
  color: #FFFFFF;
}

.achievement-header {
  text-align: center;
  padding: 48rpx 32rpx;
}

.header-decoration {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.achievement-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  display: block;
}

.medal-display {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
}

.medal-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.medal-name {
  font-size: 36rpx;
  font-weight: 600;
}

.achievement-body {
  background-color: #FFFFFF;
  color: #333333;
  padding: 48rpx 32rpx;
}

.child-showcase {
  text-align: center;
  margin-bottom: 48rpx;
}

.child-avatar-large {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
  border: 4rpx solid #4A90E2;
}

.child-name-large {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
}

.achievement-details {
  text-align: center;
  margin-bottom: 48rpx;
}

.achievement-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: block;
}

.achievement-date {
  font-size: 24rpx;
  color: #999999;
}

.achievement-stats {
  display: flex;
  justify-content: space-around;
}

.stat-card {
  text-align: center;
  background-color: #F7F8FA;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  flex: 1;
  margin: 0 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #4A90E2;
  display: block;
  margin-bottom: 8rpx;
}

.stat-desc {
  font-size: 24rpx;
  color: #666666;
}

.achievement-footer {
  background-color: #4A90E2;
  color: #FFFFFF;
  padding: 32rpx;
  text-align: center;
}

.footer-text {
  font-size: 28rpx;
  margin-bottom: 16rpx;
  display: block;
}

.qr-code-small {
  width: 80rpx;
  height: 80rpx;
  margin-left: 16rpx;
}

/* 证书海报样式 */
.certificate-poster {
  background-color: #FFFBE6;
  border: 8rpx solid #FFD700;
}

.certificate-header {
  text-align: center;
  padding: 48rpx 32rpx 32rpx;
  border-bottom: 4rpx solid #FFD700;
}

.certificate-decoration {
  margin-bottom: 16rpx;
}

.decoration-icon {
  font-size: 80rpx;
}

.certificate-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.certificate-subtitle {
  font-size: 32rpx;
  color: #666666;
}

.certificate-body {
  padding: 48rpx 32rpx;
}

.certificate-content {
  text-align: center;
  margin-bottom: 48rpx;
  line-height: 2;
}

.certificate-text {
  font-size: 32rpx;
  color: #333333;
  margin: 0 8rpx;
}

.child-name-certificate {
  font-size: 40rpx;
  font-weight: 600;
  color: #FF7A45;
  margin: 0 16rpx;
}

.contract-title-certificate {
  font-size: 36rpx;
  font-weight: 600;
  color: #4A90E2;
  margin: 0 16rpx;
}

.certificate-desc {
  font-size: 28rpx;
  color: #666666;
  margin: 16rpx 0;
  display: block;
}

.certificate-details {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 16rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.certificate-signatures {
  display: flex;
  justify-content: space-around;
}

.signature-item {
  text-align: center;
}

.signature-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
  border: 2rpx solid #FFD700;
}

.signature-name {
  font-size: 24rpx;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.signature-role {
  font-size: 20rpx;
  color: #666666;
}

.certificate-footer {
  background-color: #FFD700;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.certificate-seal {
  background-color: #FF7A45;
  color: #FFFFFF;
  border-radius: 50%;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.seal-text {
  font-size: 24rpx;
  font-weight: 600;
}

.issue-date {
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
  display: block;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #E5E5E5;
  display: flex;
  gap: 16rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn.secondary {
  background-color: #F7F8FA;
  color: #666666;
}

.action-btn.primary {
  background-color: #FF7A45;
  color: #FFFFFF;
}

.action-btn.share {
  background-color: #4A90E2;
  color: #FFFFFF;
}

/* 保存成功弹窗 */
.save-success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.save-success-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin: 32rpx;
  max-width: 500rpx;
  width: 100%;
  text-align: center;
}

.success-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.success-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.close-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
