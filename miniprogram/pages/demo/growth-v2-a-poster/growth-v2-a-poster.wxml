<!-- 方案A - 分享海报页面 -->
<view class="page-container">
  <!-- 海报类型切换 -->
  <view class="poster-tabs">
    <view class="tab-item {{currentType === 'checkin' ? 'active' : ''}}" bindtap="switchType" data-type="checkin">
      <text class="tab-text">打卡海报</text>
    </view>
    <view class="tab-item {{currentType === 'achievement' ? 'active' : ''}}" bindtap="switchType" data-type="achievement">
      <text class="tab-text">成就海报</text>
    </view>
    <view class="tab-item {{currentType === 'certificate' ? 'active' : ''}}" bindtap="switchType" data-type="certificate">
      <text class="tab-text">契约证书</text>
    </view>
  </view>

  <!-- 海报预览区 -->
  <view class="poster-preview">
    <canvas class="poster-canvas" canvas-id="posterCanvas" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"></canvas>
    
    <!-- 打卡海报模板 -->
    <view class="poster-template checkin-poster" wx:if="{{currentType === 'checkin'}}">
      <view class="poster-header">
        <view class="header-bg"></view>
        <view class="header-content">
          <image class="child-avatar" src="{{posterData.child.avatar}}" mode="aspectFill" />
          <view class="child-info">
            <text class="child-name">{{posterData.child.name}}</text>
            <text class="checkin-date">{{posterData.checkin.date}}</text>
          </view>
          <view class="checkin-badge">
            <text class="badge-text">第{{posterData.checkin.day}}天</text>
          </view>
        </view>
      </view>
      
      <view class="poster-body">
        <view class="camp-info">
          <text class="camp-title">{{posterData.camp.title}}</text>
          <text class="camp-subtitle">{{posterData.camp.subtitle}}</text>
        </view>
        
        <view class="checkin-stats">
          <view class="stat-item">
            <text class="stat-value">{{posterData.checkin.duration}}</text>
            <text class="stat-label">练习时长(分钟)</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{posterData.checkin.score}}</text>
            <text class="stat-label">跳绳成绩(个)</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{posterData.checkin.points}}</text>
            <text class="stat-label">获得积分</text>
          </view>
        </view>
        
        <view class="checkin-feeling" wx:if="{{posterData.checkin.feeling}}">
          <text class="feeling-title">今日感受</text>
          <text class="feeling-text">{{posterData.checkin.feeling}}</text>
        </view>
        
        <view class="progress-info">
          <text class="progress-title">训练营进度</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{posterData.camp.progressPercent}}%"></view>
          </view>
          <text class="progress-text">{{posterData.camp.currentDay}}/{{posterData.camp.totalDays}} 天 ({{posterData.camp.progressPercent}}%)</text>
        </view>
      </view>
      
      <view class="poster-footer">
        <view class="app-info">
          <text class="app-name">成长伙伴</text>
          <text class="app-slogan">陪伴孩子快乐成长</text>
        </view>
        <image class="qr-code" src="/images/qr_code.png" mode="aspectFit" />
      </view>
    </view>

    <!-- 成就海报模板 -->
    <view class="poster-template achievement-poster" wx:if="{{currentType === 'achievement'}}">
      <view class="achievement-header">
        <view class="header-decoration">🎉</view>
        <text class="achievement-title">恭喜获得成就</text>
        <view class="medal-display">
          <text class="medal-icon">{{posterData.achievement.medal.icon}}</text>
          <text class="medal-name">{{posterData.achievement.medal.name}}</text>
        </view>
      </view>
      
      <view class="achievement-body">
        <view class="child-showcase">
          <image class="child-avatar-large" src="{{posterData.child.avatar}}" mode="aspectFill" />
          <text class="child-name-large">{{posterData.child.name}}</text>
        </view>
        
        <view class="achievement-details">
          <text class="achievement-desc">{{posterData.achievement.description}}</text>
          <text class="achievement-date">获得时间：{{posterData.achievement.date}}</text>
        </view>
        
        <view class="achievement-stats">
          <view class="stat-card">
            <text class="stat-number">{{posterData.achievement.totalDays}}</text>
            <text class="stat-desc">坚持天数</text>
          </view>
          <view class="stat-card">
            <text class="stat-number">{{posterData.achievement.totalPoints}}</text>
            <text class="stat-desc">累计积分</text>
          </view>
          <view class="stat-card">
            <text class="stat-number">{{posterData.achievement.rank}}</text>
            <text class="stat-desc">当前排名</text>
          </view>
        </view>
      </view>
      
      <view class="achievement-footer">
        <text class="footer-text">坚持就是胜利，继续加油！</text>
        <view class="app-info">
          <text class="app-name">成长伙伴</text>
          <image class="qr-code-small" src="/images/qr_code.png" mode="aspectFit" />
        </view>
      </view>
    </view>

    <!-- 契约证书模板 -->
    <view class="poster-template certificate-poster" wx:if="{{currentType === 'certificate'}}">
      <view class="certificate-header">
        <view class="certificate-decoration">
          <text class="decoration-icon">🏆</text>
        </view>
        <text class="certificate-title">家庭荣誉契约</text>
        <text class="certificate-subtitle">完成证书</text>
      </view>
      
      <view class="certificate-body">
        <view class="certificate-content">
          <text class="certificate-text">兹证明</text>
          <text class="child-name-certificate">{{posterData.child.name}}</text>
          <text class="certificate-text">小朋友</text>
          <text class="certificate-desc">在家长的见证下，成功完成了</text>
          <text class="contract-title-certificate">{{posterData.contract.title}}</text>
          <text class="certificate-desc">挑战目标，展现了坚持不懈的精神</text>
          <text class="certificate-desc">特此颁发此证书以资鼓励</text>
        </view>
        
        <view class="certificate-details">
          <view class="detail-item">
            <text class="detail-label">挑战周期：</text>
            <text class="detail-value">{{posterData.contract.duration}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">完成时间：</text>
            <text class="detail-value">{{posterData.contract.completedDate}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">约定奖励：</text>
            <text class="detail-value">{{posterData.contract.reward}}</text>
          </view>
        </view>
        
        <view class="certificate-signatures">
          <view class="signature-item" wx:for="{{posterData.contract.witnesses}}" wx:key="id">
            <image class="signature-avatar" src="{{item.avatar}}" mode="aspectFill" />
            <text class="signature-name">{{item.name}}</text>
            <text class="signature-role">{{item.role}}</text>
          </view>
        </view>
      </view>
      
      <view class="certificate-footer">
        <view class="certificate-seal">
          <text class="seal-text">荣誉印章</text>
        </view>
        <view class="app-info">
          <text class="app-name">成长伙伴</text>
          <text class="issue-date">{{posterData.contract.issueDate}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="action-btn secondary" bindtap="previewPoster">预览海报</button>
    <button class="action-btn primary" bindtap="savePoster">保存到相册</button>
    <button class="action-btn share" bindtap="sharePoster">分享给好友</button>
  </view>

  <!-- 保存成功提示 -->
  <view class="save-success-modal {{showSaveSuccess ? 'show' : ''}}" wx:if="{{showSaveSuccess}}">
    <view class="modal-content">
      <text class="success-icon">✅</text>
      <text class="success-title">保存成功</text>
      <text class="success-desc">海报已保存到相册，快去分享吧！</text>
      <button class="close-btn" bindtap="closeSaveSuccess">知道了</button>
    </view>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
