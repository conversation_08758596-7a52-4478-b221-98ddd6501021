// 方案A - 分享海报页面
Page({
  data: {
    // 当前海报类型
    currentType: "checkin", // checkin, achievement, certificate

    // 画布尺寸
    canvasWidth: 750,
    canvasHeight: 1334,

    // 海报数据
    posterData: {
      child: {
        name: "小明",
        avatar: "/images/avatar_default.png",
      },
      // 打卡海报数据
      checkin: {
        date: "2024年1月21日",
        day: 14,
        duration: 15,
        score: 120,
        points: 25,
        feeling: "今天的跳绳训练很有趣，学会了新的花样！",
      },
      // 训练营数据
      camp: {
        title: "21天跳绳养成计划",
        subtitle: "基础跳绳技能训练",
        currentDay: 14,
        totalDays: 21,
        progressPercent: 67,
      },
      // 成就海报数据
      achievement: {
        medal: {
          name: "坚持不懈",
          icon: "🏅",
          description: "连续打卡14天，展现了坚持不懈的精神",
        },
        description: "在21天跳绳养成计划中表现优异",
        date: "2024年1月21日",
        totalDays: 14,
        totalPoints: 350,
        rank: 8,
      },
      // 契约证书数据
      contract: {
        title: "21天跳绳挑战契约",
        duration: "21天",
        completedDate: "2024年1月21日",
        reward: "一次科技馆之旅",
        issueDate: "2024年1月21日",
        witnesses: [
          {
            id: 1,
            name: "妈妈",
            avatar: "/images/avatar_default.png",
            role: "首席见证官",
          },
          {
            id: 2,
            name: "爸爸",
            avatar: "/images/avatar_default.png",
            role: "见证人",
          },
        ],
      },
    },

    // 保存成功弹窗
    showSaveSuccess: false,
  },

  onLoad(options) {
    console.log("方案A分享海报页面加载");

    // 根据参数设置海报类型和数据
    const type = options.type || "checkin";
    const campId = options.campId;
    const day = options.day;
    const contractId = options.contractId;

    this.setData({
      currentType: type,
    });

    // 根据参数加载对应数据
    if (type === "checkin" && campId && day) {
      this.loadCheckinData(campId, day);
    } else if (type === "achievement") {
      this.loadAchievementData();
    } else if (type === "certificate" && contractId) {
      this.loadCertificateData(contractId);
    }

    // 初始化画布
    this.initCanvas();
  },

  /**
   * 加载打卡数据
   */
  loadCheckinData(campId, day) {
    // 模拟加载打卡数据
    console.log("加载打卡数据:", campId, day);
  },

  /**
   * 加载成就数据
   */
  loadAchievementData() {
    // 模拟加载成就数据
    console.log("加载成就数据");
  },

  /**
   * 加载证书数据
   */
  loadCertificateData(contractId) {
    // 模拟加载证书数据
    console.log("加载证书数据:", contractId);
  },

  /**
   * 初始化画布
   */
  initCanvas() {
    const ctx = wx.createCanvasContext("posterCanvas", this);
    this.canvasContext = ctx;

    // 设置画布背景
    ctx.setFillStyle("#FFFFFF");
    ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);
    ctx.draw();
  },

  /**
   * 切换海报类型
   */
  switchType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      currentType: type,
    });
  },

  /**
   * 预览海报
   */
  previewPoster() {
    wx.showLoading({
      title: "生成中...",
    });

    // 根据当前类型生成对应海报
    this.generatePoster()
      .then(() => {
        wx.hideLoading();
        wx.previewImage({
          urls: [this.tempFilePath],
          current: this.tempFilePath,
        });
      })
      .catch((error) => {
        wx.hideLoading();
        wx.showToast({
          title: "生成失败",
          icon: "none",
        });
        console.error("海报生成失败:", error);
      });
  },

  /**
   * 生成海报
   */
  generatePoster() {
    return new Promise((resolve, reject) => {
      const ctx = this.canvasContext;
      const { currentType, posterData, canvasWidth, canvasHeight } = this.data;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 设置背景
      ctx.setFillStyle("#FFFFFF");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      try {
        // 根据类型绘制不同海报
        if (currentType === "checkin") {
          this.drawCheckinPoster(ctx);
        } else if (currentType === "achievement") {
          this.drawAchievementPoster(ctx);
        } else if (currentType === "certificate") {
          this.drawCertificatePoster(ctx);
        }

        ctx.draw(false, () => {
          // 导出图片
          wx.canvasToTempFilePath(
            {
              canvasId: "posterCanvas",
              success: (res) => {
                this.tempFilePath = res.tempFilePath;
                resolve(res.tempFilePath);
              },
              fail: reject,
            },
            this
          );
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 绘制打卡海报
   */
  drawCheckinPoster(ctx) {
    const { posterData } = this.data;

    // 绘制头部背景
    const gradient = ctx.createLinearGradient(0, 0, 0, 300);
    gradient.addColorStop(0, "#FF7A45");
    gradient.addColorStop(1, "#FF9A6B");
    ctx.setFillStyle(gradient);
    ctx.fillRect(0, 0, 750, 300);

    // 绘制标题
    ctx.setFillStyle("#FFFFFF");
    ctx.setFontSize(48);
    ctx.setTextAlign("center");
    ctx.fillText("打卡成功", 375, 80);

    // 绘制日期
    ctx.setFontSize(32);
    ctx.fillText(posterData.checkin.date, 375, 130);

    // 绘制天数徽章
    ctx.setFillStyle("rgba(255, 255, 255, 0.2)");
    ctx.fillRect(300, 180, 150, 80);
    ctx.setFillStyle("#FFFFFF");
    ctx.setFontSize(36);
    ctx.fillText(`第${posterData.checkin.day}天`, 375, 230);

    // 绘制训练营信息
    ctx.setFillStyle("#333333");
    ctx.setFontSize(40);
    ctx.fillText(posterData.camp.title, 375, 400);

    // 绘制统计数据
    const stats = [
      { label: "练习时长", value: `${posterData.checkin.duration}分钟` },
      { label: "跳绳成绩", value: `${posterData.checkin.score}个` },
      { label: "获得积分", value: `${posterData.checkin.points}分` },
    ];

    stats.forEach((stat, index) => {
      const x = 125 + index * 250;
      const y = 500;

      // 绘制数值
      ctx.setFillStyle("#FF7A45");
      ctx.setFontSize(48);
      ctx.setTextAlign("center");
      ctx.fillText(stat.value, x, y);

      // 绘制标签
      ctx.setFillStyle("#666666");
      ctx.setFontSize(28);
      ctx.fillText(stat.label, x, y + 50);
    });

    // 绘制进度条
    ctx.setFillStyle("#E5E5E5");
    ctx.fillRect(100, 650, 550, 20);
    ctx.setFillStyle("#FF7A45");
    ctx.fillRect(100, 650, 550 * (posterData.camp.progressPercent / 100), 20);

    // 绘制进度文字
    ctx.setFillStyle("#333333");
    ctx.setFontSize(32);
    ctx.setTextAlign("center");
    ctx.fillText(
      `${posterData.camp.currentDay}/${posterData.camp.totalDays} 天`,
      375,
      720
    );
  },

  /**
   * 绘制成就海报
   */
  drawAchievementPoster(ctx) {
    // 成就海报绘制逻辑
    console.log("绘制成就海报");
  },

  /**
   * 绘制证书海报
   */
  drawCertificatePoster(ctx) {
    // 证书海报绘制逻辑
    console.log("绘制证书海报");
  },

  /**
   * 保存到相册
   */
  savePoster() {
    wx.showLoading({
      title: "保存中...",
    });

    this.generatePoster()
      .then((tempFilePath) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: () => {
            wx.hideLoading();
            this.setData({
              showSaveSuccess: true,
            });
          },
          fail: (error) => {
            wx.hideLoading();
            if (error.errMsg.includes("auth deny")) {
              wx.showModal({
                title: "提示",
                content: "需要授权访问相册才能保存图片",
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting();
                  }
                },
              });
            } else {
              wx.showToast({
                title: "保存失败",
                icon: "none",
              });
            }
          },
        });
      })
      .catch(() => {
        wx.hideLoading();
        wx.showToast({
          title: "生成失败",
          icon: "none",
        });
      });
  },

  /**
   * 分享给好友
   */
  sharePoster() {
    this.generatePoster().then((tempFilePath) => {
      // 这里可以调用分享API或显示分享选项
      wx.showActionSheet({
        itemList: ["分享到微信", "分享到朋友圈", "复制链接"],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 分享到微信
            console.log("分享到微信");
          } else if (res.tapIndex === 1) {
            // 分享到朋友圈
            console.log("分享到朋友圈");
          } else if (res.tapIndex === 2) {
            // 复制链接
            wx.setClipboardData({
              data: "分享链接",
              success: () => {
                wx.showToast({
                  title: "链接已复制",
                  icon: "success",
                });
              },
            });
          }
        },
      });
    });
  },

  /**
   * 关闭保存成功弹窗
   */
  closeSaveSuccess() {
    this.setData({
      showSaveSuccess: false,
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: `${this.data.posterData.child.name}的成长海报`,
      path: "/pages/demo/growth-v2-a-poster/growth-v2-a-poster",
      imageUrl: this.tempFilePath || "/images/share_poster.jpg",
    };
  },
});
