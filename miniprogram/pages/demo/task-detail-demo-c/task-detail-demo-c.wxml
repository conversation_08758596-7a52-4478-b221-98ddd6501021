<!--Demo版本C：平衡展示型 - 任务详情页面-->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header-section">
    <view class="task-title">21天跳绳养成计划</view>
    <view class="task-subtitle">专业指导 + 系统训练 = 孩子技能飞跃</view>
  </view>

  <!-- 价值主张区域 (30%) -->
  <view class="value-section">
    <view class="section-title">
      <text class="icon">🎯</text>
      <text>孩子将获得的成长：</text>
    </view>
    
    <view class="benefits-balanced">
      <view class="benefit-card">
        <view class="benefit-icon">🏃‍♂️</view>
        <view class="benefit-content">
          <view class="benefit-title">技能掌握</view>
          <view class="benefit-desc">从0个到连续100个</view>
        </view>
      </view>
      
      <view class="benefit-card">
        <view class="benefit-icon">💪</view>
        <view class="benefit-content">
          <view class="benefit-title">习惯养成</view>
          <view class="benefit-desc">主动要求练习运动</view>
        </view>
      </view>
      
      <view class="benefit-card">
        <view class="benefit-icon">📈</view>
        <view class="benefit-content">
          <view class="benefit-title">成绩提升</view>
          <view class="benefit-desc">体育考试不再担心</view>
        </view>
      </view>
      
      <view class="benefit-card">
        <view class="benefit-icon">😊</view>
        <view class="benefit-content">
          <view class="benefit-title">自信建立</view>
          <view class="benefit-desc">运动带来成就感</view>
        </view>
      </view>
    </view>

    <view class="value-summary">
      <text class="icon">💡</text>
      <text>21天科学训练，让孩子爱上运动，掌握技能，建立自信</text>
    </view>
  </view>

  <!-- 视频展示区域 (30%) -->
  <view class="video-section">
    <view class="section-title">
      <text class="icon">📚</text>
      <text>专业教学指导：</text>
    </view>
    
    <!-- 主要视频 -->
    <view class="main-video" bindtap="onVideoTap" data-video-id="1">
      <view class="video-thumbnail-main">
        <image src="/images/app-logo.png" mode="aspectFill"></image>
        <view class="play-icon-main">▶️</view>
        <view class="video-tag">核心教程</view>
      </view>
      <view class="video-info-main">
        <view class="video-title-main">21天跳绳完整教程</view>
        <view class="video-subtitle-main">专业教练系统指导，循序渐进</view>
      </view>
    </view>

    <!-- 辅助视频列表 -->
    <view class="video-list">
      <view class="video-item-small" bindtap="onVideoTap" data-video-id="2">
        <view class="video-thumbnail-small">
          <image src="/images/app-logo.png" mode="aspectFill"></image>
          <view class="play-icon-small">▶️</view>
        </view>
        <view class="video-info-small">
          <view class="video-title-small">基础动作</view>
          <view class="video-desc-small">握绳姿势</view>
        </view>
      </view>

      <view class="video-item-small" bindtap="onVideoTap" data-video-id="3">
        <view class="video-thumbnail-small">
          <image src="/images/app-logo.png" mode="aspectFill"></image>
          <view class="play-icon-small">▶️</view>
        </view>
        <view class="video-info-small">
          <view class="video-title-small">节奏训练</view>
          <view class="video-desc-small">找到节奏</view>
        </view>
      </view>

      <view class="video-item-small" bindtap="onVideoTap" data-video-id="4">
        <view class="video-thumbnail-small">
          <image src="/images/app-logo.png" mode="aspectFill"></image>
          <view class="play-icon-small">▶️</view>
        </view>
        <view class="video-info-small">
          <view class="video-title-small">连续练习</view>
          <view class="video-desc-small">技能突破</view>
        </view>
      </view>
    </view>

    <view class="video-feature">
      <text class="icon">✨</text>
      <text>专业教练示范，家长也能轻松指导孩子</text>
    </view>
  </view>

  <!-- 操作说明区域 (25%) -->
  <view class="operation-section">
    <view class="section-title">
      <text class="icon">📋</text>
      <text>每天简单三步：</text>
    </view>
    
    <view class="steps-balanced">
      <view class="step-card">
        <view class="step-number">1</view>
        <view class="step-content">
          <view class="step-title">学习观看</view>
          <view class="step-desc">观看教学视频<br/>学习正确方法</view>
          <view class="step-time">5分钟</view>
        </view>
      </view>
      
      <view class="step-card">
        <view class="step-number">2</view>
        <view class="step-content">
          <view class="step-title">指导练习</view>
          <view class="step-desc">陪伴孩子练习<br/>按方法指导</view>
          <view class="step-time">15分钟</view>
        </view>
      </view>
      
      <view class="step-card">
        <view class="step-number">3</view>
        <view class="step-content">
          <view class="step-title">记录分享</view>
          <view class="step-desc">记录进步成果<br/>分享成长喜悦</view>
          <view class="step-time">2分钟</view>
        </view>
      </view>
    </view>

    <view class="operation-benefits">
      <view class="operation-benefit">
        <text class="icon">⏰</text>
        <text>每天仅需20分钟</text>
      </view>
      <view class="operation-benefit">
        <text class="icon">🔄</text>
        <text>可补打卡，灵活安排</text>
      </view>
      <view class="operation-benefit">
        <text class="icon">👥</text>
        <text>妈妈群互助交流</text>
      </view>
    </view>
  </view>

  <!-- 社会认同区域 -->
  <view class="social-section">
    <view class="section-title">
      <text class="icon">🏆</text>
      <text>专业保障：</text>
    </view>
    
    <view class="social-content">
      <view class="authority-badges">
        <view class="badge-item">
          <text class="badge-icon">🎓</text>
          <text class="badge-text">专业教练</text>
        </view>
        <view class="badge-item">
          <text class="badge-icon">📊</text>
          <text class="badge-text">科学方法</text>
        </view>
        <view class="badge-item">
          <text class="badge-icon">🏅</text>
          <text class="badge-text">考试标准</text>
        </view>
      </view>
      
      <view class="testimonial-simple">
        <text class="icon">💬</text>
        <text>"基于小学体育考试标准设计，已验证有效的训练方法"</text>
      </view>
    </view>
  </view>

  <!-- 行动召唤区域 (15%) -->
  <view class="action-section">
    <view class="action-header">
      <view class="free-badge">
        <text class="icon">🎁</text>
        <text>限时免费</text>
      </view>
      <view class="urgency-text">专业指导，系统训练</view>
    </view>
    
    <button class="join-button-balanced" bindtap="onJoinTask">
      立即开始21天挑战
    </button>
    
    <view class="action-footer">
      <view class="safety-item">
        <text class="icon">✅</text>
        <text>随时退出</text>
      </view>
      <view class="safety-item">
        <text class="icon">✅</text>
        <text>完全免费</text>
      </view>
      <view class="safety-item">
        <text class="icon">✅</text>
        <text>专业指导</text>
      </view>
    </view>
  </view>
</view>
