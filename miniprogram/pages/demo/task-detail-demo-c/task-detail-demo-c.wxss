/* Demo版本C：平衡展示型 - 样式文件 */

.container {
  padding: 0;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #6a4c93 0%, #8b5fbf 100%);
  padding: 40rpx 30rpx 50rpx;
  text-align: center;
  color: white;
}

.task-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.task-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
}

/* 通用区域样式 */
.value-section,
.video-section,
.operation-section,
.social-section,
.action-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.section-title .icon {
  margin-right: 16rpx;
  font-size: 40rpx;
}

/* 价值主张区域 (30%) */
.value-section {
  margin-top: -30rpx;
  z-index: 10;
  position: relative;
}

.benefits-balanced {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.benefit-card {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 25rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #6a4c93;
}

.benefit-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #666;
}

.value-summary {
  display: flex;
  align-items: flex-start;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
  padding: 25rpx;
  border-radius: 16rpx;
  border: 2rpx solid #6a4c93;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.value-summary .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
  flex-shrink: 0;
}

/* 视频展示区域 (30%) */
.main-video {
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
}

.video-thumbnail-main {
  position: relative;
  height: 300rpx;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail-main image {
  width: 100%;
  height: 100%;
}

.play-icon-main {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60rpx;
  color: white;
  text-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
}

.video-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #6a4c93;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.video-info-main {
  padding: 25rpx;
}

.video-title-main {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.video-subtitle-main {
  font-size: 26rpx;
  color: #666;
}

.video-list {
  display: flex;
  gap: 16rpx;
  margin-bottom: 25rpx;
  overflow-x: auto;
}

.video-item-small {
  flex-shrink: 0;
  width: 200rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e9ecef;
}

.video-item-small:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.video-thumbnail-small {
  position: relative;
  height: 120rpx;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail-small image {
  width: 100%;
  height: 100%;
}

.play-icon-small {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.video-info-small {
  padding: 16rpx;
  text-align: center;
}

.video-title-small {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.video-desc-small {
  font-size: 20rpx;
  color: #666;
}

.video-feature {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #6a4c93;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.video-feature .icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 操作说明区域 (25%) */
.steps-balanced {
  display: flex;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.step-card {
  flex: 1;
  background: #f8f9fa;
  padding: 25rpx 20rpx;
  border-radius: 16rpx;
  text-align: center;
  position: relative;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: #6a4c93;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin: 0 auto 16rpx;
}

.step-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.step-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.step-time {
  font-size: 20rpx;
  color: #6a4c93;
  font-weight: bold;
}

.operation-benefits {
  display: flex;
  justify-content: space-around;
  background: #fff7f0;
  padding: 20rpx;
  border-radius: 12rpx;
}

.operation-benefit {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #333;
}

.operation-benefit .icon {
  margin-right: 8rpx;
  font-size: 26rpx;
  color: #ff7a45;
}

/* 社会认同区域 */
.social-content {
  text-align: center;
}

.authority-badges {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25rpx;
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
}

.badge-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.badge-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.testimonial-simple {
  display: flex;
  align-items: flex-start;
  background: #f0f4ff;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  font-style: italic;
}

.testimonial-simple .icon {
  margin-right: 12rpx;
  font-size: 28rpx;
  flex-shrink: 0;
}

/* 行动召唤区域 (15%) */
.action-section {
  text-align: center;
  background: linear-gradient(135deg, #6a4c93 0%, #8b5fbf 100%);
  color: white;
}

.action-header {
  margin-bottom: 30rpx;
}

.free-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.free-badge .icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.urgency-text {
  font-size: 32rpx;
  font-weight: bold;
}

.join-button-balanced {
  background: white;
  color: #6a4c93;
  font-size: 36rpx;
  font-weight: bold;
  padding: 30rpx 50rpx;
  border-radius: 50rpx;
  border: none;
  margin-bottom: 25rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 76, 147, 0.3);
}

.join-button-balanced:active {
  transform: scale(0.98);
}

.action-footer {
  display: flex;
  justify-content: space-around;
}

.safety-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.9;
}

.safety-item .icon {
  margin-right: 6rpx;
  font-size: 26rpx;
}
