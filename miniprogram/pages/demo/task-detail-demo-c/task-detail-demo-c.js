// Demo版本C：平衡展示型 - 逻辑文件
Page({
  data: {
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: "专业指导 + 系统训练 = 孩子技能飞跃",
      benefits: [
        {
          icon: "🏃‍♂️",
          title: "技能掌握",
          desc: "从0个到连续100个",
        },
        {
          icon: "💪",
          title: "习惯养成",
          desc: "主动要求练习运动",
        },
        {
          icon: "📈",
          title: "成绩提升",
          desc: "体育考试不再担心",
        },
        {
          icon: "😊",
          title: "自信建立",
          desc: "运动带来成就感",
        },
      ],
      mainVideo: {
        id: 1,
        title: "21天跳绳完整教程",
        subtitle: "专业教练系统指导，循序渐进",
        thumbnail: "/images/app-logo.png",
      },
      videos: [
        {
          id: 2,
          title: "基础动作",
          desc: "握绳姿势",
          thumbnail: "/images/app-logo.png",
        },
        {
          id: 3,
          title: "节奏训练",
          desc: "找到节奏",
          thumbnail: "/images/app-logo.png",
        },
        {
          id: 4,
          title: "连续练习",
          desc: "技能突破",
          thumbnail: "/images/app-logo.png",
        },
      ],
      steps: [
        {
          number: "1",
          title: "学习观看",
          desc: "观看教学视频\n学习正确方法",
          time: "5分钟",
        },
        {
          number: "2",
          title: "指导练习",
          desc: "陪伴孩子练习\n按方法指导",
          time: "15分钟",
        },
        {
          number: "3",
          title: "记录分享",
          desc: "记录进步成果\n分享成长喜悦",
          time: "2分钟",
        },
      ],
      operationBenefits: [
        {
          icon: "⏰",
          text: "每天仅需20分钟",
        },
        {
          icon: "🔄",
          text: "可补打卡，灵活安排",
        },
        {
          icon: "👥",
          text: "妈妈群互助交流",
        },
      ],
      badges: [
        {
          icon: "🎓",
          text: "专业教练",
        },
        {
          icon: "📊",
          text: "科学方法",
        },
        {
          icon: "🏅",
          text: "考试标准",
        },
      ],
      safetyItems: [
        {
          icon: "✅",
          text: "随时退出",
        },
        {
          icon: "✅",
          text: "完全免费",
        },
        {
          icon: "✅",
          text: "专业指导",
        },
      ],
    },
  },

  onLoad: function (options) {
    console.log("Demo版本C：平衡展示型页面加载");
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const videoId = e.currentTarget.dataset.videoId;
    let video;

    if (videoId == 1) {
      video = this.data.taskInfo.mainVideo;
    } else {
      video = this.data.taskInfo.videos.find((v) => v.id == videoId);
    }

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${video.subtitle || video.desc}`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 加入任务事件
  onJoinTask: function () {
    wx.showModal({
      title: "确认参加挑战",
      content: '确定要参加"21天跳绳养成计划"吗？\n将获得专业指导和系统训练',
      confirmText: "立即参加",
      cancelText: "再想想",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "成功加入挑战！",
            icon: "success",
            duration: 2000,
          });

          // 模拟跳转到打卡页面
          setTimeout(() => {
            wx.showToast({
              title: "即将跳转到打卡页面",
              icon: "none",
              duration: 1500,
            });
          }, 2000);
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "21天跳绳养成计划 - 专业指导系统训练",
      path: "/pages/task-detail-demo-c/task-detail-demo-c",
    };
  },
});
