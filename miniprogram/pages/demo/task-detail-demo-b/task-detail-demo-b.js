// Demo版本B：价值主张优先型 - 逻辑文件
Page({
  data: {
    taskInfo: {
      title: "21天跳绳养成计划",
      subtitle: '让孩子从"不会跳"到"连续100个"',
      benefits: [
        "跳绳技能：从0个到连续100个的飞跃",
        "运动习惯：从被动练习到主动要求",
        "体育考试：不再担心跳绳项目失分",
        "身体素质：协调性和自信心大幅提升",
      ],
      painPoints: [
        "孩子体育成绩差，跳绳总是绊脚",
        "马上要体育考试了，很焦虑",
        "不知道怎么教孩子正确方法",
      ],
      timeline: [
        {
          day: "第1-7天",
          desc: "掌握基本动作和节奏",
        },
        {
          day: "第8-14天",
          desc: "连续跳20-50个",
        },
        {
          day: "第15-21天",
          desc: "稳定连续100个",
        },
      ],
      videos: [
        {
          id: 1,
          title: "基础动作教学",
          subtitle: "正确姿势和握绳方法",
          thumbnail: "/images/app-logo.png",
          duration: "5分钟",
        },
        {
          id: 2,
          title: "节奏训练",
          subtitle: "找到适合的跳绳节奏",
          thumbnail: "/images/app-logo.png",
          duration: "8分钟",
        },
        {
          id: 3,
          title: "连续跳练习",
          subtitle: "从几个到几十个的突破",
          thumbnail: "/images/app-logo.png",
          duration: "10分钟",
        },
        {
          id: 4,
          title: "技巧提升",
          subtitle: "常见问题解决方法",
          thumbnail: "/images/app-logo.png",
          duration: "12分钟",
        },
      ],
      steps: [
        {
          number: "1️⃣",
          title: "看视频学方法（5分钟）",
          desc: "跟着教练学习正确动作",
        },
        {
          number: "2️⃣",
          title: "陪孩子练习（15分钟）",
          desc: "按照视频指导进行练习",
        },
        {
          number: "3️⃣",
          title: "记录进步打卡（2分钟）",
          desc: "记录成绩，分享进步",
        },
      ],
      supportItems: [
        "忘记打卡？可以补打卡",
        "不会教？有视频教程",
        "有问题？妈妈群随时答疑",
      ],
    },
  },

  onLoad: function (options) {
    console.log("Demo版本B：价值主张优先型页面加载");
  },

  // 视频点击事件
  onVideoTap: function (e) {
    const videoId = e.currentTarget.dataset.videoId;
    const video = this.data.taskInfo.videos.find((v) => v.id == videoId);

    wx.showModal({
      title: "即将跳转到视频号",
      content: `将要播放：${video.title}\n${video.subtitle}`,
      confirmText: "去观看",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 这里模拟跳转到微信视频号
          wx.showToast({
            title: "跳转到视频号播放",
            icon: "none",
            duration: 2000,
          });
        }
      },
    });
  },

  // 加入任务事件
  onJoinTask: function () {
    wx.showModal({
      title: "确认参加挑战",
      content: '确定要参加"21天跳绳养成计划"吗？',
      confirmText: "立即参加",
      cancelText: "再想想",
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "成功加入挑战！",
            icon: "success",
            duration: 2000,
          });

          // 模拟跳转到打卡页面
          setTimeout(() => {
            wx.showToast({
              title: "即将跳转到打卡页面",
              icon: "none",
              duration: 1500,
            });
          }, 2000);
        }
      },
    });
  },

  onShareAppMessage: function () {
    return {
      title: "21天跳绳养成计划 - 让孩子爱上运动",
      path: "/pages/task-detail-demo-b/task-detail-demo-b",
    };
  },
});
