/* Demo版本B：价值主张优先型 - 样式文件 */

.container {
  padding: 0;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #ff7a45 0%, #ff9a6b 100%);
  padding: 40rpx 30rpx 50rpx;
  text-align: center;
  color: white;
}

.task-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.task-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
}

/* 通用区域样式 */
.value-section,
.video-section,
.operation-section,
.social-section,
.action-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.section-title .icon {
  margin-right: 16rpx;
  font-size: 40rpx;
}

/* 核心价值主张区域 (40%) */
.value-section {
  margin-top: -30rpx;
  z-index: 10;
  position: relative;
}

.benefits-list {
  margin-bottom: 40rpx;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #fff7f0;
  border-radius: 16rpx;
  border-left: 6rpx solid #ff7a45;
}

.check-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
  flex-shrink: 0;
}

.benefit-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
}

.pain-points {
  background: #f0f9ff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  border-left: 6rpx solid #4a90e2;
}

.pain-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 20rpx;
}

.pain-title .icon {
  margin-right: 12rpx;
}

.pain-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  padding-left: 20rpx;
  position: relative;
}

.pain-item::before {
  content: '"';
  position: absolute;
  left: 0;
  color: #4a90e2;
  font-weight: bold;
}

.pain-item::after {
  content: '"';
  color: #4a90e2;
  font-weight: bold;
}

.timeline {
  margin-top: 30rpx;
}

.timeline-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.timeline-dot {
  width: 20rpx;
  height: 20rpx;
  background: #ff7a45;
  border-radius: 50%;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.timeline-day {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff7a45;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 28rpx;
  color: #666;
}

/* 专业教学视频区域 (25%) */
.videos-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.video-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.video-item:active {
  transform: scale(0.98);
  background: #e9ecef;
}

.video-thumbnail {
  position: relative;
  height: 200rpx;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail image {
  width: 100%;
  height: 100%;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.video-info {
  padding: 20rpx;
}

.video-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.video-subtitle {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.video-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

.video-tip .icon {
  margin-right: 12rpx;
}

/* 操作简单区域 (20%) */
.steps-list {
  margin-bottom: 30rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.step-number {
  margin-right: 20rpx;
  font-size: 36rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

.support-info {
  background: #fff7f0;
  padding: 25rpx;
  border-radius: 12rpx;
}

.support-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.support-item:last-child {
  margin-bottom: 0;
}

.support-item .icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 社会认同区域 */
.authority-info {
  margin-bottom: 20rpx;
}

.authority-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.authority-item .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.testimonial {
  display: flex;
  align-items: flex-start;
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  font-style: italic;
}

.testimonial .icon {
  margin-right: 12rpx;
  font-size: 32rpx;
  flex-shrink: 0;
}

/* 行动召唤区域 (15%) */
.action-section {
  text-align: center;
  background: linear-gradient(135deg, #ff7a45 0%, #ff9a6b 100%);
  color: white;
}

.free-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.free-tag .icon {
  margin-right: 12rpx;
  font-size: 36rpx;
}

.join-button {
  background: white;
  color: #ff7a45;
  font-size: 36rpx;
  font-weight: bold;
  padding: 30rpx 60rpx;
  border-radius: 50rpx;
  border: none;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 122, 69, 0.3);
}

.join-button:active {
  transform: scale(0.98);
}

.safety-note {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  opacity: 0.9;
}

.safety-note .icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}
