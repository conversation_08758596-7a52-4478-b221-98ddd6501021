/* 方案A - 荣誉契约页面样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 契约状态头部 */
.contract-header {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  padding: 32rpx;
  color: #FFFFFF;
}

.contract-status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.status-badge {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
}

.status-badge.pending {
  background-color: #FF7A45;
  color: #FFFFFF;
}

.status-badge.completed {
  background-color: #52C41A;
  color: #FFFFFF;
}

.status-badge.failed {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

/* 通用区块样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 契约详情区块 */
.contract-details-section {
  padding: 32rpx;
}

.contract-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #FFD700;
}

/* 契约目标 */
.contract-goal {
  margin-bottom: 32rpx;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.goal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.goal-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.goal-progress .progress-text {
  font-size: 24rpx;
  color: #FF7A45;
  font-weight: 600;
}

.goal-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  display: block;
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bg {
  flex: 1;
  height: 8rpx;
  background-color: #E5E5E5;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #FF7A45;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

/* 约定奖励 */
.contract-reward {
  margin-bottom: 32rpx;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.reward-header {
  margin-bottom: 16rpx;
}

.reward-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.reward-content {
  display: flex;
  align-items: center;
  background-color: #FFF2ED;
  border-radius: 12rpx;
  padding: 24rpx;
}

.reward-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.reward-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* 见证人 */
.witnesses-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.witnesses-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.witnesses-count {
  font-size: 24rpx;
  color: #666666;
}

.witnesses-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.witness-item {
  display: flex;
  align-items: center;
  background-color: #F7F8FA;
  border-radius: 12rpx;
  padding: 16rpx;
}

.witness-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #FFD700;
}

.witness-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-right: 16rpx;
}

.witness-role {
  font-size: 24rpx;
  color: #666666;
  background-color: #FFFFFF;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

/* 时间线区块 */
.timeline-section {
  padding: 0 32rpx 32rpx;
}

.timeline-container {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  max-height: 600rpx;
  overflow-y: auto;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 30rpx;
  top: 60rpx;
  width: 2rpx;
  height: 40rpx;
  background-color: #E5E5E5;
}

.timeline-item.completed::after {
  background-color: #52C41A;
}

.timeline-dot {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  font-size: 32rpx;
  flex-shrink: 0;
}

.timeline-item.completed .timeline-dot {
  background-color: #E8F5E8;
}

.timeline-item.current .timeline-dot {
  background-color: #FFF2ED;
  border: 2rpx solid #FF7A45;
}

.timeline-item.missed .timeline-dot {
  background-color: #FFF2F0;
}

.timeline-item.future .timeline-dot {
  background-color: #F7F8FA;
  color: #999999;
  font-size: 24rpx;
}

.timeline-content {
  flex: 1;
  padding-top: 8rpx;
}

.timeline-day {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 4rpx;
}

.timeline-date {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 24rpx;
  color: #FF7A45;
  font-weight: 500;
}

/* 家长鼓励区 */
.encouragement-section {
  padding: 0 32rpx 32rpx;
}

.encouragement-list {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.encouragement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.encouragement-item:last-child {
  margin-bottom: 0;
}

.encourager-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.encouragement-content {
  flex: 1;
}

.encouragement-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.encourager-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.encouragement-time {
  font-size: 24rpx;
  color: #999999;
}

.encouragement-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.add-encouragement {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.encouragement-input {
  flex: 1;
  background-color: #F7F8FA;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  border: none;
}

.send-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.send-btn[disabled] {
  background-color: #CCCCCC;
  color: #999999;
}

/* 契约完成庆祝 */
.completion-section {
  padding: 0 32rpx 32rpx;
}

.completion-card {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  color: #FFFFFF;
}

.completion-animation {
  margin-bottom: 32rpx;
}

.completion-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.completion-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
}

.completion-desc {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

.completion-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 20rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.action-btn.secondary {
  background-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
}

.action-btn.primary {
  background-color: #FFFFFF;
  color: #FF7A45;
}

/* 契约已完成展示 */
.completed-section {
  padding: 0 32rpx 32rpx;
}

.completed-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #52C41A;
}

.medal-display {
  margin-bottom: 32rpx;
}

.medal-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.medal-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.medal-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.completion-info {
  margin-bottom: 32rpx;
}

.completion-date, .completion-reward {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

.share-btn {
  background-color: #52C41A;
  color: #FFFFFF;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 创建新契约 */
.create-section {
  padding: 0 32rpx 32rpx;
}

.create-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 2rpx dashed #E5E5E5;
}

.create-info {
  flex: 1;
  margin-right: 24rpx;
}

.create-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.create-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.create-btn {
  background-color: #FF7A45;
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
}

/* 授勋成功弹窗 */
.award-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.award-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  text-align: center;
}

.award-animation {
  margin-bottom: 32rpx;
}

.award-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 16rpx;
}

.award-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.award-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.medal-preview {
  background-color: #FFF2ED;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.medal-preview .medal-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.medal-preview .medal-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7A45;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 60rpx;
}
