// 方案A - 荣誉契约页面
Page({
  data: {
    // 是否有活跃契约
    hasActiveContract: true,

    // 契约信息
    contractInfo: {
      id: 1,
      title: "21天跳绳挑战契约",
      description: "连续打卡21天，掌握跳绳基本技能，提升身体素质",
      status: "active", // active, pending, completed, failed
      currentDay: 14,
      totalDays: 21,
      progress: 67,
      reward: "一次科技馆之旅",
      child: {
        name: "小明",
        avatar: "/images/avatar_default.png",
      },
      witnesses: [
        {
          id: 1,
          name: "妈妈",
          avatar: "/images/avatar_default.png",
          role: "首席见证官",
        },
        {
          id: 2,
          name: "爸爸",
          avatar: "/images/avatar_default.png",
          role: "见证人",
        },
      ],
      createdDate: "2024-01-01",
      completedDate: "",
      earnedMedal: {
        name: "家庭契约守护者",
        description: "完成第一个家庭荣誉契约",
        icon: "🏅",
      },
    },

    // 时间线数据
    timelineData: [
      { day: 1, date: "01-01", status: "completed", desc: "开始挑战！" },
      { day: 2, date: "01-02", status: "completed" },
      { day: 3, date: "01-03", status: "completed" },
      { day: 4, date: "01-04", status: "completed" },
      { day: 5, date: "01-05", status: "completed", desc: "坚持一周！" },
      { day: 6, date: "01-06", status: "completed" },
      { day: 7, date: "01-07", status: "completed" },
      { day: 8, date: "01-08", status: "completed" },
      { day: 9, date: "01-09", status: "completed" },
      { day: 10, date: "01-10", status: "completed", desc: "坚持十天！" },
      { day: 11, date: "01-11", status: "completed" },
      { day: 12, date: "01-12", status: "completed" },
      { day: 13, date: "01-13", status: "completed" },
      { day: 14, date: "01-14", status: "completed" },
      { day: 15, date: "01-15", status: "current", desc: "今天的挑战" },
      { day: 16, date: "01-16", status: "future" },
      { day: 17, date: "01-17", status: "future" },
      { day: 18, date: "01-18", status: "future" },
      { day: 19, date: "01-19", status: "future" },
      { day: 20, date: "01-20", status: "future" },
      { day: 21, date: "01-21", status: "future", desc: "最后一天！" },
    ],

    // 家长鼓励消息
    encouragements: [
      {
        id: 1,
        name: "妈妈",
        avatar: "/images/avatar_default.png",
        message: "小明今天的跳绳进步很大，继续加油！",
        time: "2小时前",
      },
      {
        id: 2,
        name: "爸爸",
        avatar: "/images/avatar_default.png",
        message: "坚持就是胜利，爸爸为你骄傲！",
        time: "1天前",
      },
      {
        id: 3,
        name: "妈妈",
        avatar: "/images/avatar_default.png",
        message: "已经坚持两周了，真棒！距离目标越来越近了。",
        time: "3天前",
      },
    ],

    // 新鼓励消息
    newEncouragement: "",

    // 授勋弹窗
    showAwardModal: false,
  },

  onLoad(options) {
    console.log("方案A荣誉契约页面加载");
    const action = options.action;
    const campId = options.campId;

    if (action === "create") {
      this.showCreateContractFlow(campId);
    } else {
      this.loadContractData();
    }
  },

  /**
   * 加载契约数据
   */
  loadContractData() {
    // 模拟加载契约数据
    console.log("加载契约数据");
  },

  /**
   * 显示创建契约流程
   */
  showCreateContractFlow(campId) {
    wx.showModal({
      title: "创建家庭荣誉契约",
      content: "这里将展示契约创建流程",
      showCancel: false,
      success: () => {
        // 跳转到契约创建页面或显示创建表单
        console.log("创建契约流程:", campId);
      },
    });
  },

  /**
   * 鼓励消息输入
   */
  onEncouragementInput(e) {
    this.setData({
      newEncouragement: e.detail.value,
    });
  },

  /**
   * 发送鼓励消息
   */
  sendEncouragement() {
    if (!this.data.newEncouragement.trim()) {
      return;
    }

    const newMessage = {
      id: Date.now(),
      name: "妈妈", // 这里应该是当前用户的身份
      avatar: "/images/avatar_default.png",
      message: this.data.newEncouragement,
      time: "刚刚",
    };

    const encouragements = [newMessage, ...this.data.encouragements];
    this.setData({
      encouragements,
      newEncouragement: "",
    });

    wx.showToast({
      title: "鼓励发送成功",
      icon: "success",
    });
  },

  /**
   * 确认已兑现奖励
   */
  confirmReward() {
    wx.showModal({
      title: "确认兑现奖励",
      content: `确认已经兑现了"${this.data.contractInfo.reward}"这个奖励吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: "奖励确认成功",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 为孩子授勋
   */
  awardMedal() {
    wx.showModal({
      title: "授勋确认",
      content: `确定要为${this.data.contractInfo.child.name}颁发"家庭契约守护者"勋章吗？`,
      success: (res) => {
        if (res.confirm) {
          // 更新契约状态为已完成
          const contractInfo = { ...this.data.contractInfo };
          contractInfo.status = "completed";
          contractInfo.completedDate = new Date().toLocaleDateString();

          this.setData({
            contractInfo,
            showAwardModal: true,
          });
        }
      },
    });
  },

  /**
   * 关闭授勋弹窗
   */
  closeAwardModal() {
    this.setData({
      showAwardModal: false,
    });
  },

  /**
   * 生成证书
   */
  generateCertificate() {
    this.setData({
      showAwardModal: false,
    });

    wx.navigateTo({
      url: `/pages/demo/growth-v2-a-poster/growth-v2-a-poster?type=certificate&contractId=${this.data.contractInfo.id}`,
    });
  },

  /**
   * 分享成就
   */
  shareAchievement() {
    wx.navigateTo({
      url: `/pages/demo/growth-v2-a-poster/growth-v2-a-poster?type=achievement&contractId=${this.data.contractInfo.id}`,
    });
  },

  /**
   * 创建新契约
   */
  createNewContract() {
    wx.showModal({
      title: "创建新契约",
      content: "创建新的家庭荣誉契约功能开发中...",
      showCancel: false,
    });
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: `${this.data.contractInfo.child.name}的家庭荣誉契约`,
      path: "/pages/demo/growth-v2-a-contract/growth-v2-a-contract",
      imageUrl: "/images/share_contract.jpg",
    };
  },
});
