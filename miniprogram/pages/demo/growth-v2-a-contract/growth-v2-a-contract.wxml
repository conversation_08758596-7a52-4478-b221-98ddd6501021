<!-- 方案A - 荣誉契约页面 -->
<view class="page-container">
  <!-- 契约状态头部 -->
  <view class="contract-header">
    <view class="contract-status-card">
      <view class="status-info">
        <text class="status-title">家庭荣誉契约</text>
        <text class="status-desc">{{contractInfo.title}}</text>
      </view>
      <view class="status-badge {{contractInfo.status}}">
        <text wx:if="{{contractInfo.status === 'active'}}">进行中</text>
        <text wx:elif="{{contractInfo.status === 'completed'}}">已完成</text>
        <text wx:elif="{{contractInfo.status === 'pending'}}">待授勋</text>
        <text wx:else>已失败</text>
      </view>
    </view>
  </view>

  <!-- 契约详情 -->
  <view class="contract-details-section">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text class="title-text">契约详情</text>
    </view>
    <view class="contract-card">
      <!-- 契约目标 -->
      <view class="contract-goal">
        <view class="goal-header">
          <text class="goal-title">挑战目标</text>
          <view class="goal-progress">
            <text class="progress-text">{{contractInfo.currentDay}}/{{contractInfo.totalDays}} 天</text>
          </view>
        </view>
        <text class="goal-desc">{{contractInfo.description}}</text>
        <view class="progress-bar">
          <view class="progress-bg">
            <view class="progress-fill" style="width: {{contractInfo.progress}}%"></view>
          </view>
          <text class="progress-percent">{{contractInfo.progress}}%</text>
        </view>
      </view>

      <!-- 约定奖励 -->
      <view class="contract-reward">
        <view class="reward-header">
          <text class="reward-title">约定奖励</text>
        </view>
        <view class="reward-content">
          <text class="reward-icon">🎁</text>
          <text class="reward-text">{{contractInfo.reward}}</text>
        </view>
      </view>

      <!-- 见证人 -->
      <view class="contract-witnesses">
        <view class="witnesses-header">
          <text class="witnesses-title">见证人</text>
          <text class="witnesses-count">{{contractInfo.witnesses.length}} 人</text>
        </view>
        <view class="witnesses-list">
          <view class="witness-item" wx:for="{{contractInfo.witnesses}}" wx:key="id">
            <image class="witness-avatar" src="{{item.avatar}}" mode="aspectFill" />
            <text class="witness-name">{{item.name}}</text>
            <text class="witness-role">{{item.role}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 契约进度时间线 -->
  <view class="timeline-section">
    <view class="section-title">
      <text class="title-icon">📅</text>
      <text class="title-text">进度时间线</text>
    </view>
    <view class="timeline-container">
      <view class="timeline-item {{item.status}}" wx:for="{{timelineData}}" wx:key="day">
        <view class="timeline-dot">
          <text wx:if="{{item.status === 'completed'}}">✅</text>
          <text wx:elif="{{item.status === 'current'}}">📅</text>
          <text wx:elif="{{item.status === 'missed'}}">❌</text>
          <text wx:else>{{item.day}}</text>
        </view>
        <view class="timeline-content">
          <text class="timeline-day">第{{item.day}}天</text>
          <text class="timeline-date">{{item.date}}</text>
          <text class="timeline-desc" wx:if="{{item.desc}}">{{item.desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 家长鼓励区 -->
  <view class="encouragement-section" wx:if="{{contractInfo.status === 'active'}}">
    <view class="section-title">
      <text class="title-icon">💬</text>
      <text class="title-text">家长鼓励</text>
    </view>
    <view class="encouragement-list">
      <view class="encouragement-item" wx:for="{{encouragements}}" wx:key="id">
        <image class="encourager-avatar" src="{{item.avatar}}" mode="aspectFill" />
        <view class="encouragement-content">
          <view class="encouragement-header">
            <text class="encourager-name">{{item.name}}</text>
            <text class="encouragement-time">{{item.time}}</text>
          </view>
          <text class="encouragement-text">{{item.message}}</text>
        </view>
      </view>
    </view>
    <view class="add-encouragement">
      <input class="encouragement-input" placeholder="给孩子一些鼓励吧..." value="{{newEncouragement}}" bindinput="onEncouragementInput" />
      <button class="send-btn" bindtap="sendEncouragement" disabled="{{!newEncouragement}}">发送</button>
    </view>
  </view>

  <!-- 契约完成庆祝 -->
  <view class="completion-section" wx:if="{{contractInfo.status === 'pending'}}">
    <view class="section-title">
      <text class="title-icon">🎉</text>
      <text class="title-text">契约达成</text>
    </view>
    <view class="completion-card">
      <view class="completion-animation">
        <text class="completion-icon">🏆</text>
        <text class="completion-title">恭喜完成契约！</text>
        <text class="completion-desc">{{contractInfo.child.name}} 已经完成了所有挑战目标</text>
      </view>
      <view class="completion-actions">
        <button class="action-btn secondary" bindtap="confirmReward">确认已兑现奖励</button>
        <button class="action-btn primary" bindtap="awardMedal">为TA授勋</button>
      </view>
    </view>
  </view>

  <!-- 契约已完成展示 -->
  <view class="completed-section" wx:if="{{contractInfo.status === 'completed'}}">
    <view class="section-title">
      <text class="title-icon">👑</text>
      <text class="title-text">荣誉成就</text>
    </view>
    <view class="completed-card">
      <view class="medal-display">
        <text class="medal-icon">🏅</text>
        <text class="medal-name">{{contractInfo.earnedMedal.name}}</text>
        <text class="medal-desc">{{contractInfo.earnedMedal.description}}</text>
      </view>
      <view class="completion-info">
        <text class="completion-date">完成时间：{{contractInfo.completedDate}}</text>
        <text class="completion-reward">已兑现奖励：{{contractInfo.reward}}</text>
      </view>
      <button class="share-btn" bindtap="shareAchievement">分享成就</button>
    </view>
  </view>

  <!-- 创建新契约 -->
  <view class="create-section" wx:if="{{!hasActiveContract}}">
    <view class="section-title">
      <text class="title-icon">➕</text>
      <text class="title-text">创建新契约</text>
    </view>
    <view class="create-card">
      <view class="create-info">
        <text class="create-title">开启新的挑战</text>
        <text class="create-desc">与家人一起制定新的成长目标，获得更多荣誉</text>
      </view>
      <button class="create-btn" bindtap="createNewContract">立即创建</button>
    </view>
  </view>

  <!-- 授勋成功弹窗 -->
  <view class="award-modal {{showAwardModal ? 'show' : ''}}" wx:if="{{showAwardModal}}">
    <view class="modal-content">
      <view class="award-animation">
        <text class="award-icon">👑</text>
        <text class="award-title">授勋成功！</text>
        <text class="award-desc">{{contractInfo.child.name}} 获得了专属荣誉勋章</text>
      </view>
      <view class="medal-preview">
        <text class="medal-icon">🏅</text>
        <text class="medal-name">家庭契约守护者</text>
      </view>
      <view class="modal-actions">
        <button class="action-btn secondary" bindtap="closeAwardModal">继续</button>
        <button class="action-btn primary" bindtap="generateCertificate">生成证书</button>
      </view>
    </view>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>
