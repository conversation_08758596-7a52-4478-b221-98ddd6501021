/* 打卡表单页面 V4版本样式 */

.page-container {
  background-color: #F7F8FA;
  min-height: 100vh;
  padding: 0 32rpx 120rpx;
}

/* 顶部进度展示区 */
.progress-header {
  background: linear-gradient(135deg, #FF7A45, #FF9A6B);
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #FFFFFF;
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.3);
}

.camp-info {
  flex: 1;
}

.camp-title {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.camp-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.progress-circle {
  width: 120rpx;
  height: 120rpx;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-inner {
  width: 80rpx;
  height: 80rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #FF7A45;
}

/* 通用区块样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

/* 今日任务区 */
.task-section {
  margin-bottom: 48rpx;
}

.task-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.task-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.task-icon {
  font-size: 48rpx;
}

/* 表单区域 */
.checkin-form-section {
  margin-bottom: 48rpx;
}

.form-group {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.label-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.required-mark {
  color: #FF4D4F;
  font-size: 24rpx;
}

.optional-mark {
  font-size: 20rpx;
  color: #999999;
  background-color: #F0F0F0;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 练习时长选择器 */
.duration-selector {
  margin-bottom: 24rpx;
}

.duration-slider {
  width: 100%;
  margin-bottom: 16rpx;
}

.duration-display {
  text-align: center;
  margin-bottom: 16rpx;
}

.duration-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF7A45;
}

.duration-unit {
  font-size: 24rpx;
  color: #666666;
  margin-left: 8rpx;
}

.duration-tips {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.tip-item {
  font-size: 20rpx;
  color: #999999;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background-color: #F0F0F0;
  transition: all 0.3s ease;
}

.tip-item.active {
  background-color: #E8F4FD;
  color: #4A90E2;
}

/* 成绩输入 */
.score-inputs {
  display: flex;
  gap: 24rpx;
}

.score-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background-color: #F7F8FA;
  border-radius: 12rpx;
}

.score-label {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

.score-input {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.score-unit {
  font-size: 20rpx;
  color: #999999;
}

/* 媒体上传 */
.upload-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.upload-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 24rpx 16rpx;
  background-color: #F7F8FA;
  border: 2rpx dashed #D9D9D9;
  border-radius: 12rpx;
  color: #666666;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background-color: #E8F4FD;
  border-color: #4A90E2;
  color: #4A90E2;
}

.upload-icon {
  font-size: 32rpx;
}

.media-preview {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.media-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.media-thumb {
  width: 100%;
  height: 100%;
}

.media-type {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  font-size: 16rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #FFFFFF;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.media-remove {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(255, 77, 79, 0.9);
  color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

/* 心得分享 */
.experience-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  background-color: #F7F8FA;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333333;
}

.char-count {
  text-align: right;
  font-size: 20rpx;
  color: #999999;
  margin-top: 8rpx;
}

/* 奖励预览区 */
.reward-preview-section {
  margin-bottom: 48rpx;
}

.reward-cards {
  display: flex;
  gap: 16rpx;
}

.reward-card {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.reward-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.reward-name {
  font-size: 20rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.reward-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.reward-card.points .reward-value {
  color: #FF7A45;
}

.reward-card.badge .reward-value {
  color: #52C41A;
}

.reward-card.streak .reward-value {
  color: #4A90E2;
}

/* 提交区域 */
.submit-section {
  text-align: center;
}

.submit-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
  font-size: 24rpx;
  color: #999999;
}

.submit-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  margin-bottom: 24rpx;
}

.submit-btn.active {
  background: linear-gradient(135deg, #52C41A, #73D13D);
  color: #FFFFFF;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.submit-btn.disabled {
  background-color: #F0F0F0;
  color: #CCCCCC;
}

.submit-btn.active:active {
  transform: scale(0.98);
}

.help-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
}

.help-text {
  color: #999999;
}

.help-link {
  color: #4A90E2;
  text-decoration: underline;
}

/* 帮助弹窗 */
.help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.help-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
}

.modal-body {
  padding: 32rpx;
}

.help-item {
  margin-bottom: 24rpx;
}

.help-question {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.help-answer {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

/* 底部安全距离 */
.safe-area-bottom {
  height: 120rpx;
}
