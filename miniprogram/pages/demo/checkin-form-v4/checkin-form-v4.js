// 打卡表单页面 V4版本
const app = getApp();

Page({
  data: {
    // 训练营信息
    campInfo: {
      id: 1,
      title: "《零基础21天跳绳挑战营》",
      currentDay: 8,
      totalDays: 21,
      progressPercent: 38
    },
    
    // 今日任务信息
    taskInfo: {
      name: "连续跳绳100个",
      description: "保持稳定的节奏，连续完成100个跳绳动作，注意手腕发力和脚步协调。",
      icon: "🏃‍♂️"
    },
    
    // 表单数据
    formData: {
      duration: 30,           // 练习时长（分钟）
      score1min: '',          // 1分钟跳绳成绩
      scoreContinuous: '',    // 连续跳绳成绩
      mediaList: [],          // 媒体文件列表
      experience: ''          // 心得分享
    },
    
    // 奖励预览
    rewardPreview: {
      points: 25,
      streakDays: 8,
      badge: null // 如果有勋章奖励
    },
    
    // 状态控制
    canSubmit: true,
    isSubmitting: false,
    showHelpModal: false
  },

  onLoad(options) {
    console.log("打卡表单页面加载", options);
    this.initPage(options);
  },

  /**
   * 初始化页面
   */
  initPage(options) {
    const campId = options.campId || 1;
    
    // 加载训练营信息
    this.loadCampInfo(campId);
    
    // 加载今日任务
    this.loadTodayTask(campId);
    
    // 计算奖励预览
    this.calculateRewardPreview();
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: "今日打卡"
    });
  },

  /**
   * 加载训练营信息
   */
  loadCampInfo(campId) {
    // 模拟数据，实际应该从API获取
    const campInfo = {
      id: campId,
      title: "《零基础21天跳绳挑战营》",
      currentDay: 8,
      totalDays: 21,
      progressPercent: Math.round((8 / 21) * 100)
    };
    
    this.setData({
      campInfo: campInfo
    });
  },

  /**
   * 加载今日任务
   */
  loadTodayTask(campId) {
    // 模拟数据，实际应该从API获取
    const taskInfo = {
      name: "连续跳绳100个",
      description: "保持稳定的节奏，连续完成100个跳绳动作，注意手腕发力和脚步协调。",
      icon: "🏃‍♂️"
    };
    
    this.setData({
      taskInfo: taskInfo
    });
  },

  /**
   * 计算奖励预览
   */
  calculateRewardPreview() {
    const { duration, score1min, scoreContinuous, mediaList, experience } = this.data.formData;
    
    // 基础积分计算
    let points = Math.max(10, Math.floor(duration / 5) * 2);
    
    // 成绩奖励
    if (score1min && parseInt(score1min) >= 50) points += 5;
    if (scoreContinuous && parseInt(scoreContinuous) >= 100) points += 5;
    
    // 媒体奖励
    if (mediaList.length > 0) points += 3;
    
    // 文字分享奖励
    if (experience.length >= 20) points += 2;
    
    // 检查是否有勋章奖励
    let badge = null;
    if (this.data.campInfo.currentDay === 7) {
      badge = { name: "坚持一周" };
    }
    
    this.setData({
      rewardPreview: {
        points: points,
        streakDays: this.data.campInfo.currentDay,
        badge: badge
      }
    });
  },

  /**
   * 练习时长改变
   */
  onDurationChange(e) {
    const duration = e.detail.value;
    this.setData({
      'formData.duration': duration,
      canSubmit: duration >= 5
    });
    
    // 重新计算奖励预览
    this.calculateRewardPreview();
  },

  /**
   * 1分钟成绩输入
   */
  onScore1minInput(e) {
    this.setData({
      'formData.score1min': e.detail.value
    });
    this.calculateRewardPreview();
  },

  /**
   * 连续跳绳成绩输入
   */
  onScoreContinuousInput(e) {
    this.setData({
      'formData.scoreContinuous': e.detail.value
    });
    this.calculateRewardPreview();
  },

  /**
   * 心得分享输入
   */
  onExperienceInput(e) {
    this.setData({
      'formData.experience': e.detail.value
    });
    this.calculateRewardPreview();
  },

  /**
   * 上传视频
   */
  uploadVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        const mediaItem = {
          type: 'video',
          path: res.tempFilePath,
          thumb: res.thumbTempFilePath,
          duration: res.duration
        };
        
        const mediaList = [...this.data.formData.mediaList, mediaItem];
        this.setData({
          'formData.mediaList': mediaList
        });
        
        this.calculateRewardPreview();
        
        wx.showToast({
          title: "视频添加成功",
          icon: "success"
        });
      },
      fail: (err) => {
        console.error("选择视频失败", err);
      }
    });
  },

  /**
   * 上传照片
   */
  uploadPhoto() {
    wx.chooseImage({
      count: 3,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newMediaItems = res.tempFilePaths.map(path => ({
          type: 'image',
          path: path,
          thumb: path
        }));
        
        const mediaList = [...this.data.formData.mediaList, ...newMediaItems];
        this.setData({
          'formData.mediaList': mediaList
        });
        
        this.calculateRewardPreview();
        
        wx.showToast({
          title: "图片添加成功",
          icon: "success"
        });
      },
      fail: (err) => {
        console.error("选择图片失败", err);
      }
    });
  },

  /**
   * 移除媒体文件
   */
  removeMedia(e) {
    const index = e.currentTarget.dataset.index;
    const mediaList = this.data.formData.mediaList;
    mediaList.splice(index, 1);
    
    this.setData({
      'formData.mediaList': mediaList
    });
    
    this.calculateRewardPreview();
  },

  /**
   * 提交打卡
   */
  submitCheckin() {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return;
    }
    
    // 表单验证
    if (this.data.formData.duration < 5) {
      wx.showToast({
        title: "练习时长至少5分钟",
        icon: "none"
      });
      return;
    }
    
    this.setData({
      isSubmitting: true
    });
    
    // 模拟提交过程
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });
      
      // 跳转到打卡成功页面
      wx.redirectTo({
        url: `/pages/checkin-success/checkin-success?campId=${this.data.campInfo.id}&points=${this.data.rewardPreview.points}`
      });
    }, 2000);
  },

  /**
   * 显示帮助
   */
  showHelp() {
    this.setData({
      showHelpModal: true
    });
  },

  /**
   * 隐藏帮助
   */
  hideHelp() {
    this.setData({
      showHelpModal: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
