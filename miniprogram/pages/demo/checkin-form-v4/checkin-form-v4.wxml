<!-- 打卡表单页面 V4版本 - 完整功能 -->
<view class="page-container">
  <!-- 顶部进度展示区 -->
  <view class="progress-header">
    <view class="camp-info">
      <text class="camp-title">{{campInfo.title}}</text>
      <text class="camp-subtitle">第{{campInfo.currentDay}}天 / 共{{campInfo.totalDays}}天</text>
    </view>
    <view class="progress-circle">
      <view class="circle-progress" style="background: conic-gradient(#FF7A45 {{campInfo.progressPercent * 3.6}}deg, #F0F0F0 0deg);">
        <view class="circle-inner">
          <text class="progress-text">{{campInfo.progressPercent}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日任务目标 -->
  <view class="task-section">
    <view class="section-title">
      <text class="title-icon">🎯</text>
      <text class="title-text">今日任务</text>
    </view>
    <view class="task-card">
      <view class="task-content">
        <text class="task-name">{{taskInfo.name}}</text>
        <text class="task-description">{{taskInfo.description}}</text>
      </view>
      <view class="task-icon">{{taskInfo.icon}}</view>
    </view>
  </view>

  <!-- 核心打卡表单区 -->
  <view class="checkin-form-section">
    <view class="section-title">
      <text class="title-icon">📝</text>
      <text class="title-text">记录今日练习</text>
    </view>
    
    <!-- 练习时长（必填） -->
    <view class="form-group required">
      <view class="form-label">
        <text class="label-text">练习时长</text>
        <text class="required-mark">*</text>
      </view>
      <view class="duration-selector">
        <slider class="duration-slider" 
                min="5" 
                max="120" 
                value="{{formData.duration}}" 
                step="5"
                show-value="{{false}}"
                bindchange="onDurationChange"
                activeColor="#FF7A45"
                backgroundColor="#F0F0F0" />
        <view class="duration-display">
          <text class="duration-value">{{formData.duration}}</text>
          <text class="duration-unit">分钟</text>
        </view>
      </view>
      <view class="duration-tips">
        <text class="tip-item {{formData.duration >= 15 ? 'active' : ''}}">15分钟+</text>
        <text class="tip-item {{formData.duration >= 30 ? 'active' : ''}}">30分钟+</text>
        <text class="tip-item {{formData.duration >= 60 ? 'active' : ''}}">60分钟+</text>
      </view>
    </view>

    <!-- 跳绳成绩（可选） -->
    <view class="form-group optional">
      <view class="form-label">
        <text class="label-text">跳绳成绩</text>
        <text class="optional-mark">选填</text>
      </view>
      <view class="score-inputs">
        <view class="score-item">
          <text class="score-label">1分钟跳绳</text>
          <input class="score-input" 
                 type="number" 
                 placeholder="个数"
                 value="{{formData.score1min}}"
                 bindinput="onScore1minInput" />
          <text class="score-unit">个</text>
        </view>
        <view class="score-item">
          <text class="score-label">连续跳绳</text>
          <input class="score-input" 
                 type="number" 
                 placeholder="个数"
                 value="{{formData.scoreContinuous}}"
                 bindinput="onScoreContinuousInput" />
          <text class="score-unit">个</text>
        </view>
      </view>
    </view>

    <!-- 媒体上传（可选） -->
    <view class="form-group optional">
      <view class="form-label">
        <text class="label-text">上传视频/图片</text>
        <text class="optional-mark">选填</text>
      </view>
      <view class="media-upload">
        <view class="upload-options">
          <button class="upload-btn video-btn" bindtap="uploadVideo">
            <text class="upload-icon">📹</text>
            <text class="upload-text">录制视频</text>
          </button>
          <button class="upload-btn photo-btn" bindtap="uploadPhoto">
            <text class="upload-icon">📷</text>
            <text class="upload-text">拍照上传</text>
          </button>
        </view>
        <view class="media-preview" wx:if="{{formData.mediaList.length > 0}}">
          <view class="media-item" wx:for="{{formData.mediaList}}" wx:key="index">
            <image class="media-thumb" src="{{item.thumb}}" mode="aspectFill" />
            <view class="media-type">{{item.type === 'video' ? '📹' : '📷'}}</view>
            <view class="media-remove" bindtap="removeMedia" data-index="{{index}}">×</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 心得分享（可选） -->
    <view class="form-group optional">
      <view class="form-label">
        <text class="label-text">今日感受</text>
        <text class="optional-mark">选填</text>
      </view>
      <textarea class="experience-input"
                placeholder="分享一下今天的练习感受、收获或遇到的挑战..."
                value="{{formData.experience}}"
                bindinput="onExperienceInput"
                maxlength="200"
                show-confirm-bar="{{false}}"
                auto-height />
      <view class="char-count">{{formData.experience.length}}/200</view>
    </view>
  </view>

  <!-- 激励展示区 -->
  <view class="reward-preview-section">
    <view class="section-title">
      <text class="title-icon">🎁</text>
      <text class="title-text">本次打卡奖励</text>
    </view>
    <view class="reward-cards">
      <view class="reward-card points">
        <view class="reward-icon">⭐</view>
        <view class="reward-info">
          <text class="reward-name">积分奖励</text>
          <text class="reward-value">+{{rewardPreview.points}}分</text>
        </view>
      </view>
      <view class="reward-card badge" wx:if="{{rewardPreview.badge}}">
        <view class="reward-icon">🏅</view>
        <view class="reward-info">
          <text class="reward-name">勋章奖励</text>
          <text class="reward-value">{{rewardPreview.badge.name}}</text>
        </view>
      </view>
      <view class="reward-card streak">
        <view class="reward-icon">🔥</view>
        <view class="reward-info">
          <text class="reward-name">连续天数</text>
          <text class="reward-value">{{rewardPreview.streakDays}}天</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交确认区 -->
  <view class="submit-section">
    <view class="submit-tips" wx:if="{{!canSubmit}}">
      <text class="tips-icon">💡</text>
      <text class="tips-text">请至少填写练习时长</text>
    </view>
    <button class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
            bindtap="submitCheckin" 
            disabled="{{!canSubmit || isSubmitting}}">
      <text class="submit-icon">{{isSubmitting ? '⏳' : '✅'}}</text>
      <text class="submit-text">{{isSubmitting ? '提交中...' : '完成打卡'}}</text>
    </button>
    
    <!-- 帮助入口 -->
    <view class="help-section">
      <text class="help-text">遇到问题？</text>
      <text class="help-link" bindtap="showHelp">查看帮助</text>
    </view>
  </view>

  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 帮助弹窗 -->
<view class="help-modal {{showHelpModal ? 'show' : ''}}" bindtap="hideHelp">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">打卡帮助</text>
      <view class="modal-close" bindtap="hideHelp">×</view>
    </view>
    <view class="modal-body">
      <view class="help-content">
        <view class="help-item">
          <text class="help-question">Q: 练习时长如何计算？</text>
          <text class="help-answer">A: 包括热身、正式练习和放松的总时间。</text>
        </view>
        <view class="help-item">
          <text class="help-question">Q: 跳绳成绩不理想怎么办？</text>
          <text class="help-answer">A: 重在坚持，每天进步一点点就很棒！</text>
        </view>
        <view class="help-item">
          <text class="help-question">Q: 可以补打卡吗？</text>
          <text class="help-answer">A: 当天24点前都可以打卡，过期无法补打。</text>
        </view>
      </view>
    </view>
  </view>
</view>
