// 加载管理器测试页面
const { startLoading, endLoading, isLoading } = require("../../utils/index.js");

Page({
  data: {
    testResults: [],
  },

  onLoad() {
    console.log("加载管理器测试页面启动");
  },

  // 测试1：正常加载（2秒完成）
  async testNormalLoading() {
    const loadingKey = "test-normal";

    try {
      startLoading(loadingKey, {
        title: "测试正常加载...",
        timeout: 10000,
      });

      console.log("🔄 开始正常加载测试");

      // 模拟2秒网络请求
      await this.simulateRequest(2000);

      endLoading(loadingKey, true);
      console.log("✅ 正常加载测试成功");

      this.addResult("正常加载测试", "success", "2秒完成");
    } catch (error) {
      endLoading(loadingKey, false);
      console.error("❌ 正常加载测试失败:", error);
      this.addResult("正常加载测试", "failed", error.message);
    }
  },

  // 测试2：超时测试（5秒超时）
  async testTimeoutLoading() {
    const loadingKey = "test-timeout";

    try {
      startLoading(loadingKey, {
        title: "测试超时处理...",
        timeout: 5000, // 5秒超时
        onTimeout: () => {
          console.log("🕐 超时回调触发");
          this.addResult("超时测试", "timeout", "5秒后触发超时处理");
        },
        onRetry: () => {
          console.log("🔄 重试回调触发");
          this.addResult("重试测试", "retry", "用户选择重试");
        },
      });

      console.log("🔄 开始超时测试（10秒请求 vs 5秒超时）");

      // 模拟10秒网络请求，但5秒就会超时
      await this.simulateRequest(10000);

      // 如果到这里说明没有超时（不应该发生）
      endLoading(loadingKey, true);
      console.log("❌ 超时测试异常：应该超时但没有超时");
      this.addResult("超时测试", "failed", "应该超时但没有超时");
    } catch (error) {
      endLoading(loadingKey, false);
      console.error("❌ 超时测试失败:", error);
      this.addResult("超时测试", "failed", error.message);
    }
  },

  // 测试3：网络错误测试
  async testNetworkError() {
    const loadingKey = "test-network";

    try {
      startLoading(loadingKey, {
        title: "测试网络错误...",
        timeout: 8000,
        onTimeout: () => {
          console.log("🕐 网络错误超时");
          this.addResult("网络错误测试", "timeout", "网络请求超时");
        },
      });

      console.log("🔄 开始网络错误测试");

      // 尝试真实的网络请求
      const { http } = require("../../utils/index.js");
      const result = await http.get("/api/test-loading-manager");

      endLoading(loadingKey, true);
      console.log("✅ 网络请求成功:", result);
      this.addResult("网络错误测试", "success", "网络请求成功");
    } catch (error) {
      endLoading(loadingKey, false);
      console.error("❌ 网络请求失败:", error);
      this.addResult("网络错误测试", "network-error", error.message);
    }
  },

  // 测试4：防重复请求测试
  async testDuplicateRequest() {
    const loadingKey = "test-duplicate";

    console.log("🔄 开始防重复请求测试");

    // 第一个请求
    if (!isLoading(loadingKey)) {
      startLoading(loadingKey, {
        title: "防重复测试...",
        timeout: 8000,
      });

      setTimeout(async () => {
        try {
          await this.simulateRequest(3000);
          endLoading(loadingKey, true);
          this.addResult("防重复测试", "success", "第一个请求完成");
        } catch (error) {
          endLoading(loadingKey, false);
          this.addResult("防重复测试", "failed", error.message);
        }
      }, 100);
    }

    // 立即发起第二个请求（应该被阻止）
    if (isLoading(loadingKey)) {
      console.log("🔇 检测到重复请求，已阻止");
      this.addResult("防重复测试", "duplicate-blocked", "成功阻止重复请求");
    } else {
      this.addResult("防重复测试", "failed", "没有阻止重复请求");
    }
  },

  // 模拟网络请求
  simulateRequest(delay) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({ success: true, delay });
      }, delay);
    });
  },

  // 添加测试结果
  addResult(test, status, message) {
    const results = this.data.testResults;
    results.push({
      id: Date.now(),
      test,
      status,
      message,
      time: new Date().toLocaleTimeString(),
    });

    this.setData({
      testResults: results,
    });
  },

  // 清空测试结果
  clearResults() {
    this.setData({
      testResults: [],
    });
  },

  // 运行所有测试
  async runAllTests() {
    this.clearResults();

    console.log("🧪 开始运行所有测试...");

    await this.testNormalLoading();
    await new Promise((resolve) => setTimeout(resolve, 1000)); // 等待1秒

    await this.testDuplicateRequest();
    await new Promise((resolve) => setTimeout(resolve, 4000)); // 等待4秒

    await this.testNetworkError();
    await new Promise((resolve) => setTimeout(resolve, 2000)); // 等待2秒

    await this.testTimeoutLoading();

    console.log("🎉 所有测试完成");
  },
});
