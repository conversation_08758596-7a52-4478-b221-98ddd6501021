<!-- 加载管理器测试页面 -->
<view class="test-container">
  <view class="test-header">
    <text class="test-title">🧪 智能加载管理器测试</text>
    <text class="test-subtitle">验证加载、超时、重试、防重复功能</text>
  </view>
  <view class="test-buttons">
    <button class="test-btn primary" bindtap="testNormalLoading">测试正常加载</button>
    <button class="test-btn warning" bindtap="testTimeoutLoading">测试超时处理</button>
    <button class="test-btn danger" bindtap="testNetworkError">测试网络错误</button>
    <button class="test-btn info" bindtap="testDuplicateRequest">测试防重复</button>
    <button class="test-btn success" bindtap="runAllTests">🚀 运行所有测试</button>
    <button class="test-btn secondary" bindtap="clearResults">清空结果</button>
  </view>
  <view class="test-results" wx:if="{{testResults.length > 0}}">
    <view class="results-header">
      <text class="results-title">📊 测试结果</text>
    </view>
    <view class="result-item" wx:for="{{testResults}}" wx:key="id">
      <view class="result-header">
        <text class="result-test">{{item.test}}</text>
        <text class="result-time">{{item.time}}</text>
      </view>
      <view class="result-status status-{{item.status}}">
        <text wx:if="{{item.status === 'success'}}">成功</text>
        <text wx:elif="{{item.status === 'failed'}}">失败</text>
        <text wx:elif="{{item.status === 'timeout'}}">超时</text>
        <text wx:elif="{{item.status === 'network-error'}}">网络错误</text>
        <text wx:elif="{{item.status === 'duplicate-blocked'}}">阻止重复</text>
        <text wx:elif="{{item.status === 'retry'}}">重试</text>
        <text wx:else>{{item.status}}</text>
      </view>
      <view class="result-message">{{item.message}}</view>
    </view>
  </view>
  <view class="test-instructions">
    <view class="instruction-title">📋 测试说明</view>
    <view class="instruction-item">
      <text class="instruction-label">正常加载：</text>
      <text class="instruction-text">2秒完成，测试基本功能</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">超时处理：</text>
      <text class="instruction-text">5秒超时，测试超时弹窗</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">网络错误：</text>
      <text class="instruction-text">真实API请求，测试错误处理</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">防重复：</text>
      <text class="instruction-text">同时发起请求，测试防重复机制</text>
    </view>
  </view>
</view>