/* 加载管理器测试页面样式 */
.test-container {
  padding: 32rpx;
  background-color: #F7F8FA;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.test-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.test-subtitle {
  font-size: 26rpx;
  color: #666666;
  display: block;
}

.test-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.test-btn {
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  color: #FFFFFF;
}

.test-btn.primary {
  background-color: #FF7A45;
}

.test-btn.warning {
  background-color: #FFA726;
}

.test-btn.danger {
  background-color: #EF5350;
}

.test-btn.info {
  background-color: #4A90E2;
}

.test-btn.success {
  background-color: #66BB6A;
  grid-column: span 2;
}

.test-btn.secondary {
  background-color: #9E9E9E;
  grid-column: span 2;
}

.test-results {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.results-header {
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.result-item {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border-left: 6rpx solid #E0E0E0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-test {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.result-time {
  font-size: 24rpx;
  color: #999999;
}

.result-status {
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  margin-bottom: 12rpx;
}

.status-success {
  background-color: #E8F5E8;
  color: #4CAF50;
  border-left-color: #4CAF50;
}

.status-failed {
  background-color: #FFEBEE;
  color: #F44336;
  border-left-color: #F44336;
}

.status-timeout {
  background-color: #FFF3E0;
  color: #FF9800;
  border-left-color: #FF9800;
}

.status-network-error {
  background-color: #FFEBEE;
  color: #F44336;
  border-left-color: #F44336;
}

.status-duplicate-blocked {
  background-color: #E3F2FD;
  color: #2196F3;
  border-left-color: #2196F3;
}

.status-retry {
  background-color: #F3E5F5;
  color: #9C27B0;
  border-left-color: #9C27B0;
}

.result-message {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.test-instructions {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.instruction-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.instruction-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.instruction-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #FF7A45;
  width: 160rpx;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
  line-height: 1.4;
}
