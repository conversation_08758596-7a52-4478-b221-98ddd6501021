/* 打卡成功页面样式 */

.page-container {
  background: linear-gradient(135deg, #FF7A45 0%, #E6692D 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 成功反馈区域 */
.success-feedback {
  padding: var(--spacing-xl);
  padding-top: calc(var(--spacing-xl) + env(safe-area-inset-top));
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.6s ease;
}

.success-feedback.show {
  opacity: 1;
  transform: translateY(0);
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* pointer-events: none; */
}

.star {
  position: absolute;
  font-size: var(--font-xl);
  animation: float 3s ease-in-out infinite;
}

.star-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.star-2 {
  top: 30%;
  right: 15%;
  animation-delay: 0.5s;
}

.star-3 {
  top: 50%;
  left: 20%;
  animation-delay: 1s;
}

.star-4 {
  top: 60%;
  right: 25%;
  animation-delay: 1.5s;
}

.star-5 {
  top: 80%;
  left: 30%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 1;
  }
}

/* 主要成就展示 */
.achievement-display {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  z-index: 1;
}

.success-title {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: #FFFFFF;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  margin-bottom: var(--spacing-xl);
  line-height: 1.3;
}

.rewards-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.reward-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background-color: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  /* backdrop-filter: blur(10rpx); */
}

.reward-icon {
  font-size: var(--font-xl);
}

.reward-text {
  font-size: var(--font-lg);
  font-weight: 600;
  color: #FFFFFF;
}

/* 成就卡片预览 */
.achievement-card-preview {
  width: 100%;
  margin-bottom: var(--spacing-xl);
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.5s ease;
}

.achievement-card-preview.show {
  opacity: 1;
  transform: translateY(0);
}

.card-container {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.achievement-card {
  padding: var(--spacing-lg);
}

.card-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.card-content {
  
}

.child-info {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: #F7F8FA;
  border-radius: var(--radius-md);
}

.child-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: var(--spacing-md);
}

.child-avatar image {
  width: 100%;
  height: 100%;
}

.child-details {
  flex: 1;
}

.child-name {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.child-age {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.achievements {
  margin-bottom: var(--spacing-lg);
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
}

.achievement-icon {
  font-size: var(--font-md);
  width: 48rpx;
  text-align: center;
}

.achievement-text {
  font-size: var(--font-md);
  color: var(--text-primary);
  line-height: 1.5;
}

.progress-display {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: #F7F8FA;
  border-radius: var(--radius-md);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #E5E5E5;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, #E6692D 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  display: block;
}

.card-footer {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 2rpx solid #F0F0F0;
}

.footer-text {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.qr-code {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto;
}

.qr-code image {
  width: 100%;
  height: 100%;
}

/* 分享引流区域 */
.share-actions {
  width: 100%;
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.5s ease 0.3s;
}

.share-actions.show {
  opacity: 1;
  transform: translateY(0);
}

.share-btn {
  flex: 1;
  font-weight: 600;
}

/* 多出口操作区 */
.action-buttons {
  width: 100%;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 0.5s ease 0.5s;
}

.action-buttons.show {
  opacity: 1;
  transform: translateY(0);
}

.secondary-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.secondary-actions .btn {
  flex: 1;
  min-width: 200rpx;
  font-size: 24rpx;
  padding: 16rpx 20rpx;
}

/* 智能文案预览弹窗 */
.share-text-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.share-text-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.share-text-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xl);
  color: var(--text-secondary);
  background-color: #F5F5F5;
  border-radius: 50%;
}

.modal-body {
  padding: var(--spacing-lg);
}

.share-text-content {
  background-color: #F7F8FA;
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.share-text {
  font-size: var(--font-md);
  color: var(--text-primary);
  line-height: 1.6;
  white-space: pre-line;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-md);
}

.modal-actions .btn {
  flex: 1;
}

/* 动画类 */
.bounce {
  animation: bounce 0.8s ease-in-out;
}

.fade-in {
  animation: fadeIn 0.6s ease-in-out 0.3s both;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 默认头像样式 */
.default-child-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  border-radius: 50%;
  font-size: 60rpx;
  color: var(--text-secondary);
}
