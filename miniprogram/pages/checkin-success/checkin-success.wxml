<!-- 打卡成功页面 -->
<view class="page-container">
  <!-- 成功反馈区域 -->
  <view class="success-feedback {{showContent ? 'show' : ''}}">
    <!-- 背景动画 -->
    <view class="background-animation">
      <view class="star star-1">⭐</view>
      <view class="star star-2">✨</view>
      <view class="star star-3">🌟</view>
      <view class="star star-4">⭐</view>
      <view class="star star-5">✨</view>
    </view>
    <!-- 主要成就展示 -->
    <view class="achievement-display">
      <view class="success-title bounce">🎉 恭喜完成第{{checkinData.count}}次打卡！</view>
      <view class="rewards-container fade-in">
        <view class="reward-item">
          <text class="reward-icon">⭐</text>
          <text class="reward-text">获得 {{checkinData.points}} 积分</text>
        </view>
        <view class="reward-item" wx:if="{{checkinData.newBadge}}">
          <text class="reward-icon">🏅</text>
          <text class="reward-text">{{checkinData.newBadge.name}}勋章</text>
        </view>
      </view>
    </view>
    <!-- 成就卡片预览 -->
    <view class="achievement-card-preview {{showShareOptions ? 'show' : ''}}" wx:if="{{showShareOptions}}">
      <view class="card-container">
        <view class="achievement-card">
          <view class="card-header">
            <text class="card-title">🎯 {{currentChild.name}}的跳绳成就</text>
          </view>
          <view class="card-content">
            <view class="child-info">
              <view class="child-avatar">
                <view class="default-child-avatar" wx:if="{{!currentChild.avatar}}">👶</view>
                <image wx:else src="{{currentChild.avatar}}" mode="aspectFill"></image>
              </view>
              <view class="child-details">
                <text class="child-name">{{currentChild.name}}</text>
                <text class="child-age">{{currentChild.age}}岁</text>
              </view>
            </view>
            <view class="achievements">
              <view class="achievement-item">
                <text class="achievement-icon">🏃</text>
                <text class="achievement-text">已完成第{{checkinData.count}}次打卡</text>
              </view>
              <view class="achievement-item">
                <text class="achievement-icon">💪</text>
                <text class="achievement-text">{{checkinData.taskDescription}} ✅</text>
              </view>
              <view class="achievement-item" wx:if="{{checkinData.newBadge}}">
                <text class="achievement-icon">🌟</text>
                <text class="achievement-text">获得"{{checkinData.newBadge.name}}"勋章</text>
              </view>
            </view>
            <view class="progress-display">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{progressPercentage}}%"></view>
              </view>
              <text class="progress-text">{{checkinData.count}}/{{checkinData.total}}次</text>
            </view>
            <view class="card-footer">
              <text class="footer-text">一起来跳绳，让孩子更健康！</text>
              <view class="qr-code">
                <view class="qr-code-emoji">📱</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 分享引流区域 -->
    <view class="share-actions {{showShareOptions ? 'show' : ''}}" wx:if="{{showShareOptions}}">
      <button class="btn btn-primary share-btn" bindtap="generateShareCard">🎨 生成朋友圈图片</button>
      <button class="btn btn-secondary share-btn" bindtap="shareToVideoChannel">📱 分享到视频号</button>
    </view>
    <!-- 多出口操作区 -->
    <view class="action-buttons {{showShareOptions ? 'show' : ''}}" wx:if="{{showShareOptions}}">
      <!-- 主要操作 -->
      <button class="btn btn-primary btn-large btn-block" bindtap="generateShareCard">
        🎨 生成分享海报
      </button>
      <!-- 次要操作 -->
      <view class="secondary-actions">
        <button class="btn btn-outline" bindtap="viewAllAchievements">🏆 查看所有成就</button>
        <button class="btn btn-outline" bindtap="backToGrowth">📚 返回训练营</button>
        <button class="btn btn-outline" bindtap="continueCheckin">⚡ 继续打卡</button>
      </view>
    </view>
  </view>
  <!-- 智能文案预览弹窗 -->
  <view class="share-text-modal {{showShareTextModal ? 'show' : ''}}" bindtap="hideShareTextModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">推荐分享文案</text>
        <view class="modal-close" bindtap="hideShareTextModal">×</view>
      </view>
      <view class="modal-body">
        <view class="share-text-content">
          <text class="share-text">{{shareText}}</text>
        </view>
        <view class="modal-actions">
          <button class="btn btn-outline" bindtap="copyShareText">复制文案</button>
          <button class="btn btn-primary" bindtap="confirmShare">确认分享</button>
        </view>
      </view>
    </view>
  </view>
</view>