// 打卡成功页面
const app = getApp();

Page({
  data: {
    currentChild: {},
    checkinData: {},
    progressPercentage: 0,
    showContent: false,
    showShareOptions: false,
    showShareTextModal: false,
    shareText: "",
  },

  onLoad(options) {
    console.log("打卡成功页面加载", options);
    this.initPage(options);
  },

  onReady() {
    // 页面渲染完成后开始动画
    this.startSuccessAnimation();
  },

  // 初始化页面
  initPage(options) {
    const currentChild = app.getCurrentChild();

    // 检查是否有选中的孩子
    if (!currentChild) {
      console.warn("没有选中的孩子，跳转到孩子管理页面");
      wx.showModal({
        title: "提示",
        content: "请先添加孩子信息",
        showCancel: false,
        success: () => {
          wx.redirectTo({
            url: "/pages/child-manage/child-manage",
          });
        },
      });
      return;
    }

    // 模拟打卡数据，实际应该从API获取
    const checkinData = {
      id: options.checkin_id || 1,
      count: parseInt(options.count) || 8,
      total: parseInt(options.total) || 21,
      points: parseInt(options.points) || 20,
      taskDescription: options.taskDescription || "连续跳绳50个",
      newBadge: options.newBadge
        ? JSON.parse(options.newBadge)
        : {
            id: 1,
            name: "坚持之星",
            icon: "🌟",
          },
    };

    const progressPercentage = Math.round(
      (checkinData.count / checkinData.total) * 100
    );

    this.setData({
      currentChild: currentChild,
      checkinData: checkinData,
      progressPercentage: progressPercentage,
    });

    // 生成智能分享文案
    this.generateShareText();
  },

  // 开始成功动画
  startSuccessAnimation() {
    // 立即显示内容
    this.setData({
      showContent: true,
    });

    // 3秒后显示分享选项
    setTimeout(() => {
      this.setData({
        showShareOptions: true,
      });
    }, 3000);
  },

  // 生成智能分享文案
  generateShareText() {
    const { currentChild, checkinData } = this.data;

    // 检查必要数据是否存在
    if (!currentChild || !checkinData) {
      console.warn("缺少必要数据，无法生成分享文案");
      return;
    }

    let shareText = "";
    const childName = currentChild.name || "小朋友";

    if (checkinData.count === 1) {
      // 第1次打卡模板
      shareText = `🎉 ${childName}今天开始了跳绳挑战！
第1次打卡就完成了${checkinData.taskDescription}，太棒了！
坚持运动，健康成长！💪
#儿童跳绳挑战 #亲子运动`;
    } else if (checkinData.newBadge) {
      // 获得勋章模板
      shareText = `🌟 ${childName}获得"${checkinData.newBadge.name}"勋章啦！
连续${checkinData.count}次打卡，进步真的很大！
看着孩子一天天成长，当妈妈的太骄傲了！
#跳绳小达人 #坚持的力量`;
    } else if (checkinData.count >= checkinData.total) {
      // 完成训练营模板
      shareText = `🏆 21天跳绳挑战营圆满完成！
${childName}从零基础到现在，进步太明显了！
运动真的能让孩子更自信更健康！
#训练营毕业 #成长见证`;
    } else {
      // 普通打卡模板
      shareText = `💪 ${childName}今天完成第${checkinData.count}次打卡！
${checkinData.taskDescription}，继续加油！
每一次坚持都是成长的足迹 ✨
#跳绳打卡 #健康成长`;
    }

    this.setData({
      shareText: shareText,
    });
  },

  // 生成分享卡片
  generateShareCard() {
    const posterData = {
      type: "checkin",
      points: this.data.earnedPoints,
      streakDays: this.data.streakDays,
      campTitle: this.data.campTitle || "跳绳训练营",
    };

    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=checkin&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  // 分享到视频号
  shareToVideoChannel() {
    wx.showToast({
      title: "即将跳转到视频号",
      icon: "none",
    });

    // TODO: 实际实现跳转到微信视频号发布页面
  },

  // 隐藏分享文案弹窗
  hideShareTextModal() {
    this.setData({
      showShareTextModal: false,
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击模态框内容时关闭弹窗
  },

  // 复制分享文案
  copyShareText() {
    wx.setClipboardData({
      data: this.data.shareText,
      success: () => {
        app.showSuccess("文案已复制");
      },
    });
  },

  // 确认分享
  confirmShare() {
    this.hideShareTextModal();

    // 保存图片到相册
    wx.showActionSheet({
      itemList: ["保存到相册", "分享到朋友圈", "分享到微信群"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.saveToAlbum();
            break;
          case 1:
            this.shareToMoments();
            break;
          case 2:
            this.shareToWechatGroup();
            break;
        }
      },
    });
  },

  // 保存到相册
  saveToAlbum() {
    app.showSuccess("已保存到相册");
    // TODO: 实际实现保存图片到相册
  },

  // 分享到朋友圈
  shareToMoments() {
    app.showSuccess("即将跳转到朋友圈");
    // TODO: 实际实现分享到朋友圈
  },

  // 分享到微信群
  shareToWechatGroup() {
    wx.showShareMenu({
      withShareTicket: true,
    });
  },

  // 查看所有成就
  viewAllAchievements() {
    wx.navigateTo({
      url: "/pages/growth/main/main?tab=1",
    });
  },

  // 返回训练营
  backToGrowth() {
    wx.navigateTo({
      url: "/pages/growth/main/main?tab=0",
    });
  },

  // 继续打卡
  continueCheckin() {
    wx.navigateTo({
      url: "/pages/demo/checkin-form-v4/checkin-form-v4",
    });
  },

  // 继续成长
  continueGrowth() {
    wx.switchTab({
      url: "/pages/growth/growth",
    });
  },

  // 分享给好友
  onShareAppMessage() {
    const { currentChild, checkinData } = this.data;
    return {
      title: `${currentChild.name}完成第${checkinData.count}次跳绳打卡！一起来运动吧`,
      path: "/pages/home/<USER>",
      image_url: "/images/share-checkin-success.png",
    };
  },

  onShareTimeline() {
    const { currentChild, checkinData } = this.data;
    return {
      title: `${currentChild.name}完成第${checkinData.count}次跳绳打卡！`,
      image_url: "/images/share-checkin-success.png",
    };
  },
});
