# 🎯 儿童多品类学习平台 - 微信小程序

## 📋 项目概述

专为K-6年级儿童设计的多品类学习平台微信小程序，采用Freemium模式，提供免费入门视频和付费进阶内容/训练营。目标用户为27-40岁的家长，通过游戏化的方式让孩子爱上学习，养成良好的学习习惯。

## �️ 技术架构

### 技术栈
- **前端**: 微信小程序原生开发
- **后端**: Go 1.23.10 + Gin框架
- **数据库**: MySQL（业务数据）+ MongoDB（日志分析）+ Redis（缓存会话）
- **认证**: JWT + 微信登录
- **架构**: 严格3层架构，API/Admin双服务分离

### 开发规范
- **MVS核心规则**: 确认需求 → 统一模板 → 基础测试 → 提交检查
- **3层模型**: base/（数据库模型）+ dto/（响应）+ requests/（请求）
- **4步开发**: 数据表 → 模型仓库 → 业务数据 → 处理服务路由

## �🎨 设计系统

### 视觉设计
- **主色调**: 活力橙 #FF7A45
- **辅助色**: 米白色 #F7F8FA、青蓝色 #4A90E2
- **字体规范**: 30rpx/32rpx/36rpx（标题可加粗）
- **设计风格**: 简洁明快、儿童友好、专业可信

### 核心功能
- 🏠 **首页**: 训练营计划列表（入门/进阶/冲刺/高阶）
- 📊 **成长**: 打卡任务列表和排行榜
- 👤 **我的**: 孩子信息管理中心
- 🎯 **打卡系统**: 微信视频号发布 + 提交ID
- 🏅 **虚拟奖励**: 积分、排行榜、自动内容审核
- 📹 **智能视频**: 1个隐藏区域，2-4个横向显示，5+个显示4个+查看更多

## 📁 项目结构

```
miniprogram/                    # 微信小程序源码
├── app.js                     # 小程序主入口
├── app.json                   # 小程序配置
├── app.wxss                   # 全局样式
├── sitemap.json               # 站点地图
├── project.config.json        # 项目配置
├── project.private.config.json # 私有配置
│
├── pages/                     # 页面目录（15个页面）
│   ├── login/                 # 微信登录页
│   ├── home/                  # 首页 - 训练营选择
│   ├── growth/                # 成长页 - 打卡任务中心
│   ├── profile/               # 我的页面 - 孩子管理
│   ├── checkin-success/       # 打卡成功页
│   ├── camp-detail/           # 训练营详情
│   ├── checkin/               # 打卡页面
│   ├── video-play/            # 视频播放
│   ├── video-list/            # 视频列表
│   ├── leaderboard/           # 排行榜
│   ├── badges/                # 成就徽章
│   ├── share-card/            # 分享卡片生成
│   ├── child-manage/          # 孩子管理
│   ├── child-create/          # 添加孩子
│   ├── settings/              # 设置页面
│   └── image-test/            # 图片测试页面（开发工具）
│
├── components/                # 自定义组件
│   ├── custom-navbar/         # 自定义导航栏
│   │   ├── custom-navbar.js   # 组件逻辑
│   │   ├── custom-navbar.json # 组件配置
│   │   ├── custom-navbar.wxml # 组件结构
│   │   ├── custom-navbar.wxss # 组件样式
│   │   └── README.md          # 组件文档
│   ├── empty-state/           # 空状态组件
│   │   ├── empty-state.js
│   │   ├── empty-state.json
│   │   ├── empty-state.wxml
│   │   └── empty-state.wxss
│   ├── smart-image/           # 智能图片组件
│   │   ├── smart-image.js
│   │   ├── smart-image.json
│   │   ├── smart-image.wxml
│   │   └── smart-image.wxss
│   └── video-thumbnail/       # 视频缩略图组件
│       ├── video-thumbnail.js
│       ├── video-thumbnail.json
│       ├── video-thumbnail.wxml
│       └── video-thumbnail.wxss
│
├── custom-tab-bar/            # 自定义底部导航栏
│   └── (待实现)
│
├── images/                    # 图片资源
│   ├── tab-*.png             # 底部导航图标
│   ├── icon-*.png            # 功能图标
│   ├── app-logo*.png         # 应用Logo
│   ├── mini-qr-code.png      # 小程序码
│   ├── README.md             # 图片资源说明
│   └── create-icons.md       # 图标创建指南
│
├── styles/                   # 样式文件系统
│   ├── base.wxss             # 基础样式
│   ├── variables.wxss        # CSS变量定义
│   ├── layout.wxss           # 布局样式
│   ├── spacing.wxss          # 间距规范
│   ├── buttons.wxss          # 按钮样式
│   ├── forms.wxss            # 表单样式
│   ├── css-icons.wxss        # CSS图标
│   └── emoji-icons.wxss      # Emoji图标
│
├── utils/                    # 工具函数库（已重构）
│   ├── index.js             # 统一导出入口
│   ├── common.js            # 通用工具函数
│   ├── ui.js                # UI交互工具
│   ├── navigation.js        # 页面导航
│   ├── request.js           # HTTP请求客户端
│   ├── wechat.js            # 微信API封装
│   ├── format.js            # 数据格式化
│   ├── validator.js         # 数据验证
│   ├── storage.js           # 本地存储管理
│   ├── error.js             # 错误处理
│   ├── constants.js         # 常量定义
│   ├── state-manager.js     # 状态管理核心
│   ├── state-actions.js     # 状态操作方法
│   ├── state-mixin.js       # 页面状态绑定
│   ├── login-guard.js       # 登录守卫
│   └── image-manager.js     # 图片管理
│
├── docs/                     # 项目文档
│   ├── design-system.md     # 设计系统规范
│   ├── development/          # 开发文档
│   │   ├── state-management-usage.md
│   │   └── utils-usage-example.js
│   ├── guides/               # 使用指南
│   │   ├── login-strategy-implementation.md
│   │   ├── image-integration-guide.md
│   │   └── image-optimization-plan.md
│   └── reports/              # 完成报告
│       ├── phase1-optimization-complete.md
│       ├── state-management-complete-report.md
│       ├── utils-refactor-complete-report.md
│       └── image-integration-complete-report.md
│
├── final-check.md            # 项目检查清单
├── project-review-summary.md # 项目回顾总结
└── project-cleanup-complete.md # 项目清理报告
```

## 🧩 组件系统

### 自定义组件
项目采用组件化开发，提供可复用的UI组件：

#### **custom-navbar** - 自定义导航栏
- ✅ 自动适配iOS/Android设备导航栏高度
- ✅ 支持孩子切换选择器
- ✅ 支持返回按钮和自定义标题
- ✅ 支持右侧插槽扩展
- ✅ 处理新用户无孩子数据的情况

#### **empty-state** - 空状态组件
- ✅ 统一的空状态展示
- ✅ 支持自定义图标和文案
- ✅ 支持操作按钮
- ✅ 多种预设状态（无数据、网络错误、加载失败等）

#### **smart-image** - 智能图片组件
- ✅ 自动处理图片加载状态
- ✅ 支持占位图和错误图
- ✅ 懒加载和性能优化
- ✅ 支持多种显示模式

#### **video-thumbnail** - 视频缩略图组件
- ✅ 视频封面图显示
- ✅ 播放按钮覆盖
- ✅ 支持时长显示
- ✅ 点击播放交互

### 组件使用方式
```javascript
// 在页面JSON中引入组件
{
  "usingComponents": {
    "custom-navbar": "/components/custom-navbar/custom-navbar",
    "empty-state": "/components/empty-state/empty-state",
    "smart-image": "/components/smart-image/smart-image",
    "video-thumbnail": "/components/video-thumbnail/video-thumbnail"
  }
}

// 在WXML中使用
<custom-navbar title="页面标题" show-back="{{true}}" />
<empty-state type="no-data" text="暂无数据" />
<smart-image src="{{imageUrl}}" mode="aspectFill" />
<video-thumbnail src="{{videoUrl}}" duration="{{duration}}" />
```

## 🛠️ Utils工具库

### 重构后的模块架构
经过完整重构，utils包现在提供清晰的模块分工：

#### 核心模块
- **common.js** - 纯函数工具集（防抖、节流、深拷贝、UUID等）
- **ui.js** - UI交互工具（加载提示、Toast、Modal、震动反馈等）
- **navigation.js** - 页面导航（统一跳转API、智能导航、安全返回等）
- **request.js** - HTTP请求客户端（拦截器、重试机制、文件上传等）
- **wechat.js** - 微信API封装（登录、权限、媒体、位置、分享等）

#### 数据处理
- **format.js** - 数据格式化（日期、数字、文本、手机号、金额等）
- **validator.js** - 数据验证（表单验证、数据校验等）
- **storage.js** - 本地存储管理（用户存储、缓存管理等）

#### 状态管理
- **state-manager.js** - 状态管理核心
- **state-actions.js** - 状态操作方法
- **state-mixin.js** - 页面状态绑定
- **login-guard.js** - 登录守卫

### 使用方式
```javascript
// 推荐：按需导入
const {
  common,      // 通用工具函数
  ui,          // UI交互
  navigation,  // 页面导航
  http,        // HTTP请求
  wechat,      // 微信API
  format,      // 数据格式化
  validator    // 数据验证
} = require('../../utils/index.js');

// 兼容：旧的别名仍然可用
const { pageUtils, utils, validate } = require('../../utils/index.js');
```

## � 样式系统

### 模块化样式架构
项目采用模块化的样式管理，便于维护和复用：

#### **基础样式**
- **variables.wxss** - CSS变量定义（颜色、字体、间距等）
- **base.wxss** - 基础样式重置和通用样式
- **layout.wxss** - 布局相关样式（Flex、Grid等）
- **spacing.wxss** - 间距规范（margin、padding标准）

#### **组件样式**
- **buttons.wxss** - 按钮样式库（主要、次要、危险等）
- **forms.wxss** - 表单样式（输入框、选择器等）
- **css-icons.wxss** - CSS图标库
- **emoji-icons.wxss** - Emoji图标库

### 样式使用规范
```css
/* 在app.wxss中导入基础样式 */
@import "styles/variables.wxss";
@import "styles/base.wxss";
@import "styles/layout.wxss";
@import "styles/spacing.wxss";
@import "styles/buttons.wxss";
@import "styles/forms.wxss";
@import "styles/css-icons.wxss";
@import "styles/emoji-icons.wxss";

/* 使用CSS变量 */
.custom-button {
  background-color: var(--primary-color);
  color: var(--text-white);
  border-radius: var(--border-radius-base);
}

/* 使用间距类 */
.content {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}
```

### 设计规范
- **主色调**: #FF7A45（活力橙）
- **辅助色**: #F7F8FA（米白色）、#4A90E2（青蓝色）
- **字体大小**: 30rpx/32rpx/36rpx（标题可加粗）
- **间距规范**: 8rpx的倍数（16rpx、24rpx、32rpx、40rpx）
- **圆角规范**: 8rpx、12rpx、16rpx

## �🎯 开发指南

### 快速开始
1. **克隆项目**: `git clone [项目地址]`
2. **导入微信开发者工具**: 选择miniprogram目录
3. **配置AppID**: 在project.config.json中设置
4. **启动开发**: 点击编译运行

### 开发流程
1. **需求确认** - 明确功能需求和设计规范
2. **页面开发** - 按照MVS规则创建页面
3. **组件复用** - 优先使用现有组件，必要时创建新组件
4. **功能实现** - 使用utils工具库实现功能
5. **样式规范** - 遵循设计系统和样式规范
6. **测试验证** - 基础功能测试和用户体验测试
7. **代码提交** - 遵循提交规范

### 最佳实践
- **组件化开发**: 优先使用和创建可复用组件
- **状态管理**: 使用统一的状态管理系统
- **样式规范**: 遵循模块化样式架构
- **错误处理**: 完善的错误处理和用户提示
- **性能优化**: 图片懒加载、数据缓存、按需加载
- **用户体验**: 加载状态、空状态、错误状态的友好提示

### 项目统计
- **页面总数**: 15个（包含1个开发工具页面）
- **自定义组件**: 4个（导航栏、空状态、智能图片、视频缩略图）
- **样式模块**: 8个（基础样式、组件样式、图标库等）
- **工具模块**: 14个（重构后的utils工具库）
- **图片资源**: 11个（Tab图标、功能图标、Logo等）

## 📱 微信小程序特性

### 登录策略
- **渐进式登录**: 静默登录 → 异常处理 → 关键页面检查
- **自动登录**: 服务端获取用户信息实现自动登录注册
- **权限管理**: 最小权限原则，按需获取用户授权

### 导航设计
- **底部导航**: 纯文本tabBar（首页/成长/我的）
- **顶部导航**: 居中标题，孩子切换下拉菜单
- **页面跳转**: 统一的导航API，智能选择跳转方式

### 分享机制
- **微信视频号**: 打卡内容发布到视频号
- **朋友圈分享**: 成就卡片、学习进度分享
- **好友分享**: 训练营推荐、排行榜炫耀

## 🚀 部署说明

### 开发环境
- 微信开发者工具 1.06+
- Node.js 16+ (用于工具脚本)
- 微信小程序测试号

### 生产环境
- 微信小程序正式账号
- 域名备案和HTTPS证书
- 服务端API部署完成

## 📞 技术支持

### 文档资源
- [开发文档](./docs/development/) - 详细的开发指南
- [使用指南](./docs/guides/) - 功能使用说明
- [完成报告](./docs/reports/) - 项目里程碑记录

### 常见问题
- **模块导入错误**: 检查utils包导入方式
- **状态管理问题**: 参考状态管理使用指南
- **微信API调用**: 查看微信API封装文档

---

> 📝 **注意**: 本项目遵循MVS开发规则，确保代码质量和可维护性。所有功能开发都需要经过需求确认、统一模板、基础测试、提交检查四个步骤。
│   │   ├── avatar-*.png       # 默认头像
│   │   └── share-*.png        # 分享图片
│   │
│   └── utils/                  # 工具函数
│       ├── request.js         # 网络请求封装
│       ├── storage.js         # 本地存储封装
│       └── utils.js           # 通用工具函数
│
└── README.md                   # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具 (最新稳定版)
- Node.js 16+ (如需使用npm包)

### 开发步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd frontend
   ```

2. **打开微信开发者工具**
   - 选择"导入项目"
   - 项目目录选择 `frontend/miniprogram`
   - AppID填写你的小程序AppID

3. **配置项目**
   - 修改 `project.config.json` 中的 `appid`
   - 修改 `app.js` 中的 `apiBaseUrl` 为实际API地址

4. **开始开发**
   - 在微信开发者工具中预览和调试
   - 修改代码后自动刷新

## 🎯 核心页面说明

### 首页 (pages/home)
- **功能**: 训练营发现和选择中心
- **特色**: 渐变背景、卡片式布局、孩子切换功能
- **交互**: 训练营卡片点击、孩子选择弹窗

### 成长页 (pages/growth)
- **功能**: 学习行动中心，核心功能页面
- **特色**: 任务列表、进度展示、打卡记录滑动
- **优化**: 简化设计、智能展示、快捷入口

### 打卡成功页 (pages/checkin-success)
- **功能**: 成就展示和分享引流的关键页面
- **特色**: 全屏动画、成就卡片、智能文案
- **亮点**: 自动生成分享图片、多渠道分享

## 🎨 设计系统

### 颜色变量
```css
--primary-color: #FF7A45;      /* 主色调：活力橙 */
--secondary-color: #7ED321;    /* 功能色：跳绳绿 */
--background-color: #F7F8FA;   /* 背景色：米白色 */
--info-color: #4A90E2;         /* 辅助色：青蓝色 */
```

### 间距系统
```css
--spacing-xs: 4rpx;   --spacing-sm: 8rpx;   --spacing-md: 16rpx;
--spacing-lg: 24rpx;  --spacing-xl: 32rpx;  --spacing-xxl: 48rpx;
```

### 字体大小
```css
--font-xs: 24rpx;   --font-sm: 28rpx;   --font-md: 32rpx;
--font-lg: 36rpx;   --font-xl: 40rpx;   --font-xxl: 48rpx;
```

## 🔧 开发规范

### 代码规范
- 使用ES6+语法
- 组件化开发思维
- 统一的命名规范
- 详细的注释说明

### 样式规范
- 使用CSS变量
- 移动端适配 (rpx单位)
- 统一的组件样式
- 响应式设计

### 文件命名
- 页面文件：小写+连字符 (如: `checkin-success`)
- 图片文件：功能+描述 (如: `icon-dropdown.png`)
- 样式类名：BEM命名法

## 📱 功能特色

### 智能视频展示
- 1个视频：不显示相关区域
- 2-4个视频：横向展示所有
- 5个以上：展示4个+"查看更多"

### 分享引流机制
- 打卡成功强化成就感
- 智能匹配分享文案
- 一键生成精美卡片
- 多渠道分享支持

### 勋章体系
- **成长类**: 跳绳新手、坚持之星、跳绳达人
- **挑战类**: 任务完成者、挑战者、速度之星
- **社交类**: 推广大使、分享达人、社区贡献者
- **特殊类**: 首发用户、全勤宝宝、节日特别

## 🔗 API集成

### 网络请求
```javascript
// 使用app.request方法
app.request({
  url: '/api/checkin/create',
  method: 'POST',
  data: { ... }
}).then(res => {
  // 处理成功响应
}).catch(err => {
  // 处理错误
})
```

### 数据模拟
当前版本使用模拟数据，便于前端开发和演示。实际部署时需要：
1. 配置真实的API地址
2. 实现微信登录接口
3. 对接后端数据接口

## 📊 性能优化

### 加载优化
- 图片懒加载
- 分包加载
- 预加载关键页面

### 体验优化
- 骨架屏加载
- 平滑动画过渡
- 错误状态处理

## 🚀 部署发布

### 发布前检查
- [ ] 修改真实AppID
- [ ] 配置生产环境API
- [ ] 测试所有核心功能
- [ ] 检查图片资源完整性
- [ ] 验证分享功能

### 发布流程
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台提交审核
4. 审核通过后发布上线

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**项目状态**: ✅ 静态页面完成，可进行演示和API集成  
**最后更新**: 2025-01-09
