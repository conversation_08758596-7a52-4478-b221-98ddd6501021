# 图标资源说明

## TabBar 图标要求

微信小程序 tabBar 图标需要满足以下要求：

### 尺寸要求
- 图标尺寸：81px * 81px
- 格式：PNG
- 背景：透明

### 设计建议
- 线条粗细：2-3px
- 风格：简洁、清晰
- 颜色：单色（灰色 #999999 和主题色 #FF7A45）

## 所需图标列表

### 首页 (Home)
- `tab-home.png` - 未选中状态（灰色）
- `tab-home-active.png` - 选中状态（橙色）
- 图标建议：房子/首页图标

### 成长 (Growth)  
- `tab-growth.png` - 未选中状态（灰色）
- `tab-growth-active.png` - 选中状态（橙色）
- 图标建议：上升箭头/成长图标

### 我的 (Profile)
- `tab-profile.png` - 未选中状态（灰色）
- `tab-profile-active.png` - 选中状态（橙色）
- 图标建议：用户头像/个人中心图标

## 其他图标

### 功能图标
- `icon-back.png` - 返回按钮
- `icon-add.png` - 添加按钮
- `icon-share.png` - 分享按钮
- `icon-edit.png` - 编辑按钮
- `icon-delete.png` - 删除按钮
- `icon-calendar.png` - 日历图标
- `icon-camera.png` - 相机图标
- `icon-lock.png` - 锁定图标

### 头像和插图
- `default-avatar.png` - 默认用户头像
- `default-child-avatar.png` - 默认孩子头像
- `app-logo.png` - 应用Logo
- `app-logo-white.png` - 白色Logo

### 空状态插图
- `empty-leaderboard.png` - 排行榜空状态
- `empty-badges.png` - 徽章空状态
- `empty-children.png` - 孩子列表空状态

### 分享相关
- `mini-qr-code.png` - 小程序二维码
- `share-card-preview.png` - 分享卡片预览
- `share-badges.png` - 徽章分享图
- `share-timeline-badges.png` - 朋友圈徽章分享图

## 临时解决方案

当前已移除 app.json 中的 tabBar 图标配置，使用纯文字显示。
如需添加图标，请：

1. 准备符合要求的PNG图标文件
2. 将图标文件放入 `images/` 目录
3. 在 `app.json` 中恢复图标配置：

```json
{
  "pagePath": "pages/home/<USER>",
  "iconPath": "images/tab-home.png",
  "selectedIconPath": "images/tab-home-active.png",
  "text": "首页"
}
```

## 图标制作工具推荐

- **在线工具**：iconfont.cn, flaticon.com
- **设计软件**：Figma, Sketch, Adobe Illustrator
- **转换工具**：在线PNG转换器

## 注意事项

1. 图标文件路径相对于小程序根目录
2. 文件名不能包含中文字符
3. 建议使用语义化的文件命名
4. 定期检查图标文件是否存在
