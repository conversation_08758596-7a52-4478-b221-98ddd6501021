{"pages": ["pages/home/<USER>", "pages/login/login", "demo/channels-video/channels-video", "pages/growth/main/main", "pages/growth/detail/detail", "pages/growth/checkin/checkin", "pages/social/leaderboard/leaderboard", "pages/social/share-poster/share-poster", "pages/social/badges/badges", "pages/social/share-card/share-card", "pages/content/video-list/video-list", "pages/content/video-play/video-play", "pages/auth/child-manage/child-manage", "pages/auth/child-create/child-create", "pages/user/profile/profile", "pages/user/settings/settings", "pages/camp-detail/camp-detail", "pages/checkin-success/checkin-success", "pages/checkin/checkin", "pages/image-test/image-test", "pages/test-loading/test-loading", "pages/demo/demo-index/demo-index", "pages/demo/checkin-form-v4/checkin-form-v4", "pages/demo/contract-v4/contract-v4", "pages/demo/growth-v2-index/growth-v2-index", "pages/demo/growth-v2-a-main/growth-v2-a-main", "pages/demo/growth-v2-a-detail/growth-v2-a-detail", "pages/demo/growth-v2-a-checkin/growth-v2-a-checkin", "pages/demo/growth-v2-a-ranking/growth-v2-a-ranking", "pages/demo/growth-v2-a-contract/growth-v2-a-contract", "pages/demo/growth-v2-a-poster/growth-v2-a-poster", "pages/demo/image-test/image-test", "pages/demo/network-test/network-test", "pages/demo/task-detail-demo-a/task-detail-demo-a", "pages/demo/task-detail-demo-d/task-detail-demo-d", "pages/demo/task-detail-demo-d-backup/task-detail-demo-d-backup", "pages/demo/task-detail-demo-c/task-detail-demo-c", "pages/demo/task-detail-demo-b/task-detail-demo-b"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#FF7A45", "navigationBarTitleText": "跳跳星球", "navigationBarTextStyle": "white", "backgroundColor": "#F7F8FA"}, "tabBar": {"color": "#8C8C8C", "selectedColor": "#FF7A45", "backgroundColor": "#FFFFFF", "borderStyle": "white", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "images/tab-home.png", "selectedIconPath": "images/tab-home-active.png", "text": "首页"}, {"pagePath": "pages/growth/main/main", "iconPath": "images/tab-growth.png", "selectedIconPath": "images/tab-growth-active.png", "text": "成长"}, {"pagePath": "pages/user/profile/profile", "iconPath": "images/tab-profile.png", "selectedIconPath": "images/tab-profile-active.png", "text": "我的"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredBackgroundModes": ["audio"], "style": "v2", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}