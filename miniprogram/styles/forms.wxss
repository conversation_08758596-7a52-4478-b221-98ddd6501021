/* 表单样式 - 输入框、选择器等表单元素 */

/* 基础表单样式 */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-md);
  color: var(--text-primary);
  background-color: #FFFFFF;
  min-height: var(--touch-target-min);
  box-sizing: border-box;
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-input::placeholder {
  color: var(--text-placeholder);
}

.form-input-error {
  border-color: var(--error-color);
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

.form-select {
  appearance: none;
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
  background-repeat: no-repeat;
  background-position: right var(--spacing-md) center;
  background-size: 24rpx;
  padding-right: 60rpx;
}

.form-error {
  font-size: var(--font-xs);
  color: var(--error-color);
  margin-top: var(--spacing-xs);
}

.form-help {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 复选框和单选框 */
.form-checkbox,
.form-radio {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.form-checkbox input,
.form-radio input {
  margin-right: var(--spacing-sm);
}

.form-checkbox label,
.form-radio label {
  font-size: var(--font-md);
  color: var(--text-primary);
  cursor: pointer;
}

/* 表单按钮组 */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.form-actions .btn {
  flex: 1;
}
