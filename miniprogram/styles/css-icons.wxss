/* CSS图标样式 - 替代图片方案 */

/* 空状态CSS图标 */
.empty-state-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: var(--text-secondary);
  margin: 0 auto 32rpx;
}

/* 排行榜空状态 */
.empty-leaderboard-icon::before {
  content: "📊";
}

/* 徽章空状态 */
.empty-badges-icon::before {
  content: "🏆";
}

/* 孩子列表空状态 */
.empty-children-icon::before {
  content: "👶";
}

/* CSS绘制的简单图标 */
.css-calendar-icon {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid currentColor;
  border-radius: 8rpx;
  position: relative;
  display: inline-block;
}

.css-calendar-icon::before {
  content: '';
  position: absolute;
  top: -6rpx;
  left: 8rpx;
  right: 8rpx;
  height: 4rpx;
  background-color: currentColor;
  border-radius: 2rpx;
}

.css-calendar-icon::after {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 8rpx;
  right: 8rpx;
  bottom: 8rpx;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    transparent 25%,
    currentColor 25%,
    currentColor 30%,
    transparent 30%,
    transparent 55%,
    currentColor 55%,
    currentColor 60%,
    transparent 60%
  );
}

/* CSS绘制的统计图标 */
.css-chart-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

.css-chart-icon::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 4rpx;
  width: 8rpx;
  height: 60%;
  background-color: currentColor;
  border-radius: 2rpx 2rpx 0 0;
}

.css-chart-icon::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16rpx;
  width: 8rpx;
  height: 80%;
  background-color: currentColor;
  border-radius: 2rpx 2rpx 0 0;
  box-shadow: 12rpx 0 0 currentColor, 24rpx 0 0 currentColor;
}

/* CSS绘制的奖杯图标 */
.css-trophy-icon {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  display: inline-block;
}

.css-trophy-icon::before {
  content: '';
  position: absolute;
  top: 8rpx;
  left: 12rpx;
  width: 24rpx;
  height: 20rpx;
  background-color: currentColor;
  border-radius: 4rpx 4rpx 8rpx 8rpx;
}

.css-trophy-icon::after {
  content: '';
  position: absolute;
  bottom: 4rpx;
  left: 16rpx;
  width: 16rpx;
  height: 12rpx;
  background-color: currentColor;
  border-radius: 2rpx;
}

/* 渐变背景样式 */
.gradient-bg-beginner {
  background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
}

.gradient-bg-advanced {
  background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
}

.gradient-bg-expert {
  background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
}

.gradient-bg-share {
  background: linear-gradient(135deg, #FF7A45 0%, #FFB366 100%);
}

/* 通用图标容器 */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.icon-container.active {
  color: var(--primary-color);
}

/* 动画效果 */
.icon-bounce {
  animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -6rpx, 0);
  }
  70% {
    transform: translate3d(0, -3rpx, 0);
  }
  90% {
    transform: translate3d(0, -1rpx, 0);
  }
}
