/* 通用emoji图标样式 */

/* 二维码 */
.qr-code-emoji {
  font-size: 120rpx;
  text-align: center;
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state-emoji {
  font-size: 120rpx;
  text-align: center;
  color: var(--text-secondary);
  opacity: 0.6;
}

/* 功能图标 */
.edit-small-icon,
.delete-icon,
.export-icon,
.switch-icon,
.share-icon {
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-child-icon {
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-icon-emoji {
  font-size: 80rpx;
  text-align: center;
  color: var(--primary-color);
}

.calendar-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.lock-icon-detail {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}

.invite-bonus-emoji {
  font-size: 120rpx;
  text-align: center;
  color: var(--primary-color);
}

.app-logo-white-emoji {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}
