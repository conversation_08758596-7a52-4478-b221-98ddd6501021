/* 布局样式 - 容器、卡片、flex布局 */

/* 通用容器 */
.container {
  padding: var(--spacing-md);
}

.page-container {
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 通用卡片 */
.card {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 2rpx solid var(--border-color);
  background-color: #FAFAFA;
}

/* 通用布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}
