/* WeUI 基础样式 - 适配微信小程序 */

/* ========== 基础变量 ========== */
:root {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --weui-FG-HALF: rgba(0, 0, 0, 0.9);
  --weui-FG-1: rgba(0, 0, 0, 0.55);
  --weui-FG-2: rgba(0, 0, 0, 0.3);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-RED: #fa5151;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-WHITE: #fff;
  --weui-LINK: #576b95;
  --weui-TEXTGREEN: #06ae56;
  --weui-FG: #000;
  --weui-BG: #fff;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
}

/* ========== 基础样式重置 ========== */
.weui-cells {
  margin-top: 20rpx;
  background-color: var(--weui-BG-2);
  line-height: 1.41176471;
  font-size: 34rpx;
  overflow: hidden;
  position: relative;
}

.weui-cells:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cells:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 2rpx;
  border-bottom: 2rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.weui-cells_after-title {
  margin-top: 0;
}

.weui-cells__title {
  margin-top: 60rpx;
  margin-bottom: 20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  color: var(--weui-FG-1);
  font-size: 28rpx;
  line-height: 1.4;
}

.weui-cells__title:first-child {
  margin-top: 0;
}

/* ========== 单元格样式 ========== */
.weui-cell {
  padding: 20rpx 30rpx;
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--weui-BG-2);
}

.weui-cell:before {
  content: " ";
  position: absolute;
  left: 30rpx;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cell:first-child:before {
  display: none;
}

.weui-cell_active {
  background-color: var(--weui-BG-0);
}

.weui-cell_access {
  color: inherit;
}

.weui-cell__bd {
  flex: 1;
}

.weui-cell__ft {
  text-align: right;
  color: var(--weui-FG-1);
}

.weui-cell__ft_in-access {
  padding-right: 26rpx;
  position: relative;
}

.weui-cell__ft_in-access:after {
  content: " ";
  display: inline-block;
  height: 12rpx;
  width: 12rpx;
  border-width: 4rpx 4rpx 0 0;
  border-color: var(--weui-FG-2);
  border-style: solid;
  transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position: absolute;
  top: 50%;
  margin-top: -8rpx;
  right: 4rpx;
}

/* ========== 输入框样式 ========== */
.weui-cell_input {
  padding-top: 0;
  padding-bottom: 0;
}

.weui-input {
  width: 100%;
  border: 0;
  outline: 0;
  -webkit-appearance: none;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  height: 88rpx;
  line-height: 88rpx;
}

.weui-input::-webkit-input-placeholder {
  color: var(--weui-FG-2);
}

.weui-textarea {
  display: block;
  border: 0;
  resize: none;
  width: 100%;
  color: inherit;
  font-size: 1em;
  line-height: inherit;
  outline: 0;
}

.weui-textarea::-webkit-input-placeholder {
  color: var(--weui-FG-2);
}

.weui-textarea-counter {
  color: var(--weui-FG-1);
  text-align: right;
  font-size: 30rpx;
}

/* ========== 按钮样式 ========== */
.weui-btn {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 28rpx;
  padding-right: 28rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  text-align: center;
  text-decoration: none;
  color: var(--weui-BG-2);
  line-height: 2.55555556;
  border-radius: 10rpx;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
  background-color: var(--weui-BRAND);
  border: 0;
  outline: 0;
}

.weui-btn_primary {
  background-color: var(--weui-BRAND);
  color: var(--weui-WHITE);
}

.weui-btn_default {
  background-color: var(--weui-BG-2);
  color: var(--weui-FG-0);
  border: 2rpx solid var(--weui-FG-3);
}

.weui-btn_warn {
  background-color: var(--weui-RED);
  color: var(--weui-WHITE);
}

.weui-btn_disabled {
  color: rgba(255, 255, 255, 0.6);
  background-color: var(--weui-FG-2);
}

.weui-btn_loading {
  color: transparent;
}

.weui-btn_mini {
  display: inline-block;
  line-height: 2.3;
  font-size: 26rpx;
  padding: 0 1.32em;
}

/* ========== 图标样式 ========== */
.weui-icon-radio {
  margin-left: 6rpx;
  margin-right: 6rpx;
}

/* ========== 加载样式 ========== */
.weui-loading {
  margin: 0 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  vertical-align: middle;
  animation: weuiLoading 1s steps(12, end) infinite;
  background: transparent url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgc3Ryb2tlPSIjZTllZWYzIj4KICAgIDxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMSAxKSIgc3Ryb2tlLXdpZHRoPSIyIj4KICAgICAgICAgICAgPGNpcmNsZSBjeD0iMjIiIGN5PSIyMiIgcj0iNiI+CiAgICAgICAgICAgICAgICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJyIgogICAgICAgICAgICAgICAgICAgICAgICAgYmVnaW49IjBzIiBkdXI9IjEuOHMiCiAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZXM9IjY7MjI7NjsyMiI+PC9hbmltYXRlPgogICAgICAgICAgICA8L2NpcmNsZT4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg==) no-repeat;
  background-size: 100%;
}

@keyframes weuiLoading {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

/* ========== 表单组样式 ========== */
.weui-form-preview {
  position: relative;
  background-color: var(--weui-BG-2);
}

.weui-form-preview:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-form-preview:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 2rpx;
  border-bottom: 2rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

/* ========== 自定义增强样式 ========== */
.weui-btn-area {
  margin: 30rpx;
}

.weui-btn + .weui-btn {
  margin-top: 20rpx;
}

/* 禁用状态 */
.weui-btn_disabled {
  opacity: 0.6;
}

/* 加载状态 */
.weui-btn_loading .weui-loading {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

/* 小按钮 */
.weui-btn_mini {
  font-size: 28rpx;
  line-height: 2;
  padding: 0 20rpx;
  margin: 0 10rpx;
}

/* 表单验证错误状态 */
.weui-cell_warn .weui-input {
  color: var(--weui-RED);
}

.weui-cell_warn .weui-label {
  color: var(--weui-RED);
}

/* 成功状态 */
.weui-cell_success .weui-input {
  color: var(--weui-BRAND);
}

/* 自定义图标 */
.weui-icon-success {
  color: var(--weui-BRAND);
}

.weui-icon-warn {
  color: var(--weui-RED);
}

.weui-icon-info {
  color: var(--weui-BLUE);
}
