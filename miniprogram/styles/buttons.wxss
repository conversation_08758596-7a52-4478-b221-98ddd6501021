/* 按钮样式 - 各种按钮类型和状态 */

/* 通用按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: var(--font-md);
  font-weight: 500;
  text-align: center;
  border: none;
  /* cursor: pointer; */
  transition: all 0.3s ease;
  min-height: var(--touch-target-min);
  min-width: 120rpx;
  line-height: var(--line-height-sm);
}

.btn-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-primary:active {
  background-color: #E6692D;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: #FFFFFF;
}

.btn-secondary:active {
  background-color: #6BB91C;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:active {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:active {
  background-color: #F5F5F5;
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-sm);
  min-height: var(--touch-target-min);
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-lg);
  min-height: var(--touch-target-comfortable);
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.5;
  /* pointer-events: none; */
}
