/* 基础样式 - 重置、页面基础、文字样式 */

/* 全局重置 */
view, text, button, input, textarea, image, scroll-view, swiper, picker {
  box-sizing: border-box;
}

page {
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-md);
  color: var(--text-primary);
  line-height: var(--line-height-md);
}

/* 通用文字样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-xs {
  font-size: var(--font-xs);
}

.text-sm {
  font-size: var(--font-sm);
}

.text-md {
  font-size: var(--font-md);
}

.text-lg {
  font-size: var(--font-lg);
}

.text-xl {
  font-size: var(--font-xl);
}

.text-xxl {
  font-size: var(--font-xxl);
}

.text-bold {
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 行高样式类 */
.line-height-sm {
  line-height: var(--line-height-sm);
}

.line-height-md {
  line-height: var(--line-height-md);
}

.line-height-lg {
  line-height: var(--line-height-lg);
}

/* 标准字体样式类 - 调整为合适大小 */
.text-title-main {
  font-size: 36rpx;    /* 主标题 - 不会太大 */
  color: #1A1A1A;
  line-height: 1.3;
  font-weight: 600;    /* 标题适当加粗 */
}

.text-title-section {
  font-size: 32rpx;    /* 区域标题 */
  color: #1A1A1A;
  line-height: 1.4;
  font-weight: 600;    /* 标题适当加粗 */
}

.text-title-card {
  font-size: 30rpx;    /* 卡片标题 */
  color: #1A1A1A;
  line-height: 1.4;
  font-weight: 600;    /* 标题适当加粗 */
}

.text-body {
  font-size: 30rpx;    /* 正文 */
  color: #595959;
  line-height: 1.5;
  font-weight: normal;
}

.text-caption {
  font-size: 28rpx;    /* 说明文字 */
  color: #595959;
  line-height: 1.4;
  font-weight: normal;
}

.text-label {
  font-size: 26rpx;    /* 标签 - 小一点 */
  color: #FF7A45;
  line-height: 1.3;
  font-weight: normal;
}

.text-button {
  font-size: 30rpx;    /* 按钮文字 */
  color: inherit;
  line-height: 1.3;
  font-weight: normal;
}

/* 通用工具类 */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-full {
  border-radius: 50%;
}

.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30rpx, 0); }
  70% { transform: translate3d(0, -15rpx, 0); }
  90% { transform: translate3d(0, -4rpx, 0); }
}
