/* 变量定义 - 颜色、字体、间距、圆角等 */

/* 全局变量定义 */
page {
  --primary-color: #FF7A45;      /* 主色调：活力橙 */
  --secondary-color: #7ED321;    /* 功能色：跳绳绿 */
  --background-color: #F7F8FA;   /* 背景色：米白色 */
  --info-color: #4A90E2;         /* 辅助色：青蓝色 */
  --text-primary: #1A1A1A;       /* 主要文字 - 提高对比度 */
  --text-secondary: #595959;     /* 次要文字 - 符合4.5:1对比度 */
  --text-placeholder: #8C8C8C;   /* 占位文字 - 符合3:1对比度 */
  --text-disabled: #BFBFBF;      /* 禁用文字 */
  --border-color: #E5E5E5;       /* 边框颜色 */
  --success-color: #52C41A;      /* 成功色 */
  --warning-color: #FAAD14;      /* 警告色 */
  --error-color: #F5222D;        /* 错误色 */
  
  /* 间距变量 */
  --spacing-xs: 4rpx;
  --spacing-sm: 8rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 48rpx;
  
  /* 圆角变量 */
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;
  
  /* 字体大小 - 合适的层级 */
  --font-xs: 26rpx;    /* 超小字体 - 标签 */
  --font-sm: 28rpx;    /* 小字体 - 说明文字 */
  --font-md: 30rpx;    /* 标准字体 - 正文、按钮 */
  --font-lg: 32rpx;    /* 大字体 - 区域标题 */
  --font-xl: 36rpx;    /* 特大字体 - 主标题 */

  /* 行高变量 - 符合1.3倍标准 */
  --line-height-sm: 1.3;
  --line-height-md: 1.4;
  --line-height-lg: 1.5;

  /* 触摸目标尺寸 - 符合44x44pt标准 */
  --touch-target-min: 88rpx;     /* 最小触摸目标 44pt */
  --touch-target-comfortable: 96rpx; /* 舒适触摸目标 48pt */
}
