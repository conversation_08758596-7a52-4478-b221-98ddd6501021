// 用户相关API - 遵循MVS规则
// 处理用户信息获取、更新、管理等功能

const { http } = require("../utils/request.js");
const { API } = require("../utils/constants.js");

// 用户相关API端点 - 模块内部定义
const ENDPOINTS = {
  INFO: `${API.BASE_URL}/user/info`,
  BY_ID: `${API.BASE_URL}/admin/user/:id`,
  UPLOAD_AVATAR: `${API.BASE_URL}/user/avatar`,
  STATS: `${API.BASE_URL}/user/stats`,
  BIND_PHONE: `${API.BASE_URL}/user/bind-phone`,
  GROWTH: `${API.BASE_URL}/user/growth`,
  GROWTH_STATS: `${API.BASE_URL}/user/growth/stats`,
  GROWTH_TRACK: `${API.BASE_URL}/user/growth/track`,
};

/**
 * 用户API模块
 */
const userAPI = {
  /**
   * 获取当前用户信息
   * @returns {Promise<Object>} 用户信息
   * @example
   * const userInfo = await userAPI.getCurrentUser();
   */
  async getCurrentUser() {
    console.log("👤 获取当前用户信息");

    try {
      const response = await http.get(ENDPOINTS.INFO);

      console.log("✅ 获取用户信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取用户信息失败:", error);
      throw error;
    }
  },

  /**
   * 获取用户信息（getCurrentUser的别名）
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo() {
    return this.getCurrentUser();
  },

  /**
   * 更新用户信息
   * @param {Object} userInfo 用户信息
   * @param {string} userInfo.nickname 昵称
   * @param {string} userInfo.avatar 头像URL
   * @param {number} userInfo.gender 性别 (0:未知, 1:男, 2:女)
   * @param {string} userInfo.city 城市
   * @param {string} userInfo.province 省份
   * @param {string} userInfo.country 国家
   * @returns {Promise<Object>} 更新结果
   * @example
   * const result = await userAPI.updateUserInfo({
   *   nickname: '新昵称',
   *   avatar: 'avatar_url'
   * });
   */
  async updateUserInfo(userInfo) {
    console.log("📝 更新用户信息:", userInfo);

    try {
      // 参数验证
      if (!userInfo || typeof userInfo !== "object") {
        throw new Error("用户信息参数无效");
      }

      const response = await http.put(ENDPOINTS.INFO, userInfo);

      console.log("✅ 用户信息更新成功");
      return response;
    } catch (error) {
      console.error("❌ 用户信息更新失败:", error);
      throw error;
    }
  },

  /**
   * 根据ID获取用户信息（管理员权限）
   * @param {number} userId 用户ID
   * @returns {Promise<Object>} 用户信息
   * @example
   * const user = await userAPI.getUserById(123);
   */
  async getUserById(userId) {
    console.log("🔍 根据ID获取用户信息:", userId);

    try {
      if (!userId) {
        throw new Error("用户ID不能为空");
      }

      const url = ENDPOINTS.BY_ID.replace(":id", userId);
      const response = await http.get(url);

      console.log("✅ 获取用户信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取用户信息失败:", error);
      throw error;
    }
  },

  /**
   * 上传用户头像
   * @param {string} filePath 头像文件路径
   * @returns {Promise<Object>} 上传结果
   * @example
   * const result = await userAPI.uploadAvatar(filePath);
   */
  async uploadAvatar(filePath) {
    console.log("📸 上传用户头像:", filePath);

    try {
      if (!filePath) {
        throw new Error("头像文件路径不能为空");
      }

      const response = await http.upload(ENDPOINTS.UPLOAD_AVATAR, filePath, {
        name: "avatar",
        formData: {},
      });

      console.log("✅ 头像上传成功");
      return response;
    } catch (error) {
      console.error("❌ 头像上传失败:", error);
      throw error;
    }
  },

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>} 统计信息
   * @example
   * const stats = await userAPI.getUserStats();
   */
  async getUserStats() {
    console.log("📊 获取用户统计信息");

    try {
      const response = await http.get(ENDPOINTS.STATS);

      console.log("✅ 获取统计信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取统计信息失败:", error);
      throw error;
    }
  },

  /**
   * 绑定手机号
   * @param {Object} params 绑定参数
   * @param {string} params.encryptedData 加密数据
   * @param {string} params.iv 初始向量
   * @returns {Promise<Object>} 绑定结果
   * @example
   * const result = await userAPI.bindPhone({
   *   encryptedData: 'encrypted_data',
   *   iv: 'iv_string'
   * });
   */
  async bindPhone(params) {
    console.log("📱 绑定手机号");

    try {
      if (!params.encryptedData || !params.iv) {
        throw new Error("加密数据和初始向量不能为空");
      }

      const response = await http.post(ENDPOINTS.BIND_PHONE, {
        encrypted_data: params.encryptedData,
        iv: params.iv,
      });

      console.log("✅ 手机号绑定成功");
      return response;
    } catch (error) {
      console.error("❌ 手机号绑定失败:", error);
      throw error;
    }
  },

  /**
   * 获取成长页面完整数据
   * 注意：child_id通过X-Children-ID头部自动传递
   * @returns {Promise<Object>} 成长页面数据
   * @example
   * const growthData = await userAPI.getGrowthPageData();
   */
  async getGrowthPageData() {
    console.log("📈 获取成长页面数据");

    try {
      const response = await http.get(ENDPOINTS.GROWTH);

      console.log("✅ 获取成长页面数据成功");
      return response;
    } catch (error) {
      console.error("❌ 获取成长页面数据失败:", error);
      throw error;
    }
  },

  /**
   * 获取成长统计数据
   * 注意：child_id通过X-Children-ID头部自动传递
   * @returns {Promise<Object>} 成长统计数据
   * @example
   * const growthStats = await userAPI.getGrowthStats();
   */
  async getGrowthStats() {
    console.log("📊 获取成长统计数据");

    try {
      const response = await http.get(ENDPOINTS.GROWTH_STATS);

      console.log("✅ 获取成长统计数据成功");
      return response;
    } catch (error) {
      console.error("❌ 获取成长统计数据失败:", error);
      throw error;
    }
  },

  /**
   * 获取成长轨迹记录
   * 注意：child_id通过X-Children-ID头部自动传递
   * @returns {Promise<Object>} 成长轨迹数据
   * @example
   * const growthTrack = await userAPI.getGrowthTrack();
   */
  async getGrowthTrack() {
    console.log("📈 获取成长轨迹记录");

    try {
      const response = await http.get(ENDPOINTS.GROWTH_TRACK);

      console.log("✅ 获取成长轨迹记录成功");
      return response;
    } catch (error) {
      console.error("❌ 获取成长轨迹记录失败:", error);
      throw error;
    }
  },
};

module.exports = userAPI;
