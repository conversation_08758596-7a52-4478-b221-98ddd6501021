// 孩子管理相关API - 遵循MVS规则
// 处理孩子档案的增删改查、统计信息等功能

const { http } = require("../utils/request.js");
const { API } = require("../utils/constants.js");

// 孩子管理相关API端点 - 模块内部定义
const ENDPOINTS = {
  LIST: `${API.BASE_URL}/children`,
  CREATE: `${API.BASE_URL}/children`,
  UPDATE: `${API.BASE_URL}/children/:id`,
  DELETE: `${API.BASE_URL}/children/:id`,
  DETAIL: `${API.BASE_URL}/children/:id`,
  CURRENT: `${API.BASE_URL}/children/current`,
  SELECT: `${API.BASE_URL}/children/:id/select`,
  STATS: `${API.BASE_URL}/children/:id/stats`,
  UPLOAD_AVATAR: `${API.BASE_URL}/children/avatar`,
};

/**
 * 孩子管理API模块
 */
const childrenAPI = {
  /**
   * 获取孩子列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码（可选）
   * @param {number} params.limit 每页数量（可选）
   * @returns {Promise<Array>} 孩子列表
   * @example
   * const children = await childrenAPI.getChildrenList();
   */
  async getChildrenList(params = {}) {
    console.log("👶 获取孩子列表:", params);

    try {
      const response = await http.get(ENDPOINTS.LIST, { params });

      console.log("✅ 获取孩子列表成功");
      return response;
    } catch (error) {
      console.error("❌ 获取孩子列表失败:", error);
      throw error;
    }
  },

  /**
   * 创建孩子档案
   * @param {Object} childInfo 孩子信息
   * @param {string} childInfo.name 姓名
   * @param {string} childInfo.nickname 昵称（可选）
   * @param {number} childInfo.gender 性别 (1:男, 2:女)
   * @param {string} childInfo.birth_date 出生日期 YYYY-MM-DD（可选，如果提供age则自动计算）
   * @param {number} childInfo.age 年龄（可选，如果提供birth_date则忽略）
   * @param {string} childInfo.school 学校（可选）
   * @param {string} childInfo.grade 年级（可选）
   * @param {string} childInfo.province 省份（可选）
   * @param {string} childInfo.city 城市（可选）
   * @param {string} childInfo.avatar 头像URL（可选）
   * @param {string} childInfo.relation 与孩子的关系（必填）
   * @returns {Promise<Object>} 创建结果
   * @example
   * const child = await childrenAPI.createChild({
   *   name: '小明',
   *   nickname: '明明',
   *   gender: 1,
   *   birth_date: '2015-06-15',
   *   relation: '爸爸'
   * });
   */
  async createChild(childInfo) {
    console.log("➕ 创建孩子档案:", childInfo);

    try {
      // 参数验证
      if (!childInfo.name) {
        throw new Error("孩子姓名不能为空");
      }
      if (!childInfo.gender || ![1, 2].includes(childInfo.gender)) {
        throw new Error("性别参数无效");
      }
      if (!childInfo.relation) {
        throw new Error("与孩子的关系不能为空");
      }

      // 处理年龄和出生日期的转换
      let createData = { ...childInfo };

      // 如果提供了age但没有birth_date，则根据age计算birth_date
      if (childInfo.age && !childInfo.birth_date) {
        if (childInfo.age < 1 || childInfo.age > 18) {
          throw new Error("孩子年龄必须在1-18岁之间");
        }
        const currentYear = new Date().getFullYear();
        const birthYear = currentYear - childInfo.age;
        createData.birth_date = `${birthYear}-01-01`; // 使用年初作为默认生日
      }

      // 移除age字段，后端不需要
      delete createData.age;

      const response = await http.post(ENDPOINTS.CREATE, createData);

      console.log("✅ 孩子档案创建成功");
      return response;
    } catch (error) {
      console.error("❌ 孩子档案创建失败:", error);
      throw error;
    }
  },

  /**
   * 更新孩子信息
   * @param {number} childId 孩子ID
   * @param {Object} childInfo 更新的孩子信息
   * @param {string} childInfo.name 姓名
   * @param {string} childInfo.nickname 昵称
   * @param {number} childInfo.gender 性别 (1:男, 2:女)
   * @param {string} childInfo.birth_date 出生日期 YYYY-MM-DD
   * @param {string} childInfo.school 学校（可选）
   * @param {string} childInfo.grade 年级（可选）
   * @param {string} childInfo.province 省份（可选）
   * @param {string} childInfo.city 城市（可选）
   * @param {string} childInfo.avatar 头像URL（可选）
   * @param {number} childInfo.preferred_difficulty 偏好难度 1-5级（可选，默认2）
   * @param {number} childInfo.privacy_level 隐私级别 1:公开 2:好友可见 3:仅自己（可选，默认2）
   * @param {number} childInfo.show_in_leaderboard 是否参与排行榜 0:不参与 1:参与（可选，默认1）
   * @returns {Promise<Object>} 更新结果
   * @example
   * const result = await childrenAPI.updateChild(123, {
   *   name: '小明明',
   *   nickname: '明明',
   *   gender: 1,
   *   birth_date: '2015-06-15'
   * });
   */
  async updateChild(childId, childInfo) {
    console.log("📝 更新孩子信息:", { childId, childInfo });

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }
      if (!childInfo || typeof childInfo !== "object") {
        throw new Error("更新信息参数无效");
      }

      // 直接使用传入的数据，这些字段在后端不是必填的
      const updateData = { ...childInfo };

      const url = ENDPOINTS.UPDATE.replace(":id", childId);
      const response = await http.put(url, updateData);

      console.log("✅ 孩子信息更新成功");
      return response;
    } catch (error) {
      console.error("❌ 孩子信息更新失败:", error);
      throw error;
    }
  },

  /**
   * 删除孩子档案
   * @param {number} childId 孩子ID
   * @returns {Promise<Object>} 删除结果
   * @example
   * await childrenAPI.deleteChild(123);
   */
  async deleteChild(childId) {
    console.log("🗑️ 删除孩子档案:", childId);

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }

      const url = ENDPOINTS.DELETE.replace(":id", childId);
      const response = await http.delete(url);

      console.log("✅ 孩子档案删除成功");
      return response;
    } catch (error) {
      console.error("❌ 孩子档案删除失败:", error);
      throw error;
    }
  },

  /**
   * 获取孩子详细信息
   * @param {number} childId 孩子ID
   * @returns {Promise<Object>} 孩子详细信息
   * @example
   * const child = await childrenAPI.getChildDetail(123);
   */
  async getChildDetail(childId) {
    console.log("🔍 获取孩子详细信息:", childId);

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }

      const url = ENDPOINTS.DETAIL.replace(":id", childId);
      const response = await http.get(url);

      console.log("✅ 获取孩子详细信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取孩子详细信息失败:", error);
      throw error;
    }
  },

  /**
   * 获取孩子统计信息
   * @param {number} childId 孩子ID
   * @returns {Promise<Object>} 统计信息
   * @example
   * const stats = await childrenAPI.getChildStats(123);
   */
  async getChildStats(childId) {
    console.log("📊 获取孩子统计信息:", childId);

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }

      const url = ENDPOINTS.STATS.replace(":id", childId);
      const response = await http.get(url);

      console.log("✅ 获取孩子统计信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取孩子统计信息失败:", error);
      throw error;
    }
  },

  /**
   * 上传孩子头像
   * @param {number} childId 孩子ID
   * @param {string} filePath 头像文件路径
   * @returns {Promise<Object>} 上传结果
   * @example
   * const result = await childrenAPI.uploadChildAvatar(123, filePath);
   */
  async uploadChildAvatar(childId, filePath) {
    console.log("📸 上传孩子头像:", { childId, filePath });

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }
      if (!filePath) {
        throw new Error("头像文件路径不能为空");
      }

      const response = await http.upload(ENDPOINTS.UPLOAD_AVATAR, filePath, {
        name: "avatar",
        formData: { child_id: childId },
      });

      console.log("✅ 孩子头像上传成功");
      return response;
    } catch (error) {
      console.error("❌ 孩子头像上传失败:", error);
      throw error;
    }
  },

  /**
   * 获取当前选择的孩子
   * @returns {Promise<Object>} 当前孩子信息
   * @example
   * const currentChild = await childrenAPI.getCurrentChild();
   */
  async getCurrentChild() {
    console.log("👶 获取当前选择的孩子");

    try {
      const response = await http.get(ENDPOINTS.CURRENT);

      console.log("✅ 获取当前孩子成功");
      return response;
    } catch (error) {
      console.error("❌ 获取当前孩子失败:", error);
      throw error;
    }
  },

  /**
   * 选择当前孩子
   * @param {number} childId 孩子ID
   * @returns {Promise<Object>} 选择结果
   * @example
   * const result = await childrenAPI.selectCurrentChild(123);
   */
  async selectCurrentChild(childId) {
    console.log("🔄 选择当前孩子:", childId);

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }

      const url = ENDPOINTS.SELECT.replace(":id", childId);
      const response = await http.post(url, {});

      console.log("✅ 选择当前孩子成功");
      return response;
    } catch (error) {
      console.error("❌ 选择当前孩子失败:", error);
      throw error;
    }
  },
};

module.exports = childrenAPI;
