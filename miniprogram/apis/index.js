// APIs统一管理 - 遵循MVS规则
// 统一导出所有业务API模块

// 导入各个API模块
const authAPI = require('./auth.js');
const userAPI = require('./user.js');
const childrenAPI = require('./children.js');
const checkinAPI = require('./checkin.js');
const contentAPI = require('./content.js');

// 统一导出 - 提供多种访问方式
module.exports = {
  // 方式1: 直接访问各个API模块
  authAPI,
  userAPI,
  childrenAPI,
  checkinAPI,
  contentAPI,

  // 方式2: 通过api对象访问（推荐）
  api: {
    auth: authAPI,
    user: userAPI,
    children: childrenAPI,
    checkin: checkinAPI,
    content: contentAPI,
  },

  // 方式3: 别名访问（向后兼容）
  auth: authAPI,
  user: userAPI,
  children: childrenAPI,
  checkin: checkinAPI,
  content: contentAPI,
};
