// 认证相关API - 遵循MVS规则
// 处理用户登录、登出、token管理等认证功能

const { http } = require("../utils/request.js");
const { API } = require("../utils/constants.js");

// 认证相关API端点 - 模块内部定义
const ENDPOINTS = {
  WECHAT_LOGIN: `${API.BASE_URL}/auth/wechat/login`,
  REFRESH_TOKEN: `${API.BASE_URL}/auth/refresh`,
  LOGOUT: `${API.BASE_URL}/auth/logout`,
  VERIFY_TOKEN: `${API.BASE_URL}/auth/verify`,
};

/**
 * 认证API模块
 */
const authAPI = {
  /**
   * 微信登录
   * @param {Object} params 登录参数
   * @param {string} params.code 微信登录code
   * @param {string} params.encryptedData 加密用户信息（可选）
   * @param {string} params.iv 初始向量（可选）
   * @returns {Promise<Object>} 登录结果
   * @example
   * const result = await authAPI.wechatLogin({ code: 'wx_code' });
   */
  async wechatLogin(params) {
    console.log("🌐 调用微信登录API:", params);

    try {
      // 参数验证
      if (!params.code) {
        throw new Error("微信登录code不能为空");
      }

      // 准备请求参数
      const requestData = {
        code: params.code,
        encrypted_data: params.encryptedData || "",
        iv: params.iv || "",
      };

      // 调用后端API
      const response = await http.post(ENDPOINTS.WECHAT_LOGIN, requestData);

      console.log("✅ 微信登录API成功:", response);
      return response;
    } catch (error) {
      console.error("❌ 微信登录API失败:", error);
      throw error;
    }
  },

  /**
   * 刷新访问令牌
   * @param {string} refreshToken 刷新令牌
   * @returns {Promise<Object>} 新的Token信息
   * @example
   * const newToken = await authAPI.refreshToken('refresh_token_string');
   */
  async refreshToken(refreshToken) {
    console.log("🔄 刷新访问令牌");

    try {
      if (!refreshToken) {
        throw new Error("刷新令牌不能为空");
      }

      const response = await http.post(ENDPOINTS.REFRESH_TOKEN, {
        refresh_token: refreshToken,
      });

      console.log("✅ Token刷新成功");
      return response;
    } catch (error) {
      console.error("❌ Token刷新失败:", error);
      throw error;
    }
  },

  /**
   * 用户登出
   * @returns {Promise<Object>} 登出结果
   * @example
   * await authAPI.logout();
   */
  async logout() {
    console.log("👋 用户登出");

    try {
      const response = await http.post(ENDPOINTS.LOGOUT);

      console.log("✅ 登出成功");
      return response;
    } catch (error) {
      console.error("❌ 登出失败:", error);
      throw error;
    }
  },

  /**
   * 验证Token有效性
   * @param {string} token 访问令牌
   * @returns {Promise<Object>} 验证结果
   * @example
   * const isValid = await authAPI.verifyToken('access_token');
   */
  async verifyToken(token) {
    console.log("🔍 验证Token有效性");

    try {
      if (!token) {
        throw new Error("Token不能为空");
      }

      const response = await http.post(ENDPOINTS.VERIFY_TOKEN, {
        token: token,
      });

      console.log("✅ Token验证完成");
      return response;
    } catch (error) {
      console.error("❌ Token验证失败:", error);
      throw error;
    }
  },
};

module.exports = authAPI;
