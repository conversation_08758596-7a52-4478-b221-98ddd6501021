/* 跳跳星球 - 全局样式 */

/* 导入WeUI基础样式 */
@import "styles/weui.wxss"; /* WeUI基础组件样式 */

/* 导入样式模块 - 按功能拆分便于维护 */
@import "styles/variables.wxss"; /* 变量定义：颜色、字体、间距等 */
@import "styles/base.wxss"; /* 基础样式：重置、文字、工具类 */
@import "styles/layout.wxss"; /* 布局样式：容器、卡片、flex */
@import "styles/buttons.wxss"; /* 按钮样式：各种按钮类型 */
@import "styles/spacing.wxss"; /* 间距样式：margin、padding */
@import "styles/forms.wxss"; /* 表单样式：输入框、选择器 */
@import "styles/emoji-icons.wxss"; /* 表情图标样式 */
