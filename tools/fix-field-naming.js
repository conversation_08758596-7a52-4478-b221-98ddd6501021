#!/usr/bin/env node

/**
 * 批量修正页面文件中的字段命名问题
 * 将与后端API交互的字段统一为snake_case
 */

const fs = require('fs');
const path = require('path');

// 需要修正的字段映射（驼峰 -> snake_case）
const FIELD_MAPPINGS = {
  // 用户相关字段
  'userId': 'user_id',
  'childId': 'child_id', 
  'campId': 'camp_id',
  'taskId': 'task_id',
  'checkinId': 'checkin_id',
  
  // 用户信息字段
  'realName': 'real_name',
  'nickName': 'nickname',  // 特殊：微信API返回nickName，但后端使用nickname
  'avatarUrl': 'avatar',   // 特殊：微信API返回avatarUrl，但后端使用avatar
  'birthDate': 'birth_date',
  'phoneNumber': 'phone',  // 特殊：微信API返回phoneNumber，但后端使用phone
  
  // 时间字段
  'createdAt': 'created_at',
  'updatedAt': 'updated_at',
  'lastLoginAt': 'last_login_at',
  
  // 内容相关字段
  'videoUrl': 'video_url',
  'thumbnailUrl': 'thumbnail_url',
  'imageUrl': 'image_url',
  'viewCount': 'view_count',
  'likeCount': 'like_count',
  'commentCount': 'comment_count',
  
  // 打卡相关字段
  'checkinType': 'checkin_type',
  'checkinDate': 'checkin_date',
  'checkinCount': 'checkin_count',
  'exerciseDuration': 'exercise_duration',
  'exerciseCount': 'exercise_count',
  
  // 状态字段
  'isNewUser': 'is_new_user',
  'isPublic': 'is_public',
  'isLiked': 'is_liked',
  'isFavorited': 'is_favorited',
  'userType': 'user_type',
  'currentChildId': 'current_child_id'
};

// 需要特殊处理的文件（微信API字段映射）
const WECHAT_API_FILES = [
  'miniprogram/pages/login/login.js',
  'miniprogram/pages/user/settings/settings.js'
];

const results = {
  processedFiles: 0,
  totalReplacements: 0,
  errors: []
};

/**
 * 检查是否为微信API相关文件
 */
function isWechatApiFile(filePath) {
  return WECHAT_API_FILES.some(pattern => filePath.includes(pattern));
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let fileReplacements = 0;
    
    // 对于每个字段映射进行替换
    Object.entries(FIELD_MAPPINGS).forEach(([oldField, newField]) => {
      // 特殊处理微信API文件
      if (isWechatApiFile(filePath)) {
        // 在微信API文件中，保留微信API字段，但添加注释说明映射关系
        if (oldField === 'nickName' || oldField === 'avatarUrl' || oldField === 'phoneNumber') {
          // 这些字段在微信API文件中需要保留原样，只在传递给后端时映射
          return;
        }
      }
      
      // 替换对象字段定义：fieldName: value
      const objectFieldRegex = new RegExp(`(\\s+)${oldField}(\\s*:)`, 'g');
      const objectFieldMatches = content.match(objectFieldRegex);
      if (objectFieldMatches) {
        content = content.replace(objectFieldRegex, `$1${newField}$2`);
        fileReplacements += objectFieldMatches.length;
        hasChanges = true;
      }
      
      // 替换属性访问：obj.fieldName
      const propertyAccessRegex = new RegExp(`(\\.\\s*)${oldField}(\\s*[^a-zA-Z0-9_])`, 'g');
      const propertyMatches = content.match(propertyAccessRegex);
      if (propertyMatches) {
        content = content.replace(propertyAccessRegex, `$1${newField}$2`);
        fileReplacements += propertyMatches.length;
        hasChanges = true;
      }
      
      // 替换字符串字段："fieldName": value
      const stringFieldRegex = new RegExp(`(["'])${oldField}(["']\\s*:)`, 'g');
      const stringMatches = content.match(stringFieldRegex);
      if (stringMatches) {
        content = content.replace(stringFieldRegex, `$1${newField}$2`);
        fileReplacements += stringMatches.length;
        hasChanges = true;
      }
    });
    
    // 如果有修改，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修正文件: ${filePath} (${fileReplacements} 处修改)`);
      results.totalReplacements += fileReplacements;
    }
    
    results.processedFiles++;
    
  } catch (error) {
    results.errors.push({
      file: filePath,
      error: error.message
    });
    console.error(`❌ 处理文件失败: ${filePath} - ${error.message}`);
  }
}

/**
 * 递归处理目录
 */
function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // 跳过demo目录
      if (item !== 'demo') {
        processDirectory(itemPath);
      }
    } else if (stat.isFile() && item.endsWith('.js')) {
      processFile(itemPath);
    }
  });
}

/**
 * 输出结果
 */
function outputResults() {
  console.log('\n🎯 字段命名修正完成\n');
  console.log(`📊 统计信息:`);
  console.log(`   处理文件数: ${results.processedFiles}`);
  console.log(`   总修改数: ${results.totalReplacements}`);
  console.log(`   错误数: ${results.errors.length}\n`);
  
  if (results.errors.length > 0) {
    console.log('❌ 错误列表:');
    results.errors.forEach(error => {
      console.log(`   ${error.file}: ${error.error}`);
    });
    console.log('');
  }
  
  if (results.totalReplacements > 0) {
    console.log('✅ 字段命名修正完成！建议运行测试验证修改结果。');
  } else {
    console.log('ℹ️  没有发现需要修正的字段命名问题。');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始批量修正字段命名...\n');
  
  // 处理页面目录
  const pagesDir = 'miniprogram/pages';
  console.log(`📁 处理目录: ${pagesDir}`);
  processDirectory(pagesDir);
  
  // 输出结果
  outputResults();
}

// 运行修正
if (require.main === module) {
  main();
}

module.exports = {
  processDirectory,
  processFile,
  FIELD_MAPPINGS
};
