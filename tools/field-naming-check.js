#!/usr/bin/env node

/**
 * 字段命名规范检查工具
 * 检查前端代码和API文档中的字段命名是否符合snake_case规范
 */

const fs = require("fs");
const path = require("path");

// 配置
const CONFIG = {
  // 需要检查的文件类型
  fileExtensions: [".js", ".yaml", ".yml", ".json"],

  // 需要检查的目录
  checkDirs: [
    "miniprogram/apis",
    "miniprogram/pages",
    "miniprogram/utils",
    "docs/02-design/api",
  ],

  // 排除的目录
  excludeDirs: ["node_modules", ".git", "dist", "build"],

  // 允许的驼峰命名字段（微信API返回的字段和常见变量名）
  allowedCamelCase: [
    "nickName",
    "avatarUrl",
    "encryptedData",
    "currentChild",
    "childrenList",
    "userInfo",
    "isLoggedIn",
    "activeTasks",
    "recentRecords",
    "formData",
    "canSave",
    "canSubmit",
    "isSubmitting",
    "showContent",
    "showModal",
    "taskInfo",
    "videoId",
    "checkinNote",
    "checkinData",
    "childId",
    "campId",
    "taskId",
    "currentDate",
    "childAvatar",
    "calculatedAge",
    "progressPercentage",
    "rewardPoints",
    "willGetBadge",
    "nextBadge",
    "showHelpModal",
    "showShareOptions",
    "shareText",
    "navbarHeight",
    "currentTab",
    "mainProgress",
    "userStats",
    "medalsList",
    "growthTrack",
    "activeCamps",
    "comingSoonCamps",
  ],
};

// 检查结果
const results = {
  errors: [],
  warnings: [],
  totalFiles: 0,
  checkedFiles: 0,
};

/**
 * 检查字段命名是否符合snake_case规范
 */
function isValidSnakeCase(fieldName) {
  // 允许的驼峰命名字段
  if (CONFIG.allowedCamelCase.includes(fieldName)) {
    return true;
  }

  // 检查是否为snake_case
  const snakeCaseRegex = /^[a-z][a-z0-9_]*$/;
  return snakeCaseRegex.test(fieldName);
}

/**
 * 检查JavaScript文件中的字段命名
 */
function checkJavaScriptFile(filePath, content) {
  const lines = content.split("\n");

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // 检查JSON对象中的字段
    const jsonFieldRegex = /["']([a-zA-Z][a-zA-Z0-9_]*)["']\s*:/g;
    let match;

    while ((match = jsonFieldRegex.exec(line)) !== null) {
      const fieldName = match[1];

      if (!isValidSnakeCase(fieldName)) {
        results.errors.push({
          file: filePath,
          line: lineNumber,
          field: fieldName,
          message: `字段 "${fieldName}" 应使用snake_case命名`,
          suggestion: convertToSnakeCase(fieldName),
        });
      }
    }

    // 检查对象属性访问
    const propertyAccessRegex = /\.([a-zA-Z][a-zA-Z0-9]*)/g;
    while ((match = propertyAccessRegex.exec(line)) !== null) {
      const fieldName = match[1];

      // 跳过方法调用
      if (line.includes(`${fieldName}(`)) {
        continue;
      }

      if (
        !isValidSnakeCase(fieldName) &&
        !CONFIG.allowedCamelCase.includes(fieldName)
      ) {
        results.warnings.push({
          file: filePath,
          line: lineNumber,
          field: fieldName,
          message: `属性访问 "${fieldName}" 可能需要检查命名规范`,
          suggestion: convertToSnakeCase(fieldName),
        });
      }
    }
  });
}

/**
 * 检查YAML文件中的字段命名
 */
function checkYamlFile(filePath, content) {
  const lines = content.split("\n");

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // 检查YAML字段
    const yamlFieldRegex = /^\s*([a-zA-Z][a-zA-Z0-9_]*)\s*:/;
    const match = yamlFieldRegex.exec(line);

    if (match) {
      const fieldName = match[1];

      if (!isValidSnakeCase(fieldName)) {
        results.errors.push({
          file: filePath,
          line: lineNumber,
          field: fieldName,
          message: `YAML字段 "${fieldName}" 应使用snake_case命名`,
          suggestion: convertToSnakeCase(fieldName),
        });
      }
    }
  });
}

/**
 * 将驼峰命名转换为snake_case
 */
function convertToSnakeCase(str) {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

/**
 * 递归检查目录
 */
function checkDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }

  const items = fs.readdirSync(dirPath);

  items.forEach((item) => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!CONFIG.excludeDirs.includes(item)) {
        checkDirectory(itemPath);
      }
    } else if (stat.isFile()) {
      results.totalFiles++;

      const ext = path.extname(item);
      if (CONFIG.fileExtensions.includes(ext)) {
        checkFile(itemPath);
      }
    }
  });
}

/**
 * 检查单个文件
 */
function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const ext = path.extname(filePath);

    results.checkedFiles++;

    switch (ext) {
      case ".js":
        checkJavaScriptFile(filePath, content);
        break;
      case ".yaml":
      case ".yml":
        checkYamlFile(filePath, content);
        break;
      case ".json":
        // JSON文件通常已经是规范的，暂时跳过
        break;
    }
  } catch (error) {
    results.warnings.push({
      file: filePath,
      message: `读取文件失败: ${error.message}`,
    });
  }
}

/**
 * 输出检查结果
 */
function outputResults() {
  console.log("\n🔍 字段命名规范检查结果\n");
  console.log(`📊 统计信息:`);
  console.log(`   总文件数: ${results.totalFiles}`);
  console.log(`   检查文件数: ${results.checkedFiles}`);
  console.log(`   错误数: ${results.errors.length}`);
  console.log(`   警告数: ${results.warnings.length}\n`);

  if (results.errors.length > 0) {
    console.log("❌ 错误列表:");
    results.errors.forEach((error) => {
      console.log(`   ${error.file}:${error.line} - ${error.message}`);
      if (error.suggestion) {
        console.log(`      建议: ${error.field} → ${error.suggestion}`);
      }
    });
    console.log("");
  }

  if (results.warnings.length > 0) {
    console.log("⚠️  警告列表:");
    results.warnings.forEach((warning) => {
      console.log(`   ${warning.file}:${warning.line} - ${warning.message}`);
      if (warning.suggestion) {
        console.log(`      建议: ${warning.field} → ${warning.suggestion}`);
      }
    });
    console.log("");
  }

  if (results.errors.length === 0 && results.warnings.length === 0) {
    console.log("✅ 所有检查的文件都符合命名规范！");
  }

  // 返回错误码
  process.exit(results.errors.length > 0 ? 1 : 0);
}

/**
 * 主函数
 */
function main() {
  console.log("🚀 开始检查字段命名规范...\n");

  // 检查指定目录
  CONFIG.checkDirs.forEach((dir) => {
    console.log(`📁 检查目录: ${dir}`);
    checkDirectory(dir);
  });

  // 输出结果
  outputResults();
}

// 运行检查
if (require.main === module) {
  main();
}

module.exports = {
  checkDirectory,
  checkFile,
  isValidSnakeCase,
  convertToSnakeCase,
};
