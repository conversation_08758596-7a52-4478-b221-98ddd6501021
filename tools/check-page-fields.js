#!/usr/bin/env node

/**
 * 检查页面文件中的数据字段命名规范
 * 专门针对前端页面代码中的字段命名问题
 */

const fs = require('fs');
const path = require('path');

// 需要检查的字段模式
const FIELD_PATTERNS = {
  // API请求数据字段
  apiData: /data:\s*{[^}]*([a-zA-Z][a-zA-Z0-9]*)\s*:/g,
  
  // 对象字段定义
  objectFields: /{\s*([a-zA-Z][a-zA-Z0-9]*)\s*:/g,
  
  // 赋值语句中的字段
  assignmentFields: /\.([a-zA-Z][a-zA-Z0-9]*)\s*=/g,
  
  // 访问字段
  accessFields: /\.([a-zA-Z][a-zA-Z0-9]*)/g
};

// 应该使用snake_case的字段（与后端API交互的数据字段）
const SHOULD_BE_SNAKE_CASE = [
  'userId', 'childId', 'campId', 'taskId', 'checkinId',
  'userName', 'realName', 'nickName', 'avatarUrl',
  'birthDate', 'createdAt', 'updatedAt', 'lastLoginAt',
  'currentChildId', 'userType', 'phoneNumber',
  'videoUrl', 'thumbnailUrl', 'imageUrl',
  'checkinType', 'checkinDate', 'exerciseDuration', 'exerciseCount',
  'likeCount', 'viewCount', 'commentCount',
  'isNewUser', 'isPublic', 'isLiked', 'isFavorited'
];

// 允许使用驼峰命名的字段（前端内部变量）
const ALLOWED_CAMEL_CASE = [
  'formData', 'canSave', 'canSubmit', 'isSubmitting',
  'showContent', 'showModal', 'currentChild', 'childrenList',
  'userInfo', 'isLoggedIn', 'activeTasks', 'recentRecords',
  'taskInfo', 'videoId', 'checkinNote', 'checkinData',
  'currentDate', 'childAvatar', 'calculatedAge',
  'progressPercentage', 'rewardPoints', 'willGetBadge',
  'nextBadge', 'showHelpModal', 'navbarHeight', 'currentTab'
];

const results = {
  errors: [],
  warnings: [],
  totalFiles: 0,
  checkedFiles: 0
};

/**
 * 转换为snake_case
 */
function toSnakeCase(str) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * 检查字段命名
 */
function checkFieldNaming(fieldName, filePath, lineNumber, context) {
  // 跳过允许的驼峰命名
  if (ALLOWED_CAMEL_CASE.includes(fieldName)) {
    return;
  }
  
  // 检查是否应该使用snake_case
  if (SHOULD_BE_SNAKE_CASE.includes(fieldName)) {
    results.errors.push({
      file: filePath,
      line: lineNumber,
      field: fieldName,
      context: context,
      message: `字段 "${fieldName}" 应使用snake_case命名`,
      suggestion: toSnakeCase(fieldName)
    });
  }
  
  // 检查是否为驼峰命名但不在允许列表中
  if (/^[a-z][a-zA-Z0-9]*[A-Z]/.test(fieldName) && !ALLOWED_CAMEL_CASE.includes(fieldName)) {
    results.warnings.push({
      file: filePath,
      line: lineNumber,
      field: fieldName,
      context: context,
      message: `字段 "${fieldName}" 可能需要检查命名规范`,
      suggestion: toSnakeCase(fieldName)
    });
  }
}

/**
 * 检查JavaScript文件
 */
function checkJavaScriptFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // 检查对象字段定义
      const objectFieldRegex = /["']?([a-zA-Z][a-zA-Z0-9_]*)["']?\s*:/g;
      let match;
      
      while ((match = objectFieldRegex.exec(line)) !== null) {
        const fieldName = match[1];
        
        // 跳过方法名和一些特殊情况
        if (line.includes('function') || line.includes('=>') || 
            line.includes('console.') || line.includes('wx.') ||
            line.includes('require(') || line.includes('import ')) {
          continue;
        }
        
        checkFieldNaming(fieldName, filePath, lineNumber, '对象字段定义');
      }
      
      // 检查属性访问
      const accessRegex = /\.([a-zA-Z][a-zA-Z0-9_]*)/g;
      while ((match = accessRegex.exec(line)) !== null) {
        const fieldName = match[1];
        
        // 跳过方法调用和一些特殊情况
        if (line.includes(`${fieldName}(`) || 
            line.includes('console.') || line.includes('wx.') ||
            fieldName === 'data' || fieldName === 'detail' || fieldName === 'value') {
          continue;
        }
        
        checkFieldNaming(fieldName, filePath, lineNumber, '属性访问');
      }
    });
    
    results.checkedFiles++;
  } catch (error) {
    results.warnings.push({
      file: filePath,
      message: `读取文件失败: ${error.message}`
    });
  }
}

/**
 * 递归检查目录
 */
function checkDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // 跳过demo目录
      if (item !== 'demo') {
        checkDirectory(itemPath);
      }
    } else if (stat.isFile() && item.endsWith('.js')) {
      results.totalFiles++;
      checkJavaScriptFile(itemPath);
    }
  });
}

/**
 * 输出结果
 */
function outputResults() {
  console.log('\n🔍 页面字段命名检查结果\n');
  console.log(`📊 统计信息:`);
  console.log(`   总文件数: ${results.totalFiles}`);
  console.log(`   检查文件数: ${results.checkedFiles}`);
  console.log(`   错误数: ${results.errors.length}`);
  console.log(`   警告数: ${results.warnings.length}\n`);
  
  if (results.errors.length > 0) {
    console.log('❌ 需要修正的字段命名:');
    results.errors.forEach(error => {
      console.log(`   ${error.file}:${error.line} - ${error.message}`);
      console.log(`      上下文: ${error.context}`);
      console.log(`      建议: ${error.field} → ${error.suggestion}\n`);
    });
  }
  
  if (results.warnings.length > 0) {
    console.log('⚠️  需要检查的字段命名:');
    results.warnings.forEach(warning => {
      console.log(`   ${warning.file}:${warning.line} - ${warning.message}`);
      if (warning.context) {
        console.log(`      上下文: ${warning.context}`);
      }
      if (warning.suggestion) {
        console.log(`      建议: ${warning.field} → ${warning.suggestion}`);
      }
      console.log('');
    });
  }
  
  if (results.errors.length === 0 && results.warnings.length === 0) {
    console.log('✅ 所有检查的页面文件字段命名都符合规范！');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始检查页面字段命名规范...\n');
  
  // 检查页面目录
  const pagesDir = 'miniprogram/pages';
  console.log(`📁 检查目录: ${pagesDir}`);
  checkDirectory(pagesDir);
  
  // 输出结果
  outputResults();
}

// 运行检查
if (require.main === module) {
  main();
}

module.exports = {
  checkDirectory,
  checkJavaScriptFile,
  toSnakeCase
};
