{
  // AI工作时的性能优化配置
  // 这些设置只在当前项目中生效，不影响其他项目
  
  // 禁用CPU密集型的编辑器功能
  "editor.quickSuggestions": {
    "comments": "off",
    "strings": "off",
    "other": "off"
  },
  "editor.suggestOnTriggerCharacters": false,
  "editor.parameterHints.enabled": false,
  "editor.hover.enabled": false,
  "editor.lightbulb.enabled": "off",
  "editor.codeLens": false,
  "editor.inlayHints.enabled": "off",
  "editor.semanticHighlighting.enabled": false,
  "editor.minimap.enabled": false,
  "breadcrumbs.enabled": false,
  
  // 开启Go开发支持
  "problems.decorations.enabled": true,
  "typescript.validate.enable": false,
  "javascript.validate.enable": false,
  "go.lintOnSave": "package",
  "go.formatOnSave": true,
  "go.buildOnSave": "package",
  
  // 减少文件监控范围
  "files.watcherExclude": {
    "**/.git/**": true,
    "**/node_modules/**": true,
    "**/api/internal/**": true,
    "**/api/tools/**": true,
    "**/api/tests/**": true,
    "**/docs/**": true,
    "**/vendor/**": true,
    "**/tmp/**": true,
    "**/miniprogram/**": true,
    "**/.next/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/coverage/**": true
  },
  
  // 开启Go文件的自动格式化
  "files.autoSave": "off",
  "editor.formatOnSave": true,
  "editor.formatOnType": false,
  "editor.formatOnPaste": false,
  
  // 开启Git集成和提示
  "git.enabled": true,
  "git.autorefresh": true,
  "git.autoRepositoryDetection": true,
  "git.decorations.enabled": true,
  "git.autofetch": true,
  "git.showProgress": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "git.suggestSmartCommit": true,
  "git.showInlineOpenFileAction": true,
  "git.showPushSuccessNotification": true,

  // Git增强配置
  "git.openRepositoryInParentFolders": "always",
  "git.detectSubmodules": true,
  "git.ignoreLimitWarning": true,
  "git.fetchOnPull": true,
  "git.pruneOnFetch": true,
  "git.pullBeforeCheckout": true,
  "git.showActionButton": {
    "commit": true,
    "publish": true,
    "sync": true
  },

  // 源代码管理视图配置
  "scm.diffDecorations": "all",
  "scm.diffDecorationsGutterVisibility": "always",
  "scm.diffDecorationsIgnoreTrimWhitespace": "true",
  "scm.alwaysShowActions": true,
  "scm.alwaysShowRepositories": true,
  "scm.showChangesSummary": true,

  // 搜索优化
  "search.followSymlinks": false,
  "search.useGlobalIgnoreFiles": true,
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.git": true,
    "**/api/internal": true,
    "**/api/tools": true,
    "**/docs": true
  },
  
  // 禁用其他CPU密集型功能
  "extensions.autoUpdate": false,
  "extensions.autoCheckUpdates": false,
  "typescript.surveys.enabled": false,
  "telemetry.telemetryLevel": "off",

  // 渲染优化
  "editor.renderWhitespace": "none",
  "editor.renderControlCharacters": false,
  "editor.renderLineHighlight": "none",
  "editor.occurrencesHighlight": "off",
  "editor.selectionHighlight": false,
  "editor.wordHighlight": false,
  
  // 终端优化
  "terminal.integrated.enablePersistentSessions": false,
  "terminal.integrated.localEchoLatencyThreshold": -1,

  // 更激进的性能优化
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "off",
  "editor.quickSuggestionsDelay": 500,
  "editor.suggest.showWords": false,
  "editor.suggest.showSnippets": false,
  "editor.suggest.showKeywords": false,
  "editor.wordBasedSuggestions": "off",
  "editor.wordBasedSuggestionsMode": "currentDocument",

  // 开启Go语言服务器
  "typescript.disableAutomaticTypeAcquisition": true,
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "javascript.preferences.includePackageJsonAutoImports": "off",
  "go.useLanguageServer": true,
  "go.alternateTools": {},

  // 禁用文件关联和自动检测
  "files.associations": {},
  "workbench.editorAssociations": {},

  // 完全禁用智能功能
  "editor.suggest.localityBonus": false,
  "editor.suggest.shareSuggestSelections": false,
  "editor.suggestSelection": "first",
  "editor.tabCompletion": "off",

  // 禁用所有装饰器
  "editor.glyphMargin": false,
  "editor.folding": false,
  "editor.showFoldingControls": "never",
  "editor.matchBrackets": "never",

  // 禁用工作台功能
  "workbench.enableExperiments": false,
  "workbench.settings.enableNaturalLanguageSearch": false,
  "workbench.startupEditor": "none",
  "workbench.tips.enabled": false
}
