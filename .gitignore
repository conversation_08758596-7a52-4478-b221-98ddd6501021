# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
/api/bin/
/api/api-server
/api/admin-server
/api/build/
/api/dist/
/admin/bin/
/admin/admin-server
/admin/admin-server
/admin/build/
/admin/dist/
# IDE files
# .vscode/ - 保留VSCode配置用于项目协作
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
/api/logs/
logs/

# Configuration files with sensitive data
/api/configs/config.local.yaml
/api/configs/config.production.yaml
/api/configs/.env
/api/configs/.env.local
/api/configs/.env.production
.env
.env.local
.env.production

# Database files
*.db
*.sqlite
*.sqlite3

# Upload directories
/api/uploads/
uploads/

# Test coverage
coverage.out
coverage.html
*.cover

# Temporary files
tmp/
temp/
*.tmp

# Documentation build artifacts
/docs/build/
/docs/_build/

# Node.js (if any frontend tools are used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package files
*.tar.gz
*.zip
*.rar

# Backup files
*.bak
*.backup

# Cache directories
.cache/
*.cache

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Go modules download cache
/api/go/pkg/mod/

# Air live reload tool
/api/tmp/

# Swagger generated files (if auto-generated)
/api/docs/swagger.json
/api/docs/swagger.yaml

# Test artifacts
/api/test-results/
test-results/

# Performance test results
*.prof
*.pprof

# Memory dumps
*.hprof
*.dump

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.swn

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local development certificates
*.pem
*.key
*.crt
*.cert

# Docker
.dockerignore

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Local scripts (if any)
local-*.sh
dev-*.sh

# Generated documentation
/api/docs/generated/

# Profiling data
cpu.prof
mem.prof
block.prof
mutex.prof

# Race condition detection
race.out

# Benchmarking results
bench.out

# Reference projects and demo code
reference/
