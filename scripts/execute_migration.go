package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"kids-platform/pkg/config"
	"kids-platform/pkg/database"

	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("=== 开始执行数据库迁移 ===")

	// 第一步：创建迁移日志表
	fmt.Println("1. 创建迁移日志表...")
	if err := createMigrationLogsTable(db); err != nil {
		log.Fatalf("Failed to create migration_logs table: %v", err)
	}

	// 第二步：检查当前表结构
	fmt.Println("2. 检查当前表结构...")
	if err := checkCurrentSchema(db); err != nil {
		log.Fatalf("Failed to check current schema: %v", err)
	}

	// 第三步：执行迁移脚本
	fmt.Println("3. 执行迁移脚本...")
	if err := executeMigration(db); err != nil {
		log.Fatalf("Failed to execute migration: %v", err)
	}

	// 第四步：验证迁移结果
	fmt.Println("4. 验证迁移结果...")
	if err := validateMigration(db); err != nil {
		log.Fatalf("Migration validation failed: %v", err)
	}

	fmt.Println("=== 数据库迁移完成 ===")
}

// createMigrationLogsTable 创建迁移日志表
func createMigrationLogsTable(db *gorm.DB) error {
	sqlFile := filepath.Join("..", "scripts", "create_migration_logs.sql")
	
	sqlContent, err := os.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read migration logs SQL file: %w", err)
	}

	if err := db.Exec(string(sqlContent)).Error; err != nil {
		return fmt.Errorf("failed to create migration_logs table: %w", err)
	}

	fmt.Println("   ✓ migration_logs 表创建成功")
	return nil
}

// checkCurrentSchema 检查当前表结构
func checkCurrentSchema(db *gorm.DB) error {
	// 检查 child_points 表
	var childPointsExists bool
	err := db.Raw("SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'child_points'").Scan(&childPointsExists).Error
	if err != nil {
		return fmt.Errorf("failed to check child_points table: %w", err)
	}

	if childPointsExists {
		fmt.Println("   ✓ child_points 表存在")
		
		// 检查是否已有 participation_id 字段
		var participationIdExists bool
		err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'child_points' AND column_name = 'participation_id'").Scan(&participationIdExists).Error
		if err != nil {
			return fmt.Errorf("failed to check participation_id column: %w", err)
		}
		
		if participationIdExists {
			fmt.Println("   ⚠️  participation_id 字段已存在，可能已经迁移过")
			return fmt.Errorf("migration may have already been executed")
		} else {
			fmt.Println("   → participation_id 字段不存在，需要迁移")
		}
	} else {
		return fmt.Errorf("child_points table does not exist")
	}

	// 检查 child_medals 表
	var childMedalsExists bool
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'child_medals'").Scan(&childMedalsExists).Error
	if err != nil {
		return fmt.Errorf("failed to check child_medals table: %w", err)
	}

	if childMedalsExists {
		fmt.Println("   ✓ child_medals 表存在")
		
		// 检查是否已有 participation_id 字段
		var participationIdExists bool
		err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'child_medals' AND column_name = 'participation_id'").Scan(&participationIdExists).Error
		if err != nil {
			return fmt.Errorf("failed to check participation_id column in child_medals: %w", err)
		}
		
		if participationIdExists {
			fmt.Println("   ⚠️  participation_id 字段已存在，可能已经迁移过")
			return fmt.Errorf("migration may have already been executed")
		} else {
			fmt.Println("   → participation_id 字段不存在，需要迁移")
		}
	} else {
		return fmt.Errorf("child_medals table does not exist")
	}

	// 检查 user_camp_participations 表
	var participationsExists bool
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'user_camp_participations'").Scan(&participationsExists).Error
	if err != nil {
		return fmt.Errorf("failed to check user_camp_participations table: %w", err)
	}

	if participationsExists {
		fmt.Println("   ✓ user_camp_participations 表存在")
	} else {
		return fmt.Errorf("user_camp_participations table does not exist")
	}

	return nil
}

// executeMigration 执行迁移脚本
func executeMigration(db *gorm.DB) error {
	sqlFile := filepath.Join("..", "docs", "database", "migrations", "final_clean_migration.sql")
	
	sqlContent, err := os.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read migration file: %w", err)
	}

	// 分割SQL语句
	statements := splitSQL(string(sqlContent))
	
	for i, stmt := range statements {
		stmt = trimStatement(stmt)
		if stmt == "" {
			continue
		}
		
		fmt.Printf("   执行语句 %d/%d...\n", i+1, len(statements))
		
		// 执行SQL语句
		if err := db.Exec(stmt).Error; err != nil {
			// 某些错误可以忽略（如表已存在、字段已存在等）
			if isIgnorableError(err.Error()) {
				fmt.Printf("   ⚠️  忽略错误: %v\n", err)
				continue
			}
			return fmt.Errorf("failed to execute statement %d: %w\nStatement: %s", i+1, err, stmt)
		}
	}
	
	return nil
}

// validateMigration 验证迁移结果
func validateMigration(db *gorm.DB) error {
	// 验证 child_points 表结构
	fmt.Println("   验证 child_points 表结构...")
	
	var participationIdExists bool
	err := db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'child_points' AND column_name = 'participation_id'").Scan(&participationIdExists).Error
	if err != nil {
		return fmt.Errorf("failed to validate child_points.participation_id: %w", err)
	}
	
	if !participationIdExists {
		return fmt.Errorf("child_points.participation_id field not found after migration")
	}
	fmt.Println("     ✓ participation_id 字段存在")

	// 验证唯一约束
	var constraintExists bool
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'child_points' AND index_name = 'uk_participation_id'").Scan(&constraintExists).Error
	if err != nil {
		return fmt.Errorf("failed to validate unique constraint: %w", err)
	}
	
	if !constraintExists {
		return fmt.Errorf("unique constraint uk_participation_id not found after migration")
	}
	fmt.Println("     ✓ uk_participation_id 唯一约束存在")

	// 验证 child_medals 表结构
	fmt.Println("   验证 child_medals 表结构...")
	
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'child_medals' AND column_name = 'participation_id'").Scan(&participationIdExists).Error
	if err != nil {
		return fmt.Errorf("failed to validate child_medals.participation_id: %w", err)
	}
	
	if !participationIdExists {
		return fmt.Errorf("child_medals.participation_id field not found after migration")
	}
	fmt.Println("     ✓ participation_id 字段存在")

	// 验证唯一约束
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'child_medals' AND index_name = 'uk_participation_id_medal'").Scan(&constraintExists).Error
	if err != nil {
		return fmt.Errorf("failed to validate unique constraint: %w", err)
	}
	
	if !constraintExists {
		return fmt.Errorf("unique constraint uk_participation_id_medal not found after migration")
	}
	fmt.Println("     ✓ uk_participation_id_medal 唯一约束存在")

	// 验证数据一致性
	fmt.Println("   验证数据一致性...")
	
	var pointsCount, medalsCount int64
	
	err = db.Raw("SELECT COUNT(*) FROM child_points WHERE participation_id IS NOT NULL AND participation_id > 0").Scan(&pointsCount).Error
	if err != nil {
		return fmt.Errorf("failed to count child_points records: %w", err)
	}
	fmt.Printf("     ✓ child_points 有效记录数: %d\n", pointsCount)

	err = db.Raw("SELECT COUNT(*) FROM child_medals WHERE participation_id IS NOT NULL AND participation_id > 0").Scan(&medalsCount).Error
	if err != nil {
		return fmt.Errorf("failed to count child_medals records: %w", err)
	}
	fmt.Printf("     ✓ child_medals 有效记录数: %d\n", medalsCount)

	return nil
}

// splitSQL 分割SQL语句
func splitSQL(content string) []string {
	statements := []string{}
	current := ""
	
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// 跳过注释和空行
		if line == "" || strings.HasPrefix(line, "--") || strings.HasPrefix(line, "/*") {
			continue
		}
		
		current += line + "\n"
		
		// 如果行以分号结尾，认为是一个完整的语句
		if strings.HasSuffix(line, ";") {
			statements = append(statements, current)
			current = ""
		}
	}
	
	// 添加最后一个语句（如果有）
	if strings.TrimSpace(current) != "" {
		statements = append(statements, current)
	}
	
	return statements
}

// trimStatement 清理SQL语句
func trimStatement(stmt string) string {
	stmt = strings.TrimSpace(stmt)
	stmt = strings.TrimSuffix(stmt, ";")
	stmt = strings.TrimSpace(stmt)
	return stmt
}

// isIgnorableError 判断是否为可忽略的错误
func isIgnorableError(errMsg string) bool {
	ignorableErrors := []string{
		"Duplicate column name",
		"Duplicate key name",
		"Table already exists",
		"Column already exists",
		"Can't DROP",
		"doesn't exist",
	}
	
	for _, ignorable := range ignorableErrors {
		if strings.Contains(errMsg, ignorable) {
			return true
		}
	}
	
	return false
}
