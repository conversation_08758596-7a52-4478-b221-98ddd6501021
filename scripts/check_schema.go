package main

import (
	"fmt"
	"log"

	"kids-platform/pkg/config"
	"kids-platform/pkg/database"

	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("=== 检查当前数据库表结构 ===")

	// 检查 child_points 表结构
	fmt.Println("\n1. child_points 表结构:")
	if err := checkTableStructure(db, "child_points"); err != nil {
		log.Printf("Error checking child_points: %v", err)
	}

	// 检查 child_medals 表结构
	fmt.Println("\n2. child_medals 表结构:")
	if err := checkTableStructure(db, "child_medals"); err != nil {
		log.Printf("Error checking child_medals: %v", err)
	}

	// 检查索引
	fmt.Println("\n3. 检查索引:")
	checkIndexes(db)

	// 检查数据
	fmt.Println("\n4. 检查数据:")
	checkData(db)
}

func checkTableStructure(db *gorm.DB, tableName string) error {
	type ColumnInfo struct {
		Field   string `gorm:"column:Field"`
		Type    string `gorm:"column:Type"`
		Null    string `gorm:"column:Null"`
		Key     string `gorm:"column:Key"`
		Default *string `gorm:"column:Default"`
		Extra   string `gorm:"column:Extra"`
	}

	var columns []ColumnInfo
	err := db.Raw(fmt.Sprintf("DESCRIBE %s", tableName)).Scan(&columns).Error
	if err != nil {
		return fmt.Errorf("failed to describe table %s: %w", tableName, err)
	}

	fmt.Printf("   表 %s 的字段:\n", tableName)
	for _, col := range columns {
		defaultVal := "NULL"
		if col.Default != nil {
			defaultVal = *col.Default
		}
		fmt.Printf("     %s | %s | %s | %s | %s | %s\n", 
			col.Field, col.Type, col.Null, col.Key, defaultVal, col.Extra)
	}

	return nil
}

func checkIndexes(db *gorm.DB) {
	// 检查 child_points 索引
	fmt.Println("   child_points 表索引:")
	var indexes []struct {
		Table      string `gorm:"column:Table"`
		NonUnique  int    `gorm:"column:Non_unique"`
		KeyName    string `gorm:"column:Key_name"`
		SeqInIndex int    `gorm:"column:Seq_in_index"`
		ColumnName string `gorm:"column:Column_name"`
		Collation  string `gorm:"column:Collation"`
		Cardinality int   `gorm:"column:Cardinality"`
		SubPart    *int   `gorm:"column:Sub_part"`
		Packed     *string `gorm:"column:Packed"`
		Null       string `gorm:"column:Null"`
		IndexType  string `gorm:"column:Index_type"`
		Comment    string `gorm:"column:Comment"`
	}

	err := db.Raw("SHOW INDEX FROM child_points").Scan(&indexes).Error
	if err != nil {
		log.Printf("Error checking child_points indexes: %v", err)
	} else {
		for _, idx := range indexes {
			fmt.Printf("     %s | %s | %s\n", idx.KeyName, idx.ColumnName, idx.IndexType)
		}
	}

	// 检查 child_medals 索引
	fmt.Println("   child_medals 表索引:")
	err = db.Raw("SHOW INDEX FROM child_medals").Scan(&indexes).Error
	if err != nil {
		log.Printf("Error checking child_medals indexes: %v", err)
	} else {
		for _, idx := range indexes {
			fmt.Printf("     %s | %s | %s\n", idx.KeyName, idx.ColumnName, idx.IndexType)
		}
	}
}

func checkData(db *gorm.DB) {
	// 检查 child_points 数据
	var pointsCount int64
	err := db.Raw("SELECT COUNT(*) FROM child_points").Scan(&pointsCount).Error
	if err != nil {
		log.Printf("Error counting child_points: %v", err)
	} else {
		fmt.Printf("   child_points 总记录数: %d\n", pointsCount)
	}

	var pointsWithParticipation int64
	err = db.Raw("SELECT COUNT(*) FROM child_points WHERE participation_id IS NOT NULL AND participation_id > 0").Scan(&pointsWithParticipation).Error
	if err != nil {
		log.Printf("Error counting child_points with participation_id: %v", err)
	} else {
		fmt.Printf("   child_points 有 participation_id 的记录数: %d\n", pointsWithParticipation)
	}

	// 检查 child_medals 数据
	var medalsCount int64
	err = db.Raw("SELECT COUNT(*) FROM child_medals").Scan(&medalsCount).Error
	if err != nil {
		log.Printf("Error counting child_medals: %v", err)
	} else {
		fmt.Printf("   child_medals 总记录数: %d\n", medalsCount)
	}

	var medalsWithParticipation int64
	err = db.Raw("SELECT COUNT(*) FROM child_medals WHERE participation_id IS NOT NULL AND participation_id > 0").Scan(&medalsWithParticipation).Error
	if err != nil {
		log.Printf("Error counting child_medals with participation_id: %v", err)
	} else {
		fmt.Printf("   child_medals 有 participation_id 的记录数: %d\n", medalsWithParticipation)
	}

	// 检查 user_camp_participations 数据
	var participationsCount int64
	err = db.Raw("SELECT COUNT(*) FROM user_camp_participations").Scan(&participationsCount).Error
	if err != nil {
		log.Printf("Error counting user_camp_participations: %v", err)
	} else {
		fmt.Printf("   user_camp_participations 总记录数: %d\n", participationsCount)
	}
}
