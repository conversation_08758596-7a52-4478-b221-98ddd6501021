# 跳跳星球 Makefile

# 服务配置
SERVICE ?= all
VALID_SERVICES := api admin all

.PHONY: help build start stop restart test clean deps fmt vet docs test-api test-unit test-integration test-coverage test-race bench status logs health ps

# 验证服务名称
define validate_service
	@if [ "$(SERVICE)" != "all" ] && [ "$(SERVICE)" != "api" ] && [ "$(SERVICE)" != "admin" ]; then \
		echo "❌ 无效的服务名称: $(SERVICE)"; \
		echo "✅ 有效的服务名称: api, admin, all"; \
		exit 1; \
	fi
endef

# 默认目标
help:
	@echo "🌟 跳跳星球 - 可用命令："
	@echo ""
	@echo "📦 构建和依赖："
	@echo "  build [SERVICE=api|admin|all]     - 构建项目"
	@echo "  deps                              - 安装依赖"
	@echo "  clean                             - 清理构建文件"
	@echo ""
	@echo "🚀 服务管理："
	@echo "  start [SERVICE=api|admin|all]     - 启动服务"
	@echo "  stop [SERVICE=api|admin|all]      - 停止服务"
	@echo "  restart [SERVICE=api|admin|all]   - 重启服务"
	@echo "  status [SERVICE=api|admin|all]    - 查看服务状态"
	@echo "  health [SERVICE=api|admin|all]    - 健康检查"
	@echo "  logs [SERVICE=api|admin]          - 查看服务日志"
	@echo "  ps                                - 显示进程信息"
	@echo ""
	@echo "🧪 测试："
	@echo "  test [SERVICE=api|admin|all]      - 运行所有测试"
	@echo "  test-unit [SERVICE=api|admin|all] - 运行单元测试"
	@echo "  test-integration                  - 运行集成测试"
	@echo "  test-coverage                     - 运行测试并生成覆盖率报告"
	@echo "  test-race                         - 运行竞态检测测试"
	@echo "  test-api                          - 运行API集成测试"
	@echo "  bench                             - 运行基准测试"
	@echo ""
	@echo "📚 文档和代码质量："
	@echo "  docs                              - 生成Swagger文档"
	@echo "  fmt                               - 格式化代码"
	@echo "  vet                               - 代码检查"
	@echo ""
	@echo "💡 使用示例："
	@echo "  make start                        - 启动所有服务"
	@echo "  make start SERVICE=api            - 只启动API服务"
	@echo "  make stop SERVICE=admin           - 只停止Admin服务"
	@echo "  make status                       - 查看所有服务状态"
	@echo "  make logs SERVICE=api             - 查看API服务日志"

# 构建项目
build:
	$(call validate_service)
	@echo "🔨 构建项目 (SERVICE=$(SERVICE))..."
	@./scripts/build-enhanced.sh $(SERVICE)

# 启动服务
start:
	$(call validate_service)
	@echo "🚀 启动服务 (SERVICE=$(SERVICE))..."
	@./scripts/start-enhanced.sh $(SERVICE)

# 停止服务
stop:
	$(call validate_service)
	@echo "🛑 停止服务 (SERVICE=$(SERVICE))..."
	@./scripts/stop-enhanced.sh $(SERVICE)

# 重启服务
restart:
	$(call validate_service)
	@echo "🔄 重启服务 (SERVICE=$(SERVICE))..."
	@./scripts/stop-enhanced.sh $(SERVICE)
	@sleep 2
	@./scripts/start-enhanced.sh $(SERVICE)

# 查看服务状态
status:
	$(call validate_service)
	@echo "📊 查看服务状态 (SERVICE=$(SERVICE))..."
	@./scripts/status.sh $(SERVICE)

# 查看服务日志
logs:
	@if [ "$(SERVICE)" = "all" ]; then \
		echo "❌ logs命令需要指定具体服务名称"; \
		echo "✅ 使用方法: make logs SERVICE=api 或 make logs SERVICE=admin"; \
		exit 1; \
	fi
	$(call validate_service)
	@echo "📋 查看服务日志 (SERVICE=$(SERVICE))..."
	@./scripts/logs.sh $(SERVICE)

# 健康检查
health:
	$(call validate_service)
	@echo "🏥 健康检查 (SERVICE=$(SERVICE))..."
	@./scripts/health.sh $(SERVICE)

# 显示进程信息
ps:
	@echo "🔍 显示进程信息..."
	@./scripts/ps.sh

# 运行所有测试
test:
	$(call validate_service)
	@echo "🧪 运行所有测试 (SERVICE=$(SERVICE))..."
	@./scripts/test-enhanced.sh $(SERVICE) all

# 运行单元测试
test-unit:
	$(call validate_service)
	@echo "🧪 运行单元测试 (SERVICE=$(SERVICE))..."
	@./scripts/test-enhanced.sh $(SERVICE) unit

# 运行集成测试
test-integration:
	@echo "🧪 运行集成测试..."
	@go test ./... -v -run Integration

# 运行测试并生成覆盖率报告
test-coverage:
	@echo "🧪 运行测试覆盖率..."
	@mkdir -p coverage
	@go test ./... -v -coverprofile=coverage/coverage.out
	@go tool cover -html=coverage/coverage.out -o coverage/coverage.html
	@echo "✅ 覆盖率报告生成: coverage/coverage.html"

# 运行竞态检测测试
test-race:
	@echo "🧪 运行竞态检测测试..."
	@go test ./... -v -race

# 运行基准测试
bench:
	@echo "🧪 运行基准测试..."
	@go test ./... -v -bench=. -benchmem

# 运行API集成测试
test-api:
	@echo "🧪 运行API集成测试..."
	@./scripts/tests/run_all_tests.sh

# 生成Swagger文档
docs:
	@echo "📚 生成Swagger文档..."
	@if command -v swag >/dev/null 2>&1; then \
		swag init -g cmd/api-server/main.go -o docs; \
		echo "✅ Swagger文档生成成功!"; \
	else \
		echo "⚠️  swag命令未找到，使用手动文档"; \
		echo "安装命令: go install github.com/swaggo/swag/cmd/swag@latest"; \
	fi

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf bin/
	@rm -rf logs/
	@echo "✅ 清理完成"

# 安装依赖
deps:
	@echo "📦 安装依赖..."
	@go mod tidy
	@go mod download
	@echo "✅ 依赖安装完成"

# 格式化代码
fmt:
	@echo "📝 格式化代码..."
	@go fmt ./...
	@echo "✅ 代码格式化完成"

# 代码检查
vet:
	@echo "🔍 代码检查..."
	@go vet ./...
	@echo "✅ 代码检查完成"

# 开发模式（热重载）
dev:
	@echo "🔥 开发模式启动..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "请先安装 air: go install github.com/cosmtrek/air@latest"; \
	fi

# 生成代码
generate:
	@echo "⚡ 生成代码..."
	@go generate ./...
	@echo "✅ 代码生成完成"
