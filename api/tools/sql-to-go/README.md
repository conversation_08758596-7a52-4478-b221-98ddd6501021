# SQL-to-Go 代码生成工具

自动从数据库表结构生成Go模型代码的工具，支持单文件和分文件两种架构模式。

## 🚀 功能特性

- ✅ **单文件架构**：Model + Request + Response + Converter 合并在一个文件中（推荐）
- ✅ **分文件架构**：传统的分文件夹结构（向后兼容）
- ✅ **数据库驱动**：直接连接MySQL数据库读取真实表结构
- ✅ **智能字段处理**：自动识别基础字段、自动字段、隐藏字段
- ✅ **完整验证标签**：自动生成 binding 和 validate 标签
- ✅ **Repository生成**：自动生成数据访问层代码
- ✅ **类型安全**：完整的类型转换和空值处理

## 📦 安装使用

### 前置条件

- Go 1.21+
- MySQL数据库
- 配置文件 `configs/config.yaml`

### 基本用法

```bash
# 生成单文件结构（推荐）
go run tools/sql-to-go/main.go --table video_categories --type all

# 只生成单文件模型
go run tools/sql-to-go/main.go --table video_categories --type single

# 生成分文件结构（向后兼容）
go run tools/sql-to-go/main.go --table video_categories --type all-separate

# 生成所有表
go run tools/sql-to-go/main.go --type all

# 使用自定义配置文件
go run tools/sql-to-go/main.go --config configs/config.yaml

# 指定输出目录
go run tools/sql-to-go/main.go --table users --output ./generated
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--table` | string | "" | 指定表名（为空则生成所有表） |
| `--type` | string | "all" | 生成类型：all(单文件),single(仅单文件模型),all-separate(分文件),model,request,response,repository |
| `--config` | string | "configs/config.yaml" | 配置文件路径 |
| `--output` | string | "" | 输出目录（为空则使用默认目录） |

### 生成类型说明

| 类型 | 说明 | 生成内容 |
|------|------|----------|
| `all` | 单文件结构（推荐） | 单文件模型 + Repository |
| `single` | 仅单文件模型 | 仅生成单文件模型 |
| `all-separate` | 分文件结构 | Model + Request + Response + Converter + Repository |
| `model` | 仅基础模型 | 仅生成基础模型文件 |
| `request` | 仅请求结构 | 仅生成请求结构文件 |
| `response` | 仅响应结构 | 仅生成响应结构文件 |
| `repository` | 仅Repository | 仅生成Repository文件 |

## 📁 生成文件结构

### 单文件结构（推荐）

```
internal/
├── models/
│   └── video_category.go          # Model + Request + Response + Converter
└── repositories/
    └── video_category_repository.go # Repository接口和实现
```

### 分文件结构（向后兼容）

```
internal/
├── models/
│   ├── video_category.go          # 基础模型
│   ├── requests/
│   │   └── video_category_request.go  # 请求结构
│   ├── dto/
│   │   └── video_category_response.go # 响应结构
│   └── converters/
│       └── video_category_converter.go # 转换器
└── repositories/
    └── video_category_repository.go   # Repository
```

## 🔧 配置文件

工具从 `configs/config.yaml` 读取数据库配置：

```yaml
database:
  driver: mysql
  host: localhost
  port: 3306
  username: root
  password: password
  database: your_database
  charset: utf8mb4
```

## 📝 模板系统

工具使用4个独立的模板文件：

### 1. base_model.tmpl - 基础模型
生成包含BaseModel的Go结构体，自动跳过id字段避免冲突。

### 2. request_model.tmpl - 请求结构
生成CreateRequest和UpdateRequest结构体，包含验证标签。

### 3. response_model.tmpl - 响应结构
生成Response结构体和ToResponse转换方法。

### 4. repository.tmpl - Repository
生成Repository接口和实现，包含标准CRUD操作。

## 🎯 生成示例

### 单文件模型示例 (video_category.go)

```go
package models

import (
    "time"
    "gorm.io/gorm"
)

// ==================== Model ====================

// VideoCategory 视频分类表：管理视频内容的分类体系
type VideoCategory struct {
    BaseModel
    Name        string `json:"name" gorm:"column:name;type:varchar(50);not null"`
    Description string `json:"description" gorm:"column:description;type:varchar(200)"`
    CoverImage  string `json:"cover_image" gorm:"column:cover_image;type:varchar(500)"`
    ParentID    uint   `json:"parent_id" gorm:"column:parent_id;type:int unsigned"`
    SortOrder   uint   `json:"sort_order" gorm:"column:sort_order;type:int unsigned"`
    Status      uint8  `json:"status" gorm:"column:status;type:tinyint unsigned;default:1"`
}

// TableName 指定表名
func (VideoCategory) TableName() string {
    return "video_categories"
}

// ==================== Requests ====================

// VideoCategoryCreateRequest 创建视频分类请求
type VideoCategoryCreateRequest struct {
    Name        string `json:"name" binding:"required,max=50"`
    Description string `json:"description" binding:"max=200"`
    CoverImage  string `json:"cover_image" binding:"max=500"`
    ParentID    uint   `json:"parent_id"`
    SortOrder   uint   `json:"sort_order"`
    Status      uint8  `json:"status" binding:"oneof=1 2"`
}

// VideoCategoryUpdateRequest 更新视频分类请求
type VideoCategoryUpdateRequest struct {
    Name        *string `json:"name,omitempty" binding:"omitempty,max=50"`
    Description *string `json:"description,omitempty" binding:"omitempty,max=200"`
    CoverImage  *string `json:"cover_image,omitempty" binding:"omitempty,max=500"`
    ParentID    *uint   `json:"parent_id,omitempty"`
    SortOrder   *uint   `json:"sort_order,omitempty"`
    Status      *uint8  `json:"status,omitempty" binding:"omitempty,oneof=1 2"`
}

// ==================== Responses ====================

// VideoCategoryResponse 视频分类响应
type VideoCategoryResponse struct {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    CoverImage  string    `json:"cover_image"`
    ParentID    uint      `json:"parent_id"`
    SortOrder   uint      `json:"sort_order"`
    Status      uint8     `json:"status"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}

// VideoCategoryListResponse 视频分类列表响应
type VideoCategoryListResponse struct {
    List  []*VideoCategoryResponse `json:"list"`
    Total int64                    `json:"total"`
}

// ==================== Converters ====================

// ToResponse 将视频分类模型转换为响应结构
func (m *VideoCategory) ToResponse() *VideoCategoryResponse {
    return &VideoCategoryResponse{
        ID:          m.ID,
        Name:        m.Name,
        Description: m.Description,
        CoverImage:  m.CoverImage,
        ParentID:    m.ParentID,
        SortOrder:   m.SortOrder,
        Status:      m.Status,
        CreatedAt:   m.CreatedAt,
        UpdatedAt:   m.UpdatedAt,
    }
}

// ApplyUpdateRequest 应用更新请求到视频分类模型
func (m *VideoCategory) ApplyUpdateRequest(req *VideoCategoryUpdateRequest) {
    if req.Name != nil {
        m.Name = *req.Name
    }
    if req.Description != nil {
        m.Description = *req.Description
    }
    if req.CoverImage != nil {
        m.CoverImage = *req.CoverImage
    }
    if req.ParentID != nil {
        m.ParentID = *req.ParentID
    }
    if req.SortOrder != nil {
        m.SortOrder = *req.SortOrder
    }
    if req.Status != nil {
        m.Status = *req.Status
    }
}
```

## 🔄 类型映射

| MySQL类型 | Go类型 |
|-----------|--------|
| TINYINT | int8 |
| SMALLINT | int16 |
| INT/MEDIUMINT | int |
| BIGINT | int64 |
| VARCHAR/TEXT | string |
| TIMESTAMP/DATETIME | time.Time |
| FLOAT | float32 |
| DOUBLE/DECIMAL | float64 |

## 🎯 字段处理规则

### 字段类型识别

- **基础字段**：`id`, `created_at`, `updated_at`, `deleted_at`（来自BaseModel）
- **自动字段**：`id`, `created_at`, `updated_at`, `deleted_at`（不需要用户输入）
- **隐藏字段**：`deleted_at`（不在响应中显示）

### 验证标签生成

- **必填字段**：`NOT NULL` 且非时间戳字段自动添加 `required` 标签
- **长度限制**：根据数据库字段长度自动添加 `max` 标签
- **类型验证**：根据字段类型添加相应验证规则

### GORM标签生成
- `column:name` - 数据库列名
- `type:varchar(50)` - 数据库类型
- `not null` - 非空约束
- `default:value` - 默认值
- `primaryKey` - 主键
- `autoIncrement` - 自增

### 自动包导入
工具会自动检测字段类型并导入所需的包：
- 包含 `time.Time` 字段时自动导入 `time` 包
- 包含软删除字段时自动导入 `gorm.io/gorm` 包

## 🔧 高级用法

### 批量生成

```bash
# 生成指定的多个表
go run tools/sql-to-go/main.go --table "users,children,video_categories" --type all

# 生成所有表
go run tools/sql-to-go/main.go --type all
```

### 自定义输出目录

```bash
# 输出到指定目录
go run tools/sql-to-go/main.go --table users --type all --output ./generated
```

### 混合使用

```bash
# 为新功能生成单文件结构
go run tools/sql-to-go/main.go --table new_feature --type single

# 为现有功能生成Repository
go run tools/sql-to-go/main.go --table existing_table --type repository
```

## 📋 最佳实践

1. **推荐使用单文件结构**：`--type all` 提供最佳的开发体验
2. **保持数据库设计规范**：使用标准的字段命名和类型
3. **及时更新模型**：数据库结构变更后重新生成代码
4. **版本控制**：将生成的代码纳入版本控制
5. **测试验证**：生成代码后运行测试确保功能正常

## 🚨 注意事项

1. **BaseModel冲突**：工具会自动跳过数据库中的`id`字段，使用BaseModel中的ID
2. **时间字段**：`created_at`和`updated_at`字段会被标记为时间戳字段
3. **配置文件**：确保数据库配置正确，工具需要连接数据库读取表结构
4. **模板路径**：模板文件需要放在正确的路径下（tools/generators/templates/）
5. **文件覆盖**：工具会覆盖现有文件，请注意备份

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**：检查配置文件中的数据库连接信息
2. **表不存在**：确认表名拼写正确，表确实存在于数据库中
3. **权限不足**：确保数据库用户有读取表结构的权限
4. **文件已存在**：工具会覆盖现有文件，请注意备份

### 调试技巧

```bash
# 查看详细错误信息
go run tools/sql-to-go/main.go --table users --type all 2>&1

# 检查生成的文件
ls -la internal/models/
ls -la internal/repositories/
```

## 🎉 总结

SQL-to-Go工具现在完全支持现代化的单文件架构，提供：

- 🎯 **高效开发**：一个文件包含所有相关代码
- 🛡️ **类型安全**：完整的类型检查和转换
- 🚀 **快速迭代**：自动生成减少重复工作
- 🔄 **向后兼容**：支持传统分文件结构

立即开始使用新的单文件架构，提升您的开发效率！

## 📄 许可证

本项目采用 MIT 许可证。
