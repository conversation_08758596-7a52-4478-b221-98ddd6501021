package main

import (
	"bufio"
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"text/template"

	_ "github.com/go-sql-driver/mysql"
	"gopkg.in/yaml.v3"
)

// Config 配置结构
type Config struct {
	Database struct {
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Database string `yaml:"database"`
	} `yaml:"database"`
}

// Field 字段定义
type Field struct {
	Name          string
	Type          string
	GoType        string
	GoName        string
	JsonName      string
	JsonTag       string
	GormTag       string
	Comment       string
	Required      bool
	Validation    string
	ValidateTag   string
	BindingTag    string
	IsPrimaryKey  bool
	IsTimestamp   bool
	IsBaseField   bool
	IsAutoField   bool
	IsHiddenField bool
	RequestType   string
	ResponseType  string
}

// Table 表定义
type Table struct {
	Name                string
	GoName              string
	VarName             string // Go变量名（小驼峰）
	FileName            string // 文件名（snake_case）
	Comment             string
	Fields              []Field
	PrimaryKey          string
	HasCustomPrimaryKey bool
	HasDeletedAt        bool
}

// SQL类型到Go类型的映射
var typeMapping = map[string]string{
	"BIGINT":            "int64",
	"BIGINT UNSIGNED":   "uint64",
	"INT":               "int",
	"INT UNSIGNED":      "uint",
	"TINYINT":           "int8",
	"TINYINT UNSIGNED":  "uint8",
	"SMALLINT":          "int16",
	"SMALLINT UNSIGNED": "uint16",
	"VARCHAR":           "string",
	"TEXT":              "string",
	"LONGTEXT":          "string",
	"CHAR":              "string",
	"DATE":              "*time.Time",
	"DATETIME":          "*time.Time",
	"TIMESTAMP":         "*time.Time",
	"TIME":              "string",
	"DECIMAL":           "float64",
	"FLOAT":             "float32",
	"DOUBLE":            "float64",
	"BOOLEAN":           "bool",
	"JSON":              "string",
}

func main() {
	// 命令行参数
	var (
		configFile = flag.String("config", "configs/config.yaml", "配置文件路径")
		tableName  = flag.String("table", "", "指定表名（支持逗号分隔多个表名，为空则生成所有表）")
		genType    = flag.String("type", "all", "生成类型：all(单文件),single(仅单文件模型),all-separate(分文件),model,request,response,repository")
		outputDir  = flag.String("output", "", "输出目录（为空则使用默认目录）")
		force      = flag.Bool("force", false, "强制覆盖已存在的文件（默认自动跳过已存在的文件）")
	)
	flag.Parse()

	// 读取配置文件
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	// 连接数据库
	db, err := connectDatabase(config)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 获取表信息
	var tables []Table
	if *tableName != "" {
		// 支持多个表名，逗号分隔
		tableNames := strings.Split(*tableName, ",")
		for _, name := range tableNames {
			name = strings.TrimSpace(name)
			if name == "" {
				continue
			}
			table, err := getTableInfo(db, name)
			if err != nil {
				log.Fatalf("获取表信息失败 %s: %v", name, err)
			}
			tables = append(tables, *table)
		}
	} else {
		// 生成所有表
		tables, err = getAllTables(db)
		if err != nil {
			log.Fatalf("获取所有表信息失败: %v", err)
		}
	}

	// 生成代码
	for _, table := range tables {
		if err := generateCode(table, *genType, *outputDir, *force); err != nil {
			fmt.Printf("❌ 生成失败 %s: %v\n", table.Name, err)
			continue
		}
		fmt.Printf("✅ 生成完成: %s\n", table.Name)
	}
}

// loadConfig 加载配置文件
func loadConfig(configFile string) (*Config, error) {
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

// connectDatabase 连接数据库
func connectDatabase(config *Config) (*sql.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Database,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return db, nil
}

// getAllTables 获取所有表信息
func getAllTables(db *sql.DB) ([]Table, error) {
	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		return nil, fmt.Errorf("查询表列表失败: %w", err)
	}
	defer rows.Close()

	var tables []Table
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}

		table, err := getTableInfo(db, tableName)
		if err != nil {
			fmt.Printf("⚠️  跳过表 %s: %v\n", tableName, err)
			continue
		}

		tables = append(tables, *table)
	}

	return tables, nil
}

// getTableInfo 获取单个表信息
func getTableInfo(db *sql.DB, tableName string) (*Table, error) {
	// 获取表注释
	var tableComment string
	err := db.QueryRow(`
		SELECT TABLE_COMMENT
		FROM information_schema.TABLES
		WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
	`, tableName).Scan(&tableComment)
	if err != nil {
		tableComment = ""
	}

	// 获取字段信息
	rows, err := db.Query(`
		SELECT
			COLUMN_NAME,
			DATA_TYPE,
			IS_NULLABLE,
			COLUMN_DEFAULT,
			COLUMN_COMMENT,
			COLUMN_KEY,
			EXTRA,
			CHARACTER_MAXIMUM_LENGTH,
			NUMERIC_PRECISION,
			NUMERIC_SCALE
		FROM information_schema.COLUMNS
		WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
		ORDER BY ORDINAL_POSITION
	`, tableName)
	if err != nil {
		return nil, fmt.Errorf("查询表字段失败: %w", err)
	}
	defer rows.Close()

	var fields []Field
	var primaryKey string

	for rows.Next() {
		var (
			columnName, dataType, isNullable, columnKey, extra, columnComment string
			columnDefault, maxLength, precision, scale                        sql.NullString
		)

		err := rows.Scan(&columnName, &dataType, &isNullable, &columnDefault,
			&columnComment, &columnKey, &extra, &maxLength, &precision, &scale)
		if err != nil {
			continue
		}

		field := Field{
			Name:         columnName,
			Type:         dataType,
			Comment:      columnComment,
			IsPrimaryKey: columnKey == "PRI",
			IsTimestamp:  columnName == "created_at" || columnName == "updated_at",
		}

		// 设置主键
		if field.IsPrimaryKey {
			primaryKey = columnName
		}

		// 转换Go类型
		field.GoType = convertToGoType(dataType, maxLength, precision, scale)
		field.GoName = toCamelCase(columnName)
		field.JsonName = columnName // 保持与数据库字段名一致
		field.JsonTag = columnName  // 保持与数据库字段名一致

		// 设置GORM标签
		field.GormTag = buildGormTagFromField(field, isNullable, columnDefault, maxLength)

		// 设置验证规则
		field.Required = isNullable == "NO" && !field.IsTimestamp && columnName != "id"
		field.Validation = buildValidationFromField(field, maxLength)
		field.ValidateTag = field.Validation
		field.BindingTag = buildBindingTag(field, isNullable, maxLength)

		// 设置字段类型标识
		field.IsBaseField = isBaseField(columnName)
		field.IsAutoField = isAutoField(columnName)
		field.IsHiddenField = isHiddenField(columnName)
		field.RequestType = getRequestType(field.GoType)
		field.ResponseType = getResponseType(field.GoType)

		fields = append(fields, field)
	}

	// 检查是否有deleted_at字段
	hasDeletedAt := false
	for _, field := range fields {
		if field.Name == "deleted_at" {
			hasDeletedAt = true
			break
		}
	}

	table := &Table{
		Name:                tableName,
		GoName:              toGoName(tableName),
		VarName:             toLowerCamelCase(tableName), // Go变量名（小驼峰）
		FileName:            tableName,                   // 文件名保持与数据库表名一致
		Comment:             tableComment,
		Fields:              fields,
		PrimaryKey:          primaryKey,
		HasCustomPrimaryKey: primaryKey != "" && primaryKey != "id",
		HasDeletedAt:        hasDeletedAt,
	}

	return table, nil
}

// generateCode 生成代码
func generateCode(table Table, genType, outputDir string, force bool) error {
	types := strings.Split(genType, ",")

	for _, t := range types {
		t = strings.TrimSpace(t)
		switch t {
		case "all":
			// 默认生成单文件结构
			if err := generateSingleFileModel(table, outputDir, force); err != nil {
				return err
			}
			if err := generateRepository(table, outputDir, force); err != nil {
				return err
			}
		case "all-separate":
			// 生成分文件结构（向后兼容）
			if err := generateModel(table, outputDir, force); err != nil {
				return err
			}
			if err := generateRequest(table, outputDir, force); err != nil {
				return err
			}
			if err := generateResponse(table, outputDir, force); err != nil {
				return err
			}
			if err := generateRepository(table, outputDir, force); err != nil {
				return err
			}
		case "single":
			// 只生成单文件模型
			if err := generateSingleFileModel(table, outputDir, force); err != nil {
				return err
			}
		case "model":
			if err := generateModel(table, outputDir, force); err != nil {
				return err
			}
		case "request":
			if err := generateRequest(table, outputDir, force); err != nil {
				return err
			}
		case "response":
			if err := generateResponse(table, outputDir, force); err != nil {
				return err
			}
		case "repository":
			if err := generateRepository(table, outputDir, force); err != nil {
				return err
			}
		}
	}

	return nil
}

// generateSingleFileModel 生成单文件模型（包含Model+Request+Response+Converter）
func generateSingleFileModel(table Table, outputDir string, force bool) error {
	return generateFromTemplate(table, "single_file_model.tmpl", getSingleFileModelPath(table, outputDir), force)
}

// generateModel 生成基础模型
func generateModel(table Table, outputDir string, force bool) error {
	return generateFromTemplate(table, "base_model.tmpl", getModelPath(table, outputDir), force)
}

// generateRequest 生成请求结构
func generateRequest(table Table, outputDir string, force bool) error {
	return generateFromTemplate(table, "request_model.tmpl", getRequestPath(table, outputDir), force)
}

// generateResponse 生成响应结构
func generateResponse(table Table, outputDir string, force bool) error {
	return generateFromTemplate(table, "response_model.tmpl", getResponsePath(table, outputDir), force)
}

// generateRepository 生成Repository
func generateRepository(table Table, outputDir string, force bool) error {
	return generateFromTemplate(table, "repository.tmpl", getRepositoryPath(table, outputDir), force)
}

// getSingleFileModelPath 获取单文件模型路径
func getSingleFileModelPath(table Table, outputDir string) string {
	if outputDir != "" {
		return filepath.Join(outputDir, "models", table.FileName+".go")
	}
	return filepath.Join("internal", "models", table.FileName+".go")
}

// getModelPath 获取模型文件路径
func getModelPath(table Table, outputDir string) string {
	if outputDir != "" {
		return filepath.Join(outputDir, "models", table.FileName+".go")
	}
	return filepath.Join("internal", "models", table.FileName+".go")
}

// getRequestPath 获取请求文件路径
func getRequestPath(table Table, outputDir string) string {
	if outputDir != "" {
		return filepath.Join(outputDir, "models", table.FileName+"_request.go")
	}
	return filepath.Join("internal", "models", table.FileName+"_request.go")
}

// getResponsePath 获取响应文件路径
func getResponsePath(table Table, outputDir string) string {
	if outputDir != "" {
		return filepath.Join(outputDir, "models", table.FileName+"_response.go")
	}
	return filepath.Join("internal", "models", table.FileName+"_response.go")
}

// getRepositoryPath 获取Repository文件路径
func getRepositoryPath(table Table, outputDir string) string {
	if outputDir != "" {
		return filepath.Join(outputDir, "repositories", table.FileName+"_repository.go")
	}
	return filepath.Join("internal", "repositories", table.FileName+"_repository.go")
}

// generateFromTemplate 从模板生成文件
func generateFromTemplate(table Table, templateName, outputPath string, force bool) error {
	// 查找模板文件
	templatePath := findTemplatePath(templateName)
	if templatePath == "" {
		return fmt.Errorf("模板文件不存在: %s", templateName)
	}

	// 读取模板
	tmplContent, err := os.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("读取模板失败 %s: %w", templatePath, err)
	}

	// 解析模板
	tmpl, err := template.New(templateName).Parse(string(tmplContent))
	if err != nil {
		return fmt.Errorf("解析模板失败 %s: %w", templateName, err)
	}

	// 安全创建文件
	file, shouldWrite, err := safeWriteFile(outputPath, force)
	if err != nil {
		return err
	}
	if !shouldWrite {
		return nil // 用户选择跳过
	}
	defer file.Close()

	if err := tmpl.Execute(file, table); err != nil {
		return fmt.Errorf("执行模板失败 %s: %w", templateName, err)
	}

	return nil
}

// findTemplatePath 查找模板文件路径
func findTemplatePath(templateName string) string {
	// 可能的模板路径
	paths := []string{
		filepath.Join("tools", "generators", "templates", templateName),
		filepath.Join("api", "tools", "generators", "templates", templateName),
		filepath.Join("templates", templateName),
		templateName,
	}

	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return ""
}

// parseSQLFile 解析SQL文件
func parseSQLFile(filename string) ([]Table, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var tables []Table
	var currentTable *Table
	var inCreateTable bool

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释和空行
		if strings.HasPrefix(line, "--") || strings.HasPrefix(line, "/*") || line == "" {
			continue
		}

		// 检测CREATE TABLE语句
		if strings.HasPrefix(strings.ToUpper(line), "CREATE TABLE") {
			tableName := extractTableName(line)
			if tableName != "" {
				currentTable = &Table{
					Name:    tableName,
					GoName:  toGoName(tableName),
					VarName: toVarName(tableName),
					Fields:  []Field{},
				}
				inCreateTable = true
			}
			continue
		}

		// 检测表结束
		if inCreateTable && (strings.HasPrefix(line, ")") || strings.Contains(line, "ENGINE=")) {
			if strings.Contains(line, "COMMENT=") {
				currentTable.Comment = extractTableComment(line)
			}
			if currentTable != nil {
				tables = append(tables, *currentTable)
			}
			inCreateTable = false
			currentTable = nil
			continue
		}

		// 解析字段定义
		if inCreateTable && currentTable != nil {
			field := parseField(line)
			if field.Name != "" {
				currentTable.Fields = append(currentTable.Fields, field)
				if field.IsPrimaryKey {
					currentTable.PrimaryKey = field.Name
				}
			}
		}
	}

	return tables, scanner.Err()
}

// extractTableName 提取表名
func extractTableName(line string) string {
	re := regexp.MustCompile(`CREATE TABLE\s+(\w+)`)
	matches := re.FindStringSubmatch(strings.ToUpper(line))
	if len(matches) > 1 {
		return strings.ToLower(matches[1])
	}
	return ""
}

// extractTableComment 提取表注释
func extractTableComment(line string) string {
	re := regexp.MustCompile(`COMMENT='([^']*)'`)
	matches := re.FindStringSubmatch(line)
	if len(matches) > 1 {
		parts := strings.Split(matches[1], "：")
		if len(parts) > 0 {
			return strings.TrimSpace(parts[0])
		}
	}
	return ""
}

// parseField 解析字段定义
func parseField(line string) Field {
	line = strings.TrimSpace(line)
	if strings.HasPrefix(line, "CONSTRAINT") || strings.HasPrefix(line, "INDEX") ||
		strings.HasPrefix(line, "KEY") || strings.HasPrefix(line, "PRIMARY KEY") ||
		strings.HasPrefix(line, "UNIQUE") || strings.HasPrefix(line, "--") {
		return Field{}
	}

	// 移除末尾的逗号
	line = strings.TrimSuffix(line, ",")

	parts := strings.Fields(line)
	if len(parts) < 2 {
		return Field{}
	}

	field := Field{
		Name: parts[0],
	}

	// 解析字段类型
	field.Type = parts[1]
	field.GoType = mapSQLTypeToGo(field.Type)
	field.JsonTag = toJsonTag(field.Name)

	// 检查是否为主键
	if field.Name == "id" && strings.Contains(strings.ToUpper(line), "AUTO_INCREMENT") {
		field.IsPrimaryKey = true
		field.GormTag = "primaryKey;autoIncrement"
	}

	// 检查是否为时间戳字段
	if strings.Contains(field.Name, "created_at") || strings.Contains(field.Name, "updated_at") {
		field.IsTimestamp = true
		if strings.Contains(field.Name, "created_at") {
			field.GormTag = "autoCreateTime"
		} else {
			field.GormTag = "autoUpdateTime"
		}
	}

	// 解析注释
	if strings.Contains(line, "COMMENT") {
		re := regexp.MustCompile(`COMMENT\s+'([^']*)'`)
		matches := re.FindStringSubmatch(line)
		if len(matches) > 1 {
			field.Comment = strings.Split(matches[1], "，")[0]
		}
	}

	// 设置GORM标签
	if field.GormTag == "" {
		field.GormTag = buildGormTag(line, field.Type)
	}

	// 设置验证规则
	field.Validation = buildValidation(line, field.Type)
	field.Required = strings.Contains(strings.ToUpper(line), "NOT NULL") && !field.IsTimestamp

	return field
}

// mapSQLTypeToGo 映射SQL类型到Go类型
func mapSQLTypeToGo(sqlType string) string {
	// 提取基础类型（去掉长度限制）
	baseType := strings.ToUpper(sqlType)
	if strings.Contains(baseType, "(") {
		baseType = strings.Split(baseType, "(")[0]
	}

	// 处理UNSIGNED类型
	if strings.Contains(strings.ToUpper(sqlType), "UNSIGNED") {
		baseType += " UNSIGNED"
	}

	if goType, exists := typeMapping[baseType]; exists {
		return goType
	}

	// 默认返回string
	return "string"
}

// buildGormTag 构建GORM标签
func buildGormTag(line, sqlType string) string {
	var tags []string

	// 处理字段长度
	if strings.Contains(sqlType, "(") {
		re := regexp.MustCompile(`\((\d+)\)`)
		matches := re.FindStringSubmatch(sqlType)
		if len(matches) > 1 {
			if strings.Contains(strings.ToUpper(sqlType), "VARCHAR") {
				tags = append(tags, "size:"+matches[1])
			}
		}
	}

	// 处理NOT NULL
	if strings.Contains(strings.ToUpper(line), "NOT NULL") {
		tags = append(tags, "not null")
	}

	// 处理DEFAULT值
	if strings.Contains(strings.ToUpper(line), "DEFAULT") {
		re := regexp.MustCompile(`DEFAULT\s+([^\s,]+)`)
		matches := re.FindStringSubmatch(strings.ToUpper(line))
		if len(matches) > 1 && matches[1] != "NULL" {
			if matches[1] == "CURRENT_TIMESTAMP" {
				// 时间戳字段已在上面处理
			} else {
				tags = append(tags, "default:"+strings.ToLower(matches[1]))
			}
		}
	}

	return strings.Join(tags, ";")
}

// buildValidation 构建验证规则
func buildValidation(line, sqlType string) string {
	var validations []string

	// 处理字段长度验证
	if strings.Contains(sqlType, "(") {
		re := regexp.MustCompile(`\((\d+)\)`)
		matches := re.FindStringSubmatch(sqlType)
		if len(matches) > 1 {
			if strings.Contains(strings.ToUpper(sqlType), "VARCHAR") {
				validations = append(validations, "max="+matches[1])
			}
		}
	}

	// 处理必填验证
	if strings.Contains(strings.ToUpper(line), "NOT NULL") {
		validations = append(validations, "required")
	}

	return strings.Join(validations, ",")
}

// toGoName 转换为Go结构体名称（保持与表名一致，不进行复数转单数）
func toGoName(tableName string) string {
	// 直接基于表名转换，不处理复数形式
	// 转换下划线命名为驼峰命名
	parts := strings.Split(tableName, "_")
	for i, part := range parts {
		if len(part) > 0 {
			parts[i] = strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
		}
	}

	return strings.Join(parts, "")
}

// toVarName 转换为变量名
func toVarName(tableName string) string {
	goName := toGoName(tableName)
	if len(goName) > 0 {
		return strings.ToLower(goName[:1]) + goName[1:]
	}
	return goName
}

// toJsonTag 转换为JSON标签
func toJsonTag(fieldName string) string {
	return strings.ToLower(fieldName)
}

// generateGoModel 生成Go模型文件
func generateGoModel(table Table) error {
	modelTemplate := `package models

import "time"

// {{.GoName}} {{.Comment}}
type {{.GoName}} struct {
	{{if not .HasCustomPrimaryKey}}BaseModel{{end}}
{{range .Fields}}{{if not .IsTimestamp}}	{{.GoName}} {{.GoType}} ` + "`" + `json:"{{.JsonTag}}" gorm:"{{.GormTag}}"{{if .Validation}} binding:"{{.Validation}}"{{end}}` + "`" + `{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// TableName 指定表名
func ({{.GoName}}) TableName() string {
	return "{{.Name}}"
}

// {{.GoName}}CreateRequest 创建{{.Comment}}请求
type {{.GoName}}CreateRequest struct {
{{range .Fields}}{{if and .Required (not .IsTimestamp) (not .IsPrimaryKey)}}	{{.GoName}} {{.GoType}} ` + "`" + `json:"{{.JsonTag}}"{{if .Validation}} binding:"{{.Validation}}"{{end}}` + "`" + `{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// {{.GoName}}UpdateRequest 更新{{.Comment}}请求
type {{.GoName}}UpdateRequest struct {
{{range .Fields}}{{if and (not .Required) (not .IsTimestamp) (not .IsPrimaryKey)}}	{{.GoName}} {{.GoType}} ` + "`" + `json:"{{.JsonTag}}"{{if .Validation}} binding:"{{.Validation}}"{{end}}` + "`" + `{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// {{.GoName}}Response {{.Comment}}响应
type {{.GoName}}Response struct {
	ID        uint      ` + "`" + `json:"id"` + "`" + `
{{range .Fields}}{{if not .IsTimestamp}}	{{.GoName}} {{.GoType}} ` + "`" + `json:"{{.JsonTag}}"` + "`" + `{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}	CreatedAt time.Time ` + "`" + `json:"created_at"` + "`" + `
	UpdatedAt time.Time ` + "`" + `json:"updated_at"` + "`" + `
}

// ToResponse 转换为响应格式
func (m *{{.GoName}}) ToResponse() *{{.GoName}}Response {
	return &{{.GoName}}Response{
		ID:        m.ID,
{{range .Fields}}{{if not .IsTimestamp}}		{{.GoName}}: m.{{.GoName}},
{{end}}{{end}}		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}
`

	// 处理字段，添加Go名称
	for i := range table.Fields {
		table.Fields[i].GoName = toGoName(table.Fields[i].Name)
	}

	// 检查是否有自定义主键
	table.HasCustomPrimaryKey = table.PrimaryKey != "" && table.PrimaryKey != "id"

	tmpl, err := template.New("model").Parse(modelTemplate)
	if err != nil {
		return err
	}

	// 创建输出目录
	outputDir := "../../internal/models"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	// 创建文件
	filename := filepath.Join(outputDir, table.VarName+".go")
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	return tmpl.Execute(file, table)
}

// convertToGoType 转换数据库类型到Go类型
func convertToGoType(dataType string, maxLength, precision, scale sql.NullString) string {
	dataType = strings.ToUpper(dataType)

	switch dataType {
	case "TINYINT":
		return "int8"
	case "SMALLINT":
		return "int16"
	case "MEDIUMINT", "INT":
		return "int"
	case "BIGINT":
		return "int64"
	case "TINYINT UNSIGNED":
		return "uint8"
	case "SMALLINT UNSIGNED":
		return "uint16"
	case "MEDIUMINT UNSIGNED", "INT UNSIGNED":
		return "uint"
	case "BIGINT UNSIGNED":
		return "uint64"
	case "FLOAT":
		return "float32"
	case "DOUBLE", "DECIMAL":
		return "float64"
	case "CHAR", "VARCHAR", "TEXT", "LONGTEXT", "MEDIUMTEXT", "TINYTEXT":
		return "string"
	case "DATE", "DATETIME", "TIMESTAMP":
		return "time.Time"
	case "BOOLEAN", "BOOL":
		return "bool"
	case "JSON":
		return "string"
	default:
		return "string"
	}
}

// toCamelCase 转换为大驼峰命名
func toCamelCase(s string) string {
	parts := strings.Split(s, "_")
	for i, part := range parts {
		if len(part) > 0 {
			// 特殊处理 ID 后缀
			if strings.ToLower(part) == "id" {
				parts[i] = "ID"
			} else {
				parts[i] = strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
			}
		}
	}

	result := strings.Join(parts, "")

	// 处理以id结尾但没有下划线的字段（如 openid -> OpenID, unionid -> UnionID）
	if strings.HasSuffix(strings.ToLower(result), "id") && !strings.HasSuffix(result, "ID") {
		// 将结尾的 id 替换为 ID
		result = result[:len(result)-2] + "ID"
	}

	return result
}

// toLowerCamelCase 转换为小驼峰命名
func toLowerCamelCase(s string) string {
	// 使用修复后的toGoName函数来处理复数形式
	goName := toGoName(s)
	if len(goName) > 0 {
		return strings.ToLower(goName[:1]) + goName[1:]
	}
	return goName
}

// buildBindingTag 构建Gin绑定标签
func buildBindingTag(field Field, isNullable string, maxLength sql.NullString) string {
	var tags []string

	if field.Required {
		tags = append(tags, "required")
	}

	if maxLength.Valid && maxLength.String != "" {
		tags = append(tags, "max="+maxLength.String)
	}

	return strings.Join(tags, ",")
}

// isBaseField 判断是否为基础字段（BaseModel中的字段）
func isBaseField(columnName string) bool {
	baseFields := []string{"id", "created_at", "updated_at", "deleted_at"}
	for _, field := range baseFields {
		if columnName == field {
			return true
		}
	}
	return false
}

// isAutoField 判断是否为自动字段（不需要用户输入）
func isAutoField(columnName string) bool {
	autoFields := []string{"id", "created_at", "updated_at", "deleted_at"}
	for _, field := range autoFields {
		if columnName == field {
			return true
		}
	}
	return false
}

// isHiddenField 判断是否为隐藏字段（不在响应中显示）
func isHiddenField(columnName string) bool {
	hiddenFields := []string{"deleted_at"}
	for _, field := range hiddenFields {
		if columnName == field {
			return true
		}
	}
	return false
}

// getRequestType 获取请求类型
func getRequestType(goType string) string {
	// 对于请求，大部分类型保持不变
	return goType
}

// getResponseType 获取响应类型
func getResponseType(goType string) string {
	// 对于响应，大部分类型保持不变
	return goType
}

// toSnakeCase 转换为下划线命名
func toSnakeCase(s string) string {
	var result []rune
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result = append(result, '_')
		}
		result = append(result, r)
	}
	return strings.ToLower(string(result))
}

// buildGormTagFromField 从字段信息构建GORM标签
func buildGormTagFromField(field Field, isNullable string, columnDefault sql.NullString, maxLength sql.NullString) string {
	var tags []string

	// 明确指定column名称，与数据库字段名保持一致
	tags = append(tags, "column:"+field.Name)

	// 处理字段长度
	if maxLength.Valid && field.GoType == "string" {
		tags = append(tags, "size:"+maxLength.String)
	}

	// 处理NOT NULL
	if isNullable == "NO" && !field.IsTimestamp && field.Name != "id" {
		tags = append(tags, "not null")
	}

	// 处理DEFAULT值
	if columnDefault.Valid && columnDefault.String != "" {
		if field.IsTimestamp && columnDefault.String == "CURRENT_TIMESTAMP" {
			// 时间戳字段特殊处理
		} else {
			tags = append(tags, "default:"+columnDefault.String)
		}
	}

	// 处理主键
	if field.IsPrimaryKey {
		tags = append(tags, "primaryKey")
	}

	// 处理自增
	if field.Name == "id" {
		tags = append(tags, "autoIncrement")
	}

	return strings.Join(tags, ";")
}

// buildValidationFromField 从字段信息构建验证规则
func buildValidationFromField(field Field, maxLength sql.NullString) string {
	var validations []string

	// 必填验证
	if field.Required {
		validations = append(validations, "required")
	}

	// 长度验证
	if maxLength.Valid && field.GoType == "string" {
		validations = append(validations, "max="+maxLength.String)
	}

	// 数字范围验证
	if strings.Contains(field.GoType, "int") && field.GoType != "string" {
		if strings.Contains(field.GoType, "uint") {
			validations = append(validations, "min=0")
		}
	}

	return strings.Join(validations, ",")
}

// checkFileExists 检查文件是否存在
func checkFileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// safeWriteFile 安全写入文件，检查是否覆盖
func safeWriteFile(filePath string, force bool) (*os.File, bool, error) {
	if checkFileExists(filePath) {
		if !force {
			fmt.Printf("⏭️  文件已存在，跳过: %s\n", filePath)
			return nil, false, nil // 返回 nil 表示跳过，不是错误
		}
		fmt.Printf("🔄 覆盖文件: %s\n", filePath)
	} else {
		fmt.Printf("✅ 创建文件: %s\n", filePath)
	}

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, false, fmt.Errorf("创建目录失败 %s: %w", dir, err)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return nil, false, fmt.Errorf("创建文件失败 %s: %w", filePath, err)
	}

	return file, true, nil
}
