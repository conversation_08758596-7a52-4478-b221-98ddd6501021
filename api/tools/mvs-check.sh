#!/bin/bash

# MVS核心规则检查器
# 基于docs/knowledge/mvs-core-rules.md的5条核心规则

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

check_rule() {
    ((TOTAL_CHECKS++))
}

# 开始检查
echo -e "${BLUE}🔍 MVS核心规则检查器${NC}"
echo -e "${BLUE}基于docs/knowledge/mvs-core-rules.md${NC}"
echo -e "${BLUE}集成docs/standards/项目标准文档${NC}"
echo "================================"

# 规则1: 开发前必须完成需求确认
echo -e "\n${YELLOW}📋 规则1: 开发前必须完成需求确认${NC}"

check_rule
if [ -d "docs/requirements" ] || [ -f "docs/requirements.md" ]; then
    log_success "需求文档目录存在"
else
    log_error "缺少需求文档目录 (docs/requirements/ 或 docs/requirements.md)"
fi

check_rule
if find docs/ -name "*requirements*" -o -name "*需求*" | grep -q .; then
    log_success "找到需求相关文档"
else
    log_error "未找到需求相关文档"
fi

# 检查是否有功能模块缺少需求文档
check_rule
modules=$(find internal/handlers/api -name "*_handler.go" | sed 's/.*\///;s/_handler.go//' | sort)
missing_requirements=0
for module in $modules; do
    if ! find docs/ -name "*${module}*" -o -name "*requirements*" | grep -q .; then
        log_warning "模块 $module 可能缺少需求文档"
        ((missing_requirements++))
    fi
done

if [ $missing_requirements -eq 0 ]; then
    log_success "所有模块都有对应的需求文档"
else
    log_error "有 $missing_requirements 个模块缺少需求文档"
fi

# 规则2: 使用统一的代码模板
echo -e "\n${YELLOW}🏗️  规则2: 使用统一的代码模板${NC}"

# 检查Repository层标准接口
check_rule
repo_files=$(find internal/repositories -name "*_repository.go" 2>/dev/null || true)
if [ -n "$repo_files" ]; then
    standard_methods=("Create" "GetByID" "Update" "Delete" "List")
    repo_violations=0
    
    for repo_file in $repo_files; do
        for method in "${standard_methods[@]}"; do
            if ! grep -q "func.*${method}" "$repo_file"; then
                log_warning "Repository $repo_file 缺少标准方法: $method"
                ((repo_violations++))
            fi
        done
    done
    
    if [ $repo_violations -eq 0 ]; then
        log_success "Repository层遵循标准模板"
    else
        log_error "Repository层有 $repo_violations 个模板违规"
    fi
else
    log_warning "未找到Repository文件"
fi

# 检查Service层标准接口
check_rule
service_files=$(find internal/services -name "*_service.go" 2>/dev/null || true)
if [ -n "$service_files" ]; then
    service_violations=0
    
    for service_file in $service_files; do
        if ! grep -q "type.*Service interface" "$service_file"; then
            log_warning "Service $service_file 缺少标准接口定义"
            ((service_violations++))
        fi
    done
    
    if [ $service_violations -eq 0 ]; then
        log_success "Service层遵循标准模板"
    else
        log_error "Service层有 $service_violations 个模板违规"
    fi
else
    log_warning "未找到Service文件"
fi

# 检查Handler层标准模式
check_rule
handler_files=$(find internal/handlers -name "*_handler.go" 2>/dev/null || true)
if [ -n "$handler_files" ]; then
    handler_violations=0
    
    for handler_file in $handler_files; do
        if ! grep -q "type.*Handler struct" "$handler_file"; then
            log_warning "Handler $handler_file 缺少标准结构定义"
            ((handler_violations++))
        fi
    done
    
    if [ $handler_violations -eq 0 ]; then
        log_success "Handler层遵循标准模板"
    else
        log_error "Handler层有 $handler_violations 个模板违规"
    fi
else
    log_warning "未找到Handler文件"
fi

# 规则3: 每个功能必须有基础测试
echo -e "\n${YELLOW}🧪 规则3: 每个功能必须有基础测试${NC}"

# 检查Repository层测试
check_rule
repo_test_coverage=0
repo_total=0
for repo_file in $(find internal/repositories -name "*_repository.go" 2>/dev/null || true); do
    ((repo_total++))
    test_file="${repo_file%%.go}_test.go"
    if [ -f "$test_file" ] || [ -f "test/$(basename ${repo_file%%.go})_test.go" ]; then
        ((repo_test_coverage++))
    fi
done

if [ $repo_total -gt 0 ]; then
    coverage_percent=$((repo_test_coverage * 100 / repo_total))
    if [ $coverage_percent -ge 80 ]; then
        log_success "Repository层测试覆盖率: ${coverage_percent}% (${repo_test_coverage}/${repo_total})"
    else
        log_error "Repository层测试覆盖率不足: ${coverage_percent}% (${repo_test_coverage}/${repo_total})"
    fi
else
    log_warning "未找到Repository文件"
fi

# 检查Service层测试
check_rule
service_test_coverage=0
service_total=0
for service_file in $(find internal/services -name "*_service.go" 2>/dev/null || true); do
    ((service_total++))
    test_file="${service_file%%.go}_test.go"
    if [ -f "$test_file" ] || [ -f "test/$(basename ${service_file%%.go})_test.go" ]; then
        ((service_test_coverage++))
    fi
done

if [ $service_total -gt 0 ]; then
    coverage_percent=$((service_test_coverage * 100 / service_total))
    if [ $coverage_percent -ge 80 ]; then
        log_success "Service层测试覆盖率: ${coverage_percent}% (${service_test_coverage}/${service_total})"
    else
        log_error "Service层测试覆盖率不足: ${coverage_percent}% (${service_test_coverage}/${service_total})"
    fi
else
    log_warning "未找到Service文件"
fi

# 检查编译通过
check_rule
if go build ./... >/dev/null 2>&1; then
    log_success "代码编译通过"
else
    log_error "代码编译失败"
fi

# 规则4: 提交前运行检查清单
echo -e "\n${YELLOW}📝 规则4: 提交前运行检查清单${NC}"

# 检查代码格式
check_rule
if [ -z "$(gofmt -l . | grep -v vendor/)" ]; then
    log_success "代码格式符合标准"
else
    log_error "代码格式不符合标准，需要运行 gofmt"
fi

# 检查go.mod整洁性
check_rule
if go mod tidy -diff >/dev/null 2>&1; then
    log_success "go.mod文件整洁"
else
    log_error "go.mod文件需要整理，运行 go mod tidy"
fi

# 检查基础测试通过
check_rule
if go test -short ./... >/dev/null 2>&1; then
    log_success "基础测试通过"
else
    log_error "基础测试失败"
fi

# 检查Git pre-commit hook
check_rule
if [ -f ".git/hooks/pre-commit" ]; then
    log_success "Git pre-commit hook已配置"
else
    log_warning "建议配置Git pre-commit hook自动检查"
fi

# 规则5: 合并前更新知识库
echo -e "\n${YELLOW}📚 规则5: 合并前更新知识库${NC}"

# 检查知识库目录结构
check_rule
if [ -d "docs/knowledge" ]; then
    log_success "知识库目录存在"
else
    log_error "缺少知识库目录 docs/knowledge/"
fi

# 检查决策记录
check_rule
if [ -d "docs/knowledge/decisions" ] || find docs/ -name "*decision*" | grep -q .; then
    log_success "找到决策记录目录"
else
    log_warning "建议创建决策记录目录 docs/knowledge/decisions/"
fi

# 检查问题记录
check_rule
if [ -d "docs/knowledge/problems" ] || find docs/ -name "*problem*" | grep -q .; then
    log_success "找到问题记录目录"
else
    log_warning "建议创建问题记录目录 docs/knowledge/problems/"
fi

# 检查代码模式记录
check_rule
if [ -d "docs/knowledge/patterns" ] || find docs/ -name "*pattern*" | grep -q .; then
    log_success "找到代码模式记录"
else
    log_warning "建议创建代码模式目录 docs/knowledge/patterns/"
fi

# 检查开发模板
check_rule
if find docs/ -name "*template*" | grep -q .; then
    log_success "找到开发模板"
else
    log_warning "建议创建开发模板"
fi

# 新增: 项目标准文档检查
echo -e "\n${YELLOW}📋 项目标准文档检查${NC}"

# 检查标准文档目录结构
check_rule
if [ -d "../docs/standards" ]; then
    log_success "项目标准文档目录存在"
else
    log_error "缺少项目标准文档目录 ../docs/standards/"
fi

# 检查协作规范文档
check_rule
if [ -d "../docs/standards/collaboration" ] && [ -f "../docs/standards/collaboration/collaboration-guidelines.md" ]; then
    log_success "协作规范文档完整"
else
    log_error "缺少协作规范文档"
fi

# 检查开发标准文档
check_rule
if [ -d "../docs/standards/development" ] && [ -f "../docs/standards/development/development-standards-framework.md" ]; then
    log_success "开发标准文档完整"
else
    log_error "缺少开发标准文档"
fi

# 检查标准文档索引
check_rule
if [ -f "../docs/standards/README.md" ]; then
    log_success "标准文档索引存在"
else
    log_error "缺少标准文档索引 ../docs/standards/README.md"
fi

# 检查工作流程标准
check_rule
if [ -f "../docs/standards/collaboration/workflow-standards.md" ]; then
    log_success "工作流程标准文档存在"
else
    log_warning "建议创建工作流程标准文档"
fi

# 检查需求验证机制
check_rule
if [ -f "../docs/standards/development/requirements-validation.md" ]; then
    log_success "需求验证机制文档存在"
else
    log_warning "建议创建需求验证机制文档"
fi

# 总结报告
echo -e "\n${BLUE}📊 检查结果总结${NC}"
echo "================================"
echo -e "总检查项: ${TOTAL_CHECKS}"
echo -e "${GREEN}通过: ${PASSED_CHECKS}${NC}"
echo -e "${RED}失败: ${FAILED_CHECKS}${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 恭喜！所有MVS核心规则检查通过！${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 有 ${FAILED_CHECKS} 项检查失败，请修复后重试${NC}"
    echo -e "${YELLOW}💡 建议查看以下文档了解详细规则:${NC}"
    echo -e "${YELLOW}   - docs/knowledge/mvs-core-rules.md (MVS核心规则)${NC}"
    echo -e "${YELLOW}   - ../docs/standards/README.md (项目标准概览)${NC}"
    echo -e "${YELLOW}   - ../docs/standards/collaboration/collaboration-guidelines.md (协作指南)${NC}"
    exit 1
fi
