package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 数据库连接配置
	dsn := "admin:235lwx123456@tcp(*************:3306)/hop_planet_dev?charset=utf8mb4&parseTime=True&loc=Local"
	
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("=== 检查表结构 ===")

	// 检查user_camp_participations表结构
	fmt.Println("user_camp_participations table structure:")
	rows, err := db.Raw("DESCRIBE user_camp_participations").Rows()
	if err != nil {
		log.Fatal("Failed to describe table:", err)
	}
	defer rows.Close()

	for rows.Next() {
		var field, typ, null, key, def, extra string
		err := rows.Scan(&field, &typ, &null, &key, &def, &extra)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}
		fmt.Printf("  %s | %s | %s | %s | %s | %s\n", field, typ, null, key, def, extra)
	}

	// 尝试直接插入一条简单记录
	fmt.Println("\n=== 尝试插入简单记录 ===")
	simpleSQL := `
	INSERT INTO user_camp_participations (
		camp_id, user_id, child_id, participation_status, participation_date,
		current_day, progress_percentage, total_checkins, consecutive_days,
		total_study_minutes, rating,
		created_at, updated_at
	) VALUES (
		1, 1, 1, 1, '2024-01-15',
		14, 66.67, 12, 10,
		420, 0,
		'2024-01-15 08:00:00', '2024-01-28 20:30:00'
	);`
	
	err = db.Exec(simpleSQL).Error
	if err != nil {
		log.Printf("Failed to insert simple record: %v", err)
	} else {
		fmt.Println("Simple record inserted successfully!")
	}

	// 再次检查数据
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM user_camp_participations WHERE camp_id = 1 AND child_id = 1").Scan(&count).Error
	if err != nil {
		log.Printf("Error checking count: %v", err)
	} else {
		fmt.Printf("Records found: %d\n", count)
	}

	// 查看实际数据
	fmt.Println("\n=== 查看实际数据 ===")
	rows2, err := db.Raw("SELECT id, camp_id, user_id, child_id, participation_status FROM user_camp_participations LIMIT 5").Rows()
	if err != nil {
		log.Printf("Error querying data: %v", err)
	} else {
		defer rows2.Close()
		for rows2.Next() {
			var id, campID, userID, childID, status int
			err := rows2.Scan(&id, &campID, &userID, &childID, &status)
			if err != nil {
				log.Printf("Error scanning data: %v", err)
				continue
			}
			fmt.Printf("  ID: %d, CampID: %d, UserID: %d, ChildID: %d, Status: %d\n", id, campID, userID, childID, status)
		}
	}
}
