# 🛠️ Tools 目录

本目录包含项目的开发工具，用于代码生成、质量检查和开发效率提升。

## 📁 目录结构

```
tools/
├── README.md              # 📖 本文档
├── generate-module.sh     # 🚀 模块生成器（推荐）
├── mvs-check.sh          # ✅ MVS规则检查器
├── sql-to-go/            # 🎯 数据库驱动代码生成器（强烈推荐）
│   ├── README.md         # SQL-to-Go详细说明
│   └── main.go           # 主程序
└── generators/           # 📦 代码生成器套件
    ├── README.md         # 生成器详细说明
    ├── generate.sh       # 基础代码生成器
    └── templates/        # 代码模板
        ├── model.tmpl    # 模型模板
        └── repository.tmpl # 仓储模板
```

## 🎯 工具分类

### 代码生成工具
- `sql-to-go/` - **🏆 强烈推荐**：数据库驱动代码生成器
- `generate-module.sh` - **主力工具**：完整模块生成器
- `generators/generate.sh` - 基础代码生成器
- `generators/templates/` - 代码模板库

### 质量保证工具
- `mvs-check.sh` - MVS规则合规性检查

## 🚀 主要工具详解

### `sql-to-go/` - 数据库驱动代码生成器 ⭐⭐⭐⭐⭐ 🏆

**功能**: 直接从MySQL数据库读取表结构，自动生成对应的Go代码

**核心优势**:
- 🎯 **数据库驱动**: 直接读取真实表结构，确保代码与数据库100%一致
- 🚀 **智能生成**: 自动处理类型转换、GORM标签、验证规则
- 📦 **完整覆盖**: 生成Model、Request、Response、Repository四套代码
- 🔧 **自动导包**: 智能检测并导入所需包（如time包）
- 🛡️ **字段过滤**: 自动跳过与BaseModel冲突的字段
- ⚙️ **灵活配置**: 支持指定表、生成类型、输出目录等选项

**生成的文件**:
```
internal/
├── models/
│   ├── user.go           # 基础模型
│   ├── user_request.go   # 请求结构
│   └── user_response.go  # 响应结构
└── repositories/
    └── user_repository.go # Repository接口和实现
```

**使用方法**:
```bash
# 生成所有表的所有代码（推荐）
go run tools/sql-to-go/main.go

# 生成指定表的所有代码
go run tools/sql-to-go/main.go --table users

# 生成指定表的特定类型
go run tools/sql-to-go/main.go --table users --type model,repository

# 使用自定义配置文件
go run tools/sql-to-go/main.go --config configs/config.yaml
```

**适用场景**:
- ✅ **数据库表已建立** - 最适合已有数据库表结构的情况
- ✅ **确保一致性** - 需要代码与数据库结构完全匹配
- ✅ **批量生成** - 需要为多个表快速生成代码
- ✅ **自动化优先** - 希望减少手动配置和人为错误

**详细文档**: 请参考 [`sql-to-go/README.md`](sql-to-go/README.md)

### `generate-module.sh` - 模块生成器 ⭐⭐⭐⭐⭐

**功能**: 一键生成完整的CRUD模块，支持三种服务类型

**支持的服务类型**:
- `api` - API服务模块（默认）
- `admin` - 管理后台模块  
- `shared` - 共享服务模块

**生成的文件**:
```
internal/
├── models/{module}.go              # 数据模型（仅API类型）
├── repositories/{module}_repository.go  # 数据访问层（仅API类型）
├── services/{type}/{module}_service.go  # 业务逻辑层
└── handlers/{type}/{module}_handler.go  # HTTP处理器
```

**使用方法**:
```bash
# 生成API模块（默认）
./tools/generate-module.sh product

# 生成API模块（显式指定）
./tools/generate-module.sh product api

# 生成管理后台模块
./tools/generate-module.sh user_management admin

# 生成共享服务模块
./tools/generate-module.sh notification shared
```

**高级功能**:
- ✅ **MVS规则检查**: 自动验证开发规范
- 🔄 **智能命名**: 自动转换下划线到驼峰命名
- 📝 **完整CRUD**: 生成增删改查全套代码
- 🎯 **路由注册**: 自动生成路由注册代码
- 📊 **详细日志**: 彩色输出，清晰的操作反馈

**示例输出**:
```bash
$ ./tools/generate-module.sh product api
🚀 开始生成模块: product (类型: api)
📝 生成模型文件: internal/models/product.go
🗄️ 生成仓储文件: internal/repositories/product_repository.go  
⚙️ 生成服务文件: internal/services/api/product_service.go
🌐 生成处理器文件: internal/handlers/api/product_handler.go
✅ 模块生成完成！
```

### `mvs-check.sh` - MVS规则检查器

**功能**: 检查项目是否符合MVS（Minimum Viable Standards）规则

**检查项目**:
1. 需求确认文档
2. 代码模板使用
3. 测试覆盖率
4. 提交前检查清单
5. 知识库更新
6. 架构变更管理

**使用方法**:
```bash
./tools/mvs-check.sh
```

### `generators/` - 代码生成器套件

**说明**: 基础代码生成器，提供模板驱动的代码生成

**详细文档**: 请参考 [`generators/README.md`](generators/README.md)

## 📋 使用指南

### 快速开始

#### 方案A：数据库驱动生成（推荐）
```bash
# 1. 确保数据库配置正确
cat configs/config.yaml

# 2. 生成所有表的代码
go run tools/sql-to-go/main.go

# 3. 检查生成的代码
ls -la internal/models/
ls -la internal/repositories/

# 4. 运行MVS检查
./tools/mvs-check.sh
```

#### 方案B：传统模块生成
```bash
# 1. 生成一个产品模块
./tools/generate-module.sh product api

# 2. 检查生成的代码
ls -la internal/models/product.go
ls -la internal/services/api/product_service.go

# 3. 运行MVS检查
./tools/mvs-check.sh
```

### 开发工作流集成

#### 数据库优先工作流（推荐）
```bash
# 1. 设计数据库表结构
# 2. 创建数据库表
# 3. 使用sql-to-go生成代码
go run tools/sql-to-go/main.go

# 4. 补充业务逻辑层（可选）
./tools/generate-module.sh user api

# 5. 检查规范和测试
./tools/mvs-check.sh
make test
```

#### 传统开发工作流
```bash
# 开发新功能的标准流程
./tools/generate-module.sh new_feature api  # 生成代码
# 编辑业务逻辑...
./tools/mvs-check.sh                        # 检查规范
make test                                   # 运行测试
```

### 批量生成示例

#### 使用sql-to-go批量生成
```bash
# 生成所有表的基础代码
go run tools/sql-to-go/main.go

# 或者分表生成
go run tools/sql-to-go/main.go --table users
go run tools/sql-to-go/main.go --table videos
go run tools/sql-to-go/main.go --table training_camps
```

#### 使用generate-module.sh批量生成
```bash
# 生成多个相关模块
./tools/generate-module.sh user api
./tools/generate-module.sh user_profile api
./tools/generate-module.sh user_management admin
./tools/generate-module.sh user_notification shared
```

## 🎯 工具选择指南

### 何时使用 `sql-to-go/`？ 🏆
- ✅ **数据库表已建立** - 最适合当前情况
- ✅ **确保数据一致性** - 代码与数据库结构完全匹配
- ✅ **批量生成需求** - 需要为多个表快速生成代码
- ✅ **自动化优先** - 希望减少手动配置
- ✅ **类型安全** - 自动处理MySQL到Go的类型转换

### 何时使用 `generate-module.sh`？
- ✅ 需要完整的CRUD模块（包含Service和Handler）
- ✅ 要求符合项目架构规范
- ✅ 需要自动路由注册
- ✅ 希望有详细的生成日志
- ✅ 数据库表尚未建立，需要先设计代码结构

### 何时使用 `generators/generate.sh`？
- ✅ 只需要基础模型和仓储
- ✅ 需要自定义模板
- ✅ 特殊的生成需求

### 推荐使用策略

#### 🏆 最佳实践：组合使用
```bash
# 第一步：使用sql-to-go生成数据层（推荐）
go run tools/sql-to-go/main.go --type model,repository

# 第二步：使用generate-module.sh补充业务层
./tools/generate-module.sh user api
./tools/generate-module.sh video api
```

#### 📊 工具对比

| 工具 | 数据源 | 生成内容 | 自动化程度 | 推荐场景 |
|------|--------|----------|------------|----------|
| **sql-to-go** | 数据库表结构 | Model+Repository+Request/Response | ⭐⭐⭐⭐⭐ | 数据库已建立 |
| **generate-module.sh** | 手动配置 | 完整CRUD模块 | ⭐⭐⭐⭐ | 需要完整业务模块 |
| **generators/generate.sh** | 手动配置 | 基础Model+Repository | ⭐⭐⭐ | 简单快速生成 |

**当前情况推荐**: `sql-to-go` - 因为您的数据库表已经建立，这是最高效的选择

## ⚠️ 注意事项

### 执行环境
- **工作目录**: 必须从 `api/` 目录执行
- **权限**: 确保脚本有执行权限
  ```bash
  chmod +x tools/*.sh
  chmod +x tools/generators/*.sh
  ```
- **数据库配置**: sql-to-go需要正确的数据库配置
  ```bash
  # 检查配置文件
  cat configs/config.yaml
  ```

### 文件覆盖
- 生成器会检查文件是否存在
- 默认不覆盖已存在的文件
- 使用 `--force` 参数强制覆盖（如果支持）

### 命名规范
- **模块名**: 使用下划线命名 (`user_profile`)
- **自动转换**: 工具会自动转换为驼峰命名 (`UserProfile`)
- **文件名**: 遵循Go语言命名规范

## 🔧 自定义和扩展

### 添加新模板
1. 在 `generators/templates/` 中添加新模板
2. 修改生成器脚本以支持新模板
3. 更新文档

### 修改生成逻辑
1. 编辑对应的生成器脚本
2. 测试生成结果
3. 更新使用文档

### 集成到IDE
```bash
# VS Code 任务配置示例
{
  "label": "Generate Module",
  "type": "shell", 
  "command": "./tools/generate-module.sh",
  "args": ["${input:moduleName}", "${input:serviceType}"]
}
```

## 🎯 最佳实践

### 代码生成最佳实践
1. **数据库优先**: 优先使用sql-to-go，确保代码与数据库一致
2. **命名一致性**: 使用清晰、一致的模块命名
3. **类型选择**: 根据功能选择合适的服务类型
4. **代码审查**: 生成后检查和调整业务逻辑
5. **测试编写**: 为生成的代码编写对应测试
6. **文档更新**: 及时更新API文档和使用说明

### sql-to-go使用建议
1. **配置检查**: 生成前确保数据库配置正确
2. **分批生成**: 大项目可以分表生成，便于管理
3. **类型指定**: 根据需要指定生成类型，避免不必要的文件
4. **结果验证**: 生成后检查字段类型和标签是否正确

### 工具组合策略
```bash
# 推荐的完整工作流
1. 设计数据库表结构
2. go run tools/sql-to-go/main.go          # 生成数据层
3. ./tools/generate-module.sh module api   # 补充业务层
4. ./tools/mvs-check.sh                    # 检查规范
5. 编写业务逻辑和测试
```

---

**让开发更高效，让代码更规范！** 🚀
