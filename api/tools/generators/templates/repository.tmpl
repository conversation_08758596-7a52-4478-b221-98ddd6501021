package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// {{.GoName}}Repository {{.Comment}}仓储接口
type {{.GoName}}Repository interface {
	Create({{.VarName}} *models.{{.GoName}}) error
	GetByID(id uint) (*models.{{.GoName}}, error)
	Update(id uint, {{.VarName}} *models.{{.GoName}}) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.{{.GoName}}, int64, error)
}

// {{.VarName}}Repository {{.Comment}}仓储实现
type {{.VarName}}Repository struct {
	db *gorm.DB
}

// New{{.GoName}}Repository 创建{{.Comment}}仓储
func New{{.GoName}}Repository(db *gorm.DB) {{.GoName}}Repository {
	return &{{.VarName}}Repository{
		db: db,
	}
}

// Create 创建{{.Comment}}
func (r *{{.VarName}}Repository) Create({{.VarName}} *models.{{.GoName}}) error {
	if err := r.db.Create({{.VarName}}).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建{{.Comment}}失败")
	}
	return nil
}

// GetByID 根据ID获取{{.Comment}}
func (r *{{.VarName}}Repository) GetByID(id uint) (*models.{{.GoName}}, error) {
	var {{.VarName}} models.{{.GoName}}
	if err := r.db.First(&{{.VarName}}, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("{{.Comment}}不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询{{.Comment}}失败")
	}
	return &{{.VarName}}, nil
}

// Update 更新{{.Comment}}
func (r *{{.VarName}}Repository) Update(id uint, {{.VarName}} *models.{{.GoName}}) error {
	if err := r.db.Model(&models.{{.GoName}}{}).Where("id = ?", id).Updates({{.VarName}}).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新{{.Comment}}失败")
	}
	return nil
}

// Delete 删除{{.Comment}}
func (r *{{.VarName}}Repository) Delete(id uint) error {
	if err := r.db.Delete(&models.{{.GoName}}{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除{{.Comment}}失败")
	}
	return nil
}

// List 获取{{.Comment}}列表
func (r *{{.VarName}}Repository) List(offset, limit int) ([]*models.{{.GoName}}, int64, error) {
	var {{.VarName}}s []*models.{{.GoName}}
	var total int64

	// 获取总数
	if err := r.db.Model(&models.{{.GoName}}{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询{{.Comment}}总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&{{.VarName}}s).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询{{.Comment}}列表失败")
	}

	return {{.VarName}}s, total, nil
}
