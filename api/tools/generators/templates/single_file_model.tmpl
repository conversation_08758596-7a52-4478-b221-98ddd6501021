package models

import (
	"time"
{{if .HasDeletedAt}}
	"gorm.io/gorm"
{{end}}
)

// ==================== Model ====================

// {{.GoName}} {{.Comment}}
type {{.GoName}} struct {
	BaseModel
{{range .Fields}}{{if not .IsBaseField}}	{{.GoName}} {{.GoType}} `json:"{{.JsonName}}" gorm:"{{.GormTag}}"{{if .ValidateTag}} validate:"{{.ValidateTag}}"{{end}}`{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// TableName 指定表名
func ({{.GoName}}) TableName() string {
	return "{{.Name}}"
}

// ==================== Requests ====================

// {{.GoName}}CreateRequest 创建{{.Comment}}请求
type {{.GoName}}CreateRequest struct {
{{range .Fields}}{{if and (not .IsBaseField) (not .IsAutoField)}}	{{.GoName}} {{.RequestType}} `json:"{{.JsonName}}"{{if .BindingTag}} binding:"{{.BindingTag}}"{{end}}{{if .ValidateTag}} validate:"{{.ValidateTag}}"{{end}}`{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// {{.GoName}}UpdateRequest 更新{{.Comment}}请求
type {{.GoName}}UpdateRequest struct {
{{range .Fields}}{{if and (not .IsBaseField) (not .IsAutoField)}}	{{.GoName}} *{{.RequestType}} `json:"{{.JsonName}},omitempty"{{if .BindingTag}} binding:"omitempty,{{.BindingTag}}"{{end}}{{if .ValidateTag}} validate:"omitempty,{{.ValidateTag}}"{{end}}`{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}}

// ==================== Responses ====================

// {{.GoName}}Response {{.Comment}}响应
type {{.GoName}}Response struct {
	ID uint `json:"id"` // 主键ID
{{range .Fields}}{{if and (not .IsHiddenField) (not .IsBaseField)}}	{{.GoName}} {{.ResponseType}} `json:"{{.JsonName}}"`{{if .Comment}} // {{.Comment}}{{end}}
{{end}}{{end}}	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// {{.GoName}}ListResponse {{.Comment}}列表响应
type {{.GoName}}ListResponse struct {
	List  []*{{.GoName}}Response `json:"list"`  // {{.Comment}}列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将{{.Comment}}模型转换为响应结构
func (m *{{.GoName}}) ToResponse() *{{.GoName}}Response {
	return &{{.GoName}}Response{
{{range .Fields}}{{if not .IsHiddenField}}		{{.GoName}}: m.{{.GoName}},
{{end}}{{end}}	}
}

// ToResponseList 将{{.Comment}}模型列表转换为响应列表
func {{.GoName}}ToResponseList(models []*{{.GoName}}, total int64) *{{.GoName}}ListResponse {
	list := make([]*{{.GoName}}Response, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &{{.GoName}}ListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到{{.Comment}}模型
func (m *{{.GoName}}) ApplyUpdateRequest(req *{{.GoName}}UpdateRequest) {
{{range .Fields}}{{if and (not .IsBaseField) (not .IsAutoField)}}	if req.{{.GoName}} != nil {
		m.{{.GoName}} = *req.{{.GoName}}
	}
{{end}}{{end}}}
