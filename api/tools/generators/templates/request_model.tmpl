package models
{{$hasTimeField := false}}{{range .Fields}}{{if or (eq .GoType "time.Time") (eq .GoType "*time.Time")}}{{$hasTimeField = true}}{{end}}{{end}}{{if $hasTimeField}}
import "time"
{{end}}
// {{.GoName}}CreateRequest 创建{{.Comment}}请求
type {{.GoName}}CreateRequest struct {
{{range .Fields}}{{if and .Required (not .IsTimestamp) (ne .Name "id")}}	{{.GoName}} {{.GoType}} `json:"{{.JsonTag}}" binding:"{{.Validation}}"{{if .Comment}} // {{.Comment}}{{end}}`
{{end}}{{end}}}

// {{.GoName}}UpdateRequest 更新{{.Comment}}请求
type {{.GoName}}UpdateRequest struct {
{{range .Fields}}{{if and (not .Required) (not .IsTimestamp) (ne .Name "id")}}	{{.GoName}} {{.GoType}} `json:"{{.JsonTag}}" binding:"{{.Validation}}"{{if .Comment}} // {{.Comment}}{{end}}`
{{end}}{{end}}}
