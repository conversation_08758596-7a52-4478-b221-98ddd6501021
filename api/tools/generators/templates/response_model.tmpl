package models

import "time"

// {{.GoName}}Response {{.Comment}}响应
type {{.GoName}}Response struct {
	ID        uint      `json:"id"`
{{range .Fields}}{{if and (not .IsTimestamp) (ne .Name "id")}}	{{.GoName}} {{.GoType}} `json:"{{.JsonTag}}"{{if .Comment}} // {{.Comment}}{{end}}`
{{end}}{{end}}	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (m *{{.GoName}}) ToResponse() *{{.GoName}}Response {
	return &{{.GoName}}Response{
		ID:        m.ID,
{{range .Fields}}{{if and (not .IsTimestamp) (ne .Name "id")}}		{{.GoName}}: m.{{.GoName}},
{{end}}{{end}}		CreatedAt: m.<PERSON>,
		UpdatedAt: m.Updated<PERSON>t,
	}
}
