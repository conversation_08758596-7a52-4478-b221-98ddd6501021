#!/bin/bash

# 代码生成器脚本

set -e

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <模块名> [选项]"
    echo ""
    echo "选项:"
    echo "  --comment <注释>     模块注释 (默认: 模块名)"
    echo "  --fields <字段>      字段定义 (格式: name:type:comment)"
    echo "  --endpoints <端点>   生成端点 (api,admin)"
    echo ""
    echo "示例:"
    echo "  $0 product --comment '产品' --fields 'name:string:产品名称,price:float64:价格' --endpoints api,admin"
    exit 1
fi

MODULE_NAME=$1
shift

# 默认值
COMMENT=$MODULE_NAME
FIELDS=""
ENDPOINTS="api,admin"

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --comment)
            COMMENT="$2"
            shift 2
            ;;
        --fields)
            FIELDS="$2"
            shift 2
            ;;
        --endpoints)
            ENDPOINTS="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 转换为首字母大写
MODEL_NAME=$(echo "$MODULE_NAME" | sed 's/^./\U&/')
VAR_NAME=$(echo "$MODULE_NAME" | sed 's/^./\L&/')
TABLE_NAME="${MODULE_NAME}s"

echo "🚀 生成模块: $MODULE_NAME"
echo "   - 模型名: $MODEL_NAME"
echo "   - 变量名: $VAR_NAME"
echo "   - 表名: $TABLE_NAME"
echo "   - 注释: $COMMENT"
echo "   - 端点: $ENDPOINTS"

# 进入api目录
cd "$(dirname "$0")/../api"

# 创建目录
mkdir -p internal/models
mkdir -p internal/repositories
mkdir -p internal/services
mkdir -p internal/handlers/api
mkdir -p internal/handlers/admin

# 生成模型文件
echo "📝 生成模型文件..."
cat > "internal/models/${VAR_NAME}.go" << EOF
package models

import "time"

// $MODEL_NAME $COMMENT
type $MODEL_NAME struct {
	BaseModel
	Name   string \`json:"name" gorm:"size:100;not null" binding:"required,max=100"\` // 名称
	Status int    \`json:"status" gorm:"default:1" binding:"min=1,max=2"\`            // 状态：1-正常，2-禁用
}

// TableName 指定表名
func ($MODEL_NAME) TableName() string {
	return "$TABLE_NAME"
}

// ${MODEL_NAME}CreateRequest 创建${COMMENT}请求
type ${MODEL_NAME}CreateRequest struct {
	Name string \`json:"name" binding:"required,max=100"\` // 名称
}

// ${MODEL_NAME}UpdateRequest 更新${COMMENT}请求
type ${MODEL_NAME}UpdateRequest struct {
	Name   string \`json:"name" binding:"max=100"\`   // 名称
	Status int    \`json:"status" binding:"min=1,max=2"\` // 状态
}

// ${MODEL_NAME}Response ${COMMENT}响应
type ${MODEL_NAME}Response struct {
	ID        uint      \`json:"id"\`
	Name      string    \`json:"name"\`
	Status    int       \`json:"status"\`
	CreatedAt time.Time \`json:"created_at"\`
	UpdatedAt time.Time \`json:"updated_at"\`
}

// ToResponse 转换为响应格式
func (m *$MODEL_NAME) ToResponse() *${MODEL_NAME}Response {
	return &${MODEL_NAME}Response{
		ID:        m.ID,
		Name:      m.Name,
		Status:    m.Status,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}
EOF

# 生成仓储文件
echo "📝 生成仓储文件..."
cat > "internal/repositories/${VAR_NAME}_repository.go" << EOF
package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errors"

	"gorm.io/gorm"
)

// ${MODEL_NAME}Repository ${COMMENT}仓储接口
type ${MODEL_NAME}Repository interface {
	Create(${VAR_NAME} *models.$MODEL_NAME) error
	GetByID(id uint) (*models.$MODEL_NAME, error)
	Update(id uint, ${VAR_NAME} *models.$MODEL_NAME) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.$MODEL_NAME, int64, error)
}

// ${VAR_NAME}Repository ${COMMENT}仓储实现
type ${VAR_NAME}Repository struct {
	db *gorm.DB
}

// New${MODEL_NAME}Repository 创建${COMMENT}仓储
func New${MODEL_NAME}Repository(db *gorm.DB) ${MODEL_NAME}Repository {
	return &${VAR_NAME}Repository{
		db: db,
	}
}

// Create 创建${COMMENT}
func (r *${VAR_NAME}Repository) Create(${VAR_NAME} *models.$MODEL_NAME) error {
	if err := r.db.Create(${VAR_NAME}).Error; err != nil {
		return errors.NewBusinessError(errors.ErrCodeDatabaseError, "创建${COMMENT}失败")
	}
	return nil
}

// GetByID 根据ID获取${COMMENT}
func (r *${VAR_NAME}Repository) GetByID(id uint) (*models.$MODEL_NAME, error) {
	var ${VAR_NAME} models.$MODEL_NAME
	if err := r.db.First(&${VAR_NAME}, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "${COMMENT}不存在")
		}
		return nil, errors.NewBusinessError(errors.ErrCodeDatabaseError, "查询${COMMENT}失败")
	}
	return &${VAR_NAME}, nil
}

// Update 更新${COMMENT}
func (r *${VAR_NAME}Repository) Update(id uint, ${VAR_NAME} *models.$MODEL_NAME) error {
	if err := r.db.Model(&models.$MODEL_NAME{}).Where("id = ?", id).Updates(${VAR_NAME}).Error; err != nil {
		return errors.NewBusinessError(errors.ErrCodeDatabaseError, "更新${COMMENT}失败")
	}
	return nil
}

// Delete 删除${COMMENT}
func (r *${VAR_NAME}Repository) Delete(id uint) error {
	if err := r.db.Delete(&models.$MODEL_NAME{}, id).Error; err != nil {
		return errors.NewBusinessError(errors.ErrCodeDatabaseError, "删除${COMMENT}失败")
	}
	return nil
}

// List 获取${COMMENT}列表
func (r *${VAR_NAME}Repository) List(offset, limit int) ([]*models.$MODEL_NAME, int64, error) {
	var ${VAR_NAME}s []*models.$MODEL_NAME
	var total int64

	// 获取总数
	if err := r.db.Model(&models.$MODEL_NAME{}).Count(&total).Error; err != nil {
		return nil, 0, errors.NewBusinessError(errors.ErrCodeDatabaseError, "查询${COMMENT}总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&${VAR_NAME}s).Error; err != nil {
		return nil, 0, errors.NewBusinessError(errors.ErrCodeDatabaseError, "查询${COMMENT}列表失败")
	}

	return ${VAR_NAME}s, total, nil
}
EOF

echo "✅ 模块 $MODULE_NAME 生成完成！"
echo ""
echo "📁 生成的文件："
echo "   - internal/models/${VAR_NAME}.go"
echo "   - internal/repositories/${VAR_NAME}_repository.go"
echo ""
echo "📝 下一步："
echo "   1. 根据需要修改模型字段"
echo "   2. 生成数据库迁移文件"
echo "   3. 实现Service和Handler层"
