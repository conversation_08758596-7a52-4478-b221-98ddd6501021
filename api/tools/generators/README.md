# 代码生成器 (Code Generators)

这是一个强大的代码生成工具，用于快速生成标准化的模型、仓储、服务和处理器代码。

## 功能特性

- 🚀 **快速生成**：一键生成完整的CRUD代码结构
- 📝 **模板驱动**：基于Go模板引擎，支持自定义模板
- 🎯 **标准化**：生成的代码遵循项目架构规范
- 🔧 **可配置**：支持自定义字段、注释和端点
- 📦 **完整性**：自动生成模型、仓储、请求/响应结构

## 目录结构

```
tools/generators/
├── README.md           # 本文档
├── generate.sh         # 主生成脚本
└── templates/          # 模板文件目录
    ├── model.tmpl      # 模型模板
    └── repository.tmpl # 仓储模板
```

## 使用方法

### 基础用法

```bash
# 从项目根目录执行
./tools/generators/generate.sh <模块名> [选项]
```

### 参数说明

- `<模块名>`：必需，要生成的模块名称（如：product, user, order）
- `--comment <注释>`：可选，模块的中文注释（默认使用模块名）
- `--fields <字段>`：可选，自定义字段定义
- `--endpoints <端点>`：可选，生成的端点类型（api,admin）

### 使用示例

#### 1. 基础生成
```bash
./tools/generators/generate.sh product --comment '产品'
```

#### 2. 带自定义字段
```bash
./tools/generators/generate.sh product \
  --comment '产品' \
  --fields 'name:string:产品名称,price:float64:价格,category_id:uint:分类ID' \
  --endpoints api,admin
```

#### 3. 复杂示例
```bash
./tools/generators/generate.sh order \
  --comment '订单' \
  --fields 'order_no:string:订单号,user_id:uint:用户ID,total_amount:decimal:总金额,status:int:订单状态' \
  --endpoints api
```

## 生成的文件

执行生成命令后，会在以下位置创建文件：

```
api/
├── internal/
│   ├── models/
│   │   └── {module}.go           # 模型定义
│   └── repositories/
│       └── {module}_repository.go # 仓储实现
```

## 生成的代码结构

### 1. 模型文件 (`internal/models/{module}.go`)

包含以下结构：
- **主模型**：继承BaseModel，包含业务字段
- **创建请求**：用于创建操作的请求结构
- **更新请求**：用于更新操作的请求结构  
- **响应结构**：用于API响应的数据结构
- **ToResponse方法**：模型到响应的转换方法

### 2. 仓储文件 (`internal/repositories/{module}_repository.go`)

包含以下功能：
- **接口定义**：标准CRUD操作接口
- **实现结构**：基于GORM的具体实现
- **CRUD方法**：Create, GetByID, Update, Delete, List
- **错误处理**：统一的错误处理和业务异常

## 模板系统

### 模板变量

生成器支持以下模板变量：

- `{{.ModelName}}`：首字母大写的模型名
- `{{.VarName}}`：首字母小写的变量名
- `{{.TableName}}`：数据库表名（复数形式）
- `{{.Comment}}`：模块注释
- `{{.Fields}}`：字段列表
- `{{.Endpoints}}`：端点配置

### 自定义模板

可以修改 `templates/` 目录下的模板文件来自定义生成的代码：

- `model.tmpl`：模型代码模板
- `repository.tmpl`：仓储代码模板

## 最佳实践

### 1. 命名规范
- 模块名使用小写单数形式（如：product, user, order）
- 数据库表名自动转换为复数形式（products, users, orders）
- 模型名自动转换为首字母大写（Product, User, Order）

### 2. 字段定义格式
```
字段名:类型:注释
```

支持的类型：
- `string`：字符串类型
- `int`, `uint`：整数类型
- `float64`：浮点数类型
- `bool`：布尔类型
- `time.Time`：时间类型
- `decimal`：decimal.Decimal类型（需要导入相应包）

### 3. 生成后的工作

1. **检查生成的代码**：确认字段定义和业务逻辑
2. **添加数据库迁移**：创建对应的数据库表结构
3. **实现服务层**：在 `internal/services/` 中实现业务逻辑
4. **实现处理器**：在 `internal/handlers/` 中实现HTTP处理器
5. **注册路由**：将新的端点添加到路由配置中

## 扩展功能

### 计划中的功能
- [ ] 服务层代码生成
- [ ] HTTP处理器生成
- [ ] 数据库迁移文件生成
- [ ] API文档注释生成
- [ ] 单元测试代码生成

## 注意事项

1. **执行位置**：必须从项目根目录（api/）执行生成脚本
2. **文件覆盖**：生成器会覆盖同名文件，请注意备份
3. **依赖检查**：确保项目依赖已正确安装
4. **代码审查**：生成后请仔细检查代码并根据需要调整

## 故障排除

### 常见问题

1. **权限错误**：确保脚本有执行权限
   ```bash
   chmod +x tools/generators/generate.sh
   ```

2. **路径错误**：确保从正确的目录执行
   ```bash
   # 应该在 api/ 目录下执行
   pwd  # 应该显示 .../api
   ```

3. **模板错误**：检查模板文件是否存在且格式正确
