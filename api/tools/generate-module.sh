#!/bin/bash

# 模块生成脚本  支持新的目录结构
# 使用方法: ./generate-module.sh <module_name> [service_type]
# service_type: api, admin, shared (默认: api)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_mvs() {
    echo -e "${CYAN}[MVS]${NC} $1"
}

# MVS规则检查
check_mvs_rules() {
    log_mvs "执行MVS核心规则检查..."
    
    # 规则1: 检查是否有需求文档
    if [ ! -f "$PROJECT_ROOT/docs/01-planning/requirements.md" ]; then
        log_warning "未找到需求文档，建议先完成需求分析"
    fi
    
    # 规则2: 检查模板是否存在
    if [ ! -d "$PROJECT_ROOT/docs/knowledge/patterns" ]; then
        log_warning "未找到代码模板目录，将使用内置模板"
    fi
    
    log_mvs "MVS规则检查完成"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "使用方法: $0 <module_name> [service_type]"
    log_info "service_type: api, admin, shared (默认: api)"
    log_info "示例:"
    log_info "  $0 user api          # 生成API用户模块"
    log_info "  $0 user_management admin  # 生成管理后台用户管理模块"
    log_info "  $0 notification shared    # 生成共享通知模块"
    exit 1
fi

MODULE_NAME=$1
SERVICE_TYPE=${2:-"api"}

# 验证service_type
if [[ ! "$SERVICE_TYPE" =~ ^(api|admin|shared)$ ]]; then
    log_error "service_type 必须是: api, admin, shared"
    exit 1
fi

# 验证模块名称格式
if [[ ! "$MODULE_NAME" =~ ^[a-z][a-z0-9_]*$ ]]; then
    log_error "模块名称必须以小写字母开头，只能包含小写字母、数字和下划线"
    exit 1
fi

log_step "开始生成模块: $MODULE_NAME (类型: $SERVICE_TYPE)"

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
TEMPLATE_DIR="$PROJECT_ROOT/template"

# 执行MVS规则检查
check_mvs_rules

# 目标目录 - 新的目录结构
MODELS_DIR="$TEMPLATE_DIR/internal/models"
REPOSITORIES_DIR="$TEMPLATE_DIR/internal/repositories"
SERVICES_DIR="$TEMPLATE_DIR/internal/services/$SERVICE_TYPE"
HANDLERS_DIR="$TEMPLATE_DIR/internal/handlers/$SERVICE_TYPE"

# 创建目录
log_step "创建目录结构..."
mkdir -p "$MODELS_DIR"
mkdir -p "$REPOSITORIES_DIR"
mkdir -p "$SERVICES_DIR"
mkdir -p "$HANDLERS_DIR"

# 生成文件名
MODEL_FILE="$MODELS_DIR/${MODULE_NAME}.go"
REPOSITORY_FILE="$REPOSITORIES_DIR/${MODULE_NAME}_repository.go"
SERVICE_FILE="$SERVICES_DIR/${MODULE_NAME}_service.go"
HANDLER_FILE="$HANDLERS_DIR/${MODULE_NAME}_handler.go"

# 变量处理
MODULE_NAME_LOWER=$(echo "$MODULE_NAME" | tr '[:upper:]' '[:lower:]')
MODULE_NAME_UPPER=$(echo "$MODULE_NAME" | tr '[:lower:]' '[:upper:]')
# 修复驼峰命名转换：使用Python来处理更复杂的字符串转换
MODULE_NAME_CAMEL=$(python3 -c "
import sys
name = sys.argv[1]
words = name.split('_')
camel = ''.join(word.capitalize() for word in words)
print(camel)
" "$MODULE_NAME")

# 变量验证完成
log_info "模块变量设置完成: $MODULE_NAME -> $MODULE_NAME_CAMEL"

# 检查文件是否已存在
if [ -f "$MODEL_FILE" ] || [ -f "$REPOSITORY_FILE" ] || [ -f "$SERVICE_FILE" ] || [ -f "$HANDLER_FILE" ]; then
    log_warning "模块 $MODULE_NAME ($SERVICE_TYPE) 的部分文件已存在，是否覆盖? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "取消生成"
        exit 0
    fi
fi

# 生成Model文件（只在api类型时生成，避免重复）
if [ "$SERVICE_TYPE" = "api" ] && [ ! -f "$MODEL_FILE" ]; then
    log_step "生成Model文件..."
    cat > "$MODEL_FILE" << EOF
package models

import (
	"time"
)

// ${MODULE_NAME_CAMEL} ${MODULE_NAME_CAMEL}模型
type ${MODULE_NAME_CAMEL} struct {
	ID        int       \`json:"id" db:"id"\`
	Name      string    \`json:"name" db:"name" validate:"required,min=1,max=100"\`
	Status    string    \`json:"status" db:"status" validate:"required,oneof=active inactive"\`
	CreatedAt time.Time \`json:"created_at" db:"created_at"\`
	UpdatedAt time.Time \`json:"updated_at" db:"updated_at"\`
}

// ${MODULE_NAME_CAMEL}CreateRequest 创建${MODULE_NAME_CAMEL}请求
type ${MODULE_NAME_CAMEL}CreateRequest struct {
	Name   string \`json:"name" validate:"required,min=1,max=100"\`
	Status string \`json:"status" validate:"required,oneof=active inactive"\`
}

// ${MODULE_NAME_CAMEL}UpdateRequest 更新${MODULE_NAME_CAMEL}请求
type ${MODULE_NAME_CAMEL}UpdateRequest struct {
	Name   *string \`json:"name,omitempty" validate:"omitempty,min=1,max=100"\`
	Status *string \`json:"status,omitempty" validate:"omitempty,oneof=active inactive"\`
}

// ${MODULE_NAME_CAMEL}Response ${MODULE_NAME_CAMEL}响应
type ${MODULE_NAME_CAMEL}Response struct {
	ID        int       \`json:"id"\`
	Name      string    \`json:"name"\`
	Status    string    \`json:"status"\`
	CreatedAt time.Time \`json:"created_at"\`
	UpdatedAt time.Time \`json:"updated_at"\`
}

// ${MODULE_NAME_CAMEL}ListRequest ${MODULE_NAME_CAMEL}列表请求
type ${MODULE_NAME_CAMEL}ListRequest struct {
	Page     int    \`json:"page" form:"page" validate:"min=1"\`
	PageSize int    \`json:"page_size" form:"page_size" validate:"min=1,max=100"\`
	Status   string \`json:"status" form:"status" validate:"omitempty,oneof=active inactive"\`
	Keyword  string \`json:"keyword" form:"keyword" validate:"omitempty,max=100"\`
}

// ToResponse 转换为响应格式
func (u *${MODULE_NAME_CAMEL}) ToResponse() *${MODULE_NAME_CAMEL}Response {
	return &${MODULE_NAME_CAMEL}Response{
		ID:        u.ID,
		Name:      u.Name,
		Status:    u.Status,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}
EOF
    log_success "Model文件生成完成: $MODEL_FILE"
fi

# 生成Repository文件（只在api类型时生成，避免重复）
if [ "$SERVICE_TYPE" = "api" ] && [ ! -f "$REPOSITORY_FILE" ]; then
    log_step "生成Repository文件..."
    cat > "$REPOSITORY_FILE" << EOF
package repositories

import (
	"database/sql"
	"time"

	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/pkg/database"
	"{{.ProjectName}}/pkg/errors"
)

// ${MODULE_NAME_CAMEL}Repository ${MODULE_NAME_CAMEL}数据访问接口
type ${MODULE_NAME_CAMEL}Repository interface {
	// 基础CRUD操作
	Create(${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}) error
	GetByID(id int) (*models.${MODULE_NAME_CAMEL}, error)
	Update(${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}) error
	Delete(id int) error

	// 查询操作
	List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}, int, error)
	GetByStatus(status string) ([]*models.${MODULE_NAME_CAMEL}, error)
	GetByName(name string) (*models.${MODULE_NAME_CAMEL}, error)
}

// ${MODULE_NAME_LOWER}Repository ${MODULE_NAME_CAMEL}数据访问实现
type ${MODULE_NAME_LOWER}Repository struct {
	*database.BaseRepository
}

// New${MODULE_NAME_CAMEL}Repository 创建${MODULE_NAME_CAMEL}数据访问实例
func New${MODULE_NAME_CAMEL}Repository(db *database.DB) ${MODULE_NAME_CAMEL}Repository {
	return &${MODULE_NAME_LOWER}Repository{
		BaseRepository: database.NewBaseRepository(db),
	}
}

// Create 创建${MODULE_NAME_CAMEL}
func (r *${MODULE_NAME_LOWER}Repository) Create(${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}) error {
	query := \`INSERT INTO ${MODULE_NAME_LOWER}s (name, status, created_at, updated_at)
			  VALUES (?, ?, NOW(), NOW())\`

	result, err := r.GetDB().Exec(query, ${MODULE_NAME_LOWER}.Name, ${MODULE_NAME_LOWER}.Status)
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "创建${MODULE_NAME_CAMEL}失败")
	}

	id, err := result.LastInsertId()
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "获取${MODULE_NAME_CAMEL}ID失败")
	}

	${MODULE_NAME_LOWER}.ID = int(id)
	${MODULE_NAME_LOWER}.CreatedAt = time.Now()
	${MODULE_NAME_LOWER}.UpdatedAt = time.Now()
	return nil
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (r *${MODULE_NAME_LOWER}Repository) GetByID(id int) (*models.${MODULE_NAME_CAMEL}, error) {
	${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{}
	query := \`SELECT id, name, status, created_at, updated_at
			  FROM ${MODULE_NAME_LOWER}s WHERE id = ?\`

	err := r.GetDB().QueryRow(query, id).Scan(
		&${MODULE_NAME_LOWER}.ID,
		&${MODULE_NAME_LOWER}.Name,
		&${MODULE_NAME_LOWER}.Status,
		&${MODULE_NAME_LOWER}.CreatedAt,
		&${MODULE_NAME_LOWER}.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New(errors.CodeNotFound, "${MODULE_NAME_CAMEL}不存在")
		}
		return nil, errors.Wrap(errors.CodeDatabaseError, err, "查询${MODULE_NAME_CAMEL}失败")
	}

	return ${MODULE_NAME_LOWER}, nil
}

// Update 更新${MODULE_NAME_CAMEL}
func (r *${MODULE_NAME_LOWER}Repository) Update(${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}) error {
	query := \`UPDATE ${MODULE_NAME_LOWER}s SET name = ?, status = ?, updated_at = NOW()
			  WHERE id = ?\`

	result, err := r.GetDB().Exec(query, ${MODULE_NAME_LOWER}.Name, ${MODULE_NAME_LOWER}.Status, ${MODULE_NAME_LOWER}.ID)
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "更新${MODULE_NAME_CAMEL}失败")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "获取更新行数失败")
	}

	if rowsAffected == 0 {
		return errors.New(errors.CodeNotFound, "${MODULE_NAME_CAMEL}不存在")
	}

	${MODULE_NAME_LOWER}.UpdatedAt = time.Now()
	return nil
}

// Delete 删除${MODULE_NAME_CAMEL}
func (r *${MODULE_NAME_LOWER}Repository) Delete(id int) error {
	query := \`DELETE FROM ${MODULE_NAME_LOWER}s WHERE id = ?\`

	result, err := r.GetDB().Exec(query, id)
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "删除${MODULE_NAME_CAMEL}失败")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(errors.CodeDatabaseError, err, "获取删除行数失败")
	}

	if rowsAffected == 0 {
		return errors.New(errors.CodeNotFound, "${MODULE_NAME_CAMEL}不存在")
	}

	return nil
}

// List 获取${MODULE_NAME_CAMEL}列表
func (r *${MODULE_NAME_LOWER}Repository) List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}, int, error) {
	qb := database.NewQueryBuilder("SELECT id, name, status, created_at, updated_at FROM ${MODULE_NAME_LOWER}s")
	
	// 添加查询条件
	if req.Status != "" {
		qb.Where("status = ?", req.Status)
	}
	
	if req.Keyword != "" {
		qb.Where("name LIKE ?", "%"+req.Keyword+"%")
	}
	
	// 获取总数
	countQuery, countArgs := qb.Build()
	countQuery = "SELECT COUNT(*) FROM ${MODULE_NAME_LOWER}s" + countQuery[len("SELECT id, name, status, created_at, updated_at FROM ${MODULE_NAME_LOWER}s"):]
	
	var total int
	err := r.GetDB().QueryRow(countQuery, countArgs...).Scan(&total)
	if err != nil {
		return nil, 0, errors.Wrap(errors.CodeDatabaseError, err, "查询${MODULE_NAME_CAMEL}总数失败")
	}
	
	// 添加排序和分页
	qb.OrderBy("created_at DESC")
	qb.Limit(req.PageSize)
	qb.Offset((req.Page - 1) * req.PageSize)
	
	query, args := qb.Build()
	rows, err := r.GetDB().Query(query, args...)
	if err != nil {
		return nil, 0, errors.Wrap(errors.CodeDatabaseError, err, "查询${MODULE_NAME_CAMEL}列表失败")
	}
	defer rows.Close()

	var ${MODULE_NAME_LOWER}s []*models.${MODULE_NAME_CAMEL}
	for rows.Next() {
		${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{}
		err := rows.Scan(
			&${MODULE_NAME_LOWER}.ID,
			&${MODULE_NAME_LOWER}.Name,
			&${MODULE_NAME_LOWER}.Status,
			&${MODULE_NAME_LOWER}.CreatedAt,
			&${MODULE_NAME_LOWER}.UpdatedAt,
		)
		if err != nil {
			return nil, 0, errors.Wrap(errors.CodeDatabaseError, err, "扫描${MODULE_NAME_CAMEL}数据失败")
		}
		${MODULE_NAME_LOWER}s = append(${MODULE_NAME_LOWER}s, ${MODULE_NAME_LOWER})
	}

	return ${MODULE_NAME_LOWER}s, total, nil
}

// GetByStatus 根据状态获取${MODULE_NAME_CAMEL}列表
func (r *${MODULE_NAME_LOWER}Repository) GetByStatus(status string) ([]*models.${MODULE_NAME_CAMEL}, error) {
	query := \`SELECT id, name, status, created_at, updated_at
			  FROM ${MODULE_NAME_LOWER}s WHERE status = ?
			  ORDER BY created_at DESC\`

	rows, err := r.GetDB().Query(query, status)
	if err != nil {
		return nil, errors.Wrap(errors.CodeDatabaseError, err, "查询${MODULE_NAME_CAMEL}列表失败")
	}
	defer rows.Close()

	var ${MODULE_NAME_LOWER}s []*models.${MODULE_NAME_CAMEL}
	for rows.Next() {
		${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{}
		err := rows.Scan(
			&${MODULE_NAME_LOWER}.ID,
			&${MODULE_NAME_LOWER}.Name,
			&${MODULE_NAME_LOWER}.Status,
			&${MODULE_NAME_LOWER}.CreatedAt,
			&${MODULE_NAME_LOWER}.UpdatedAt,
		)
		if err != nil {
			return nil, errors.Wrap(errors.CodeDatabaseError, err, "扫描${MODULE_NAME_CAMEL}数据失败")
		}
		${MODULE_NAME_LOWER}s = append(${MODULE_NAME_LOWER}s, ${MODULE_NAME_LOWER})
	}

	return ${MODULE_NAME_LOWER}s, nil
}

// GetByName 根据名称获取${MODULE_NAME_CAMEL}
func (r *${MODULE_NAME_LOWER}Repository) GetByName(name string) (*models.${MODULE_NAME_CAMEL}, error) {
	${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{}
	query := \`SELECT id, name, status, created_at, updated_at
			  FROM ${MODULE_NAME_LOWER}s WHERE name = ?\`

	err := r.GetDB().QueryRow(query, name).Scan(
		&${MODULE_NAME_LOWER}.ID,
		&${MODULE_NAME_LOWER}.Name,
		&${MODULE_NAME_LOWER}.Status,
		&${MODULE_NAME_LOWER}.CreatedAt,
		&${MODULE_NAME_LOWER}.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New(errors.CodeNotFound, "${MODULE_NAME_CAMEL}不存在")
		}
		return nil, errors.Wrap(errors.CodeDatabaseError, err, "查询${MODULE_NAME_CAMEL}失败")
	}

	return ${MODULE_NAME_LOWER}, nil
}
EOF
    log_success "Repository文件生成完成: $REPOSITORY_FILE"
fi

# 生成Service文件
log_step "生成Service文件 ($SERVICE_TYPE)..."

# 根据服务类型生成不同的Service实现
if [ "$SERVICE_TYPE" = "api" ]; then
    # API服务实现
    cat > "$SERVICE_FILE" << EOF
package api

import (
	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/repositories"
	"{{.ProjectName}}/internal/services/shared"
	"{{.ProjectName}}/pkg/errors"
)

// ${MODULE_NAME_CAMEL}Service API ${MODULE_NAME_CAMEL}业务逻辑接口
type ${MODULE_NAME_CAMEL}Service interface {
	// C端用户操作
	Create(req *models.${MODULE_NAME_CAMEL}CreateRequest) (*models.${MODULE_NAME_CAMEL}Response, error)
	GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error)
	Update(id int, req *models.${MODULE_NAME_CAMEL}UpdateRequest) (*models.${MODULE_NAME_CAMEL}Response, error)
	Delete(id int) error
	List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}Response, int, error)

	// 业务特定操作
	GetActiveList() ([]*models.${MODULE_NAME_CAMEL}Response, error)
	Search(keyword string) ([]*models.${MODULE_NAME_CAMEL}Response, error)
}

// ${MODULE_NAME_LOWER}Service API ${MODULE_NAME_CAMEL}业务逻辑实现
type ${MODULE_NAME_LOWER}Service struct {
	${MODULE_NAME_LOWER}Repo repositories.${MODULE_NAME_CAMEL}Repository
	authService shared.AuthService
	fileService shared.FileService
}

// New${MODULE_NAME_CAMEL}Service 创建API ${MODULE_NAME_CAMEL}业务逻辑实例
func New${MODULE_NAME_CAMEL}Service(
	${MODULE_NAME_LOWER}Repo repositories.${MODULE_NAME_CAMEL}Repository,
	authService shared.AuthService,
	fileService shared.FileService,
) ${MODULE_NAME_CAMEL}Service {
	return &${MODULE_NAME_LOWER}Service{
		${MODULE_NAME_LOWER}Repo: ${MODULE_NAME_LOWER}Repo,
		authService: authService,
		fileService: fileService,
	}
}

// Create 创建${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) Create(req *models.${MODULE_NAME_CAMEL}CreateRequest) (*models.${MODULE_NAME_CAMEL}Response, error) {
	// 业务验证
	if req.Name == "" {
		return nil, errors.New(errors.CodeInvalidParams, "名称不能为空")
	}

	// 检查名称是否已存在
	existing, err := s.${MODULE_NAME_LOWER}Repo.GetByName(req.Name)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	if existing != nil {
		return nil, errors.New(errors.CodeConflict, "名称已存在")
	}

	// 创建模型
	${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{
		Name:   req.Name,
		Status: req.Status,
	}

	// 保存到数据库
	if err := s.${MODULE_NAME_LOWER}Repo.Create(${MODULE_NAME_LOWER}); err != nil {
		return nil, err
	}

	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// Update 更新${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) Update(id int, req *models.${MODULE_NAME_CAMEL}UpdateRequest) (*models.${MODULE_NAME_CAMEL}Response, error) {
	// 获取现有记录
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != nil {
		// 检查新名称是否已存在
		if *req.Name != ${MODULE_NAME_LOWER}.Name {
			existing, err := s.${MODULE_NAME_LOWER}Repo.GetByName(*req.Name)
			if err != nil && !errors.IsNotFound(err) {
				return nil, err
			}
			if existing != nil {
				return nil, errors.New(errors.CodeConflict, "名称已存在")
			}
		}
		${MODULE_NAME_LOWER}.Name = *req.Name
	}

	if req.Status != nil {
		${MODULE_NAME_LOWER}.Status = *req.Status
	}

	// 保存更新
	if err := s.${MODULE_NAME_LOWER}Repo.Update(${MODULE_NAME_LOWER}); err != nil {
		return nil, err
	}

	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// Delete 删除${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) Delete(id int) error {
	// 检查记录是否存在
	_, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return err
	}

	// 执行删除
	return s.${MODULE_NAME_LOWER}Repo.Delete(id)
}

// List 获取${MODULE_NAME_CAMEL}列表
func (s *${MODULE_NAME_LOWER}Service) List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}Response, int, error) {
	// 参数验证
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	${MODULE_NAME_LOWER}s, total, err := s.${MODULE_NAME_LOWER}Repo.List(req)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.${MODULE_NAME_CAMEL}Response, len(${MODULE_NAME_LOWER}s))
	for i, ${MODULE_NAME_LOWER} := range ${MODULE_NAME_LOWER}s {
		responses[i] = ${MODULE_NAME_LOWER}.ToResponse()
	}

	return responses, total, nil
}

// GetActiveList 获取活跃${MODULE_NAME_CAMEL}列表
func (s *${MODULE_NAME_LOWER}Service) GetActiveList() ([]*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}s, err := s.${MODULE_NAME_LOWER}Repo.GetByStatus("active")
	if err != nil {
		return nil, err
	}

	responses := make([]*models.${MODULE_NAME_CAMEL}Response, len(${MODULE_NAME_LOWER}s))
	for i, ${MODULE_NAME_LOWER} := range ${MODULE_NAME_LOWER}s {
		responses[i] = ${MODULE_NAME_LOWER}.ToResponse()
	}

	return responses, nil
}

// Search 搜索${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) Search(keyword string) ([]*models.${MODULE_NAME_CAMEL}Response, error) {
	req := &models.${MODULE_NAME_CAMEL}ListRequest{
		Page:     1,
		PageSize: 50,
		Keyword:  keyword,
	}

	${MODULE_NAME_LOWER}s, _, err := s.${MODULE_NAME_LOWER}Repo.List(req)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.${MODULE_NAME_CAMEL}Response, len(${MODULE_NAME_LOWER}s))
	for i, ${MODULE_NAME_LOWER} := range ${MODULE_NAME_LOWER}s {
		responses[i] = ${MODULE_NAME_LOWER}.ToResponse()
	}

	return responses, nil
}
EOF

elif [ "$SERVICE_TYPE" = "admin" ]; then
    # Admin服务实现
    cat > "$SERVICE_FILE" << EOF
package admin

import (
	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/repositories"
	"{{.ProjectName}}/internal/services/shared"
	"{{.ProjectName}}/pkg/errors"
)

// ${MODULE_NAME_CAMEL}ManagementService 管理后台${MODULE_NAME_CAMEL}管理业务逻辑接口
type ${MODULE_NAME_CAMEL}ManagementService interface {
	// 管理员操作
	Create(adminID int, req *models.${MODULE_NAME_CAMEL}CreateRequest) (*models.${MODULE_NAME_CAMEL}Response, error)
	GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error)
	Update(adminID int, id int, req *models.${MODULE_NAME_CAMEL}UpdateRequest) (*models.${MODULE_NAME_CAMEL}Response, error)
	Delete(adminID int, id int) error
	List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}Response, int, error)

	// 管理特定操作
	BatchUpdateStatus(adminID int, ids []int, status string) error
	BatchDelete(adminID int, ids []int) error
	Export(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]byte, error)
	GetStatistics() (map[string]interface{}, error)
}

// ${MODULE_NAME_LOWER}ManagementService 管理后台${MODULE_NAME_CAMEL}管理业务逻辑实现
type ${MODULE_NAME_LOWER}ManagementService struct {
	${MODULE_NAME_LOWER}Repo   repositories.${MODULE_NAME_CAMEL}Repository
	auditService shared.AuditService
	authService  shared.AuthService
}

// New${MODULE_NAME_CAMEL}ManagementService 创建管理后台${MODULE_NAME_CAMEL}管理业务逻辑实例
func New${MODULE_NAME_CAMEL}ManagementService(
	${MODULE_NAME_LOWER}Repo repositories.${MODULE_NAME_CAMEL}Repository,
	auditService shared.AuditService,
	authService shared.AuthService,
) ${MODULE_NAME_CAMEL}ManagementService {
	return &${MODULE_NAME_LOWER}ManagementService{
		${MODULE_NAME_LOWER}Repo:   ${MODULE_NAME_LOWER}Repo,
		auditService: auditService,
		authService:  authService,
	}
}

// Create 创建${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}ManagementService) Create(adminID int, req *models.${MODULE_NAME_CAMEL}CreateRequest) (*models.${MODULE_NAME_CAMEL}Response, error) {
	// 业务验证
	if req.Name == "" {
		return nil, errors.New(errors.CodeInvalidParams, "名称不能为空")
	}

	// 检查名称是否已存在
	existing, err := s.${MODULE_NAME_LOWER}Repo.GetByName(req.Name)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	if existing != nil {
		return nil, errors.New(errors.CodeConflict, "名称已存在")
	}

	// 创建模型
	${MODULE_NAME_LOWER} := &models.${MODULE_NAME_CAMEL}{
		Name:   req.Name,
		Status: req.Status,
	}

	// 保存到数据库
	if err := s.${MODULE_NAME_LOWER}Repo.Create(${MODULE_NAME_LOWER}); err != nil {
		return nil, err
	}

	// 记录审计日志
	s.auditService.LogAction(adminID, "create_${MODULE_NAME_LOWER}", map[string]interface{}{
		"${MODULE_NAME_LOWER}_id": ${MODULE_NAME_LOWER}.ID,
		"name":        req.Name,
		"status":      req.Status,
	})

	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}ManagementService) GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// Update 更新${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}ManagementService) Update(adminID int, id int, req *models.${MODULE_NAME_CAMEL}UpdateRequest) (*models.${MODULE_NAME_CAMEL}Response, error) {
	// 获取现有记录
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 记录原始值用于审计
	originalData := map[string]interface{}{
		"name":   ${MODULE_NAME_LOWER}.Name,
		"status": ${MODULE_NAME_LOWER}.Status,
	}

	// 更新字段
	if req.Name != nil {
		if *req.Name != ${MODULE_NAME_LOWER}.Name {
			existing, err := s.${MODULE_NAME_LOWER}Repo.GetByName(*req.Name)
			if err != nil && !errors.IsNotFound(err) {
				return nil, err
			}
			if existing != nil {
				return nil, errors.New(errors.CodeConflict, "名称已存在")
			}
		}
		${MODULE_NAME_LOWER}.Name = *req.Name
	}

	if req.Status != nil {
		${MODULE_NAME_LOWER}.Status = *req.Status
	}

	// 保存更新
	if err := s.${MODULE_NAME_LOWER}Repo.Update(${MODULE_NAME_LOWER}); err != nil {
		return nil, err
	}

	// 记录审计日志
	s.auditService.LogAction(adminID, "update_${MODULE_NAME_LOWER}", map[string]interface{}{
		"${MODULE_NAME_LOWER}_id": id,
		"original":    originalData,
		"updated": map[string]interface{}{
			"name":   ${MODULE_NAME_LOWER}.Name,
			"status": ${MODULE_NAME_LOWER}.Status,
		},
	})

	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// Delete 删除${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}ManagementService) Delete(adminID int, id int) error {
	// 检查记录是否存在
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return err
	}

	// 执行删除
	if err := s.${MODULE_NAME_LOWER}Repo.Delete(id); err != nil {
		return err
	}

	// 记录审计日志
	s.auditService.LogAction(adminID, "delete_${MODULE_NAME_LOWER}", map[string]interface{}{
		"${MODULE_NAME_LOWER}_id": id,
		"name":        ${MODULE_NAME_LOWER}.Name,
	})

	return nil
}

// List 获取${MODULE_NAME_CAMEL}列表
func (s *${MODULE_NAME_LOWER}ManagementService) List(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]*models.${MODULE_NAME_CAMEL}Response, int, error) {
	// 参数验证
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 10
	}

	${MODULE_NAME_LOWER}s, total, err := s.${MODULE_NAME_LOWER}Repo.List(req)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]*models.${MODULE_NAME_CAMEL}Response, len(${MODULE_NAME_LOWER}s))
	for i, ${MODULE_NAME_LOWER} := range ${MODULE_NAME_LOWER}s {
		responses[i] = ${MODULE_NAME_LOWER}.ToResponse()
	}

	return responses, total, nil
}

// BatchUpdateStatus 批量更新状态
func (s *${MODULE_NAME_LOWER}ManagementService) BatchUpdateStatus(adminID int, ids []int, status string) error {
	if len(ids) == 0 {
		return errors.New(errors.CodeInvalidParams, "ID列表不能为空")
	}

	// 验证状态值
	if status != "active" && status != "inactive" {
		return errors.New(errors.CodeInvalidParams, "无效的状态值")
	}

	// 批量更新
	for _, id := range ids {
		${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
		if err != nil {
			continue // 跳过不存在的记录
		}

		${MODULE_NAME_LOWER}.Status = status
		if err := s.${MODULE_NAME_LOWER}Repo.Update(${MODULE_NAME_LOWER}); err != nil {
			continue // 跳过更新失败的记录
		}
	}

	// 记录审计日志
	s.auditService.LogAction(adminID, "batch_update_${MODULE_NAME_LOWER}_status", map[string]interface{}{
		"ids":    ids,
		"status": status,
	})

	return nil
}

// BatchDelete 批量删除
func (s *${MODULE_NAME_LOWER}ManagementService) BatchDelete(adminID int, ids []int) error {
	if len(ids) == 0 {
		return errors.New(errors.CodeInvalidParams, "ID列表不能为空")
	}

	// 批量删除
	deletedIDs := make([]int, 0)
	for _, id := range ids {
		if err := s.${MODULE_NAME_LOWER}Repo.Delete(id); err == nil {
			deletedIDs = append(deletedIDs, id)
		}
	}

	// 记录审计日志
	s.auditService.LogAction(adminID, "batch_delete_${MODULE_NAME_LOWER}", map[string]interface{}{
		"requested_ids": ids,
		"deleted_ids":   deletedIDs,
	})

	return nil
}

// Export 导出数据
func (s *${MODULE_NAME_LOWER}ManagementService) Export(req *models.${MODULE_NAME_CAMEL}ListRequest) ([]byte, error) {
	// 获取所有数据
	req.Page = 1
	req.PageSize = 10000 // 设置一个较大的值

	${MODULE_NAME_LOWER}s, _, err := s.${MODULE_NAME_LOWER}Repo.List(req)
	if err != nil {
		return nil, err
	}

	// 这里应该实现具体的导出逻辑（CSV、Excel等）
	// 暂时返回空数据
	return []byte{}, errors.New(errors.CodeNotImplemented, "导出功能未实现")
}

// GetStatistics 获取统计信息
func (s *${MODULE_NAME_LOWER}ManagementService) GetStatistics() (map[string]interface{}, error) {
	// 获取各状态的数量
	active${MODULE_NAME_CAMEL}s, err := s.${MODULE_NAME_LOWER}Repo.GetByStatus("active")
	if err != nil {
		return nil, err
	}

	inactive${MODULE_NAME_CAMEL}s, err := s.${MODULE_NAME_LOWER}Repo.GetByStatus("inactive")
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":    len(active${MODULE_NAME_CAMEL}s) + len(inactive${MODULE_NAME_CAMEL}s),
		"active_count":   len(active${MODULE_NAME_CAMEL}s),
		"inactive_count": len(inactive${MODULE_NAME_CAMEL}s),
	}, nil
}
EOF

else
    # Shared服务实现
    cat > "$SERVICE_FILE" << EOF
package shared

import (
	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/repositories"
	"{{.ProjectName}}/pkg/errors"
)

// ${MODULE_NAME_CAMEL}Service 共享${MODULE_NAME_CAMEL}业务逻辑接口
type ${MODULE_NAME_CAMEL}Service interface {
	// 通用操作
	GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error)
	GetByName(name string) (*models.${MODULE_NAME_CAMEL}Response, error)
	GetActiveList() ([]*models.${MODULE_NAME_CAMEL}Response, error)

	// 缓存操作
	GetFromCache(key string) (*models.${MODULE_NAME_CAMEL}Response, error)
	SetToCache(key string, ${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}Response) error
	ClearCache(key string) error
}

// ${MODULE_NAME_LOWER}Service 共享${MODULE_NAME_CAMEL}业务逻辑实现
type ${MODULE_NAME_LOWER}Service struct {
	${MODULE_NAME_LOWER}Repo repositories.${MODULE_NAME_CAMEL}Repository
	// cacheService CacheService // 如果需要缓存功能
}

// New${MODULE_NAME_CAMEL}Service 创建共享${MODULE_NAME_CAMEL}业务逻辑实例
func New${MODULE_NAME_CAMEL}Service(
	${MODULE_NAME_LOWER}Repo repositories.${MODULE_NAME_CAMEL}Repository,
) ${MODULE_NAME_CAMEL}Service {
	return &${MODULE_NAME_LOWER}Service{
		${MODULE_NAME_LOWER}Repo: ${MODULE_NAME_LOWER}Repo,
	}
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) GetByID(id int) (*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// GetByName 根据名称获取${MODULE_NAME_CAMEL}
func (s *${MODULE_NAME_LOWER}Service) GetByName(name string) (*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}, err := s.${MODULE_NAME_LOWER}Repo.GetByName(name)
	if err != nil {
		return nil, err
	}
	return ${MODULE_NAME_LOWER}.ToResponse(), nil
}

// GetActiveList 获取活跃${MODULE_NAME_CAMEL}列表
func (s *${MODULE_NAME_LOWER}Service) GetActiveList() ([]*models.${MODULE_NAME_CAMEL}Response, error) {
	${MODULE_NAME_LOWER}s, err := s.${MODULE_NAME_LOWER}Repo.GetByStatus("active")
	if err != nil {
		return nil, err
	}

	responses := make([]*models.${MODULE_NAME_CAMEL}Response, len(${MODULE_NAME_LOWER}s))
	for i, ${MODULE_NAME_LOWER} := range ${MODULE_NAME_LOWER}s {
		responses[i] = ${MODULE_NAME_LOWER}.ToResponse()
	}

	return responses, nil
}

// GetFromCache 从缓存获取数据
func (s *${MODULE_NAME_LOWER}Service) GetFromCache(key string) (*models.${MODULE_NAME_CAMEL}Response, error) {
	// TODO: 实现缓存逻辑
	return nil, errors.New(errors.CodeNotImplemented, "缓存功能未实现")
}

// SetToCache 设置缓存数据
func (s *${MODULE_NAME_LOWER}Service) SetToCache(key string, ${MODULE_NAME_LOWER} *models.${MODULE_NAME_CAMEL}Response) error {
	// TODO: 实现缓存逻辑
	return errors.New(errors.CodeNotImplemented, "缓存功能未实现")
}

// ClearCache 清除缓存
func (s *${MODULE_NAME_LOWER}Service) ClearCache(key string) error {
	// TODO: 实现缓存逻辑
	return errors.New(errors.CodeNotImplemented, "缓存功能未实现")
}
EOF
fi

log_success "Service文件生成完成: $SERVICE_FILE"

# 生成Handler文件
log_step "生成Handler文件 ($SERVICE_TYPE)..."

if [ "$SERVICE_TYPE" = "api" ]; then
    # API Handler实现
    cat > "$HANDLER_FILE" << EOF
package api

import (
	"strconv"

	"{{.ProjectName}}/internal/middleware"
	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/services/api"
	"{{.ProjectName}}/pkg/errors"
	"{{.ProjectName}}/pkg/response"
	"{{.ProjectName}}/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ${MODULE_NAME_CAMEL}Handler API ${MODULE_NAME_CAMEL}处理器
type ${MODULE_NAME_CAMEL}Handler struct {
	${MODULE_NAME_LOWER}Service api.${MODULE_NAME_CAMEL}Service
}

// New${MODULE_NAME_CAMEL}Handler 创建API ${MODULE_NAME_CAMEL}处理器实例
func New${MODULE_NAME_CAMEL}Handler(${MODULE_NAME_LOWER}Service api.${MODULE_NAME_CAMEL}Service) *${MODULE_NAME_CAMEL}Handler {
	return &${MODULE_NAME_CAMEL}Handler{
		${MODULE_NAME_LOWER}Service: ${MODULE_NAME_LOWER}Service,
	}
}

// Create 创建${MODULE_NAME_CAMEL}
// @Summary 创建${MODULE_NAME_CAMEL}
// @Description 创建新的${MODULE_NAME_CAMEL}
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param request body models.${MODULE_NAME_CAMEL}CreateRequest true "创建请求"
// @Success 200 {object} response.Response{data=models.${MODULE_NAME_CAMEL}Response}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s [post]
// @Security BearerAuth
func (h *${MODULE_NAME_CAMEL}Handler) Create(c *gin.Context) {
	var req models.${MODULE_NAME_CAMEL}CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.Create(&req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}创建成功", result)
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
// @Summary 获取${MODULE_NAME_CAMEL}详情
// @Description 根据ID获取${MODULE_NAME_CAMEL}详情
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param id path int true "${MODULE_NAME_CAMEL}ID"
// @Success 200 {object} response.Response{data=models.${MODULE_NAME_CAMEL}Response}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s/{id} [get]
func (h *${MODULE_NAME_CAMEL}Handler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.GetByID(id)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, result)
}

// Update 更新${MODULE_NAME_CAMEL}
// @Summary 更新${MODULE_NAME_CAMEL}
// @Description 更新${MODULE_NAME_CAMEL}信息
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param id path int true "${MODULE_NAME_CAMEL}ID"
// @Param request body models.${MODULE_NAME_CAMEL}UpdateRequest true "更新请求"
// @Success 200 {object} response.Response{data=models.${MODULE_NAME_CAMEL}Response}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s/{id} [put]
// @Security BearerAuth
func (h *${MODULE_NAME_CAMEL}Handler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	var req models.${MODULE_NAME_CAMEL}UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.Update(id, &req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}更新成功", result)
}

// Delete 删除${MODULE_NAME_CAMEL}
// @Summary 删除${MODULE_NAME_CAMEL}
// @Description 删除${MODULE_NAME_CAMEL}
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param id path int true "${MODULE_NAME_CAMEL}ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s/{id} [delete]
// @Security BearerAuth
func (h *${MODULE_NAME_CAMEL}Handler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	if err := h.${MODULE_NAME_LOWER}Service.Delete(id); err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}删除成功", nil)
}

// List 获取${MODULE_NAME_CAMEL}列表
// @Summary 获取${MODULE_NAME_CAMEL}列表
// @Description 分页获取${MODULE_NAME_CAMEL}列表
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param status query string false "状态筛选"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} response.Response{data=response.PaginationResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s [get]
func (h *${MODULE_NAME_CAMEL}Handler) List(c *gin.Context) {
	var req models.${MODULE_NAME_CAMEL}ListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	list, total, err := h.${MODULE_NAME_LOWER}Service.List(&req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithPagination(c, list, int64(total), req.Page, req.PageSize)
}

// GetActiveList 获取活跃${MODULE_NAME_CAMEL}列表
// @Summary 获取活跃${MODULE_NAME_CAMEL}列表
// @Description 获取所有活跃状态的${MODULE_NAME_CAMEL}
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]models.${MODULE_NAME_CAMEL}Response}
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s/active [get]
func (h *${MODULE_NAME_CAMEL}Handler) GetActiveList(c *gin.Context) {
	list, err := h.${MODULE_NAME_LOWER}Service.GetActiveList()
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, list)
}

// Search 搜索${MODULE_NAME_CAMEL}
// @Summary 搜索${MODULE_NAME_CAMEL}
// @Description 根据关键词搜索${MODULE_NAME_CAMEL}
// @Tags ${MODULE_NAME_CAMEL}
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Success 200 {object} response.Response{data=[]models.${MODULE_NAME_CAMEL}Response}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/${MODULE_NAME_LOWER}s/search [get]
func (h *${MODULE_NAME_CAMEL}Handler) Search(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "搜索关键词不能为空")
		return
	}

	list, err := h.${MODULE_NAME_LOWER}Service.Search(keyword)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, list)
}

// RegisterRoutes 注册路由
func (h *${MODULE_NAME_CAMEL}Handler) RegisterRoutes(router *gin.RouterGroup, middleware *middleware.MiddlewareManager) {
	${MODULE_NAME_LOWER}Group := router.Group("/${MODULE_NAME_LOWER}s")
	{
		// 公开接口
		${MODULE_NAME_LOWER}Group.GET("", h.List)
		${MODULE_NAME_LOWER}Group.GET("/active", h.GetActiveList)
		${MODULE_NAME_LOWER}Group.GET("/search", h.Search)
		${MODULE_NAME_LOWER}Group.GET("/:id", h.GetByID)

		// 需要认证的接口
		authGroup := ${MODULE_NAME_LOWER}Group.Group("")
		authGroup.Use(middleware.APIAuth())
		{
			authGroup.POST("", h.Create)
			authGroup.PUT("/:id", h.Update)
			authGroup.DELETE("/:id", h.Delete)
		}
	}
}
EOF

elif [ "$SERVICE_TYPE" = "admin" ]; then
    # Admin Handler实现
    cat > "$HANDLER_FILE" << EOF
package admin

import (
	"strconv"
	"strings"

	"{{.ProjectName}}/internal/middleware"
	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/services/admin"
	"{{.ProjectName}}/pkg/errors"
	"{{.ProjectName}}/pkg/response"
	"{{.ProjectName}}/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ${MODULE_NAME_CAMEL}ManagementHandler 管理后台${MODULE_NAME_CAMEL}管理处理器
type ${MODULE_NAME_CAMEL}ManagementHandler struct {
	${MODULE_NAME_LOWER}Service admin.${MODULE_NAME_CAMEL}ManagementService
}

// New${MODULE_NAME_CAMEL}ManagementHandler 创建管理后台${MODULE_NAME_CAMEL}管理处理器实例
func New${MODULE_NAME_CAMEL}ManagementHandler(${MODULE_NAME_LOWER}Service admin.${MODULE_NAME_CAMEL}ManagementService) *${MODULE_NAME_CAMEL}ManagementHandler {
	return &${MODULE_NAME_CAMEL}ManagementHandler{
		${MODULE_NAME_LOWER}Service: ${MODULE_NAME_LOWER}Service,
	}
}

// Create 创建${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}ManagementHandler) Create(c *gin.Context) {
	adminID, err := middleware.GetAdminID(c)
	if err != nil {
		response.Error(c, err)
		return
	}

	var req models.${MODULE_NAME_CAMEL}CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.Create(adminID, &req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}创建成功", result)
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}ManagementHandler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.GetByID(id)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, result)
}

// Update 更新${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}ManagementHandler) Update(c *gin.Context) {
	adminID, err := middleware.GetAdminID(c)
	if err != nil {
		response.Error(c, err)
		return
	}

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	var req models.${MODULE_NAME_CAMEL}UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.Update(adminID, id, &req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}更新成功", result)
}

// Delete 删除${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}ManagementHandler) Delete(c *gin.Context) {
	adminID, err := middleware.GetAdminID(c)
	if err != nil {
		response.Error(c, err)
		return
	}

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	if err := h.${MODULE_NAME_LOWER}Service.Delete(adminID, id); err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "${MODULE_NAME_CAMEL}删除成功", nil)
}

// List 获取${MODULE_NAME_CAMEL}列表
func (h *${MODULE_NAME_CAMEL}ManagementHandler) List(c *gin.Context) {
	var req models.${MODULE_NAME_CAMEL}ListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	list, total, err := h.${MODULE_NAME_LOWER}Service.List(&req)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithPagination(c, list, int64(total), req.Page, req.PageSize)
}

// BatchUpdateStatus 批量更新状态
func (h *${MODULE_NAME_CAMEL}ManagementHandler) BatchUpdateStatus(c *gin.Context) {
	adminID, err := middleware.GetAdminID(c)
	if err != nil {
		response.Error(c, err)
		return
	}

	var req struct {
		IDs    []int  \`json:"ids" validate:"required,min=1"\`
		Status string \`json:"status" validate:"required,oneof=active inactive"\`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	if err := h.${MODULE_NAME_LOWER}Service.BatchUpdateStatus(adminID, req.IDs, req.Status); err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "批量更新状态成功", nil)
}

// BatchDelete 批量删除
func (h *${MODULE_NAME_CAMEL}ManagementHandler) BatchDelete(c *gin.Context) {
	adminID, err := middleware.GetAdminID(c)
	if err != nil {
		response.Error(c, err)
		return
	}

	var req struct {
		IDs []int \`json:"ids" validate:"required,min=1"\`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	if err := h.${MODULE_NAME_LOWER}Service.BatchDelete(adminID, req.IDs); err != nil {
		response.Error(c, err)
		return
	}

	response.SuccessWithMessage(c, "批量删除成功", nil)
}

// Export 导出数据
func (h *${MODULE_NAME_CAMEL}ManagementHandler) Export(c *gin.Context) {
	var req models.${MODULE_NAME_CAMEL}ListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationError(c, validator.GetValidationErrors(err))
		return
	}

	data, err := h.${MODULE_NAME_LOWER}Service.Export(&req)
	if err != nil {
		response.Error(c, err)
		return
	}

	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", "attachment; filename=${MODULE_NAME_LOWER}s.csv")
	c.Data(200, "application/octet-stream", data)
}

// GetStatistics 获取统计信息
func (h *${MODULE_NAME_CAMEL}ManagementHandler) GetStatistics(c *gin.Context) {
	stats, err := h.${MODULE_NAME_LOWER}Service.GetStatistics()
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, stats)
}

// RegisterRoutes 注册路由
func (h *${MODULE_NAME_CAMEL}ManagementHandler) RegisterRoutes(router *gin.RouterGroup, middleware *middleware.MiddlewareManager) {
	${MODULE_NAME_LOWER}Group := router.Group("/${MODULE_NAME_LOWER}s")
	${MODULE_NAME_LOWER}Group.Use(middleware.AdminAuth())
	{
		${MODULE_NAME_LOWER}Group.GET("", h.List)
		${MODULE_NAME_LOWER}Group.GET("/statistics", h.GetStatistics)
		${MODULE_NAME_LOWER}Group.GET("/export", h.Export)
		${MODULE_NAME_LOWER}Group.GET("/:id", h.GetByID)
		${MODULE_NAME_LOWER}Group.POST("", h.Create)
		${MODULE_NAME_LOWER}Group.PUT("/:id", h.Update)
		${MODULE_NAME_LOWER}Group.DELETE("/:id", h.Delete)
		${MODULE_NAME_LOWER}Group.POST("/batch/status", h.BatchUpdateStatus)
		${MODULE_NAME_LOWER}Group.POST("/batch/delete", h.BatchDelete)
	}
}
EOF

else
    # Shared Handler (通常不需要，但为了完整性)
    cat > "$HANDLER_FILE" << EOF
package shared

import (
	"strconv"

	"{{.ProjectName}}/internal/models"
	"{{.ProjectName}}/internal/services/shared"
	"{{.ProjectName}}/pkg/errors"
	"{{.ProjectName}}/pkg/response"

	"github.com/gin-gonic/gin"
)

// ${MODULE_NAME_CAMEL}Handler 共享${MODULE_NAME_CAMEL}处理器
type ${MODULE_NAME_CAMEL}Handler struct {
	${MODULE_NAME_LOWER}Service shared.${MODULE_NAME_CAMEL}Service
}

// New${MODULE_NAME_CAMEL}Handler 创建共享${MODULE_NAME_CAMEL}处理器实例
func New${MODULE_NAME_CAMEL}Handler(${MODULE_NAME_LOWER}Service shared.${MODULE_NAME_CAMEL}Service) *${MODULE_NAME_CAMEL}Handler {
	return &${MODULE_NAME_CAMEL}Handler{
		${MODULE_NAME_LOWER}Service: ${MODULE_NAME_LOWER}Service,
	}
}

// GetByID 根据ID获取${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}Handler) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "无效的ID格式")
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.GetByID(id)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, result)
}

// GetByName 根据名称获取${MODULE_NAME_CAMEL}
func (h *${MODULE_NAME_CAMEL}Handler) GetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		response.ErrorWithCode(c, errors.CodeInvalidParams, "名称不能为空")
		return
	}

	result, err := h.${MODULE_NAME_LOWER}Service.GetByName(name)
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, result)
}

// GetActiveList 获取活跃${MODULE_NAME_CAMEL}列表
func (h *${MODULE_NAME_CAMEL}Handler) GetActiveList(c *gin.Context) {
	list, err := h.${MODULE_NAME_LOWER}Service.GetActiveList()
	if err != nil {
		response.Error(c, err)
		return
	}

	response.Success(c, list)
}
EOF
fi

log_success "Handler文件生成完成: $HANDLER_FILE"

# 生成完成总结
log_step "模块生成完成！"
echo ""
log_success "✅ 已生成的文件:"

if [ "$SERVICE_TYPE" = "api" ]; then
    if [ -f "$MODEL_FILE" ]; then
        log_info "  📄 Model: $MODEL_FILE"
    fi
    if [ -f "$REPOSITORY_FILE" ]; then
        log_info "  🗄️  Repository: $REPOSITORY_FILE"
    fi
fi

log_info "  ⚙️  Service: $SERVICE_FILE"
log_info "  🌐 Handler: $HANDLER_FILE"

echo ""
log_step "📋 下一步操作建议:"

if [ "$SERVICE_TYPE" = "api" ]; then
    log_info "1. 创建数据库迁移:"
    log_info "   make migrate-create name=create_${MODULE_NAME_LOWER}s_table"
    echo ""
    log_info "2. 在路由配置中注册Handler:"
    log_info "   编辑 configs/api/routes.go"
    echo ""
    log_info "3. 在依赖注入容器中注册服务:"
    log_info "   编辑 cmd/api-server/main.go"
    echo ""
elif [ "$SERVICE_TYPE" = "admin" ]; then
    log_info "1. 在管理后台路由配置中注册Handler:"
    log_info "   编辑 configs/admin/routes.go"
    echo ""
    log_info "2. 在依赖注入容器中注册服务:"
    log_info "   编辑 cmd/admin-server/main.go"
    echo ""
else
    log_info "1. 在共享服务中注册:"
    log_info "   编辑相关的依赖注入配置"
    echo ""
fi

log_info "4. 生成测试文件:"
log_info "   ./generate-test.sh $MODULE_NAME $SERVICE_TYPE"
echo ""

log_info "5. 运行MVS检查:"
log_info "   ./tools/mvs-checker/mvs-check.sh"
echo ""

log_mvs "🎯 MVS提醒: 请确保遵循MVS核心规则完成后续开发！"
