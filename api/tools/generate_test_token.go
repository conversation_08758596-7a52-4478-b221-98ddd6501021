package main

import (
	"fmt"
	"log"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明
type Claims struct {
	UserID   uint64 `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	UserType uint8  `json:"user_type"`
	jwt.RegisteredClaims
}

func generateTestToken() {
	// 使用配置文件中的secret
	secretKey := "kids-platform-jwt-secret-key-2025-production-ready"

	// 创建测试用户的claims
	claims := &Claims{
		UserID:   1,
		Username: "test_user",
		Role:     "user",
		UserType: 1,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "kids-platform",
			Subject:   "1",
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名token
	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		log.Fatal("Failed to sign token:", err)
	}

	fmt.Println("Generated JWT Token:")
	fmt.Println(tokenString)
	fmt.Println()
	fmt.Println("Use this token in Authorization header:")
	fmt.Printf("Authorization: Bearer %s\n", tokenString)
}
