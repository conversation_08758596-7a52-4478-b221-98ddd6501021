package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 数据库连接配置
	dsn := "admin:235lwx123456@tcp(47.120.16.235:3306)/hop_planet_dev?charset=utf8mb4&parseTime=True&loc=Local"

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("Connected to database successfully!")

	// 检查camp_checkin_dates表是否存在
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'hop_planet_dev' AND table_name = 'camp_checkin_dates'").Scan(&count).Error
	if err != nil {
		log.Fatal("Failed to check table existence:", err)
	}

	if count == 0 {
		fmt.Println("camp_checkin_dates table does not exist. Creating...")

		// 创建camp_checkin_dates表
		createTableSQL := `
		CREATE TABLE camp_checkin_dates (
			id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
			participation_id bigint unsigned NOT NULL COMMENT '参与记录ID，关联user_camp_participations表',
			day_number int unsigned NOT NULL COMMENT '训练营天数（第几天），从1开始',
			checkin_date date NOT NULL COMMENT '打卡日期',
			status tinyint unsigned DEFAULT 1 COMMENT '状态：1=待打卡，2=已完成，3=补打卡，4=已跳过',
			date_type tinyint unsigned DEFAULT 1 COMMENT '日期类型：1=工作日，2=周末，3=节假日',
			created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			deleted_at timestamp NULL DEFAULT NULL COMMENT '软删除时间',
			PRIMARY KEY (id),
			UNIQUE KEY uk_participation_day (participation_id, day_number) COMMENT '同一参与记录的天数唯一',
			KEY idx_participation_id (participation_id) COMMENT '参与记录ID索引',
			KEY idx_checkin_date (checkin_date) COMMENT '打卡日期索引',
			KEY idx_status (status) COMMENT '状态索引'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营打卡日期表';`

		err = db.Exec(createTableSQL).Error
		if err != nil {
			log.Fatal("Failed to create camp_checkin_dates table:", err)
		}
		fmt.Println("camp_checkin_dates table created successfully!")
	} else {
		fmt.Println("camp_checkin_dates table already exists.")
	}

	// 检查user_camp_participations表是否需要添加新字段
	var columnCount int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'hop_planet_dev' AND table_name = 'user_camp_participations' AND column_name = 'makeup_total_count'").Scan(&columnCount).Error
	if err != nil {
		log.Fatal("Failed to check column existence:", err)
	}

	if columnCount == 0 {
		fmt.Println("Adding new columns to user_camp_participations table...")

		alterTableSQL := `
		ALTER TABLE user_camp_participations 
		ADD COLUMN camp_title varchar(100) DEFAULT '' COMMENT '训练营标题（冗余字段）',
		ADD COLUMN camp_subtitle varchar(200) DEFAULT '' COMMENT '训练营副标题（冗余字段）',
		ADD COLUMN total_checkin_days int unsigned DEFAULT 0 COMMENT '总打卡天数（冗余字段）',
		ADD COLUMN completed_checkin_days int unsigned DEFAULT 0 COMMENT '已完成打卡天数（冗余字段）',
		ADD COLUMN makeup_used_count int unsigned DEFAULT 0 COMMENT '已使用补打卡次数',
		ADD COLUMN makeup_total_count int unsigned DEFAULT 3 COMMENT '总补打卡次数';`

		err = db.Exec(alterTableSQL).Error
		if err != nil {
			log.Fatal("Failed to alter user_camp_participations table:", err)
		}
		fmt.Println("user_camp_participations table updated successfully!")
	} else {
		fmt.Println("user_camp_participations table already has new columns.")
	}

	// 检查是否已有测试数据
	var participationCount int64
	err = db.Raw("SELECT COUNT(*) FROM user_camp_participations WHERE id IN (1, 2, 3)").Scan(&participationCount).Error
	if err != nil {
		log.Fatal("Failed to check test data:", err)
	}

	if participationCount == 0 {
		fmt.Println("Inserting test data...")

		// 读取并执行测试数据SQL文件
		sqlFile := "../docs/02-design/database/test_data_camp_checkin.sql"
		sqlContent, err := os.ReadFile(sqlFile)
		if err != nil {
			log.Fatal("Failed to read SQL file:", err)
		}

		// 执行SQL（简单版本，实际应该分割SQL语句）
		err = db.Exec(string(sqlContent)).Error
		if err != nil {
			log.Printf("Warning: Failed to execute test data SQL: %v", err)
			fmt.Println("Manually inserting basic test data...")

			// 手动插入基本测试数据
			basicTestSQL := `
			INSERT INTO user_camp_participations (
				id, camp_id, user_id, child_id, participation_status, participation_date,
				current_day, progress_percentage, total_checkins, consecutive_days,
				total_study_minutes, rating, review_text,
				camp_title, camp_subtitle, total_checkin_days, completed_checkin_days,
				makeup_used_count, makeup_total_count,
				created_at, updated_at
			) VALUES (
				1, 1, 1, 1, 1, '2024-01-15',
				14, 66.67, 12, 10,
				420, 0, NULL,
				'21天跳绳挑战', '从零基础到连续跳绳300个', 21, 12,
				1, 3,
				'2024-01-15 08:00:00', '2024-01-28 20:30:00'
			) ON DUPLICATE KEY UPDATE id=id;`

			err = db.Exec(basicTestSQL).Error
			if err != nil {
				log.Fatal("Failed to insert basic test data:", err)
			}
		}
		fmt.Println("Test data inserted successfully!")
	} else {
		fmt.Println("Test data already exists.")
	}

	fmt.Println("Database setup completed successfully!")
}
