// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.example.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/points/add": {
            "post": {
                "description": "为用户添加积分（管理员接口）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "积分管理"
                ],
                "summary": "添加积分",
                "parameters": [
                    {
                        "description": "添加积分请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.AddPointsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/admin/points/update-ranking": {
            "post": {
                "description": "更新周排名（管理员接口）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "积分管理"
                ],
                "summary": "更新周排名",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/admin/user/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员根据用户ID获取用户详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "根据ID获取用户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UsersResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "权限不足",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/auth/wechat/login": {
            "post": {
                "description": "通过微信授权码进行登录，支持静默登录和获取用户信息登录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户认证"
                ],
                "summary": "微信登录",
                "parameters": [
                    {
                        "description": "微信登录请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.WechatLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.WechatLoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "微信授权失败",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/checkins": {
            "post": {
                "description": "用户创建训练打卡记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打卡管理"
                ],
                "summary": "创建打卡记录",
                "parameters": [
                    {
                        "description": "打卡请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.CheckinCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CheckinResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/checkins/history": {
            "get": {
                "description": "获取用户在指定训练营的打卡历史记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打卡管理"
                ],
                "summary": "获取打卡历史",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "camp_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CheckinHistoryResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/checkins/stats": {
            "get": {
                "description": "获取用户在指定训练营的打卡统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打卡管理"
                ],
                "summary": "获取打卡统计",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "camp_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CheckinStatsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/checkins/today": {
            "get": {
                "description": "获取用户今日的打卡状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "打卡管理"
                ],
                "summary": "获取今日打卡状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "camp_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.TodayCheckinStatus"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/children": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户管理的所有孩子列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "获取孩子列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.ChildrenResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为当前用户创建新的孩子档案",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "创建孩子档案",
                "parameters": [
                    {
                        "description": "创建孩子档案请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ChildrenCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ChildrenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/children/current": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取用户当前选择的孩子信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "获取当前选择的孩子",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ChildrenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "当前孩子不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/children/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定孩子的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "获取孩子详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ChildrenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "孩子不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新指定孩子的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "更新孩子信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新孩子信息请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ChildrenUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ChildrenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "孩子不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "软删除指定的孩子档案",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "删除孩子档案",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "孩子不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/children/{id}/select": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "设置用户当前选择的孩子",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "孩子管理"
                ],
                "summary": "选择当前孩子",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "选择成功",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "403": {
                        "description": "无权限访问",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "孩子不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/content/camps": {
            "get": {
                "description": "获取训练营列表，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容管理"
                ],
                "summary": "获取训练营列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.TrainingCampsListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/content/camps/{id}": {
            "get": {
                "description": "获取训练营详情信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容管理"
                ],
                "summary": "获取训练营详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.TrainingCampDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/content/camps/{id}/join": {
            "post": {
                "description": "用户参与指定的训练营",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "内容管理"
                ],
                "summary": "参与训练营",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CampJoinResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/contracts": {
            "get": {
                "description": "获取用户的契约列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "获取契约列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "description": "契约状态",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/api.ContractResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "description": "创建新的家庭契约",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "创建家庭契约",
                "parameters": [
                    {
                        "description": "契约创建请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.ContractCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.ContractResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/contracts/stats": {
            "get": {
                "description": "获取用户的契约统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "获取契约统计",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.ContractStatsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/contracts/{id}": {
            "get": {
                "description": "获取指定契约的详情信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "获取契约详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "契约ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.ContractDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/contracts/{id}/complete": {
            "post": {
                "description": "标记契约为完成状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "完成契约",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "契约ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.ContractCompleteResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/contracts/{id}/progress": {
            "put": {
                "description": "更新契约的完成进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "契约管理"
                ],
                "summary": "更新契约进度",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "契约ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "进度更新请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.UpdateProgressRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/leaderboard": {
            "get": {
                "description": "获取积分排行榜",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "积分管理"
                ],
                "summary": "获取排行榜",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "排行榜数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.LeaderboardResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/camps": {
            "get": {
                "description": "获取用户参与的训练营列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户训练营列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/api.UserCampResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/camps/{id}/progress": {
            "get": {
                "description": "获取用户在指定训练营的进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取训练营进度",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "训练营ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CampProgressResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/growth": {
            "get": {
                "description": "获取成长页面的完整数据，包括统计、训练营、契约、成长轨迹等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "成长管理"
                ],
                "summary": "获取成长页面数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.GrowthPageResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/growth/stats": {
            "get": {
                "description": "获取用户的详细成长统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "成长管理"
                ],
                "summary": "获取成长统计",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.GrowthStatsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/growth/today": {
            "get": {
                "description": "获取用户今日的状态信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "成长管理"
                ],
                "summary": "获取今日状态",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.TodayStatusInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/growth/track": {
            "get": {
                "description": "获取用户的成长轨迹记录",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "成长管理"
                ],
                "summary": "获取成长轨迹",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/api.GrowthTrackItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/info": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UsersResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "description": "用户更新请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UsersUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UsersResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/response.Response"
                        }
                    }
                }
            }
        },
        "/user/points": {
            "get": {
                "description": "获取用户的积分统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "积分管理"
                ],
                "summary": "获取积分统计",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.PointsStatsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/points/history": {
            "get": {
                "description": "获取用户的积分变动历史",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "积分管理"
                ],
                "summary": "获取积分历史",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "孩子ID",
                        "name": "child_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.PointsHistoryResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/user/stats": {
            "get": {
                "description": "获取用户的基础统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "成长管理"
                ],
                "summary": "获取用户统计信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.UserStatsInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "api.AddPointsRequest": {
            "type": "object",
            "required": [
                "child_id",
                "description",
                "points",
                "source_id",
                "source_type"
            ],
            "properties": {
                "child_id": {
                    "description": "孩子ID",
                    "type": "integer"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "points": {
                    "description": "积分数量",
                    "type": "integer"
                },
                "source_id": {
                    "description": "来源ID",
                    "type": "integer"
                },
                "source_type": {
                    "description": "来源类型",
                    "type": "integer"
                }
            }
        },
        "api.CampJoinResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "description": "响应消息",
                    "type": "string"
                },
                "participation_id": {
                    "description": "参与记录ID",
                    "type": "integer"
                },
                "success": {
                    "description": "是否成功",
                    "type": "boolean"
                }
            }
        },
        "api.CampProgressResponse": {
            "type": "object",
            "properties": {
                "camp_id": {
                    "description": "训练营ID",
                    "type": "integer"
                },
                "completion_rate": {
                    "description": "完成率",
                    "type": "number"
                },
                "consecutive_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "current_day": {
                    "description": "当前训练天数",
                    "type": "integer"
                },
                "progress_percentage": {
                    "description": "进度百分比",
                    "type": "number"
                },
                "title": {
                    "description": "训练营标题",
                    "type": "string"
                },
                "total_checkins": {
                    "description": "总打卡次数",
                    "type": "integer"
                },
                "total_days": {
                    "description": "总天数",
                    "type": "integer"
                },
                "total_study_minutes": {
                    "description": "总学习时长",
                    "type": "integer"
                }
            }
        },
        "api.CampStatsInfo": {
            "type": "object",
            "properties": {
                "active_camps": {
                    "description": "活跃训练营",
                    "type": "integer"
                },
                "average_progress": {
                    "description": "平均进度",
                    "type": "number"
                },
                "completed_camps": {
                    "description": "完成训练营",
                    "type": "integer"
                },
                "total_joined": {
                    "description": "总参与数",
                    "type": "integer"
                }
            }
        },
        "api.CheckinCreateRequest": {
            "type": "object",
            "required": [
                "camp_id",
                "child_id",
                "practice_duration",
                "user_id"
            ],
            "properties": {
                "camp_id": {
                    "description": "训练营ID",
                    "type": "integer"
                },
                "child_id": {
                    "description": "孩子ID",
                    "type": "integer"
                },
                "feeling_score": {
                    "description": "感受评分 1-10",
                    "type": "integer",
                    "maximum": 10,
                    "minimum": 1
                },
                "feeling_text": {
                    "description": "训练感受",
                    "type": "string"
                },
                "jump_count_1min": {
                    "description": "1分钟跳绳个数",
                    "type": "integer"
                },
                "jump_count_continuous": {
                    "description": "连续跳绳个数",
                    "type": "integer"
                },
                "photos": {
                    "description": "打卡照片URL数组",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "practice_duration": {
                    "description": "练习时长（分钟）",
                    "type": "integer"
                },
                "user_id": {
                    "description": "操作用户ID",
                    "type": "integer"
                }
            }
        },
        "api.CheckinHistoryResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "打卡记录列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CheckinRecordsResponse"
                    }
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "api.CheckinResponse": {
            "type": "object",
            "properties": {
                "camp_id": {
                    "description": "训练营ID，关联training_camps.id",
                    "type": "integer"
                },
                "checkin_date": {
                    "description": "打卡日期",
                    "type": "string"
                },
                "child_id": {
                    "description": "孩子ID，关联children.id",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "feeling_score": {
                    "description": "感受评分 1-10",
                    "type": "integer"
                },
                "feeling_text": {
                    "description": "训练感受",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "jump_count_1min": {
                    "description": "1分钟跳绳个数",
                    "type": "integer"
                },
                "jump_count_continuous": {
                    "description": "连续跳绳个数",
                    "type": "integer"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string"
                },
                "photos": {
                    "description": "打卡照片URL数组",
                    "type": "string"
                },
                "points_earned": {
                    "description": "获得积分",
                    "type": "integer"
                },
                "practice_duration": {
                    "description": "练习时长（分钟）",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1:正常 2:补卡",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_id": {
                    "description": "操作用户ID，关联users.id",
                    "type": "integer"
                }
            }
        },
        "api.CheckinStatsResponse": {
            "type": "object",
            "properties": {
                "average_feeling": {
                    "description": "平均感受评分",
                    "type": "number"
                },
                "consecutive_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "max_consecutive_days": {
                    "description": "最大连续天数",
                    "type": "integer"
                },
                "this_month_checkins": {
                    "description": "本月打卡次数",
                    "type": "integer"
                },
                "this_week_checkins": {
                    "description": "本周打卡次数",
                    "type": "integer"
                },
                "total_checkins": {
                    "description": "总打卡次数",
                    "type": "integer"
                },
                "total_study_minutes": {
                    "description": "总学习时长",
                    "type": "integer"
                }
            }
        },
        "api.ContractCompleteResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "description": "响应消息",
                    "type": "string"
                },
                "points_earned": {
                    "description": "获得积分",
                    "type": "integer"
                },
                "success": {
                    "description": "是否成功",
                    "type": "boolean"
                }
            }
        },
        "api.ContractCreateRequest": {
            "type": "object",
            "required": [
                "camp_id",
                "child_id",
                "description",
                "reward_content",
                "target_days",
                "title",
                "user_id",
                "witness_user_ids"
            ],
            "properties": {
                "camp_id": {
                    "description": "关联训练营ID",
                    "type": "integer"
                },
                "child_id": {
                    "description": "孩子ID",
                    "type": "integer"
                },
                "description": {
                    "description": "契约描述",
                    "type": "string"
                },
                "reward_content": {
                    "description": "奖励内容",
                    "type": "string"
                },
                "target_days": {
                    "description": "目标天数",
                    "type": "integer"
                },
                "title": {
                    "description": "契约标题",
                    "type": "string"
                },
                "user_id": {
                    "description": "创建用户ID",
                    "type": "integer"
                },
                "witness_user_ids": {
                    "description": "见证人用户ID列表",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "api.ContractDetailResponse": {
            "type": "object",
            "properties": {
                "awarded_date": {
                    "description": "授勋日期，NULL表示未授勋",
                    "type": "string"
                },
                "camp_id": {
                    "description": "训练营ID，关联training_camps.id",
                    "type": "integer"
                },
                "camp_info": {
                    "description": "关联训练营信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.TrainingCampsResponse"
                        }
                    ]
                },
                "child_id": {
                    "description": "孩子ID，关联children.id",
                    "type": "integer"
                },
                "completed_date": {
                    "description": "完成日期，NULL表示未完成",
                    "type": "string"
                },
                "contract_status": {
                    "description": "状态 1:进行中 2:已完成 3:待授勋 4:已授勋",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "creator_user_id": {
                    "description": "创建者用户ID，关联users.id",
                    "type": "integer"
                },
                "current_progress": {
                    "description": "当前进度",
                    "type": "integer"
                },
                "goal_description": {
                    "description": "目标描述",
                    "type": "string"
                },
                "goal_type": {
                    "description": "目标类型 1:连续打卡 2:总打卡数 3:成绩达标",
                    "type": "integer"
                },
                "goal_value": {
                    "description": "目标数值",
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "reward_description": {
                    "description": "奖励描述",
                    "type": "string"
                },
                "start_date": {
                    "description": "开始日期",
                    "type": "string"
                },
                "target_date": {
                    "description": "目标日期",
                    "type": "string"
                },
                "title": {
                    "description": "契约标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "witnesses": {
                    "description": "见证人信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.ContractWitnessInfo"
                    }
                }
            }
        },
        "api.ContractResponse": {
            "type": "object",
            "properties": {
                "awarded_date": {
                    "description": "授勋日期，NULL表示未授勋",
                    "type": "string"
                },
                "camp_id": {
                    "description": "训练营ID，关联training_camps.id",
                    "type": "integer"
                },
                "child_id": {
                    "description": "孩子ID，关联children.id",
                    "type": "integer"
                },
                "completed_date": {
                    "description": "完成日期，NULL表示未完成",
                    "type": "string"
                },
                "contract_status": {
                    "description": "状态 1:进行中 2:已完成 3:待授勋 4:已授勋",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "creator_user_id": {
                    "description": "创建者用户ID，关联users.id",
                    "type": "integer"
                },
                "current_progress": {
                    "description": "当前进度",
                    "type": "integer"
                },
                "goal_description": {
                    "description": "目标描述",
                    "type": "string"
                },
                "goal_type": {
                    "description": "目标类型 1:连续打卡 2:总打卡数 3:成绩达标",
                    "type": "integer"
                },
                "goal_value": {
                    "description": "目标数值",
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "reward_description": {
                    "description": "奖励描述",
                    "type": "string"
                },
                "start_date": {
                    "description": "开始日期",
                    "type": "string"
                },
                "target_date": {
                    "description": "目标日期",
                    "type": "string"
                },
                "title": {
                    "description": "契约标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "witnesses": {
                    "description": "见证人信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.ContractWitnessInfo"
                    }
                }
            }
        },
        "api.ContractStatsResponse": {
            "type": "object",
            "properties": {
                "active_contracts": {
                    "description": "进行中契约数",
                    "type": "integer"
                },
                "completed_contracts": {
                    "description": "已完成契约数",
                    "type": "integer"
                },
                "completion_rate": {
                    "description": "完成率",
                    "type": "number"
                },
                "total_contracts": {
                    "description": "总契约数",
                    "type": "integer"
                }
            }
        },
        "api.ContractWitnessInfo": {
            "type": "object",
            "properties": {
                "relation": {
                    "description": "关系（爸爸、妈妈等）",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "user_name": {
                    "description": "用户姓名",
                    "type": "string"
                }
            }
        },
        "api.GrowthPageResponse": {
            "type": "object",
            "properties": {
                "camp_list": {
                    "description": "训练营列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.UserCampResponse"
                    }
                },
                "contract_list": {
                    "description": "契约列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.ContractResponse"
                    }
                },
                "growth_track": {
                    "description": "成长轨迹",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.GrowthTrackItem"
                    }
                },
                "medals_list": {
                    "description": "勋章列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.MedalInfo"
                    }
                },
                "today_status": {
                    "description": "今日状态",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.TodayStatusInfo"
                        }
                    ]
                },
                "user_stats": {
                    "description": "用户统计信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.UserStatsInfo"
                        }
                    ]
                }
            }
        },
        "api.GrowthStatsResponse": {
            "type": "object",
            "properties": {
                "camp_stats": {
                    "description": "训练营统计",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.CampStatsInfo"
                        }
                    ]
                },
                "checkin_stats": {
                    "description": "打卡统计",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.CheckinStatsResponse"
                        }
                    ]
                },
                "contract_stats": {
                    "description": "契约统计",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.ContractStatsResponse"
                        }
                    ]
                },
                "overall_stats": {
                    "description": "总体统计",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.UserStatsInfo"
                        }
                    ]
                },
                "points_stats": {
                    "description": "积分统计",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.PointsStatsResponse"
                        }
                    ]
                }
            }
        },
        "api.GrowthTrackItem": {
            "type": "object",
            "properties": {
                "date": {
                    "description": "日期",
                    "type": "string"
                },
                "description": {
                    "description": "描述",
                    "type": "string"
                },
                "points": {
                    "description": "获得积分",
                    "type": "integer"
                },
                "title": {
                    "description": "标题",
                    "type": "string"
                },
                "type": {
                    "description": "类型：checkin, contract, honor, award",
                    "type": "string"
                }
            }
        },
        "api.LeaderboardItem": {
            "type": "object",
            "properties": {
                "child_id": {
                    "description": "孩子ID",
                    "type": "integer"
                },
                "child_name": {
                    "description": "孩子姓名",
                    "type": "string"
                },
                "continuous_days": {
                    "description": "连续天数",
                    "type": "integer"
                },
                "points": {
                    "description": "积分",
                    "type": "integer"
                },
                "rank": {
                    "description": "排名",
                    "type": "integer"
                }
            }
        },
        "api.LeaderboardResponse": {
            "type": "object",
            "properties": {
                "monthly_ranking": {
                    "description": "月排行榜",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.LeaderboardItem"
                    }
                },
                "weekly_ranking": {
                    "description": "周排行榜",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.LeaderboardItem"
                    }
                }
            }
        },
        "api.MedalInfo": {
            "type": "object",
            "properties": {
                "condition_description": {
                    "description": "条件描述",
                    "type": "string"
                },
                "condition_type": {
                    "description": "条件类型 1:打卡次数 2:连续天数 3:积分达标",
                    "type": "integer"
                },
                "description": {
                    "description": "勋章描述",
                    "type": "string"
                },
                "icon": {
                    "description": "勋章图标",
                    "type": "string"
                },
                "id": {
                    "description": "勋章ID",
                    "type": "integer"
                },
                "is_contract_medal": {
                    "description": "是否为契约勋章",
                    "type": "boolean"
                },
                "level": {
                    "description": "勋章等级 1:铜 2:银 3:金 4:钻石",
                    "type": "integer"
                },
                "level_name": {
                    "description": "等级名称",
                    "type": "string"
                },
                "name": {
                    "description": "勋章名称",
                    "type": "string"
                },
                "points_reward": {
                    "description": "奖励积分",
                    "type": "integer"
                },
                "progress": {
                    "description": "当前进度",
                    "type": "integer"
                },
                "sort_order": {
                    "description": "排序权重",
                    "type": "integer"
                },
                "target": {
                    "description": "目标值",
                    "type": "integer"
                },
                "unlocked": {
                    "description": "是否已解锁",
                    "type": "boolean"
                },
                "unlocked_at": {
                    "description": "解锁时间",
                    "type": "string"
                }
            }
        },
        "api.PointsHistoryResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "积分记录列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.PointRecordsResponse"
                    }
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "api.PointsStatsResponse": {
            "type": "object",
            "properties": {
                "continuous_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "current_level": {
                    "description": "当前等级",
                    "type": "integer"
                },
                "last_checkin_date": {
                    "description": "最后打卡日期",
                    "type": "string"
                },
                "last_week_rank": {
                    "description": "上周排名",
                    "type": "integer"
                },
                "max_continuous_days": {
                    "description": "最大连续天数",
                    "type": "integer"
                },
                "month_points": {
                    "description": "本月积分",
                    "type": "integer"
                },
                "next_level_points": {
                    "description": "下一等级所需积分",
                    "type": "integer"
                },
                "total_checkins": {
                    "description": "总打卡次数",
                    "type": "integer"
                },
                "total_points": {
                    "description": "总积分",
                    "type": "integer"
                },
                "week_points": {
                    "description": "本周积分",
                    "type": "integer"
                },
                "week_rank": {
                    "description": "本周排名",
                    "type": "integer"
                }
            }
        },
        "api.TodayCheckinStatus": {
            "type": "object",
            "properties": {
                "checkin_data": {
                    "description": "打卡数据（如果已打卡）",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.CheckinRecordsResponse"
                        }
                    ]
                },
                "has_checked_in": {
                    "description": "是否已打卡",
                    "type": "boolean"
                }
            }
        },
        "api.TodayStatusInfo": {
            "type": "object",
            "properties": {
                "active_camps": {
                    "description": "活跃训练营数",
                    "type": "integer"
                },
                "has_checked_in": {
                    "description": "是否已打卡",
                    "type": "boolean"
                },
                "pending_contracts": {
                    "description": "待完成契约数",
                    "type": "integer"
                },
                "recommendations": {
                    "description": "推荐行动",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "today_points": {
                    "description": "今日积分",
                    "type": "integer"
                }
            }
        },
        "api.TrainingCampDetailResponse": {
            "type": "object",
            "properties": {
                "age_group": {
                    "description": "适用年龄段",
                    "type": "string"
                },
                "average_rating": {
                    "description": "平均评分",
                    "type": "number"
                },
                "background_color": {
                    "description": "背景颜色",
                    "type": "string"
                },
                "completion_rate": {
                    "description": "完成率百分比",
                    "type": "number"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "daily_minutes": {
                    "description": "每日建议训练时长（分钟）",
                    "type": "integer"
                },
                "difficulty_level": {
                    "description": "难度等级 1-5",
                    "type": "integer"
                },
                "duration_days": {
                    "description": "训练营总天数",
                    "type": "integer"
                },
                "hero_number": {
                    "description": "核心数字展示",
                    "type": "string"
                },
                "hero_text": {
                    "description": "核心文案展示",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "is_featured": {
                    "description": "是否推荐 0:普通 1:推荐",
                    "type": "integer"
                },
                "is_free": {
                    "description": "是否免费 0:付费 1:免费",
                    "type": "integer"
                },
                "key_benefits": {
                    "description": "核心收益点",
                    "type": "string"
                },
                "key_benefits_list": {
                    "description": "解析后的核心收益点列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "original_price": {
                    "description": "原价（元）",
                    "type": "number"
                },
                "pain_solution": {
                    "description": "解决痛点描述",
                    "type": "string"
                },
                "price": {
                    "description": "价格（元）",
                    "type": "number"
                },
                "promises": {
                    "description": "服务承诺",
                    "type": "string"
                },
                "promises_list": {
                    "description": "解析后的服务承诺列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "share_desc": {
                    "description": "分享描述",
                    "type": "string"
                },
                "share_title": {
                    "description": "分享标题",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序权重",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1:正常 2:暂停 3:下线",
                    "type": "integer"
                },
                "subtitle": {
                    "description": "训练营副标题",
                    "type": "string"
                },
                "tags": {
                    "description": "训练营标签",
                    "type": "string"
                },
                "tags_list": {
                    "description": "解析后的标签列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "title": {
                    "description": "训练营标题",
                    "type": "string"
                },
                "total_participants": {
                    "description": "总参与人数",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_status": {
                    "description": "用户参与状态",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.UserCampStatus"
                        }
                    ]
                },
                "video_collection": {
                    "description": "关联的视频集合",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.VideoCollectionsResponse"
                        }
                    ]
                },
                "video_collection_id": {
                    "description": "关联视频集合ID，关联video_collections.id",
                    "type": "integer"
                },
                "wechat_helper": {
                    "description": "微信助手号",
                    "type": "string"
                }
            }
        },
        "api.UpdateProgressRequest": {
            "type": "object",
            "required": [
                "progress"
            ],
            "properties": {
                "progress": {
                    "description": "进度百分比",
                    "type": "number",
                    "maximum": 100,
                    "minimum": 0
                }
            }
        },
        "api.UserCampResponse": {
            "type": "object",
            "properties": {
                "camp_id": {
                    "description": "训练营ID",
                    "type": "integer"
                },
                "consecutive_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "current_day": {
                    "description": "当前训练天数",
                    "type": "integer"
                },
                "duration_days": {
                    "description": "训练营总天数",
                    "type": "integer"
                },
                "participation_status": {
                    "description": "参与状态",
                    "type": "integer"
                },
                "progress_percentage": {
                    "description": "进度百分比",
                    "type": "number"
                },
                "subtitle": {
                    "description": "训练营副标题",
                    "type": "string"
                },
                "title": {
                    "description": "训练营标题",
                    "type": "string"
                },
                "today_status": {
                    "description": "今日状态 pending/completed",
                    "type": "string"
                },
                "total_checkins": {
                    "description": "总打卡次数",
                    "type": "integer"
                },
                "total_study_minutes": {
                    "description": "总学习时长",
                    "type": "integer"
                }
            }
        },
        "api.UserCampStatus": {
            "type": "object",
            "properties": {
                "consecutive_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "current_day": {
                    "description": "当前训练天数",
                    "type": "integer"
                },
                "is_joined": {
                    "description": "是否已参与",
                    "type": "boolean"
                },
                "participation_status": {
                    "description": "参与状态",
                    "type": "integer"
                },
                "progress_percentage": {
                    "description": "进度百分比",
                    "type": "number"
                },
                "total_checkins": {
                    "description": "总打卡次数",
                    "type": "integer"
                }
            }
        },
        "api.UserStatsInfo": {
            "type": "object",
            "properties": {
                "completed_contracts": {
                    "description": "完成契约数",
                    "type": "integer"
                },
                "completion_rate": {
                    "description": "完成率",
                    "type": "number"
                },
                "current_level": {
                    "description": "当前等级",
                    "type": "integer"
                },
                "streak_days": {
                    "description": "连续打卡天数",
                    "type": "integer"
                },
                "total_points": {
                    "description": "总积分",
                    "type": "integer"
                },
                "week_rank": {
                    "description": "本周排名",
                    "type": "integer"
                }
            }
        },
        "models.CheckinRecordsResponse": {
            "type": "object",
            "properties": {
                "camp_id": {
                    "description": "训练营ID，关联training_camps.id",
                    "type": "integer"
                },
                "checkin_date": {
                    "description": "打卡日期",
                    "type": "string"
                },
                "child_id": {
                    "description": "孩子ID，关联children.id",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "feeling_score": {
                    "description": "感受评分 1-10",
                    "type": "integer"
                },
                "feeling_text": {
                    "description": "训练感受",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "jump_count_1min": {
                    "description": "1分钟跳绳个数",
                    "type": "integer"
                },
                "jump_count_continuous": {
                    "description": "连续跳绳个数",
                    "type": "integer"
                },
                "photos": {
                    "description": "打卡照片URL数组",
                    "type": "string"
                },
                "points_earned": {
                    "description": "获得积分",
                    "type": "integer"
                },
                "practice_duration": {
                    "description": "练习时长（分钟）",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1:正常 2:补卡",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_id": {
                    "description": "操作用户ID，关联users.id",
                    "type": "integer"
                }
            }
        },
        "models.ChildrenCreateRequest": {
            "type": "object",
            "required": [
                "name",
                "relation"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "birth_date": {
                    "type": "string"
                },
                "city": {
                    "type": "string",
                    "maxLength": 20
                },
                "gender": {
                    "type": "integer",
                    "enum": [
                        0,
                        1,
                        2
                    ]
                },
                "grade": {
                    "type": "string",
                    "maxLength": 20
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "nickname": {
                    "type": "string",
                    "maxLength": 50
                },
                "province": {
                    "type": "string",
                    "maxLength": 20
                },
                "relation": {
                    "type": "string",
                    "maxLength": 20
                },
                "school": {
                    "type": "string",
                    "maxLength": 100
                }
            }
        },
        "models.ChildrenResponse": {
            "type": "object",
            "properties": {
                "age": {
                    "type": "integer"
                },
                "avatar": {
                    "type": "string"
                },
                "best_score_1min": {
                    "type": "integer"
                },
                "best_score_continuous": {
                    "type": "integer"
                },
                "birth_date": {
                    "type": "string"
                },
                "city": {
                    "type": "string"
                },
                "continuous_days": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "gender": {
                    "type": "integer"
                },
                "grade": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_current": {
                    "type": "boolean"
                },
                "last_checkin_date": {
                    "type": "string"
                },
                "learning_goals": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "preferred_difficulty": {
                    "type": "integer"
                },
                "privacy_level": {
                    "type": "integer"
                },
                "province": {
                    "type": "string"
                },
                "relation": {
                    "type": "string"
                },
                "school": {
                    "type": "string"
                },
                "show_in_leaderboard": {
                    "type": "integer"
                },
                "skill_level": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "total_checkins": {
                    "type": "integer"
                },
                "total_points": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.ChildrenUpdateRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "birth_date": {
                    "type": "string"
                },
                "city": {
                    "type": "string",
                    "maxLength": 20
                },
                "gender": {
                    "type": "integer",
                    "enum": [
                        0,
                        1,
                        2
                    ]
                },
                "grade": {
                    "type": "string",
                    "maxLength": 20
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1
                },
                "nickname": {
                    "type": "string",
                    "maxLength": 50
                },
                "preferred_difficulty": {
                    "type": "integer",
                    "enum": [
                        1,
                        2,
                        3,
                        4,
                        5
                    ]
                },
                "privacy_level": {
                    "type": "integer",
                    "enum": [
                        1,
                        2,
                        3
                    ]
                },
                "province": {
                    "type": "string",
                    "maxLength": 20
                },
                "school": {
                    "type": "string",
                    "maxLength": 100
                },
                "show_in_leaderboard": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "models.PointRecordsResponse": {
            "type": "object",
            "properties": {
                "child_id": {
                    "description": "孩子ID，关联children.id",
                    "type": "integer"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "变化描述",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "points_after": {
                    "description": "变化后总积分",
                    "type": "integer"
                },
                "points_change": {
                    "description": "积分变化",
                    "type": "integer"
                },
                "source_id": {
                    "description": "来源ID",
                    "type": "integer"
                },
                "source_type": {
                    "description": "来源 1:打卡 2:完成契约 3:系统奖励",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "models.TrainingCampsListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "训练营主表：存储训练营的基本信息和配置列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TrainingCampsResponse"
                    }
                },
                "total": {
                    "description": "总数",
                    "type": "integer"
                }
            }
        },
        "models.TrainingCampsResponse": {
            "type": "object",
            "properties": {
                "age_group": {
                    "description": "适用年龄段",
                    "type": "string"
                },
                "average_rating": {
                    "description": "平均评分",
                    "type": "number"
                },
                "background_color": {
                    "description": "背景颜色",
                    "type": "string"
                },
                "completion_rate": {
                    "description": "完成率百分比",
                    "type": "number"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "daily_minutes": {
                    "description": "每日建议训练时长（分钟）",
                    "type": "integer"
                },
                "difficulty_level": {
                    "description": "难度等级 1-5",
                    "type": "integer"
                },
                "duration_days": {
                    "description": "训练营总天数",
                    "type": "integer"
                },
                "hero_number": {
                    "description": "核心数字展示",
                    "type": "string"
                },
                "hero_text": {
                    "description": "核心文案展示",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "is_featured": {
                    "description": "是否推荐 0:普通 1:推荐",
                    "type": "integer"
                },
                "is_free": {
                    "description": "是否免费 0:付费 1:免费",
                    "type": "integer"
                },
                "key_benefits": {
                    "description": "核心收益点",
                    "type": "string"
                },
                "original_price": {
                    "description": "原价（元）",
                    "type": "number"
                },
                "pain_solution": {
                    "description": "解决痛点描述",
                    "type": "string"
                },
                "price": {
                    "description": "价格（元）",
                    "type": "number"
                },
                "promises": {
                    "description": "服务承诺",
                    "type": "string"
                },
                "share_desc": {
                    "description": "分享描述",
                    "type": "string"
                },
                "share_title": {
                    "description": "分享标题",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序权重",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1:正常 2:暂停 3:下线",
                    "type": "integer"
                },
                "subtitle": {
                    "description": "训练营副标题",
                    "type": "string"
                },
                "tags": {
                    "description": "训练营标签",
                    "type": "string"
                },
                "title": {
                    "description": "训练营标题",
                    "type": "string"
                },
                "total_participants": {
                    "description": "总参与人数",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "video_collection_id": {
                    "description": "关联视频集合ID，关联video_collections.id",
                    "type": "integer"
                },
                "wechat_helper": {
                    "description": "微信助手号",
                    "type": "string"
                }
            }
        },
        "models.UsersResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "description": "用户头像URL，存储微信头像或用户上传头像的完整URL地址",
                    "type": "string"
                },
                "city": {
                    "description": "城市，用于本地化服务和用户分组",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "current_child_id": {
                    "description": "当前选择的孩子ID，0表示未选择",
                    "type": "integer"
                },
                "gender": {
                    "description": "性别 0:未知 1:男 2:女，来源于微信授权信息",
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "last_login_at": {
                    "description": "最后登录时间，用于用户活跃度分析",
                    "type": "string"
                },
                "nickname": {
                    "description": "用户昵称，来源于微信昵称或用户自定义，最长50字符",
                    "type": "string"
                },
                "openid": {
                    "type": "string"
                },
                "phone": {
                    "description": "手机号码，可选填写，用于重要通知和账户安全",
                    "type": "string"
                },
                "province": {
                    "description": "省份，用于地域化推荐和统计分析",
                    "type": "string"
                },
                "status": {
                    "description": "账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控",
                    "type": "integer"
                },
                "unionid": {
                    "description": "微信unionid，28位固定长度，用于跨应用用户识别",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_type": {
                    "description": "用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制",
                    "type": "integer"
                }
            }
        },
        "models.UsersUpdateRequest": {
            "type": "object",
            "properties": {
                "avatar": {
                    "description": "用户头像URL，存储微信头像或用户上传头像的完整URL地址",
                    "type": "string",
                    "maxLength": 255
                },
                "city": {
                    "description": "城市，用于本地化服务和用户分组",
                    "type": "string",
                    "maxLength": 20
                },
                "current_child_id": {
                    "description": "当前选择的孩子ID，0表示未选择",
                    "type": "integer"
                },
                "gender": {
                    "description": "性别 0:未知 1:男 2:女，来源于微信授权信息",
                    "type": "integer"
                },
                "last_login_at": {
                    "description": "最后登录时间，用于用户活跃度分析",
                    "type": "string"
                },
                "nickname": {
                    "description": "用户昵称，来源于微信昵称或用户自定义，最长50字符",
                    "type": "string",
                    "maxLength": 50
                },
                "openid": {
                    "type": "string",
                    "maxLength": 28
                },
                "phone": {
                    "description": "手机号码，可选填写，用于重要通知和账户安全",
                    "type": "string",
                    "maxLength": 20
                },
                "province": {
                    "description": "省份，用于地域化推荐和统计分析",
                    "type": "string",
                    "maxLength": 20
                },
                "status": {
                    "description": "账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控",
                    "type": "integer"
                },
                "unionid": {
                    "description": "微信unionid，28位固定长度，用于跨应用用户识别",
                    "type": "string",
                    "maxLength": 28
                },
                "user_type": {
                    "description": "用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制",
                    "type": "integer"
                }
            }
        },
        "models.VideoCollectionsResponse": {
            "type": "object",
            "properties": {
                "age_group": {
                    "description": "适用年龄段",
                    "type": "string"
                },
                "category_id": {
                    "description": "分类ID，关联video_categories.id",
                    "type": "integer"
                },
                "cover_image": {
                    "description": "集合封面图URL",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "集合描述",
                    "type": "string"
                },
                "difficulty_level": {
                    "description": "难度等级 1-5",
                    "type": "integer"
                },
                "free_video_count": {
                    "description": "免费视频数量",
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "is_free": {
                    "description": "是否免费 0:付费 1:免费",
                    "type": "integer"
                },
                "price": {
                    "description": "价格（元）",
                    "type": "number"
                },
                "status": {
                    "description": "状态 1:正常 2:下架",
                    "type": "integer"
                },
                "title": {
                    "description": "集合标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "video_count": {
                    "description": "视频总数量",
                    "type": "integer"
                },
                "videos": {
                    "description": "集合中的视频列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideosResponse"
                    }
                }
            }
        },
        "models.VideosResponse": {
            "type": "object",
            "properties": {
                "age_group": {
                    "description": "适用年龄段",
                    "type": "string"
                },
                "category_id": {
                    "description": "分类ID，关联video_categories.id",
                    "type": "integer"
                },
                "cover_image": {
                    "description": "视频封面图URL",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "视频描述",
                    "type": "string"
                },
                "difficulty_level": {
                    "description": "难度等级 1-5",
                    "type": "integer"
                },
                "duration": {
                    "description": "视频时长（秒）",
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "like_count": {
                    "description": "点赞数量",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 1:正常 2:下架",
                    "type": "integer"
                },
                "tags": {
                    "description": "视频标签，逗号分隔",
                    "type": "string"
                },
                "title": {
                    "description": "视频标题",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "video_source": {
                    "description": "视频来源 1:微信视频号 2:本地上传",
                    "type": "integer"
                },
                "video_url": {
                    "description": "视频文件URL",
                    "type": "string"
                },
                "view_count": {
                    "description": "播放次数",
                    "type": "integer"
                }
            }
        },
        "models.WechatLoginRequest": {
            "type": "object",
            "required": [
                "code"
            ],
            "properties": {
                "code": {
                    "description": "微信登录凭证，通过wx.login()获取",
                    "type": "string"
                },
                "encrypted_data": {
                    "description": "加密的用户数据，可选，用于获取用户详细信息",
                    "type": "string"
                },
                "iv": {
                    "description": "加密算法的初始向量，与encrypted_data配套使用",
                    "type": "string"
                },
                "phone_code": {
                    "description": "手机号授权码，可选，用于绑定手机号",
                    "type": "string"
                }
            }
        },
        "models.WechatLoginResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "description": "JWT访问令牌",
                    "type": "string"
                },
                "expires_in": {
                    "description": "访问令牌过期时间（秒）",
                    "type": "integer"
                },
                "is_new_user": {
                    "description": "是否为新注册用户",
                    "type": "boolean"
                },
                "refresh_token": {
                    "description": "刷新令牌",
                    "type": "string"
                },
                "token_type": {
                    "description": "令牌类型，固定为Bearer",
                    "type": "string"
                },
                "user_info": {
                    "description": "用户基本信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.UsersResponse"
                        }
                    ]
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "JWT认证，格式：Bearer {token}",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Go API Framework",
	Description:      "基于Go和Gin的API框架，支持JWT认证、Redis缓存、健康检查等功能",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
