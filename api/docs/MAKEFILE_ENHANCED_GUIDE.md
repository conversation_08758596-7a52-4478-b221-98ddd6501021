# 增强版 Makefile 使用指南

## 概述

增强版 Makefile 支持参数化操作，让您可以灵活地管理 API 和 Admin 两个服务。大部分命令都支持 `SERVICE` 参数来指定操作的目标服务。

## 服务配置

项目包含两个主要服务：
- **api**: API服务器 (端口 8080)
- **admin**: Admin管理后台服务器 (端口 8081)

## 基本语法

```bash
make <command> [SERVICE=<service>]
```

其中 `<service>` 可以是：
- `api` - 只操作API服务
- `admin` - 只操作Admin服务  
- `all` - 操作所有服务（默认值）

## 🚀 服务管理命令

### 启动服务
```bash
# 启动所有服务
make start

# 只启动API服务
make start SERVICE=api

# 只启动Admin服务
make start SERVICE=admin
```

### 停止服务
```bash
# 停止所有服务
make stop

# 只停止API服务
make stop SERVICE=api

# 只停止Admin服务
make stop SERVICE=admin
```

### 重启服务
```bash
# 重启所有服务
make restart

# 只重启API服务
make restart SERVICE=api

# 只重启Admin服务
make restart SERVICE=admin
```

## 📊 状态查询命令

### 查看服务状态
```bash
# 查看所有服务状态
make status

# 查看API服务状态
make status SERVICE=api

# 查看Admin服务状态
make status SERVICE=admin
```

### 健康检查
```bash
# 检查所有服务健康状态
make health

# 检查API服务健康状态
make health SERVICE=api

# 检查Admin服务健康状态
make health SERVICE=admin
```

### 查看进程信息
```bash
# 显示所有Go服务进程信息
make ps
```

### 查看日志
```bash
# 查看API服务日志（最近50行）
make logs SERVICE=api

# 查看Admin服务日志（最近50行）
make logs SERVICE=admin

# 注意：logs命令必须指定具体服务，不支持 SERVICE=all
```

## 📦 构建和依赖管理

### 构建项目
```bash
# 构建所有服务
make build

# 只构建API服务
make build SERVICE=api

# 只构建Admin服务
make build SERVICE=admin
```

### 依赖管理
```bash
# 安装依赖
make deps

# 清理构建文件
make clean
```

## 🧪 测试命令

### 运行测试
```bash
# 运行所有服务的所有测试
make test

# 只运行API服务的测试
make test SERVICE=api

# 只运行Admin服务的测试
make test SERVICE=admin
```

### 单元测试
```bash
# 运行所有服务的单元测试
make test-unit

# 只运行API服务的单元测试
make test-unit SERVICE=api

# 只运行Admin服务的单元测试
make test-unit SERVICE=admin
```

### 其他测试
```bash
# 集成测试
make test-integration

# 测试覆盖率
make test-coverage

# 竞态检测
make test-race

# API集成测试
make test-api

# 基准测试
make bench
```

## 📚 代码质量

```bash
# 生成Swagger文档
make docs

# 格式化代码
make fmt

# 代码检查
make vet
```

## 💡 使用示例

### 开发工作流
```bash
# 1. 安装依赖
make deps

# 2. 构建项目
make build

# 3. 启动服务
make start

# 4. 查看状态
make status

# 5. 查看日志
make logs SERVICE=api

# 6. 运行测试
make test

# 7. 停止服务
make stop
```

### 单服务开发
```bash
# 只开发API服务
make build SERVICE=api
make start SERVICE=api
make status SERVICE=api
make logs SERVICE=api
make test SERVICE=api
make stop SERVICE=api
```

### 问题排查
```bash
# 查看服务状态
make status

# 健康检查
make health

# 查看进程信息
make ps

# 查看错误日志
make logs SERVICE=api

# 重启有问题的服务
make restart SERVICE=api
```

## 🔧 高级功能

### 日志查看选项
日志脚本支持多种选项：
```bash
# 查看最近100行日志
./scripts/logs.sh api 100

# 实时跟踪日志
./scripts/logs.sh api follow

# 查看日志统计
./scripts/logs.sh api stats

# 清理日志文件
./scripts/logs.sh api clean
```

### 健康检查详情
健康检查会验证：
- 进程状态
- 端口监听
- HTTP响应
- 内存使用
- 日志错误
- 数据库连接

### 状态查询详情
状态查询会显示：
- 进程信息（PID、运行时间、CPU/内存使用）
- 端口状态
- 日志文件信息
- 错误统计
- 系统资源使用

## ⚠️ 注意事项

1. **logs命令限制**: `make logs` 命令必须指定具体服务名称，不支持 `SERVICE=all`
2. **权限要求**: 确保脚本有执行权限：`chmod +x scripts/*.sh`
3. **端口冲突**: 确保端口8080和8081没有被其他程序占用
4. **Go环境**: 构建和测试命令需要正确配置的Go环境

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   make status SERVICE=api
   make logs SERVICE=api
   ```

2. **端口被占用**
   ```bash
   lsof -i :8080
   lsof -i :8081
   ```

3. **构建失败**
   ```bash
   go version
   go env
   make deps
   ```

4. **权限问题**
   ```bash
   chmod +x scripts/*.sh
   ```

## 📞 获取帮助

```bash
# 查看所有可用命令
make help

# 查看脚本帮助
./scripts/logs.sh help
```
