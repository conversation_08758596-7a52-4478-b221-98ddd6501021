package response

import (
	"context"
	"net/http"

	"kids-platform/pkg/config"
	"kids-platform/pkg/errcode"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code       int         `json:"code"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data,omitempty"`
	Pagination Pagination  `json:"pagination"`
}

// Pagination 分页信息
type Pagination struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}

// 响应码定义
const (
	CodeSuccess      = 0
	CodeError        = 1
	CodeInvalidParam = 1001
	CodeUnauthorized = 1002
	CodeForbidden    = 1003
	CodeNotFound     = 1004
	CodeServerError  = 1005
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string, details ...string) {
	resp := Response{
		Code:    code,
		Message: message,
	}

	// 如果有详细信息，添加到响应中
	if len(details) > 0 {
		resp.Data = map[string]string{"details": details[0]}
	}

	c.JSON(http.StatusOK, resp)
}

// ErrorWithCode 根据错误码响应（带日志记录和脱敏处理）
func ErrorWithCode(c *gin.Context, err error) {
	if err == nil {
		Success(c, gin.H{})
		return
	}

	// 获取配置
	cfg, exists := c.Get("config")
	var appConfig *config.Config
	if exists {
		appConfig = cfg.(*config.Config)
	} else {
		// 如果没有配置，使用默认的生产环境配置
		appConfig = &config.Config{
			Security: config.SecurityConfig{
				ProductionMode: true,
				DebugMode:      false,
			},
		}
	}

	// 创建错误上下文
	errCtx := errcode.NewErrorContext(
		c.GetString("request_id"),
		c.Request.Method,
		c.Request.URL.Path,
		c.ClientIP(),
		c.GetHeader("User-Agent"),
	)

	// 从context中获取用户ID
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uint64); ok {
			errCtx.WithUserID(uid)
		}
	}

	// 记录错误日志
	ctx := context.Background()
	errcode.LogError(ctx, err, errCtx)

	// 脱敏处理
	sanitizedErr := errcode.SanitizeError(err, appConfig)

	// 响应处理
	if errCodeErr, ok := sanitizedErr.(*errcode.Error); ok {
		statusCode := errcode.ToHTTPStatusCode(errCodeErr.Code())

		resp := Response{
			Code:    errCodeErr.Code(),
			Message: errCodeErr.Msg(),
		}

		// 开发环境下添加详细信息
		if appConfig.Security.DebugMode {
			if details := errCodeErr.Details(); len(details) > 0 {
				resp.Data = map[string]interface{}{"details": details}
			}
		}

		c.JSON(statusCode, resp)
		return
	}

	// 兜底处理
	c.JSON(http.StatusInternalServerError, Response{
		Code:    CodeServerError,
		Message: "服务器内部错误",
	})
}

// InvalidParam 参数错误响应
func InvalidParam(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    CodeInvalidParam,
		Message: message,
	})
}

// BadRequest 请求错误响应
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    CodeInvalidParam,
		Message: message,
	})
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, Response{
		Code:    CodeUnauthorized,
		Message: message,
	})
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, Response{
		Code:    CodeForbidden,
		Message: message,
	})
}

// NotFound 未找到响应
func NotFound(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, Response{
		Code:    CodeNotFound,
		Message: message,
	})
}

// ServerError 服务器错误响应
func ServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, Response{
		Code:    CodeServerError,
		Message: message,
	})
}

// Page 分页响应
func Page(c *gin.Context, data interface{}, pagination Pagination) {
	c.JSON(http.StatusOK, PageResponse{
		Code:       CodeSuccess,
		Message:    "success",
		Data:       data,
		Pagination: pagination,
	})
}

// SuccessWithPagination 成功响应（带分页）
func SuccessWithPagination(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	pagination := Pagination{
		Page:     page,
		PageSize: pageSize,
		Total:    int(total),
	}
	Page(c, data, pagination)
}
