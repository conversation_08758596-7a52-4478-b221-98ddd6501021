package database

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
)

// TxFunc 事务函数类型
type TxFunc func(*gorm.DB) error

// WithTransaction 执行事务
func WithTransaction(db *gorm.DB, fn TxFunc) error {
	return db.Transaction(fn)
}

// WithTransactionContext 带上下文的事务执行
func WithTransactionContext(ctx context.Context, db *gorm.DB, fn TxFunc) error {
	return db.WithContext(ctx).Transaction(fn)
}

// ReadOnlyTransaction 只读事务
func ReadOnlyTransaction(db *gorm.DB, fn TxFunc) error {
	return db.Transaction(fn, &sql.TxOptions{
		ReadOnly: true,
	})
}

// BatchInsert 批量插入
func BatchInsert[T any](db *gorm.DB, data []T, batchSize int) error {
	if len(data) == 0 {
		return nil
	}

	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}
		
		if err := db.CreateInBatches(data[i:end], batchSize).Error; err != nil {
			return err
		}
	}
	
	return nil
}

// Paginate 分页查询
func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}
		
		if pageSize <= 0 {
			pageSize = 10
		}
		
		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// Count 计算总数
func Count(db *gorm.DB, model interface{}) (int64, error) {
	var count int64
	err := db.Model(model).Count(&count).Error
	return count, err
}
