package database

import (
	"context"
	"fmt"
	"kids-platform/pkg/config"
	"time"

	"gorm.io/gorm"
)

// OptimizedDB 优化的数据库连接
type OptimizedDB struct {
	*gorm.DB
	readDB  *gorm.DB
	writeDB *gorm.DB
	config  *config.DatabaseConfig
}

// NewOptimizedDB 创建优化的数据库连接
func NewOptimizedDB(cfg *config.DatabaseConfig) (*OptimizedDB, error) {
	// 主数据库连接（写）
	writeDB, err := initConnection(cfg, "write")
	if err != nil {
		return nil, fmt.Errorf("failed to init write database: %w", err)
	}

	// 读数据库连接（如果配置了读写分离）
	var readDB *gorm.DB
	if cfg.ReadHost != "" {
		readConfig := *cfg
		readConfig.Host = cfg.ReadHost
		readConfig.Port = cfg.ReadPort
		readConfig.Username = cfg.ReadUsername
		readConfig.Password = cfg.ReadPassword

		readDB, err = initConnection(&readConfig, "read")
		if err != nil {
			return nil, fmt.Errorf("failed to init read database: %w", err)
		}
	} else {
		readDB = writeDB
	}

	return &OptimizedDB{
		DB:      writeDB,
		readDB:  readDB,
		writeDB: writeDB,
		config:  cfg,
	}, nil
}

// initConnection 初始化数据库连接
func initConnection(cfg *config.DatabaseConfig, role string) (*gorm.DB, error) {
	db, err := Init(*cfg)
	if err != nil {
		return nil, err
	}

	// 根据角色优化连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	if role == "read" {
		// 读库优化：更多连接，更长生命周期
		sqlDB.SetMaxIdleConns(cfg.MaxIdleConns * 2)
		sqlDB.SetMaxOpenConns(cfg.MaxOpenConns * 2)
		sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime*2) * time.Second)
	} else {
		// 写库优化：标准配置
		sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
		sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
		sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)
	}

	return db, nil
}

// Read 获取读数据库连接
func (odb *OptimizedDB) Read() *gorm.DB {
	return odb.readDB
}

// Write 获取写数据库连接
func (odb *OptimizedDB) Write() *gorm.DB {
	return odb.writeDB
}

// Transaction 执行事务（总是使用写库）
func (odb *OptimizedDB) Transaction(fn func(*gorm.DB) error) error {
	return odb.writeDB.Transaction(fn)
}

// WithTimeout 设置查询超时
func (odb *OptimizedDB) WithTimeout(timeout time.Duration) *gorm.DB {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	return odb.DB.WithContext(ctx)
}

// IndexManager 索引管理器
type IndexManager struct {
	db *gorm.DB
}

// NewIndexManager 创建索引管理器
func NewIndexManager(db *gorm.DB) *IndexManager {
	return &IndexManager{db: db}
}

// CreateIndex 创建索引
func (im *IndexManager) CreateIndex(tableName, indexName string, columns []string, unique bool) error {
	var indexType string
	if unique {
		indexType = "UNIQUE INDEX"
	} else {
		indexType = "INDEX"
	}

	sql := fmt.Sprintf("CREATE %s %s ON %s (%s)",
		indexType, indexName, tableName, joinColumns(columns))

	return im.db.Exec(sql).Error
}

// DropIndex 删除索引
func (im *IndexManager) DropIndex(tableName, indexName string) error {
	sql := fmt.Sprintf("DROP INDEX %s ON %s", indexName, tableName)
	return im.db.Exec(sql).Error
}

// ListIndexes 列出表的所有索引
func (im *IndexManager) ListIndexes(tableName string) ([]IndexInfo, error) {
	var indexes []IndexInfo
	sql := `
		SELECT 
			INDEX_NAME as name,
			COLUMN_NAME as column_name,
			NON_UNIQUE = 0 as is_unique,
			INDEX_TYPE as type
		FROM INFORMATION_SCHEMA.STATISTICS 
		WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
		ORDER BY INDEX_NAME, SEQ_IN_INDEX
	`

	err := im.db.Raw(sql, tableName).Scan(&indexes).Error
	return indexes, err
}

// IndexInfo 索引信息
type IndexInfo struct {
	Name       string `json:"name"`
	ColumnName string `json:"column_name"`
	IsUnique   bool   `json:"is_unique"`
	Type       string `json:"type"`
}

// QueryOptimizer 查询优化器
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// ExplainQuery 分析查询执行计划
func (qo *QueryOptimizer) ExplainQuery(query string, args ...interface{}) ([]ExplainResult, error) {
	var results []ExplainResult
	explainSQL := "EXPLAIN " + query

	err := qo.db.Raw(explainSQL, args...).Scan(&results).Error
	return results, err
}

// ExplainResult 查询执行计划结果
type ExplainResult struct {
	ID           int     `json:"id"`
	SelectType   string  `json:"select_type"`
	Table        string  `json:"table"`
	Partitions   *string `json:"partitions"`
	Type         string  `json:"type"`
	PossibleKeys *string `json:"possible_keys"`
	Key          *string `json:"key"`
	KeyLen       *string `json:"key_len"`
	Ref          *string `json:"ref"`
	Rows         int     `json:"rows"`
	Filtered     float64 `json:"filtered"`
	Extra        *string `json:"extra"`
}

// AnalyzeSlowQueries 分析慢查询
func (qo *QueryOptimizer) AnalyzeSlowQueries(limit int) ([]SlowQuery, error) {
	var queries []SlowQuery
	sql := `
		SELECT 
			sql_text,
			exec_count,
			avg_timer_wait/********** as avg_time_seconds,
			max_timer_wait/********** as max_time_seconds,
			sum_timer_wait/********** as total_time_seconds
		FROM performance_schema.events_statements_summary_by_digest 
		WHERE schema_name = DATABASE()
		ORDER BY avg_timer_wait DESC 
		LIMIT ?
	`

	err := qo.db.Raw(sql, limit).Scan(&queries).Error
	return queries, err
}

// SlowQuery 慢查询信息
type SlowQuery struct {
	SQLText          string  `json:"sql_text"`
	ExecCount        int     `json:"exec_count"`
	AvgTimeSeconds   float64 `json:"avg_time_seconds"`
	MaxTimeSeconds   float64 `json:"max_time_seconds"`
	TotalTimeSeconds float64 `json:"total_time_seconds"`
}

// ConnectionPoolMonitor 连接池监控器
type ConnectionPoolMonitor struct {
	db *gorm.DB
}

// NewConnectionPoolMonitor 创建连接池监控器
func NewConnectionPoolMonitor(db *gorm.DB) *ConnectionPoolMonitor {
	return &ConnectionPoolMonitor{db: db}
}

// GetPoolStats 获取连接池统计信息
func (cpm *ConnectionPoolMonitor) GetPoolStats() (*PoolStats, error) {
	sqlDB, err := cpm.db.DB()
	if err != nil {
		return nil, err
	}

	stats := sqlDB.Stats()
	return &PoolStats{
		MaxOpenConnections: stats.MaxOpenConnections,
		OpenConnections:    stats.OpenConnections,
		InUse:              stats.InUse,
		Idle:               stats.Idle,
		WaitCount:          stats.WaitCount,
		WaitDuration:       stats.WaitDuration,
		MaxIdleClosed:      stats.MaxIdleClosed,
		MaxLifetimeClosed:  stats.MaxLifetimeClosed,
	}, nil
}

// PoolStats 连接池统计信息
type PoolStats struct {
	MaxOpenConnections int           `json:"max_open_connections"`
	OpenConnections    int           `json:"open_connections"`
	InUse              int           `json:"in_use"`
	Idle               int           `json:"idle"`
	WaitCount          int64         `json:"wait_count"`
	WaitDuration       time.Duration `json:"wait_duration"`
	MaxIdleClosed      int64         `json:"max_idle_closed"`
	MaxLifetimeClosed  int64         `json:"max_lifetime_closed"`
}

// DatabaseHealthChecker 数据库健康检查器
type DatabaseHealthChecker struct {
	db *gorm.DB
}

// NewDatabaseHealthChecker 创建数据库健康检查器
func NewDatabaseHealthChecker(db *gorm.DB) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{db: db}
}

// CheckHealth 检查数据库健康状态
func (dhc *DatabaseHealthChecker) CheckHealth() *HealthStatus {
	status := &HealthStatus{
		Timestamp: time.Now(),
	}

	// 检查连接
	sqlDB, err := dhc.db.DB()
	if err != nil {
		status.Status = "unhealthy"
		status.Error = err.Error()
		return status
	}

	// Ping测试
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		status.Status = "unhealthy"
		status.Error = err.Error()
		return status
	}

	// 获取连接池状态
	stats := sqlDB.Stats()
	status.Status = "healthy"
	status.ConnectionPool = &stats

	// 检查慢查询
	var slowQueryCount int64
	dhc.db.Raw("SELECT COUNT(*) FROM performance_schema.events_statements_summary_by_digest WHERE avg_timer_wait > **********").Scan(&slowQueryCount)
	status.SlowQueryCount = slowQueryCount

	return status
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status         string      `json:"status"`
	Timestamp      time.Time   `json:"timestamp"`
	Error          string      `json:"error,omitempty"`
	ConnectionPool interface{} `json:"connection_pool,omitempty"`
	SlowQueryCount int64       `json:"slow_query_count"`
}

// 辅助函数
func joinColumns(columns []string) string {
	result := ""
	for i, col := range columns {
		if i > 0 {
			result += ", "
		}
		result += col
	}
	return result
}
