package config

import (
	"os"
	"testing"
)

func TestLoad(t *testing.T) {
	// 测试加载默认配置
	cfg, err := Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 验证基本配置
	if cfg.Server.Mode == "" {
		t.Error("Server mode should not be empty")
	}

	if cfg.Server.API.Port == "" {
		t.Error("API port should not be empty")
	}

	if cfg.Server.Admin.Port == "" {
		t.Error("Admin port should not be empty")
	}

	if cfg.JWT.Secret == "" {
		t.Error("JWT secret should not be empty")
	}
}

func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	tempConfig := `
server:
  mode: test
  api:
    host: "localhost"
    port: "9999"
  admin:
    host: "localhost"
    port: "9998"

database:
  driver: mysql
  host: localhost
  port: 3306
  username: test
  password: test
  database: test
  charset: utf8mb4

redis:
  enabled: false
  host: localhost
  port: 6379
  password: ""
  database: 0

mongodb:
  enabled: false
  host: localhost
  port: 27017
  username: ""
  password: ""
  database: test_db
  auth_source: admin
  max_pool_size: 10
  min_pool_size: 1

jwt:
  secret: "test-secret-key-for-unit-testing"
  expire_hours: 1

log:
  level: "debug"
  format: "text"
  output: "stdout"
  file_path: ""

upload:
  max_size: 1048576
  allowed_types: ["jpg", "png"]
  upload_path: "./test_uploads"

security:
  cors_allowed_origins: ["http://localhost:3000"]
  rate_limit_requests: 100
  rate_limit_window: 60
  production_mode: false
  debug_mode: true
`

	// 写入临时文件
	tmpFile, err := os.CreateTemp("", "test_config_*.yaml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(tempConfig); err != nil {
		t.Fatalf("Failed to write temp config: %v", err)
	}
	tmpFile.Close()

	// 测试加载临时配置
	cfg, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load test config: %v", err)
	}

	// 验证配置值
	if cfg.Server.Mode != "test" {
		t.Errorf("Expected server mode 'test', got '%s'", cfg.Server.Mode)
	}

	if cfg.Server.API.Port != "9999" {
		t.Errorf("Expected API port '9999', got '%s'", cfg.Server.API.Port)
	}

	if cfg.JWT.Secret != "test-secret-key-for-unit-testing" {
		t.Errorf("Expected JWT secret 'test-secret-key-for-unit-testing', got '%s'", cfg.JWT.Secret)
	}

	if cfg.Redis.Enabled != false {
		t.Errorf("Expected Redis enabled false, got %v", cfg.Redis.Enabled)
	}

	// 验证MongoDB配置
	if cfg.MongoDB.Enabled != false {
		t.Errorf("Expected MongoDB enabled false, got %v", cfg.MongoDB.Enabled)
	}

	if cfg.MongoDB.Database != "test_db" {
		t.Errorf("Expected MongoDB database 'test_db', got '%s'", cfg.MongoDB.Database)
	}

	if cfg.MongoDB.Port != 27017 {
		t.Errorf("Expected MongoDB port 27017, got %d", cfg.MongoDB.Port)
	}
}
