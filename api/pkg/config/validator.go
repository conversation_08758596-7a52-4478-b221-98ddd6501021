package config

import (
	"fmt"
	"net"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	config *Config
	errors []string
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(config *Config) *ConfigValidator {
	return &ConfigValidator{
		config: config,
		errors: make([]string, 0),
	}
}

// Validate 验证配置
func (cv *ConfigValidator) Validate() error {
	cv.errors = make([]string, 0)

	// 验证服务器配置
	cv.validateServerConfig()

	// 验证数据库配置
	cv.validateDatabaseConfig()

	// 验证Redis配置
	cv.validateRedisConfig()

	// 验证JWT配置
	cv.validateJWTConfig()

	// 验证日志配置
	cv.validateLogConfig()

	// 验证安全配置
	cv.validateSecurityConfig()

	// 验证环境变量
	cv.validateEnvironmentVariables()

	if len(cv.errors) > 0 {
		return fmt.Errorf("configuration validation failed:\n%s", strings.Join(cv.errors, "\n"))
	}

	return nil
}

// validateServerConfig 验证服务器配置
func (cv *ConfigValidator) validateServerConfig() {
	// 验证API服务器配置
	if cv.config.Server.API.Host == "" {
		cv.addError("server.api.host cannot be empty")
	}
	if cv.config.Server.API.Port == "" {
		cv.addError("server.api.port cannot be empty")
	}

	// 验证Admin服务器配置
	if cv.config.Server.Admin.Host == "" {
		cv.addError("server.admin.host cannot be empty")
	}
	if cv.config.Server.Admin.Port == "" {
		cv.addError("server.admin.port cannot be empty")
	}

	// 验证端口不能相同
	if cv.config.Server.API.Port == cv.config.Server.Admin.Port {
		cv.addError("server.api.port and server.admin.port cannot be the same")
	}

	// 验证服务器模式
	if cv.config.Server.Mode == "" {
		cv.addError("server.mode cannot be empty")
	}

	// 验证模式
	validModes := []string{"debug", "release", "test"}
	if !cv.isValidMode(cv.config.Server.Mode, validModes) {
		cv.addError(fmt.Sprintf("server.mode must be one of: %s", strings.Join(validModes, ", ")))
	}
}

// validateDatabaseConfig 验证数据库配置
func (cv *ConfigValidator) validateDatabaseConfig() {
	// 验证数据库类型
	validDrivers := []string{"mysql", "postgres", "sqlite"}
	if !cv.isValidMode(cv.config.Database.Driver, validDrivers) {
		cv.addError(fmt.Sprintf("database.driver must be one of: %s", strings.Join(validDrivers, ", ")))
	}

	// 验证主机和端口
	if cv.config.Database.Host == "" {
		cv.addError("database.host cannot be empty")
	}

	if cv.config.Database.Port <= 0 || cv.config.Database.Port > 65535 {
		cv.addError("database.port must be between 1 and 65535")
	}

	// 验证数据库名称
	if cv.config.Database.Database == "" {
		cv.addError("database.database cannot be empty")
	}

	// 验证用户名
	if cv.config.Database.Username == "" {
		cv.addError("database.username cannot be empty")
	}

	// 验证连接池配置
	if cv.config.Database.MaxOpenConns <= 0 {
		cv.addError("database.max_open_conns must be positive")
	}

	if cv.config.Database.MaxIdleConns <= 0 {
		cv.addError("database.max_idle_conns must be positive")
	}

	if cv.config.Database.MaxIdleConns > cv.config.Database.MaxOpenConns {
		cv.addError("database.max_idle_conns cannot be greater than max_open_conns")
	}

	// 验证连接生命周期
	if cv.config.Database.ConnMaxLifetime <= 0 {
		cv.addError("database.conn_max_lifetime must be positive")
	}

	// 验证读写分离配置
	if cv.config.Database.ReadHost != "" {
		if cv.config.Database.ReadPort <= 0 || cv.config.Database.ReadPort > 65535 {
			cv.addError("database.read_port must be between 1 and 65535")
		}
	}
}

// validateRedisConfig 验证Redis配置
func (cv *ConfigValidator) validateRedisConfig() {
	if !cv.config.Redis.Enabled {
		return
	}

	// 验证主机和端口
	if cv.config.Redis.Host == "" {
		cv.addError("redis.host cannot be empty when redis is enabled")
	}

	if cv.config.Redis.Port <= 0 || cv.config.Redis.Port > 65535 {
		cv.addError("redis.port must be between 1 and 65535")
	}

	// 验证数据库编号
	if cv.config.Redis.Database < 0 || cv.config.Redis.Database > 15 {
		cv.addError("redis.database must be between 0 and 15")
	}
}

// validateJWTConfig 验证JWT配置
func (cv *ConfigValidator) validateJWTConfig() {
	// 验证密钥长度
	if len(cv.config.JWT.Secret) < 32 {
		cv.addError("jwt.secret must be at least 32 characters long")
	}

	// 验证过期时间
	if cv.config.JWT.ExpireHours <= 0 {
		cv.addError("jwt.expire_hours must be positive")
	}
}

// validateLogConfig 验证日志配置
func (cv *ConfigValidator) validateLogConfig() {
	// 验证日志级别
	validLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
	if !cv.isValidMode(cv.config.Log.Level, validLevels) {
		cv.addError(fmt.Sprintf("log.level must be one of: %s", strings.Join(validLevels, ", ")))
	}

	// 验证日志文件路径
	if cv.config.Log.FilePath != "" {
		dir := filepath.Dir(cv.config.Log.FilePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			cv.addError(fmt.Sprintf("cannot create log directory %s: %v", dir, err))
		}
	}
}

// validateSecurityConfig 验证安全配置
func (cv *ConfigValidator) validateSecurityConfig() {
	// 验证CORS配置
	if len(cv.config.Security.CORSAllowedOrigins) == 0 {
		cv.addError("security.cors_allowed_origins cannot be empty")
	}

	// 验证允许的源
	for _, origin := range cv.config.Security.CORSAllowedOrigins {
		if origin != "*" {
			if _, err := url.Parse(origin); err != nil {
				cv.addError(fmt.Sprintf("invalid origin URL: %s", origin))
			}
		}
	}

	// 验证速率限制
	if cv.config.Security.RateLimitRequests <= 0 {
		cv.addError("security.rate_limit_requests must be positive")
	}

	if cv.config.Security.RateLimitWindow <= 0 {
		cv.addError("security.rate_limit_window must be positive")
	}
}

// validateEnvironmentVariables 验证环境变量
func (cv *ConfigValidator) validateEnvironmentVariables() {
	// 检查必需的环境变量
	requiredEnvVars := []string{
		"DB_PASSWORD",
		"JWT_SECRET",
	}

	for _, envVar := range requiredEnvVars {
		if os.Getenv(envVar) == "" {
			cv.addError(fmt.Sprintf("required environment variable %s is not set", envVar))
		}
	}

	// 验证敏感信息不在配置文件中
	if cv.config.Database.Password != "" && cv.config.Database.Password != "${DB_PASSWORD}" {
		cv.addError("database password should be set via environment variable DB_PASSWORD")
	}

	if cv.config.JWT.Secret != "" && cv.config.JWT.Secret != "${JWT_SECRET}" {
		cv.addError("JWT secret should be set via environment variable JWT_SECRET")
	}
}

// isValidMode 检查模式是否有效
func (cv *ConfigValidator) isValidMode(mode string, validModes []string) bool {
	for _, validMode := range validModes {
		if mode == validMode {
			return true
		}
	}
	return false
}

// addError 添加错误
func (cv *ConfigValidator) addError(message string) {
	cv.errors = append(cv.errors, message)
}

// GetErrors 获取所有错误
func (cv *ConfigValidator) GetErrors() []string {
	return cv.errors
}

// ValidateNetworkConnectivity 验证网络连接性
func (cv *ConfigValidator) ValidateNetworkConnectivity() error {
	var errors []string

	// 验证数据库连接
	dbAddr := fmt.Sprintf("%s:%d", cv.config.Database.Host, cv.config.Database.Port)
	if err := cv.checkTCPConnection(dbAddr, 5*time.Second); err != nil {
		errors = append(errors, fmt.Sprintf("cannot connect to database at %s: %v", dbAddr, err))
	}

	// 验证Redis连接（如果启用）
	if cv.config.Redis.Enabled {
		redisAddr := fmt.Sprintf("%s:%d", cv.config.Redis.Host, cv.config.Redis.Port)
		if err := cv.checkTCPConnection(redisAddr, 5*time.Second); err != nil {
			errors = append(errors, fmt.Sprintf("cannot connect to redis at %s: %v", redisAddr, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("network connectivity validation failed:\n%s", strings.Join(errors, "\n"))
	}

	return nil
}

// checkTCPConnection 检查TCP连接
func (cv *ConfigValidator) checkTCPConnection(address string, timeout time.Duration) error {
	conn, err := net.DialTimeout("tcp", address, timeout)
	if err != nil {
		return err
	}
	defer conn.Close()
	return nil
}

// ValidateConfigSecurity 验证配置安全性
func (cv *ConfigValidator) ValidateConfigSecurity() error {
	var warnings []string

	// 检查是否在生产环境使用调试模式
	if cv.config.Server.Mode == "debug" && os.Getenv("ENV") == "production" {
		warnings = append(warnings, "debug mode should not be used in production")
	}

	// 检查JWT密钥强度
	if len(cv.config.JWT.Secret) < 64 {
		warnings = append(warnings, "JWT secret should be at least 64 characters for better security")
	}

	// 检查是否使用默认密码
	commonPasswords := []string{"password", "123456", "admin", "root"}
	for _, commonPwd := range commonPasswords {
		if cv.config.Database.Password == commonPwd {
			warnings = append(warnings, "database password appears to be a common/weak password")
			break
		}
	}

	// 检查CORS配置
	for _, origin := range cv.config.Security.CORSAllowedOrigins {
		if origin == "*" && os.Getenv("ENV") == "production" {
			warnings = append(warnings, "wildcard CORS origin (*) should not be used in production")
			break
		}
	}

	if len(warnings) > 0 {
		// 这里只是警告，不返回错误
		fmt.Printf("Security warnings:\n%s\n", strings.Join(warnings, "\n"))
	}

	return nil
}

// ValidateConfigFormat 验证配置格式
func (cv *ConfigValidator) ValidateConfigFormat() error {
	var errors []string

	// 验证服务器模式
	validModes := []string{"debug", "release", "test"}
	if cv.config.Server.Mode != "" {
		found := false
		for _, mode := range validModes {
			if cv.config.Server.Mode == mode {
				found = true
				break
			}
		}
		if !found {
			errors = append(errors, fmt.Sprintf("server.mode must be one of: %s", strings.Join(validModes, ", ")))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("configuration format validation failed:\n%s", strings.Join(errors, "\n"))
	}

	return nil
}

// StartupValidator 启动时配置验证器
type StartupValidator struct {
	config *Config
}

// NewStartupValidator 创建启动时验证器
func NewStartupValidator(config *Config) *StartupValidator {
	return &StartupValidator{config: config}
}

// ValidateAll 执行所有验证
func (sv *StartupValidator) ValidateAll() error {
	validator := NewConfigValidator(sv.config)

	// 基本配置验证
	if err := validator.Validate(); err != nil {
		return fmt.Errorf("basic configuration validation failed: %w", err)
	}

	// 格式验证
	if err := validator.ValidateConfigFormat(); err != nil {
		return fmt.Errorf("configuration format validation failed: %w", err)
	}

	// 安全验证（只警告，不阻止启动）
	validator.ValidateConfigSecurity()

	// 网络连接验证（可选，根据需要启用）
	if os.Getenv("VALIDATE_CONNECTIVITY") == "true" {
		if err := validator.ValidateNetworkConnectivity(); err != nil {
			return fmt.Errorf("network connectivity validation failed: %w", err)
		}
	}

	return nil
}

// ValidateEnvironment 验证环境配置
func ValidateEnvironment() error {
	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	validEnvs := []string{"development", "testing", "staging", "production"}
	for _, validEnv := range validEnvs {
		if env == validEnv {
			return nil
		}
	}

	return fmt.Errorf("invalid environment: %s, must be one of: %s", env, strings.Join(validEnvs, ", "))
}
