package config

import (
	"fmt"
	"log"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	MongoDB  MongoDBConfig  `mapstructure:"mongodb"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Log      LogConfig      `mapstructure:"log"`
	Upload   UploadConfig   `mapstructure:"upload"`
	Security SecurityConfig `mapstructure:"security"`
	Wechat   WechatConfig   `mapstructure:"wechat"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Mode  string           `mapstructure:"mode"`
	API   ServerPortConfig `mapstructure:"api"`
	Admin ServerPortConfig `mapstructure:"admin"`
}

// ServerPortConfig 服务器端口配置
type ServerPortConfig struct {
	Host string `mapstructure:"host"`
	Port string `mapstructure:"port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `mapstructure:"driver"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	Charset         string `mapstructure:"charset"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`

	// 读写分离配置
	ReadHost     string `mapstructure:"read_host"`
	ReadPort     int    `mapstructure:"read_port"`
	ReadUsername string `mapstructure:"read_username"`
	ReadPassword string `mapstructure:"read_password"`

	// 性能优化配置
	SlowQueryThreshold int  `mapstructure:"slow_query_threshold"` // 慢查询阈值（毫秒）
	EnableQueryLog     bool `mapstructure:"enable_query_log"`     // 是否启用查询日志
	LogLevel           int  `mapstructure:"log_level"`            // 日志级别
}

// RedisConfig Redis配置
type RedisConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Database int    `mapstructure:"database"`
}

// MongoDBConfig MongoDB配置
type MongoDBConfig struct {
	Enabled    bool   `mapstructure:"enabled"`
	URI        string `mapstructure:"uri"`
	Host       string `mapstructure:"host"`
	Port       int    `mapstructure:"port"`
	Username   string `mapstructure:"username"`
	Password   string `mapstructure:"password"`
	Database   string `mapstructure:"database"`
	AuthSource string `mapstructure:"auth_source"`

	// 连接池配置
	MaxPoolSize      int `mapstructure:"max_pool_size"`
	MinPoolSize      int `mapstructure:"min_pool_size"`
	MaxIdleTimeMS    int `mapstructure:"max_idle_time_ms"`
	ConnectTimeoutMS int `mapstructure:"connect_timeout_ms"`

	// SSL配置
	SSL struct {
		Enabled            bool   `mapstructure:"enabled"`
		CertificateKeyFile string `mapstructure:"certificate_key_file"`
		CAFile             string `mapstructure:"ca_file"`
		InsecureSkipVerify bool   `mapstructure:"insecure_skip_verify"`
	} `mapstructure:"ssl"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret             string `mapstructure:"secret"`
	ExpireHours        int    `mapstructure:"expire_hours"`
	RefreshExpireHours int    `mapstructure:"refresh_expire_hours"`
	Issuer             string `mapstructure:"issuer"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `mapstructure:"level"`
	Format   string `mapstructure:"format"`
	Output   string `mapstructure:"output"`
	FilePath string `mapstructure:"file_path"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxSize      int64    `mapstructure:"max_size"`
	AllowedTypes []string `mapstructure:"allowed_types"`
	UploadPath   string   `mapstructure:"upload_path"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	CORSAllowedOrigins []string `mapstructure:"cors_allowed_origins"`
	RateLimitRequests  int      `mapstructure:"rate_limit_requests"`
	RateLimitWindow    int      `mapstructure:"rate_limit_window"`
	ProductionMode     bool     `mapstructure:"production_mode"`
	DebugMode          bool     `mapstructure:"debug_mode"`
	SecureCookies      bool     `mapstructure:"secure_cookies"`
}

// WechatConfig 微信配置
type WechatConfig struct {
	Miniprogram MiniprogramConfig `mapstructure:"miniprogram"`
}

// MiniprogramConfig 微信小程序配置
type MiniprogramConfig struct {
	AppID          string `mapstructure:"app_id"`
	AppSecret      string `mapstructure:"app_secret"`
	MockEnabled    bool   `mapstructure:"mock_enabled"`
	MockOpenID     string `mapstructure:"mock_openid"`
	MockSessionKey string `mapstructure:"mock_session_key"`
	MockUnionID    string `mapstructure:"mock_unionid"`
	AuthURL        string `mapstructure:"auth_url"`
	Timeout        int    `mapstructure:"timeout"`
}

// Load 加载配置文件
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	// 支持环境变量
	viper.AutomaticEnv()
	viper.SetEnvPrefix("") // 不使用前缀，直接使用环境变量名

	// 绑定环境变量
	bindEnvVars()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Warning: Could not read config file: %v", err)
		// 继续执行，使用环境变量和默认值
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, err
	}

	return &config, nil
}

// LoadConfig 加载指定路径的配置文件（主要用于测试）
func LoadConfig(configPath string) (*Config, error) {
	// 创建新的viper实例以避免全局状态冲突
	v := viper.New()

	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// 支持环境变量
	v.AutomaticEnv()
	v.SetEnvPrefix("") // 不使用前缀，直接使用环境变量名

	// 绑定环境变量到新的viper实例
	bindEnvVarsToViper(v)

	// 设置默认值到新的viper实例
	setDefaultsToViper(v)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	setDefaultsToViper(viper.GetViper())
}

// setDefaultsToViper 设置默认配置值到指定的viper实例
func setDefaultsToViper(v *viper.Viper) {
	v.SetDefault("server.mode", "debug")
	v.SetDefault("server.api.host", "0.0.0.0")
	v.SetDefault("server.api.port", "8080")
	v.SetDefault("server.admin.host", "0.0.0.0")
	v.SetDefault("server.admin.port", "8081")

	v.SetDefault("database.driver", "mysql")
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 3306)
	v.SetDefault("database.charset", "utf8mb4")
	v.SetDefault("database.max_idle_conns", 10)
	v.SetDefault("database.max_open_conns", 100)
	v.SetDefault("database.conn_max_lifetime", 3600)

	v.SetDefault("redis.enabled", false)
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.database", 0)

	// MongoDB默认配置
	v.SetDefault("mongodb.enabled", false)
	v.SetDefault("mongodb.host", "localhost")
	v.SetDefault("mongodb.port", 27017)
	v.SetDefault("mongodb.database", "kids_platform")
	v.SetDefault("mongodb.auth_source", "admin")
	v.SetDefault("mongodb.max_pool_size", 100)
	v.SetDefault("mongodb.min_pool_size", 5)
	v.SetDefault("mongodb.max_idle_time_ms", 300000)
	v.SetDefault("mongodb.connect_timeout_ms", 10000)
	v.SetDefault("mongodb.ssl.enabled", false)
	v.SetDefault("mongodb.ssl.insecure_skip_verify", false)

	v.SetDefault("jwt.expire_hours", 24)
	v.SetDefault("jwt.refresh_expire_hours", 168) // 7天
	v.SetDefault("jwt.issuer", "kids-platform")

	v.SetDefault("log.level", "debug")
	v.SetDefault("log.format", "json")
	v.SetDefault("log.output", "stdout")

	v.SetDefault("upload.max_size", 10485760)
	v.SetDefault("upload.upload_path", "uploads/")

	// 微信配置默认值
	v.SetDefault("wechat.miniprogram.mock_enabled", true)
	v.SetDefault("wechat.miniprogram.auth_url", "https://api.weixin.qq.com/sns/jscode2session")
	v.SetDefault("wechat.miniprogram.timeout", 5000)
}

// ConfigManager 配置管理器，支持热重载
type ConfigManager struct {
	config *Config
	mutex  sync.RWMutex
}

// NewConfigManager 创建配置管理器
func NewConfigManager() (*ConfigManager, error) {
	config, err := Load()
	if err != nil {
		return nil, err
	}

	return &ConfigManager{
		config: config,
	}, nil
}

// GetConfig 获取当前配置
func (cm *ConfigManager) GetConfig() *Config {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.config
}

// StartWatching 开始监听配置文件变化
func (cm *ConfigManager) StartWatching() {
	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Printf("配置文件发生变化: %s", e.Name)

		// 重新加载配置
		var newConfig Config
		if err := viper.Unmarshal(&newConfig); err != nil {
			log.Printf("重新加载配置失败: %v", err)
			return
		}

		// 更新配置
		cm.mutex.Lock()
		cm.config = &newConfig
		cm.mutex.Unlock()

		log.Println("配置已热重载")
	})
}

// bindEnvVars 绑定环境变量
func bindEnvVars() {
	bindEnvVarsToViper(viper.GetViper())
}

// bindEnvVarsToViper 绑定环境变量到指定的viper实例
func bindEnvVarsToViper(v *viper.Viper) {
	// 服务器配置
	v.BindEnv("server.mode", "SERVER_MODE")
	v.BindEnv("server.api_host", "API_HOST")
	v.BindEnv("server.api_port", "API_PORT")
	v.BindEnv("server.admin_host", "ADMIN_HOST")
	v.BindEnv("server.admin_port", "ADMIN_PORT")

	// 数据库配置
	v.BindEnv("database.driver", "DB_DRIVER")
	v.BindEnv("database.host", "DB_HOST")
	v.BindEnv("database.port", "DB_PORT")
	v.BindEnv("database.username", "DB_USERNAME")
	v.BindEnv("database.password", "DB_PASSWORD")
	v.BindEnv("database.database", "DB_DATABASE")
	v.BindEnv("database.charset", "DB_CHARSET")
	v.BindEnv("database.max_idle_conns", "DB_MAX_IDLE_CONNS")
	v.BindEnv("database.max_open_conns", "DB_MAX_OPEN_CONNS")
	v.BindEnv("database.conn_max_lifetime", "DB_CONN_MAX_LIFETIME")

	// Redis配置
	v.BindEnv("redis.enabled", "REDIS_ENABLED")
	v.BindEnv("redis.host", "REDIS_HOST")
	v.BindEnv("redis.port", "REDIS_PORT")
	v.BindEnv("redis.password", "REDIS_PASSWORD")
	v.BindEnv("redis.database", "REDIS_DATABASE")

	// MongoDB配置
	v.BindEnv("mongodb.enabled", "MONGODB_ENABLED")
	v.BindEnv("mongodb.uri", "MONGODB_URI")
	v.BindEnv("mongodb.host", "MONGODB_HOST")
	v.BindEnv("mongodb.port", "MONGODB_PORT")
	v.BindEnv("mongodb.username", "MONGODB_USERNAME")
	v.BindEnv("mongodb.password", "MONGODB_PASSWORD")
	v.BindEnv("mongodb.database", "MONGODB_DATABASE")
	v.BindEnv("mongodb.auth_source", "MONGODB_AUTH_SOURCE")
	v.BindEnv("mongodb.max_pool_size", "MONGODB_MAX_POOL_SIZE")
	v.BindEnv("mongodb.min_pool_size", "MONGODB_MIN_POOL_SIZE")
	v.BindEnv("mongodb.max_idle_time_ms", "MONGODB_MAX_IDLE_TIME_MS")
	v.BindEnv("mongodb.connect_timeout_ms", "MONGODB_CONNECT_TIMEOUT_MS")
	v.BindEnv("mongodb.ssl.enabled", "MONGODB_SSL_ENABLED")
	v.BindEnv("mongodb.ssl.certificate_key_file", "MONGODB_SSL_CERT_KEY_FILE")
	v.BindEnv("mongodb.ssl.ca_file", "MONGODB_SSL_CA_FILE")
	v.BindEnv("mongodb.ssl.insecure_skip_verify", "MONGODB_SSL_INSECURE_SKIP_VERIFY")

	// JWT配置
	v.BindEnv("jwt.secret", "JWT_SECRET")
	v.BindEnv("jwt.expire_hours", "JWT_EXPIRE_HOURS")
	v.BindEnv("jwt.refresh_expire_hours", "JWT_REFRESH_EXPIRE_HOURS")

	// 日志配置
	v.BindEnv("log.level", "LOG_LEVEL")
	v.BindEnv("log.format", "LOG_FORMAT")
	v.BindEnv("log.output", "LOG_OUTPUT")
	v.BindEnv("log.file_path", "LOG_FILE_PATH")

	// 文件上传配置
	v.BindEnv("upload.max_size", "UPLOAD_MAX_SIZE")
	v.BindEnv("upload.upload_path", "UPLOAD_PATH")
	v.BindEnv("upload.allowed_types", "UPLOAD_ALLOWED_TYPES")

	// 安全配置
	v.BindEnv("security.cors_allowed_origins", "CORS_ALLOWED_ORIGINS")
	v.BindEnv("security.rate_limit_requests", "RATE_LIMIT_REQUESTS")
	v.BindEnv("security.rate_limit_window", "RATE_LIMIT_WINDOW")
	v.BindEnv("security.production_mode", "PRODUCTION_MODE")
	v.BindEnv("security.debug_mode", "DEBUG_MODE")
	v.BindEnv("security.secure_cookies", "SECURE_COOKIES")

	// 微信配置
	v.BindEnv("wechat.miniprogram.app_id", "WECHAT_MINIPROGRAM_APP_ID")
	v.BindEnv("wechat.miniprogram.app_secret", "WECHAT_MINIPROGRAM_APP_SECRET")
	v.BindEnv("wechat.miniprogram.mock_enabled", "WECHAT_MINIPROGRAM_MOCK_ENABLED")
	v.BindEnv("wechat.miniprogram.mock_openid", "WECHAT_MINIPROGRAM_MOCK_OPENID")
	v.BindEnv("wechat.miniprogram.mock_session_key", "WECHAT_MINIPROGRAM_MOCK_SESSION_KEY")
	v.BindEnv("wechat.miniprogram.mock_unionid", "WECHAT_MINIPROGRAM_MOCK_UNIONID")
	v.BindEnv("wechat.miniprogram.auth_url", "WECHAT_MINIPROGRAM_AUTH_URL")
	v.BindEnv("wechat.miniprogram.timeout", "WECHAT_MINIPROGRAM_TIMEOUT")
}

// Validate 验证配置
func (c *Config) Validate() error {
	var errs []string

	// 验证JWT密钥
	if c.JWT.Secret == "your-secret-key-change-in-production" || c.JWT.Secret == "" {
		errs = append(errs, "JWT secret must be set and changed from default value")
	}
	if len(c.JWT.Secret) < 32 {
		errs = append(errs, "JWT secret must be at least 32 characters long")
	}

	// 验证数据库配置
	if c.Database.Password == "" {
		errs = append(errs, "database password is required")
	}
	if c.Database.Password == "235lwx123456" && c.Security.ProductionMode {
		errs = append(errs, "database password must be changed from default value in production")
	}

	// 验证生产环境配置
	if c.Security.ProductionMode {
		if c.Security.DebugMode {
			errs = append(errs, "debug mode must be disabled in production")
		}
		if !c.Security.SecureCookies {
			errs = append(errs, "secure cookies must be enabled in production")
		}
		if c.Server.Mode == "debug" {
			errs = append(errs, "server mode must not be 'debug' in production")
		}
	}

	// 验证端口配置
	if c.Server.API.Port == c.Server.Admin.Port {
		errs = append(errs, "API port and Admin port must be different")
	}

	if len(errs) > 0 {
		return fmt.Errorf("configuration validation failed: %s", strings.Join(errs, "; "))
	}

	return nil
}

// IsProduction 检查是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Security.ProductionMode
}

// IsDebug 检查是否为调试模式
func (c *Config) IsDebug() bool {
	return c.Security.DebugMode
}
