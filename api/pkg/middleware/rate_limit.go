package middleware

import (
	"kids-platform/pkg/response"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 简单的内存限流器
type RateLimiter struct {
	visitors map[string]*Visitor
	mutex    sync.RWMutex
	rate     int           // 每分钟允许的请求数
	window   time.Duration // 时间窗口
}

// Visitor 访问者信息
type Visitor struct {
	count     int
	lastReset time.Time
}

// NewRateLimiter 创建限流器
func NewRateLimiter(rate int, window time.Duration) *RateLimiter {
	return &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		window:   window,
	}
}

// RateLimit 限流中间件
func (rl *RateLimiter) RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()

		rl.mutex.Lock()
		defer rl.mutex.Unlock()

		visitor, exists := rl.visitors[ip]
		if !exists {
			rl.visitors[ip] = &Visitor{
				count:     1,
				lastReset: time.Now(),
			}
			c.Next()
			return
		}

		// 检查是否需要重置计数器
		if time.Since(visitor.lastReset) > rl.window {
			visitor.count = 1
			visitor.lastReset = time.Now()
			c.Next()
			return
		}

		// 检查是否超过限制
		if visitor.count >= rl.rate {
			response.Error(c, 429, "请求过于频繁，请稍后再试")
			c.Abort()
			return
		}

		visitor.count++
		c.Next()
	}
}

// DefaultRateLimit 默认限流中间件（每分钟100次请求）
func DefaultRateLimit() gin.HandlerFunc {
	limiter := NewRateLimiter(100, time.Minute)
	return limiter.RateLimit()
}
