package middleware

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

const RequestChildrenIDKey = "X-Children-ID"

func ChildrenMiddlewareGin() gin.HandlerFunc {
	return func(c *gin.Context) {
		childrenIDStr := c.<PERSON>(RequestChildrenIDKey)

		// 将字符串转换为uint类型
		var childrenID uint = 0
		if childrenIDStr != "" {
			if id, err := strconv.ParseUint(childrenIDStr, 10, 32); err == nil {
				childrenID = uint(id)
			}
		}

		// 存储转换后的uint类型到上下文
		c.Set("child_id", childrenID)
		c.Next()
	}
}
