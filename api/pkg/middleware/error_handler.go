package middleware

import (
	"kids-platform/pkg/config"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/response"
	"log"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 错误处理中间件
func ErrorHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 将配置存储到上下文中，供response包使用
		c.Set("config", cfg)

		defer func() {
			if err := recover(); err != nil {
				// 记录panic信息
				stack := debug.Stack()
				log.Printf("Panic recovered: %v\nStack: %s", err, stack)

				// 使用新的错误处理
				response.ErrorWithCode(c, errcode.ErrInternalServer.WithDetails("系统发生异常"))
				c.Abort()
			}
		}()

		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			// 获取最后一个错误
			err := c.Errors.Last().Err
			response.ErrorWithCode(c, err)
			c.Abort()
		}
	}
}

// DatabaseErrorHandler 数据库错误处理中间件（已简化，主要错误处理由ErrorHandler统一处理）
func DatabaseErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		// 数据库错误现在由统一的ErrorHandler处理
	}
}

// ValidationErrorHandler 验证错误处理中间件（已简化）
func ValidationErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		// 验证错误现在由统一的ErrorHandler处理
	}
}

// AuthErrorHandler 认证错误处理中间件（已简化）
func AuthErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		// 认证错误现在由统一的ErrorHandler处理
	}
}

// FileErrorHandler 文件错误处理中间件（已简化）
func FileErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		// 文件错误现在由统一的ErrorHandler处理
	}
}

// NotFoundHandler 404错误处理
func NotFoundHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)
		response.ErrorWithCode(c, errcode.ErrNotFound.WithDetails("请求的资源不存在"))
	}
}

// MethodNotAllowedHandler 405错误处理
func MethodNotAllowedHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)
		response.ErrorWithCode(c, errcode.ErrMethodNotAllowed.WithDetails("请求方法不被允许"))
	}
}

// TimeoutHandler 超时错误处理
func TimeoutHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)
		c.Next()

		// 检查是否超时
		if c.IsAborted() && c.Writer.Status() == http.StatusRequestTimeout {
			response.ErrorWithCode(c, errcode.ErrTimeout.WithDetails("请求处理超时"))
		}
	}
}

// RateLimitErrorHandler 限流错误处理
func RateLimitErrorHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)
		c.Next()

		// 检查是否被限流
		if c.IsAborted() && c.Writer.Status() == http.StatusTooManyRequests {
			response.ErrorWithCode(c, errcode.ErrTooManyRequests.WithDetails("请求过于频繁，请稍后重试"))
		}
	}
}

// CORSErrorHandler CORS错误处理
func CORSErrorHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)
		c.Next()

		// 检查CORS错误
		if c.IsAborted() && c.Writer.Status() == http.StatusForbidden {
			origin := c.GetHeader("Origin")
			if origin != "" {
				response.ErrorWithCode(c, errcode.ErrForbidden.WithDetails("跨域请求被拒绝"))
			}
		}
	}
}

// ContentTypeErrorHandler 内容类型错误处理
func ContentTypeErrorHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)

		// 检查Content-Type
		if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut || c.Request.Method == http.MethodPatch {
			contentType := c.GetHeader("Content-Type")
			if contentType == "" {
				response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails("缺少Content-Type头"))
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RequestSizeErrorHandler 请求大小错误处理
func RequestSizeErrorHandler(cfg *config.Config, maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("config", cfg)

		if c.Request.ContentLength > maxSize {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails("请求体过大"))
			c.Abort()
			return
		}

		c.Next()
	}
}

// SecurityHeadersErrorHandler 安全头错误处理
func SecurityHeadersErrorHandler(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加安全头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		if cfg.IsProduction() {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		c.Next()
	}
}
