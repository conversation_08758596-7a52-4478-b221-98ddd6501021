package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/response"
	"kids-platform/pkg/validator"
	"net/http"
	"reflect"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
)

var (
	// SQL注入检测正则
	sqlInjectionPatterns = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(union\s+select|select\s+.*\s+from|insert\s+into|update\s+.*\s+set|delete\s+from)`),
		regexp.MustCompile(`(?i)(drop\s+table|create\s+table|alter\s+table|truncate\s+table)`),
		regexp.MustCompile(`(?i)(exec\s*\(|execute\s*\(|sp_|xp_)`),
		regexp.MustCompile(`(?i)(script\s*>|javascript:|vbscript:|data:)`),
	}

	// XSS检测正则
	xssPatterns = []*regexp.Regexp{
		regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`),
		regexp.MustCompile(`(?i)<iframe[^>]*>.*?</iframe>`),
		regexp.MustCompile(`(?i)<object[^>]*>.*?</object>`),
		regexp.MustCompile(`(?i)<embed[^>]*>.*?</embed>`),
		regexp.MustCompile(`(?i)on\w+\s*=`),
		regexp.MustCompile(`(?i)javascript:`),
		regexp.MustCompile(`(?i)vbscript:`),
		regexp.MustCompile(`(?i)data:text/html`),
	}

	// 路径遍历检测正则
	pathTraversalPatterns = []*regexp.Regexp{
		regexp.MustCompile(`\.\.[\\/]`),
		regexp.MustCompile(`[\\/]\.\.`),
		regexp.MustCompile(`%2e%2e[\\/]`),
		regexp.MustCompile(`[\\/]%2e%2e`),
	}
)

// InputValidationConfig 输入验证配置
type InputValidationConfig struct {
	EnableSQLInjectionCheck  bool
	EnableXSSCheck           bool
	EnablePathTraversalCheck bool
	MaxRequestSize           int64
	SanitizeInput            bool
}

// DefaultInputValidationConfig 默认输入验证配置
func DefaultInputValidationConfig() InputValidationConfig {
	return InputValidationConfig{
		EnableSQLInjectionCheck:  true,
		EnableXSSCheck:           true,
		EnablePathTraversalCheck: true,
		MaxRequestSize:           10 * 1024 * 1024, // 10MB
		SanitizeInput:            true,
	}
}

// InputValidation 输入验证中间件
func InputValidation(config ...InputValidationConfig) gin.HandlerFunc {
	cfg := DefaultInputValidationConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(c *gin.Context) {
		// 检查请求大小
		if c.Request.ContentLength > cfg.MaxRequestSize {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails("请求体过大"))
			c.Abort()
			return
		}

		// 验证查询参数
		if err := validateQueryParams(c, cfg); err != nil {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails( err.Error()))
			c.Abort()
			return
		}

		// 验证路径参数
		if err := validatePathParams(c, cfg); err != nil {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails( err.Error()))
			c.Abort()
			return
		}

		// 验证请求体
		if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut || c.Request.Method == http.MethodPatch {
			if err := validateRequestBody(c, cfg); err != nil {
				response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails( err.Error()))
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// validateQueryParams 验证查询参数
func validateQueryParams(c *gin.Context, cfg InputValidationConfig) error {
	for key, values := range c.Request.URL.Query() {
		for _, value := range values {
			if err := validateString(value, cfg); err != nil {
				return fmt.Errorf("查询参数 %s 包含不安全内容: %v", key, err)
			}
		}
	}
	return nil
}

// validatePathParams 验证路径参数
func validatePathParams(c *gin.Context, cfg InputValidationConfig) error {
	for _, param := range c.Params {
		if err := validateString(param.Value, cfg); err != nil {
			return fmt.Errorf("路径参数 %s 包含不安全内容: %v", param.Key, err)
		}
	}
	return nil
}

// validateRequestBody 验证请求体
func validateRequestBody(c *gin.Context, cfg InputValidationConfig) error {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return fmt.Errorf("读取请求体失败: %v", err)
	}

	// 恢复请求体供后续处理
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 如果是JSON请求，验证JSON内容
	if strings.Contains(c.GetHeader("Content-Type"), "application/json") {
		return validateJSONBody(body, cfg)
	}

	// 验证原始请求体
	return validateString(string(body), cfg)
}

// validateJSONBody 验证JSON请求体
func validateJSONBody(body []byte, cfg InputValidationConfig) error {
	var data interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return fmt.Errorf("JSON格式错误: %v", err)
	}

	return validateJSONValue(data, cfg, "")
}

// validateJSONValue 递归验证JSON值
func validateJSONValue(value interface{}, cfg InputValidationConfig, path string) error {
	switch v := value.(type) {
	case string:
		if err := validateString(v, cfg); err != nil {
			return fmt.Errorf("字段 %s 包含不安全内容: %v", path, err)
		}
	case map[string]interface{}:
		for key, val := range v {
			newPath := key
			if path != "" {
				newPath = path + "." + key
			}
			if err := validateJSONValue(val, cfg, newPath); err != nil {
				return err
			}
		}
	case []interface{}:
		for i, val := range v {
			newPath := fmt.Sprintf("%s[%d]", path, i)
			if err := validateJSONValue(val, cfg, newPath); err != nil {
				return err
			}
		}
	}
	return nil
}

// validateString 验证字符串
func validateString(value string, cfg InputValidationConfig) error {
	// SQL注入检测
	if cfg.EnableSQLInjectionCheck {
		for _, pattern := range sqlInjectionPatterns {
			if pattern.MatchString(value) {
				return fmt.Errorf("检测到SQL注入攻击")
			}
		}
	}

	// XSS检测
	if cfg.EnableXSSCheck {
		for _, pattern := range xssPatterns {
			if pattern.MatchString(value) {
				return fmt.Errorf("检测到XSS攻击")
			}
		}
	}

	// 路径遍历检测
	if cfg.EnablePathTraversalCheck {
		for _, pattern := range pathTraversalPatterns {
			if pattern.MatchString(value) {
				return fmt.Errorf("检测到路径遍历攻击")
			}
		}
	}

	return nil
}

// ValidateStruct 验证结构体中间件
func ValidateStruct(structType interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 创建结构体实例
		structValue := reflect.New(reflect.TypeOf(structType)).Interface()

		// 绑定请求数据
		if err := c.ShouldBindJSON(structValue); err != nil {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails( "请求参数格式错误: "+err.Error()))
			c.Abort()
			return
		}

		// 使用增强验证器验证
		if err := validator.ValidateStruct(structValue); err != nil {
			response.ErrorWithCode(c, errcode.ErrInvalidParam.WithDetails( err.Error()))
			c.Abort()
			return
		}

		// 将验证后的结构体存储到上下文
		c.Set("validated_data", structValue)
		c.Next()
	}
}

// SanitizeInput 输入清理中间件
func SanitizeInput() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 清理查询参数
		sanitizeQueryParams(c)

		// 清理路径参数
		sanitizePathParams(c)

		c.Next()
	}
}

// sanitizeQueryParams 清理查询参数
func sanitizeQueryParams(c *gin.Context) {
	query := c.Request.URL.Query()
	for key, values := range query {
		for i, value := range values {
			values[i] = validator.SanitizeInput(value)
		}
		query[key] = values
	}
	c.Request.URL.RawQuery = query.Encode()
}

// sanitizePathParams 清理路径参数
func sanitizePathParams(c *gin.Context) {
	for i, param := range c.Params {
		c.Params[i].Value = validator.SanitizeInput(param.Value)
	}
}
