package validator

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

var (
	// 用户名正则表达式 (字母数字下划线，不能以数字开头)
	usernameRegex = regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_]{2,19}$`)
	// 手机号正则表达式 (中国大陆) - 使用不同的变量名避免冲突
	enhancedPhoneRegex = regexp.MustCompile(`^1[3-9]\d{9}$`)
	// 密码组件检测正则
	hasLowerRegex   = regexp.MustCompile(`[a-z]`)
	hasUpperRegex   = regexp.MustCompile(`[A-Z]`)
	hasDigitRegex   = regexp.MustCompile(`\d`)
	hasSpecialRegex = regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`)
	// SQL注入检测正则
	sqlInjectionRegex = regexp.MustCompile(`(?i)(union|select|insert|update|delete|drop|create|alter|exec|script|javascript|vbscript|onload|onerror|onclick)`)
)

// InitEnhancedValidator 初始化增强验证器
func InitEnhancedValidator() {
	// 获取Gin的验证器实例
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册自定义验证规则
		v.RegisterValidation("strong_password", validateStrongPassword)
		v.RegisterValidation("safe_username", validateSafeUsername)
		v.RegisterValidation("china_phone", validateChinaPhone)
		v.RegisterValidation("no_sql_injection", validateNoSQLInjection)
		v.RegisterValidation("safe_string", validateSafeString)
		v.RegisterValidation("alphanumeric_underscore", validateAlphanumericUnderscore)
	}
}

// validateStrongPassword 验证强密码
func validateStrongPassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()

	// 基本长度检查
	if len(password) < 8 || len(password) > 128 {
		return false
	}

	// 检查是否包含必要的字符类型
	var (
		hasLower   = false
		hasUpper   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	// 至少包含3种类型的字符
	count := 0
	if hasLower {
		count++
	}
	if hasUpper {
		count++
	}
	if hasNumber {
		count++
	}
	if hasSpecial {
		count++
	}

	return count >= 3
}

// validateSafeUsername 验证安全用户名
func validateSafeUsername(fl validator.FieldLevel) bool {
	username := fl.Field().String()

	// 长度检查
	if len(username) < 3 || len(username) > 20 {
		return false
	}

	// 格式检查
	if !usernameRegex.MatchString(username) {
		return false
	}

	// 禁用词检查
	forbiddenWords := []string{"admin", "root", "system", "test", "guest", "null", "undefined"}
	lowerUsername := strings.ToLower(username)
	for _, word := range forbiddenWords {
		if strings.Contains(lowerUsername, word) {
			return false
		}
	}

	return true
}

// validateChinaPhone 验证中国大陆手机号
func validateChinaPhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	if phone == "" {
		return true // 允许空值
	}
	return enhancedPhoneRegex.MatchString(phone)
}

// validateNoSQLInjection 检测SQL注入
func validateNoSQLInjection(fl validator.FieldLevel) bool {
	value := strings.ToLower(fl.Field().String())
	return !sqlInjectionRegex.MatchString(value)
}

// validateSafeString 验证安全字符串 (防止XSS)
func validateSafeString(fl validator.FieldLevel) bool {
	value := fl.Field().String()

	// 检查危险字符
	dangerousChars := []string{"<", ">", "\"", "'", "&", "javascript:", "data:", "vbscript:"}
	lowerValue := strings.ToLower(value)

	for _, char := range dangerousChars {
		if strings.Contains(lowerValue, char) {
			return false
		}
	}

	return true
}

// validateAlphanumericUnderscore 验证字母数字下划线
func validateAlphanumericUnderscore(fl validator.FieldLevel) bool {
	value := fl.Field().String()
	for _, char := range value {
		if !unicode.IsLetter(char) && !unicode.IsNumber(char) && char != '_' {
			return false
		}
	}
	return true
}

// ValidateStruct 验证结构体并返回友好的错误信息
func ValidateStruct(s interface{}) error {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		err := v.Struct(s)
		if err == nil {
			return nil
		}

		var errorMessages []string

		for _, err := range err.(validator.ValidationErrors) {
			field := err.Field()
			tag := err.Tag()

			var message string
			switch tag {
			case "required":
				message = fmt.Sprintf("%s 是必填字段", field)
			case "min":
				message = fmt.Sprintf("%s 长度不能少于 %s 个字符", field, err.Param())
			case "max":
				message = fmt.Sprintf("%s 长度不能超过 %s 个字符", field, err.Param())
			case "email":
				message = fmt.Sprintf("%s 必须是有效的邮箱地址", field)
			case "alphanum":
				message = fmt.Sprintf("%s 只能包含字母和数字", field)
			case "e164":
				message = fmt.Sprintf("%s 必须是有效的国际电话号码格式", field)
			case "oneof":
				message = fmt.Sprintf("%s 的值必须是 %s 中的一个", field, err.Param())
			case "containsany":
				message = fmt.Sprintf("%s 必须包含特殊字符 (%s)", field, err.Param())
			case "strong_password":
				message = fmt.Sprintf("%s 必须是强密码(8-128位，包含大小写字母、数字、特殊字符中的至少3种)", field)
			case "safe_username":
				message = fmt.Sprintf("%s 必须是安全的用户名(3-20位，字母开头，只能包含字母数字下划线)", field)
			case "china_phone":
				message = fmt.Sprintf("%s 必须是有效的中国大陆手机号", field)
			case "no_sql_injection":
				message = fmt.Sprintf("%s 包含不安全的内容", field)
			case "safe_string":
				message = fmt.Sprintf("%s 包含不安全的字符", field)
			case "alphanumeric_underscore":
				message = fmt.Sprintf("%s 只能包含字母、数字和下划线", field)
			default:
				message = fmt.Sprintf("%s 验证失败: %s", field, tag)
			}

			errorMessages = append(errorMessages, message)
		}

		return fmt.Errorf("验证失败: %s", strings.Join(errorMessages, "; "))
	}
	return fmt.Errorf("无法获取验证器实例")
}

// SanitizeInput 清理输入数据
func SanitizeInput(input string) string {
	// 移除前后空格
	input = strings.TrimSpace(input)

	// 替换危险字符
	replacements := map[string]string{
		"<":  "&lt;",
		">":  "&gt;",
		"\"": "&quot;",
		"'":  "&#x27;",
		"&":  "&amp;",
	}

	for old, new := range replacements {
		input = strings.ReplaceAll(input, old, new)
	}

	return input
}

// ValidateAndSanitize 验证并清理结构体
func ValidateAndSanitize(s interface{}) error {
	// 首先进行验证
	if err := ValidateStruct(s); err != nil {
		return err
	}

	// TODO: 实现结构体字段的自动清理
	// 这里可以使用反射来自动清理字符串字段

	return nil
}
