package validator

import (
	"testing"

	"github.com/go-playground/validator/v10"
)

func TestBasicValidation(t *testing.T) {
	// 创建验证器实例
	v := validator.New()

	// 测试基本验证
	type User struct {
		Name  string `validate:"required,min=2,max=20"`
		Email string `validate:"required,email"`
		Age   int    `validate:"gte=0,lte=130"`
	}

	// 测试有效数据
	validUser := User{
		Name:  "张三",
		Email: "<EMAIL>",
		Age:   25,
	}

	err := v.Struct(validUser)
	if err != nil {
		t.Errorf("Valid user should pass validation, got error: %v", err)
	}

	// 测试无效数据
	invalidUser := User{
		Name:  "", // 空名称
		Email: "invalid-email",
		Age:   -1,
	}

	err = v.Struct(invalidUser)
	if err == nil {
		t.Error("Invalid user should fail validation")
	}
}

func TestCustomPasswordValidation(t *testing.T) {
	v := validator.New()

	// 注册自定义密码验证
	v.RegisterValidation("strong_password", func(fl validator.FieldLevel) bool {
		password := fl.Field().String()

		// 长度检查
		if len(password) < 8 {
			return false
		}

		// 字符类型检查
		var hasLower, hasUpper, hasNumber, hasSpecial bool
		for _, char := range password {
			if char >= 'a' && char <= 'z' {
				hasLower = true
			} else if char >= 'A' && char <= 'Z' {
				hasUpper = true
			} else if char >= '0' && char <= '9' {
				hasNumber = true
			} else {
				hasSpecial = true
			}
		}

		// 至少包含3种类型
		count := 0
		if hasLower {
			count++
		}
		if hasUpper {
			count++
		}
		if hasNumber {
			count++
		}
		if hasSpecial {
			count++
		}

		return count >= 3
	})

	type LoginForm struct {
		Password string `validate:"strong_password"`
	}

	// 测试强密码
	strongForm := LoginForm{Password: "StrongPass123!"}
	err := v.Struct(strongForm)
	if err != nil {
		t.Errorf("Strong password should pass validation, got: %v", err)
	}

	// 测试弱密码
	weakForm := LoginForm{Password: "weak"}
	err = v.Struct(weakForm)
	if err == nil {
		t.Error("Weak password should fail validation")
	}
}

func TestValidateStructFunction(t *testing.T) {
	// 测试 ValidateStruct 函数是否能正常调用
	type TestData struct {
		Name string `validate:"required,min=2"`
		Age  int    `validate:"gte=0"`
	}

	// 测试有效数据
	validData := TestData{Name: "测试", Age: 18}
	err := ValidateStruct(validData)
	// 由于 ValidateStruct 依赖 Gin 的验证器，可能返回错误，这是正常的
	t.Logf("ValidateStruct with valid data returned: %v", err)

	// 测试无效数据
	invalidData := TestData{Name: "a", Age: -1}
	err = ValidateStruct(invalidData)
	t.Logf("ValidateStruct with invalid data returned: %v", err)

	// 只要函数不 panic 就算通过
	t.Log("ValidateStruct function executed without panic")
}

func TestInitEnhancedValidatorFunction(t *testing.T) {
	// 测试初始化函数不会panic
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("InitEnhancedValidator should not panic, got: %v", r)
		}
	}()

	InitEnhancedValidator()
	t.Log("InitEnhancedValidator completed successfully")
}
