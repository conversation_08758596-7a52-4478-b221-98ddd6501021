package validator

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

var (
	phoneRegex = regexp.MustCompile(`^1[3-9]\d{9}$`)
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
)

// InitValidator 初始化验证器
func InitValidator() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册自定义验证器
		v.RegisterValidation("phone", validatePhone)
		v.RegisterValidation("strong_password", validateStrongPassword)

		// 注册标签名函数
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			return name
		})
	}
	// 初始化增强验证器
	InitEnhancedValidator()
}

// validatePhone 验证手机号
func validatePhone(fl validator.FieldLevel) bool {
	phone := fl.Field().String()
	return phoneRegex.MatchString(phone)
}

// 注意：validateStrongPassword 函数已移至 enhanced_validator.go

// TranslateError 翻译验证错误
func TranslateError(err error) map[string]string {
	errors := make(map[string]string)

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, e := range validationErrors {
			field := e.Field()
			tag := e.Tag()

			switch tag {
			case "required":
				errors[field] = fmt.Sprintf("%s是必填字段", field)
			case "email":
				errors[field] = fmt.Sprintf("%s格式不正确", field)
			case "phone":
				errors[field] = fmt.Sprintf("%s格式不正确", field)
			case "strong_password":
				errors[field] = "密码至少8位，包含大小写字母和数字"
			case "min":
				errors[field] = fmt.Sprintf("%s长度不能少于%s位", field, e.Param())
			case "max":
				errors[field] = fmt.Sprintf("%s长度不能超过%s位", field, e.Param())
			default:
				errors[field] = fmt.Sprintf("%s验证失败", field)
			}
		}
	}

	return errors
}
