package cache

import (
	"fmt"
	"time"

	"kids-platform/pkg/config"
)

// CacheConfig 缓存配置
type CacheConfig struct {
	// Redis配置
	RedisEnabled  bool   `mapstructure:"redis_enabled"`
	RedisHost     string `mapstructure:"redis_host"`
	RedisPort     int    `mapstructure:"redis_port"`
	RedisPassword string `mapstructure:"redis_password"`
	RedisDB       int    `mapstructure:"redis_db"`
	RedisPoolSize int    `mapstructure:"redis_pool_size"`

	// 内存缓存配置
	MemoryEnabled    bool          `mapstructure:"memory_enabled"`
	MemoryMaxSize    int           `mapstructure:"memory_max_size"`
	MemoryDefaultTTL time.Duration `mapstructure:"memory_default_ttl"`

	// 分层缓存配置
	LayeredEnabled bool          `mapstructure:"layered_enabled"`
	L1TTL          time.Duration `mapstructure:"l1_ttl"`
	L2TTL          time.Duration `mapstructure:"l2_ttl"`

	// 缓存策略
	Strategy string `mapstructure:"strategy"` // "redis", "memory", "layered"
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		RedisEnabled:     false,
		RedisHost:        "localhost",
		RedisPort:        6379,
		RedisPassword:    "",
		RedisDB:          0,
		RedisPoolSize:    10,
		MemoryEnabled:    true,
		MemoryMaxSize:    1000,
		MemoryDefaultTTL: 30 * time.Minute,
		LayeredEnabled:   false,
		L1TTL:            5 * time.Minute,
		L2TTL:            30 * time.Minute,
		Strategy:         "memory",
	}
}

// CacheFactory 缓存工厂
type CacheFactory struct {
	config *config.Config
}

// NewCacheFactory 创建缓存工厂
func NewCacheFactory(cfg *config.Config) *CacheFactory {
	return &CacheFactory{config: cfg}
}

// CreateCache 创建缓存实例
func (cf *CacheFactory) CreateCache() (Cache, error) {
	// 根据配置决定使用哪种缓存策略
	if cf.config.Redis.Enabled {
		return cf.createRedisCache()
	}

	// 默认使用内存缓存
	return cf.createMemoryCache(), nil
}

// CreateLayeredCache 创建分层缓存
func (cf *CacheFactory) CreateLayeredCache() (CacheStrategy, error) {
	// 创建一级缓存（内存）
	l1Cache := cf.createMemoryCache()

	// 创建二级缓存（Redis）
	l2Cache, err := cf.createRedisCache()
	if err != nil {
		// 如果Redis不可用，使用内存缓存作为二级缓存
		l2Cache = cf.createMemoryCache()
	}

	// 创建分层缓存配置
	layeredConfig := &LayeredCacheConfig{
		L1TTL:        5 * time.Minute,
		L2TTL:        30 * time.Minute,
		L1MaxSize:    1000,
		SyncInterval: 1 * time.Minute,
		EnableL1:     true,
		EnableL2:     true,
	}

	return NewLayeredCacheStrategy(l1Cache, l2Cache, layeredConfig), nil
}

// CreateCacheWithMetrics 创建带指标的缓存
func (cf *CacheFactory) CreateCacheWithMetrics() (Cache, *CacheMetrics, error) {
	baseCache, err := cf.CreateCache()
	if err != nil {
		return nil, nil, err
	}

	wrapper := NewMetricsCacheWrapper(baseCache)
	return wrapper, wrapper.GetMetrics(), nil
}

// createRedisCache 创建Redis缓存
func (cf *CacheFactory) createRedisCache() (Cache, error) {
	if !cf.config.Redis.Enabled {
		return nil, fmt.Errorf("redis is not enabled")
	}

	// 创建Redis客户端
	redisClient, err := NewRedisClient(cf.config.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to create redis client: %w", err)
	}

	// 创建Redis缓存
	return NewRedisCache(redisClient), nil
}

// createMemoryCache 创建内存缓存
func (cf *CacheFactory) createMemoryCache() Cache {
	return NewMemoryCache(1000, 30*time.Minute)
}

// CacheInitializer 缓存初始化器
type CacheInitializer struct {
	factory *CacheFactory
	manager *CacheManager
}

// NewCacheInitializer 创建缓存初始化器
func NewCacheInitializer(cfg *config.Config) *CacheInitializer {
	factory := NewCacheFactory(cfg)
	manager := NewCacheManager()

	return &CacheInitializer{
		factory: factory,
		manager: manager,
	}
}

// Initialize 初始化缓存系统
func (ci *CacheInitializer) Initialize() error {
	// 创建默认缓存
	defaultCache, err := ci.factory.CreateCache()
	if err != nil {
		return fmt.Errorf("failed to create default cache: %w", err)
	}

	// 设置默认策略
	ci.manager.SetDefaultStrategy(&SimpleCacheStrategy{cache: defaultCache})

	// 注册不同的缓存策略
	ci.registerStrategies()

	return nil
}

// registerStrategies 注册缓存策略
func (ci *CacheInitializer) registerStrategies() {
	// 注册内存缓存策略
	memoryCache := ci.factory.createMemoryCache()
	ci.manager.RegisterStrategy("memory", &SimpleCacheStrategy{cache: memoryCache})

	// 注册Redis缓存策略（如果可用）
	if redisCache, err := ci.factory.createRedisCache(); err == nil {
		ci.manager.RegisterStrategy("redis", &SimpleCacheStrategy{cache: redisCache})
	}

	// 注册分层缓存策略
	if layeredCache, err := ci.factory.CreateLayeredCache(); err == nil {
		ci.manager.RegisterStrategy("layered", layeredCache)
	}

	// 注册LRU缓存策略
	lruCache := NewLRUCache(500)
	ci.manager.RegisterStrategy("lru", &SimpleCacheStrategy{cache: lruCache})
}

// GetManager 获取缓存管理器
func (ci *CacheInitializer) GetManager() *CacheManager {
	return ci.manager
}

// SimpleCacheStrategy 简单缓存策略（适配器模式）
type SimpleCacheStrategy struct {
	cache Cache
}

// Get 获取缓存数据
func (scs *SimpleCacheStrategy) Get(key string, dest interface{}) error {
	return scs.cache.Get(key, dest)
}

// Set 设置缓存数据
func (scs *SimpleCacheStrategy) Set(key string, value interface{}, expiration time.Duration) error {
	return scs.cache.Set(key, value, expiration)
}

// Delete 删除缓存数据
func (scs *SimpleCacheStrategy) Delete(key string) error {
	return scs.cache.Delete(key)
}

// DeleteByPattern 按模式删除缓存（简化实现）
func (scs *SimpleCacheStrategy) DeleteByPattern(pattern string) error {
	return fmt.Errorf("pattern deletion not supported")
}

// DeleteByTags 按标签删除缓存（简化实现）
func (scs *SimpleCacheStrategy) DeleteByTags(tags []string) error {
	return fmt.Errorf("tag deletion not supported")
}

// SetWithTags 设置带标签的缓存（简化实现）
func (scs *SimpleCacheStrategy) SetWithTags(key string, value interface{}, expiration time.Duration, tags []string) error {
	return scs.cache.Set(key, value, expiration)
}

// Exists 检查键是否存在
func (scs *SimpleCacheStrategy) Exists(key string) bool {
	return scs.cache.Exists(key)
}

// TTL 获取键的TTL
func (scs *SimpleCacheStrategy) TTL(key string) time.Duration {
	return scs.cache.TTL(key)
}

// Clear 清空所有缓存
func (scs *SimpleCacheStrategy) Clear() error {
	return scs.cache.Clear()
}

// CacheHealthChecker 缓存健康检查器
type CacheHealthChecker struct {
	cache Cache
}

// NewCacheHealthChecker 创建缓存健康检查器
func NewCacheHealthChecker(cache Cache) *CacheHealthChecker {
	return &CacheHealthChecker{cache: cache}
}

// Check 检查缓存健康状态
func (chc *CacheHealthChecker) Check() error {
	// 尝试设置和获取测试数据
	testKey := "health_check_test"
	testValue := "test_value"

	// 设置测试数据
	if err := chc.cache.Set(testKey, testValue, time.Minute); err != nil {
		return fmt.Errorf("cache set failed: %w", err)
	}

	// 获取测试数据
	var result string
	if err := chc.cache.Get(testKey, &result); err != nil {
		return fmt.Errorf("cache get failed: %w", err)
	}

	// 验证数据
	if result != testValue {
		return fmt.Errorf("cache data mismatch: expected %s, got %s", testValue, result)
	}

	// 清理测试数据
	chc.cache.Delete(testKey)

	return nil
}

// GetStatus 获取缓存状态信息
func (chc *CacheHealthChecker) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"healthy": true,
		"type":    "unknown",
	}

	// 尝试健康检查
	if err := chc.Check(); err != nil {
		status["healthy"] = false
		status["error"] = err.Error()
	}

	// 获取缓存类型信息
	switch chc.cache.(type) {
	case *MemoryCache:
		status["type"] = "memory"
		if mc, ok := chc.cache.(*MemoryCache); ok {
			status["stats"] = mc.Stats()
		}
	case *RedisCache:
		status["type"] = "redis"
	case *LRUCache:
		status["type"] = "lru"
	case *MetricsCacheWrapper:
		status["type"] = "metrics_wrapper"
		if mcw, ok := chc.cache.(*MetricsCacheWrapper); ok {
			status["metrics"] = mcw.GetMetrics()
		}
	}

	return status
}
