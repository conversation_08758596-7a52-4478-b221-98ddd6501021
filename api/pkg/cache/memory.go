package cache

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// MemoryCache 内存缓存实现
type MemoryCache struct {
	data    map[string]*cacheItem
	mutex   sync.RWMutex
	maxSize int
	ttl     time.Duration
}

// cacheItem 缓存项
type cacheItem struct {
	value      interface{}
	expiration time.Time
	lastAccess time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(maxSize int, defaultTTL time.Duration) *MemoryCache {
	mc := &MemoryCache{
		data:    make(map[string]*cacheItem),
		maxSize: maxSize,
		ttl:     defaultTTL,
	}

	// 启动清理协程
	go mc.cleanup()

	return mc
}

// Get 获取缓存数据
func (mc *MemoryCache) Get(key string, dest interface{}) error {
	mc.mutex.RLock()
	item, exists := mc.data[key]
	mc.mutex.RUnlock()

	if !exists {
		return ErrCacheMiss
	}

	// 检查是否过期
	if time.Now().After(item.expiration) {
		mc.Delete(key)
		return ErrCacheMiss
	}

	// 更新最后访问时间
	mc.mutex.Lock()
	item.lastAccess = time.Now()
	mc.mutex.Unlock()

	// 反序列化数据
	return mc.deserialize(item.value, dest)
}

// Set 设置缓存数据
func (mc *MemoryCache) Set(key string, value interface{}, expiration time.Duration) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// 检查缓存大小限制
	if len(mc.data) >= mc.maxSize {
		mc.evictLRU()
	}

	// 序列化数据
	serialized, err := mc.serialize(value)
	if err != nil {
		return err
	}

	// 设置过期时间
	if expiration <= 0 {
		expiration = mc.ttl
	}

	mc.data[key] = &cacheItem{
		value:      serialized,
		expiration: time.Now().Add(expiration),
		lastAccess: time.Now(),
	}

	return nil
}

// Delete 删除缓存数据
func (mc *MemoryCache) Delete(key string) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	delete(mc.data, key)
	return nil
}

// Exists 检查键是否存在
func (mc *MemoryCache) Exists(key string) bool {
	mc.mutex.RLock()
	item, exists := mc.data[key]
	mc.mutex.RUnlock()

	if !exists {
		return false
	}

	// 检查是否过期
	if time.Now().After(item.expiration) {
		mc.Delete(key)
		return false
	}

	return true
}

// TTL 获取键的TTL
func (mc *MemoryCache) TTL(key string) time.Duration {
	mc.mutex.RLock()
	item, exists := mc.data[key]
	mc.mutex.RUnlock()

	if !exists {
		return -1
	}

	remaining := time.Until(item.expiration)
	if remaining < 0 {
		mc.Delete(key)
		return -1
	}

	return remaining
}

// Clear 清空所有缓存
func (mc *MemoryCache) Clear() error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.data = make(map[string]*cacheItem)
	return nil
}

// Size 获取缓存大小
func (mc *MemoryCache) Size() int {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	return len(mc.data)
}

// Keys 获取所有键
func (mc *MemoryCache) Keys() []string {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	keys := make([]string, 0, len(mc.data))
	for key := range mc.data {
		keys = append(keys, key)
	}

	return keys
}

// Stats 获取缓存统计信息
func (mc *MemoryCache) Stats() *MemoryCacheStats {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	stats := &MemoryCacheStats{
		Size:    len(mc.data),
		MaxSize: mc.maxSize,
	}

	now := time.Now()
	for _, item := range mc.data {
		if now.After(item.expiration) {
			stats.ExpiredItems++
		}
	}

	return stats
}

// MemoryCacheStats 内存缓存统计信息
type MemoryCacheStats struct {
	Size         int `json:"size"`
	MaxSize      int `json:"max_size"`
	ExpiredItems int `json:"expired_items"`
}

// serialize 序列化数据
func (mc *MemoryCache) serialize(value interface{}) (interface{}, error) {
	// 对于简单类型，直接存储
	switch value.(type) {
	case string, int, int32, int64, float32, float64, bool:
		return value, nil
	default:
		// 对于复杂类型，使用JSON序列化
		data, err := json.Marshal(value)
		if err != nil {
			return nil, err
		}
		return data, nil
	}
}

// deserialize 反序列化数据
func (mc *MemoryCache) deserialize(stored interface{}, dest interface{}) error {
	switch data := stored.(type) {
	case []byte:
		// JSON反序列化
		return json.Unmarshal(data, dest)
	default:
		// 简单类型直接赋值
		switch d := dest.(type) {
		case *string:
			if v, ok := stored.(string); ok {
				*d = v
				return nil
			}
		case *int:
			if v, ok := stored.(int); ok {
				*d = v
				return nil
			}
		case *int64:
			if v, ok := stored.(int64); ok {
				*d = v
				return nil
			}
		case *float64:
			if v, ok := stored.(float64); ok {
				*d = v
				return nil
			}
		case *bool:
			if v, ok := stored.(bool); ok {
				*d = v
				return nil
			}
		}

		// 尝试JSON反序列化
		data, err := json.Marshal(stored)
		if err != nil {
			return err
		}
		if dataBytes, ok := data.([]byte); ok {
			return json.Unmarshal(dataBytes, dest)
		}
		return fmt.Errorf("invalid data type for unmarshaling")
	}
}

// evictLRU 使用LRU策略淘汰缓存
func (mc *MemoryCache) evictLRU() {
	if len(mc.data) == 0 {
		return
	}

	var oldestKey string
	var oldestTime time.Time

	// 找到最久未访问的键
	for key, item := range mc.data {
		if oldestKey == "" || item.lastAccess.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.lastAccess
		}
	}

	// 删除最久未访问的键
	if oldestKey != "" {
		delete(mc.data, oldestKey)
	}
}

// cleanup 清理过期缓存
func (mc *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		mc.cleanupExpired()
	}
}

// cleanupExpired 清理过期的缓存项
func (mc *MemoryCache) cleanupExpired() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	for key, item := range mc.data {
		if now.After(item.expiration) {
			delete(mc.data, key)
		}
	}
}

// LRUCache LRU缓存实现
type LRUCache struct {
	capacity int
	cache    map[string]*LRUNode
	head     *LRUNode
	tail     *LRUNode
	mutex    sync.RWMutex
}

// LRUNode LRU节点
type LRUNode struct {
	key        string
	value      interface{}
	expiration time.Time
	prev       *LRUNode
	next       *LRUNode
}

// NewLRUCache 创建LRU缓存
func NewLRUCache(capacity int) *LRUCache {
	lru := &LRUCache{
		capacity: capacity,
		cache:    make(map[string]*LRUNode),
	}

	// 创建哨兵节点
	lru.head = &LRUNode{}
	lru.tail = &LRUNode{}
	lru.head.next = lru.tail
	lru.tail.prev = lru.head

	return lru
}

// Get 获取缓存数据
func (lru *LRUCache) Get(key string, dest interface{}) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	node, exists := lru.cache[key]
	if !exists {
		return ErrCacheMiss
	}

	// 检查是否过期
	if time.Now().After(node.expiration) {
		lru.removeNode(node)
		delete(lru.cache, key)
		return ErrCacheMiss
	}

	// 移动到头部
	lru.moveToHead(node)

	// 反序列化数据
	data, err := json.Marshal(node.value)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, dest)
}

// Set 设置缓存数据
func (lru *LRUCache) Set(key string, value interface{}, expiration time.Duration) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	if node, exists := lru.cache[key]; exists {
		// 更新现有节点
		node.value = value
		node.expiration = time.Now().Add(expiration)
		lru.moveToHead(node)
	} else {
		// 创建新节点
		newNode := &LRUNode{
			key:        key,
			value:      value,
			expiration: time.Now().Add(expiration),
		}

		lru.cache[key] = newNode
		lru.addToHead(newNode)

		// 检查容量限制
		if len(lru.cache) > lru.capacity {
			tail := lru.removeTail()
			delete(lru.cache, tail.key)
		}
	}

	return nil
}

// Delete 删除缓存数据
func (lru *LRUCache) Delete(key string) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	if node, exists := lru.cache[key]; exists {
		lru.removeNode(node)
		delete(lru.cache, key)
	}

	return nil
}

// Exists 检查键是否存在
func (lru *LRUCache) Exists(key string) bool {
	lru.mutex.RLock()
	defer lru.mutex.RUnlock()

	node, exists := lru.cache[key]
	if !exists {
		return false
	}

	// 检查是否过期
	if time.Now().After(node.expiration) {
		return false
	}

	return true
}

// TTL 获取键的TTL
func (lru *LRUCache) TTL(key string) time.Duration {
	lru.mutex.RLock()
	defer lru.mutex.RUnlock()

	node, exists := lru.cache[key]
	if !exists {
		return -1
	}

	remaining := time.Until(node.expiration)
	if remaining < 0 {
		return -1
	}

	return remaining
}

// Clear 清空所有缓存
func (lru *LRUCache) Clear() error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	lru.cache = make(map[string]*LRUNode)
	lru.head.next = lru.tail
	lru.tail.prev = lru.head

	return nil
}

// 双向链表操作方法
func (lru *LRUCache) addToHead(node *LRUNode) {
	node.prev = lru.head
	node.next = lru.head.next
	lru.head.next.prev = node
	lru.head.next = node
}

func (lru *LRUCache) removeNode(node *LRUNode) {
	node.prev.next = node.next
	node.next.prev = node.prev
}

func (lru *LRUCache) moveToHead(node *LRUNode) {
	lru.removeNode(node)
	lru.addToHead(node)
}

func (lru *LRUCache) removeTail() *LRUNode {
	lastNode := lru.tail.prev
	lru.removeNode(lastNode)
	return lastNode
}
