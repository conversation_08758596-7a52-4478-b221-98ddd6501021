package cache

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
)

// CacheStrategy 缓存策略接口
type CacheStrategy interface {
	Get(key string, dest interface{}) error
	Set(key string, value interface{}, expiration time.Duration) error
	Delete(key string) error
	DeleteByPattern(pattern string) error
	DeleteByTags(tags []string) error
	SetWithTags(key string, value interface{}, expiration time.Duration, tags []string) error
	Exists(key string) bool
	TTL(key string) time.Duration
	Clear() error
}

// LayeredCacheStrategy 分层缓存策略
type LayeredCacheStrategy struct {
	l1Cache Cache // 一级缓存（内存）
	l2Cache Cache // 二级缓存（Redis）
	config  *LayeredCacheConfig
}

// LayeredCacheConfig 分层缓存配置
type LayeredCacheConfig struct {
	L1TTL        time.Duration // 一级缓存TTL
	L2TTL        time.Duration // 二级缓存TTL
	L1MaxSize    int           // 一级缓存最大条目数
	SyncInterval time.Duration // 同步间隔
	EnableL1     bool          // 是否启用一级缓存
	EnableL2     bool          // 是否启用二级缓存
}

// NewLayeredCacheStrategy 创建分层缓存策略
func NewLayeredCacheStrategy(l1Cache, l2Cache Cache, config *LayeredCacheConfig) *LayeredCacheStrategy {
	if config == nil {
		config = &LayeredCacheConfig{
			L1TTL:        5 * time.Minute,
			L2TTL:        30 * time.Minute,
			L1MaxSize:    1000,
			SyncInterval: 1 * time.Minute,
			EnableL1:     true,
			EnableL2:     true,
		}
	}

	return &LayeredCacheStrategy{
		l1Cache: l1Cache,
		l2Cache: l2Cache,
		config:  config,
	}
}

// Get 获取缓存数据
func (lcs *LayeredCacheStrategy) Get(key string, dest interface{}) error {
	// 先尝试从一级缓存获取
	if lcs.config.EnableL1 {
		if err := lcs.l1Cache.Get(key, dest); err == nil {
			return nil
		}
	}

	// 一级缓存未命中，尝试二级缓存
	if lcs.config.EnableL2 {
		if err := lcs.l2Cache.Get(key, dest); err == nil {
			// 回写到一级缓存
			if lcs.config.EnableL1 {
				lcs.l1Cache.Set(key, dest, lcs.config.L1TTL)
			}
			return nil
		}
	}

	return fmt.Errorf("cache miss")
}

// Set 设置缓存数据
func (lcs *LayeredCacheStrategy) Set(key string, value interface{}, expiration time.Duration) error {
	var err error

	// 设置二级缓存
	if lcs.config.EnableL2 {
		if err = lcs.l2Cache.Set(key, value, lcs.config.L2TTL); err != nil {
			return err
		}
	}

	// 设置一级缓存
	if lcs.config.EnableL1 {
		if err = lcs.l1Cache.Set(key, value, lcs.config.L1TTL); err != nil {
			return err
		}
	}

	return nil
}

// Delete 删除缓存数据
func (lcs *LayeredCacheStrategy) Delete(key string) error {
	var err error

	// 删除一级缓存
	if lcs.config.EnableL1 {
		lcs.l1Cache.Delete(key)
	}

	// 删除二级缓存
	if lcs.config.EnableL2 {
		err = lcs.l2Cache.Delete(key)
	}

	return err
}

// DeleteByPattern 按模式删除缓存
func (lcs *LayeredCacheStrategy) DeleteByPattern(pattern string) error {
	// 这里需要实现模式匹配删除逻辑
	// 简化实现，实际应该遍历所有键进行匹配
	return fmt.Errorf("pattern deletion not implemented")
}

// DeleteByTags 按标签删除缓存
func (lcs *LayeredCacheStrategy) DeleteByTags(tags []string) error {
	// 实现标签删除逻辑
	return fmt.Errorf("tag deletion not implemented")
}

// SetWithTags 设置带标签的缓存
func (lcs *LayeredCacheStrategy) SetWithTags(key string, value interface{}, expiration time.Duration, tags []string) error {
	// 先设置普通缓存
	if err := lcs.Set(key, value, expiration); err != nil {
		return err
	}

	// 设置标签映射
	for _, tag := range tags {
		_ = fmt.Sprintf("tag:%s", tag)
		// 这里应该维护标签到键的映射关系
		// 简化实现，暂时忽略标签功能
	}

	return nil
}

// Exists 检查键是否存在
func (lcs *LayeredCacheStrategy) Exists(key string) bool {
	if lcs.config.EnableL1 {
		if lcs.l1Cache.Exists(key) {
			return true
		}
	}

	if lcs.config.EnableL2 {
		return lcs.l2Cache.Exists(key)
	}

	return false
}

// TTL 获取键的TTL
func (lcs *LayeredCacheStrategy) TTL(key string) time.Duration {
	if lcs.config.EnableL2 {
		return lcs.l2Cache.TTL(key)
	}
	return 0
}

// Clear 清空所有缓存
func (lcs *LayeredCacheStrategy) Clear() error {
	if lcs.config.EnableL1 {
		lcs.l1Cache.Clear()
	}

	if lcs.config.EnableL2 {
		return lcs.l2Cache.Clear()
	}

	return nil
}

// TaggedCacheStrategy 带标签的缓存策略
type TaggedCacheStrategy struct {
	cache  Cache
	client *redis.Client
}

// NewTaggedCacheStrategy 创建带标签的缓存策略
func NewTaggedCacheStrategy(cache Cache, client *redis.Client) *TaggedCacheStrategy {
	return &TaggedCacheStrategy{
		cache:  cache,
		client: client,
	}
}

// SetWithTags 设置带标签的缓存
func (tcs *TaggedCacheStrategy) SetWithTags(key string, value interface{}, expiration time.Duration, tags []string) error {
	// 设置主缓存
	if err := tcs.cache.Set(key, value, expiration); err != nil {
		return err
	}

	// 设置标签映射
	ctx := context.Background()
	for _, tag := range tags {
		tagKey := fmt.Sprintf("tag:%s", tag)
		// 将键添加到标签集合中
		tcs.client.SAdd(ctx, tagKey, key)
		// 设置标签的过期时间
		tcs.client.Expire(ctx, tagKey, expiration+time.Hour) // 标签比数据多保存1小时
	}

	return nil
}

// DeleteByTags 按标签删除缓存
func (tcs *TaggedCacheStrategy) DeleteByTags(tags []string) error {
	ctx := context.Background()

	for _, tag := range tags {
		tagKey := fmt.Sprintf("tag:%s", tag)

		// 获取标签下的所有键
		keys, err := tcs.client.SMembers(ctx, tagKey).Result()
		if err != nil {
			continue
		}

		// 删除所有相关的缓存键
		for _, key := range keys {
			tcs.cache.Delete(key)
		}

		// 删除标签本身
		tcs.client.Del(ctx, tagKey)
	}

	return nil
}

// CacheManager 缓存管理器
type CacheManager struct {
	strategies map[string]CacheStrategy
	default_   CacheStrategy
}

// NewCacheManager 创建缓存管理器
func NewCacheManager() *CacheManager {
	return &CacheManager{
		strategies: make(map[string]CacheStrategy),
	}
}

// RegisterStrategy 注册缓存策略
func (cm *CacheManager) RegisterStrategy(name string, strategy CacheStrategy) {
	cm.strategies[name] = strategy
}

// SetDefaultStrategy 设置默认策略
func (cm *CacheManager) SetDefaultStrategy(strategy CacheStrategy) {
	cm.default_ = strategy
}

// GetStrategy 获取缓存策略
func (cm *CacheManager) GetStrategy(name string) CacheStrategy {
	if strategy, exists := cm.strategies[name]; exists {
		return strategy
	}
	return cm.default_
}

// CacheKeyBuilder 缓存键构建器
type CacheKeyBuilder struct {
	prefix    string
	separator string
}

// NewCacheKeyBuilder 创建缓存键构建器
func NewCacheKeyBuilder(prefix string) *CacheKeyBuilder {
	return &CacheKeyBuilder{
		prefix:    prefix,
		separator: ":",
	}
}

// Build 构建缓存键
func (ckb *CacheKeyBuilder) Build(parts ...string) string {
	allParts := []string{ckb.prefix}
	allParts = append(allParts, parts...)
	return strings.Join(allParts, ckb.separator)
}

// BuildWithTags 构建带标签的缓存键
func (ckb *CacheKeyBuilder) BuildWithTags(tags []string, parts ...string) (string, []string) {
	key := ckb.Build(parts...)
	return key, tags
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	Hits        int64     `json:"hits"`
	Misses      int64     `json:"misses"`
	Sets        int64     `json:"sets"`
	Deletes     int64     `json:"deletes"`
	HitRate     float64   `json:"hit_rate"`
	LastUpdated time.Time `json:"last_updated"`
}

// MetricsCacheWrapper 带指标的缓存包装器
type MetricsCacheWrapper struct {
	cache   Cache
	metrics *CacheMetrics
}

// NewMetricsCacheWrapper 创建带指标的缓存包装器
func NewMetricsCacheWrapper(cache Cache) *MetricsCacheWrapper {
	return &MetricsCacheWrapper{
		cache: cache,
		metrics: &CacheMetrics{
			LastUpdated: time.Now(),
		},
	}
}

// Get 获取缓存数据并记录指标
func (mcw *MetricsCacheWrapper) Get(key string, dest interface{}) error {
	err := mcw.cache.Get(key, dest)
	if err == nil {
		mcw.metrics.Hits++
	} else {
		mcw.metrics.Misses++
	}
	mcw.updateHitRate()
	return err
}

// Set 设置缓存数据并记录指标
func (mcw *MetricsCacheWrapper) Set(key string, value interface{}, expiration time.Duration) error {
	err := mcw.cache.Set(key, value, expiration)
	if err == nil {
		mcw.metrics.Sets++
	}
	return err
}

// Delete 删除缓存数据并记录指标
func (mcw *MetricsCacheWrapper) Delete(key string) error {
	err := mcw.cache.Delete(key)
	if err == nil {
		mcw.metrics.Deletes++
	}
	return err
}

// GetMetrics 获取缓存指标
func (mcw *MetricsCacheWrapper) GetMetrics() *CacheMetrics {
	mcw.updateHitRate()
	return mcw.metrics
}

// updateHitRate 更新命中率
func (mcw *MetricsCacheWrapper) updateHitRate() {
	total := mcw.metrics.Hits + mcw.metrics.Misses
	if total > 0 {
		mcw.metrics.HitRate = float64(mcw.metrics.Hits) / float64(total)
	}
	mcw.metrics.LastUpdated = time.Now()
}

// 实现Cache接口的其他方法
func (mcw *MetricsCacheWrapper) Exists(key string) bool {
	return mcw.cache.Exists(key)
}

func (mcw *MetricsCacheWrapper) TTL(key string) time.Duration {
	return mcw.cache.TTL(key)
}

func (mcw *MetricsCacheWrapper) Clear() error {
	return mcw.cache.Clear()
}

// CacheWarmup 缓存预热器
type CacheWarmup struct {
	cache Cache
}

// NewCacheWarmup 创建缓存预热器
func NewCacheWarmup(cache Cache) *CacheWarmup {
	return &CacheWarmup{cache: cache}
}

// WarmupFunc 预热函数类型
type WarmupFunc func() (key string, value interface{}, expiration time.Duration, err error)

// Warmup 执行缓存预热
func (cw *CacheWarmup) Warmup(funcs []WarmupFunc) error {
	for _, fn := range funcs {
		key, value, expiration, err := fn()
		if err != nil {
			continue // 忽略错误，继续预热其他数据
		}
		cw.cache.Set(key, value, expiration)
	}
	return nil
}

// BatchWarmup 批量预热
func (cw *CacheWarmup) BatchWarmup(data map[string]interface{}, expiration time.Duration) error {
	for key, value := range data {
		cw.cache.Set(key, value, expiration)
	}
	return nil
}
