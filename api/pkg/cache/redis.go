package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"kids-platform/pkg/config"

	"github.com/redis/go-redis/v9"
)

var (
	ErrCacheMiss = errors.New("cache miss")
)

// Cache 缓存接口
type Cache interface {
	Get(key string, dest interface{}) error
	Set(key string, value interface{}, expiration time.Duration) error
	Delete(key string) error
	Exists(key string) bool
	TTL(key string) time.Duration
	Clear() error
}

// RedisClient Redis客户端接口
type RedisClient interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	GetObject(ctx context.Context, key string, dest interface{}) error
	SetObject(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Delete(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, keys ...string) (int64, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	Close() error
}

// redisClient Redis客户端实现
type redisClient struct {
	client *redis.Client
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg config.RedisConfig) (RedisClient, error) {
	if !cfg.Enabled {
		return &mockRedisClient{}, nil
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.Database,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %w", err)
	}

	return &redisClient{client: rdb}, nil
}

// Set 设置键值
func (r *redisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值
func (r *redisClient) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

// GetObject 获取对象
func (r *redisClient) GetObject(ctx context.Context, key string, dest interface{}) error {
	val, err := r.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), dest)
}

// SetObject 设置对象
func (r *redisClient) SetObject(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.client.Set(ctx, key, data, expiration).Err()
}

// Delete 删除键
func (r *redisClient) Delete(ctx context.Context, keys ...string) error {
	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func (r *redisClient) Exists(ctx context.Context, keys ...string) (int64, error) {
	return r.client.Exists(ctx, keys...).Result()
}

// Expire 设置过期时间
func (r *redisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Expire(ctx, key, expiration).Err()
}

// Close 关闭连接
func (r *redisClient) Close() error {
	return r.client.Close()
}

// mockRedisClient 模拟Redis客户端（当Redis未启用时）
type mockRedisClient struct{}

func (m *mockRedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return nil
}

func (m *mockRedisClient) Get(ctx context.Context, key string) (string, error) {
	return "", redis.Nil
}

func (m *mockRedisClient) GetObject(ctx context.Context, key string, dest interface{}) error {
	return redis.Nil
}

func (m *mockRedisClient) SetObject(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return nil
}

func (m *mockRedisClient) Delete(ctx context.Context, keys ...string) error {
	return nil
}

func (m *mockRedisClient) Exists(ctx context.Context, keys ...string) (int64, error) {
	return 0, nil
}

func (m *mockRedisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return nil
}

func (m *mockRedisClient) Close() error {
	return nil
}

// NewMockRedisClient 创建模拟Redis客户端
func NewMockRedisClient() RedisClient {
	return &mockRedisClient{}
}

// RedisCache Redis缓存实现Cache接口
type RedisCache struct {
	client RedisClient
	ctx    context.Context
}

// NewRedisCache 创建Redis缓存
func NewRedisCache(client RedisClient) *RedisCache {
	return &RedisCache{
		client: client,
		ctx:    context.Background(),
	}
}

// Get 获取缓存数据
func (rc *RedisCache) Get(key string, dest interface{}) error {
	return rc.client.GetObject(rc.ctx, key, dest)
}

// Set 设置缓存数据
func (rc *RedisCache) Set(key string, value interface{}, expiration time.Duration) error {
	return rc.client.SetObject(rc.ctx, key, value, expiration)
}

// Delete 删除缓存数据
func (rc *RedisCache) Delete(key string) error {
	return rc.client.Delete(rc.ctx, key)
}

// Exists 检查键是否存在
func (rc *RedisCache) Exists(key string) bool {
	count, err := rc.client.Exists(rc.ctx, key)
	return err == nil && count > 0
}

// TTL 获取键的TTL
func (rc *RedisCache) TTL(key string) time.Duration {
	// 这里需要扩展RedisClient接口来支持TTL
	// 简化实现，返回0
	return 0
}

// Clear 清空所有缓存
func (rc *RedisCache) Clear() error {
	// 这里需要扩展RedisClient接口来支持FLUSHDB
	// 简化实现，返回nil
	return nil
}
