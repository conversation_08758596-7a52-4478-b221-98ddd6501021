package jwt

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明
type Claims struct {
	UserID   uint64 `json:"user_id"` // 改为uint64与数据库一致
	Username string `json:"username"`
	Role     string `json:"role"`
	UserType uint8  `json:"user_type"` // 添加用户类型
	jwt.RegisteredClaims
}

// TokenType token类型
type TokenType string

const (
	AccessToken  TokenType = "access"
	RefreshToken TokenType = "refresh"
)

// TokenPair token对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey    string
	expireHours  int
	refreshHours int
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey string, expireHours int) *JWTManager {
	return &JWTManager{
		secretKey:    secretKey,
		expireHours:  expireHours,
		refreshHours: expireHours * 24 * 7, // refresh token 7天
	}
}

// GenerateToken 生成访问token
func (j *JWTManager) GenerateToken(userID uint64, username, role string, userType uint8) (string, error) {
	claims := Claims{
		UserID:   userID,
		Username: username,
		Role:     role,
		UserType: userType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * time.Duration(j.expireHours))),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "kids-platform",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// GenerateRefreshToken 生成刷新token
func (j *JWTManager) GenerateRefreshToken(userID uint64, username string, userType uint8) (string, error) {
	claims := Claims{
		UserID:   userID,
		Username: username,
		Role:     "refresh",
		UserType: userType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * time.Duration(j.refreshHours))),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "kids-platform",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secretKey))
}

// ParseToken 解析token
func (j *JWTManager) ParseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(j.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// RefreshToken 刷新token
func (j *JWTManager) RefreshToken(refreshToken string) (string, string, error) {
	claims, err := j.ParseToken(refreshToken)
	if err != nil {
		return "", "", err
	}

	// 检查是否为refresh token
	if claims.Role != "refresh" {
		return "", "", errors.New("invalid refresh token")
	}

	// 生成新的访问token和刷新token
	accessToken, err := j.GenerateToken(claims.UserID, claims.Username, "user", claims.UserType)
	if err != nil {
		return "", "", err
	}

	newRefreshToken, err := j.GenerateRefreshToken(claims.UserID, claims.Username, claims.UserType)
	if err != nil {
		return "", "", err
	}

	return accessToken, newRefreshToken, nil
}

// GenerateTokenPair 生成token对
func (j *JWTManager) GenerateTokenPair(userID uint64, username string, userType uint8) (*TokenPair, error) {
	// 生成访问token
	accessToken, err := j.GenerateToken(userID, username, "user", userType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 生成刷新token
	refreshToken, err := j.GenerateRefreshToken(userID, username, userType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(j.expireHours * 3600), // 转换为秒
		TokenType:    "Bearer",
	}, nil
}

// ValidateToken 验证token有效性
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	// 检查token是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("token expired")
	}

	// 检查是否为访问token
	if claims.Role != "user" && claims.Role != "admin" {
		return nil, errors.New("invalid token type")
	}

	return claims, nil
}

// IsTokenExpired 检查token是否过期
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return true
	}

	if claims.ExpiresAt == nil {
		return false
	}

	return claims.ExpiresAt.Time.Before(time.Now())
}

// GetExpireHours 获取访问token过期时间（小时）
func (j *JWTManager) GetExpireHours() int {
	return j.expireHours
}

// GetRefreshExpireHours 获取刷新token过期时间（小时）
func (j *JWTManager) GetRefreshExpireHours() int {
	return j.refreshHours
}
