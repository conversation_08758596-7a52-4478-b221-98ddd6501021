package version

import (
	"runtime"
	"time"
)

var (
	// 这些变量在构建时通过ldflags设置
	Version   = "dev"
	GitCommit = "unknown"
	BuildTime = "unknown"
	GoVersion = runtime.Version()
)

// Info 版本信息
type Info struct {
	Version   string `json:"version"`
	GitCommit string `json:"git_commit"`
	BuildTime string `json:"build_time"`
	GoVersion string `json:"go_version"`
	Platform  string `json:"platform"`
}

// Get 获取版本信息
func Get() Info {
	return Info{
		Version:   Version,
		GitCommit: GitCommit,
		BuildTime: BuildTime,
		GoVersion: GoVersion,
		Platform:  runtime.GOOS + "/" + runtime.GOARCH,
	}
}

// GetBuildTime 获取构建时间
func GetBuildTime() (time.Time, error) {
	if BuildTime == "unknown" {
		return time.Time{}, nil
	}
	return time.Parse(time.RFC3339, BuildTime)
}
