package wechat

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"kids-platform/pkg/config"
	"kids-platform/pkg/logger"
)

// MiniprogramService 微信小程序服务
type MiniprogramService struct {
	config *config.Config
	client *http.Client
}

// NewMiniprogramService 创建微信小程序服务
func NewMiniprogramService(cfg *config.Config) *MiniprogramService {
	return &MiniprogramService{
		config: cfg,
		client: &http.Client{
			Timeout: time.Duration(cfg.Wechat.Miniprogram.Timeout) * time.Millisecond,
		},
	}
}

// Code2SessionResponse 微信code2session接口响应
type Code2SessionResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
}

// UserInfo 微信用户信息
type UserInfo struct {
	OpenID    string `json:"openId"`
	NickName  string `json:"nickName"`
	Gender    int    `json:"gender"`
	City      string `json:"city"`
	Province  string `json:"province"`
	Country   string `json:"country"`
	AvatarURL string `json:"avatarUrl"`
	UnionID   string `json:"unionId"`
}

// Code2Session 通过code获取session_key和openid
func (s *MiniprogramService) Code2Session(code string) (*Code2SessionResponse, error) {
	// 开发环境Mock模式
	if s.config.Wechat.Miniprogram.MockEnabled {
		logger.Info("Using WeChat mock mode for development")
		return &Code2SessionResponse{
			OpenID:     s.config.Wechat.Miniprogram.MockOpenID,
			SessionKey: s.config.Wechat.Miniprogram.MockSessionKey,
			UnionID:    s.config.Wechat.Miniprogram.MockUnionID,
			ErrCode:    0,
			ErrMsg:     "",
		}, nil
	}

	// 构建请求URL
	params := url.Values{}
	params.Add("appid", s.config.Wechat.Miniprogram.AppID)
	params.Add("secret", s.config.Wechat.Miniprogram.AppSecret)
	params.Add("js_code", code)
	params.Add("grant_type", "authorization_code")

	requestURL := s.config.Wechat.Miniprogram.AuthURL + "?" + params.Encode()

	// 发送请求
	resp, err := s.client.Get(requestURL)
	if err != nil {
		logger.Error("Failed to call WeChat API", "error", err)
		return nil, fmt.Errorf("failed to call WeChat API: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read WeChat API response", "error", err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 解析响应
	var result Code2SessionResponse
	if err := json.Unmarshal(body, &result); err != nil {
		logger.Error("Failed to parse WeChat API response", "error", err, "body", string(body))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 检查错误
	if result.ErrCode != 0 {
		logger.Error("WeChat API returned error", "errcode", result.ErrCode, "errmsg", result.ErrMsg)
		return nil, fmt.Errorf("WeChat API error: %d - %s", result.ErrCode, result.ErrMsg)
	}

	return &result, nil
}

// DecryptUserInfo 解密用户信息
func (s *MiniprogramService) DecryptUserInfo(encryptedData, iv, sessionKey string) (*UserInfo, error) {
	// 开发环境Mock模式
	if s.config.Wechat.Miniprogram.MockEnabled {
		logger.Info("Using WeChat mock mode for user info decryption")
		return &UserInfo{
			OpenID:    s.config.Wechat.Miniprogram.MockOpenID,
			NickName:  "Mock用户",
			Gender:    1,
			City:      "深圳",
			Province:  "广东",
			Country:   "中国",
			AvatarURL: "https://thirdwx.qlogo.cn/mmopen/mock_avatar.png",
			UnionID:   s.config.Wechat.Miniprogram.MockUnionID,
		}, nil
	}

	// Base64解码
	aesKey, err := base64.StdEncoding.DecodeString(sessionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode session key: %w", err)
	}

	cipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode encrypted data: %w", err)
	}

	ivBytes, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, fmt.Errorf("failed to decode iv: %w", err)
	}

	// AES解密
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	mode := cipher.NewCBCDecrypter(block, ivBytes)
	mode.CryptBlocks(cipherText, cipherText)

	// 去除PKCS7填充
	cipherText = pkcs7UnPadding(cipherText)

	// 解析JSON
	var userInfo UserInfo
	if err := json.Unmarshal(cipherText, &userInfo); err != nil {
		return nil, fmt.Errorf("failed to parse user info: %w", err)
	}

	return &userInfo, nil
}

// pkcs7UnPadding 去除PKCS7填充
func pkcs7UnPadding(data []byte) []byte {
	length := len(data)
	if length == 0 {
		return data
	}
	unPadding := int(data[length-1])
	if unPadding > length {
		return data
	}
	return data[:(length - unPadding)]
}
