package errcode

// 系统级错误码定义 (10000-19999)
// 这些错误码用于系统级别的通用错误，可以被任何包使用

var (
	// 基础错误 (10000-10099)
	Success               = NewError(0, "操作成功")
	ErrInternalServer     = NewError(10000, "服务器内部错误")
	ErrInvalidParam       = NewError(10001, "参数错误")
	ErrUnauthorized       = NewError(10002, "未授权")
	ErrForbidden          = NewError(10003, "禁止访问")
	ErrNotFound           = NewError(10004, "资源不存在")
	ErrMethodNotAllowed   = NewError(10005, "请求方法不允许")
	ErrTooManyRequests    = NewError(10006, "请求过于频繁")
	ErrServiceUnavailable = NewError(10007, "服务不可用")
	ErrTimeout            = NewError(10008, "请求超时")
	ErrDuplicateEntry     = NewError(10009, "数据重复")

	// 数据库相关错误 (10100-10199)
	ErrDatabase           = NewError(10100, "数据库错误")
	ErrDatabaseConnection = NewError(10101, "数据库连接失败")
	ErrDatabaseQuery      = NewError(10102, "数据库查询失败")
	ErrDatabaseTransaction = NewError(10103, "数据库事务失败")
	ErrDataNotFound       = NewError(10104, "数据不存在")
	ErrDataExists         = NewError(10105, "数据已存在")

	// 缓存相关错误 (10200-10299)
	ErrCache              = NewError(10200, "缓存错误")
	ErrCacheConnection    = NewError(10201, "缓存连接失败")
	ErrCacheKeyNotFound   = NewError(10202, "缓存键不存在")
	ErrCacheExpired       = NewError(10203, "缓存已过期")

	// 网络相关错误 (10300-10399)
	ErrNetwork            = NewError(10300, "网络错误")
	ErrNetworkTimeout     = NewError(10301, "网络超时")
	ErrNetworkConnection  = NewError(10302, "网络连接失败")
	ErrHTTPRequest        = NewError(10303, "HTTP请求失败")

	// 文件相关错误 (10400-10499)
	ErrFile               = NewError(10400, "文件操作错误")
	ErrFileNotFound       = NewError(10401, "文件不存在")
	ErrFilePermission     = NewError(10402, "文件权限不足")
	ErrFileSize           = NewError(10403, "文件大小超限")
	ErrFileFormat         = NewError(10404, "文件格式错误")

	// 验证相关错误 (10500-10599)
	ErrValidation         = NewError(10500, "数据验证失败")
	ErrValidationRequired = NewError(10501, "必填字段缺失")
	ErrValidationFormat   = NewError(10502, "字段格式错误")
	ErrValidationLength   = NewError(10503, "字段长度错误")
	ErrValidationRange    = NewError(10504, "字段值超出范围")

	// 认证相关错误 (10600-10699)
	ErrAuth               = NewError(10600, "认证错误")
	ErrToken              = NewError(10601, "Token错误")
	ErrInvalidToken       = NewError(10602, "Token无效")
	ErrTokenExpired       = NewError(10603, "Token已过期")
	ErrTokenTimeout       = NewError(10604, "Token超时")
	ErrTokenGeneration    = NewError(10605, "Token生成失败")
	ErrRefreshToken       = NewError(10606, "刷新Token失败")

	// 加密相关错误 (10700-10799)
	ErrEncryption         = NewError(10700, "加密错误")
	ErrDecryption         = NewError(10701, "解密错误")
	ErrHashGeneration     = NewError(10702, "哈希生成失败")
	ErrSignature          = NewError(10703, "签名错误")

	// 配置相关错误 (10800-10899)
	ErrConfig             = NewError(10800, "配置错误")
	ErrConfigLoad         = NewError(10801, "配置加载失败")
	ErrConfigValidation   = NewError(10802, "配置验证失败")
	ErrConfigMissing      = NewError(10803, "配置项缺失")

	// 第三方服务错误 (10900-10999)
	ErrThirdParty         = NewError(10900, "第三方服务错误")
	ErrThirdPartyTimeout  = NewError(10901, "第三方服务超时")
	ErrThirdPartyResponse = NewError(10902, "第三方服务响应错误")
	ErrThirdPartyAuth     = NewError(10903, "第三方服务认证失败")
)
