package errcode

import (
	"fmt"
	"net/http"
	"sync"
)

// Error 错误结构体
type Error struct {
	code    int      `json:"-"`
	msg     string   `json:"-"`
	details []string `json:"-"`
}

var errorCodes = map[int]struct{}{}
var toStatus sync.Map

// NewError 创建错误
func NewError(code int, msg string) *Error {
	if _, ok := errorCodes[code]; ok {
		fmt.Printf("警告: 错误码 %d 已存在，请使用不同的错误码\n", code)
		return &Error{code: code, msg: msg}
	}
	errorCodes[code] = struct{}{}
	return &Error{code: code, msg: msg}
}

// Error 实现error接口
func (e Error) Error() string {
	return fmt.Sprintf("code: %d, msg: %s", e.Code(), e.Msg())
}

// Code 返回错误码
func (e *Error) Code() int {
	return e.code
}

// Msg 返回错误消息
func (e *Error) Msg() string {
	return e.msg
}

// Msgf 格式化错误消息
func (e *Error) Msgf(args []interface{}) string {
	return fmt.Sprintf(e.msg, args...)
}

// Details 返回错误详情
func (e *Error) Details() []string {
	return e.details
}

// WithDetails 添加错误详情
func (e *Error) WithDetails(details ...string) *Error {
	newError := *e
	newError.details = []string{}
	newError.details = append(newError.details, details...)
	return &newError
}

// SetHTTPStatusCode 设置HTTP状态码映射
func SetHTTPStatusCode(err *Error, status int) {
	toStatus.Store(err.Code(), status)
}

// ToHTTPStatusCode 转换为HTTP状态码
func ToHTTPStatusCode(code int) int {
	if status, ok := toStatus.Load(code); ok {
		return status.(int)
	}
	return http.StatusBadRequest
}

// IsSystemError 判断是否为系统级错误（10000-19999）
func IsSystemError(code int) bool {
	return code >= 10000 && code <= 19999
}

// IsBusinessError 判断是否为业务级错误（20000+）
func IsBusinessError(code int) bool {
	return code >= 20000
}

// DecodeErr 解码错误，返回错误码和消息
func DecodeErr(err error) (int, string) {
	if err == nil {
		return Success.code, Success.msg
	}

	if typed, ok := err.(*Error); ok {
		return typed.code, typed.msg
	}

	return ErrInternalServer.Code(), err.Error()
}

// initToStatus 初始化HTTP状态码映射
func initToStatus() {
	statusMappings := map[int]int{
		Success.Code():               http.StatusOK,
		ErrInternalServer.Code():     http.StatusInternalServerError,
		ErrInvalidParam.Code():       http.StatusBadRequest,
		ErrUnauthorized.Code():       http.StatusUnauthorized,
		ErrForbidden.Code():          http.StatusForbidden,
		ErrNotFound.Code():           http.StatusNotFound,
		ErrDuplicateEntry.Code():     http.StatusConflict,
		ErrTooManyRequests.Code():    http.StatusTooManyRequests,
		ErrServiceUnavailable.Code(): http.StatusServiceUnavailable,
		ErrDatabase.Code():           http.StatusInternalServerError,
		ErrDataNotFound.Code():       http.StatusNotFound, // 数据不存在应该返回404
		ErrValidation.Code():         http.StatusBadRequest,
		ErrToken.Code():              http.StatusUnauthorized,
		ErrInvalidToken.Code():       http.StatusUnauthorized,
		ErrTokenExpired.Code():       http.StatusUnauthorized,
		ErrTokenTimeout.Code():       http.StatusUnauthorized,
	}

	for code, status := range statusMappings {
		toStatus.Store(code, status)
	}
}

func init() {
	initToStatus()
}
