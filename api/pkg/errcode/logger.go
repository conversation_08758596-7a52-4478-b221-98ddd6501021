package errcode

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"kids-platform/pkg/config"
	"kids-platform/pkg/logger"
)

// ErrorContext 错误上下文信息
type ErrorContext struct {
	RequestID string                 `json:"request_id"`
	UserID    uint64                 `json:"user_id,omitempty"`
	Method    string                 `json:"method"`
	Path      string                 `json:"path"`
	IP        string                 `json:"ip"`
	UserAgent string                 `json:"user_agent"`
	Timestamp time.Time              `json:"timestamp"`
	Stack     string                 `json:"stack,omitempty"`
	Extra     map[string]interface{} `json:"extra,omitempty"`
}

// LogError 记录错误日志
func LogError(ctx context.Context, err error, errCtx *ErrorContext) {
	if err == nil {
		return
	}

	// 获取调用栈信息
	if errCtx.Stack == "" {
		errCtx.Stack = getStackTrace(3) // 跳过当前函数和调用者
	}

	// 设置时间戳
	if errCtx.Timestamp.IsZero() {
		errCtx.Timestamp = time.Now()
	}

	// 从context中提取信息
	if errCtx.RequestID == "" {
		if reqID := ctx.Value("request_id"); reqID != nil {
			errCtx.RequestID = reqID.(string)
		}
	}

	if errCtx.UserID == 0 {
		if userID := ctx.Value("user_id"); userID != nil {
			if uid, ok := userID.(uint64); ok {
				errCtx.UserID = uid
			}
		}
	}

	// 判断错误类型并记录不同级别的日志
	if errCodeErr, ok := err.(*Error); ok {
		if IsSystemError(errCodeErr.Code()) {
			// 系统级错误记录为ERROR级别
			logger.Error("System error occurred",
				"error", err.Error(),
				"code", errCodeErr.Code(),
				"message", errCodeErr.Msg(),
				"details", errCodeErr.Details(),
				"context", errCtx,
			)
		} else {
			// 业务级错误记录为WARN级别
			logger.Warn("Business error occurred",
				"error", err.Error(),
				"code", errCodeErr.Code(),
				"message", errCodeErr.Msg(),
				"context", errCtx,
			)
		}
	} else {
		// 未知错误记录为ERROR级别
		logger.Error("Unknown error occurred",
			"error", err.Error(),
			"context", errCtx,
		)
	}
}

// SanitizeError 根据环境脱敏错误信息
func SanitizeError(err error, cfg *config.Config) error {
	if err == nil {
		return nil
	}

	// 开发环境或调试模式下返回原始错误
	if cfg.Security.DebugMode || !cfg.Security.ProductionMode {
		return err
	}

	// 生产环境下对系统级错误进行脱敏
	if errCodeErr, ok := err.(*Error); ok {
		if IsSystemError(errCodeErr.Code()) {
			// 某些系统级错误可以直接返回，不需要脱敏
			switch errCodeErr.Code() {
			case ErrDataNotFound.Code():
				// 数据不存在错误可以直接返回，有助于前端处理
				return err
			case ErrNotFound.Code():
				// 资源不存在错误可以直接返回
				return err
			case ErrUnauthorized.Code():
				// 未授权错误可以直接返回
				return err
			case ErrForbidden.Code():
				// 禁止访问错误可以直接返回
				return err
			case ErrInvalidParam.Code():
				// 参数错误可以直接返回
				return err
			case ErrValidation.Code():
				// 验证错误可以直接返回
				return err
			case ErrToken.Code():
				// JWT相关错误可以直接返回
				return err
			case ErrInvalidToken.Code():
				// JWT无效错误可以直接返回
				return err
			case ErrTokenExpired.Code():
				// JWT过期错误可以直接返回
				return err
			case ErrTokenTimeout.Code():
				// JWT超时错误可以直接返回
				return err
			case ErrTokenGeneration.Code():
				// JWT生成失败错误可以直接返回
				return err
			case ErrRefreshToken.Code():
				// 刷新Token失败错误可以直接返回
				return err
			default:
				// 其他系统级错误返回通用错误信息
				return ErrInternalServer
			}
		}
		// 业务级错误可以直接返回
		return err
	}

	// 未知错误返回通用错误信息
	return ErrInternalServer
}

// getStackTrace 获取调用栈信息
func getStackTrace(skip int) string {
	var stack []string
	for i := skip; i < skip+10; i++ { // 最多获取10层调用栈
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		// 获取函数名
		fn := runtime.FuncForPC(pc)
		var funcName string
		if fn != nil {
			funcName = fn.Name()
		}

		// 简化文件路径，只保留相对路径
		if idx := strings.LastIndex(file, "/"); idx >= 0 {
			file = file[idx+1:]
		}

		stack = append(stack, fmt.Sprintf("%s:%d %s", file, line, funcName))
	}

	return strings.Join(stack, "\n")
}

// WrapError 包装错误并添加上下文信息
func WrapError(err error, message string, details ...string) *Error {
	if err == nil {
		return nil
	}

	// 如果已经是Error类型，添加详情
	if errCodeErr, ok := err.(*Error); ok {
		newErr := *errCodeErr
		newErr.details = append(newErr.details, details...)
		if message != "" {
			newErr.msg = fmt.Sprintf("%s: %s", message, newErr.msg)
		}
		return &newErr
	}

	// 否则创建新的内部服务器错误
	wrappedErr := ErrInternalServer.WithDetails(err.Error())
	if message != "" {
		wrappedErr.msg = fmt.Sprintf("%s: %s", message, wrappedErr.msg)
	}
	if len(details) > 0 {
		wrappedErr.details = append(wrappedErr.details, details...)
	}

	return wrappedErr
}

// NewErrorContext 创建错误上下文
func NewErrorContext(requestID, method, path, ip, userAgent string) *ErrorContext {
	return &ErrorContext{
		RequestID: requestID,
		Method:    method,
		Path:      path,
		IP:        ip,
		UserAgent: userAgent,
		Timestamp: time.Now(),
		Extra:     make(map[string]interface{}),
	}
}

// WithUserID 添加用户ID到错误上下文
func (ec *ErrorContext) WithUserID(userID uint64) *ErrorContext {
	ec.UserID = userID
	return ec
}

// WithExtra 添加额外信息到错误上下文
func (ec *ErrorContext) WithExtra(key string, value interface{}) *ErrorContext {
	if ec.Extra == nil {
		ec.Extra = make(map[string]interface{})
	}
	ec.Extra[key] = value
	return ec
}
