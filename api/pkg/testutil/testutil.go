package testutil

import (
	"bytes"
	"encoding/json"
	"fmt"
	"kids-platform/pkg/cache"
	"kids-platform/pkg/config"
	"kids-platform/pkg/database"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestConfig 测试配置
type TestConfig struct {
	DatabaseURL string
	RedisURL    string
	JWTSecret   string
}

// TestSuite 测试套件 - 只包含基础设施，不包含业务层依赖
type TestSuite struct {
	DB     *gorm.DB
	Redis  cache.RedisClient
	Config *config.Config
	Router *gin.Engine
}

// SetupTestSuite 设置测试套件
func SetupTestSuite(t *testing.T) *TestSuite {
	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 加载测试配置
	cfg := loadTestConfig(t)

	// 初始化数据库
	db := setupTestDatabase(t, cfg)

	// 初始化Redis
	redisClient := setupTestRedis(t, cfg)

	// 创建路由
	router := gin.New()

	return &TestSuite{
		DB:     db,
		Redis:  redisClient,
		Config: cfg,
		Router: router,
	}
}

// TeardownTestSuite 清理测试套件
func (ts *TestSuite) TeardownTestSuite(t *testing.T) {
	// 清理数据库
	if ts.DB != nil {
		cleanupTestDatabase(t, ts.DB)
	}

	// 清理Redis
	if ts.Redis != nil {
		cleanupTestRedis(t, ts.Redis)
	}
}

// loadTestConfig 加载测试配置
func loadTestConfig(t *testing.T) *config.Config {
	// 设置测试环境变量
	os.Setenv("APP_ENV", "test")
	os.Setenv("DB_HOST", "localhost")
	os.Setenv("DB_PORT", "3306")
	os.Setenv("DB_NAME", "test_db")
	os.Setenv("DB_USER", "test_user")
	os.Setenv("DB_PASSWORD", "test_password")
	os.Setenv("REDIS_HOST", "localhost")
	os.Setenv("REDIS_PORT", "6379")
	os.Setenv("JWT_SECRET", "test_jwt_secret_key_for_testing_only")

	cfg, err := config.Load()
	require.NoError(t, err, "Failed to load test config")

	return cfg
}

// setupTestDatabase 设置测试数据库
func setupTestDatabase(t *testing.T, cfg *config.Config) *gorm.DB {
	db, err := database.Init(cfg.Database)
	require.NoError(t, err, "Failed to initialize test database")

	// 运行迁移 - 简化实现，直接返回数据库连接
	// 在实际测试中，可以根据需要添加表结构迁移

	return db
}

// setupTestRedis 设置测试Redis
func setupTestRedis(t *testing.T, cfg *config.Config) cache.RedisClient {
	redisClient, err := cache.NewRedisClient(cfg.Redis)
	require.NoError(t, err, "Failed to initialize test redis")

	return redisClient
}

// cleanupTestDatabase 清理测试数据库
func cleanupTestDatabase(t *testing.T, db *gorm.DB) {
	// 清理测试数据
	// 这里可以添加具体的清理逻辑
	// 例如：删除测试期间创建的数据
}

// cleanupTestRedis 清理测试Redis
func cleanupTestRedis(t *testing.T, redisClient cache.RedisClient) {
	// 清理Redis测试数据
	// 这里可以添加具体的清理逻辑
}

// CreateTestRequest 创建测试请求
func CreateTestRequest(method, url string, body interface{}) *http.Request {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}
	req, _ := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	return req
}

// PerformRequest 执行测试请求
func PerformRequest(router *gin.Engine, req *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	return w
}

// AssertJSONResponse 断言JSON响应
func AssertJSONResponse(t *testing.T, w *httptest.ResponseRecorder, expectedCode int, expectedBody interface{}) {
	assert.Equal(t, expectedCode, w.Code)

	if expectedBody != nil {
		var actualBody interface{}
		err := json.Unmarshal(w.Body.Bytes(), &actualBody)
		require.NoError(t, err)

		expectedJSON, _ := json.Marshal(expectedBody)
		var expectedBodyParsed interface{}
		json.Unmarshal(expectedJSON, &expectedBodyParsed)

		assert.Equal(t, expectedBodyParsed, actualBody)
	}
}

// RandomString 生成随机字符串
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[len(charset)/2] // 简化实现，实际应该用随机数
	}
	return string(b)
}

// RandomEmail 生成随机邮箱
func RandomEmail() string {
	return fmt.Sprintf("<EMAIL>", RandomString(8))
}

// RandomUsername 生成随机用户名
func RandomUsername() string {
	return fmt.Sprintf("user_%s", RandomString(6))
}

// SkipIfShort 如果是短测试则跳过
func SkipIfShort(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过长时间运行的测试")
	}
}

// SkipIfNoDatabase 如果没有数据库则跳过
func SkipIfNoDatabase(t *testing.T) {
	if os.Getenv("DB_HOST") == "" {
		t.Skip("跳过需要数据库的测试")
	}
}

// SkipIfNoRedis 如果没有Redis则跳过
func SkipIfNoRedis(t *testing.T) {
	if os.Getenv("REDIS_HOST") == "" {
		t.Skip("跳过需要Redis的测试")
	}
}

// HTTPTestRequest HTTP测试请求结构
type HTTPTestRequest struct {
	Method      string
	URL         string
	Body        interface{}
	Headers     map[string]string
	QueryParams map[string]string
}

// HTTPTestResponse HTTP测试响应结构
type HTTPTestResponse struct {
	StatusCode int
	Body       []byte
	Headers    map[string][]string
}

// MakeHTTPRequest 发送HTTP测试请求
func (ts *TestSuite) MakeHTTPRequest(t *testing.T, req HTTPTestRequest) *HTTPTestResponse {
	var body io.Reader
	if req.Body != nil {
		jsonBody, err := json.Marshal(req.Body)
		if err != nil {
			t.Fatalf("Failed to marshal request body: %v", err)
		}
		body = bytes.NewBuffer(jsonBody)
	}

	httpReq, err := http.NewRequest(req.Method, req.URL, body)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	// 设置默认Content-Type
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 设置自定义headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 设置查询参数
	if len(req.QueryParams) > 0 {
		q := httpReq.URL.Query()
		for key, value := range req.QueryParams {
			q.Add(key, value)
		}
		httpReq.URL.RawQuery = q.Encode()
	}

	// 执行请求
	w := httptest.NewRecorder()
	ts.Router.ServeHTTP(w, httpReq)

	return &HTTPTestResponse{
		StatusCode: w.Code,
		Body:       w.Body.Bytes(),
		Headers:    w.Header(),
	}
}

// UnmarshalJSON 解析JSON响应
func UnmarshalJSON(body []byte, v interface{}) error {
	return json.Unmarshal(body, v)
}

// AssertErrorResponse 断言错误响应
func AssertErrorResponse(t *testing.T, resp *HTTPTestResponse, expectedStatus int, expectedError string) {
	assert.Equal(t, expectedStatus, resp.StatusCode)

	var result map[string]interface{}
	err := json.Unmarshal(resp.Body, &result)
	require.NoError(t, err)

	// 检查错误码
	if code, ok := result["code"]; ok {
		assert.NotEqual(t, 200, int(code.(float64)))
	}

	// 检查错误消息
	if message, ok := result["message"]; ok {
		if expectedError != "" {
			assert.Contains(t, message.(string), expectedError)
		}
	}
}
