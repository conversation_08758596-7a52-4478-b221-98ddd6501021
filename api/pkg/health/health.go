package health

import (
	"context"
	"time"

	"kids-platform/pkg/cache"

	"gorm.io/gorm"
)

// Status 健康状态
type Status string

const (
	StatusHealthy   Status = "healthy"
	StatusUnhealthy Status = "unhealthy"
	StatusDegraded  Status = "degraded"
)

// CheckResult 检查结果
type CheckResult struct {
	Status  Status        `json:"status"`
	Message string        `json:"message,omitempty"`
	Latency time.Duration `json:"latency"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status   Status                 `json:"status"`
	Checks   map[string]CheckResult `json:"checks"`
	Metadata map[string]interface{} `json:"metadata"`
}

// Checker 健康检查器接口
type Checker interface {
	Check(ctx context.Context) CheckResult
}

// DatabaseChecker 数据库检查器
type DatabaseChecker struct {
	db *gorm.DB
}

// NewDatabaseChecker 创建数据库检查器
func NewDatabaseChecker(db *gorm.DB) *DatabaseChecker {
	return &DatabaseChecker{db: db}
}

// Check 检查数据库连接
func (d *DatabaseChecker) Check(ctx context.Context) CheckResult {
	start := time.Now()

	sqlDB, err := d.db.DB()
	if err != nil {
		return CheckResult{
			Status:  StatusUnhealthy,
			Message: "Failed to get database instance: " + err.Error(),
			Latency: time.Since(start),
		}
	}

	if err := sqlDB.PingContext(ctx); err != nil {
		return CheckResult{
			Status:  StatusUnhealthy,
			Message: "Database ping failed: " + err.Error(),
			Latency: time.Since(start),
		}
	}

	return CheckResult{
		Status:  StatusHealthy,
		Latency: time.Since(start),
	}
}

// RedisChecker Redis检查器
type RedisChecker struct {
	redis cache.RedisClient
}

// NewRedisChecker 创建Redis检查器
func NewRedisChecker(redis cache.RedisClient) *RedisChecker {
	return &RedisChecker{redis: redis}
}

// Check 检查Redis连接
func (r *RedisChecker) Check(ctx context.Context) CheckResult {
	start := time.Now()

	// 尝试设置和获取一个测试键
	testKey := "health_check_test"
	if err := r.redis.Set(ctx, testKey, "ok", time.Minute); err != nil {
		return CheckResult{
			Status:  StatusUnhealthy,
			Message: "Redis set failed: " + err.Error(),
			Latency: time.Since(start),
		}
	}

	if _, err := r.redis.Get(ctx, testKey); err != nil {
		return CheckResult{
			Status:  StatusUnhealthy,
			Message: "Redis get failed: " + err.Error(),
			Latency: time.Since(start),
		}
	}

	// 清理测试键
	r.redis.Delete(ctx, testKey)

	return CheckResult{
		Status:  StatusHealthy,
		Latency: time.Since(start),
	}
}

// HealthChecker 健康检查管理器
type HealthChecker struct {
	checkers map[string]Checker
}

// NewHealthChecker 创建健康检查管理器
func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checkers: make(map[string]Checker),
	}
}

// AddChecker 添加检查器
func (h *HealthChecker) AddChecker(name string, checker Checker) {
	h.checkers[name] = checker
}

// Check 执行所有健康检查
func (h *HealthChecker) Check(ctx context.Context) HealthResponse {
	checks := make(map[string]CheckResult)
	overallStatus := StatusHealthy

	for name, checker := range h.checkers {
		result := checker.Check(ctx)
		checks[name] = result

		// 更新整体状态
		if result.Status == StatusUnhealthy {
			overallStatus = StatusUnhealthy
		} else if result.Status == StatusDegraded && overallStatus == StatusHealthy {
			overallStatus = StatusDegraded
		}
	}

	return HealthResponse{
		Status: overallStatus,
		Checks: checks,
		Metadata: map[string]interface{}{
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0", // 可以从配置或环境变量获取
		},
	}
}
