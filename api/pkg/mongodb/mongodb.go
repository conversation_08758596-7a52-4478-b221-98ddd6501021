package mongodb

import (
	"context"
	"crypto/tls"
	"fmt"
	"kids-platform/pkg/config"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

// Client MongoDB客户端接口
type Client interface {
	Database(name string) *mongo.Database
	Connect(ctx context.Context) error
	Disconnect(ctx context.Context) error
	Ping(ctx context.Context, rp *readpref.ReadPref) error
}

// MongoClient MongoDB客户端实现
type MongoClient struct {
	client *mongo.Client
	config config.MongoDBConfig
}

// NewMongoClient 创建MongoDB客户端
func NewMongoClient(cfg config.MongoDBConfig) (Client, error) {
	if !cfg.Enabled {
		return &MockMongoClient{}, nil
	}

	// 构建连接URI
	uri := buildConnectionURI(cfg)

	// 创建客户端选项
	clientOptions := options.Client().ApplyURI(uri)

	// 设置连接池配置
	if cfg.MaxPoolSize > 0 {
		clientOptions.SetMaxPoolSize(uint64(cfg.MaxPoolSize))
	}
	if cfg.MinPoolSize > 0 {
		clientOptions.SetMinPoolSize(uint64(cfg.MinPoolSize))
	}
	if cfg.MaxIdleTimeMS > 0 {
		clientOptions.SetMaxConnIdleTime(time.Duration(cfg.MaxIdleTimeMS) * time.Millisecond)
	}
	if cfg.ConnectTimeoutMS > 0 {
		clientOptions.SetConnectTimeout(time.Duration(cfg.ConnectTimeoutMS) * time.Millisecond)
	}

	// SSL配置
	if cfg.SSL.Enabled {
		clientOptions.SetTLSConfig(&tls.Config{
			InsecureSkipVerify: cfg.SSL.InsecureSkipVerify,
		})
	}

	// 创建客户端
	client, err := mongo.NewClient(clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create MongoDB client: %w", err)
	}

	return &MongoClient{
		client: client,
		config: cfg,
	}, nil
}

// buildConnectionURI 构建连接URI
func buildConnectionURI(cfg config.MongoDBConfig) string {
	// 如果提供了完整的URI，直接使用
	if cfg.URI != "" {
		return cfg.URI
	}

	// 构建URI
	uri := "mongodb://"

	// 添加认证信息
	if cfg.Username != "" && cfg.Password != "" {
		uri += fmt.Sprintf("%s:%s@", cfg.Username, cfg.Password)
	}

	// 添加主机和端口
	uri += fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)

	// 添加数据库
	if cfg.Database != "" {
		uri += "/" + cfg.Database
	}

	// 添加认证源
	if cfg.AuthSource != "" {
		uri += "?authSource=" + cfg.AuthSource
	}

	return uri
}

// Database 获取数据库实例
func (mc *MongoClient) Database(name string) *mongo.Database {
	return mc.client.Database(name)
}

// Connect 连接到MongoDB
func (mc *MongoClient) Connect(ctx context.Context) error {
	return mc.client.Connect(ctx)
}

// Disconnect 断开MongoDB连接
func (mc *MongoClient) Disconnect(ctx context.Context) error {
	return mc.client.Disconnect(ctx)
}

// Ping 测试连接
func (mc *MongoClient) Ping(ctx context.Context, rp *readpref.ReadPref) error {
	return mc.client.Ping(ctx, rp)
}

// MockMongoClient Mock MongoDB客户端（用于测试或禁用MongoDB时）
type MockMongoClient struct{}

// Database Mock实现
func (mmc *MockMongoClient) Database(name string) *mongo.Database {
	return nil
}

// Connect Mock实现
func (mmc *MockMongoClient) Connect(ctx context.Context) error {
	return nil
}

// Disconnect Mock实现
func (mmc *MockMongoClient) Disconnect(ctx context.Context) error {
	return nil
}

// Ping Mock实现
func (mmc *MockMongoClient) Ping(ctx context.Context, rp *readpref.ReadPref) error {
	return nil
}

// Init 初始化MongoDB连接
func Init(cfg config.MongoDBConfig) (Client, error) {
	client, err := NewMongoClient(cfg)
	if err != nil {
		return nil, err
	}

	// 如果MongoDB被禁用，直接返回mock客户端
	if !cfg.Enabled {
		return client, nil
	}

	// 连接到MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.Connect(ctx); err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// 测试连接
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	return client, nil
}

// IsEnabled 检查MongoDB是否启用
func IsEnabled(cfg config.MongoDBConfig) bool {
	return cfg.Enabled
}

// GetConnectionInfo 获取连接信息（用于日志记录）
func GetConnectionInfo(cfg config.MongoDBConfig) string {
	if !cfg.Enabled {
		return "MongoDB disabled"
	}

	if cfg.URI != "" {
		// 隐藏密码信息
		return fmt.Sprintf("MongoDB URI: %s", maskPassword(cfg.URI))
	}

	return fmt.Sprintf("MongoDB: %s:%d/%s", cfg.Host, cfg.Port, cfg.Database)
}

// maskPassword 隐藏URI中的密码
func maskPassword(uri string) string {
	// 简单的密码隐藏实现
	// 在生产环境中可能需要更复杂的处理
	return uri // 这里可以实现密码隐藏逻辑
}
