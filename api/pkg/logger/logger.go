package logger

import (
	"io"
	"os"
	"path/filepath"

	"kids-platform/pkg/config"

	"github.com/sirupsen/logrus"
)

var Log *logrus.Logger

// IsInitialized 检查logger是否已经初始化
func IsInitialized() bool {
	return Log != nil
}

// Init 初始化日志
func Init(cfg config.LogConfig) error {
	Log = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	Log.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		Log.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		Log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 设置输出
	var output io.Writer = os.Stdout
	if cfg.Output == "file" && cfg.FilePath != "" {
		// 确保日志目录存在
		if err := os.MkdirAll(filepath.Dir(cfg.FilePath), 0755); err != nil {
			return err
		}

		file, err := os.OpenFile(cfg.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return err
		}

		// 同时输出到文件和控制台
		output = io.MultiWriter(os.Stdout, file)
	}

	Log.SetOutput(output)
	return nil
}

// WithRequestID 添加请求ID到日志
func WithRequestID(requestID string) *logrus.Entry {
	return Log.WithField("request_id", requestID)
}

// WithUserID 添加用户ID到日志
func WithUserID(userID uint) *logrus.Entry {
	return Log.WithField("user_id", userID)
}

// WithError 添加错误到日志
func WithError(err error) *logrus.Entry {
	return Log.WithError(err)
}

// Debug 调试日志
func Debug(args ...any) {
	Log.Debug(args...)
}

// Debugf 格式化调试日志
func Debugf(format string, args ...any) {
	Log.Debugf(format, args...)
}

// Info 信息日志
func Info(args ...any) {
	Log.Info(args...)
}

// Infof 格式化信息日志
func Infof(format string, args ...any) {
	Log.Infof(format, args...)
}

// Warn 警告日志
func Warn(args ...any) {
	Log.Warn(args...)
}

// Warnf 格式化警告日志
func Warnf(format string, args ...any) {
	Log.Warnf(format, args...)
}

// Error 错误日志
func Error(args ...any) {
	Log.Error(args...)
}

// Errorf 格式化错误日志
func Errorf(format string, args ...any) {
	Log.Errorf(format, args...)
}

// Fatal 致命错误日志
func Fatal(args ...any) {
	Log.Fatal(args...)
}

// Fatalf 格式化致命错误日志
func Fatalf(format string, args ...any) {
	Log.Fatalf(format, args...)
}
