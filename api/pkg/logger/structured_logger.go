package logger

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"kids-platform/pkg/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LogLevel 日志级别
type LogLevel string

const (
	DebugLevel LogLevel = "debug"
	InfoLevel  LogLevel = "info"
	WarnLevel  LogLevel = "warn"
	ErrorLevel LogLevel = "error"
	FatalLevel LogLevel = "fatal"
	PanicLevel LogLevel = "panic"
)

// LogFormat 日志格式
type LogFormat string

const (
	JSONFormat LogFormat = "json"
	TextFormat LogFormat = "text"
)

// ContextKey 上下文键类型
type ContextKey string

const (
	RequestIDKey ContextKey = "request_id"
	UserIDKey    ContextKey = "user_id"
	TraceIDKey   ContextKey = "trace_id"
	SpanIDKey    ContextKey = "span_id"
)

// StructuredLogger 结构化日志器
type StructuredLogger struct {
	logger *logrus.Logger
	config *LoggerConfig
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      LogLevel  `mapstructure:"level"`
	Format     LogFormat `mapstructure:"format"`
	Output     string    `mapstructure:"output"`      // stdout, stderr, file
	FilePath   string    `mapstructure:"file_path"`   // 日志文件路径
	MaxSize    int       `mapstructure:"max_size"`    // 最大文件大小(MB)
	MaxAge     int       `mapstructure:"max_age"`     // 最大保存天数
	MaxBackups int       `mapstructure:"max_backups"` // 最大备份文件数
	Compress   bool      `mapstructure:"compress"`    // 是否压缩

	// 结构化字段配置
	EnableCaller     bool `mapstructure:"enable_caller"`      // 是否记录调用者信息
	EnableStackTrace bool `mapstructure:"enable_stack_trace"` // 是否记录堆栈信息
	EnableTimestamp  bool `mapstructure:"enable_timestamp"`   // 是否记录时间戳

	// 性能配置
	BufferSize int  `mapstructure:"buffer_size"` // 缓冲区大小
	AsyncWrite bool `mapstructure:"async_write"` // 是否异步写入
}

// DefaultLoggerConfig 默认日志配置
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:            InfoLevel,
		Format:           JSONFormat,
		Output:           "stdout",
		FilePath:         "logs/app.log",
		MaxSize:          100,
		MaxAge:           30,
		MaxBackups:       10,
		Compress:         true,
		EnableCaller:     true,
		EnableStackTrace: false,
		EnableTimestamp:  true,
		BufferSize:       1024,
		AsyncWrite:       false,
	}
}

// NewStructuredLogger 创建结构化日志器
func NewStructuredLogger(config *LoggerConfig) (*StructuredLogger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(string(config.Level))
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}
	logger.SetLevel(level)

	// 设置日志格式
	if config.Format == JSONFormat {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339Nano,
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "caller",
			},
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: time.RFC3339,
			FullTimestamp:   true,
		})
	}

	// 设置输出
	switch config.Output {
	case "stdout":
		logger.SetOutput(os.Stdout)
	case "stderr":
		logger.SetOutput(os.Stderr)
	case "file":
		if err := ensureLogDir(config.FilePath); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}
		file, err := os.OpenFile(config.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %w", err)
		}
		logger.SetOutput(file)
	default:
		logger.SetOutput(os.Stdout)
	}

	// 设置调用者信息
	if config.EnableCaller {
		logger.SetReportCaller(true)
	}

	return &StructuredLogger{
		logger: logger,
		config: config,
	}, nil
}

// ensureLogDir 确保日志目录存在
func ensureLogDir(filePath string) error {
	dir := filepath.Dir(filePath)
	return os.MkdirAll(dir, 0755)
}

// WithContext 添加上下文信息
func (sl *StructuredLogger) WithContext(ctx context.Context) *logrus.Entry {
	entry := sl.logger.WithContext(ctx)

	// 添加请求ID
	if requestID := ctx.Value(RequestIDKey); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}

	// 添加用户ID
	if userID := ctx.Value(UserIDKey); userID != nil {
		entry = entry.WithField("user_id", userID)
	}

	// 添加追踪ID
	if traceID := ctx.Value(TraceIDKey); traceID != nil {
		entry = entry.WithField("trace_id", traceID)
	}

	// 添加Span ID
	if spanID := ctx.Value(SpanIDKey); spanID != nil {
		entry = entry.WithField("span_id", spanID)
	}

	return entry
}

// WithFields 添加字段
func (sl *StructuredLogger) WithFields(fields logrus.Fields) *logrus.Entry {
	return sl.logger.WithFields(fields)
}

// WithField 添加单个字段
func (sl *StructuredLogger) WithField(key string, value interface{}) *logrus.Entry {
	return sl.logger.WithField(key, value)
}

// WithError 添加错误信息
func (sl *StructuredLogger) WithError(err error) *logrus.Entry {
	entry := sl.logger.WithError(err)

	// 如果启用堆栈跟踪，添加堆栈信息
	if sl.config.EnableStackTrace {
		entry = entry.WithField("stack_trace", getStackTrace())
	}

	return entry
}

// Debug 调试日志
func (sl *StructuredLogger) Debug(args ...interface{}) {
	sl.logger.Debug(args...)
}

// Debugf 格式化调试日志
func (sl *StructuredLogger) Debugf(format string, args ...interface{}) {
	sl.logger.Debugf(format, args...)
}

// Info 信息日志
func (sl *StructuredLogger) Info(args ...interface{}) {
	sl.logger.Info(args...)
}

// Infof 格式化信息日志
func (sl *StructuredLogger) Infof(format string, args ...interface{}) {
	sl.logger.Infof(format, args...)
}

// Warn 警告日志
func (sl *StructuredLogger) Warn(args ...interface{}) {
	sl.logger.Warn(args...)
}

// Warnf 格式化警告日志
func (sl *StructuredLogger) Warnf(format string, args ...interface{}) {
	sl.logger.Warnf(format, args...)
}

// Error 错误日志
func (sl *StructuredLogger) Error(args ...interface{}) {
	sl.logger.Error(args...)
}

// Errorf 格式化错误日志
func (sl *StructuredLogger) Errorf(format string, args ...interface{}) {
	sl.logger.Errorf(format, args...)
}

// Fatal 致命错误日志
func (sl *StructuredLogger) Fatal(args ...interface{}) {
	sl.logger.Fatal(args...)
}

// Fatalf 格式化致命错误日志
func (sl *StructuredLogger) Fatalf(format string, args ...interface{}) {
	sl.logger.Fatalf(format, args...)
}

// Panic 恐慌日志
func (sl *StructuredLogger) Panic(args ...interface{}) {
	sl.logger.Panic(args...)
}

// Panicf 格式化恐慌日志
func (sl *StructuredLogger) Panicf(format string, args ...interface{}) {
	sl.logger.Panicf(format, args...)
}

// getStackTrace 获取堆栈跟踪信息
func getStackTrace() string {
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(3, pcs[:])

	var builder strings.Builder
	frames := runtime.CallersFrames(pcs[:n])

	for {
		frame, more := frames.Next()
		builder.WriteString(fmt.Sprintf("%s:%d %s\n", frame.File, frame.Line, frame.Function))
		if !more {
			break
		}
	}

	return builder.String()
}

// LoggerMiddleware Gin日志中间件
func (sl *StructuredLogger) LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)

		// 获取客户端IP
		clientIP := c.ClientIP()

		// 获取请求方法
		method := c.Request.Method

		// 获取状态码
		statusCode := c.Writer.Status()

		// 获取响应大小
		bodySize := c.Writer.Size()

		// 构建完整路径
		if raw != "" {
			path = path + "?" + raw
		}

		// 构建日志字段
		fields := logrus.Fields{
			"status_code": statusCode,
			"latency":     latency.String(),
			"client_ip":   clientIP,
			"method":      method,
			"path":        path,
			"body_size":   bodySize,
			"user_agent":  c.Request.UserAgent(),
		}

		// 添加请求ID
		if requestID := c.GetString("request_id"); requestID != "" {
			fields["request_id"] = requestID
		}

		// 添加用户ID
		if userID := c.GetString("user_id"); userID != "" {
			fields["user_id"] = userID
		}

		// 根据状态码选择日志级别
		entry := sl.logger.WithFields(fields)

		if len(c.Errors) > 0 {
			// 如果有错误，记录错误日志
			entry.Error(c.Errors.String())
		} else if statusCode >= 500 {
			entry.Error("Server error")
		} else if statusCode >= 400 {
			entry.Warn("Client error")
		} else {
			entry.Info("Request completed")
		}
	}
}

// RequestLogger 请求日志记录器
type RequestLogger struct {
	logger *StructuredLogger
}

// NewRequestLogger 创建请求日志记录器
func NewRequestLogger(logger *StructuredLogger) *RequestLogger {
	return &RequestLogger{logger: logger}
}

// LogRequest 记录请求日志
func (rl *RequestLogger) LogRequest(c *gin.Context, fields logrus.Fields) {
	entry := rl.logger.WithFields(fields)

	// 添加基本请求信息
	entry = entry.WithFields(logrus.Fields{
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"query":      c.Request.URL.RawQuery,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	})

	entry.Info("Request received")
}

// LogResponse 记录响应日志
func (rl *RequestLogger) LogResponse(c *gin.Context, statusCode int, latency time.Duration, fields logrus.Fields) {
	entry := rl.logger.WithFields(fields)

	// 添加响应信息
	entry = entry.WithFields(logrus.Fields{
		"status_code": statusCode,
		"latency":     latency.String(),
		"body_size":   c.Writer.Size(),
	})

	if statusCode >= 500 {
		entry.Error("Response sent with server error")
	} else if statusCode >= 400 {
		entry.Warn("Response sent with client error")
	} else {
		entry.Info("Response sent successfully")
	}
}

// BusinessLogger 业务日志记录器
type BusinessLogger struct {
	logger *StructuredLogger
}

// NewBusinessLogger 创建业务日志记录器
func NewBusinessLogger(logger *StructuredLogger) *BusinessLogger {
	return &BusinessLogger{logger: logger}
}

// LogUserAction 记录用户操作日志
func (bl *BusinessLogger) LogUserAction(ctx context.Context, userID uint, action string, resource string, details map[string]interface{}) {
	fields := logrus.Fields{
		"user_id":  userID,
		"action":   action,
		"resource": resource,
		"details":  details,
	}

	bl.logger.WithContext(ctx).WithFields(fields).Info("User action performed")
}

// LogDatabaseOperation 记录数据库操作日志
func (bl *BusinessLogger) LogDatabaseOperation(ctx context.Context, operation string, table string, duration time.Duration, err error) {
	fields := logrus.Fields{
		"operation": operation,
		"table":     table,
		"duration":  duration.String(),
	}

	entry := bl.logger.WithContext(ctx).WithFields(fields)

	if err != nil {
		entry.WithError(err).Error("Database operation failed")
	} else {
		entry.Info("Database operation completed")
	}
}

// LogCacheOperation 记录缓存操作日志
func (bl *BusinessLogger) LogCacheOperation(ctx context.Context, operation string, key string, hit bool, duration time.Duration) {
	fields := logrus.Fields{
		"operation": operation,
		"key":       key,
		"hit":       hit,
		"duration":  duration.String(),
	}

	bl.logger.WithContext(ctx).WithFields(fields).Debug("Cache operation performed")
}

// LogSecurityEvent 记录安全事件日志
func (bl *BusinessLogger) LogSecurityEvent(ctx context.Context, event string, severity string, details map[string]interface{}) {
	fields := logrus.Fields{
		"event":    event,
		"severity": severity,
		"details":  details,
	}

	entry := bl.logger.WithContext(ctx).WithFields(fields)

	switch severity {
	case "critical":
		entry.Error("Critical security event")
	case "high":
		entry.Warn("High severity security event")
	case "medium":
		entry.Warn("Medium severity security event")
	case "low":
		entry.Info("Low severity security event")
	default:
		entry.Info("Security event")
	}
}

// LoggerManager 日志管理器
type LoggerManager struct {
	structured *StructuredLogger
	request    *RequestLogger
	business   *BusinessLogger
	config     *LoggerConfig
}

// NewLoggerManager 创建日志管理器
func NewLoggerManager(cfg *config.Config) (*LoggerManager, error) {
	// 从配置中获取日志配置
	loggerConfig := &LoggerConfig{
		Level:            LogLevel(cfg.Log.Level),
		Format:           JSONFormat,
		Output:           "stdout",
		FilePath:         cfg.Log.FilePath,
		MaxSize:          100,
		MaxAge:           30,
		MaxBackups:       10,
		Compress:         true,
		EnableCaller:     true,
		EnableStackTrace: false,
		EnableTimestamp:  true,
		BufferSize:       1024,
		AsyncWrite:       false,
	}

	// 创建结构化日志器
	structured, err := NewStructuredLogger(loggerConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create structured logger: %w", err)
	}

	// 创建请求日志器
	request := NewRequestLogger(structured)

	// 创建业务日志器
	business := NewBusinessLogger(structured)

	return &LoggerManager{
		structured: structured,
		request:    request,
		business:   business,
		config:     loggerConfig,
	}, nil
}

// GetStructuredLogger 获取结构化日志器
func (lm *LoggerManager) GetStructuredLogger() *StructuredLogger {
	return lm.structured
}

// GetRequestLogger 获取请求日志器
func (lm *LoggerManager) GetRequestLogger() *RequestLogger {
	return lm.request
}

// GetBusinessLogger 获取业务日志器
func (lm *LoggerManager) GetBusinessLogger() *BusinessLogger {
	return lm.business
}

// GetMiddleware 获取日志中间件
func (lm *LoggerManager) GetMiddleware() gin.HandlerFunc {
	return lm.structured.LoggerMiddleware()
}

// Close 关闭日志管理器
func (lm *LoggerManager) Close() error {
	// 这里可以添加清理逻辑，比如关闭文件句柄等
	return nil
}
