package main

import (
	"fmt"
	"kids-platform/pkg/config"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/validator"
)

func main() {
	fmt.Println("Testing basic build and imports...")

	// 测试配置加载
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Config load error: %v\n", err)
	} else {
		fmt.Printf("Config loaded successfully: %s\n", cfg.Server.API.Host)
	}

	// 测试验证器初始化
	validator.InitValidator()
	fmt.Println("Validator initialized successfully")

	// 测试日志管理器
	if cfg != nil {
		loggerManager, err := logger.NewLoggerManager(cfg)
		if err != nil {
			fmt.Printf("Logger manager error: %v\n", err)
		} else {
			fmt.Println("Logger manager created successfully")
			loggerManager.GetStructuredLogger().Info("Test log message")
		}
	}

	fmt.Println("Basic build test completed!")
}
