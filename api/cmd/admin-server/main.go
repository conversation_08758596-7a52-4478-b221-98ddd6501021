package main

import (
	"log"

	"kids-platform/internal/handlers/admin"
	"kids-platform/internal/models"
	"kids-platform/pkg/config"
	"kids-platform/pkg/database"
	"kids-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	if err := logger.Init(cfg.Log); err != nil {
		log.Fatalf("Failed to init logger: %v", err)
	}

	// 初始化数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to init database: %v", err)
	}

	// 自动迁移数据库表
	if err := database.AutoMigrate(db, &models.Users{}); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := gin.Default()

	// 注册Admin路由
	admin.RegisterRoutes(router, db)

	// 启动服务器
	addr := cfg.Server.Admin.Host + ":" + cfg.Server.Admin.Port
	log.Printf("Admin Server starting on %s", addr)

	if err := router.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
