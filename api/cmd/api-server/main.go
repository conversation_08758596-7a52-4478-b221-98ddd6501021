// Package main API服务器
//
//	@title			Go API Framework
//	@version		1.0
//	@description	基于Go和Gin的API框架，支持JWT认证、Redis缓存、健康检查等功能
//	@termsOfService	http://swagger.io/terms/
//
//	@contact.name	API Support
//	@contact.url	http://www.example.com/support
//	@contact.email	<EMAIL>
//
//	@license.name	MIT
//	@license.url	https://opensource.org/licenses/MIT
//
//	@host		localhost:8080
//	@BasePath	/api/v1
//
//	@securityDefinitions.apikey	BearerAuth
//	@in							header
//	@name						Authorization
//	@description				JWT认证，格式：Bearer {token}
package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "kids-platform/docs" // 导入Swagger文档
	"kids-platform/internal/handlers/api"
	"kids-platform/internal/models"
	"kids-platform/pkg/cache"
	"kids-platform/pkg/config"
	"kids-platform/pkg/database"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/validator"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	if err := logger.Init(cfg.Log); err != nil {
		log.Fatalf("Failed to init logger: %v", err)
	}

	// 初始化验证器
	validator.InitValidator()
	log.Printf("Validator initialized")

	// 初始化数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to init database: %v", err)
	}

	// 自动迁移数据库表
	if err := database.AutoMigrate(db, &models.Users{}); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化Redis缓存
	var redisClient cache.RedisClient
	if cfg.Redis.Enabled {
		client, err := cache.NewRedisClient(cfg.Redis)
		if err != nil {
			log.Printf("Redis connection failed, using mock client: %v", err)
			redisClient = cache.NewMockRedisClient()
		} else {
			redisClient = client
			log.Printf("Redis client initialized")
		}
	} else {
		redisClient = cache.NewMockRedisClient()
		log.Printf("Using mock Redis client")
	}

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := gin.Default()

	// 注册API路由
	api.SetupRoutes(router, db, cfg, redisClient)

	// 启动服务器
	addr := cfg.Server.API.Host + ":" + cfg.Server.API.Port
	log.Printf("API Server starting on %s", addr)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	// 在goroutine中启动服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 设置5秒的超时时间来完成现有请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
