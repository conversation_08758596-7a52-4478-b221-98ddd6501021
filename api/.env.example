# 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# 服务器配置
# ===========================================
SERVER_MODE=debug
API_HOST=0.0.0.0
API_PORT=8080
ADMIN_HOST=0.0.0.0
ADMIN_PORT=8081

# ===========================================
# 数据库配置
# ===========================================
DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_db_password
DB_DATABASE=your_database_name
DB_CHARSET=utf8mb4
DB_MAX_IDLE_CONNS=25
DB_MAX_OPEN_CONNS=200
DB_CONN_MAX_LIFETIME=1800

# ===========================================
# Redis配置
# ===========================================
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password
REDIS_DATABASE=0

# ===========================================
# JWT配置
# ===========================================
JWT_SECRET=your_super_secure_jwt_secret_key_at_least_32_characters
JWT_EXPIRE_HOURS=24
JWT_REFRESH_EXPIRE_HOURS=168

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILE_PATH=logs/app.log

# ===========================================
# 文件上传配置
# ===========================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_PATH=uploads/
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# ===========================================
# 安全配置
# ===========================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ===========================================
# 监控配置
# ===========================================
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# ===========================================
# 生产环境特殊配置
# ===========================================
# 生产环境请设置为 true
PRODUCTION_MODE=false
# 生产环境请设置为 false
DEBUG_MODE=true
# 生产环境请设置为 true
SECURE_COOKIES=false
