#!/bin/bash

# 进程信息查看脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${CYAN}🔍 $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 显示进程信息
show_processes() {
    log_header "Go服务进程信息"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 查找Go相关进程
    local go_processes=$(ps aux | grep -E "(api-server|admin-server)" | grep -v grep)
    
    if [ -z "$go_processes" ]; then
        log_warning "未找到运行中的Go服务进程"
        echo ""
        log_info "💡 启动服务: make start [SERVICE=<service>]"
        return 0  # 改为返回0，这不是错误状态
    fi
    
    echo "📊 运行中的Go服务:"
    echo ""
    printf "%-8s %-12s %-8s %-6s %-6s %-10s %-s\n" "PID" "SERVICE" "PORT" "CPU%" "MEM%" "TIME" "COMMAND"
    echo "────────────────────────────────────────────────────────────────────────────────"
    
    echo "$go_processes" | while IFS= read -r line; do
        local pid=$(echo "$line" | awk '{print $2}')
        local cpu=$(echo "$line" | awk '{print $3}')
        local mem=$(echo "$line" | awk '{print $4}')
        local time=$(echo "$line" | awk '{print $10}')
        local cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
        
        # 确定服务类型和端口
        local service=""
        local port=""
        if echo "$cmd" | grep -q "api-server"; then
            service="api-server"
            port="8080"
        elif echo "$cmd" | grep -q "admin-server"; then
            service="admin-server"
            port="8081"
        else
            service="unknown"
            port="N/A"
        fi
        
        # 根据CPU和内存使用率着色
        local cpu_color=""
        local mem_color=""
        
        if (( $(echo "$cpu > 50" | bc -l 2>/dev/null || echo "0") )); then
            cpu_color="${RED}"
        elif (( $(echo "$cpu > 20" | bc -l 2>/dev/null || echo "0") )); then
            cpu_color="${YELLOW}"
        else
            cpu_color="${GREEN}"
        fi
        
        if (( $(echo "$mem > 50" | bc -l 2>/dev/null || echo "0") )); then
            mem_color="${RED}"
        elif (( $(echo "$mem > 20" | bc -l 2>/dev/null || echo "0") )); then
            mem_color="${YELLOW}"
        else
            mem_color="${GREEN}"
        fi
        
        printf "%-8s %-12s %-8s ${cpu_color}%-6s${NC} ${mem_color}%-6s${NC} %-10s %-s\n" \
            "$pid" "$service" "$port" "${cpu}%" "${mem}%" "$time" "$(echo $cmd | cut -c1-40)..."
    done
    
    echo ""
}

# 显示端口占用情况
show_port_usage() {
    log_header "端口占用情况"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local ports=("8080" "8081")
    
    printf "%-8s %-12s %-8s %-s\n" "PORT" "STATUS" "PID" "PROCESS"
    echo "────────────────────────────────────────────────────────"
    
    for port in "${ports[@]}"; do
        if lsof -i :$port >/dev/null 2>&1; then
            local process_info=$(lsof -i :$port -t 2>/dev/null | head -1)
            if [ -n "$process_info" ]; then
                local process_name=$(ps -p $process_info -o comm= 2>/dev/null || echo "unknown")
                printf "%-8s ${GREEN}%-12s${NC} %-8s %-s\n" "$port" "LISTENING" "$process_info" "$process_name"
            else
                printf "%-8s ${GREEN}%-12s${NC} %-8s %-s\n" "$port" "LISTENING" "N/A" "unknown"
            fi
        else
            printf "%-8s ${YELLOW}%-12s${NC} %-8s %-s\n" "$port" "FREE" "N/A" "N/A"
        fi
    done
    
    echo ""
}

# 显示PID文件状态
show_pid_files() {
    log_header "PID文件状态"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local services=("api-server" "admin-server")
    
    printf "%-15s %-10s %-8s %-s\n" "SERVICE" "PID_FILE" "PID" "STATUS"
    echo "─────────────────────────────────────────────────────────"
    
    for service in "${services[@]}"; do
        local pid_file="logs/${service}.pid"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                printf "%-15s ${GREEN}%-10s${NC} %-8s ${GREEN}%-s${NC}\n" "$service" "EXISTS" "$pid" "RUNNING"
            else
                printf "%-15s ${YELLOW}%-10s${NC} %-8s ${RED}%-s${NC}\n" "$service" "EXISTS" "$pid" "DEAD"
            fi
        else
            printf "%-15s ${RED}%-10s${NC} %-8s ${YELLOW}%-s${NC}\n" "$service" "MISSING" "N/A" "STOPPED"
        fi
    done
    
    echo ""
}

# 显示系统资源使用情况
show_system_resources() {
    log_header "系统资源使用情况"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # CPU使用率
    if command -v top >/dev/null 2>&1; then
        echo -n "🖥️  CPU使用率: "
        local cpu_usage=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | tr -d '%' 2>/dev/null || echo "N/A")
        if [ "$cpu_usage" != "N/A" ]; then
            if (( $(echo "$cpu_usage > 80" | bc -l 2>/dev/null || echo "0") )); then
                echo -e "${RED}${cpu_usage}%${NC}"
            elif (( $(echo "$cpu_usage > 50" | bc -l 2>/dev/null || echo "0") )); then
                echo -e "${YELLOW}${cpu_usage}%${NC}"
            else
                echo -e "${GREEN}${cpu_usage}%${NC}"
            fi
        else
            echo -e "${YELLOW}无法获取${NC}"
        fi
    fi
    
    # 内存使用
    if command -v free >/dev/null 2>&1; then
        echo -n "💾 内存使用: "
        local memory_info=$(free -h | grep '^Mem:')
        local used=$(echo $memory_info | awk '{print $3}')
        local total=$(echo $memory_info | awk '{print $2}')
        local percent=$(free | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100.0}')
        
        if (( $(echo "$percent > 80" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${RED}${used}/${total} (${percent}%)${NC}"
        elif (( $(echo "$percent > 60" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${YELLOW}${used}/${total} (${percent}%)${NC}"
        else
            echo -e "${GREEN}${used}/${total} (${percent}%)${NC}"
        fi
    fi
    
    # 磁盘使用
    echo -n "💿 磁盘使用: "
    local disk_info=$(df -h . | tail -1)
    local disk_used=$(echo $disk_info | awk '{print $3}')
    local disk_total=$(echo $disk_info | awk '{print $2}')
    local disk_percent=$(echo $disk_info | awk '{print $5}' | tr -d '%')
    
    if [ "$disk_percent" -gt 90 ]; then
        echo -e "${RED}${disk_used}/${disk_total} (${disk_percent}%)${NC}"
    elif [ "$disk_percent" -gt 80 ]; then
        echo -e "${YELLOW}${disk_used}/${disk_total} (${disk_percent}%)${NC}"
    else
        echo -e "${GREEN}${disk_used}/${disk_total} (${disk_percent}%)${NC}"
    fi
    
    # 系统负载
    if command -v uptime >/dev/null 2>&1; then
        echo -n "📈 系统负载: "
        local load_avg=$(uptime | awk -F'load average:' '{print $2}' | xargs)
        echo -e "${BLUE}${load_avg}${NC}"
    fi
    
    echo ""
}

# 主函数
main() {
    log_header "进程和系统信息概览"
    echo ""
    
    show_processes
    show_port_usage
    show_pid_files
    show_system_resources
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_info "💡 常用命令:"
    log_info "   - 查看详细状态: make status [SERVICE=<service>]"
    log_info "   - 健康检查: make health [SERVICE=<service>]"
    log_info "   - 查看日志: make logs SERVICE=<service>"
    log_info "   - 启动服务: make start [SERVICE=<service>]"
    log_info "   - 停止服务: make stop [SERVICE=<service>]"
}

# 执行主函数
main "$@"
