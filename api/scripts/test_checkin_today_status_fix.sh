#!/bin/bash

# 测试checkin模块"获取今日打卡状态"功能修复
# 验证业务逻辑错误修复和训练营过滤功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置Go环境
export GOROOT=/usr/local/Cellar/go@1.23/1.23.10/libexec

echo "🧪 Checkin模块'获取今日打卡状态'功能修复验证"
echo "=================================================="

# 1. 编译测试
log_info "1. 编译测试..."
if go build -v ./internal/repositories ./internal/services/api ./internal/handlers/api > /dev/null 2>&1; then
    log_success "编译测试通过"
else
    log_error "编译测试失败"
    exit 1
fi

# 2. 语法检查
log_info "2. 语法检查..."
if go vet ./internal/repositories ./internal/services/api ./internal/handlers/api > /dev/null 2>&1; then
    log_success "语法检查通过"
else
    log_error "语法检查失败"
    exit 1
fi

# 3. 检查新增的Repository方法
log_info "3. 检查Repository层新增方法..."

# 检查接口定义
if grep -q "GetByChildAndDateByCampID" ./internal/repositories/checkin_records_repository.go; then
    log_success "Repository接口已添加GetByChildAndDateByCampID方法"
else
    log_error "Repository接口缺少GetByChildAndDateByCampID方法"
    exit 1
fi

# 检查方法实现
if grep -A 10 "func.*GetByChildAndDateByCampID" ./internal/repositories/checkin_records_repository.go | grep -q "camp_id"; then
    log_success "Repository方法实现包含camp_id过滤"
else
    log_error "Repository方法实现缺少camp_id过滤"
    exit 1
fi

# 4. 检查Service层修改
log_info "4. 检查Service层修改..."

# 检查是否使用了新的Repository方法
if grep -q "GetByChildAndDateByCampID" ./internal/services/api/checkin_service.go; then
    log_success "Service层已使用新的Repository方法"
else
    log_error "Service层未使用新的Repository方法"
    exit 1
fi

# 检查错误处理逻辑
if grep -A 5 "GetByChildAndDateByCampID" ./internal/services/api/checkin_service.go | grep -q "ErrDataNotFound"; then
    log_success "Service层正确处理'数据不存在'错误"
else
    log_error "Service层错误处理逻辑有问题"
    exit 1
fi

# 5. 检查SQL查询逻辑
log_info "5. 检查SQL查询逻辑..."

# 检查新方法的SQL查询是否包含camp_id
if grep -A 3 "child_id = ? AND camp_id = ? AND DATE" ./internal/repositories/checkin_records_repository.go; then
    log_success "SQL查询正确包含camp_id过滤条件"
else
    log_error "SQL查询缺少camp_id过滤条件"
    exit 1
fi

# 6. 业务逻辑验证
log_info "6. 业务逻辑验证..."

# 检查返回状态结构
if grep -A 5 "HasCheckedIn.*checkin != nil" ./internal/services/api/checkin_service.go; then
    log_success "正确设置HasCheckedIn状态"
else
    log_error "HasCheckedIn状态设置有问题"
    exit 1
fi

# 7. 向后兼容性检查
log_info "7. 向后兼容性检查..."

# 检查原有的GetByChildAndDate方法是否保留
if grep -q "func.*GetByChildAndDate.*childID uint, date time.Time" ./internal/repositories/checkin_records_repository.go; then
    log_success "原有GetByChildAndDate方法保持向后兼容"
else
    log_error "原有GetByChildAndDate方法被意外修改"
    exit 1
fi

# 8. 错误处理改进验证
log_info "8. 错误处理改进验证..."

# 检查是否正确区分"记录不存在"和"数据库错误"
if grep -A 10 "GetByChildAndDateByCampID" ./internal/services/api/checkin_service.go | grep -q "errCodeErr.*ErrDataNotFound"; then
    log_success "正确区分'记录不存在'和'数据库错误'"
else
    log_error "错误处理逻辑需要改进"
    exit 1
fi

echo ""
echo "🎉 Checkin模块'获取今日打卡状态'功能修复验证完成！"
echo "=================================================="
log_success "所有检查项目都通过了"

echo ""
echo "📋 修复总结："
echo "✅ 新增GetByChildAndDateByCampID方法，支持按训练营过滤"
echo "✅ 修复业务逻辑，正确处理'未打卡'状态"
echo "✅ 改进错误处理，区分'记录不存在'和'数据库错误'"
echo "✅ 保持向后兼容性，原有方法继续可用"
echo "✅ SQL查询正确包含camp_id过滤条件"

echo ""
echo "🔧 技术改进："
echo "• Repository层: 新增支持camp_id的查询方法"
echo "• Service层: 使用新方法并优化错误处理逻辑"
echo "• 业务逻辑: '未打卡'返回200状态码而非500错误"
echo "• 数据过滤: 按特定训练营查询今日打卡状态"

echo ""
echo "📝 下一步建议："
echo "1. 启动后端服务进行功能测试"
echo "2. 使用不同的child_id和camp_id组合测试"
echo "3. 验证'未打卡'状态返回正确的业务数据"
echo "4. 确认前端能正确处理新的响应格式"
