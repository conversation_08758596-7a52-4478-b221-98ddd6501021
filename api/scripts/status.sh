#!/bin/bash

# 服务状态查询脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${CYAN}📊 $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 获取服务状态
get_service_status() {
    local service_name=$1
    local port=$2
    local pid_file="logs/${service_name}.pid"
    local log_file="logs/${service_name}.log"
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}🔍 $service_name 状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查PID文件
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        echo "📄 PID文件: $pid_file (PID: $pid)"
        
        # 检查进程是否存在
        if kill -0 $pid 2>/dev/null; then
            echo -e "🟢 进程状态: ${GREEN}运行中${NC}"
            
            # 获取进程信息
            local process_info=$(ps -p $pid -o pid,ppid,etime,pcpu,pmem,cmd --no-headers 2>/dev/null || echo "")
            if [ -n "$process_info" ]; then
                echo "📈 进程信息:"
                echo "   PID: $(echo $process_info | awk '{print $1}')"
                echo "   PPID: $(echo $process_info | awk '{print $2}')"
                echo "   运行时间: $(echo $process_info | awk '{print $3}')"
                echo "   CPU使用率: $(echo $process_info | awk '{print $4}')%"
                echo "   内存使用率: $(echo $process_info | awk '{print $5}')%"
            fi
            
            # 检查端口
            if [ -n "$port" ]; then
                if lsof -i :$port >/dev/null 2>&1; then
                    echo -e "🌐 端口状态: ${GREEN}$port 已监听${NC}"
                    
                    # 健康检查
                    local health_url=""
                    if [ "$service_name" = "api-server" ]; then
                        health_url="http://localhost:$port/api/v1/health"
                    elif [ "$service_name" = "admin-server" ]; then
                        health_url="http://localhost:$port/admin/v1/health"
                    fi
                    
                    if [ -n "$health_url" ]; then
                        echo -n "🏥 健康检查: "
                        if curl -s --max-time 5 "$health_url" >/dev/null 2>&1; then
                            echo -e "${GREEN}通过${NC}"
                        else
                            echo -e "${RED}失败${NC}"
                        fi
                    fi
                else
                    echo -e "🌐 端口状态: ${RED}$port 未监听${NC}"
                fi
            fi
            
        else
            echo -e "🔴 进程状态: ${RED}已停止${NC} (PID文件存在但进程不存在)"
        fi
    else
        echo -e "📄 PID文件: ${YELLOW}不存在${NC}"
        echo -e "🔴 进程状态: ${RED}未运行${NC}"
        
        # 检查端口是否被其他进程占用
        if [ -n "$port" ] && lsof -i :$port >/dev/null 2>&1; then
            echo -e "🌐 端口状态: ${YELLOW}$port 被其他进程占用${NC}"
            local port_process=$(lsof -i :$port -t 2>/dev/null | head -1)
            if [ -n "$port_process" ]; then
                echo "   占用进程PID: $port_process"
            fi
        fi
    fi
    
    # 检查日志文件
    if [ -f "$log_file" ]; then
        local log_size=$(du -h "$log_file" | cut -f1)
        local log_lines=$(wc -l < "$log_file")
        echo "📋 日志文件: $log_file ($log_size, $log_lines 行)"
        
        # 显示最近的错误日志
        local error_count=$(grep -i "error\|fatal\|panic" "$log_file" 2>/dev/null | wc -l || echo "0")
        if [ "$error_count" -gt 0 ]; then
            echo -e "⚠️  错误日志: ${YELLOW}发现 $error_count 条错误${NC}"
            echo "   最近错误:"
            grep -i "error\|fatal\|panic" "$log_file" 2>/dev/null | tail -3 | sed 's/^/   /' || true
        else
            echo -e "✅ 错误日志: ${GREEN}无错误${NC}"
        fi
    else
        echo -e "📋 日志文件: ${YELLOW}不存在${NC}"
    fi
    
    echo ""
}

# 显示系统概览
show_system_overview() {
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${CYAN}🖥️  系统概览${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 系统负载
    if command -v uptime >/dev/null 2>&1; then
        echo "📈 系统负载: $(uptime | awk -F'load average:' '{print $2}' | xargs)"
    fi
    
    # 内存使用
    if command -v free >/dev/null 2>&1; then
        local memory_info=$(free -h | grep '^Mem:')
        echo "💾 内存使用: $(echo $memory_info | awk '{print $3"/"$2}')"
    fi
    
    # 磁盘使用
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}')
    echo "💿 磁盘使用: $disk_usage"
    
    # Go进程数量
    local go_processes=$(ps aux | grep -E "(api-server|admin-server)" | grep -v grep | wc -l)
    echo "🔧 Go进程数: $go_processes"
    
    echo ""
}

# 主函数
main() {
    local target_service=${1:-all}
    
    log_header "服务状态查询 (SERVICE=$target_service)"
    echo ""
    
    case $target_service in
        "api")
            get_service_status "api-server" "8080"
            ;;
        "admin")
            get_service_status "admin-server" "8081"
            ;;
        "all")
            show_system_overview
            get_service_status "api-server" "8080"
            get_service_status "admin-server" "8081"
            ;;
        *)
            log_error "无效的服务名称: $target_service"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_info "💡 常用命令:"
    log_info "   - 查看日志: make logs SERVICE=<service>"
    log_info "   - 健康检查: make health [SERVICE=<service>]"
    log_info "   - 启动服务: make start [SERVICE=<service>]"
    log_info "   - 停止服务: make stop [SERVICE=<service>]"
}

# 执行主函数
main "$@"
