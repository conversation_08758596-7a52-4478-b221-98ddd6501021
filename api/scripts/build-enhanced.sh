#!/bin/bash

# Go API框架增强构建脚本 - 支持参数化构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 项目信息
PROJECT_NAME="kids-platform"
VERSION=${VERSION:-"dev"}
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u '+%Y-%m-%dT%H:%M:%SZ')
GO_VERSION=$(go version | awk '{print $3}')

# 构建目录
BUILD_DIR="bin"
DIST_DIR="dist"

# 获取目标服务
TARGET_SERVICE=${1:-all}

log_info "开始构建 ${PROJECT_NAME} (SERVICE=$TARGET_SERVICE)..."
echo -e "${YELLOW}版本: ${VERSION}${NC}"
echo -e "${YELLOW}提交: ${GIT_COMMIT}${NC}"
echo -e "${YELLOW}时间: ${BUILD_TIME}${NC}"
echo -e "${YELLOW}Go版本: ${GO_VERSION}${NC}"

# 验证服务名称
validate_service() {
    case $TARGET_SERVICE in
        "api"|"admin"|"all")
            return 0
            ;;
        *)
            log_error "无效的服务名称: $TARGET_SERVICE"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
}

# 清理构建文件
clean_build() {
    local service=$1

    if [ "$service" = "all" ]; then
        log_info "清理所有构建文件..."
        rm -rf ${BUILD_DIR}
        rm -rf ${DIST_DIR}
    else
        log_info "清理 $service 构建文件..."
        rm -f ${BUILD_DIR}/${service}-server*
    fi

    mkdir -p ${BUILD_DIR}
    mkdir -p ${DIST_DIR}
}

# 设置构建标志
setup_build_flags() {
    LDFLAGS="-X kids-platform/pkg/version.Version=${VERSION}"
    LDFLAGS="${LDFLAGS} -X kids-platform/pkg/version.GitCommit=${GIT_COMMIT}"
    LDFLAGS="${LDFLAGS} -X kids-platform/pkg/version.BuildTime=${BUILD_TIME}"

    # 生产环境优化
    LDFLAGS="${LDFLAGS} -s -w"  # 去除调试信息和符号表
}

# 构建单个服务
build_service() {
    local service=$1
    local current_os=$(uname -s | tr '[:upper:]' '[:lower:]')

    log_info "构建 ${service}-server..."

    # 构建当前平台版本（用于本地运行）
    CGO_ENABLED=0 go build \
        -ldflags "${LDFLAGS}" \
        -o ${BUILD_DIR}/${service}-server \
        ./cmd/${service}-server

    if [ $? -eq 0 ]; then
        log_success "${service}-server 构建成功"

        # 显示二进制文件信息
        local binary_size=$(du -h ${BUILD_DIR}/${service}-server | cut -f1)
        log_info "二进制文件大小: $binary_size"
    else
        log_error "${service}-server 构建失败"
        exit 1
    fi
}

# 主构建函数
main_build() {
    validate_service
    clean_build $TARGET_SERVICE
    setup_build_flags

    case $TARGET_SERVICE in
        "api")
            build_service "api"
            ;;
        "admin")
            build_service "admin"
            ;;
        "all")
            build_service "api"
            build_service "admin"
            ;;
    esac
}

# 执行构建
main_build

echo ""
log_success "构建完成！"
echo ""
log_info "📊 构建结果:"

case $TARGET_SERVICE in
    "api")
        if [ -f "${BUILD_DIR}/api-server" ]; then
            log_success "API服务器: ${BUILD_DIR}/api-server"
        fi
        ;;
    "admin")
        if [ -f "${BUILD_DIR}/admin-server" ]; then
            log_success "Admin服务器: ${BUILD_DIR}/admin-server"
        fi
        ;;
    "all")
        if [ -f "${BUILD_DIR}/api-server" ]; then
            log_success "API服务器: ${BUILD_DIR}/api-server"
        fi
        if [ -f "${BUILD_DIR}/admin-server" ]; then
            log_success "Admin服务器: ${BUILD_DIR}/admin-server"
        fi
        ;;
esac

echo ""
log_info "💡 下一步:"
log_info "   - 启动服务: make start [SERVICE=$TARGET_SERVICE]"
log_info "   - 查看状态: make status"
log_info "   - 运行测试: make test [SERVICE=$TARGET_SERVICE]"
