# 🔧 Scripts 目录

本目录包含项目的各种脚本文件，用于构建、部署、测试和维护。所有脚本都经过精心设计，支持开发、测试和生产环境。

## 📁 目录结构

```
scripts/
├── README.md              # 📖 本文档
├── build.sh              # 🔨 基础构建脚本
├── build-enhanced.sh      # ⚡ 增强构建脚本（生产级）
├── start.sh              # 🚀 启动服务脚本
├── stop.sh               # 🛑 停止服务脚本
└── tests/                # 🧪 测试脚本目录
    ├── README.md         # 📋 测试脚本详细说明
    ├── run_all_tests.sh  # 🎯 运行所有测试
    ├── test_api.sh       # 🔍 基础API功能测试
    └── test_enhanced_api.sh # 🚀 增强功能测试（JWT、缓存等）
```

## 🎯 脚本分类

### 构建类脚本
- `build.sh` - 开发环境快速构建
- `build-enhanced.sh` - 生产环境优化构建

### 服务管理脚本
- `start.sh` - 启动API和Admin服务
- `stop.sh` - 优雅停止所有服务

### 测试类脚本
- `tests/` - 完整的测试脚本套件

## 🔨 构建脚本详解

### `build.sh` - 基础构建
**用途**: 开发环境快速构建，适合日常开发和调试

**功能**:
- 编译API服务器 (`cmd/api-server`)
- 编译Admin服务器 (`cmd/admin-server`)
- 输出到 `bin/` 目录
- 基础错误检查

**使用方法**:
```bash
./scripts/build.sh
```

**适用场景**: 开发环境、快速测试、CI基础构建

### `build-enhanced.sh` - 生产级构建
**用途**: 生产环境优化构建，包含完整的构建流程

**功能**:
- 代码格式化检查 (`go fmt`)
- 静态代码分析 (`go vet`)
- 安全漏洞扫描
- 优化编译参数
- 构建信息注入（版本、时间等）
- 二进制文件压缩

**使用方法**:
```bash
./scripts/build-enhanced.sh
```

**适用场景**: 生产部署、发布构建、完整CI/CD流水线

## 🚀 服务管理脚本

### `start.sh` - 服务启动
**用途**: 启动API和Admin服务器

**功能**:
- 检查配置文件
- 创建必要目录（logs等）
- 后台启动API服务器 (端口8080)
- 后台启动Admin服务器 (端口8081)
- 进程ID记录和管理
- 启动状态检查

**使用方法**:
```bash
./scripts/start.sh
```

**环境要求**:
- 配置文件 `configs/config.yaml` 存在
- 数据库连接正常
- 端口8080和8081可用

### `stop.sh` - 服务停止
**用途**: 优雅停止所有运行中的服务

**功能**:
- 查找运行中的服务进程
- 发送SIGTERM信号优雅关闭
- 等待进程正常退出
- 强制终止超时进程
- 清理PID文件

**使用方法**:
```bash
./scripts/stop.sh
```

**安全特性**:
- 优雅关闭，保证数据完整性
- 超时保护，避免僵尸进程

## 🧪 测试脚本套件

测试脚本位于 `tests/` 子目录中，提供完整的API功能验证。

### 快速测试命令
```bash
# 🎯 运行所有测试（推荐）
./scripts/tests/run_all_tests.sh

# 🔍 运行基础API测试
./scripts/tests/test_api.sh

# 🚀 运行增强功能测试
./scripts/tests/test_enhanced_api.sh
```

**详细说明**: 请参考 [`tests/README.md`](tests/README.md)

## 📋 使用指南

### 开发工作流
```bash
# 1. 构建项目
./scripts/build.sh

# 2. 启动服务
./scripts/start.sh

# 3. 运行测试
./scripts/tests/run_all_tests.sh

# 4. 停止服务
./scripts/stop.sh
```

### 生产部署流程
```bash
# 1. 生产级构建
./scripts/build-enhanced.sh

# 2. 启动服务
./scripts/start.sh

# 3. 验证部署
./scripts/tests/test_api.sh
```

### CI/CD集成
```yaml
# GitHub Actions 示例
- name: Build
  run: ./scripts/build-enhanced.sh

- name: Start Services
  run: ./scripts/start.sh

- name: Run Tests
  run: ./scripts/tests/run_all_tests.sh

- name: Stop Services
  run: ./scripts/stop.sh
```

## 🔗 Makefile集成

所有脚本都已集成到项目Makefile中，提供统一的命令接口：

```bash
# 构建相关
make build          # 基础构建 (build.sh)
make build-enhanced # 生产构建 (build-enhanced.sh)

# 服务管理
make start          # 启动服务 (start.sh)
make stop           # 停止服务 (stop.sh)

# 测试相关
make test-api       # API测试 (test_api.sh)
make test-enhanced  # 增强测试 (test_enhanced_api.sh)
make test-all       # 所有测试 (run_all_tests.sh)
```

## ⚠️ 重要注意事项

### 执行环境
- **工作目录**: 所有脚本必须从 `api/` 目录执行
- **权限设置**: 确保脚本有执行权限
  ```bash
  chmod +x scripts/*.sh
  chmod +x scripts/tests/*.sh
  ```

### 依赖要求
- **Go环境**: Go 1.19+
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **Redis**: 6.0+ (可选，有Mock实现)
- **网络**: 端口8080、8081可用

### 环境变量
```bash
# 可选的环境变量
export GO_ENV=development     # 环境模式
export CONFIG_PATH=configs/   # 配置文件路径
export LOG_LEVEL=info         # 日志级别
```

### 故障排除
1. **权限错误**: `chmod +x scripts/*.sh`
2. **端口占用**: 检查8080、8081端口
3. **配置缺失**: 确保 `configs/config.yaml` 存在
4. **数据库连接**: 检查数据库配置和连接

## 🎯 最佳实践

1. **开发阶段**: 使用 `build.sh` + `start.sh` + `test_api.sh`
2. **测试阶段**: 使用 `build-enhanced.sh` + 完整测试套件
3. **生产部署**: 使用 `build-enhanced.sh` + 监控脚本
4. **CI/CD**: 集成所有脚本到自动化流水线
5. **故障排查**: 查看日志文件 `logs/` 目录
