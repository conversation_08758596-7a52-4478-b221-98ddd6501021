#!/bin/bash

# Go包依赖检查脚本
# 用于检查pkg包是否违规依赖internal包

set -e

echo "🔍 开始检查Go包依赖规则..."

# 检查pkg包是否导入internal包
echo "📋 检查pkg包依赖..."
# 如果当前目录不是api，则尝试进入api目录
if [ ! -d "pkg" ] && [ -d "api" ]; then
    cd api
fi
violation_files=$(find pkg/ -name "*.go" -exec grep -l "internal/" {} \; 2>/dev/null)
if [ -n "$violation_files" ]; then
    echo "❌ 错误：发现pkg包依赖internal包"
    echo "违规文件："
    echo "$violation_files"
    echo ""
    echo "🔧 解决方案："
    echo "1. 将业务相关的测试工具移动到 internal/pkg/testutil/"
    echo "2. 保持 pkg/ 包只包含通用工具，不依赖业务逻辑"
    echo "3. 参考文档：docs/standards/development/GO_PACKAGE_ARCHITECTURE_GUIDE.md"
    exit 1
fi

# 检查是否存在循环导入（简单检查）
echo "🔄 检查潜在的循环依赖..."
if go list -f '{{.ImportPath}}: {{.Imports}}' ./... 2>/dev/null | grep -q "internal.*pkg.*internal"; then
    echo "⚠️  警告：可能存在循环依赖，请手动检查"
fi

# 检查测试文件的导入规范
echo "🧪 检查测试文件导入规范..."
test_violations=0

# 检查是否有测试文件同时导入pkg/testutil和internal包
for test_file in $(find . -name "*_test.go"); do
    if grep -q "pkg/testutil" "$test_file" && grep -q "internal/" "$test_file"; then
        if ! grep -q "internal/pkg/testutil" "$test_file"; then
            echo "⚠️  建议：$test_file 可能需要使用 internal/pkg/testutil 而不是 pkg/testutil"
            ((test_violations++))
        fi
    fi
done

if [ $test_violations -gt 0 ]; then
    echo "💡 建议：考虑将业务测试工具迁移到 internal/pkg/testutil/"
fi

echo "✅ 包依赖检查完成"
echo ""
echo "📊 检查结果："
echo "- pkg包依赖检查: ✅ 通过"
echo "- 循环依赖检查: ✅ 通过"
if [ $test_violations -gt 0 ]; then
    echo "- 测试文件规范: ⚠️  有 $test_violations 个建议"
else
    echo "- 测试文件规范: ✅ 通过"
fi
