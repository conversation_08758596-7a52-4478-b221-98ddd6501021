#!/bin/bash

# 增强版启动脚本 - 支持参数化服务管理

set -e

# 服务配置函数
get_service_info() {
    local service=$1
    case $service in
        "api")
            echo "api-server:8080"
            ;;
        "admin")
            echo "admin-server:8081"
            ;;
        *)
            echo ""
            ;;
    esac
}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 检查配置文件
check_config() {
    if [ ! -f "configs/config.yaml" ]; then
        log_warning "配置文件不存在，复制示例配置..."
        cp configs/config.example.yaml configs/config.yaml
        log_info "请编辑 configs/config.yaml 配置文件"
    fi
}

# 检查是否需要构建
check_build() {
    local service=$1
    local binary_name=""
    
    if [ "$service" = "api" ]; then
        binary_name="api-server"
    elif [ "$service" = "admin" ]; then
        binary_name="admin-server"
    fi
    
    if [ ! -f "bin/$binary_name" ]; then
        log_info "检测到 $binary_name 未构建，开始构建..."
        ./scripts/build-enhanced.sh $service
    fi
}

# 启动单个服务
start_service() {
    local service_name=$1
    local binary_path=$2
    local port=$3
    
    local pid_file="logs/${service_name}.pid"
    local log_file="logs/${service_name}.log"
    
    # 检查服务是否已经运行
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            log_warning "$service_name 已经在运行 (PID: $pid)"
            return 0
        else
            log_info "清理过期的PID文件: $pid_file"
            rm -f "$pid_file"
        fi
    fi
    
    # 检查端口是否被占用
    #if lsof -i :$port >/dev/null 2>&1; then
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_error "端口 $port 已被占用，无法启动 $service_name"
        return 1
    fi
    
    log_info "启动 $service_name (端口: $port)..."
    
    # 启动服务
    nohup $binary_path > "$log_file" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$pid_file"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否成功启动
    if kill -0 $pid 2>/dev/null; then
        log_success "$service_name 启动成功 (PID: $pid)"
        log_info "日志文件: $log_file"
        
        # 简单的健康检查
        local health_url=""
        if [ "$service_name" = "api-server" ]; then
            health_url="http://localhost:$port/api/v1/health"
        elif [ "$service_name" = "admin-server" ]; then
            health_url="http://localhost:$port/admin/v1/health"
        fi
        
        if [ -n "$health_url" ]; then
            log_info "等待服务就绪..."
            local count=0
            while [ $count -lt 10 ]; do
                if curl -s "$health_url" >/dev/null 2>&1; then
                    log_success "$service_name 健康检查通过"
                    break
                fi
                sleep 1
                count=$((count + 1))
            done
            
            if [ $count -eq 10 ]; then
                log_warning "$service_name 健康检查超时，请检查日志"
            fi
        fi
    else
        log_error "$service_name 启动失败"
        rm -f "$pid_file"
        return 1
    fi
}

# 主函数
main() {
    local target_service=${1:-all}
    
    log_info "启动Go API框架 (SERVICE=$target_service)..."
    
    # 检查配置文件
    check_config
    
    # 创建日志目录
    mkdir -p logs/
    
    case $target_service in
        "api")
            check_build "api"
            start_service "api-server" "./bin/api-server" "8080"
            ;;
        "admin")
            check_build "admin"
            start_service "admin-server" "./bin/admin-server" "8081"
            ;;
        "all")
            check_build "api"
            check_build "admin"
            start_service "api-server" "./bin/api-server" "8080"
            start_service "admin-server" "./bin/admin-server" "8081"
            ;;
        *)
            log_error "无效的服务名称: $target_service"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "服务启动完成！"
    echo ""
    log_info "📊 服务状态："
    if [ "$target_service" = "api" ] || [ "$target_service" = "all" ]; then
        echo "   - API服务器: http://localhost:8080"
    fi
    if [ "$target_service" = "admin" ] || [ "$target_service" = "all" ]; then
        echo "   - Admin服务器: http://localhost:8081"
    fi
    echo ""
    log_info "📝 查看日志: make logs SERVICE=<service>"
    log_info "📊 查看状态: make status"
    log_info "🛑 停止服务: make stop [SERVICE=<service>]"
}

# 执行主函数
main "$@"
