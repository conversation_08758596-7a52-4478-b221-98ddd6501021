#!/bin/bash

# 增强版测试脚本 - 支持参数化测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${CYAN}🧪 $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 获取参数
TARGET_SERVICE=${1:-all}
TEST_TYPE=${2:-all}

# 验证参数
validate_params() {
    case $TARGET_SERVICE in
        "api"|"admin"|"all")
            ;;
        *)
            log_error "无效的服务名称: $TARGET_SERVICE"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
    
    case $TEST_TYPE in
        "unit"|"integration"|"all")
            ;;
        *)
            log_error "无效的测试类型: $TEST_TYPE"
            log_info "有效的测试类型: unit, integration, all"
            exit 1
            ;;
    esac
}

# 获取测试包路径
get_test_packages() {
    local service=$1
    local packages=""
    
    case $service in
        "api")
            packages="./internal/handlers/api/... ./internal/services/api/... ./internal/repositories/..."
            ;;
        "admin")
            packages="./internal/handlers/admin/... ./internal/services/admin/..."
            ;;
        "all")
            packages="./..."
            ;;
    esac
    
    echo $packages
}

# 运行单元测试
run_unit_tests() {
    local service=$1
    local packages=$(get_test_packages $service)
    
    log_header "运行单元测试 (SERVICE=$service)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    log_info "测试包: $packages"
    echo ""
    
    # 运行单元测试（排除集成测试）
    if go test $packages -v -short -timeout=30s; then
        log_success "单元测试通过"
        return 0
    else
        log_error "单元测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    local service=$1
    
    log_header "运行集成测试 (SERVICE=$service)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查服务是否运行
    check_service_running() {
        local svc=$1
        local port=""
        
        case $svc in
            "api")
                port="8080"
                ;;
            "admin")
                port="8081"
                ;;
        esac
        
        if [ -n "$port" ] && lsof -i :$port >/dev/null 2>&1; then
            log_success "$svc 服务正在运行 (端口: $port)"
            return 0
        else
            log_warning "$svc 服务未运行，将启动服务进行测试"
            return 1
        fi
    }
    
    # 启动测试服务
    start_test_services() {
        log_info "启动测试服务..."
        
        case $service in
            "api")
                if ! check_service_running "api"; then
                    ./scripts/start-enhanced.sh api >/dev/null 2>&1 &
                    sleep 3
                fi
                ;;
            "admin")
                if ! check_service_running "admin"; then
                    ./scripts/start-enhanced.sh admin >/dev/null 2>&1 &
                    sleep 3
                fi
                ;;
            "all")
                if ! check_service_running "api"; then
                    ./scripts/start-enhanced.sh api >/dev/null 2>&1 &
                fi
                if ! check_service_running "admin"; then
                    ./scripts/start-enhanced.sh admin >/dev/null 2>&1 &
                fi
                sleep 5
                ;;
        esac
    }
    
    start_test_services
    
    # 运行集成测试
    local packages=$(get_test_packages $service)
    
    if go test $packages -v -run Integration -timeout=60s; then
        log_success "集成测试通过"
        return 0
    else
        log_error "集成测试失败"
        return 1
    fi
}

# 运行性能测试
run_benchmark_tests() {
    local service=$1
    local packages=$(get_test_packages $service)
    
    log_header "运行性能测试 (SERVICE=$service)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    log_info "测试包: $packages"
    echo ""
    
    if go test $packages -v -bench=. -benchmem -timeout=120s; then
        log_success "性能测试完成"
        return 0
    else
        log_error "性能测试失败"
        return 1
    fi
}

# 生成测试覆盖率报告
generate_coverage() {
    local service=$1
    local packages=$(get_test_packages $service)
    
    log_header "生成测试覆盖率报告 (SERVICE=$service)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    mkdir -p coverage
    local coverage_file="coverage/coverage-${service}.out"
    local coverage_html="coverage/coverage-${service}.html"
    
    log_info "生成覆盖率数据..."
    if go test $packages -v -coverprofile=$coverage_file -timeout=60s; then
        log_info "生成HTML报告..."
        go tool cover -html=$coverage_file -o $coverage_html
        
        # 显示覆盖率统计
        local coverage_percent=$(go tool cover -func=$coverage_file | grep total | awk '{print $3}')
        log_success "测试覆盖率: $coverage_percent"
        log_info "HTML报告: $coverage_html"
        
        return 0
    else
        log_error "覆盖率测试失败"
        return 1
    fi
}

# 显示测试总结
show_test_summary() {
    local service=$1
    local test_type=$2
    local results=("$@")
    
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "测试总结 (SERVICE=$service, TYPE=$test_type)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local total_tests=0
    local passed_tests=0
    
    for result in "${results[@]:2}"; do
        total_tests=$((total_tests + 1))
        if [ "$result" = "0" ]; then
            passed_tests=$((passed_tests + 1))
        fi
    done
    
    echo "📊 测试结果:"
    echo "   总测试数: $total_tests"
    echo "   通过数: $passed_tests"
    echo "   失败数: $((total_tests - passed_tests))"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "所有测试通过！"
    else
        log_error "有 $((total_tests - passed_tests)) 个测试失败"
    fi
    
    echo ""
    log_info "💡 更多操作:"
    log_info "   - 查看覆盖率: make test-coverage"
    log_info "   - 运行基准测试: make bench"
    log_info "   - 查看服务状态: make status"
}

# 主函数
main() {
    validate_params
    
    log_header "开始测试 (SERVICE=$TARGET_SERVICE, TYPE=$TEST_TYPE)"
    echo ""
    
    local results=()
    local exit_code=0
    
    case $TEST_TYPE in
        "unit")
            run_unit_tests $TARGET_SERVICE
            results+=($?)
            ;;
        "integration")
            run_integration_tests $TARGET_SERVICE
            results+=($?)
            ;;
        "all")
            run_unit_tests $TARGET_SERVICE
            results+=($?)
            
            run_integration_tests $TARGET_SERVICE
            results+=($?)
            ;;
    esac
    
    # 计算总体结果
    for result in "${results[@]}"; do
        if [ "$result" != "0" ]; then
            exit_code=1
        fi
    done
    
    show_test_summary $TARGET_SERVICE $TEST_TYPE "${results[@]}"
    
    exit $exit_code
}

# 执行主函数
main "$@"
