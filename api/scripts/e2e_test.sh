#!/bin/bash

# 端到端测试脚本
# 验证从前端到后端的完整流程

set -e

echo "=== 训练营打卡日历 - 端到端测试 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
API_BASE_URL="http://localhost:8080"
TEST_CAMP_ID=1
TEST_CHILD_ID=1
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.CiUcAjXWfZNVmLR954IJdanr0C3MOmEiK5mzXr695AM"

# 函数：打印状态
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 函数：检查服务状态
check_service() {
    print_status "检查后端服务状态..."

    # 直接测试我们的API端点而不是health端点
    if curl -s -f "${API_BASE_URL}/api/v1/camps/${TEST_CAMP_ID}/checkin-calendar/${TEST_CHILD_ID}" \
        -H "Authorization: Bearer ${JWT_TOKEN}" > /dev/null 2>&1; then
        print_success "后端服务运行正常"
        return 0
    else
        print_error "后端服务未运行或不可访问"
        print_status "请确保后端服务在 ${API_BASE_URL} 运行"
        return 1
    fi
}

# 函数：测试API端点
test_api_endpoint() {
    local endpoint="$1"
    local description="$2"
    
    print_status "测试 ${description}..."
    
    local response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer ${JWT_TOKEN}" \
        -H "Content-Type: application/json" \
        "${API_BASE_URL}${endpoint}")

    local body=$(echo "$response" | sed '$d')
    local status_code=$(echo "$response" | tail -n 1)
    
    if [ "$status_code" = "200" ]; then
        print_success "${description} - 状态码: ${status_code}"
        
        # 验证响应是否为有效JSON
        if echo "$body" | jq . > /dev/null 2>&1; then
            print_success "响应数据格式正确"
            
            # 保存响应到临时文件用于进一步验证
            echo "$body" > "/tmp/api_response_$(basename $endpoint).json"
            return 0
        else
            print_error "响应数据不是有效的JSON格式"
            return 1
        fi
    else
        print_error "${description} - 状态码: ${status_code}"
        print_error "响应内容: $body"
        return 1
    fi
}

# 函数：验证API响应数据结构
validate_api_response() {
    local response_file="$1"
    local description="$2"
    
    print_status "验证 ${description} 数据结构..."
    
    if [ ! -f "$response_file" ]; then
        print_error "响应文件不存在: $response_file"
        return 1
    fi
    
    # 检查基本结构
    local has_code=$(jq -r '.code' "$response_file")
    local has_message=$(jq -r '.message' "$response_file")
    local has_data=$(jq -r '.data' "$response_file")
    
    if [ "$has_code" = "0" ] && [ "$has_message" = "success" ] && [ "$has_data" != "null" ]; then
        print_success "基本响应结构正确"
    else
        print_error "基本响应结构不正确"
        return 1
    fi
    
    # 检查训练营打卡日历特定结构
    if [[ "$description" == *"打卡日历"* ]]; then
        local camp_info=$(jq -r '.data.camp_info' "$response_file")
        local calendar_data=$(jq -r '.data.calendar_data' "$response_file")
        local makeup_info=$(jq -r '.data.makeup_info' "$response_file")
        
        if [ "$camp_info" != "null" ] && [ "$calendar_data" != "null" ] && [ "$makeup_info" != "null" ]; then
            print_success "训练营打卡日历数据结构正确"
            
            # 检查日期数据
            local dates_count=$(jq -r '.data.calendar_data.dates | length' "$response_file")
            print_status "日历包含 ${dates_count} 天数据"
            
            # 检查补卡信息
            local total_makeup=$(jq -r '.data.makeup_info.total_count' "$response_file")
            local used_makeup=$(jq -r '.data.makeup_info.used_count' "$response_file")
            local available_makeup=$(jq -r '.data.makeup_info.available_count' "$response_file")
            print_status "补卡信息: 总计${total_makeup}次, 已用${used_makeup}次, 剩余${available_makeup}次"
            
            return 0
        else
            print_error "训练营打卡日历数据结构不完整"
            return 1
        fi
    fi
    
    return 0
}

# 函数：测试数据库连接
test_database() {
    print_status "测试数据库连接..."
    
    # 通过API调用间接测试数据库连接
    if test_api_endpoint "/api/v1/camps/${TEST_CAMP_ID}/checkin-calendar/${TEST_CHILD_ID}" "数据库连接"; then
        print_success "数据库连接正常"
        return 0
    else
        print_error "数据库连接失败"
        return 1
    fi
}

# 函数：性能测试
performance_test() {
    print_status "执行性能测试..."

    local endpoint="${API_BASE_URL}/api/v1/camps/${TEST_CAMP_ID}/checkin-calendar/${TEST_CHILD_ID}"

    # 使用curl的内置时间测量
    local duration=$(curl -s -o /dev/null -w "%{time_total}" \
        -H "Authorization: Bearer ${JWT_TOKEN}" \
        -H "Content-Type: application/json" \
        "$endpoint")

    # 转换为毫秒（duration是秒，带小数）
    local duration_ms=$(echo "$duration * 1000" | bc -l | cut -d. -f1)

    print_status "API响应时间: ${duration_ms}ms"

    if [ $duration_ms -lt 1000 ]; then
        print_success "响应时间良好 (<1秒)"
    elif [ $duration_ms -lt 3000 ]; then
        print_warning "响应时间一般 (1-3秒)"
    else
        print_error "响应时间过慢 (>3秒)"
    fi
}

# 主测试流程
main() {
    echo "开始时间: $(date)"
    echo "测试配置:"
    echo "  - API地址: ${API_BASE_URL}"
    echo "  - 训练营ID: ${TEST_CAMP_ID}"
    echo "  - 孩子ID: ${TEST_CHILD_ID}"
    echo ""
    
    local test_passed=0
    local test_total=0
    
    # 1. 检查服务状态
    ((test_total++))
    if check_service; then
        ((test_passed++))
    fi
    
    # 2. 测试数据库连接
    ((test_total++))
    if test_database; then
        ((test_passed++))
    fi
    
    # 3. 测试训练营打卡日历API
    ((test_total++))
    if test_api_endpoint "/api/v1/camps/${TEST_CAMP_ID}/checkin-calendar/${TEST_CHILD_ID}" "训练营打卡日历API"; then
        ((test_passed++))
        
        # 验证响应数据
        if validate_api_response "/tmp/api_response_checkin-calendar.json" "训练营打卡日历"; then
            print_success "数据结构验证通过"
        else
            print_error "数据结构验证失败"
        fi
    fi
    
    # 4. 性能测试
    performance_test
    
    # 清理临时文件
    rm -f /tmp/api_response_*.json
    
    # 测试结果汇总
    echo ""
    echo "=== 测试结果汇总 ==="
    echo "通过测试: ${test_passed}/${test_total}"
    
    if [ $test_passed -eq $test_total ]; then
        print_success "🎉 所有测试通过！"
        echo ""
        echo "✅ 端到端功能验证成功"
        echo "✅ 用户可以正常查看训练营打卡日历"
        echo "✅ 前后端集成工作正常"
        exit 0
    else
        print_error "❌ 部分测试失败"
        echo ""
        echo "请检查失败的测试项并修复问题"
        exit 1
    fi
}

# 检查依赖
if ! command -v curl &> /dev/null; then
    print_error "curl 命令未找到，请安装 curl"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    print_error "jq 命令未找到，请安装 jq"
    exit 1
fi

# 运行主测试
main
