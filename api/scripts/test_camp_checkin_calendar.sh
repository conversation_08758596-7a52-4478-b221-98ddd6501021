#!/bin/bash

# 训练营打卡日历API测试脚本

set -e

# 配置
API_BASE_URL="http://localhost:8080/api/v1"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查API服务状态..."
    if curl -s "${API_BASE_URL}/health" > /dev/null 2>&1; then
        log_success "API服务正在运行"
    else
        log_error "API服务未运行，请先启动服务"
        exit 1
    fi
}

# 获取测试用的JWT token
get_test_token() {
    log_info "尝试获取测试JWT token..."
    
    # 尝试微信登录获取token
    local test_data='{
        "code": "test_code_123",
        "encrypted_data": "test_encrypted_data", 
        "iv": "test_iv"
    }'
    
    local response=$(curl -s -X POST \
        -H "$CONTENT_TYPE" \
        -d "$test_data" \
        "${API_BASE_URL}/auth/wechat/login" || echo '{"code":1}')
    
    if echo "$response" | grep -q '"code":0'; then
        ACCESS_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        log_success "获取到access_token: ${ACCESS_TOKEN:0:20}..."
    else
        log_warning "无法获取有效token，使用测试token"
        # 使用生成的有效测试token
        ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6InRlc3RfdXNlciIsInJvbGUiOiJ1c2VyIiwidXNlcl90eXBlIjoxLCJpc3MiOiJraWRzLXBsYXRmb3JtIiwic3ViIjoiMSIsImV4cCI6MTc1Mzk1ODYyNywibmJmIjoxNzUzODcyMjI3LCJpYXQiOjE3NTM4NzIyMjd9.CiUcAjXWfZNVmLR954IJdanr0C3MOmEiK5mzXr695AM"
    fi
}

# 测试训练营打卡日历API
test_camp_checkin_calendar() {
    log_info "测试训练营打卡日历API..."
    
    local camp_id=1
    local child_id=1
    
    log_info "请求URL: ${API_BASE_URL}/camps/${camp_id}/checkin-calendar/${child_id}"
    
    local response=$(curl -s -X GET \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "${API_BASE_URL}/camps/${camp_id}/checkin-calendar/${child_id}")
    
    echo "Response: $response"
    
    # 检查响应
    if echo "$response" | grep -q '"code":0'; then
        log_success "训练营打卡日历API测试通过"
        
        # 检查响应结构
        if echo "$response" | grep -q '"camp_info"' && \
           echo "$response" | grep -q '"calendar_data"' && \
           echo "$response" | grep -q '"makeup_info"'; then
            log_success "响应结构正确，包含所有必需字段"
        else
            log_warning "响应结构可能不完整"
        fi
        
    elif echo "$response" | grep -q '"code":40001'; then
        log_warning "认证失败，可能需要有效的JWT token"
    elif echo "$response" | grep -q '"code":40004'; then
        log_warning "数据不存在，可能需要先创建测试数据"
    else
        log_error "API调用失败"
        echo "错误响应: $response"
    fi
}

# 测试不同参数的API调用
test_different_parameters() {
    log_info "测试不同参数的API调用..."
    
    # 测试不存在的训练营
    log_info "测试不存在的训练营ID..."
    local response=$(curl -s -X GET \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "${API_BASE_URL}/camps/999/checkin-calendar/1")
    
    if echo "$response" | grep -q '"code":40004'; then
        log_success "正确处理不存在的训练营"
    else
        log_warning "不存在训练营的处理可能有问题"
    fi
    
    # 测试不存在的孩子
    log_info "测试不存在的孩子ID..."
    local response=$(curl -s -X GET \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "${API_BASE_URL}/camps/1/checkin-calendar/999")
    
    if echo "$response" | grep -q '"code":40004'; then
        log_success "正确处理不存在的孩子"
    else
        log_warning "不存在孩子的处理可能有问题"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "训练营打卡日历API测试"
    echo "=========================================="
    
    check_service
    get_test_token
    test_camp_checkin_calendar
    test_different_parameters
    
    echo "=========================================="
    echo "测试完成"
    echo "=========================================="
}

# 运行测试
main "$@"
