#!/bin/bash

# 启动脚本

set -e

echo "🚀 启动Go API框架..."

# 进入api目录
cd "$(dirname "$0")/.."

# 检查配置文件
if [ ! -f "configs/config.yaml" ]; then
    echo "⚠️  配置文件不存在，复制示例配置..."
    cp configs/config.example.yaml configs/config.yaml
    echo "📝 请编辑 configs/config.yaml 配置文件"
fi

# 检查是否需要构建
if [ ! -f "bin/api-server" ] || [ ! -f "bin/admin-server" ]; then
    echo "🔨 检测到未构建，开始构建..."
    ./scripts/build.sh
fi

# 启动服务的函数
start_service() {
    local service_name=$1
    local binary_path=$2
    local port=$3
    
    echo "🚀 启动 $service_name (端口: $port)..."
    
    # 检查端口是否被占用
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，请检查配置或停止占用进程"
        return 1
    fi
    
    # 启动服务
    nohup $binary_path > logs/${service_name}.log 2>&1 &
    local pid=$!
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否启动成功
    if kill -0 $pid 2>/dev/null; then
        echo "✅ $service_name 启动成功 (PID: $pid)"
        echo $pid > logs/${service_name}.pid
    else
        echo "❌ $service_name 启动失败"
        return 1
    fi
}

# 创建日志目录
mkdir -p logs/

# 启动API服务器
start_service "api-server" "./bin/api-server" "8080"

# 启动Admin服务器
start_service "admin-server" "./bin/admin-server" "8081"

echo ""
echo "🎉 所有服务启动完成！"
echo ""
echo "📊 服务状态："
echo "   - API服务器: http://localhost:8080"
echo "   - Admin服务器: http://localhost:8081"
echo ""
echo "📝 日志文件："
echo "   - API服务器: logs/api-server.log"
echo "   - Admin服务器: logs/admin-server.log"
echo ""
echo "🛑 停止服务: ./scripts/stop.sh"
