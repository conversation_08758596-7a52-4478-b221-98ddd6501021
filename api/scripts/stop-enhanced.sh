#!/bin/bash

# 增强版停止脚本 - 支持参数化服务管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 停止单个服务
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if kill -0 $pid 2>/dev/null; then
            log_info "停止 $service_name (PID: $pid)..."
            kill $pid
            
            # 等待进程结束
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
                echo -n "."
            done
            echo ""
            
            # 如果进程仍然存在，强制杀死
            if kill -0 $pid 2>/dev/null; then
                log_warning "强制停止 $service_name..."
                kill -9 $pid
                sleep 1
            fi
            
            # 再次检查进程是否已停止
            if kill -0 $pid 2>/dev/null; then
                log_error "$service_name 停止失败"
                return 1
            else
                log_success "$service_name 已停止"
            fi
        else
            log_warning "$service_name 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        log_warning "$service_name PID文件不存在，可能未运行"
    fi
}

# 检查服务状态
check_service_status() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            return 0  # 服务正在运行
        fi
    fi
    return 1  # 服务未运行
}

# 主函数
main() {
    local target_service=${1:-all}
    
    log_info "停止Go API框架 (SERVICE=$target_service)..."
    
    case $target_service in
        "api")
            stop_service "api-server"
            ;;
        "admin")
            stop_service "admin-server"
            ;;
        "all")
            stop_service "api-server"
            stop_service "admin-server"
            ;;
        *)
            log_error "无效的服务名称: $target_service"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
    
    echo ""
    
    # 显示最终状态
    case $target_service in
        "api")
            if check_service_status "api-server"; then
                log_error "API服务器仍在运行"
            else
                log_success "API服务器已完全停止"
            fi
            ;;
        "admin")
            if check_service_status "admin-server"; then
                log_error "Admin服务器仍在运行"
            else
                log_success "Admin服务器已完全停止"
            fi
            ;;
        "all")
            local api_running=false
            local admin_running=false
            
            if check_service_status "api-server"; then
                api_running=true
                log_error "API服务器仍在运行"
            fi
            
            if check_service_status "admin-server"; then
                admin_running=true
                log_error "Admin服务器仍在运行"
            fi
            
            if [ "$api_running" = false ] && [ "$admin_running" = false ]; then
                log_success "所有服务已完全停止！"
            fi
            ;;
    esac
    
    echo ""
    log_info "💡 提示："
    log_info "   - 查看状态: make status"
    log_info "   - 启动服务: make start [SERVICE=<service>]"
}

# 执行主函数
main "$@"
