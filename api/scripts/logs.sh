#!/bin/bash

# 日志查看脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${CYAN}📋 $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 显示日志
show_logs() {
    local service_name=$1
    local lines=${2:-50}
    local follow=${3:-false}
    local log_file="logs/${service_name}.log"
    
    if [ ! -f "$log_file" ]; then
        log_error "日志文件不存在: $log_file"
        log_info "请确保服务已启动或曾经启动过"
        return 1
    fi
    
    local file_size=$(du -h "$log_file" | cut -f1)
    local total_lines=$(wc -l < "$log_file")
    
    log_header "$service_name 日志 ($file_size, $total_lines 行)"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if [ "$follow" = true ]; then
        log_info "实时跟踪日志 (按 Ctrl+C 退出)..."
        echo ""
        tail -f "$log_file"
    else
        log_info "显示最近 $lines 行日志..."
        echo ""
        
        # 使用颜色高亮不同级别的日志
        tail -n "$lines" "$log_file" | while IFS= read -r line; do
            if echo "$line" | grep -qi "error\|fatal\|panic"; then
                echo -e "${RED}$line${NC}"
            elif echo "$line" | grep -qi "warn"; then
                echo -e "${YELLOW}$line${NC}"
            elif echo "$line" | grep -qi "info"; then
                echo -e "${GREEN}$line${NC}"
            elif echo "$line" | grep -qi "debug"; then
                echo -e "${BLUE}$line${NC}"
            else
                echo "$line"
            fi
        done
    fi
}

# 显示日志统计
show_log_stats() {
    local service_name=$1
    local log_file="logs/${service_name}.log"
    
    if [ ! -f "$log_file" ]; then
        log_error "日志文件不存在: $log_file"
        return 1
    fi
    
    log_header "$service_name 日志统计"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local total_lines=$(wc -l < "$log_file")
    local file_size=$(du -h "$log_file" | cut -f1)
    
    echo "📊 基本信息:"
    echo "   总行数: $total_lines"
    echo "   文件大小: $file_size"
    echo "   文件路径: $log_file"
    
    echo ""
    echo "📈 日志级别统计:"
    
    local error_count=$(grep -ci "error\|fatal\|panic" "$log_file" 2>/dev/null || echo "0")
    local warn_count=$(grep -ci "warn" "$log_file" 2>/dev/null || echo "0")
    local info_count=$(grep -ci "info" "$log_file" 2>/dev/null || echo "0")
    local debug_count=$(grep -ci "debug" "$log_file" 2>/dev/null || echo "0")
    
    echo -e "   ${RED}ERROR/FATAL/PANIC: $error_count${NC}"
    echo -e "   ${YELLOW}WARN: $warn_count${NC}"
    echo -e "   ${GREEN}INFO: $info_count${NC}"
    echo -e "   ${BLUE}DEBUG: $debug_count${NC}"
    
    # 显示最近的错误
    if [ "$error_count" -gt 0 ]; then
        echo ""
        echo "🚨 最近的错误 (最多5条):"
        grep -i "error\|fatal\|panic" "$log_file" 2>/dev/null | tail -5 | while IFS= read -r line; do
            echo -e "   ${RED}$line${NC}"
        done
    fi
    
    echo ""
}

# 清理日志
clean_logs() {
    local service_name=$1
    local log_file="logs/${service_name}.log"
    
    if [ ! -f "$log_file" ]; then
        log_warning "日志文件不存在: $log_file"
        return 0
    fi
    
    local file_size=$(du -h "$log_file" | cut -f1)
    
    echo -n "确定要清理 $service_name 的日志文件吗？($file_size) [y/N]: "
    read -r confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        > "$log_file"
        log_success "$service_name 日志已清理"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助
show_help() {
    echo "日志查看脚本使用方法:"
    echo ""
    echo "基本用法:"
    echo "  $0 <service>                    - 显示最近50行日志"
    echo "  $0 <service> <lines>            - 显示最近N行日志"
    echo "  $0 <service> follow             - 实时跟踪日志"
    echo "  $0 <service> stats              - 显示日志统计"
    echo "  $0 <service> clean              - 清理日志文件"
    echo ""
    echo "参数说明:"
    echo "  service: api 或 admin"
    echo "  lines: 显示的行数 (默认50)"
    echo ""
    echo "示例:"
    echo "  $0 api                          - 显示API服务最近50行日志"
    echo "  $0 admin 100                    - 显示Admin服务最近100行日志"
    echo "  $0 api follow                   - 实时跟踪API服务日志"
    echo "  $0 admin stats                  - 显示Admin服务日志统计"
}

# 主函数
main() {
    local service_name=$1
    local action=${2:-50}
    
    if [ -z "$service_name" ]; then
        log_error "请指定服务名称"
        show_help
        exit 1
    fi
    
    # 验证服务名称
    if [ "$service_name" != "api" ] && [ "$service_name" != "admin" ]; then
        log_error "无效的服务名称: $service_name"
        log_info "有效的服务名称: api, admin"
        exit 1
    fi
    
    # 转换服务名称为完整名称
    local full_service_name=""
    if [ "$service_name" = "api" ]; then
        full_service_name="api-server"
    elif [ "$service_name" = "admin" ]; then
        full_service_name="admin-server"
    fi
    
    case $action in
        "follow")
            show_logs "$full_service_name" 50 true
            ;;
        "stats")
            show_log_stats "$full_service_name"
            ;;
        "clean")
            clean_logs "$full_service_name"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            # 检查是否是数字
            if [[ "$action" =~ ^[0-9]+$ ]]; then
                show_logs "$full_service_name" "$action" false
            else
                log_error "无效的操作: $action"
                show_help
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
