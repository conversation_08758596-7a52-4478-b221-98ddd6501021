#!/bin/bash

# 测试运行脚本
# 用于运行项目的单元测试和集成测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo -e "${BLUE}=== 微信登录功能测试套件 ===${NC}"
echo "项目目录: $PROJECT_ROOT"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: Go未安装或未在PATH中${NC}"
    exit 1
fi

echo -e "${BLUE}Go版本:${NC}"
go version
echo ""

# 设置测试环境变量
export GO_ENV=test
export CONFIG_PATH="$PROJECT_ROOT/tests/test_config.yaml"

# 创建测试输出目录
mkdir -p "$PROJECT_ROOT/tests/output"

echo -e "${YELLOW}=== 安装测试依赖 ===${NC}"
go mod tidy
echo ""

echo -e "${YELLOW}=== 运行单元测试 ===${NC}"
echo "测试目录: tests/unit"

# 运行单元测试
if go test -v -race -coverprofile="$PROJECT_ROOT/tests/output/unit_coverage.out" ./tests/unit/...; then
    echo -e "${GREEN}✓ 单元测试通过${NC}"
else
    echo -e "${RED}✗ 单元测试失败${NC}"
    exit 1
fi
echo ""

echo -e "${YELLOW}=== 运行集成测试 ===${NC}"
echo "测试目录: tests/integration"

# 运行集成测试
if go test -v -race -coverprofile="$PROJECT_ROOT/tests/output/integration_coverage.out" ./tests/integration/...; then
    echo -e "${GREEN}✓ 集成测试通过${NC}"
else
    echo -e "${RED}✗ 集成测试失败${NC}"
    exit 1
fi
echo ""

echo -e "${YELLOW}=== 生成测试覆盖率报告 ===${NC}"

# 合并覆盖率文件
echo "mode: atomic" > "$PROJECT_ROOT/tests/output/coverage.out"
tail -n +2 "$PROJECT_ROOT/tests/output/unit_coverage.out" >> "$PROJECT_ROOT/tests/output/coverage.out"
tail -n +2 "$PROJECT_ROOT/tests/output/integration_coverage.out" >> "$PROJECT_ROOT/tests/output/coverage.out"

# 生成HTML覆盖率报告
go tool cover -html="$PROJECT_ROOT/tests/output/coverage.out" -o "$PROJECT_ROOT/tests/output/coverage.html"

# 显示覆盖率统计
echo "总体测试覆盖率:"
go tool cover -func="$PROJECT_ROOT/tests/output/coverage.out" | tail -1

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo "覆盖率报告: $PROJECT_ROOT/tests/output/coverage.html"
echo ""

# 可选：运行基准测试
if [ "$1" = "--bench" ]; then
    echo -e "${YELLOW}=== 运行基准测试 ===${NC}"
    go test -bench=. -benchmem ./tests/unit/... ./tests/integration/...
    echo ""
fi

# 可选：运行竞态检测
if [ "$1" = "--race" ]; then
    echo -e "${YELLOW}=== 运行竞态检测 ===${NC}"
    go test -race ./tests/unit/... ./tests/integration/...
    echo ""
fi

echo -e "${BLUE}测试套件执行完成！${NC}"
