#!/bin/bash

# 增强API测试脚本
# 测试JWT认证、Redis缓存、健康检查等功能

BASE_URL="http://localhost:8080/api/v1"

echo "=== 增强API功能测试 ==="

# 1. 健康检查测试
echo "1. 测试健康检查端点..."
curl -s "$BASE_URL/health" | jq '.'
echo ""

# 2. 用户注册测试
echo "2. 测试用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "Test User"
  }')
echo "$REGISTER_RESPONSE" | jq '.'
echo ""

# 3. 用户登录测试
echo "3. 测试用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }')
echo "$LOGIN_RESPONSE" | jq '.'

# 提取JWT token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')
if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
  echo "❌ 登录失败，无法获取token"
  exit 1
fi
echo "✅ 获取到JWT token: ${TOKEN:0:50}..."
echo ""

# 4. 测试受保护的端点
echo "4. 测试受保护的用户资料端点..."
curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/auth/profile" | jq '.'
echo ""

# 5. 测试用户列表（缓存测试）
echo "5. 测试用户列表（第一次请求，应该从数据库获取）..."
curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/users" | jq '.'
echo ""

echo "6. 测试用户列表（第二次请求，应该从缓存获取）..."
curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/users" | jq '.'
echo ""

# 7. 测试无效token
echo "7. 测试无效token..."
curl -s -H "Authorization: Bearer invalid-token" "$BASE_URL/users" | jq '.'
echo ""

# 8. 测试登出
echo "8. 测试用户登出..."
curl -s -X POST -H "Authorization: Bearer $TOKEN" "$BASE_URL/auth/logout" | jq '.'
echo ""

echo "=== 测试完成 ==="
