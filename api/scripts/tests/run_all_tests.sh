#!/bin/bash

# 运行所有API测试的主脚本

set -e  # 遇到错误时退出

echo "🧪 开始运行API测试套件..."
echo "=================================="

# 检查必要的工具
command -v curl >/dev/null 2>&1 || { echo "❌ 需要安装 curl"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo "❌ 需要安装 jq"; exit 1; }

# 检查API服务是否运行
API_URL="http://localhost:8080/api/v1/health"
if ! curl -s "$API_URL" >/dev/null 2>&1; then
    echo "❌ API服务未运行，请先启动服务："
    echo "   cd api && ./scripts/start.sh"
    exit 1
fi

echo "✅ API服务运行正常"
echo ""

# 运行基础测试
echo "📋 运行基础API测试..."
echo "------------------------"
if ./test_api.sh; then
    echo "✅ 基础测试通过"
else
    echo "❌ 基础测试失败"
    exit 1
fi

echo ""

# 运行增强功能测试
echo "🚀 运行增强功能测试..."
echo "------------------------"
if ./test_enhanced_api.sh; then
    echo "✅ 增强功能测试通过"
else
    echo "❌ 增强功能测试失败"
    exit 1
fi

echo ""
echo "🎉 所有测试通过！"
echo "=================================="
