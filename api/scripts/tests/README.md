# 🧪 API 测试脚本套件

本目录包含完整的API功能测试脚本，提供自动化的功能验证和回归测试。

## 📁 测试脚本概览

```
tests/
├── README.md              # 📖 本文档
├── run_all_tests.sh      # 🎯 主测试脚本（运行所有测试）
├── test_api.sh           # 🔍 基础API功能测试
└── test_enhanced_api.sh  # 🚀 增强功能测试
```

## 🔍 测试脚本详解

### `run_all_tests.sh` - 主测试脚本
**功能**: 运行所有测试脚本，提供完整的测试覆盖

**测试流程**:
1. 环境检查（服务状态、依赖工具）
2. 运行基础API测试
3. 运行增强功能测试
4. 生成测试报告
5. 清理测试数据

**使用方法**:
```bash
./scripts/tests/run_all_tests.sh
```

### `test_api.sh` - 基础API功能测试
**测试范围**:
- ✅ **用户管理**: 注册、登录、获取用户信息
- ✅ **CRUD操作**: 创建、读取、更新、删除
- ✅ **数据验证**: 请求参数验证、响应格式检查
- ✅ **错误处理**: 错误状态码、错误消息格式
- ✅ **基础认证**: 简单的认证流程

**测试用例**:
```bash
# 用户注册
POST /api/v1/auth/register

# 用户登录
POST /api/v1/auth/login

# 获取用户列表
GET /api/v1/users

# 创建用户
POST /api/v1/users

# 更新用户
PUT /api/v1/users/{id}

# 删除用户
DELETE /api/v1/users/{id}
```

### `test_enhanced_api.sh` - 增强功能测试
**测试范围**:
- 🔐 **JWT认证系统**: 访问令牌、刷新令牌、令牌过期处理
- 🚀 **Redis缓存**: 缓存命中、缓存失效、性能对比
- 💚 **健康检查**: 服务状态、数据库连接、Redis连接
- 🛡️ **中间件功能**: CORS、请求ID、速率限制、日志记录
- 🔒 **权限控制**: 受保护端点、权限验证
- 📊 **性能监控**: 响应时间、并发处理

**高级测试用例**:
```bash
# JWT认证流程
POST /api/v1/auth/login          # 获取令牌
GET /api/v1/users (with token)   # 使用令牌访问
POST /api/v1/auth/refresh        # 刷新令牌

# 缓存测试
GET /api/v1/users (第一次)        # 数据库查询
GET /api/v1/users (第二次)        # 缓存命中

# 健康检查
GET /api/v1/health               # 服务健康状态
GET /api/v1/health/detailed      # 详细健康信息

# 中间件测试
OPTIONS /api/v1/users            # CORS预检请求
GET /api/v1/users (高频)          # 速率限制测试
```

## 🚀 使用指南

### 📋 前置条件

#### 1. 服务启动
```bash
# 从项目根目录启动服务
cd api
./scripts/start.sh

# 验证服务状态
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/admin/health
```

#### 2. 依赖工具安装
```bash
# macOS
brew install curl jq

# Ubuntu/Debian
sudo apt-get install curl jq

# CentOS/RHEL
sudo yum install curl jq
```

#### 3. 环境检查
```bash
# 检查端口占用
lsof -i :8080  # API服务
lsof -i :8081  # Admin服务

# 检查数据库连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查Redis连接（如果使用）
redis-cli ping
```

### 🎯 运行测试

#### 快速测试（推荐）
```bash
# 运行所有测试
./scripts/tests/run_all_tests.sh
```

#### 单独运行测试
```bash
# 基础功能测试
./scripts/tests/test_api.sh

# 增强功能测试
./scripts/tests/test_enhanced_api.sh
```

#### 详细模式运行
```bash
# 显示详细输出
./scripts/tests/test_api.sh --verbose

# 保存测试日志
./scripts/tests/run_all_tests.sh > test_results.log 2>&1
```

## ⚙️ 测试配置

### 默认配置
```bash
# 服务地址
API_BASE_URL="http://localhost:8080"
ADMIN_BASE_URL="http://localhost:8081"

# 测试数据
TEST_USER="testuser"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="Test123456"

# 超时设置
REQUEST_TIMEOUT=30
TEST_TIMEOUT=300
```

### 自定义配置
```bash
# 设置环境变量覆盖默认配置
export API_BASE_URL="http://your-api-server:8080"
export TEST_USER="your_test_user"

# 或编辑脚本中的配置变量
vim scripts/tests/test_api.sh
```

## 📊 测试结果解读

### ✅ 成功指标
```bash
# 基础测试成功示例
✅ 用户注册: HTTP 201 Created
✅ 用户登录: HTTP 200 OK, Token获取成功
✅ 获取用户列表: HTTP 200 OK, 数据格式正确
✅ 创建用户: HTTP 201 Created, ID返回
✅ 更新用户: HTTP 200 OK, 数据更新成功
✅ 删除用户: HTTP 200 OK, 用户已删除

# 增强测试成功示例
✅ JWT认证: 令牌生成和验证成功
✅ 令牌刷新: 新令牌获取成功
✅ 缓存测试: 第二次请求响应时间 < 50ms
✅ 健康检查: 所有组件状态 healthy
✅ CORS测试: 预检请求成功
✅ 速率限制: 限制机制正常工作
```

### ❌ 失败排查

#### 连接问题
```bash
# 检查服务状态
ps aux | grep api-server
ps aux | grep admin-server

# 检查端口监听
netstat -tlnp | grep :8080
netstat -tlnp | grep :8081

# 重启服务
./scripts/stop.sh
./scripts/start.sh
```

#### 认证问题
```bash
# 检查JWT配置
cat configs/config.yaml | grep -A 5 jwt

# 检查密钥设置
echo $JWT_SECRET_KEY

# 手动测试登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"Test123456"}'
```

#### 数据库问题
```bash
# 检查数据库连接
mysql -h localhost -u root -p -e "SHOW DATABASES;"

# 检查表结构
mysql -h localhost -u root -p your_db -e "SHOW TABLES;"

# 重新迁移数据库
./scripts/migrate.sh
```

#### 缓存问题
```bash
# 检查Redis状态
redis-cli ping
redis-cli info

# 清理缓存
redis-cli flushall

# 检查缓存配置
cat configs/config.yaml | grep -A 5 redis
```

## 🎯 最佳实践

### 开发阶段
```bash
# 每次代码变更后运行
./scripts/tests/test_api.sh

# 重要功能开发后运行
./scripts/tests/test_enhanced_api.sh
```

### 部署前验证
```bash
# 完整测试套件
./scripts/tests/run_all_tests.sh

# 性能基准测试
./scripts/tests/test_enhanced_api.sh --benchmark
```

### CI/CD集成
```yaml
# GitHub Actions 示例
- name: Run API Tests
  run: |
    ./scripts/start.sh
    sleep 10
    ./scripts/tests/run_all_tests.sh
    ./scripts/stop.sh
```

### 测试数据管理
```bash
# 测试前清理数据
./scripts/clean-test-data.sh

# 测试后恢复数据
./scripts/restore-test-data.sh
```

---

**让测试成为开发的安全网！** 🛡️
