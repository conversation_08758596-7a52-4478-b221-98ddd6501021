#!/bin/bash

# 健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${CYAN}🏥 $1${NC}"
}

# 进入api目录
cd "$(dirname "$0")/.."

# 健康检查单个服务
check_service_health() {
    local service_name=$1
    local port=$2
    local health_endpoint=$3
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "$service_name 健康检查"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local pid_file="logs/${service_name}.pid"
    local overall_status="healthy"
    
    # 1. 检查进程状态
    echo -n "🔍 进程检查: "
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            echo -e "${GREEN}运行中 (PID: $pid)${NC}"
        else
            echo -e "${RED}进程不存在${NC}"
            overall_status="unhealthy"
        fi
    else
        echo -e "${RED}PID文件不存在${NC}"
        overall_status="unhealthy"
    fi
    
    # 2. 检查端口监听
    echo -n "🌐 端口检查: "
    if lsof -i :$port >/dev/null 2>&1; then
        echo -e "${GREEN}端口 $port 正在监听${NC}"
    else
        echo -e "${RED}端口 $port 未监听${NC}"
        overall_status="unhealthy"
    fi
    
    # 3. HTTP健康检查
    echo -n "🏥 HTTP检查: "
    local health_url="http://localhost:$port$health_endpoint"
    local response_time=""
    local http_status=""
    
    # 使用curl进行健康检查，记录响应时间
    if command -v curl >/dev/null 2>&1; then
        local curl_output=$(curl -s -w "\n%{http_code}:%{time_total}" --max-time 10 "$health_url" 2>/dev/null || echo -e "\n000:0")
        http_status=$(echo "$curl_output" | tail -1 | cut -d':' -f1)
        response_time=$(echo "$curl_output" | tail -1 | cut -d':' -f2)
        
        if [ "$http_status" = "200" ]; then
            echo -e "${GREEN}正常 (${response_time}s)${NC}"
        else
            echo -e "${RED}失败 (HTTP $http_status)${NC}"
            overall_status="unhealthy"
        fi
    else
        echo -e "${YELLOW}curl未安装，跳过HTTP检查${NC}"
    fi
    
    # 4. 内存使用检查
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            echo -n "💾 内存检查: "
            local memory_usage=$(ps -p $pid -o pmem | tail -1 | xargs 2>/dev/null || echo "0")
            if [ -n "$memory_usage" ] && [ "$memory_usage" != "0" ]; then
                local memory_mb=$(ps -p $pid -o rss | tail -1 | xargs 2>/dev/null || echo "0")
                memory_mb=$((memory_mb / 1024))

                # 使用整数比较而不是bc
                local mem_int=$(echo "$memory_usage" | cut -d'.' -f1)
                if [ "$mem_int" -gt 80 ]; then
                    echo -e "${RED}内存使用过高: ${memory_usage}% (${memory_mb}MB)${NC}"
                    overall_status="warning"
                elif [ "$mem_int" -gt 50 ]; then
                    echo -e "${YELLOW}内存使用较高: ${memory_usage}% (${memory_mb}MB)${NC}"
                else
                    echo -e "${GREEN}内存使用正常: ${memory_usage}% (${memory_mb}MB)${NC}"
                fi
            else
                echo -e "${YELLOW}无法获取内存信息${NC}"
            fi
        fi
    fi
    
    # 5. 日志错误检查
    echo -n "📋 日志检查: "
    local log_file="logs/${service_name}.log"
    if [ -f "$log_file" ]; then
        # 检查最近5分钟的错误日志
        local recent_errors=$(find "$log_file" -mmin -5 -exec grep -c -i "error\|fatal\|panic" {} \; 2>/dev/null || echo "0")
        # 确保recent_errors是数字
        if [ -z "$recent_errors" ] || ! [[ "$recent_errors" =~ ^[0-9]+$ ]]; then
            recent_errors=0
        fi

        if [ "$recent_errors" -gt 10 ]; then
            echo -e "${RED}最近5分钟有 $recent_errors 条错误${NC}"
            overall_status="warning"
        elif [ "$recent_errors" -gt 0 ]; then
            echo -e "${YELLOW}最近5分钟有 $recent_errors 条错误${NC}"
        else
            echo -e "${GREEN}无最近错误${NC}"
        fi
    else
        echo -e "${YELLOW}日志文件不存在${NC}"
    fi
    
    # 6. 数据库连接检查（如果有健康检查端点）
    if [ "$http_status" = "200" ]; then
        echo -n "🗄️  数据库检查: "
        # 尝试从健康检查响应中获取数据库状态
        local health_response=$(curl -s --max-time 5 "$health_url" 2>/dev/null || echo "{}")
        if echo "$health_response" | grep -q '"database":{"status":"healthy"' 2>/dev/null; then
            echo -e "${GREEN}数据库连接正常${NC}"
        elif echo "$health_response" | grep -q '"database":{"status":"unhealthy"' 2>/dev/null; then
            echo -e "${RED}数据库连接异常${NC}"
            overall_status="unhealthy"
        elif echo "$health_response" | grep -q '"database"' 2>/dev/null; then
            echo -e "${YELLOW}数据库状态未知${NC}"
        else
            echo -e "${YELLOW}无数据库检查信息${NC}"
        fi
    fi
    
    # 显示总体状态
    echo ""
    echo -n "🎯 总体状态: "
    case $overall_status in
        "healthy")
            echo -e "${GREEN}健康${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}警告${NC}"
            ;;
        "unhealthy")
            echo -e "${RED}不健康${NC}"
            ;;
    esac
    
    echo ""
    return $([ "$overall_status" = "healthy" ] && echo 0 || echo 1)
}

# 显示系统健康概览
show_system_health() {
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "系统健康概览"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 系统负载
    echo -n "📈 系统负载: "
    if command -v uptime >/dev/null 2>&1; then
        local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
        if (( $(echo "$load_avg > 2.0" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${RED}高负载: $load_avg${NC}"
        elif (( $(echo "$load_avg > 1.0" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${YELLOW}中等负载: $load_avg${NC}"
        else
            echo -e "${GREEN}正常: $load_avg${NC}"
        fi
    else
        echo -e "${YELLOW}无法获取${NC}"
    fi
    
    # 磁盘空间
    echo -n "💿 磁盘空间: "
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | tr -d '%')
    if [ "$disk_usage" -gt 90 ]; then
        echo -e "${RED}空间不足: ${disk_usage}%${NC}"
    elif [ "$disk_usage" -gt 80 ]; then
        echo -e "${YELLOW}空间紧张: ${disk_usage}%${NC}"
    else
        echo -e "${GREEN}空间充足: ${disk_usage}%${NC}"
    fi
    
    # 内存使用
    echo -n "💾 系统内存: "
    if command -v free >/dev/null 2>&1; then
        local memory_usage=$(free | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100.0}')
        if (( $(echo "$memory_usage > 90" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${RED}内存不足: ${memory_usage}%${NC}"
        elif (( $(echo "$memory_usage > 80" | bc -l 2>/dev/null || echo "0") )); then
            echo -e "${YELLOW}内存紧张: ${memory_usage}%${NC}"
        else
            echo -e "${GREEN}内存正常: ${memory_usage}%${NC}"
        fi
    else
        echo -e "${YELLOW}无法获取${NC}"
    fi
    
    echo ""
}

# 主函数
main() {
    local target_service=${1:-all}
    
    log_header "服务健康检查 (SERVICE=$target_service)"
    echo ""
    
    local exit_code=0
    
    case $target_service in
        "api")
            check_service_health "api-server" "8080" "/api/v1/health"
            exit_code=$?
            ;;
        "admin")
            check_service_health "admin-server" "8081" "/admin/v1/health"
            exit_code=$?
            ;;
        "all")
            show_system_health
            
            check_service_health "api-server" "8080" "/api/v1/health"
            local api_status=$?
            
            check_service_health "admin-server" "8081" "/admin/v1/health"
            local admin_status=$?
            
            exit_code=$((api_status + admin_status))
            ;;
        *)
            log_error "无效的服务名称: $target_service"
            log_info "有效的服务名称: api, admin, all"
            exit 1
            ;;
    esac
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    if [ $exit_code -eq 0 ]; then
        log_success "所有检查项目通过"
    else
        log_warning "发现 $exit_code 个问题，请检查上述输出"
    fi
    
    log_info "💡 更多操作:"
    log_info "   - 查看状态: make status [SERVICE=<service>]"
    log_info "   - 查看日志: make logs SERVICE=<service>"
    log_info "   - 重启服务: make restart [SERVICE=<service>]"
    
    exit $exit_code
}

# 执行主函数
main "$@"
