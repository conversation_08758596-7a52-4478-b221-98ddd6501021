#!/bin/bash

# API接口测试脚本
# 用于快速测试微信登录和用户相关接口

set -e

# 配置
API_BASE_URL="http://localhost:8080/api/v1"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查API服务状态..."
    if curl -s "${API_BASE_URL}/health" > /dev/null 2>&1; then
        log_success "API服务正在运行"
    else
        log_error "API服务未运行，请先启动服务"
        exit 1
    fi
}

# 测试微信登录接口
test_wechat_login() {
    log_info "测试微信登录接口..."
    
    # 测试数据
    local test_data='{
        "code": "test_code_123",
        "encrypted_data": "test_encrypted_data",
        "iv": "test_iv"
    }'
    
    local response=$(curl -s -X POST \
        -H "$CONTENT_TYPE" \
        -d "$test_data" \
        "${API_BASE_URL}/auth/wechat/login")
    
    echo "Response: $response"
    
    # 检查响应
    if echo "$response" | grep -q '"code":0'; then
        log_success "微信登录接口测试通过"
        # 提取access_token
        ACCESS_TOKEN=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        log_info "获取到access_token: ${ACCESS_TOKEN:0:20}..."
    else
        log_warning "微信登录接口返回错误（这在测试环境中是正常的）"
    fi
}

# 测试获取用户信息接口
test_get_user_info() {
    if [ -z "$ACCESS_TOKEN" ]; then
        log_warning "跳过用户信息测试（没有有效的access_token）"
        return
    fi
    
    log_info "测试获取用户信息接口..."
    
    local response=$(curl -s -X GET \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        "${API_BASE_URL}/user/info")
    
    echo "Response: $response"
    
    if echo "$response" | grep -q '"code":0'; then
        log_success "获取用户信息接口测试通过"
    else
        log_warning "获取用户信息接口返回错误"
    fi
}

# 测试更新用户信息接口
test_update_user_info() {
    if [ -z "$ACCESS_TOKEN" ]; then
        log_warning "跳过用户信息更新测试（没有有效的access_token）"
        return
    fi
    
    log_info "测试更新用户信息接口..."
    
    local test_data='{
        "nickname": "测试用户更新",
        "avatar": "https://test.avatar.url/new.png",
        "phone": "13800138000",
        "gender": 1,
        "province": "广东",
        "city": "深圳"
    }'
    
    local response=$(curl -s -X PUT \
        -H "$CONTENT_TYPE" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "$test_data" \
        "${API_BASE_URL}/user/info")
    
    echo "Response: $response"
    
    if echo "$response" | grep -q '"code":0'; then
        log_success "更新用户信息接口测试通过"
    else
        log_warning "更新用户信息接口返回错误"
    fi
}

# 测试无效请求
test_invalid_requests() {
    log_info "测试无效请求处理..."
    
    # 测试缺少code的微信登录
    local invalid_data='{
        "encrypted_data": "test_encrypted_data",
        "iv": "test_iv"
    }'
    
    local response=$(curl -s -X POST \
        -H "$CONTENT_TYPE" \
        -d "$invalid_data" \
        "${API_BASE_URL}/auth/wechat/login")
    
    echo "Invalid request response: $response"
    
    if echo "$response" | grep -q '"code":400\|"code":1001'; then
        log_success "无效请求处理正确"
    else
        log_warning "无效请求处理可能有问题"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "           API接口测试工具"
    echo "=========================================="
    
    check_service
    echo
    
    test_wechat_login
    echo
    
    test_get_user_info
    echo
    
    test_update_user_info
    echo
    
    test_invalid_requests
    echo
    
    log_info "测试完成！"
    echo "=========================================="
}

# 运行主函数
main "$@"
