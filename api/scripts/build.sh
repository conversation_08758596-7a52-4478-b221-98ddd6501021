#!/bin/bash

# 构建脚本

set -e

echo "🔨 开始构建Go API框架..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go"
    exit 1
fi

# 进入api目录
cd "$(dirname "$0")/.."

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf bin/

# 创建构建目录
mkdir -p bin/

# 构建API服务器
echo "🔧 构建API服务器..."
go build -o bin/api-server ./cmd/api-server

# 构建Admin服务器
echo "🔧 构建Admin服务器..."
go build -o bin/admin-server ./cmd/admin-server

# 检查构建结果
if [ -f "bin/api-server" ] && [ -f "bin/admin-server" ]; then
    echo "✅ 构建成功！"
    echo "📁 构建文件位置："
    echo "   - API服务器: bin/api-server"
    echo "   - Admin服务器: bin/admin-server"
else
    echo "❌ 构建失败！"
    exit 1
fi

echo "🎉 构建完成！"
