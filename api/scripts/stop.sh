#!/bin/bash

# 停止脚本

set -e

echo "🛑 停止Go API框架..."

# 进入api目录
cd "$(dirname "$0")/.."

# 停止服务的函数
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if kill -0 $pid 2>/dev/null; then
            echo "🛑 停止 $service_name (PID: $pid)..."
            kill $pid
            
            # 等待进程结束
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍然存在，强制杀死
            if kill -0 $pid 2>/dev/null; then
                echo "⚠️  强制停止 $service_name..."
                kill -9 $pid
            fi
            
            echo "✅ $service_name 已停止"
        else
            echo "⚠️  $service_name 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        echo "⚠️  $service_name PID文件不存在"
    fi
}

# 停止API服务器
stop_service "api-server"

# 停止Admin服务器
stop_service "admin-server"

echo ""
echo "✅ 所有服务已停止！"
