# 服务器配置
server:
  mode: debug # debug, release
  api:
    host: "0.0.0.0"
    port: "8080"
  admin:
    host: "0.0.0.0"
    port: "8081"

# 数据库配置
database:
  driver: mysql # mysql, postgres
  host: *************
  port: 3306
  username: admin
  password: "235lwx123456"
  database: hop_planet_dev
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

  # 读写分离配置（可选）
  read_host: "" # 读库地址，为空则使用主库
  read_port: 3306 # 读库端口
  read_username: "" # 读库用户名，为空则使用主库配置
  read_password: "" # 读库密码，为空则使用主库配置

  # 性能优化配置
  slow_query_threshold: 1000 # 慢查询阈值（毫秒）
  enable_query_log: false # 是否启用查询日志
  log_level: 1 # 日志级别：1-Silent, 2-<PERSON><PERSON><PERSON>, 3-War<PERSON>, 4-Info

# Redis配置 (可选)
redis:
  enabled: false
  host: *************
  port: 6379
  password: "235lwx123!@#"
  database: 6

# MongoDB配置 (可选)
mongodb:
  enabled: false # 是否启用MongoDB
  # 方式1：使用URI连接（推荐）
  uri: "**************************************************************************"

  # 方式2：使用分离的配置参数
  host: localhost
  port: 27017
  username: ""
  password: ""
  database: kids_platform
  auth_source: admin

  # 连接池配置
  max_pool_size: 100 # 最大连接数
  min_pool_size: 5 # 最小连接数
  max_idle_time_ms: 300000 # 连接最大空闲时间（毫秒）
  connect_timeout_ms: 10000 # 连接超时时间（毫秒）

  # SSL配置
  ssl:
    enabled: false
    certificate_key_file: ""
    ca_file: ""
    insecure_skip_verify: false

# JWT配置
jwt:
  secret: "kids-platform-jwt-secret-key-2025-production-ready"
  expire_hours: 24
  refresh_expire_hours: 168 # 7天
  issuer: "kids-platform"

# 日志配置
log:
  level: debug # debug, info, warn, error
  format: json # json, text
  output: stdout # stdout, file
  file_path: logs/app.log

# 文件上传配置
upload:
  max_size: 10485760 # 10MB
  allowed_types:
    - image/jpeg
    - image/png
    - image/gif
  upload_path: uploads/

# 微信小程序配置
wechat:
  miniprogram:
    app_id: "wxef69f6054571da52" # 微信小程序AppID
    app_secret: "7c496731f731c9b9a13de7afd127f019" # 微信小程序AppSecret
    # 开发环境Mock配置
    mock_enabled: false # 开发环境启用Mock，生产环境设为false
    mock_openid: "mock_openid_for_dev_testing" # Mock用的openid
    mock_session_key: "mock_session_key_for_dev" # Mock用的session_key
    mock_unionid: "mock_unionid_for_dev_testing" # Mock用的unionid
    # API配置
    auth_url: "https://api.weixin.qq.com/sns/jscode2session" # 微信登录凭证校验接口
    timeout: 5000 # 请求超时时间（毫秒）

# 安全配置
security:
  cors_allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
  rate_limit_requests: 100
  rate_limit_window: 60
  production_mode: false
  debug_mode: true
  secure_cookies: false
