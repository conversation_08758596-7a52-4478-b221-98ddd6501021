# 测试环境配置文件
# 服务器配置
server:
  mode: test # debug, release, test
  api:
    host: "localhost"
    port: "8080"
  admin:
    host: "localhost"
    port: "8081"

# 数据库配置
database:
  driver: mysql # mysql, postgres
  host: *************
  port: 3306
  username: admin
  password: "235lwx123456"
  database: hop_planet_dev
  charset: utf8mb4
  max_idle_conns: 5
  max_open_conns: 10
  conn_max_lifetime: 300

  # 读写分离配置（可选）
  read_host: "" # 读库地址，为空则使用主库
  read_port: 3306 # 读库端口
  read_username: "" # 读库用户名，为空则使用主库配置
  read_password: "" # 读库密码，为空则使用主库配置

  # 性能优化配置
  enable_query_log: true
  log_level: 4

# Redis配置
redis:
  enabled: false # 测试环境禁用Redis，使用mock
  host: *************
  port: 6379
  password: "235lwx123!@#"
  database: 7

# MongoDB配置
mongodb:
  enabled: false # 测试环境禁用MongoDB，使用mock
  host: localhost
  port: 27017
  username: ""
  password: ""
  database: kids_platform_test
  auth_source: admin
  max_pool_size: 10
  min_pool_size: 1
  max_idle_time_ms: 60000
  connect_timeout_ms: 5000
  ssl:
    enabled: false

# JWT配置
jwt:
  secret: "test_jwt_secret_key_for_testing_only_do_not_use_in_production"
  expire_hours: 1

# 日志配置
log:
  level: "debug"
  format: "text"
  output: "stdout"
  file_path: ""

# 文件上传配置
upload:
  max_size: 10485760 # 10MB
  allowed_types: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"]
  upload_path: "./uploads/test"

# 安全配置
security:
  cors_allowed_origins: ["http://localhost:3000", "http://localhost:8080"]
  rate_limit_requests: 1000 # 测试环境放宽限制
  rate_limit_window: 60
  production_mode: false
  debug_mode: true
