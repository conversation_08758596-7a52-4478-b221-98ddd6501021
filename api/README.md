# 🚀 跳跳星球

## 📋 项目概述

这是一个专为儿童跳绳学习设计的后端平台，采用清晰的分层架构和API/Admin双服务设计。平台内置了完整的开发工具链，包括代码生成器、构建脚本和部署工具。

## 🏗️ 架构设计

### 目录结构
```
api/
├── cmd/                     # 应用入口
│   ├── api-server/         # API服务入口
│   └── admin-server/       # 管理后台入口
├── internal/               # 内部代码
│   ├── handlers/           # HTTP处理器
│   │   ├── api/           # API处理器
│   │   └── admin/         # 管理后台处理器
│   ├── services/          # 业务逻辑层
│   │   ├── api/           # API业务逻辑
│   │   ├── admin/         # 管理后台业务逻辑
│   │   └── shared/        # 共享业务逻辑
│   ├── repositories/      # 数据访问层
│   └── models/           # 数据模型
├── pkg/                   # 可复用包
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接
│   ├── cache/            # 缓存管理
│   ├── middleware/       # 中间件
│   ├── logger/           # 日志管理
│   ├── response/         # 响应格式
│   └── errors/           # 错误处理
├── configs/              # 配置文件
├── database/            # 数据库相关
│   └── migrations/      # 数据库迁移
├── docs/                # API技术文档
│   └── docs.go          # Swagger文档
├── scripts/             # 脚本工具
│   ├── tests/           # 测试脚本
│   ├── build.sh         # 构建脚本
│   ├── start.sh         # 启动脚本
│   └── stop.sh          # 停止脚本
├── tools/               # 开发工具
│   └── generators/      # 代码生成器
├── bin/                 # 编译输出
└── logs/               # 日志文件
```

### 分层架构
- **Handler层**：处理HTTP请求，参数验证，调用Service层
- **Service层**：业务逻辑处理，事务管理，调用Repository层
- **Repository层**：数据访问，数据库操作，错误处理
- **Model层**：数据模型定义，请求/响应结构

## 🚀 快速开始

### 1. 环境要求
- Go 1.23.10+
- MySQL 8.0+ / PostgreSQL 13+
- Redis 6.0+ (可选)

### 2. 初始化项目
```bash
# 安装依赖
make deps

# 复制配置文件
cp configs/config.example.yaml configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

### 3. 启动服务
```bash
# 构建项目
make build

# 启动服务
make start

# 或者使用脚本
./scripts/start.sh
```

### 4. 验证服务
```bash
# 检查API服务
curl http://localhost:8080/api/v1/health

# 检查Admin服务
curl http://localhost:8081/admin/v1/health
```

## 🛠️ 代码生成器

### 生成基础CRUD模块
```bash
# 生成API模块（默认）
./tools/generators/generate.sh product api

# 生成管理后台模块
./tools/generators/generate.sh user_management admin

# 生成共享模块
./tools/generators/generate.sh notification shared
```

### 生成的文件
- `internal/models/{module}.go` - 数据模型（仅API类型）
- `internal/repositories/{module}_repository.go` - 数据访问层（仅API类型）
- `internal/services/{type}/{module}_service.go` - 业务逻辑层
- `internal/handlers/{type}/{module}_handler.go` - HTTP处理器

### 模块生成器功能
- **支持三种服务类型**：api、admin、shared
- **自动生成完整CRUD**：创建、读取、更新、删除
- **内置MVS规则检查**：确保符合开发规范
- **智能命名转换**：支持下划线到驼峰命名
- **路由自动注册**：生成路由注册代码

## 📝 开发指南

### 1. 添加新模块
1. 使用代码生成器生成基础文件
2. 根据业务需求修改模型字段
3. 实现Service层业务逻辑
4. 实现Handler层HTTP接口
5. 添加数据库迁移文件

### 2. 配置管理
配置文件位于 `configs/config.yaml`，支持以下配置：
- 服务器配置（端口、模式）
- 数据库配置（MySQL/PostgreSQL）
- Redis配置（可选）
- JWT配置
- 日志配置
- 文件上传配置

### 3. 错误处理
框架提供统一的错误处理机制：
```go
// 业务错误
return errors.NewBusinessError(errors.ErrCodeNotFound, "用户不存在")

// 响应错误
response.Error(c, errors.ErrCodeNotFound, "用户不存在")
```

### 4. 响应格式
所有API响应使用统一格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {...}
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
make test

# 运行API功能测试
./scripts/tests/test_api.sh

# 运行增强功能测试
./scripts/tests/test_enhanced_api.sh

# 运行特定包的测试
go test ./internal/services/... -v

# 运行测试并查看覆盖率
go test ./... -cover
```

### 测试脚本说明
- `scripts/tests/test_api.sh` - 基础API功能测试
- `scripts/tests/test_enhanced_api.sh` - 增强功能测试（JWT、缓存等）
- `scripts/tests/README.md` - 测试脚本详细说明

## 📊 监控和日志

### 日志管理
- 日志文件位于 `logs/` 目录
- 支持JSON和文本格式
- 可配置日志级别（debug/info/warn/error）

### 服务监控
- API服务：http://localhost:8080/api/v1/health
- Admin服务：http://localhost:8081/admin/v1/health

## 🚀 部署

### 构建
```bash
# 构建二进制文件
make build

# 构建文件位于 bin/ 目录
ls bin/
```

### 启动和停止
```bash
# 启动服务
./scripts/start.sh

# 停止服务
./scripts/stop.sh
```

## 📚 API文档

### API服务端点
- `GET /api/v1/health` - 健康检查
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户

### Admin服务端点
- `GET /admin/v1/health` - 健康检查
- `POST /admin/v1/auth/login` - 管理员登录
- `GET /admin/v1/users` - 用户管理
- `GET /admin/v1/system/stats` - 系统统计

## 🔧 常用命令

```bash
# 开发相关
make deps      # 安装依赖
make fmt       # 格式化代码
make vet       # 代码检查
make test      # 运行测试

# 构建和部署
make build     # 构建项目
make start     # 启动服务
make stop      # 停止服务
make clean     # 清理构建文件

# 代码生成
./tools/generators/generate.sh <模块名> [服务类型]

# 测试相关
./scripts/tests/test_api.sh              # API功能测试
./scripts/tests/test_enhanced_api.sh     # 增强功能测试
```

## 🎯 最佳实践

1. **严格遵守分层架构**：Handler -> Service -> Repository
2. **使用统一错误处理**：业务错误使用BusinessError
3. **参数验证**：在Handler层进行参数验证
4. **事务管理**：在Service层处理事务
5. **日志记录**：关键操作记录日志
6. **测试覆盖**：每个模块都要有测试
7. **遵循MVS规则**：确保需求、模板、测试、检查清单、知识库、架构变更管理
8. **使用代码生成器**：提高开发效率，保证代码一致性

## 🔧 开发工具

### 代码生成器
- **位置**: `tools/generators/`
- **功能**: 自动生成CRUD模块代码
- **支持**: API、Admin、Shared三种服务类型

### 脚本工具
- **位置**: `scripts/`
- **包含**: 构建、启动、停止、测试脚本
- **测试脚本**: `scripts/tests/` 目录下的各种测试脚本

### 项目文档
- **项目级文档**: `../docs/` 目录
- **API技术文档**: `docs/` 目录

### 📋 项目标准文档 ⭐ **重要**
- **标准文档概览**: [`../docs/standards/README.md`](../docs/standards/README.md)
- **协作规范**: [`../docs/standards/collaboration/`](../docs/standards/collaboration/)
  - [协作指南](../docs/standards/collaboration/collaboration-guidelines.md) - AI与用户协作规范
  - [工作流程标准](../docs/standards/collaboration/workflow-standards.md) - 项目开发流程
- **开发标准**: [`../docs/standards/development/`](../docs/standards/development/)
  - [开发标准框架](../docs/standards/development/development-standards-framework.md) - 开发规范基础
  - [需求验证机制](../docs/standards/development/requirements-validation.md) - 需求确认方法

### 🧠 核心知识库
- **MVS核心规则**: [`../docs/knowledge/mvs-core-rules.md`](../docs/knowledge/mvs-core-rules.md)
- **AI协作指南**: [`../docs/knowledge/ai-collaboration-guide.md`](../docs/knowledge/ai-collaboration-guide.md)

### 📋 项目规范
- **目录结构规范**: [`../docs/PROJECT_DIRECTORY_STRUCTURE.md`](../docs/PROJECT_DIRECTORY_STRUCTURE.md)

## 🚀 生产特性

### 中间件系统
- **JWT认证**: API和Admin双重认证
- **请求日志**: 详细的请求响应日志
- **错误处理**: 统一的错误处理和响应
- **CORS支持**: 跨域请求支持
- **限流保护**: API访问频率限制

### 缓存系统
- **Redis集成**: 支持数据缓存和会话存储
- **缓存策略**: 可配置的缓存过期时间
- **缓存键管理**: 统一的缓存键命名规范

### 监控和健康检查
- **健康检查端点**: `/health` 接口
- **服务状态监控**: 数据库、Redis连接状态
- **性能指标**: 响应时间、错误率统计
- **优雅关闭**: 支持优雅关闭和重启

---

**让我们一起构建高质量的Go API服务！** 🎯
