package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// PointRecordsRepository 积分变动记录表：记录所有积分变化的详细历史仓储接口
type PointRecordsRepository interface {
	Create(pointRecords *models.PointRecords) error
	GetByID(id uint) (*models.PointRecords, error)
	Update(id uint, pointRecords *models.PointRecords) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.PointRecords, int64, error)
	// 根据孩子ID获取积分记录列表
	GetByChildID(childID uint, offset, limit int) ([]*models.PointRecords, int64, error)
}

// pointRecordsRepository 积分变动记录表：记录所有积分变化的详细历史仓储实现
type pointRecordsRepository struct {
	db *gorm.DB
}

// NewPointRecordsRepository 创建积分变动记录表：记录所有积分变化的详细历史仓储
func NewPointRecordsRepository(db *gorm.DB) PointRecordsRepository {
	return &pointRecordsRepository{
		db: db,
	}
}

// Create 创建积分变动记录表：记录所有积分变化的详细历史
func (r *pointRecordsRepository) Create(pointRecords *models.PointRecords) error {
	if err := r.db.Create(pointRecords).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建积分变动记录表：记录所有积分变化的详细历史失败")
	}
	return nil
}

// GetByID 根据ID获取积分变动记录表：记录所有积分变化的详细历史
func (r *pointRecordsRepository) GetByID(id uint) (*models.PointRecords, error) {
	var pointRecords models.PointRecords
	if err := r.db.First(&pointRecords, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("积分变动记录表：记录所有积分变化的详细历史不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询积分变动记录表：记录所有积分变化的详细历史失败")
	}
	return &pointRecords, nil
}

// Update 更新积分变动记录表：记录所有积分变化的详细历史
func (r *pointRecordsRepository) Update(id uint, pointRecords *models.PointRecords) error {
	if err := r.db.Model(&models.PointRecords{}).Where("id = ?", id).Updates(pointRecords).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新积分变动记录表：记录所有积分变化的详细历史失败")
	}
	return nil
}

// Delete 删除积分变动记录表：记录所有积分变化的详细历史
func (r *pointRecordsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.PointRecords{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除积分变动记录表：记录所有积分变化的详细历史失败")
	}
	return nil
}

// List 获取积分变动记录表：记录所有积分变化的详细历史列表
func (r *pointRecordsRepository) List(offset, limit int) ([]*models.PointRecords, int64, error) {
	var pointRecordss []*models.PointRecords
	var total int64

	// 获取总数
	if err := r.db.Model(&models.PointRecords{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分变动记录表：记录所有积分变化的详细历史总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&pointRecordss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分变动记录表：记录所有积分变化的详细历史列表失败")
	}

	return pointRecordss, total, nil
}

// GetByChildID 根据孩子ID获取积分记录列表
func (r *pointRecordsRepository) GetByChildID(childID uint, offset, limit int) ([]*models.PointRecords, int64, error) {
	var pointRecords []*models.PointRecords
	var total int64

	// 构建查询条件
	query := r.db.Model(&models.PointRecords{}).Where("child_id = ?", childID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录总数失败")
	}

	// 获取列表，按创建时间倒序
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&pointRecords).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录列表失败")
	}

	return pointRecords, total, nil
}
