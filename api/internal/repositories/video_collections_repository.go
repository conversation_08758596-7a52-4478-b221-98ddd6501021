package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// VideoCollectionsRepository 视频集合表：管理视频的集合，支持灵活的付费模式仓储接口
type VideoCollectionsRepository interface {
	Create(videoCollections *models.VideoCollections) error
	GetByID(id uint) (*models.VideoCollections, error)
	Update(id uint, videoCollections *models.VideoCollections) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.VideoCollections, int64, error)
}

// videoCollectionsRepository 视频集合表：管理视频的集合，支持灵活的付费模式仓储实现
type videoCollectionsRepository struct {
	db *gorm.DB
}

// NewVideoCollectionsRepository 创建视频集合表：管理视频的集合，支持灵活的付费模式仓储
func NewVideoCollectionsRepository(db *gorm.DB) VideoCollectionsRepository {
	return &videoCollectionsRepository{
		db: db,
	}
}

// Create 创建视频集合表：管理视频的集合，支持灵活的付费模式
func (r *videoCollectionsRepository) Create(videoCollections *models.VideoCollections) error {
	if err := r.db.Create(videoCollections).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建视频集合表：管理视频的集合，支持灵活的付费模式失败")
	}
	return nil
}

// GetByID 根据ID获取视频集合表：管理视频的集合，支持灵活的付费模式
func (r *videoCollectionsRepository) GetByID(id uint) (*models.VideoCollections, error) {
	var videoCollections models.VideoCollections
	if err := r.db.First(&videoCollections, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("视频集合表：管理视频的集合，支持灵活的付费模式不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询视频集合表：管理视频的集合，支持灵活的付费模式失败")
	}
	return &videoCollections, nil
}

// Update 更新视频集合表：管理视频的集合，支持灵活的付费模式
func (r *videoCollectionsRepository) Update(id uint, videoCollections *models.VideoCollections) error {
	if err := r.db.Model(&models.VideoCollections{}).Where("id = ?", id).Updates(videoCollections).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新视频集合表：管理视频的集合，支持灵活的付费模式失败")
	}
	return nil
}

// Delete 删除视频集合表：管理视频的集合，支持灵活的付费模式
func (r *videoCollectionsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.VideoCollections{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除视频集合表：管理视频的集合，支持灵活的付费模式失败")
	}
	return nil
}

// List 获取视频集合表：管理视频的集合，支持灵活的付费模式列表
func (r *videoCollectionsRepository) List(offset, limit int) ([]*models.VideoCollections, int64, error) {
	var videoCollectionss []*models.VideoCollections
	var total int64

	// 获取总数
	if err := r.db.Model(&models.VideoCollections{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频集合表：管理视频的集合，支持灵活的付费模式总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&videoCollectionss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频集合表：管理视频的集合，支持灵活的付费模式列表失败")
	}

	return videoCollectionss, total, nil
}
