package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// LikesRepository 点赞表：记录用户的点赞行为仓储接口
type LikesRepository interface {
	Create(likes *models.Likes) error
	GetByID(id uint) (*models.Likes, error)
	Update(id uint, likes *models.Likes) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Likes, int64, error)
}

// likesRepository 点赞表：记录用户的点赞行为仓储实现
type likesRepository struct {
	db *gorm.DB
}

// NewLikesRepository 创建点赞表：记录用户的点赞行为仓储
func NewLikesRepository(db *gorm.DB) LikesRepository {
	return &likesRepository{
		db: db,
	}
}

// Create 创建点赞表：记录用户的点赞行为
func (r *likesRepository) Create(likes *models.Likes) error {
	if err := r.db.Create(likes).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建点赞表：记录用户的点赞行为失败")
	}
	return nil
}

// GetByID 根据ID获取点赞表：记录用户的点赞行为
func (r *likesRepository) GetByID(id uint) (*models.Likes, error) {
	var likes models.Likes
	if err := r.db.First(&likes, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("点赞表：记录用户的点赞行为不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询点赞表：记录用户的点赞行为失败")
	}
	return &likes, nil
}

// Update 更新点赞表：记录用户的点赞行为
func (r *likesRepository) Update(id uint, likes *models.Likes) error {
	if err := r.db.Model(&models.Likes{}).Where("id = ?", id).Updates(likes).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新点赞表：记录用户的点赞行为失败")
	}
	return nil
}

// Delete 删除点赞表：记录用户的点赞行为
func (r *likesRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.Likes{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除点赞表：记录用户的点赞行为失败")
	}
	return nil
}

// List 获取点赞表：记录用户的点赞行为列表
func (r *likesRepository) List(offset, limit int) ([]*models.Likes, int64, error) {
	var likess []*models.Likes
	var total int64

	// 获取总数
	if err := r.db.Model(&models.Likes{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询点赞表：记录用户的点赞行为总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&likess).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询点赞表：记录用户的点赞行为列表失败")
	}

	return likess, total, nil
}
