package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// CommentsRepository 评论表：记录用户的评论信息仓储接口
type CommentsRepository interface {
	Create(comments *models.Comments) error
	GetByID(id uint) (*models.Comments, error)
	Update(id uint, comments *models.Comments) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Comments, int64, error)
}

// commentsRepository 评论表：记录用户的评论信息仓储实现
type commentsRepository struct {
	db *gorm.DB
}

// NewCommentsRepository 创建评论表：记录用户的评论信息仓储
func NewCommentsRepository(db *gorm.DB) CommentsRepository {
	return &commentsRepository{
		db: db,
	}
}

// Create 创建评论表：记录用户的评论信息
func (r *commentsRepository) Create(comments *models.Comments) error {
	if err := r.db.Create(comments).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建评论表：记录用户的评论信息失败")
	}
	return nil
}

// GetByID 根据ID获取评论表：记录用户的评论信息
func (r *commentsRepository) GetByID(id uint) (*models.Comments, error) {
	var comments models.Comments
	if err := r.db.First(&comments, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("评论表：记录用户的评论信息不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询评论表：记录用户的评论信息失败")
	}
	return &comments, nil
}

// Update 更新评论表：记录用户的评论信息
func (r *commentsRepository) Update(id uint, comments *models.Comments) error {
	if err := r.db.Model(&models.Comments{}).Where("id = ?", id).Updates(comments).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新评论表：记录用户的评论信息失败")
	}
	return nil
}

// Delete 删除评论表：记录用户的评论信息
func (r *commentsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.Comments{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除评论表：记录用户的评论信息失败")
	}
	return nil
}

// List 获取评论表：记录用户的评论信息列表
func (r *commentsRepository) List(offset, limit int) ([]*models.Comments, int64, error) {
	var commentss []*models.Comments
	var total int64

	// 获取总数
	if err := r.db.Model(&models.Comments{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询评论表：记录用户的评论信息总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&commentss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询评论表：记录用户的评论信息列表失败")
	}

	return commentss, total, nil
}
