package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// MedalsRepository 勋章定义表：定义系统中所有可获得的勋章仓储接口
type MedalsRepository interface {
	Create(medals *models.Medals) error
	GetByID(id uint) (*models.Medals, error)
	Update(id uint, medals *models.Medals) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Medals, int64, error)
}

// medalsRepository 勋章定义表：定义系统中所有可获得的勋章仓储实现
type medalsRepository struct {
	db *gorm.DB
}

// NewMedalsRepository 创建勋章定义表：定义系统中所有可获得的勋章仓储
func NewMedalsRepository(db *gorm.DB) MedalsRepository {
	return &medalsRepository{
		db: db,
	}
}

// Create 创建勋章定义表：定义系统中所有可获得的勋章
func (r *medalsRepository) Create(medals *models.Medals) error {
	if err := r.db.Create(medals).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建勋章定义表：定义系统中所有可获得的勋章失败")
	}
	return nil
}

// GetByID 根据ID获取勋章定义表：定义系统中所有可获得的勋章
func (r *medalsRepository) GetByID(id uint) (*models.Medals, error) {
	var medals models.Medals
	if err := r.db.First(&medals, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("勋章定义表：定义系统中所有可获得的勋章不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询勋章定义表：定义系统中所有可获得的勋章失败")
	}
	return &medals, nil
}

// Update 更新勋章定义表：定义系统中所有可获得的勋章
func (r *medalsRepository) Update(id uint, medals *models.Medals) error {
	if err := r.db.Model(&models.Medals{}).Where("id = ?", id).Updates(medals).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新勋章定义表：定义系统中所有可获得的勋章失败")
	}
	return nil
}

// Delete 删除勋章定义表：定义系统中所有可获得的勋章
func (r *medalsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.Medals{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除勋章定义表：定义系统中所有可获得的勋章失败")
	}
	return nil
}

// List 获取勋章定义表：定义系统中所有可获得的勋章列表
func (r *medalsRepository) List(offset, limit int) ([]*models.Medals, int64, error) {
	var medalss []*models.Medals
	var total int64

	// 获取总数
	if err := r.db.Model(&models.Medals{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询勋章定义表：定义系统中所有可获得的勋章总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&medalss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询勋章定义表：定义系统中所有可获得的勋章列表失败")
	}

	return medalss, total, nil
}
