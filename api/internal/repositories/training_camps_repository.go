package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// TrainingCampsRepository 训练营主表：存储训练营的基本信息和配置仓储接口
type TrainingCampsRepository interface {
	Create(trainingCamps *models.TrainingCamps) error
	GetByID(id uint) (*models.TrainingCamps, error)
	Update(id uint, trainingCamps *models.TrainingCamps) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.TrainingCamps, int64, error)
}

// trainingCampsRepository 训练营主表：存储训练营的基本信息和配置仓储实现
type trainingCampsRepository struct {
	db *gorm.DB
}

// NewTrainingCampsRepository 创建训练营主表：存储训练营的基本信息和配置仓储
func NewTrainingCampsRepository(db *gorm.DB) TrainingCampsRepository {
	return &trainingCampsRepository{
		db: db,
	}
}

// Create 创建训练营主表：存储训练营的基本信息和配置
func (r *trainingCampsRepository) Create(trainingCamps *models.TrainingCamps) error {
	if err := r.db.Create(trainingCamps).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建训练营主表：存储训练营的基本信息和配置失败")
	}
	return nil
}

// GetByID 根据ID获取训练营主表：存储训练营的基本信息和配置
func (r *trainingCampsRepository) GetByID(id uint) (*models.TrainingCamps, error) {
	var trainingCamps models.TrainingCamps
	if err := r.db.First(&trainingCamps, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营主表：存储训练营的基本信息和配置不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询训练营主表：存储训练营的基本信息和配置失败")
	}
	return &trainingCamps, nil
}

// Update 更新训练营主表：存储训练营的基本信息和配置
func (r *trainingCampsRepository) Update(id uint, trainingCamps *models.TrainingCamps) error {
	if err := r.db.Model(&models.TrainingCamps{}).Where("id = ?", id).Updates(trainingCamps).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新训练营主表：存储训练营的基本信息和配置失败")
	}
	return nil
}

// Delete 删除训练营主表：存储训练营的基本信息和配置
func (r *trainingCampsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.TrainingCamps{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除训练营主表：存储训练营的基本信息和配置失败")
	}
	return nil
}

// List 获取训练营主表：存储训练营的基本信息和配置列表
func (r *trainingCampsRepository) List(offset, limit int) ([]*models.TrainingCamps, int64, error) {
	var trainingCampss []*models.TrainingCamps
	var total int64

	// 构建查询条件：只查询正常状态的训练营
	query := r.db.Model(&models.TrainingCamps{}).Where("status = ?", 1)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询训练营主表：存储训练营的基本信息和配置总数失败")
	}

	// 获取列表，按推荐和排序权重排序
	if err := query.Order("is_featured DESC, sort_order DESC, created_at DESC").
		Offset(offset).Limit(limit).Find(&trainingCampss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询训练营主表：存储训练营的基本信息和配置列表失败")
	}

	return trainingCampss, total, nil
}
