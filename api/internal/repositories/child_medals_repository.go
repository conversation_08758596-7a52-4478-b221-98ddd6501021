package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ChildMedalsRepository 孩子勋章记录表：记录每个孩子的勋章获得情况仓储接口
type ChildMedalsRepository interface {
	// 基础CRUD操作
	Create(childMedals *models.ChildMedals) error
	GetByID(id uint) (*models.ChildMedals, error)
	Update(id uint, childMedals *models.ChildMedals) error
	Delete(id uint) error

	// 基于participation_id的核心方法
	GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error)
	GetByParticipationID(participationID uint) ([]*models.ChildMedals, error)

	// 训练营级别操作
	GetByCampID(campID uint) ([]*models.ChildMedals, error)
	GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)

	// 批量操作
	BatchUpdateProgress(participationID uint, medalID uint, progress int) error
}

// childMedalsRepository 孩子勋章记录表：记录每个孩子的勋章获得情况仓储实现
type childMedalsRepository struct {
	db *gorm.DB
}

// NewChildMedalsRepository 创建孩子勋章记录表：记录每个孩子的勋章获得情况仓储
func NewChildMedalsRepository(db *gorm.DB) ChildMedalsRepository {
	return &childMedalsRepository{
		db: db,
	}
}

// Create 创建孩子勋章记录表：记录每个孩子的勋章获得情况
func (r *childMedalsRepository) Create(childMedals *models.ChildMedals) error {
	if err := r.db.Create(childMedals).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建孩子勋章记录表：记录每个孩子的勋章获得情况失败")
	}
	return nil
}

// GetByID 根据ID获取孩子勋章记录表：记录每个孩子的勋章获得情况
func (r *childMedalsRepository) GetByID(id uint) (*models.ChildMedals, error) {
	var childMedals models.ChildMedals
	if err := r.db.First(&childMedals, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("孩子勋章记录表：记录每个孩子的勋章获得情况不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询孩子勋章记录表：记录每个孩子的勋章获得情况失败")
	}
	return &childMedals, nil
}

// Update 更新孩子勋章记录表：记录每个孩子的勋章获得情况
func (r *childMedalsRepository) Update(id uint, childMedals *models.ChildMedals) error {
	if err := r.db.Model(&models.ChildMedals{}).Where("id = ?", id).Updates(childMedals).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新孩子勋章记录表：记录每个孩子的勋章获得情况失败")
	}
	return nil
}

// Delete 删除孩子勋章记录表：记录每个孩子的勋章获得情况
func (r *childMedalsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.ChildMedals{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除孩子勋章记录表：记录每个孩子的勋章获得情况失败")
	}
	return nil
}

// List 获取孩子勋章记录表：记录每个孩子的勋章获得情况列表
func (r *childMedalsRepository) List(offset, limit int) ([]*models.ChildMedals, int64, error) {
	var childMedalss []*models.ChildMedals
	var total int64

	// 获取总数
	if err := r.db.Model(&models.ChildMedals{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子勋章记录表：记录每个孩子的勋章获得情况总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&childMedalss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子勋章记录表：记录每个孩子的勋章获得情况列表失败")
	}

	return childMedalss, total, nil
}

// GetByParticipationIDAndMedalID 根据参与记录ID和勋章ID获取勋章记录
func (r *childMedalsRepository) GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error) {
	var childMedals models.ChildMedals
	if err := r.db.Where("participation_id = ? AND medal_id = ?", participationID, medalID).First(&childMedals).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("勋章记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询勋章记录失败")
	}
	return &childMedals, nil
}

// GetByParticipationID 根据参与记录ID获取所有勋章记录
func (r *childMedalsRepository) GetByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
	var childMedals []*models.ChildMedals
	if err := r.db.Where("participation_id = ?", participationID).Find(&childMedals).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询参与记录勋章失败")
	}
	return childMedals, nil
}

// GetByCampID 获取训练营的所有勋章记录
func (r *childMedalsRepository) GetByCampID(campID uint) ([]*models.ChildMedals, error) {
	var childMedals []*models.ChildMedals
	if err := r.db.Joins("JOIN user_camp_participations ucp ON child_medals.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND ucp.deleted_at IS NULL", campID).
		Find(&childMedals).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营勋章记录失败")
	}
	return childMedals, nil
}

// GetUnlockedMedalsByParticipationID 获取参与记录已解锁的勋章
func (r *childMedalsRepository) GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
	var childMedals []*models.ChildMedals
	if err := r.db.Where("participation_id = ? AND is_unlocked = 1", participationID).Find(&childMedals).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询已解锁勋章失败")
	}
	return childMedals, nil
}

// BatchUpdateProgress 批量更新勋章进度
func (r *childMedalsRepository) BatchUpdateProgress(participationID uint, medalID uint, progress int) error {
	if err := r.db.Model(&models.ChildMedals{}).
		Where("participation_id = ? AND medal_id = ?", participationID, medalID).
		Update("current_progress", progress).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量更新勋章进度失败")
	}
	return nil
}
