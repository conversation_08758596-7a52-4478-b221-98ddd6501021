package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// UsersRepository 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析仓储接口
type UsersRepository interface {
	Create(users *models.Users) error
	GetByID(id uint) (*models.Users, error)
	GetByOpenID(openid string) (*models.Users, error)
	Update(id uint, users *models.Users) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Users, int64, error)
}

// usersRepository 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析仓储实现
type usersRepository struct {
	db *gorm.DB
}

// NewUsersRepository 创建用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析仓储
func NewUsersRepository(db *gorm.DB) UsersRepository {
	return &usersRepository{
		db: db,
	}
}

// Create 创建用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析
func (r *usersRepository) Create(users *models.Users) error {
	if err := r.db.Create(users).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建用户失败", err.Error())
	}
	return nil
}

// GetByID 根据ID获取用户（自动过滤软删除）
func (r *usersRepository) GetByID(id uint) (*models.Users, error) {
	var users models.Users
	if err := r.db.Where("deleted_at IS NULL").First(&users, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户失败", err.Error())
	}
	return &users, nil
}

// GetByOpenID 根据微信OpenID获取用户（自动过滤软删除）
func (r *usersRepository) GetByOpenID(openid string) (*models.Users, error) {
	var users models.Users
	if err := r.db.Where("openid = ? AND deleted_at IS NULL", openid).First(&users).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户失败", err.Error())
	}
	return &users, nil
}

// Update 更新用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析
func (r *usersRepository) Update(id uint, users *models.Users) error {
	if err := r.db.Model(&models.Users{}).Where("id = ?", id).Updates(users).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新用户失败", err.Error())
	}
	return nil
}

// Delete 软删除用户
func (r *usersRepository) Delete(id uint) error {
	if err := r.db.Model(&models.Users{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除用户失败", err.Error())
	}
	return nil
}

// List 获取用户列表（自动过滤软删除）
func (r *usersRepository) List(offset, limit int) ([]*models.Users, int64, error) {
	var users []*models.Users
	var total int64

	// 获取总数（过滤软删除）
	if err := r.db.Model(&models.Users{}).Where("deleted_at IS NULL").Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询用户总数失败", err.Error())
	}

	// 获取列表（过滤软删除）
	if err := r.db.Where("deleted_at IS NULL").Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询用户列表失败", err.Error())
	}

	return users, total, nil
}
