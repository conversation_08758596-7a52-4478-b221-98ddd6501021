package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ContractWitnessesRepository 契约见证人表：管理家庭契约的见证人信息仓储接口
type ContractWitnessesRepository interface {
	Create(contractWitnesses *models.ContractWitnesses) error
	GetByID(id uint) (*models.ContractWitnesses, error)
	Update(id uint, contractWitnesses *models.ContractWitnesses) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.ContractWitnesses, int64, error)
	// 根据契约ID获取见证人列表
	GetByContractID(contractID uint) ([]*models.ContractWitnesses, error)
}

// contractWitnessesRepository 契约见证人表：管理家庭契约的见证人信息仓储实现
type contractWitnessesRepository struct {
	db *gorm.DB
}

// NewContractWitnessesRepository 创建契约见证人表：管理家庭契约的见证人信息仓储
func NewContractWitnessesRepository(db *gorm.DB) ContractWitnessesRepository {
	return &contractWitnessesRepository{
		db: db,
	}
}

// Create 创建契约见证人表：管理家庭契约的见证人信息
func (r *contractWitnessesRepository) Create(contractWitnesses *models.ContractWitnesses) error {
	if err := r.db.Create(contractWitnesses).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建契约见证人表：管理家庭契约的见证人信息失败")
	}
	return nil
}

// GetByID 根据ID获取契约见证人表：管理家庭契约的见证人信息
func (r *contractWitnessesRepository) GetByID(id uint) (*models.ContractWitnesses, error) {
	var contractWitnesses models.ContractWitnesses
	if err := r.db.First(&contractWitnesses, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("契约见证人表：管理家庭契约的见证人信息不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询契约见证人表：管理家庭契约的见证人信息失败")
	}
	return &contractWitnesses, nil
}

// Update 更新契约见证人表：管理家庭契约的见证人信息
func (r *contractWitnessesRepository) Update(id uint, contractWitnesses *models.ContractWitnesses) error {
	if err := r.db.Model(&models.ContractWitnesses{}).Where("id = ?", id).Updates(contractWitnesses).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新契约见证人表：管理家庭契约的见证人信息失败")
	}
	return nil
}

// Delete 删除契约见证人表：管理家庭契约的见证人信息
func (r *contractWitnessesRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.ContractWitnesses{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除契约见证人表：管理家庭契约的见证人信息失败")
	}
	return nil
}

// List 获取契约见证人表：管理家庭契约的见证人信息列表
func (r *contractWitnessesRepository) List(offset, limit int) ([]*models.ContractWitnesses, int64, error) {
	var contractWitnessess []*models.ContractWitnesses
	var total int64

	// 获取总数
	if err := r.db.Model(&models.ContractWitnesses{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询契约见证人表：管理家庭契约的见证人信息总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&contractWitnessess).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询契约见证人表：管理家庭契约的见证人信息列表失败")
	}

	return contractWitnessess, total, nil
}

// GetByContractID 根据契约ID获取见证人列表
func (r *contractWitnessesRepository) GetByContractID(contractID uint) ([]*models.ContractWitnesses, error) {
	var contractWitnesses []*models.ContractWitnesses

	if err := r.db.Where("contract_id = ?", contractID).Find(&contractWitnesses).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询契约见证人列表失败")
	}

	return contractWitnesses, nil
}
