package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// FamilyContractsRepository 家庭荣誉契约表：记录家庭契约的完整信息仓储接口
type FamilyContractsRepository interface {
	Create(familyContracts *models.FamilyContracts) error
	GetByID(id uint) (*models.FamilyContracts, error)
	Update(id uint, familyContracts *models.FamilyContracts) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.FamilyContracts, int64, error)
	// 根据孩子ID和状态获取契约列表
	GetByChildIDAndStatus(childID uint, status int8) ([]*models.FamilyContracts, error)
}

// familyContractsRepository 家庭荣誉契约表：记录家庭契约的完整信息仓储实现
type familyContractsRepository struct {
	db *gorm.DB
}

// NewFamilyContractsRepository 创建家庭荣誉契约表：记录家庭契约的完整信息仓储
func NewFamilyContractsRepository(db *gorm.DB) FamilyContractsRepository {
	return &familyContractsRepository{
		db: db,
	}
}

// Create 创建家庭荣誉契约表：记录家庭契约的完整信息
func (r *familyContractsRepository) Create(familyContracts *models.FamilyContracts) error {
	if err := r.db.Create(familyContracts).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建家庭荣誉契约表：记录家庭契约的完整信息失败")
	}
	return nil
}

// GetByID 根据ID获取家庭荣誉契约表：记录家庭契约的完整信息
func (r *familyContractsRepository) GetByID(id uint) (*models.FamilyContracts, error) {
	var familyContracts models.FamilyContracts
	if err := r.db.First(&familyContracts, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("家庭荣誉契约表：记录家庭契约的完整信息不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询家庭荣誉契约表：记录家庭契约的完整信息失败")
	}
	return &familyContracts, nil
}

// Update 更新家庭荣誉契约表：记录家庭契约的完整信息
func (r *familyContractsRepository) Update(id uint, familyContracts *models.FamilyContracts) error {
	if err := r.db.Model(&models.FamilyContracts{}).Where("id = ?", id).Updates(familyContracts).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新家庭荣誉契约表：记录家庭契约的完整信息失败")
	}
	return nil
}

// Delete 删除家庭荣誉契约表：记录家庭契约的完整信息
func (r *familyContractsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.FamilyContracts{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除家庭荣誉契约表：记录家庭契约的完整信息失败")
	}
	return nil
}

// List 获取家庭荣誉契约表：记录家庭契约的完整信息列表
func (r *familyContractsRepository) List(offset, limit int) ([]*models.FamilyContracts, int64, error) {
	var familyContractss []*models.FamilyContracts
	var total int64

	// 获取总数
	if err := r.db.Model(&models.FamilyContracts{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询家庭荣誉契约表：记录家庭契约的完整信息总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&familyContractss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询家庭荣誉契约表：记录家庭契约的完整信息列表失败")
	}

	return familyContractss, total, nil
}

// GetByChildIDAndStatus 根据孩子ID和状态获取契约列表
func (r *familyContractsRepository) GetByChildIDAndStatus(childID uint, status int8) ([]*models.FamilyContracts, error) {
	var familyContracts []*models.FamilyContracts

	query := r.db.Where("child_id = ?", childID)
	if status > 0 {
		query = query.Where("contract_status = ?", status)
	}

	if err := query.Order("created_at DESC").Find(&familyContracts).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询家庭契约列表失败")
	}

	return familyContracts, nil
}
