package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"
	"time"

	"gorm.io/gorm"
)

// UserCampParticipationsRepository 用户训练营参与记录表：记录用户参与训练营的完整生命周期仓储接口
type UserCampParticipationsRepository interface {
	Create(userCampParticipations *models.UserCampParticipations) error
	GetByID(id uint) (*models.UserCampParticipations, error)
	Update(id uint, userCampParticipations *models.UserCampParticipations) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.UserCampParticipations, int64, error)
	// 根据训练营ID和孩子ID获取参与记录
	GetByCampAndChild(campID uint, childID uint) (*models.UserCampParticipations, error)
	// 检查用户是否已参与指定训练营
	ExistsByCampAndChild(campID uint, childID uint) (bool, error)
	// 根据孩子ID获取参与的训练营列表
	GetByChildID(childID uint) ([]*models.UserCampParticipations, error)
	// 更新最后打卡日期
	UpdateLastCheckinDate(id uint, checkinDate time.Time) error
}

// userCampParticipationsRepository 用户训练营参与记录表：记录用户参与训练营的完整生命周期仓储实现
type userCampParticipationsRepository struct {
	db *gorm.DB
}

// NewUserCampParticipationsRepository 创建用户训练营参与记录表：记录用户参与训练营的完整生命周期仓储
func NewUserCampParticipationsRepository(db *gorm.DB) UserCampParticipationsRepository {
	return &userCampParticipationsRepository{
		db: db,
	}
}

// Create 创建用户训练营参与记录表：记录用户参与训练营的完整生命周期
func (r *userCampParticipationsRepository) Create(userCampParticipations *models.UserCampParticipations) error {
	if err := r.db.Create(userCampParticipations).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建用户训练营参与记录表：记录用户参与训练营的完整生命周期失败")
	}
	return nil
}

// GetByID 根据ID获取用户训练营参与记录表：记录用户参与训练营的完整生命周期
func (r *userCampParticipationsRepository) GetByID(id uint) (*models.UserCampParticipations, error) {
	var userCampParticipations models.UserCampParticipations
	if err := r.db.First(&userCampParticipations, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户训练营参与记录表：记录用户参与训练营的完整生命周期不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户训练营参与记录表：记录用户参与训练营的完整生命周期失败")
	}
	return &userCampParticipations, nil
}

// GetByCampAndChild 根据训练营ID和孩子ID获取参与记录
func (r *userCampParticipationsRepository) GetByCampAndChild(campID uint, childID uint) (*models.UserCampParticipations, error) {
	var participation models.UserCampParticipations
	if err := r.db.Where("camp_id = ? AND child_id = ?", campID, childID).First(&participation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户训练营参与记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户训练营参与记录失败")
	}
	return &participation, nil
}

// ExistsByCampAndChild 检查用户是否已参与指定训练营
func (r *userCampParticipationsRepository) ExistsByCampAndChild(campID uint, childID uint) (bool, error) {
	var count int64
	if err := r.db.Model(&models.UserCampParticipations{}).
		Where("camp_id = ? AND child_id = ?", campID, childID).
		Count(&count).Error; err != nil {
		return false, errcode.ErrDatabase.WithDetails("检查用户训练营参与状态失败")
	}
	return count > 0, nil
}

// GetByChildID 根据孩子ID获取参与的训练营列表
func (r *userCampParticipationsRepository) GetByChildID(childID uint) ([]*models.UserCampParticipations, error) {
	var participations []*models.UserCampParticipations
	if err := r.db.Where("child_id = ?", childID).Find(&participations).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询用户训练营参与列表失败")
	}
	return participations, nil
}

// Update 更新用户训练营参与记录表：记录用户参与训练营的完整生命周期
func (r *userCampParticipationsRepository) Update(id uint, userCampParticipations *models.UserCampParticipations) error {
	if err := r.db.Model(&models.UserCampParticipations{}).Where("id = ?", id).Updates(userCampParticipations).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新用户训练营参与记录表：记录用户参与训练营的完整生命周期失败")
	}
	return nil
}

// Delete 删除用户训练营参与记录表：记录用户参与训练营的完整生命周期
func (r *userCampParticipationsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.UserCampParticipations{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除用户训练营参与记录表：记录用户参与训练营的完整生命周期失败")
	}
	return nil
}

// List 获取用户训练营参与记录表：记录用户参与训练营的完整生命周期列表
func (r *userCampParticipationsRepository) List(offset, limit int) ([]*models.UserCampParticipations, int64, error) {
	var userCampParticipationss []*models.UserCampParticipations
	var total int64

	// 获取总数
	if err := r.db.Model(&models.UserCampParticipations{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询用户训练营参与记录表：记录用户参与训练营的完整生命周期总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&userCampParticipationss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询用户训练营参与记录表：记录用户参与训练营的完整生命周期列表失败")
	}

	return userCampParticipationss, total, nil
}

// UpdateLastCheckinDate 更新最后打卡日期
func (r *userCampParticipationsRepository) UpdateLastCheckinDate(id uint, checkinDate time.Time) error {
	if err := r.db.Model(&models.UserCampParticipations{}).Where("id = ?", id).Update("last_checkin_date", checkinDate).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新最后打卡日期失败")
	}
	return nil
}
