package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"
	"time"

	"gorm.io/gorm"
)

// CampCheckinDatesRepository 训练营打卡日期表仓储接口
type CampCheckinDatesRepository interface {
	// 基础CRUD操作
	Create(campCheckinDates *models.CampCheckinDates) error
	GetByID(id uint) (*models.CampCheckinDates, error)
	Update(campCheckinDates *models.CampCheckinDates) error
	Delete(id uint) error

	// 批量操作
	BatchCreate(campCheckinDatesList []*models.CampCheckinDates) error
	BatchUpdateStatus(participationID int64, updates map[int]models.CampCheckinDatesUpdateRequest) error

	// 查询操作
	GetByParticipationID(participationID int64) ([]*models.CampCheckinDates, error)
	GetByParticipationIDAndDateRange(participationID int64, startDate, endDate time.Time) ([]*models.CampCheckinDates, error)
	GetByParticipationIDAndDayNumber(participationID int64, dayNumber int) (*models.CampCheckinDates, error)
	GetByParticipationIDAndDate(participationID int64, date time.Time) (*models.CampCheckinDates, error)

	// 统计操作
	CountByParticipationIDAndStatus(participationID int64, status int8) (int64, error)
	GetCompletedDaysCount(participationID int64) (int64, error)
	GetMissedDaysCount(participationID int64) (int64, error)
}

// campCheckinDatesRepository 训练营打卡日期表仓储实现
type campCheckinDatesRepository struct {
	db *gorm.DB
}

// NewCampCheckinDatesRepository 创建训练营打卡日期表仓储
func NewCampCheckinDatesRepository(db *gorm.DB) CampCheckinDatesRepository {
	return &campCheckinDatesRepository{db: db}
}

// Create 创建训练营打卡日期记录
func (r *campCheckinDatesRepository) Create(campCheckinDates *models.CampCheckinDates) error {
	if err := r.db.Create(campCheckinDates).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建打卡日期记录失败")
	}
	return nil
}

// GetByID 根据ID获取训练营打卡日期记录
func (r *campCheckinDatesRepository) GetByID(id uint) (*models.CampCheckinDates, error) {
	var campCheckinDates models.CampCheckinDates
	err := r.db.First(&campCheckinDates, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound
		}
		return nil, errcode.ErrDatabase.WithDetails("获取打卡日期记录失败")
	}
	return &campCheckinDates, nil
}

// Update 更新训练营打卡日期记录
func (r *campCheckinDatesRepository) Update(campCheckinDates *models.CampCheckinDates) error {
	if err := r.db.Save(campCheckinDates).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新打卡日期记录失败")
	}
	return nil
}

// Delete 删除训练营打卡日期记录（软删除）
func (r *campCheckinDatesRepository) Delete(id uint) error {
	if err := r.db.Model(&models.CampCheckinDates{}).Where("id = ?", id).Update("deleted_at", time.Now()).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除打卡日期记录失败")
	}
	return nil
}

// BatchCreate 批量创建训练营打卡日期记录
func (r *campCheckinDatesRepository) BatchCreate(campCheckinDatesList []*models.CampCheckinDates) error {
	if len(campCheckinDatesList) == 0 {
		return nil
	}

	if err := r.db.CreateInBatches(campCheckinDatesList, 100).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量创建打卡日期记录失败")
	}
	return nil
}

// BatchUpdateStatus 批量更新打卡状态
func (r *campCheckinDatesRepository) BatchUpdateStatus(participationID int64, updates map[int]models.CampCheckinDatesUpdateRequest) error {
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for dayNumber, update := range updates {
		updateData := make(map[string]interface{})
		if update.Status != nil {
			updateData["status"] = *update.Status
		}
		if update.CheckinRecordID != nil {
			updateData["checkin_record_id"] = *update.CheckinRecordID
		}

		if len(updateData) > 0 {
			if err := tx.Model(&models.CampCheckinDates{}).
				Where("participation_id = ? AND day_number = ?", participationID, dayNumber).
				Updates(updateData).Error; err != nil {
				tx.Rollback()
				return errcode.ErrDatabase.WithDetails("批量更新打卡状态失败")
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量更新打卡状态事务提交失败")
	}
	return nil
}

// GetByParticipationID 根据参与记录ID获取所有打卡日期
func (r *campCheckinDatesRepository) GetByParticipationID(participationID int64) ([]*models.CampCheckinDates, error) {
	var campCheckinDatesList []*models.CampCheckinDates
	err := r.db.Where("participation_id = ?", participationID).
		Order("day_number ASC").
		Find(&campCheckinDatesList).Error
	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("获取打卡日期列表失败")
	}
	return campCheckinDatesList, nil
}

// GetByParticipationIDAndDateRange 根据参与记录ID和日期范围获取打卡日期
func (r *campCheckinDatesRepository) GetByParticipationIDAndDateRange(participationID int64, startDate, endDate time.Time) ([]*models.CampCheckinDates, error) {
	var campCheckinDatesList []*models.CampCheckinDates
	err := r.db.Where("participation_id = ? AND checkin_date >= ? AND checkin_date <= ?",
		participationID, startDate, endDate).
		Order("day_number ASC").
		Find(&campCheckinDatesList).Error
	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("获取日期范围内打卡日期失败")
	}
	return campCheckinDatesList, nil
}

// GetByParticipationIDAndDayNumber 根据参与记录ID和天数序号获取打卡日期
func (r *campCheckinDatesRepository) GetByParticipationIDAndDayNumber(participationID int64, dayNumber int) (*models.CampCheckinDates, error) {
	var campCheckinDates models.CampCheckinDates
	err := r.db.Where("participation_id = ? AND day_number = ?", participationID, dayNumber).
		First(&campCheckinDates).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound
		}
		return nil, errcode.ErrDatabase.WithDetails("获取指定天数打卡日期失败")
	}
	return &campCheckinDates, nil
}

// GetByParticipationIDAndDate 根据参与记录ID和日期获取打卡日期
func (r *campCheckinDatesRepository) GetByParticipationIDAndDate(participationID int64, date time.Time) (*models.CampCheckinDates, error) {
	var campCheckinDates models.CampCheckinDates
	err := r.db.Where("participation_id = ? AND checkin_date = ?", participationID, date).
		First(&campCheckinDates).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound
		}
		return nil, errcode.ErrDatabase.WithDetails("获取指定日期打卡记录失败")
	}
	return &campCheckinDates, nil
}

// CountByParticipationIDAndStatus 根据参与记录ID和状态统计数量
func (r *campCheckinDatesRepository) CountByParticipationIDAndStatus(participationID int64, status int8) (int64, error) {
	var count int64
	err := r.db.Model(&models.CampCheckinDates{}).
		Where("participation_id = ? AND status = ?", participationID, status).
		Count(&count).Error
	if err != nil {
		return 0, errcode.ErrDatabase.WithDetails("统计打卡状态数量失败")
	}
	return count, nil
}

// GetCompletedDaysCount 获取已完成打卡天数
func (r *campCheckinDatesRepository) GetCompletedDaysCount(participationID int64) (int64, error) {
	var count int64
	err := r.db.Model(&models.CampCheckinDates{}).
		Where("participation_id = ? AND status IN (?, ?)",
			participationID, models.CheckinStatusCompleted, models.CheckinStatusMakeup).
		Count(&count).Error
	if err != nil {
		return 0, errcode.ErrDatabase.WithDetails("统计已完成打卡天数失败")
	}
	return count, nil
}

// GetMissedDaysCount 获取错过打卡天数
func (r *campCheckinDatesRepository) GetMissedDaysCount(participationID int64) (int64, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	err := r.db.Model(&models.CampCheckinDates{}).
		Where("participation_id = ? AND status = ? AND checkin_date < ?",
			participationID, models.CheckinStatusPending, today).
		Count(&count).Error
	if err != nil {
		return 0, errcode.ErrDatabase.WithDetails("统计错过打卡天数失败")
	}
	return count, nil
}
