package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ChildPointsRepository 孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储接口
type ChildPointsRepository interface {
	// 基础CRUD操作
	Create(childPoints *models.ChildPoints) error
	GetByID(id uint) (*models.ChildPoints, error)
	Update(id uint, childPoints *models.ChildPoints) error
	Delete(id uint) error

	// 基于participation_id的核心方法
	GetByParticipationID(participationID uint) (*models.ChildPoints, error)

	// 训练营级别操作
	GetByCampID(campID uint) ([]*models.ChildPoints, error)
	GetRankingByCampID(campID uint, limit int) ([]*models.ChildPoints, error)
	GetParticipationRankInCamp(participationID uint, campID uint) (int, error)

	// 批量操作
	BatchUpdateWeeklyPoints(campID uint) error
	BatchUpdateMonthlyPoints(campID uint) error
	BatchUpdateRankings(campID uint) error
}

// childPointsRepository 孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储实现
type childPointsRepository struct {
	db *gorm.DB
}

// NewChildPointsRepository 创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储
func NewChildPointsRepository(db *gorm.DB) ChildPointsRepository {
	return &childPointsRepository{
		db: db,
	}
}

// Create 创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Create(childPoints *models.ChildPoints) error {
	if err := r.db.Create(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// GetByID 根据ID获取孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) GetByID(id uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.First(&childPoints, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("孩子积分统计表：统计每个孩子的积分和排行榜相关数据不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return &childPoints, nil
}

// Update 更新孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Update(id uint, childPoints *models.ChildPoints) error {
	if err := r.db.Model(&models.ChildPoints{}).Where("id = ?", id).Updates(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// Delete 删除孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.ChildPoints{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// List 获取孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表
func (r *childPointsRepository) List(offset, limit int) ([]*models.ChildPoints, int64, error) {
	var childPointss []*models.ChildPoints
	var total int64

	// 获取总数
	if err := r.db.Model(&models.ChildPoints{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&childPointss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表失败")
	}

	return childPointss, total, nil
}

// GetByParticipationID 根据参与记录ID获取积分记录
func (r *childPointsRepository) GetByParticipationID(participationID uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.Where("participation_id = ?", participationID).First(&childPoints).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("参与记录积分不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询参与记录积分失败")
	}
	return &childPoints, nil
}

// GetByCampID 获取训练营的所有积分记录
func (r *childPointsRepository) GetByCampID(campID uint) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Joins("JOIN user_camp_participations ucp ON child_points.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND ucp.deleted_at IS NULL", campID).
		Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营积分记录失败")
	}
	return childPoints, nil
}

// GetRankingByCampID 获取训练营排行榜
func (r *childPointsRepository) GetRankingByCampID(campID uint, limit int) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Joins("JOIN user_camp_participations ucp ON child_points.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND ucp.deleted_at IS NULL", campID).
		Order("child_points.total_points DESC").
		Limit(limit).
		Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营排行榜失败")
	}
	return childPoints, nil
}

// GetParticipationRankInCamp 获取参与记录在训练营中的排名
func (r *childPointsRepository) GetParticipationRankInCamp(participationID uint, campID uint) (int, error) {
	var rank int64
	if err := r.db.Raw(`
		SELECT COUNT(*) + 1 as rank
		FROM child_points cp1
		JOIN user_camp_participations ucp1 ON cp1.participation_id = ucp1.id
		JOIN child_points cp2 ON cp2.participation_id = ?
		WHERE ucp1.camp_id = ? AND ucp1.deleted_at IS NULL
		AND cp1.total_points > cp2.total_points
	`, participationID, campID).Scan(&rank).Error; err != nil {
		return 0, errcode.ErrDatabase.WithDetails("查询训练营排名失败")
	}
	return int(rank), nil
}

// BatchUpdateWeeklyPoints 批量更新训练营的周积分
func (r *childPointsRepository) BatchUpdateWeeklyPoints(campID uint) error {
	if err := r.db.Exec(`
		UPDATE child_points cp
		JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
		SET cp.week_points = 0
		WHERE ucp.camp_id = ? AND ucp.deleted_at IS NULL
	`, campID).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量更新周积分失败")
	}
	return nil
}

// BatchUpdateMonthlyPoints 批量更新训练营的月积分
func (r *childPointsRepository) BatchUpdateMonthlyPoints(campID uint) error {
	if err := r.db.Exec(`
		UPDATE child_points cp
		JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
		SET cp.month_points = 0
		WHERE ucp.camp_id = ? AND ucp.deleted_at IS NULL
	`, campID).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量更新月积分失败")
	}
	return nil
}

// BatchUpdateRankings 批量更新训练营的排名
func (r *childPointsRepository) BatchUpdateRankings(campID uint) error {
	if err := r.db.Exec(`
		UPDATE child_points cp
		JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
		JOIN (
			SELECT
				cp2.participation_id,
				ROW_NUMBER() OVER (ORDER BY cp2.total_points DESC) as new_rank
			FROM child_points cp2
			JOIN user_camp_participations ucp2 ON cp2.participation_id = ucp2.id
			WHERE ucp2.camp_id = ? AND ucp2.deleted_at IS NULL
		) rankings ON cp.participation_id = rankings.participation_id
		SET cp.week_rank = rankings.new_rank
		WHERE ucp.camp_id = ? AND ucp.deleted_at IS NULL
	`, campID, campID).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("批量更新排名失败")
	}
	return nil
}
