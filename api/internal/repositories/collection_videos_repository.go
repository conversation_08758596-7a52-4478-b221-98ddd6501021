package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// CollectionVideosRepository 集合视频关联表：管理视频集合与具体视频的关联关系仓储接口
type CollectionVideosRepository interface {
	Create(collectionVideos *models.CollectionVideos) error
	GetByID(id uint) (*models.CollectionVideos, error)
	Update(id uint, collectionVideos *models.CollectionVideos) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.CollectionVideos, int64, error)
	GetVideosByCollectionID(collectionID int64) ([]*models.CollectionVideos, error)
}

// collectionVideosRepository 集合视频关联表：管理视频集合与具体视频的关联关系仓储实现
type collectionVideosRepository struct {
	db *gorm.DB
}

// NewCollectionVideosRepository 创建集合视频关联表：管理视频集合与具体视频的关联关系仓储
func NewCollectionVideosRepository(db *gorm.DB) CollectionVideosRepository {
	return &collectionVideosRepository{
		db: db,
	}
}

// Create 创建集合视频关联表：管理视频集合与具体视频的关联关系
func (r *collectionVideosRepository) Create(collectionVideos *models.CollectionVideos) error {
	if err := r.db.Create(collectionVideos).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建集合视频关联表：管理视频集合与具体视频的关联关系失败")
	}
	return nil
}

// GetByID 根据ID获取集合视频关联表：管理视频集合与具体视频的关联关系
func (r *collectionVideosRepository) GetByID(id uint) (*models.CollectionVideos, error) {
	var collectionVideos models.CollectionVideos
	if err := r.db.First(&collectionVideos, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("集合视频关联表：管理视频集合与具体视频的关联关系不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询集合视频关联表：管理视频集合与具体视频的关联关系失败")
	}
	return &collectionVideos, nil
}

// Update 更新集合视频关联表：管理视频集合与具体视频的关联关系
func (r *collectionVideosRepository) Update(id uint, collectionVideos *models.CollectionVideos) error {
	if err := r.db.Model(&models.CollectionVideos{}).Where("id = ?", id).Updates(collectionVideos).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新集合视频关联表：管理视频集合与具体视频的关联关系失败")
	}
	return nil
}

// Delete 删除集合视频关联表：管理视频集合与具体视频的关联关系
func (r *collectionVideosRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.CollectionVideos{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除集合视频关联表：管理视频集合与具体视频的关联关系失败")
	}
	return nil
}

// List 获取集合视频关联表：管理视频集合与具体视频的关联关系列表
func (r *collectionVideosRepository) List(offset, limit int) ([]*models.CollectionVideos, int64, error) {
	var collectionVideoss []*models.CollectionVideos
	var total int64

	// 获取总数
	if err := r.db.Model(&models.CollectionVideos{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询集合视频关联表：管理视频集合与具体视频的关联关系总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&collectionVideoss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询集合视频关联表：管理视频集合与具体视频的关联关系列表失败")
	}

	return collectionVideoss, total, nil
}

// GetVideosByCollectionID 根据集合ID获取视频关联关系列表，按排序顺序返回
func (r *collectionVideosRepository) GetVideosByCollectionID(collectionID int64) ([]*models.CollectionVideos, error) {
	var collectionVideos []*models.CollectionVideos

	// 查询指定集合的视频关联关系，按sort_order升序排序
	if err := r.db.Where("collection_id = ?", collectionID).
		Order("sort_order ASC").
		Find(&collectionVideos).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询集合视频关联关系失败")
	}

	return collectionVideos, nil
}
