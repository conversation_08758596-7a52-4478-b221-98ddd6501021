package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// GrowthTracksRepository 成长轨迹记录表：记录孩子的成长里程碑仓储接口
type GrowthTracksRepository interface {
	Create(growthTracks *models.GrowthTracks) error
	GetByID(id uint) (*models.GrowthTracks, error)
	Update(id uint, growthTracks *models.GrowthTracks) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.GrowthTracks, int64, error)
	// 根据孩子ID获取成长轨迹列表
	GetByChildID(childID uint, offset, limit int) ([]*models.GrowthTracks, int64, error)
}

// growthTracksRepository 成长轨迹记录表：记录孩子的成长里程碑仓储实现
type growthTracksRepository struct {
	db *gorm.DB
}

// NewGrowthTracksRepository 创建成长轨迹记录表：记录孩子的成长里程碑仓储
func NewGrowthTracksRepository(db *gorm.DB) GrowthTracksRepository {
	return &growthTracksRepository{
		db: db,
	}
}

// Create 创建成长轨迹记录表：记录孩子的成长里程碑
func (r *growthTracksRepository) Create(growthTracks *models.GrowthTracks) error {
	if err := r.db.Create(growthTracks).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建成长轨迹记录表：记录孩子的成长里程碑失败")
	}
	return nil
}

// GetByID 根据ID获取成长轨迹记录表：记录孩子的成长里程碑
func (r *growthTracksRepository) GetByID(id uint) (*models.GrowthTracks, error) {
	var growthTracks models.GrowthTracks
	if err := r.db.First(&growthTracks, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("成长轨迹记录表：记录孩子的成长里程碑不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询成长轨迹记录表：记录孩子的成长里程碑失败")
	}
	return &growthTracks, nil
}

// Update 更新成长轨迹记录表：记录孩子的成长里程碑
func (r *growthTracksRepository) Update(id uint, growthTracks *models.GrowthTracks) error {
	if err := r.db.Model(&models.GrowthTracks{}).Where("id = ?", id).Updates(growthTracks).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新成长轨迹记录表：记录孩子的成长里程碑失败")
	}
	return nil
}

// Delete 删除成长轨迹记录表：记录孩子的成长里程碑
func (r *growthTracksRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.GrowthTracks{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除成长轨迹记录表：记录孩子的成长里程碑失败")
	}
	return nil
}

// List 获取成长轨迹记录表：记录孩子的成长里程碑列表
func (r *growthTracksRepository) List(offset, limit int) ([]*models.GrowthTracks, int64, error) {
	var growthTrackss []*models.GrowthTracks
	var total int64

	// 获取总数
	if err := r.db.Model(&models.GrowthTracks{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询成长轨迹记录表：记录孩子的成长里程碑总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&growthTrackss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询成长轨迹记录表：记录孩子的成长里程碑列表失败")
	}

	return growthTrackss, total, nil
}

// GetByChildID 根据孩子ID获取成长轨迹列表
func (r *growthTracksRepository) GetByChildID(childID uint, offset, limit int) ([]*models.GrowthTracks, int64, error) {
	var growthTrackss []*models.GrowthTracks
	var total int64

	// 获取总数
	if err := r.db.Model(&models.GrowthTracks{}).Where("child_id = ?", childID).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询成长轨迹记录总数失败")
	}

	// 获取列表，按创建时间倒序排列
	if err := r.db.Where("child_id = ?", childID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&growthTrackss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询成长轨迹记录列表失败")
	}

	return growthTrackss, total, nil
}
