package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// VideoCategoriesRepository 视频分类表：管理视频内容的分类体系仓储接口
type VideoCategoriesRepository interface {
	Create(videoCategories *models.VideoCategories) error
	GetByID(id uint) (*models.VideoCategories, error)
	Update(id uint, videoCategories *models.VideoCategories) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.VideoCategories, int64, error)
}

// videoCategoriesRepository 视频分类表：管理视频内容的分类体系仓储实现
type videoCategoriesRepository struct {
	db *gorm.DB
}

// NewVideoCategoriesRepository 创建视频分类表：管理视频内容的分类体系仓储
func NewVideoCategoriesRepository(db *gorm.DB) VideoCategoriesRepository {
	return &videoCategoriesRepository{
		db: db,
	}
}

// Create 创建视频分类表：管理视频内容的分类体系
func (r *videoCategoriesRepository) Create(videoCategories *models.VideoCategories) error {
	if err := r.db.Create(videoCategories).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建视频分类表：管理视频内容的分类体系失败")
	}
	return nil
}

// GetByID 根据ID获取视频分类表：管理视频内容的分类体系
func (r *videoCategoriesRepository) GetByID(id uint) (*models.VideoCategories, error) {
	var videoCategories models.VideoCategories
	if err := r.db.First(&videoCategories, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("视频分类表：管理视频内容的分类体系不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询视频分类表：管理视频内容的分类体系失败")
	}
	return &videoCategories, nil
}

// Update 更新视频分类表：管理视频内容的分类体系
func (r *videoCategoriesRepository) Update(id uint, videoCategories *models.VideoCategories) error {
	if err := r.db.Model(&models.VideoCategories{}).Where("id = ?", id).Updates(videoCategories).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新视频分类表：管理视频内容的分类体系失败")
	}
	return nil
}

// Delete 删除视频分类表：管理视频内容的分类体系
func (r *videoCategoriesRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.VideoCategories{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除视频分类表：管理视频内容的分类体系失败")
	}
	return nil
}

// List 获取视频分类表：管理视频内容的分类体系列表
func (r *videoCategoriesRepository) List(offset, limit int) ([]*models.VideoCategories, int64, error) {
	var videoCategoriess []*models.VideoCategories
	var total int64

	// 获取总数
	if err := r.db.Model(&models.VideoCategories{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频分类表：管理视频内容的分类体系总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&videoCategoriess).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频分类表：管理视频内容的分类体系列表失败")
	}

	return videoCategoriess, total, nil
}
