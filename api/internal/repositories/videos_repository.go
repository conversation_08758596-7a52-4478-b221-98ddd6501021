package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// VideosRepository 视频主表：存储教学视频的基本信息和元数据仓储接口
type VideosRepository interface {
	Create(videos *models.Videos) error
	GetByID(id uint) (*models.Videos, error)
	Update(id uint, videos *models.Videos) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Videos, int64, error)
	GetByIDs(ids []uint) ([]*models.Videos, error)
}

// videosRepository 视频主表：存储教学视频的基本信息和元数据仓储实现
type videosRepository struct {
	db *gorm.DB
}

// NewVideosRepository 创建视频主表：存储教学视频的基本信息和元数据仓储
func NewVideosRepository(db *gorm.DB) VideosRepository {
	return &videosRepository{
		db: db,
	}
}

// Create 创建视频主表：存储教学视频的基本信息和元数据
func (r *videosRepository) Create(videos *models.Videos) error {
	if err := r.db.Create(videos).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建视频主表：存储教学视频的基本信息和元数据失败")
	}
	return nil
}

// GetByID 根据ID获取视频主表：存储教学视频的基本信息和元数据
func (r *videosRepository) GetByID(id uint) (*models.Videos, error) {
	var videos models.Videos
	if err := r.db.First(&videos, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("视频主表：存储教学视频的基本信息和元数据不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询视频主表：存储教学视频的基本信息和元数据失败")
	}
	return &videos, nil
}

// Update 更新视频主表：存储教学视频的基本信息和元数据
func (r *videosRepository) Update(id uint, videos *models.Videos) error {
	if err := r.db.Model(&models.Videos{}).Where("id = ?", id).Updates(videos).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新视频主表：存储教学视频的基本信息和元数据失败")
	}
	return nil
}

// Delete 删除视频主表：存储教学视频的基本信息和元数据
func (r *videosRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.Videos{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除视频主表：存储教学视频的基本信息和元数据失败")
	}
	return nil
}

// List 获取视频主表：存储教学视频的基本信息和元数据列表
func (r *videosRepository) List(offset, limit int) ([]*models.Videos, int64, error) {
	var videoss []*models.Videos
	var total int64

	// 获取总数
	if err := r.db.Model(&models.Videos{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频主表：存储教学视频的基本信息和元数据总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&videoss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询视频主表：存储教学视频的基本信息和元数据列表失败")
	}

	return videoss, total, nil
}

// GetByIDs 根据ID列表批量获取视频，只返回状态为正常的视频
func (r *videosRepository) GetByIDs(ids []uint) ([]*models.Videos, error) {
	if len(ids) == 0 {
		return []*models.Videos{}, nil
	}

	var videos []*models.Videos

	// 批量查询视频，只查询状态为1（正常）的视频
	if err := r.db.Where("id IN ? AND status = ?", ids, 1).Find(&videos).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("批量查询视频失败")
	}

	return videos, nil
}
