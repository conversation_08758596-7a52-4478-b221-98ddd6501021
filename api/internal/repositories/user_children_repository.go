package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// UserChildrenRepository 用户孩子关联仓储接口
type UserChildrenRepository interface {
	Create(userChild *models.UserChildren) error
	GetByID(id uint) (*models.UserChildren, error)
	Update(id uint, userChild *models.UserChildren) error
	Delete(id uint) error
	GetByUserAndChild(userID, childID uint) (*models.UserChildren, error)
	GetChildrenByUserID(userID uint) ([]*models.UserChildren, error)
	GetUsersByChildID(childID uint) ([]*models.UserChildren, error)
	CheckUserCanManageChild(userID, childID uint) (bool, error)
	DeleteByUserAndChild(userID, childID uint) error
}

// userChildrenRepository 用户孩子关联仓储实现
type userChildrenRepository struct {
	db *gorm.DB
}

// NewUserChildrenRepository 创建用户孩子关联仓储
func NewUserChildrenRepository(db *gorm.DB) UserChildrenRepository {
	return &userChildrenRepository{
		db: db,
	}
}

// Create 创建用户孩子关联
func (r *userChildrenRepository) Create(userChild *models.UserChildren) error {
	if err := r.db.Create(userChild).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建用户孩子关联失败", err.Error())
	}
	return nil
}

// GetByID 根据ID获取用户孩子关联（自动过滤软删除）
func (r *userChildrenRepository) GetByID(id uint) (*models.UserChildren, error) {
	var userChild models.UserChildren
	if err := r.db.Where("deleted_at IS NULL").First(&userChild, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户孩子关联失败", err.Error())
	}
	return &userChild, nil
}

// Update 更新用户孩子关联
func (r *userChildrenRepository) Update(id uint, userChild *models.UserChildren) error {
	if err := r.db.Model(&models.UserChildren{}).Where("id = ? AND deleted_at IS NULL", id).Updates(userChild).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新用户孩子关联失败", err.Error())
	}
	return nil
}

// Delete 软删除用户孩子关联
func (r *userChildrenRepository) Delete(id uint) error {
	if err := r.db.Model(&models.UserChildren{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除用户孩子关联失败", err.Error())
	}
	return nil
}

// GetByUserAndChild 根据用户ID和孩子ID获取关联记录
func (r *userChildrenRepository) GetByUserAndChild(userID, childID uint) (*models.UserChildren, error) {
	var userChild models.UserChildren
	err := r.db.Where("user_id = ? AND child_id = ? AND deleted_at IS NULL", userID, childID).First(&userChild).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询用户孩子关联失败", err.Error())
	}
	return &userChild, nil
}

// GetChildrenByUserID 获取用户管理的所有孩子关联（预加载孩子信息）
func (r *userChildrenRepository) GetChildrenByUserID(userID uint) ([]*models.UserChildren, error) {
	var userChildren []*models.UserChildren
	
	err := r.db.Where("user_id = ? AND deleted_at IS NULL", userID).
		Preload("Child", "deleted_at IS NULL").
		Order("created_at DESC").
		Find(&userChildren).Error
		
	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询用户孩子关联失败", err.Error())
	}
	
	return userChildren, nil
}

// GetUsersByChildID 获取管理某个孩子的所有用户关联（预加载用户信息）
func (r *userChildrenRepository) GetUsersByChildID(childID uint) ([]*models.UserChildren, error) {
	var userChildren []*models.UserChildren
	
	err := r.db.Where("child_id = ? AND deleted_at IS NULL", childID).
		Preload("User", "deleted_at IS NULL").
		Order("created_at ASC").
		Find(&userChildren).Error
		
	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询孩子用户关联失败", err.Error())
	}
	
	return userChildren, nil
}

// CheckUserCanManageChild 检查用户是否可以管理某个孩子
func (r *userChildrenRepository) CheckUserCanManageChild(userID, childID uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.UserChildren{}).
		Where("user_id = ? AND child_id = ? AND deleted_at IS NULL", userID, childID).
		Count(&count).Error
		
	if err != nil {
		return false, errcode.ErrDatabase.WithDetails("检查用户权限失败", err.Error())
	}
	
	return count > 0, nil
}

// DeleteByUserAndChild 根据用户ID和孩子ID软删除关联
func (r *userChildrenRepository) DeleteByUserAndChild(userID, childID uint) error {
	err := r.db.Model(&models.UserChildren{}).
		Where("user_id = ? AND child_id = ?", userID, childID).
		Update("deleted_at", "NOW()").Error
		
	if err != nil {
		return errcode.ErrDatabase.WithDetails("删除用户孩子关联失败", err.Error())
	}
	
	return nil
}
