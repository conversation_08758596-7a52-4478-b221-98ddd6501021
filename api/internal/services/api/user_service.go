package api

import (
	"time"

	"kids-platform/internal/ecode"
	"kids-platform/internal/models"

	"kids-platform/internal/repositories"
	"kids-platform/pkg/config"
	"kids-platform/pkg/jwt"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/wechat"
)

// WechatServiceInterface 微信服务接口
type WechatServiceInterface interface {
	Code2Session(code string) (*wechat.Code2SessionResponse, error)
	DecryptUserInfo(encryptedData, iv, sessionKey string) (*wechat.UserInfo, error)
}

// JWTManagerInterface JWT管理器接口
type JWTManagerInterface interface {
	GenerateTokenPair(userID uint64, username string, userType uint8) (*jwt.TokenPair, error)
	ValidateToken(token string) (*jwt.Claims, error)
	RefreshToken(refreshToken string) (string, string, error)
}

// UserService 用户服务接口
type UserService interface {
	// 微信登录
	WechatLogin(req *models.WechatLoginRequest) (*models.WechatLoginResponse, error)
	// 获取用户信息
	GetUserByID(id uint) (*models.UsersResponse, error)
	// 更新用户信息
	UpdateUser(id uint, req *models.UsersUpdateRequest) (*models.UsersResponse, error)
}

// userService 用户服务实现
type userService struct {
	userRepo      repositories.UsersRepository
	wechatService WechatServiceInterface
	jwtManager    JWTManagerInterface
	config        *config.Config
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repositories.UsersRepository,
	wechatService WechatServiceInterface,
	jwtManager JWTManagerInterface,
	config *config.Config,
) UserService {
	return &userService{
		userRepo:      userRepo,
		wechatService: wechatService,
		jwtManager:    jwtManager,
		config:        config,
	}
}

// WechatLogin 微信登录
func (s *userService) WechatLogin(req *models.WechatLoginRequest) (*models.WechatLoginResponse, error) {
	// 1. 通过code获取微信用户信息
	code2SessionResp, err := s.wechatService.Code2Session(req.Code)
	if err != nil {
		logger.Error("Failed to get WeChat session", "error", err)
		return nil, ecode.ErrWeChatAuthFailed
	}

	// 2. 查找或创建用户
	user, isNewUser, err := s.findOrCreateUser(code2SessionResp, req)
	if err != nil {
		logger.Error("Failed to find or create user", "error", err)
		return nil, err
	}

	// 3. 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = now
	if err := s.userRepo.Update(user.ID, &models.Users{LastLoginAt: now}); err != nil {
		logger.Error("Failed to update last login time", "error", err)
		// 不影响登录流程，只记录错误
	}

	// 4. 生成JWT token
	tokenPair, err := s.jwtManager.GenerateTokenPair(uint64(user.ID), user.Nickname, uint8(user.UserType))
	if err != nil {
		logger.Error("Failed to generate token", "error", err)
		return nil, ecode.ErrTokenGenerationFailed
	}

	// 5. 构建响应
	return &models.WechatLoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    tokenPair.TokenType,
		UserInfo:     user.ToResponse(),
		IsNewUser:    isNewUser,
	}, nil
}

// findOrCreateUser 查找或创建用户
func (s *userService) findOrCreateUser(code2SessionResp *wechat.Code2SessionResponse, req *models.WechatLoginRequest) (*models.Users, bool, error) {
	// 先尝试根据openid查找用户
	existingUser, err := s.userRepo.GetByOpenID(code2SessionResp.OpenID)
	if err == nil {
		// 用户已存在，更新用户信息（如果有加密数据）
		if req.EncryptedData != "" && req.Iv != "" {
			if err := s.updateUserInfoFromWechat(existingUser, code2SessionResp, req); err != nil {
				logger.Error("Failed to update user info from WeChat", "error", err)
				// 不影响登录流程，继续使用现有用户信息
			}
		}
		return existingUser, false, nil
	}

	// 用户不存在，创建新用户
	newUser := &models.Users{
		OpenID:         code2SessionResp.OpenID,
		UnionID:        code2SessionResp.UnionID,
		Nickname:       "微信用户", // 默认昵称
		Status:         1,      // 正常状态
		UserType:       1,      // 普通用户
		CurrentChildID: 0,      // 新用户默认未选择孩子
		LastLoginAt:    time.Now(),
	}

	// 如果有加密的用户信息，解密并填充
	if req.EncryptedData != "" && req.Iv != "" {
		userInfo, err := s.wechatService.DecryptUserInfo(req.EncryptedData, req.Iv, code2SessionResp.SessionKey)
		if err != nil {
			logger.Error("Failed to decrypt user info", "error", err)
			// 不影响用户创建，使用默认信息
		} else {
			s.fillUserInfoFromWechat(newUser, userInfo)
		}
	}

	// 创建用户
	if err := s.userRepo.Create(newUser); err != nil {
		logger.Error("Failed to create user", "error", err)
		return nil, false, ecode.ErrUserAlreadyExists
	}

	return newUser, true, nil
}

// updateUserInfoFromWechat 从微信信息更新用户
func (s *userService) updateUserInfoFromWechat(user *models.Users, code2SessionResp *wechat.Code2SessionResponse, req *models.WechatLoginRequest) error {
	userInfo, err := s.wechatService.DecryptUserInfo(req.EncryptedData, req.Iv, code2SessionResp.SessionKey)
	if err != nil {
		return err
	}

	// 更新用户信息
	updates := &models.Users{
		Nickname: userInfo.NickName,
		Avatar:   userInfo.AvatarURL,
		Gender:   int8(userInfo.Gender),
		Province: userInfo.Province,
		City:     userInfo.City,
		UnionID:  userInfo.UnionID,
	}

	return s.userRepo.Update(user.ID, updates)
}

// fillUserInfoFromWechat 从微信信息填充用户数据
func (s *userService) fillUserInfoFromWechat(user *models.Users, userInfo *wechat.UserInfo) {
	user.Nickname = userInfo.NickName
	user.Avatar = userInfo.AvatarURL
	user.Gender = int8(userInfo.Gender)
	user.Province = userInfo.Province
	user.City = userInfo.City
	if userInfo.UnionID != "" {
		user.UnionID = userInfo.UnionID
	}
}

// GetUserByID 根据ID获取用户信息
func (s *userService) GetUserByID(id uint) (*models.UsersResponse, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	return user.ToResponse(), nil
}

// UpdateUser 更新用户信息
func (s *userService) UpdateUser(id uint, req *models.UsersUpdateRequest) (*models.UsersResponse, error) {
	// 获取现有用户
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 应用更新请求
	user.ApplyUpdateRequest(req)

	// 更新用户
	if err := s.userRepo.Update(id, user); err != nil {
		return nil, err
	}

	// 获取更新后的用户信息
	updatedUser, err := s.userRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	return updatedUser.ToResponse(), nil
}
