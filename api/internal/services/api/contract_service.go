package api

import (
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// ContractService 家庭契约服务接口
type ContractService interface {
	// 创建家庭契约
	CreateContract(req *ContractCreateRequest) (*ContractResponse, error)
	// 获取契约列表
	GetContracts(childID uint, status int8) ([]*ContractResponse, error)
	// 获取契约详情
	GetContractDetail(contractID uint) (*ContractDetailResponse, error)
	// 更新契约进度
	UpdateContractProgress(contractID uint, progress float64) error
	// 完成契约
	CompleteContract(contractID uint, witnessUserID uint) (*ContractCompleteResponse, error)
	// 获取契约统计
	GetContractStats(childID uint) (*ContractStatsResponse, error)
}

// contractService 家庭契约服务实现
type contractService struct {
	familyContractsRepo       repositories.FamilyContractsRepository
	contractWitnessesRepo     repositories.ContractWitnessesRepository
	trainingCampsRepo         repositories.TrainingCampsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	pointsService             PointsService
}

// NewContractService 创建家庭契约服务
func NewContractService(
	familyContractsRepo repositories.FamilyContractsRepository,
	contractWitnessesRepo repositories.ContractWitnessesRepository,
	trainingCampsRepo repositories.TrainingCampsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	pointsService PointsService,
) ContractService {
	return &contractService{
		familyContractsRepo:       familyContractsRepo,
		contractWitnessesRepo:     contractWitnessesRepo,
		trainingCampsRepo:         trainingCampsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		pointsService:             pointsService,
	}
}

// ==================== 请求和响应结构定义 ====================

// ContractCreateRequest 创建契约请求
type ContractCreateRequest struct {
	ChildID        uint   `json:"child_id" binding:"required"`         // 孩子ID
	CampID         uint   `json:"camp_id" binding:"required"`          // 关联训练营ID
	UserID         uint   `json:"user_id" binding:"required"`          // 创建用户ID
	Title          string `json:"title" binding:"required"`            // 契约标题
	Description    string `json:"description" binding:"required"`      // 契约描述
	TargetDays     int    `json:"target_days" binding:"required"`      // 目标天数
	RewardContent  string `json:"reward_content" binding:"required"`   // 奖励内容
	WitnessUserIDs []uint `json:"witness_user_ids" binding:"required"` // 见证人用户ID列表
}

// ContractResponse 契约响应
type ContractResponse struct {
	*models.FamilyContractsResponse
	Witnesses []*ContractWitnessInfo `json:"witnesses"` // 见证人信息
}

// ContractDetailResponse 契约详情响应
type ContractDetailResponse struct {
	*ContractResponse
	CampInfo *models.TrainingCampsResponse `json:"camp_info"` // 关联训练营信息
}

// ContractCompleteResponse 完成契约响应
type ContractCompleteResponse struct {
	Success      bool   `json:"success"`       // 是否成功
	Message      string `json:"message"`       // 响应消息
	PointsEarned int    `json:"points_earned"` // 获得积分
}

// ContractWitnessInfo 契约见证人信息
type ContractWitnessInfo struct {
	UserID   uint   `json:"user_id"`   // 用户ID
	UserName string `json:"user_name"` // 用户姓名
	Relation string `json:"relation"`  // 关系（爸爸、妈妈等）
}

// ContractStatsResponse 契约统计响应
type ContractStatsResponse struct {
	TotalContracts     int     `json:"total_contracts"`     // 总契约数
	CompletedContracts int     `json:"completed_contracts"` // 已完成契约数
	ActiveContracts    int     `json:"active_contracts"`    // 进行中契约数
	CompletionRate     float64 `json:"completion_rate"`     // 完成率
}

// ==================== 服务方法实现 ====================

// CreateContract 创建家庭契约
func (s *contractService) CreateContract(req *ContractCreateRequest) (*ContractResponse, error) {
	// 检查训练营是否存在
	_, err := s.trainingCampsRepo.GetByID(req.CampID)
	if err != nil {
		logger.Error("Training camp not found", "camp_id", req.CampID, "error", err)
		return nil, err
	}

	// 创建契约记录
	contract := &models.FamilyContracts{
		ChildID:           int64(req.ChildID),
		CampID:            int64(req.CampID),
		CreatorUserID:     int64(req.UserID),
		Title:             req.Title,
		GoalDescription:   req.Description,
		RewardDescription: req.RewardContent,
		GoalType:          1, // 连续打卡
		GoalValue:         req.TargetDays,
		CurrentProgress:   0,
		ContractStatus:    1, // 进行中
		StartDate:         time.Now(),
		TargetDate:        time.Now().AddDate(0, 0, req.TargetDays),
	}

	if err := s.familyContractsRepo.Create(contract); err != nil {
		logger.Error("Failed to create family contract", "child_id", req.ChildID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("创建家庭契约失败")
	}

	// 创建见证人记录
	var witnesses []*ContractWitnessInfo
	for _, witnessUserID := range req.WitnessUserIDs {
		witness := &models.ContractWitnesses{
			ContractID:       int64(contract.ID),
			UserID:           int64(witnessUserID),
			WitnessRole:      2, // 见证人
			InvitationStatus: 2, // 已接受
			JoinedAt:         time.Now(),
		}

		if err := s.contractWitnessesRepo.Create(witness); err != nil {
			logger.Error("Failed to create contract witness", "contract_id", contract.ID, "user_id", witnessUserID, "error", err)
			// 见证人创建失败不影响契约创建，继续执行
		} else {
			witnesses = append(witnesses, &ContractWitnessInfo{
				UserID:   witnessUserID,
				UserName: "家长", // 这里需要关联users表获取真实姓名
				Relation: "见证人",
			})
		}
	}

	logger.Info("Family contract created successfully", "contract_id", contract.ID, "child_id", req.ChildID)

	return &ContractResponse{
		FamilyContractsResponse: contract.ToResponse(),
		Witnesses:               witnesses,
	}, nil
}

// GetContracts 获取契约列表
func (s *contractService) GetContracts(childID uint, status int8) ([]*ContractResponse, error) {
	contracts, err := s.familyContractsRepo.GetByChildIDAndStatus(childID, status)
	if err != nil {
		logger.Error("Failed to get contracts", "child_id", childID, "status", status, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取契约列表失败")
	}

	var responses []*ContractResponse
	for _, contract := range contracts {
		// 获取见证人信息
		witnesses, err := s.contractWitnessesRepo.GetByContractID(contract.ID)
		if err != nil {
			logger.Error("Failed to get contract witnesses", "contract_id", contract.ID, "error", err)
			// 见证人获取失败不影响主要功能，继续执行
			witnesses = []*models.ContractWitnesses{}
		}

		// 转换见证人信息
		var witnessInfos []*ContractWitnessInfo
		for _, witness := range witnesses {
			witnessInfos = append(witnessInfos, &ContractWitnessInfo{
				UserID:   uint(witness.UserID),
				UserName: "家长", // 这里需要关联users表获取真实姓名
				Relation: "见证人",
			})
		}

		responses = append(responses, &ContractResponse{
			FamilyContractsResponse: contract.ToResponse(),
			Witnesses:               witnessInfos,
		})
	}

	logger.Info("Contracts retrieved successfully", "child_id", childID, "count", len(responses))
	return responses, nil
}

// GetContractDetail 获取契约详情
func (s *contractService) GetContractDetail(contractID uint) (*ContractDetailResponse, error) {
	// 获取契约基本信息
	contract, err := s.familyContractsRepo.GetByID(contractID)
	if err != nil {
		logger.Error("Failed to get contract", "contract_id", contractID, "error", err)
		return nil, err
	}

	// 获取见证人信息
	witnesses, err := s.contractWitnessesRepo.GetByContractID(contractID)
	if err != nil {
		logger.Error("Failed to get contract witnesses", "contract_id", contractID, "error", err)
		witnesses = []*models.ContractWitnesses{}
	}

	// 转换见证人信息
	var witnessInfos []*ContractWitnessInfo
	for _, witness := range witnesses {
		witnessInfos = append(witnessInfos, &ContractWitnessInfo{
			UserID:   uint(witness.UserID),
			UserName: "家长",
			Relation: "见证人",
		})
	}

	// 获取关联训练营信息
	var campInfo *models.TrainingCampsResponse
	if contract.CampID > 0 {
		camp, err := s.trainingCampsRepo.GetByID(uint(contract.CampID))
		if err != nil {
			logger.Error("Failed to get camp info", "camp_id", contract.CampID, "error", err)
		} else {
			campInfo = camp.ToResponse()
		}
	}

	detail := &ContractDetailResponse{
		ContractResponse: &ContractResponse{
			FamilyContractsResponse: contract.ToResponse(),
			Witnesses:               witnessInfos,
		},
		CampInfo: campInfo,
	}

	logger.Info("Contract detail retrieved successfully", "contract_id", contractID)
	return detail, nil
}

// UpdateContractProgress 更新契约进度
func (s *contractService) UpdateContractProgress(contractID uint, progress float64) error {
	contract, err := s.familyContractsRepo.GetByID(contractID)
	if err != nil {
		logger.Error("Failed to get contract for progress update", "contract_id", contractID, "error", err)
		return err
	}

	// 更新进度
	contract.CurrentProgress = int(progress)
	if progress >= 100.0 {
		contract.ContractStatus = 2 // 已完成
		contract.CompletedDate = time.Now()
	}

	if err := s.familyContractsRepo.Update(contractID, contract); err != nil {
		logger.Error("Failed to update contract progress", "contract_id", contractID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新契约进度失败")
	}

	logger.Info("Contract progress updated successfully", "contract_id", contractID, "progress", progress)
	return nil
}

// CompleteContract 完成契约
func (s *contractService) CompleteContract(contractID uint, witnessUserID uint) (*ContractCompleteResponse, error) {
	contract, err := s.familyContractsRepo.GetByID(contractID)
	if err != nil {
		logger.Error("Failed to get contract for completion", "contract_id", contractID, "error", err)
		return nil, err
	}

	if contract.ContractStatus == 2 {
		return &ContractCompleteResponse{
			Success:      false,
			Message:      "契约已经完成",
			PointsEarned: 0,
		}, nil
	}

	// 更新契约状态
	contract.ContractStatus = 2 // 已完成
	contract.CurrentProgress = 100
	contract.CompletedDate = time.Now()

	if err := s.familyContractsRepo.Update(contractID, contract); err != nil {
		logger.Error("Failed to complete contract", "contract_id", contractID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("完成契约失败")
	}

	// 计算奖励积分（基于目标数值）
	pointsEarned := contract.GoalValue * 20 // 每天20积分
	if pointsEarned > 500 {
		pointsEarned = 500 // 最多500积分
	}

	// 获取参与记录ID
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(uint(contract.CampID), uint(contract.ChildID))
	if err != nil {
		logger.Error("Failed to get participation for contract completion", "contract_id", contractID, "child_id", contract.ChildID, "camp_id", contract.CampID, "error", err)
		// 获取参与记录失败不影响契约完成，只记录错误
	} else {
		// 添加积分奖励
		if err := s.pointsService.AddPoints(participation.ID, 3, contractID, pointsEarned, "完成家庭契约"); err != nil {
			logger.Error("Failed to add points for contract completion", "contract_id", contractID, "error", err)
			// 积分添加失败不影响契约完成，只记录错误
		}
	}

	logger.Info("Contract completed successfully", "contract_id", contractID, "points_earned", pointsEarned)

	return &ContractCompleteResponse{
		Success:      true,
		Message:      "恭喜完成家庭契约！",
		PointsEarned: pointsEarned,
	}, nil
}

// GetContractStats 获取契约统计
func (s *contractService) GetContractStats(childID uint) (*ContractStatsResponse, error) {
	// 获取所有契约
	allContracts, err := s.familyContractsRepo.GetByChildIDAndStatus(childID, 0) // 0表示获取所有状态
	if err != nil {
		logger.Error("Failed to get contracts for stats", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取契约统计失败")
	}

	totalContracts := len(allContracts)
	completedContracts := 0
	activeContracts := 0

	for _, contract := range allContracts {
		if contract.ContractStatus == 2 {
			completedContracts++
		} else if contract.ContractStatus == 1 {
			activeContracts++
		}
	}

	var completionRate float64
	if totalContracts > 0 {
		completionRate = float64(completedContracts) / float64(totalContracts) * 100
	}

	stats := &ContractStatsResponse{
		TotalContracts:     totalContracts,
		CompletedContracts: completedContracts,
		ActiveContracts:    activeContracts,
		CompletionRate:     completionRate,
	}

	logger.Info("Contract stats calculated successfully", "child_id", childID, "total", totalContracts)
	return stats, nil
}
