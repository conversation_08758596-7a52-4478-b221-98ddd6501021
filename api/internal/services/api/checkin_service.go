package api

import (
	"context"
	"encoding/json"
	"fmt"
	"kids-platform/internal/ecode"
	"kids-platform/internal/models"
	"kids-platform/internal/models/dto"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/cache"
	"kids-platform/pkg/database"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"

	"gorm.io/gorm"
)

// CheckinService 打卡服务接口
type CheckinService interface {
	// 创建打卡记录
	CreateCheckin(req *models.CheckinCreateRequest) (*models.CheckinResponse, error)
	// 获取打卡历史
	GetCheckinHistory(childID uint, campID uint, offset, limit int) (*models.CheckinHistoryResponse, error)
	// 获取今日打卡状态
	GetTodayCheckinStatus(childID uint, campID uint) (*models.TodayCheckinStatus, error)
	// 获取打卡统计（指定训练营）
	GetCheckinStats(childID uint, campID uint) (*models.CheckinStatsResponse, error)
	// 获取打卡统计（所有训练营）
	GetCheckinStatsAll(childID uint) (*models.CheckinStatsResponse, error)
	// 获取训练营打卡日历
	GetCampCheckinCalendar(childID uint, campID uint) (*dto.CampCheckinCalendarResponse, error)
	// 初始化训练营打卡日期
	InitializeCampCheckinDates(participationID int64, campID uint, startDate time.Time, totalDays int) error
}

// checkinService 打卡服务实现
type checkinService struct {
	checkinRecordsRepo        repositories.CheckinRecordsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	trainingCampsRepo         repositories.TrainingCampsRepository
	campCheckinDatesRepo      repositories.CampCheckinDatesRepository
	childrenRepo              repositories.ChildrenRepository
	pointsService             PointsService
	growthSystemService       GrowthSystemService
	db                        *gorm.DB
	redisClient               cache.RedisClient
}

// NewCheckinService 创建打卡服务
func NewCheckinService(
	checkinRecordsRepo repositories.CheckinRecordsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	trainingCampsRepo repositories.TrainingCampsRepository,
	campCheckinDatesRepo repositories.CampCheckinDatesRepository,
	childrenRepo repositories.ChildrenRepository,
	pointsService PointsService,
	growthSystemService GrowthSystemService,
	db *gorm.DB,
	redisClient cache.RedisClient,
) CheckinService {
	return &checkinService{
		checkinRecordsRepo:        checkinRecordsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		trainingCampsRepo:         trainingCampsRepo,
		campCheckinDatesRepo:      campCheckinDatesRepo,
		childrenRepo:              childrenRepo,
		pointsService:             pointsService,
		growthSystemService:       growthSystemService,
		db:                        db,
		redisClient:               redisClient,
	}
}

// ==================== 服务方法实现 ====================

// ==================== 服务方法实现 ====================

// CreateCheckin 创建打卡记录
func (s *checkinService) CreateCheckin(req *models.CheckinCreateRequest) (*models.CheckinResponse, error) {
	// 1. 解析打卡日期（支持指定日期打卡）
	var checkinDate time.Time
	var err error

	if req.CheckinDate != "" {
		// 使用指定的打卡日期
		checkinDate, err = time.Parse("2006-01-02", req.CheckinDate)
		if err != nil {
			logger.Error("Invalid checkin date format", "checkin_date", req.CheckinDate, "error", err)
			return nil, errcode.ErrValidation.WithDetails("打卡日期格式错误，请使用YYYY-MM-DD格式")
		}
	} else {
		// 默认使用今天
		today := time.Now().Format("2006-01-02")
		checkinDate, _ = time.Parse("2006-01-02", today)
	}

	// 2. 获取用户训练营参与记录
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(req.CampID, req.ChildID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			logger.Error("User camp participation not found", "child_id", req.ChildID, "camp_id", req.CampID)
			return nil, errcode.ErrValidation.WithDetails("用户未参与该训练营")
		}
		logger.Error("Failed to get user camp participation", "child_id", req.ChildID, "camp_id", req.CampID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取训练营参与记录失败")
	}

	// 3. 使用分布式锁防止重复打卡
	lockKey := fmt.Sprintf("checkin_lock:%d:%s:%d", req.ChildID, checkinDate.Format("2006-01-02"), participation.ID)
	lockAcquired, err := s.acquireDistributedLock(lockKey, 30) // 30秒超时
	if err != nil {
		logger.Error("Failed to acquire distributed lock", "child_id", req.ChildID, "lock_key", lockKey, "error", err)
		return nil, errcode.ErrInternalServer.WithDetails("系统繁忙，请稍后重试")
	}
	if !lockAcquired {
		logger.Warn("Failed to acquire lock, possible concurrent checkin", "child_id", req.ChildID, "checkin_date", checkinDate.Format("2006-01-02"))
		return nil, ecode.ErrCheckinAlreadyExists.WithDetails("打卡请求处理中，请稍后重试")
	}
	defer s.releaseDistributedLock(lockKey)

	// 4. 如果是补卡，验证补卡次数限制
	if req.Status == 2 { // 2表示补卡
		usedCount, totalCount, err := s.childrenRepo.GetMakeupInfo(req.ChildID)
		if err != nil {
			logger.Error("Failed to get makeup info", "child_id", req.ChildID, "error", err)
			return nil, errcode.ErrDatabase.WithDetails("获取补卡信息失败")
		}

		if totalCount < 1 {
			logger.Warn("No makeup cards available", "child_id", req.ChildID, "available_count", totalCount, "used_count", usedCount)
			return nil, ecode.ErrMakeupCheckinLimitExceeded.WithDetails(fmt.Sprintf("补卡次数不足，当前可用：%d次", totalCount))
		}

		logger.Info("Makeup validation passed", "child_id", req.ChildID, "available_count", totalCount, "used_count", usedCount)
	}

	// 5. 检查指定日期是否已打卡（基于participation_id）
	existing, err := s.checkinRecordsRepo.GetByChildAndDateAndParticipation(req.ChildID, checkinDate, int64(participation.ID))
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("Failed to check existing checkin", "child_id", req.ChildID, "date", checkinDate.Format("2006-01-02"), "participation_id", participation.ID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("检查打卡状态失败")
	}

	// 如果该日期已经打卡，根据打卡类型处理
	if existing != nil {
		// 如果是补卡，允许覆盖之前的记录
		if req.Status == 2 { // 2表示补卡
			logger.Info("Makeup checkin will override existing record", "child_id", req.ChildID, "date", checkinDate.Format("2006-01-02"), "participation_id", participation.ID, "existing_checkin_id", existing.ID)
			// 继续执行，允许补卡覆盖
		} else {
			// 正常打卡时，如果已经打卡则返回已存在的记录
			logger.Info("Checkin already exists for date", "child_id", req.ChildID, "date", checkinDate.Format("2006-01-02"), "participation_id", participation.ID, "checkin_id", existing.ID)

			return &models.CheckinResponse{
				CheckinRecordsResponse: existing.ToResponse(),
				PointsEarned:           existing.PointsEarned,
				Message:                "今天已经打卡了",
				Status:                 "already_checked_in", // 业务状态标识
			}, nil
		}
	}

	// 4. 计算获得积分（基础积分 + 时长奖励）
	pointsEarned := s.calculatePoints(req.PracticeDuration, req.JumpCount1min)

	// 5. 创建或更新打卡记录
	var checkinRecord *models.CheckinRecords
	if existing != nil && req.Status == 2 { // 补卡且已存在记录，更新现有记录
		checkinRecord = existing
		// 更新记录字段
		checkinRecord.PracticeDuration = req.PracticeDuration
		checkinRecord.JumpCount1min = req.JumpCount1min
		checkinRecord.JumpCountContinuous = req.JumpCountContinuous
		checkinRecord.FeelingText = req.FeelingText
		checkinRecord.FeelingScore = req.FeelingScore
		checkinRecord.PointsEarned = pointsEarned
		checkinRecord.Status = req.Status // 更新为补卡状态
	} else {
		// 创建新的打卡记录
		checkinRecord = &models.CheckinRecords{
			ChildID:             int64(req.ChildID),
			CampID:              int64(req.CampID),
			UserID:              int64(req.UserID),
			ParticipationID:     int64(participation.ID), // 设置参与记录ID
			CheckinDate:         checkinDate,             // 使用解析后的打卡日期
			PracticeDuration:    req.PracticeDuration,
			JumpCount1min:       req.JumpCount1min,
			JumpCountContinuous: req.JumpCountContinuous,
			FeelingText:         req.FeelingText,
			FeelingScore:        req.FeelingScore,
			PointsEarned:        pointsEarned,
			Status:              req.Status, // 使用请求中的状态（1:正常 2:补卡）
		}
	}

	// 6. 处理照片数组（转换为JSON字符串存储到数据库）
	photosJSON := ""
	if len(req.Photos) > 0 {
		// 将[]string转换为JSON字符串存储
		photosBytes, err := json.Marshal(req.Photos)
		if err != nil {
			logger.Error("Failed to marshal photos to JSON", "photos", req.Photos, "error", err)
			return nil, errcode.ErrValidation.WithDetails("照片数据格式错误")
		}
		photosJSON = string(photosBytes)
	} else {
		// 空数组情况，存储空的JSON数组字符串
		photosJSON = "[]"
	}
	checkinRecord.Photos = photosJSON

	// 7. 使用事务处理多表更新，确保数据一致性
	err = s.executeCheckinTransaction(checkinRecord, participation, checkinDate)
	if err != nil {
		logger.Error("Failed to execute checkin transaction", "child_id", req.ChildID, "participation_id", participation.ID, "error", err)
		return nil, err
	}

	// 8. 更新训练营参与进度
	if err := s.updateCampProgressWithParticipation(participation, pointsEarned, req.PracticeDuration); err != nil {
		logger.Error("Failed to update camp progress", "child_id", req.ChildID, "camp_id", req.CampID, "participation_id", participation.ID, "error", err)
		// 进度更新失败不影响打卡成功，只记录错误
	}

	// 9. 处理打卡后的系统更新（积分、成长轨迹、勋章等）
	if err := s.handlePostCheckinUpdates(checkinRecord, participation); err != nil {
		logger.Error("Failed to handle post-checkin updates", "checkin_id", checkinRecord.ID, "error", err)
		// 这些更新失败不影响打卡成功，只记录错误
	}

	logger.Info("Checkin created successfully",
		"child_id", req.ChildID,
		"camp_id", req.CampID,
		"participation_id", participation.ID,
		"checkin_date", checkinDate.Format("2006-01-02"),
		"points", pointsEarned,
		"status", req.Status)

	return &models.CheckinResponse{
		CheckinRecordsResponse: checkinRecord.ToResponse(),
		PointsEarned:           pointsEarned,
		Message:                "打卡成功！",
		Status:                 "success", // 业务状态标识
	}, nil
}

// GetCheckinHistory 获取打卡历史
func (s *checkinService) GetCheckinHistory(childID uint, campID uint, offset, limit int) (*models.CheckinHistoryResponse, error) {
	records, total, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, offset, limit)
	if err != nil {
		logger.Error("Failed to get checkin history", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡历史失败")
	}

	var responseList []*models.CheckinRecordsResponse
	for _, record := range records {
		responseList = append(responseList, record.ToResponse())
	}

	logger.Info("Checkin history retrieved successfully", "child_id", childID, "camp_id", campID, "total", total)
	return &models.CheckinHistoryResponse{
		List:  responseList,
		Total: total,
	}, nil
}

// GetTodayCheckinStatus 获取今日打卡状态
func (s *checkinService) GetTodayCheckinStatus(childID uint, campID uint) (*models.TodayCheckinStatus, error) {
	today := time.Now().Format("2006-01-02")
	todayDate, _ := time.Parse("2006-01-02", today)

	// 优化：首先使用 last_checkin_date 字段快速检查是否今日已打卡
	child, err := s.childrenRepo.GetByID(childID)
	if err != nil {
		logger.Error("Failed to get child for checkin status", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取孩子信息失败")
	}

	// 检查最后打卡日期是否是今天
	hasCheckedInToday := false
	if child.LastCheckinDate != nil {
		lastCheckinDateStr := child.LastCheckinDate.Format("2006-01-02")
		hasCheckedInToday = lastCheckinDateStr == today
	}

	status := &models.TodayCheckinStatus{
		HasCheckedIn: hasCheckedInToday,
	}

	// 如果今日已打卡，获取具体的打卡记录详情（仅在需要时查询）
	if hasCheckedInToday {
		checkin, err := s.checkinRecordsRepo.GetByChildAndDateByCampID(childID, campID, todayDate)
		if err != nil {
			// 检查是否为"数据不存在"错误
			if errCodeErr, ok := err.(*errcode.Error); ok && errCodeErr.Code() == errcode.ErrDataNotFound.Code() {
				// 可能是其他训练营的打卡，当前训练营未打卡
				status.HasCheckedIn = false
			} else {
				// 其他错误为系统错误
				logger.Error("Failed to get today checkin details", "child_id", childID, "camp_id", campID, "error", err)
				return nil, errcode.ErrDatabase.WithDetails("获取今日打卡详情失败")
			}
		} else {
			status.CheckinData = checkin.ToResponse()
		}
	}

	return status, nil
}

// GetCheckinStats 获取打卡统计
func (s *checkinService) GetCheckinStats(childID uint, campID uint) (*models.CheckinStatsResponse, error) {
	// 获取指定训练营的所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &models.CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats calculated successfully", "child_id", childID, "camp_id", campID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// GetCheckinStatsAll 获取打卡统计（所有训练营）
func (s *checkinService) GetCheckinStatsAll(childID uint) (*models.CheckinStatsResponse, error) {
	// 获取所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildID(childID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &models.CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats (all camps) calculated successfully", "child_id", childID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// ==================== 辅助方法 ====================

// calculatePoints 计算打卡获得的积分
func (s *checkinService) calculatePoints(duration int, jumpCount int) int {
	basePoints := 10                  // 基础积分
	durationBonus := duration / 5 * 2 // 每5分钟额外2积分
	jumpBonus := jumpCount / 50 * 5   // 每50个跳绳额外5积分

	total := basePoints + durationBonus + jumpBonus
	if total > 100 {
		total = 100 // 单次打卡最多100积分
	}

	return total
}

// updateCampProgress 更新训练营参与进度
func (s *checkinService) updateCampProgress(childID uint, campID uint, pointsEarned int) error {
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		return err
	}

	// 更新统计数据
	participation.TotalCheckins++
	participation.TotalStudyMinutes += pointsEarned // 这里需要传入实际的学习时长

	// 计算进度百分比
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err == nil {
		participation.ProgressPercentage = float64(participation.TotalCheckins) / float64(camp.DurationDays) * 100
		if participation.ProgressPercentage > 100 {
			participation.ProgressPercentage = 100
		}
	}

	// 更新当前天数
	if participation.TotalCheckins > participation.CurrentDay {
		participation.CurrentDay = participation.TotalCheckins
	}

	return s.userCampParticipationRepo.Update(participation.ID, participation)
}

// updateCampProgressWithParticipation 使用已有的参与记录更新训练营进度
func (s *checkinService) updateCampProgressWithParticipation(participation *models.UserCampParticipations, pointsEarned int, studyMinutes int) error {
	// 更新统计数据
	participation.TotalCheckins++
	participation.TotalStudyMinutes += studyMinutes // 使用实际的学习时长

	// 计算进度百分比
	camp, err := s.trainingCampsRepo.GetByID(uint(participation.CampID))
	if err == nil {
		participation.ProgressPercentage = float64(participation.TotalCheckins) / float64(camp.DurationDays) * 100
		if participation.ProgressPercentage > 100 {
			participation.ProgressPercentage = 100
		}
	}

	// 更新当前天数
	if participation.TotalCheckins > participation.CurrentDay {
		participation.CurrentDay = participation.TotalCheckins
	}

	return s.userCampParticipationRepo.Update(participation.ID, participation)
}

// calculateConsecutiveDays 计算连续打卡天数
func (s *checkinService) calculateConsecutiveDays(records []*models.CheckinRecords) int {
	if len(records) == 0 {
		return 0
	}

	// 按日期排序（最新的在前）
	// 这里简化处理，假设records已经按日期排序
	consecutive := 1

	for i := 0; i < len(records)-1; i++ {
		current := records[i].CheckinDate
		next := records[i+1].CheckinDate

		// 检查是否连续
		if current.Sub(next).Hours() <= 24 {
			consecutive++
		} else {
			break
		}
	}

	return consecutive
}

// calculateMaxConsecutiveDays 计算最大连续天数
func (s *checkinService) calculateMaxConsecutiveDays(records []*models.CheckinRecords) int {
	// 简化实现，返回当前连续天数
	return s.calculateConsecutiveDays(records)
}

// calculateTotalStudyMinutes 计算总学习时长
func (s *checkinService) calculateTotalStudyMinutes(records []*models.CheckinRecords) int {
	total := 0
	for _, record := range records {
		total += record.PracticeDuration
	}
	return total
}

// calculateAverageFeeling 计算平均感受评分
func (s *checkinService) calculateAverageFeeling(records []*models.CheckinRecords) float64 {
	if len(records) == 0 {
		return 0
	}

	total := 0
	for _, record := range records {
		total += int(record.FeelingScore)
	}

	return float64(total) / float64(len(records))
}

// calculateThisWeekCheckins 计算本周打卡次数
func (s *checkinService) calculateThisWeekCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	weekStart := now.AddDate(0, 0, -int(now.Weekday()))

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(weekStart) {
			count++
		}
	}

	return count
}

// calculateThisMonthCheckins 计算本月打卡次数
func (s *checkinService) calculateThisMonthCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(monthStart) {
			count++
		}
	}

	return count
}

// ==================== 新增方法：训练营打卡日历 ====================

// GetCampCheckinCalendar 获取训练营打卡日历
func (s *checkinService) GetCampCheckinCalendar(childID uint, campID uint) (*dto.CampCheckinCalendarResponse, error) {
	// 1. 获取参与记录
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营参与记录不存在")
		}
		logger.Error("获取训练营参与记录失败", "error", err, "childID", childID, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 2. 获取训练营信息
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营不存在")
		}
		logger.Error("获取训练营信息失败", "error", err, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 3. 获取打卡日期列表
	checkinDates, err := s.campCheckinDatesRepo.GetByParticipationID(int64(participation.ID))
	if err != nil {
		logger.Error("获取打卡日期列表失败", "error", err, "participationID", participation.ID)
		return nil, errcode.ErrInternalServer
	}

	// 4. 如果没有打卡日期数据，初始化
	if len(checkinDates) == 0 {
		err = s.InitializeCampCheckinDates(int64(participation.ID), campID, participation.ParticipationDate, camp.DurationDays)
		if err != nil {
			logger.Error("初始化打卡日期失败", "error", err, "participationID", participation.ID)
			return nil, errcode.ErrInternalServer
		}

		// 重新获取打卡日期列表
		checkinDates, err = s.campCheckinDatesRepo.GetByParticipationID(int64(participation.ID))
		if err != nil {
			logger.Error("重新获取打卡日期列表失败", "error", err, "participationID", participation.ID)
			return nil, errcode.ErrInternalServer
		}
	}

	// 5. 获取打卡记录详情
	checkinRecords, _, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, 0, 1000)
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("获取打卡记录失败", "error", err, "childID", childID, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 6. 构建响应数据
	var recordsList []*models.CheckinRecords
	if checkinRecords != nil {
		recordsList = checkinRecords
	}
	return s.buildCampCheckinCalendarResponse(camp, participation, checkinDates, recordsList), nil
}

// InitializeCampCheckinDates 初始化训练营打卡日期
func (s *checkinService) InitializeCampCheckinDates(participationID int64, campID uint, startDate time.Time, totalDays int) error {
	// 生成打卡日期列表
	checkinDatesList := make([]*models.CampCheckinDates, 0, totalDays)

	for i := 0; i < totalDays; i++ {
		checkinDate := startDate.AddDate(0, 0, i)

		checkinDateRecord := &models.CampCheckinDates{
			ParticipationID: participationID,
			DayNumber:       i + 1,
			CheckinDate:     checkinDate,
			DateType:        models.DateTypeNormal, // 暂时都设为正常打卡日
			Status:          models.CheckinStatusPending,
			IsMakeupAllowed: 1,
		}

		checkinDatesList = append(checkinDatesList, checkinDateRecord)
	}

	// 批量创建
	return s.campCheckinDatesRepo.BatchCreate(checkinDatesList)
}

// buildCampCheckinCalendarResponse 构建训练营打卡日历响应
func (s *checkinService) buildCampCheckinCalendarResponse(
	camp *models.TrainingCamps,
	participation *models.UserCampParticipations,
	checkinDates []*models.CampCheckinDates,
	checkinRecords []*models.CheckinRecords,
) *dto.CampCheckinCalendarResponse {
	// 构建打卡记录映射
	checkinRecordMap := make(map[string]*models.CheckinRecords)
	for _, record := range checkinRecords {
		dateKey := record.CheckinDate.Format("2006-01-02")
		checkinRecordMap[dateKey] = record
	}

	// 构建日历日期信息
	dates := make([]*dto.CalendarDateInfo, 0, len(checkinDates))
	currentDay := 1

	for _, dateInfo := range checkinDates {
		dateKey := dateInfo.CheckinDate.Format("2006-01-02")

		// 获取对应的打卡记录
		var checkinData *models.CheckinRecordsResponse
		if record, exists := checkinRecordMap[dateKey]; exists {
			checkinData = record.ToResponse()
		}

		// 构建日期信息
		calendarDate := &dto.CalendarDateInfo{
			Date:        dateKey,
			DayNumber:   dateInfo.DayNumber,
			Status:      dateInfo.GetStatusText(),
			CanMakeup:   dateInfo.CanMakeup(),
			CheckinData: checkinData,
		}

		dates = append(dates, calendarDate)

		// 更新当前天数（找到今天或最近的未完成日期）
		if dateInfo.IsToday() || (dateInfo.Status == models.CheckinStatusPending && dateInfo.CheckinDate.Before(time.Now())) {
			currentDay = dateInfo.DayNumber
		}
	}

	// 计算开始和结束日期
	var startDate, endDate string
	if len(checkinDates) > 0 {
		startDate = checkinDates[0].CheckinDate.Format("2006-01-02")
		endDate = checkinDates[len(checkinDates)-1].CheckinDate.Format("2006-01-02")
	}

	return &dto.CampCheckinCalendarResponse{
		CampInfo: &dto.CampInfo{
			ID:        camp.ID,
			Title:     camp.Title,
			Subtitle:  camp.Subtitle,
			TotalDays: camp.DurationDays,
		},
		CalendarData: &dto.CalendarData{
			TotalDays:  camp.DurationDays,
			CurrentDay: currentDay,
			StartDate:  startDate,
			EndDate:    endDate,
			Dates:      dates,
		},
		MakeupInfo: s.getMakeupInfoForChild(uint(participation.ChildID)),
	}
}

// getMakeupInfoForChild 获取孩子的补卡信息
func (s *checkinService) getMakeupInfoForChild(childID uint) *dto.MakeupInfo {
	usedCount, totalCount, err := s.childrenRepo.GetMakeupInfo(childID)
	if err != nil {
		logger.Error("Failed to get makeup info for child", "child_id", childID, "error", err)
		// 返回默认值
		return &dto.MakeupInfo{
			TotalCount:     0, // 默认可用次数
			UsedCount:      0, // 默认已使用次数
			AvailableCount: 0, // 默认可用次数
		}
	}

	// totalCount 本身就是可用次数
	availableCount := int(totalCount)
	if availableCount < 0 {
		availableCount = 0
	}

	return &dto.MakeupInfo{
		TotalCount:     int(totalCount), // 可用补卡次数
		UsedCount:      int(usedCount),  // 已使用补卡次数
		AvailableCount: availableCount,  // 当前可用次数（等于totalCount）
	}
}

// executeCheckinTransaction 执行打卡事务，确保多表更新的原子性
func (s *checkinService) executeCheckinTransaction(
	checkinRecord *models.CheckinRecords,
	participation *models.UserCampParticipations,
	checkinDate time.Time) error {
	// 使用数据库事务确保数据一致性
	return database.WithTransaction(s.db, func(tx *gorm.DB) error {
		// 1. 创建或更新打卡记录
		if checkinRecord.ID == 0 {
			// 新记录，创建
			if err := tx.Create(checkinRecord).Error; err != nil {
				logger.Error("Failed to create checkin record in transaction", "error", err)
				return errcode.ErrDatabase.WithDetails("创建打卡记录失败")
			}
		} else {
			// 已存在记录，更新
			if err := tx.Save(checkinRecord).Error; err != nil {
				logger.Error("Failed to update checkin record in transaction", "checkin_id", checkinRecord.ID, "error", err)
				return errcode.ErrDatabase.WithDetails("更新打卡记录失败")
			}
		}

		// 2. 更新 camp_checkin_dates 表状态
		err := s.updateCampCheckinDateStatus(tx, int64(participation.ID), checkinDate, checkinRecord.ID, checkinRecord.Status)
		if err != nil {
			logger.Error("Failed to update camp checkin date status", "participation_id", participation.ID, "date", checkinDate, "error", err)
			return err
		}

		// 3. 如果是补卡且是新记录，更新孩子的补卡次数
		if checkinRecord.Status == 2 { // 2表示补卡
			// 检查是否是新创建的记录（ID为0表示刚创建）
			// 如果是更新现有记录，则不重复扣除补卡次数
			var isNewRecord bool
			if checkinRecord.CreatedAt.Equal(checkinRecord.UpdatedAt) {
				isNewRecord = true
			}

			if isNewRecord {
				// 使用补卡：makeup_used_count+1, makeup_total_count-1
				if err := s.childrenRepo.UseMakeupCard(uint(checkinRecord.ChildID)); err != nil {
					logger.Error("Failed to use makeup card", "child_id", checkinRecord.ChildID, "error", err)
					return errcode.ErrDatabase.WithDetails("使用补卡失败")
				}

				logger.Info("Makeup card used successfully", "child_id", checkinRecord.ChildID)
			} else {
				logger.Info("Updating existing makeup record, no need to use makeup card again", "child_id", checkinRecord.ChildID, "checkin_id", checkinRecord.ID)
			}
		}

		// 4. 更新训练营参与进度
		err = s.updateParticipationProgress(tx, participation, checkinRecord)
		if err != nil {
			logger.Error("Failed to update participation progress", "participation_id", participation.ID, "error", err)
			return err
		}

		logger.Info("Checkin transaction completed successfully",
			"checkin_id", checkinRecord.ID,
			"participation_id", participation.ID,
			"child_id", checkinRecord.ChildID)
		return nil
	})
}

// updateCampCheckinDateStatus 更新训练营打卡日期状态
func (s *checkinService) updateCampCheckinDateStatus(tx *gorm.DB, participationID int64, checkinDate time.Time, checkinID uint, status int8) error {
	// 查找对应的打卡日期记录
	var campCheckinDate models.CampCheckinDates
	dateStr := checkinDate.Format("2006-01-02")

	err := tx.Where("participation_id = ? AND DATE(checkin_date) = ?", participationID, dateStr).
		First(&campCheckinDate).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warn("Camp checkin date not found", "participation_id", participationID, "date", dateStr)
			return errcode.ErrDataNotFound.WithDetails("打卡日期记录不存在")
		}
		return errcode.ErrDatabase.WithDetails("查询打卡日期记录失败")
	}

	// 更新状态和关联的打卡记录ID
	updates := map[string]interface{}{
		"status":            status,
		"checkin_record_id": checkinID,
	}

	err = tx.Model(&campCheckinDate).Updates(updates).Error
	if err != nil {
		return errcode.ErrDatabase.WithDetails("更新打卡日期状态失败")
	}

	logger.Info("Camp checkin date status updated",
		"participation_id", participationID,
		"date", dateStr,
		"status", status,
		"checkin_id", checkinID)
	return nil
}

// updateParticipationProgress 更新训练营参与进度
func (s *checkinService) updateParticipationProgress(tx *gorm.DB, participation *models.UserCampParticipations, checkinRecord *models.CheckinRecords) error {
	// 获取训练营信息以获取总天数
	camp, err := s.trainingCampsRepo.GetByID(uint(participation.CampID))
	if err != nil {
		logger.Error("Failed to get training camp for progress calculation", "camp_id", participation.CampID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取训练营信息失败")
	}

	// 计算新的进度数据
	newTotalCheckins := participation.TotalCheckins + 1
	newCurrentDay := participation.CurrentDay + 1

	// 计算连续打卡天数
	newConsecutiveDays := participation.ConsecutiveDays + 1

	// 计算进度百分比（使用训练营的 duration_days 作为基准）
	var newProgressPercentage float64
	if camp.DurationDays > 0 {
		newProgressPercentage = float64(newTotalCheckins) / float64(camp.DurationDays) * 100
		if newProgressPercentage > 100 {
			newProgressPercentage = 100 // 确保不超过100%
		}
	}

	// 累加学习时长
	newTotalStudyMinutes := participation.TotalStudyMinutes + checkinRecord.PracticeDuration

	// 更新参与记录
	updates := map[string]interface{}{
		"total_checkins":      newTotalCheckins,
		"current_day":         newCurrentDay,
		"consecutive_days":    newConsecutiveDays,
		"progress_percentage": newProgressPercentage,
		"total_study_minutes": newTotalStudyMinutes,
		"last_checkin_date":   checkinRecord.CheckinDate.Format("2006-01-02"), // 转换为日期格式
	}

	if err := tx.Model(participation).Updates(updates).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新训练营参与进度失败")
	}

	logger.Info("Participation progress updated",
		"participation_id", participation.ID,
		"total_checkins", newTotalCheckins,
		"progress_percentage", newProgressPercentage)
	return nil
}

// handlePostCheckinUpdates 处理打卡后的系统更新
func (s *checkinService) handlePostCheckinUpdates(checkinRecord *models.CheckinRecords, participation *models.UserCampParticipations) error {
	// 1. 更新孩子表的最后打卡日期
	err := s.updateChildLastCheckinDate(uint(checkinRecord.ChildID), checkinRecord.CheckinDate)
	if err != nil {
		logger.Error("Failed to update child last checkin date",
			"child_id", checkinRecord.ChildID,
			"checkin_date", checkinRecord.CheckinDate,
			"error", err)
		// 更新失败不影响整体流程
	}

	// 2. 添加积分记录
	err = s.pointsService.AddPoints(
		uint(checkinRecord.ParticipationID), // participationID
		1,                                   // sourceType: 1表示打卡获得积分
		checkinRecord.ID,                    // sourceID: 打卡记录ID
		checkinRecord.PointsEarned,          // points
		"训练营打卡获得积分",                         // description
	)
	if err != nil {
		logger.Error("Failed to add points for checkin",
			"checkin_id", checkinRecord.ID,
			"child_id", checkinRecord.ChildID,
			"points", checkinRecord.PointsEarned,
			"error", err)
		// 积分添加失败不影响整体流程
	}

	// 2. 调用成长系统处理打卡完成事件
	err = s.growthSystemService.HandleCheckinComplete(
		uint(checkinRecord.ChildID),
		uint(checkinRecord.CampID),
		checkinRecord.ID,
	)
	if err != nil {
		logger.Error("Failed to handle checkin complete in growth system",
			"checkin_id", checkinRecord.ID,
			"child_id", checkinRecord.ChildID,
			"camp_id", checkinRecord.CampID,
			"error", err)
		// 成长系统更新失败不影响整体流程
	}

	logger.Info("Post-checkin updates completed",
		"checkin_id", checkinRecord.ID,
		"child_id", checkinRecord.ChildID,
		"points_earned", checkinRecord.PointsEarned)
	return nil
}

// acquireDistributedLock 获取分布式锁
func (s *checkinService) acquireDistributedLock(lockKey string, timeoutSeconds int) (bool, error) {
	// 检查键是否已存在
	exists, err := s.redisClient.Exists(context.Background(), lockKey)
	if err != nil {
		return false, err
	}
	if exists > 0 {
		return false, nil // 锁已被占用
	}

	// 设置锁，带过期时间
	err = s.redisClient.Set(context.Background(), lockKey, "locked", time.Duration(timeoutSeconds)*time.Second)
	if err != nil {
		return false, err
	}
	return true, nil
}

// releaseDistributedLock 释放分布式锁
func (s *checkinService) releaseDistributedLock(lockKey string) error {
	return s.redisClient.Delete(context.Background(), lockKey)
}

// updateChildLastCheckinDate 更新孩子的最后打卡日期
func (s *checkinService) updateChildLastCheckinDate(childID uint, checkinDate time.Time) error {
	// 获取孩子记录
	child, err := s.childrenRepo.GetByID(childID)
	if err != nil {
		logger.Error("Failed to get child for updating last checkin date", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取孩子信息失败")
	}

	// 更新最后打卡日期
	checkinDateOnly := time.Date(checkinDate.Year(), checkinDate.Month(), checkinDate.Day(), 0, 0, 0, 0, time.UTC)
	child.LastCheckinDate = &checkinDateOnly

	// 保存更新
	if err := s.childrenRepo.Update(childID, child); err != nil {
		logger.Error("Failed to update child last checkin date", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新孩子最后打卡日期失败")
	}

	logger.Info("Child last checkin date updated successfully", "child_id", childID, "date", checkinDateOnly)
	return nil
}
