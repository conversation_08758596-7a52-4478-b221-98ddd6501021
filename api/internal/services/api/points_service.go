package api

import (
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// PointsService 积分服务接口
type PointsService interface {
	// 基于participation_id的核心方法
	GetPointsByParticipationID(participationID uint) (*PointsStatsResponse, error)
	AddPoints(participationID uint, sourceType int8, sourceID uint, points int, description string) error
	DeductPoints(participationID uint, points int, description string) error

	// 训练营级别操作
	GetCampRanking(campID uint, limit int) (*LeaderboardResponse, error)
	GetParticipationRankInCamp(participationID uint, campID uint) (int, error)
	GetCampStatistics(campID uint) (*CampStatisticsResponse, error)

	// 维护操作
	UpdateCampRankings(campID uint) error
	RecalculateWeeklyPoints(campID uint) error
	RecalculateMonthlyPoints(campID uint) error
}

// pointsService 积分服务实现
type pointsService struct {
	childPointsRepo  repositories.ChildPointsRepository
	pointRecordsRepo repositories.PointRecordsRepository
}

// NewPointsService 创建积分服务
func NewPointsService(
	childPointsRepo repositories.ChildPointsRepository,
	pointRecordsRepo repositories.PointRecordsRepository,
) PointsService {
	return &pointsService{
		childPointsRepo:  childPointsRepo,
		pointRecordsRepo: pointRecordsRepo,
	}
}

// ==================== 响应结构定义 ====================

// PointsStatsResponse 积分统计响应
type PointsStatsResponse struct {
	TotalPoints       int64     `json:"total_points"`        // 总积分
	WeekPoints        int       `json:"week_points"`         // 本周积分
	MonthPoints       int       `json:"month_points"`        // 本月积分
	TotalCheckins     int       `json:"total_checkins"`      // 总打卡次数
	ContinuousDays    int       `json:"continuous_days"`     // 连续打卡天数
	MaxContinuousDays int       `json:"max_continuous_days"` // 最大连续天数
	WeekRank          int       `json:"week_rank"`           // 本周排名
	LastWeekRank      int       `json:"last_week_rank"`      // 上周排名
	LastCheckinDate   time.Time `json:"last_checkin_date"`   // 最后打卡日期
	CurrentLevel      int       `json:"current_level"`       // 当前等级
	NextLevelPoints   int64     `json:"next_level_points"`   // 下一等级所需积分
}

// PointsHistoryResponse 积分历史响应
type PointsHistoryResponse struct {
	List  []*models.PointRecordsResponse `json:"list"`  // 积分记录列表
	Total int64                          `json:"total"` // 总数
}

// LeaderboardResponse 排行榜响应
type LeaderboardResponse struct {
	WeeklyRanking  []*LeaderboardItem `json:"weekly_ranking"`  // 周排行榜
	MonthlyRanking []*LeaderboardItem `json:"monthly_ranking"` // 月排行榜
}

// LeaderboardItem 排行榜项目
type LeaderboardItem struct {
	ParticipationID uint   `json:"participation_id"` // 参与记录ID
	ChildID         uint   `json:"child_id"`         // 孩子ID
	ChildName       string `json:"child_name"`       // 孩子姓名
	Points          int64  `json:"points"`           // 积分
	Rank            int    `json:"rank"`             // 排名
	ContinuousDays  int    `json:"continuous_days"`  // 连续天数
}

// CampStatisticsResponse 训练营统计响应
type CampStatisticsResponse struct {
	CampID             uint    `json:"camp_id"`              // 训练营ID
	TotalParticipants  int     `json:"total_participants"`   // 总参与人数
	ActiveParticipants int     `json:"active_participants"`  // 活跃参与人数
	AveragePoints      float64 `json:"average_points"`       // 平均积分
	TotalPoints        int64   `json:"total_points"`         // 总积分
	TopPerformerPoints int64   `json:"top_performer_points"` // 最高积分
	CheckinRate        float64 `json:"checkin_rate"`         // 打卡率
	CompletionRate     float64 `json:"completion_rate"`      // 完成率
}

// ==================== 服务方法实现 ====================

// GetPointsByParticipationID 根据参与记录ID获取积分统计
func (s *pointsService) GetPointsByParticipationID(participationID uint) (*PointsStatsResponse, error) {
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			// 返回默认的空统计
			return &PointsStatsResponse{
				TotalPoints:       0,
				WeekPoints:        0,
				MonthPoints:       0,
				TotalCheckins:     0,
				ContinuousDays:    0,
				MaxContinuousDays: 0,
				WeekRank:          0,
				LastWeekRank:      0,
				LastCheckinDate:   time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
				CurrentLevel:      1,
				NextLevelPoints:   100,
			}, nil
		}
		logger.Error("Failed to get child points", "participation_id", participationID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取积分统计失败")
	}

	// 计算当前等级和下一等级所需积分
	currentLevel := s.calculateLevel(childPoints.TotalPoints)
	nextLevelPoints := s.calculateNextLevelPoints(currentLevel)

	stats := &PointsStatsResponse{
		TotalPoints:       childPoints.TotalPoints,
		WeekPoints:        childPoints.WeekPoints,
		MonthPoints:       childPoints.MonthPoints,
		TotalCheckins:     childPoints.TotalCheckins,
		ContinuousDays:    childPoints.ContinuousDays,
		MaxContinuousDays: childPoints.MaxContinuousDays,
		WeekRank:          childPoints.WeekRank,
		LastWeekRank:      childPoints.LastWeekRank,
		LastCheckinDate:   childPoints.LastCheckinDate,
		CurrentLevel:      currentLevel,
		NextLevelPoints:   nextLevelPoints,
	}

	logger.Info("Points stats retrieved successfully", "participation_id", participationID, "total_points", stats.TotalPoints)
	return stats, nil
}

// AddPoints 添加积分
func (s *pointsService) AddPoints(participationID uint, sourceType int8, sourceID uint, points int, description string) error {
	// 获取参与记录积分
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		logger.Error("Failed to get child points", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取参与记录积分失败")
	}

	// 更新积分
	newTotalPoints := childPoints.TotalPoints + int64(points)

	// 创建积分变动记录
	pointRecord := &models.PointRecords{
		ChildID:         childPoints.ChildID,
		ParticipationID: int64(participationID),
		SourceType:      sourceType,
		SourceID:        int64(sourceID),
		PointsChange:    points,
		PointsAfter:     newTotalPoints,
		Description:     description,
	}

	if err := s.pointRecordsRepo.Create(pointRecord); err != nil {
		logger.Error("Failed to create point record", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("创建积分记录失败")
	}

	// 更新孩子积分统计
	childPoints.TotalPoints = newTotalPoints
	childPoints.WeekPoints += points
	childPoints.MonthPoints += points

	// 如果是打卡获得积分，更新最后打卡日期
	if sourceType == 1 { // 1表示打卡获得积分
		childPoints.LastCheckinDate = time.Now()
	}

	if err := s.childPointsRepo.Update(childPoints.ID, childPoints); err != nil {
		logger.Error("Failed to update child points", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新参与记录积分失败")
	}

	logger.Info("Points added successfully", "participation_id", participationID, "points", points, "total", newTotalPoints)
	return nil
}

// DeductPoints 扣除积分
func (s *pointsService) DeductPoints(participationID uint, points int, description string) error {
	// 获取参与记录积分
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		logger.Error("Failed to get child points", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取参与记录积分失败")
	}

	// 检查积分是否足够
	if childPoints.TotalPoints < int64(points) {
		return errcode.ErrInvalidParam.WithDetails("积分不足")
	}

	// 更新积分
	newTotalPoints := childPoints.TotalPoints - int64(points)

	// 创建积分变动记录
	pointRecord := &models.PointRecords{
		ChildID:         childPoints.ChildID,
		ParticipationID: int64(participationID),
		SourceType:      4, // 4表示积分扣除
		SourceID:        0,
		PointsChange:    -points,
		PointsAfter:     newTotalPoints,
		Description:     description,
	}

	if err := s.pointRecordsRepo.Create(pointRecord); err != nil {
		logger.Error("Failed to create point record", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("创建积分记录失败")
	}

	// 更新孩子积分统计
	childPoints.TotalPoints = newTotalPoints

	if err := s.childPointsRepo.Update(childPoints.ID, childPoints); err != nil {
		logger.Error("Failed to update child points", "participation_id", participationID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新参与记录积分失败")
	}

	logger.Info("Points deducted successfully", "participation_id", participationID, "points", points, "total", newTotalPoints)
	return nil
}

// GetCampRanking 获取训练营排行榜
func (s *pointsService) GetCampRanking(campID uint, limit int) (*LeaderboardResponse, error) {
	// 获取训练营排行榜
	childPoints, err := s.childPointsRepo.GetRankingByCampID(campID, limit)
	if err != nil {
		logger.Error("Failed to get camp ranking", "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取训练营排行榜失败")
	}

	// 转换为响应格式
	items := s.convertToLeaderboardItems(childPoints)

	return &LeaderboardResponse{
		WeeklyRanking:  items, // 这里简化处理，实际可以分别获取周排行和月排行
		MonthlyRanking: items,
	}, nil
}

// GetParticipationRankInCamp 获取参与记录在训练营中的排名
func (s *pointsService) GetParticipationRankInCamp(participationID uint, campID uint) (int, error) {
	rank, err := s.childPointsRepo.GetParticipationRankInCamp(participationID, campID)
	if err != nil {
		logger.Error("Failed to get participation rank", "participation_id", participationID, "camp_id", campID, "error", err)
		return 0, errcode.ErrDatabase.WithDetails("获取训练营排名失败")
	}

	logger.Info("Participation rank retrieved successfully", "participation_id", participationID, "camp_id", campID, "rank", rank)
	return rank, nil
}

// GetCampStatistics 获取训练营统计
func (s *pointsService) GetCampStatistics(campID uint) (*CampStatisticsResponse, error) {
	// 获取训练营所有积分记录
	childPoints, err := s.childPointsRepo.GetByCampID(campID)
	if err != nil {
		logger.Error("Failed to get camp points", "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取训练营积分记录失败")
	}

	// 计算统计数据
	totalParticipants := len(childPoints)
	activeParticipants := 0
	totalPoints := int64(0)
	topPerformerPoints := int64(0)

	for _, cp := range childPoints {
		totalPoints += cp.TotalPoints
		if cp.TotalPoints > topPerformerPoints {
			topPerformerPoints = cp.TotalPoints
		}
		// 简化判断：有积分就算活跃
		if cp.TotalPoints > 0 {
			activeParticipants++
		}
	}

	averagePoints := float64(0)
	if totalParticipants > 0 {
		averagePoints = float64(totalPoints) / float64(totalParticipants)
	}

	// 简化处理打卡率和完成率
	checkinRate := float64(activeParticipants) / float64(totalParticipants) * 100
	completionRate := checkinRate // 简化处理

	stats := &CampStatisticsResponse{
		CampID:             campID,
		TotalParticipants:  totalParticipants,
		ActiveParticipants: activeParticipants,
		AveragePoints:      averagePoints,
		TotalPoints:        totalPoints,
		TopPerformerPoints: topPerformerPoints,
		CheckinRate:        checkinRate,
		CompletionRate:     completionRate,
	}

	logger.Info("Camp statistics retrieved successfully", "camp_id", campID, "total_participants", totalParticipants)
	return stats, nil
}

// UpdateCampRankings 更新训练营排名
func (s *pointsService) UpdateCampRankings(campID uint) error {
	if err := s.childPointsRepo.BatchUpdateRankings(campID); err != nil {
		logger.Error("Failed to update camp rankings", "camp_id", campID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新训练营排名失败")
	}

	logger.Info("Camp rankings updated successfully", "camp_id", campID)
	return nil
}

// RecalculateWeeklyPoints 重新计算训练营的周积分
func (s *pointsService) RecalculateWeeklyPoints(campID uint) error {
	if err := s.childPointsRepo.BatchUpdateWeeklyPoints(campID); err != nil {
		logger.Error("Failed to recalculate weekly points", "camp_id", campID, "error", err)
		return errcode.ErrDatabase.WithDetails("重新计算周积分失败")
	}

	logger.Info("Weekly points recalculated successfully", "camp_id", campID)
	return nil
}

// RecalculateMonthlyPoints 重新计算训练营的月积分
func (s *pointsService) RecalculateMonthlyPoints(campID uint) error {
	if err := s.childPointsRepo.BatchUpdateMonthlyPoints(campID); err != nil {
		logger.Error("Failed to recalculate monthly points", "camp_id", campID, "error", err)
		return errcode.ErrDatabase.WithDetails("重新计算月积分失败")
	}

	logger.Info("Monthly points recalculated successfully", "camp_id", campID)
	return nil
}

// GetPointsHistory 获取积分历史
func (s *pointsService) GetPointsHistory(childID uint, offset, limit int) (*PointsHistoryResponse, error) {
	records, total, err := s.pointRecordsRepo.GetByChildID(childID, offset, limit)
	if err != nil {
		logger.Error("Failed to get points history", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取积分历史失败")
	}

	var responseList []*models.PointRecordsResponse
	for _, record := range records {
		responseList = append(responseList, record.ToResponse())
	}

	logger.Info("Points history retrieved successfully", "child_id", childID, "total", total)
	return &PointsHistoryResponse{
		List:  responseList,
		Total: total,
	}, nil
}

// ==================== 辅助方法 ====================

// calculateLevel 根据总积分计算等级
func (s *pointsService) calculateLevel(totalPoints int64) int {
	// 等级计算规则：每100积分一个等级
	level := int(totalPoints/100) + 1
	if level > 100 {
		level = 100 // 最高等级100
	}
	return level
}

// calculateNextLevelPoints 计算下一等级所需积分
func (s *pointsService) calculateNextLevelPoints(currentLevel int) int64 {
	if currentLevel >= 100 {
		return 0 // 已达到最高等级
	}
	return int64(currentLevel * 100)
}

// convertToLeaderboardItems 转换为排行榜项目
func (s *pointsService) convertToLeaderboardItems(childPoints []*models.ChildPoints) []*LeaderboardItem {
	var items []*LeaderboardItem

	for i, cp := range childPoints {
		item := &LeaderboardItem{
			ParticipationID: uint(cp.ParticipationID),
			ChildID:         uint(cp.ChildID),
			ChildName:       "孩子", // 这里需要关联children表获取真实姓名
			Points:          cp.TotalPoints,
			Rank:            i + 1,
			ContinuousDays:  cp.ContinuousDays,
		}
		items = append(items, item)
	}

	return items
}
