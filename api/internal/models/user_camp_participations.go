package models

import (
	"time"
)

// ==================== Model ====================

// UserCampParticipations 用户训练营参与记录表：记录用户参与训练营的完整生命周期
type UserCampParticipations struct {
	BaseModel
	CampID              int64     `json:"camp_id" gorm:"column:camp_id;not null;default:0" validate:"required"`                            // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id" gorm:"column:user_id;not null;default:0" validate:"required"`                            // 用户ID，关联users.id
	ChildID             int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                          // 孩子ID，关联children.id
	ParticipationStatus int8      `json:"participation_status" gorm:"column:participation_status;not null;default:1" validate:"required"`  // 参与状态 1:进行中 2:已完成 3:已暂停 4:已退出
	ParticipationDate   time.Time `json:"participation_date" gorm:"column:participation_date;not null" validate:"required"`                // 参与日期
	CurrentDay          int       `json:"current_day" gorm:"column:current_day;not null;default:1" validate:"required"`                    // 当前训练天数
	ProgressPercentage  float64   `json:"progress_percentage" gorm:"column:progress_percentage;not null;default:0.00" validate:"required"` // 进度百分比
	TotalCheckins       int       `json:"total_checkins" gorm:"column:total_checkins;not null;default:0" validate:"required"`              // 总打卡次数
	ConsecutiveDays     int       `json:"consecutive_days" gorm:"column:consecutive_days;not null;default:0" validate:"required"`          // 连续打卡天数
	TotalStudyMinutes   int       `json:"total_study_minutes" gorm:"column:total_study_minutes;not null;default:0" validate:"required"`    // 总学习时长（分钟）
	Rating              int8      `json:"rating" gorm:"column:rating;not null;default:0" validate:"required"`                              // 用户评分 1-5
	ReviewText          string    `json:"review_text" gorm:"column:review_text;size:65535" validate:"max=65535"`                           // 评价内容

	// 新增冗余字段（优化查询性能）
	CampTitle            string `json:"camp_title" gorm:"column:camp_title;not null;default:''" validate:"max=200"`                         // 训练营标题（冗余字段）
	CampSubtitle         string `json:"camp_subtitle" gorm:"column:camp_subtitle;not null;default:''" validate:"max=300"`                   // 训练营副标题（冗余字段）
	TotalCheckinDays     int    `json:"total_checkin_days" gorm:"column:total_checkin_days;not null;default:0" validate:"required"`         // 总打卡天数（排除跳过的日期）
	CompletedCheckinDays int    `json:"completed_checkin_days" gorm:"column:completed_checkin_days;not null;default:0" validate:"required"` // 已完成打卡天数
	// 注意：补卡字段 MakeupUsedCount 和 MakeupTotalCount 已移动到 children 表中，作为小孩的全局属性
	LastCheckinDate *time.Time `json:"last_checkin_date" gorm:"column:last_checkin_date"` // 最后打卡日期，NULL表示未打卡
}

// TableName 指定表名
func (UserCampParticipations) TableName() string {
	return "user_camp_participations"
}

// ==================== Requests ====================

// UserCampParticipationsCreateRequest 创建用户训练营参与记录表：记录用户参与训练营的完整生命周期请求
type UserCampParticipationsCreateRequest struct {
	CampID              int64     `json:"camp_id" binding:"required" validate:"required"`              // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id" binding:"required" validate:"required"`              // 用户ID，关联users.id
	ChildID             int64     `json:"child_id" binding:"required" validate:"required"`             // 孩子ID，关联children.id
	ParticipationStatus int8      `json:"participation_status" binding:"required" validate:"required"` // 参与状态 1:进行中 2:已完成 3:已暂停 4:已退出
	ParticipationDate   time.Time `json:"participation_date" binding:"required" validate:"required"`   // 参与日期
	CurrentDay          int       `json:"current_day" binding:"required" validate:"required"`          // 当前训练天数
	ProgressPercentage  float64   `json:"progress_percentage" binding:"required" validate:"required"`  // 进度百分比
	TotalCheckins       int       `json:"total_checkins" binding:"required" validate:"required"`       // 总打卡次数
	ConsecutiveDays     int       `json:"consecutive_days" binding:"required" validate:"required"`     // 连续打卡天数
	TotalStudyMinutes   int       `json:"total_study_minutes" binding:"required" validate:"required"`  // 总学习时长（分钟）
	Rating              int8      `json:"rating" binding:"required" validate:"required"`               // 用户评分 1-5
	ReviewText          string    `json:"review_text" binding:"max=65535" validate:"max=65535"`        // 评价内容
}

// UserCampParticipationsUpdateRequest 更新用户训练营参与记录表：记录用户参与训练营的完整生命周期请求
type UserCampParticipationsUpdateRequest struct {
	CampID              *int64     `json:"camp_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`              // 训练营ID，关联training_camps.id
	UserID              *int64     `json:"user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`              // 用户ID，关联users.id
	ChildID             *int64     `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`             // 孩子ID，关联children.id
	ParticipationStatus *int8      `json:"participation_status,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 参与状态 1:进行中 2:已完成 3:已暂停 4:已退出
	ParticipationDate   *time.Time `json:"participation_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`   // 参与日期
	CurrentDay          *int       `json:"current_day,omitempty" binding:"omitempty,required" validate:"omitempty,required"`          // 当前训练天数
	ProgressPercentage  *float64   `json:"progress_percentage,omitempty" binding:"omitempty,required" validate:"omitempty,required"`  // 进度百分比
	TotalCheckins       *int       `json:"total_checkins,omitempty" binding:"omitempty,required" validate:"omitempty,required"`       // 总打卡次数
	ConsecutiveDays     *int       `json:"consecutive_days,omitempty" binding:"omitempty,required" validate:"omitempty,required"`     // 连续打卡天数
	TotalStudyMinutes   *int       `json:"total_study_minutes,omitempty" binding:"omitempty,required" validate:"omitempty,required"`  // 总学习时长（分钟）
	Rating              *int8      `json:"rating,omitempty" binding:"omitempty,required" validate:"omitempty,required"`               // 用户评分 1-5
	ReviewText          *string    `json:"review_text,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`        // 评价内容
}

// ==================== Responses ====================

// UserCampParticipationsResponse 用户训练营参与记录表：记录用户参与训练营的完整生命周期响应
type UserCampParticipationsResponse struct {
	ID                  uint      `json:"id"`                   // 主键ID
	CampID              int64     `json:"camp_id"`              // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id"`              // 用户ID，关联users.id
	ChildID             int64     `json:"child_id"`             // 孩子ID，关联children.id
	ParticipationStatus int8      `json:"participation_status"` // 参与状态 1:进行中 2:已完成 3:已暂停 4:已退出
	ParticipationDate   time.Time `json:"participation_date"`   // 参与日期
	CurrentDay          int       `json:"current_day"`          // 当前训练天数
	ProgressPercentage  float64   `json:"progress_percentage"`  // 进度百分比
	TotalCheckins       int       `json:"total_checkins"`       // 总打卡次数
	ConsecutiveDays     int       `json:"consecutive_days"`     // 连续打卡天数
	TotalStudyMinutes   int       `json:"total_study_minutes"`  // 总学习时长（分钟）
	Rating              int8      `json:"rating"`               // 用户评分 1-5
	ReviewText          string    `json:"review_text"`          // 评价内容

	// 新增冗余字段
	CampTitle            string `json:"camp_title"`             // 训练营标题（冗余字段）
	CampSubtitle         string `json:"camp_subtitle"`          // 训练营副标题（冗余字段）
	TotalCheckinDays     int    `json:"total_checkin_days"`     // 总打卡天数（排除跳过的日期）
	CompletedCheckinDays int    `json:"completed_checkin_days"` // 已完成打卡天数
	// 注意：补卡字段已移动到 children 表中，前端应从 children 数据获取补卡信息
	LastCheckinDate *time.Time `json:"last_checkin_date"` // 最后打卡日期，NULL表示未打卡

	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// UserCampParticipationsListResponse 用户训练营参与记录表：记录用户参与训练营的完整生命周期列表响应
type UserCampParticipationsListResponse struct {
	List  []*UserCampParticipationsResponse `json:"list"`  // 用户训练营参与记录表：记录用户参与训练营的完整生命周期列表
	Total int64                             `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将用户训练营参与记录表：记录用户参与训练营的完整生命周期模型转换为响应结构
func (m *UserCampParticipations) ToResponse() *UserCampParticipationsResponse {
	return &UserCampParticipationsResponse{
		ID:                  m.ID,
		CampID:              m.CampID,
		UserID:              m.UserID,
		ChildID:             m.ChildID,
		ParticipationStatus: m.ParticipationStatus,
		ParticipationDate:   m.ParticipationDate,
		CurrentDay:          m.CurrentDay,
		ProgressPercentage:  m.ProgressPercentage,
		TotalCheckins:       m.TotalCheckins,
		ConsecutiveDays:     m.ConsecutiveDays,
		TotalStudyMinutes:   m.TotalStudyMinutes,
		Rating:              m.Rating,
		ReviewText:          m.ReviewText,

		// 新增冗余字段
		CampTitle:            m.CampTitle,
		CampSubtitle:         m.CampSubtitle,
		TotalCheckinDays:     m.TotalCheckinDays,
		CompletedCheckinDays: m.CompletedCheckinDays,
		// 注意：补卡字段已移动到 children 表中，不再从这里返回
		LastCheckinDate: m.LastCheckinDate,

		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToResponseList 将用户训练营参与记录表：记录用户参与训练营的完整生命周期模型列表转换为响应列表
func UserCampParticipationsToResponseList(models []*UserCampParticipations, total int64) *UserCampParticipationsListResponse {
	list := make([]*UserCampParticipationsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &UserCampParticipationsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到用户训练营参与记录表：记录用户参与训练营的完整生命周期模型
func (m *UserCampParticipations) ApplyUpdateRequest(req *UserCampParticipationsUpdateRequest) {
	if req.CampID != nil {
		m.CampID = *req.CampID
	}
	if req.UserID != nil {
		m.UserID = *req.UserID
	}
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.ParticipationStatus != nil {
		m.ParticipationStatus = *req.ParticipationStatus
	}
	if req.ParticipationDate != nil {
		m.ParticipationDate = *req.ParticipationDate
	}
	if req.CurrentDay != nil {
		m.CurrentDay = *req.CurrentDay
	}
	if req.ProgressPercentage != nil {
		m.ProgressPercentage = *req.ProgressPercentage
	}
	if req.TotalCheckins != nil {
		m.TotalCheckins = *req.TotalCheckins
	}
	if req.ConsecutiveDays != nil {
		m.ConsecutiveDays = *req.ConsecutiveDays
	}
	if req.TotalStudyMinutes != nil {
		m.TotalStudyMinutes = *req.TotalStudyMinutes
	}
	if req.Rating != nil {
		m.Rating = *req.Rating
	}
	if req.ReviewText != nil {
		m.ReviewText = *req.ReviewText
	}
}
