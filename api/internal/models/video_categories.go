package models

import (
	"time"

)

// ==================== Model ====================

// VideoCategories 视频分类表：管理视频内容的分类体系
type VideoCategories struct {
	BaseModel
	Name string `json:"name" gorm:"column:name;size:50;not null" validate:"required,max=50"` // 分类名称
	Description string `json:"description" gorm:"column:description;size:200;not null" validate:"required,max=200"` // 分类描述
	CoverImage string `json:"cover_image" gorm:"column:cover_image;size:500;not null" validate:"required,max=500"` // 分类封面图URL
	ParentID int `json:"parent_id" gorm:"column:parent_id;not null;default:0" validate:"required"` // 父分类ID，关联video_categories.id
	SortOrder int `json:"sort_order" gorm:"column:sort_order;not null;default:0" validate:"required"` // 排序权重
	Status int8 `json:"status" gorm:"column:status;not null;default:1" validate:"required"` // 状态 1:启用 2:禁用
}

// TableName 指定表名
func (VideoCategories) TableName() string {
	return "video_categories"
}

// ==================== Requests ====================

// VideoCategoriesCreateRequest 创建视频分类表：管理视频内容的分类体系请求
type VideoCategoriesCreateRequest struct {
	Name string `json:"name" binding:"required,max=50" validate:"required,max=50"` // 分类名称
	Description string `json:"description" binding:"required,max=200" validate:"required,max=200"` // 分类描述
	CoverImage string `json:"cover_image" binding:"required,max=500" validate:"required,max=500"` // 分类封面图URL
	ParentID int `json:"parent_id" binding:"required" validate:"required"` // 父分类ID，关联video_categories.id
	SortOrder int `json:"sort_order" binding:"required" validate:"required"` // 排序权重
	Status int8 `json:"status" binding:"required" validate:"required"` // 状态 1:启用 2:禁用
}

// VideoCategoriesUpdateRequest 更新视频分类表：管理视频内容的分类体系请求
type VideoCategoriesUpdateRequest struct {
	Name *string `json:"name,omitempty" binding:"omitempty,required,max=50" validate:"omitempty,required,max=50"` // 分类名称
	Description *string `json:"description,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"` // 分类描述
	CoverImage *string `json:"cover_image,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"` // 分类封面图URL
	ParentID *int `json:"parent_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 父分类ID，关联video_categories.id
	SortOrder *int `json:"sort_order,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 排序权重
	Status *int8 `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 状态 1:启用 2:禁用
}

// ==================== Responses ====================

// VideoCategoriesResponse 视频分类表：管理视频内容的分类体系响应
type VideoCategoriesResponse struct {
	ID uint `json:"id"` // 主键ID
	Name string `json:"name"` // 分类名称
	Description string `json:"description"` // 分类描述
	CoverImage string `json:"cover_image"` // 分类封面图URL
	ParentID int `json:"parent_id"` // 父分类ID，关联video_categories.id
	SortOrder int `json:"sort_order"` // 排序权重
	Status int8 `json:"status"` // 状态 1:启用 2:禁用
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// VideoCategoriesListResponse 视频分类表：管理视频内容的分类体系列表响应
type VideoCategoriesListResponse struct {
	List  []*VideoCategoriesResponse `json:"list"`  // 视频分类表：管理视频内容的分类体系列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将视频分类表：管理视频内容的分类体系模型转换为响应结构
func (m *VideoCategories) ToResponse() *VideoCategoriesResponse {
	return &VideoCategoriesResponse{
		ID: m.ID,
		Name: m.Name,
		Description: m.Description,
		CoverImage: m.CoverImage,
		ParentID: m.ParentID,
		SortOrder: m.SortOrder,
		Status: m.Status,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToResponseList 将视频分类表：管理视频内容的分类体系模型列表转换为响应列表
func VideoCategoriesToResponseList(models []*VideoCategories, total int64) *VideoCategoriesListResponse {
	list := make([]*VideoCategoriesResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &VideoCategoriesListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到视频分类表：管理视频内容的分类体系模型
func (m *VideoCategories) ApplyUpdateRequest(req *VideoCategoriesUpdateRequest) {
	if req.Name != nil {
		m.Name = *req.Name
	}
	if req.Description != nil {
		m.Description = *req.Description
	}
	if req.CoverImage != nil {
		m.CoverImage = *req.CoverImage
	}
	if req.ParentID != nil {
		m.ParentID = *req.ParentID
	}
	if req.SortOrder != nil {
		m.SortOrder = *req.SortOrder
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
}
