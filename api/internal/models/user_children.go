package models

import (
	"time"

	"gorm.io/gorm"
)

// UserChildren 用户孩子关联表：实现多用户管理同一孩子的核心表，支持爸爸妈妈等家庭成员共同管理孩子学习
type UserChildren struct {
	BaseModel
	UserID   uint64 `json:"user_id" gorm:"column:user_id;not null;index:idx_user_id;uniqueIndex:uk_user_child,priority:1"`    // 用户ID，关联users.id，表示孩子的管理者
	ChildID  uint64 `json:"child_id" gorm:"column:child_id;not null;index:idx_child_id;uniqueIndex:uk_user_child,priority:2"` // 孩子ID，关联children.id，表示被管理的孩子档案
	Relation string `json:"relation" gorm:"column:relation;size:20;default:''"`                                               // 关系描述，如：爸爸、妈妈、爷爷、奶奶、外公、外婆、其他监护人等

	// 软删除
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"column:deleted_at;index:idx_deleted_at"` // 软删除时间，NULL表示未删除

	// 关联对象（用于预加载）
	User  *Users    `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Child *Children `json:"child,omitempty" gorm:"foreignKey:ChildID"`
}

// TableName 指定表名
func (UserChildren) TableName() string {
	return "user_children"
}

// IsDeleted 检查是否已软删除
func (uc *UserChildren) IsDeleted() bool {
	return uc.DeletedAt.Valid
}

// GetRelationDisplayName 获取关系显示名称
func (uc *UserChildren) GetRelationDisplayName() string {
	relationMap := map[string]string{
		"father":      "爸爸",
		"mother":      "妈妈",
		"grandfather": "爷爷",
		"grandmother": "奶奶",
		"grandpa":     "外公",
		"grandma":     "外婆",
		"guardian":    "监护人",
		"other":       "其他",
	}

	if displayName, exists := relationMap[uc.Relation]; exists {
		return displayName
	}

	// 如果是中文直接返回
	if uc.Relation != "" {
		return uc.Relation
	}

	return "家长"
}

// ==================== Requests ====================

// UserChildrenCreateRequest 创建用户孩子关联请求
type UserChildrenCreateRequest struct {
	ChildID  uint64 `json:"child_id" binding:"required,min=1"`
	Relation string `json:"relation" binding:"required,max=20"`
}

// UserChildrenUpdateRequest 更新用户孩子关联请求
type UserChildrenUpdateRequest struct {
	Relation string `json:"relation" binding:"required,max=20"`
}

// ==================== Responses ====================

// UserChildrenResponse 用户孩子关联响应
type UserChildrenResponse struct {
	ID        uint              `json:"id"`
	UserID    uint64            `json:"user_id"`
	ChildID   uint64            `json:"child_id"`
	Relation  string            `json:"relation"`
	Child     *ChildrenResponse `json:"child,omitempty"`
	CreatedAt time.Time         `json:"created_at"`
}

// UserChildrenListResponse 用户孩子关联列表响应
type UserChildrenListResponse struct {
	List  []*UserChildrenResponse `json:"list"`
	Total int64                   `json:"total"`
}

// ==================== Converters ====================

// ToResponse 将用户孩子关联模型转换为响应结构
func (uc *UserChildren) ToResponse() *UserChildrenResponse {
	if uc == nil {
		return nil
	}

	response := &UserChildrenResponse{
		ID:        uc.ID,
		UserID:    uc.UserID,
		ChildID:   uc.ChildID,
		Relation:  uc.Relation,
		CreatedAt: uc.CreatedAt,
	}

	// 如果预加载了孩子信息，转换它
	if uc.Child != nil {
		response.Child = uc.Child.ToResponse()
	}

	return response
}

// ApplyUpdateRequest 应用更新请求到用户孩子关联模型
func (uc *UserChildren) ApplyUpdateRequest(req *UserChildrenUpdateRequest) {
	uc.Relation = req.Relation
}
