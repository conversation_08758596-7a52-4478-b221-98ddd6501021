package models

import (
	"time"
)

// ==================== Model ====================

// Videos 视频主表：存储教学视频的基本信息和元数据
type Videos struct {
	BaseModel
	Title           string `json:"title" gorm:"column:title;size:200;not null" validate:"required,max=200"`                // 视频标题
	Description     string `json:"description" gorm:"column:description;size:65535" validate:"max=65535"`                  // 视频描述
	CoverImage      string `json:"cover_image" gorm:"column:cover_image;size:500;not null" validate:"required,max=500"`    // 视频封面图URL
	VideoUrl        string `json:"video_url" gorm:"column:video_url;size:500;not null" validate:"required,max=500"`        // 视频文件URL
	VideoSource     int8   `json:"video_source" gorm:"column:video_source;not null;default:1" validate:"required"`         // 视频来源 1:微信视频号 2:本地上传
	Duration        int    `json:"duration" gorm:"column:duration;not null;default:0" validate:"required"`                 // 视频时长（秒）
	CategoryID      int    `json:"category_id" gorm:"column:category_id;not null;default:0" validate:"required"`           // 分类ID，关联video_categories.id
	DifficultyLevel int8   `json:"difficulty_level" gorm:"column:difficulty_level;not null;default:1" validate:"required"` // 难度等级 1-5
	AgeGroup        string `json:"age_group" gorm:"column:age_group;size:20;not null" validate:"required,max=20"`          // 适用年龄段
	Tags            string `json:"tags" gorm:"column:tags;size:500;not null" validate:"required,max=500"`                  // 视频标签，逗号分隔
	ViewCount       int    `json:"view_count" gorm:"column:view_count;not null;default:0" validate:"required"`             // 播放次数
	LikeCount       int    `json:"like_count" gorm:"column:like_count;not null;default:0" validate:"required"`             // 点赞数量
	Status          int8   `json:"status" gorm:"column:status;not null;default:1" validate:"required"`                     // 状态 1:正常 2:下架
}

// TableName 指定表名
func (Videos) TableName() string {
	return "videos"
}

// ==================== Requests ====================

// VideosCreateRequest 创建视频主表：存储教学视频的基本信息和元数据请求
type VideosCreateRequest struct {
	Title           string `json:"title" binding:"required,max=200" validate:"required,max=200"`       // 视频标题
	Description     string `json:"description" binding:"max=65535" validate:"max=65535"`               // 视频描述
	CoverImage      string `json:"cover_image" binding:"required,max=500" validate:"required,max=500"` // 视频封面图URL
	VideoUrl        string `json:"video_url" binding:"required,max=500" validate:"required,max=500"`   // 视频文件URL
	VideoSource     int8   `json:"video_source" binding:"required" validate:"required"`                // 视频来源 1:微信视频号 2:本地上传
	Duration        int    `json:"duration" binding:"required" validate:"required"`                    // 视频时长（秒）
	CategoryID      int    `json:"category_id" binding:"required" validate:"required"`                 // 分类ID，关联video_categories.id
	DifficultyLevel int8   `json:"difficulty_level" binding:"required" validate:"required"`            // 难度等级 1-5
	AgeGroup        string `json:"age_group" binding:"required,max=20" validate:"required,max=20"`     // 适用年龄段
	Tags            string `json:"tags" binding:"required,max=500" validate:"required,max=500"`        // 视频标签，逗号分隔
	ViewCount       int    `json:"view_count" binding:"required" validate:"required"`                  // 播放次数
	LikeCount       int    `json:"like_count" binding:"required" validate:"required"`                  // 点赞数量
	Status          int8   `json:"status" binding:"required" validate:"required"`                      // 状态 1:正常 2:下架
}

// VideosUpdateRequest 更新视频主表：存储教学视频的基本信息和元数据请求
type VideosUpdateRequest struct {
	Title           *string `json:"title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`       // 视频标题
	Description     *string `json:"description,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`               // 视频描述
	CoverImage      *string `json:"cover_image,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"` // 视频封面图URL
	VideoUrl        *string `json:"video_url,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"`   // 视频文件URL
	VideoSource     *int8   `json:"video_source,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                // 视频来源 1:微信视频号 2:本地上传
	Duration        *int    `json:"duration,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 视频时长（秒）
	CategoryID      *int    `json:"category_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 分类ID，关联video_categories.id
	DifficultyLevel *int8   `json:"difficulty_level,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 难度等级 1-5
	AgeGroup        *string `json:"age_group,omitempty" binding:"omitempty,required,max=20" validate:"omitempty,required,max=20"`     // 适用年龄段
	Tags            *string `json:"tags,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"`        // 视频标签，逗号分隔
	ViewCount       *int    `json:"view_count,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                  // 播放次数
	LikeCount       *int    `json:"like_count,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                  // 点赞数量
	Status          *int8   `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                      // 状态 1:正常 2:下架
}

// ==================== Responses ====================

// VideosResponse 视频主表：存储教学视频的基本信息和元数据响应
type VideosResponse struct {
	ID              uint      `json:"id"`               // 主键ID
	Title           string    `json:"title"`            // 视频标题
	Description     string    `json:"description"`      // 视频描述
	CoverImage      string    `json:"cover_image"`      // 视频封面图URL
	VideoUrl        string    `json:"video_url"`        // 视频文件URL
	VideoSource     int8      `json:"video_source"`     // 视频来源 1:微信视频号 2:本地上传
	Duration        int       `json:"duration"`         // 视频时长（秒）
	CategoryID      int       `json:"category_id"`      // 分类ID，关联video_categories.id
	DifficultyLevel int8      `json:"difficulty_level"` // 难度等级 1-5
	AgeGroup        string    `json:"age_group"`        // 适用年龄段
	Tags            string    `json:"tags"`             // 视频标签，逗号分隔
	ViewCount       int       `json:"view_count"`       // 播放次数
	LikeCount       int       `json:"like_count"`       // 点赞数量
	Status          int8      `json:"status"`           // 状态 1:正常 2:下架
	CreatedAt       time.Time `json:"created_at"`       // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`       // 更新时间
}

// VideosListResponse 视频主表：存储教学视频的基本信息和元数据列表响应
type VideosListResponse struct {
	List  []*VideosResponse `json:"list"`  // 视频主表：存储教学视频的基本信息和元数据列表
	Total int64             `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将视频主表：存储教学视频的基本信息和元数据模型转换为响应结构
func (m *Videos) ToResponse() *VideosResponse {
	return &VideosResponse{
		ID:              m.ID,
		Title:           m.Title,
		Description:     m.Description,
		CoverImage:      m.CoverImage,
		VideoUrl:        m.VideoUrl,
		VideoSource:     m.VideoSource,
		Duration:        m.Duration,
		CategoryID:      m.CategoryID,
		DifficultyLevel: m.DifficultyLevel,
		AgeGroup:        m.AgeGroup,
		Tags:            m.Tags,
		ViewCount:       m.ViewCount,
		LikeCount:       m.LikeCount,
		Status:          m.Status,
		CreatedAt:       m.CreatedAt,
		UpdatedAt:       m.UpdatedAt,
	}
}

// ToResponseList 将视频主表：存储教学视频的基本信息和元数据模型列表转换为响应列表
func VideosToResponseList(models []*Videos, total int64) *VideosListResponse {
	list := make([]*VideosResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &VideosListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到视频主表：存储教学视频的基本信息和元数据模型
func (m *Videos) ApplyUpdateRequest(req *VideosUpdateRequest) {
	if req.Title != nil {
		m.Title = *req.Title
	}
	if req.Description != nil {
		m.Description = *req.Description
	}
	if req.CoverImage != nil {
		m.CoverImage = *req.CoverImage
	}
	if req.VideoUrl != nil {
		m.VideoUrl = *req.VideoUrl
	}
	if req.VideoSource != nil {
		m.VideoSource = *req.VideoSource
	}
	if req.Duration != nil {
		m.Duration = *req.Duration
	}
	if req.CategoryID != nil {
		m.CategoryID = *req.CategoryID
	}
	if req.DifficultyLevel != nil {
		m.DifficultyLevel = *req.DifficultyLevel
	}
	if req.AgeGroup != nil {
		m.AgeGroup = *req.AgeGroup
	}
	if req.Tags != nil {
		m.Tags = *req.Tags
	}
	if req.ViewCount != nil {
		m.ViewCount = *req.ViewCount
	}
	if req.LikeCount != nil {
		m.LikeCount = *req.LikeCount
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
}
