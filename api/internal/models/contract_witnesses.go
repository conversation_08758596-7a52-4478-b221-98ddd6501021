package models

import (
	"time"

)

// ==================== Model ====================

// ContractWitnesses 契约见证人表：管理家庭契约的见证人信息
type ContractWitnesses struct {
	BaseModel
	ContractID int64 `json:"contract_id" gorm:"column:contract_id;not null;default:0" validate:"required"` // 契约ID，关联family_contracts.id
	UserID int64 `json:"user_id" gorm:"column:user_id;not null;default:0" validate:"required"` // 见证人用户ID，关联users.id
	WitnessRole int8 `json:"witness_role" gorm:"column:witness_role;not null;default:2" validate:"required"` // 角色 1:首席见证官 2:见证人
	InvitationStatus int8 `json:"invitation_status" gorm:"column:invitation_status;not null;default:1" validate:"required"` // 状态 1:已邀请 2:已接受 3:已拒绝
	JoinedAt time.Time `json:"joined_at" gorm:"column:joined_at"` // 加入时间，NULL表示未加入
}

// TableName 指定表名
func (ContractWitnesses) TableName() string {
	return "contract_witnesses"
}

// ==================== Requests ====================

// ContractWitnessesCreateRequest 创建契约见证人表：管理家庭契约的见证人信息请求
type ContractWitnessesCreateRequest struct {
	ContractID int64 `json:"contract_id" binding:"required" validate:"required"` // 契约ID，关联family_contracts.id
	UserID int64 `json:"user_id" binding:"required" validate:"required"` // 见证人用户ID，关联users.id
	WitnessRole int8 `json:"witness_role" binding:"required" validate:"required"` // 角色 1:首席见证官 2:见证人
	InvitationStatus int8 `json:"invitation_status" binding:"required" validate:"required"` // 状态 1:已邀请 2:已接受 3:已拒绝
	JoinedAt time.Time `json:"joined_at"` // 加入时间，NULL表示未加入
}

// ContractWitnessesUpdateRequest 更新契约见证人表：管理家庭契约的见证人信息请求
type ContractWitnessesUpdateRequest struct {
	ContractID *int64 `json:"contract_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 契约ID，关联family_contracts.id
	UserID *int64 `json:"user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 见证人用户ID，关联users.id
	WitnessRole *int8 `json:"witness_role,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 角色 1:首席见证官 2:见证人
	InvitationStatus *int8 `json:"invitation_status,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 状态 1:已邀请 2:已接受 3:已拒绝
	JoinedAt *time.Time `json:"joined_at,omitempty"` // 加入时间，NULL表示未加入
}

// ==================== Responses ====================

// ContractWitnessesResponse 契约见证人表：管理家庭契约的见证人信息响应
type ContractWitnessesResponse struct {
	ID uint `json:"id"` // 主键ID
	ContractID int64 `json:"contract_id"` // 契约ID，关联family_contracts.id
	UserID int64 `json:"user_id"` // 见证人用户ID，关联users.id
	WitnessRole int8 `json:"witness_role"` // 角色 1:首席见证官 2:见证人
	InvitationStatus int8 `json:"invitation_status"` // 状态 1:已邀请 2:已接受 3:已拒绝
	JoinedAt time.Time `json:"joined_at"` // 加入时间，NULL表示未加入
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// ContractWitnessesListResponse 契约见证人表：管理家庭契约的见证人信息列表响应
type ContractWitnessesListResponse struct {
	List  []*ContractWitnessesResponse `json:"list"`  // 契约见证人表：管理家庭契约的见证人信息列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将契约见证人表：管理家庭契约的见证人信息模型转换为响应结构
func (m *ContractWitnesses) ToResponse() *ContractWitnessesResponse {
	return &ContractWitnessesResponse{
		ID: m.ID,
		ContractID: m.ContractID,
		UserID: m.UserID,
		WitnessRole: m.WitnessRole,
		InvitationStatus: m.InvitationStatus,
		JoinedAt: m.JoinedAt,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToResponseList 将契约见证人表：管理家庭契约的见证人信息模型列表转换为响应列表
func ContractWitnessesToResponseList(models []*ContractWitnesses, total int64) *ContractWitnessesListResponse {
	list := make([]*ContractWitnessesResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &ContractWitnessesListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到契约见证人表：管理家庭契约的见证人信息模型
func (m *ContractWitnesses) ApplyUpdateRequest(req *ContractWitnessesUpdateRequest) {
	if req.ContractID != nil {
		m.ContractID = *req.ContractID
	}
	if req.UserID != nil {
		m.UserID = *req.UserID
	}
	if req.WitnessRole != nil {
		m.WitnessRole = *req.WitnessRole
	}
	if req.InvitationStatus != nil {
		m.InvitationStatus = *req.InvitationStatus
	}
	if req.JoinedAt != nil {
		m.JoinedAt = *req.JoinedAt
	}
}
