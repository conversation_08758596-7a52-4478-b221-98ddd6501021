package models

import (
	"time"
)

// CampCheckinDates 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态
type CampCheckinDates struct {
	BaseModel
	ParticipationID   int64     `json:"participation_id" gorm:"column:participation_id;not null;default:0" validate:"required"`                   // 参与记录ID，关联user_camp_participations.id
	DayNumber         int       `json:"day_number" gorm:"column:day_number;not null;default:0" validate:"required"`                               // 打卡天数序号（1-N）
	CheckinDate       time.Time `json:"checkin_date" gorm:"column:checkin_date;not null" validate:"required"`                                     // 具体打卡日期
	DateType          int8      `json:"date_type" gorm:"column:date_type;not null;default:1" validate:"required"`                                 // 日期类型 1:正常打卡日 2:跳过日期（周末/节假日）
	Status            int8      `json:"status" gorm:"column:status;not null;default:0" validate:"required"`                                       // 打卡状态 0:未打卡 1:已打卡 2:补打卡 3:已跳过
	CheckinRecordID   int64     `json:"checkin_record_id" gorm:"column:checkin_record_id;not null;default:0"`                                     // 关联的详细打卡记录ID
	IsMakeupAllowed   int8      `json:"is_makeup_allowed" gorm:"column:is_makeup_allowed;not null;default:1" validate:"required"`                // 是否允许补卡 0:不允许 1:允许
	MakeupDeadline    *time.Time `json:"makeup_deadline" gorm:"column:makeup_deadline"`                                                           // 补卡截止日期
}

// TableName 指定表名
func (CampCheckinDates) TableName() string {
	return "camp_checkin_dates"
}

// ==================== 常量定义 ====================

// DateType 日期类型常量
const (
	DateTypeNormal = 1 // 正常打卡日
	DateTypeSkip   = 2 // 跳过日期（周末/节假日）
)

// CheckinStatus 打卡状态常量
const (
	CheckinStatusPending   = 0 // 未打卡
	CheckinStatusCompleted = 1 // 已打卡
	CheckinStatusMakeup    = 2 // 补打卡
	CheckinStatusSkipped   = 3 // 已跳过
)

// ==================== Requests ====================

// CampCheckinDatesCreateRequest 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态创建请求
type CampCheckinDatesCreateRequest struct {
	ParticipationID   int64      `json:"participation_id" binding:"required" validate:"required"`                   // 参与记录ID，关联user_camp_participations.id
	DayNumber         int        `json:"day_number" binding:"required" validate:"required"`                         // 打卡天数序号（1-N）
	CheckinDate       time.Time  `json:"checkin_date" binding:"required" validate:"required"`                       // 具体打卡日期
	DateType          int8       `json:"date_type" binding:"required" validate:"required"`                          // 日期类型 1:正常打卡日 2:跳过日期（周末/节假日）
	Status            int8       `json:"status" binding:"required" validate:"required"`                             // 打卡状态 0:未打卡 1:已打卡 2:补打卡 3:已跳过
	CheckinRecordID   int64      `json:"checkin_record_id"`                                                         // 关联的详细打卡记录ID
	IsMakeupAllowed   int8       `json:"is_makeup_allowed" binding:"required" validate:"required"`                  // 是否允许补卡 0:不允许 1:允许
	MakeupDeadline    *time.Time `json:"makeup_deadline"`                                                           // 补卡截止日期
}

// CampCheckinDatesUpdateRequest 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态更新请求
type CampCheckinDatesUpdateRequest struct {
	ParticipationID   *int64     `json:"participation_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                   // 参与记录ID，关联user_camp_participations.id
	DayNumber         *int       `json:"day_number,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                         // 打卡天数序号（1-N）
	CheckinDate       *time.Time `json:"checkin_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                       // 具体打卡日期
	DateType          *int8      `json:"date_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                          // 日期类型 1:正常打卡日 2:跳过日期（周末/节假日）
	Status            *int8      `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                             // 打卡状态 0:未打卡 1:已打卡 2:补打卡 3:已跳过
	CheckinRecordID   *int64     `json:"checkin_record_id,omitempty"`                                                                              // 关联的详细打卡记录ID
	IsMakeupAllowed   *int8      `json:"is_makeup_allowed,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                  // 是否允许补卡 0:不允许 1:允许
	MakeupDeadline    *time.Time `json:"makeup_deadline,omitempty"`                                                                               // 补卡截止日期
}

// ==================== Responses ====================

// CampCheckinDatesResponse 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态响应
type CampCheckinDatesResponse struct {
	ID                uint       `json:"id"`                  // 主键ID
	ParticipationID   int64      `json:"participation_id"`    // 参与记录ID，关联user_camp_participations.id
	DayNumber         int        `json:"day_number"`          // 打卡天数序号（1-N）
	CheckinDate       time.Time  `json:"checkin_date"`        // 具体打卡日期
	DateType          int8       `json:"date_type"`           // 日期类型 1:正常打卡日 2:跳过日期（周末/节假日）
	Status            int8       `json:"status"`              // 打卡状态 0:未打卡 1:已打卡 2:补打卡 3:已跳过
	CheckinRecordID   int64      `json:"checkin_record_id"`   // 关联的详细打卡记录ID
	IsMakeupAllowed   int8       `json:"is_makeup_allowed"`   // 是否允许补卡 0:不允许 1:允许
	MakeupDeadline    *time.Time `json:"makeup_deadline"`     // 补卡截止日期
	CreatedAt         time.Time  `json:"created_at"`          // 创建时间
	UpdatedAt         time.Time  `json:"updated_at"`          // 更新时间
}

// CampCheckinDatesListResponse 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态列表响应
type CampCheckinDatesListResponse struct {
	List  []*CampCheckinDatesResponse `json:"list"`  // 训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态列表
	Total int64                       `json:"total"` // 总数
}

// ==================== 转换方法 ====================

// ToResponse 转换为响应格式
func (m *CampCheckinDates) ToResponse() *CampCheckinDatesResponse {
	return &CampCheckinDatesResponse{
		ID:                m.ID,
		ParticipationID:   m.ParticipationID,
		DayNumber:         m.DayNumber,
		CheckinDate:       m.CheckinDate,
		DateType:          m.DateType,
		Status:            m.Status,
		CheckinRecordID:   m.CheckinRecordID,
		IsMakeupAllowed:   m.IsMakeupAllowed,
		MakeupDeadline:    m.MakeupDeadline,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// ==================== 业务方法 ====================

// IsCompleted 是否已完成打卡
func (m *CampCheckinDates) IsCompleted() bool {
	return m.Status == CheckinStatusCompleted || m.Status == CheckinStatusMakeup
}

// CanMakeup 是否可以补卡
func (m *CampCheckinDates) CanMakeup() bool {
	if m.IsMakeupAllowed == 0 {
		return false
	}
	
	// 如果设置了补卡截止日期，检查是否已过期
	if m.MakeupDeadline != nil {
		return time.Now().Before(*m.MakeupDeadline)
	}
	
	return true
}

// IsToday 是否是今天
func (m *CampCheckinDates) IsToday() bool {
	today := time.Now().Format("2006-01-02")
	checkinDate := m.CheckinDate.Format("2006-01-02")
	return today == checkinDate
}

// IsFuture 是否是未来日期
func (m *CampCheckinDates) IsFuture() bool {
	today := time.Now().Format("2006-01-02")
	checkinDate := m.CheckinDate.Format("2006-01-02")
	return checkinDate > today
}

// GetStatusText 获取状态文本
func (m *CampCheckinDates) GetStatusText() string {
	switch m.Status {
	case CheckinStatusPending:
		if m.IsToday() {
			return "today"
		} else if m.IsFuture() {
			return "future"
		} else {
			return "missed"
		}
	case CheckinStatusCompleted:
		return "completed"
	case CheckinStatusMakeup:
		return "makeup"
	case CheckinStatusSkipped:
		return "skipped"
	default:
		return "unknown"
	}
}
