package models

import (
	"time"
)

// ==================== Model ====================

// VideoCollections 视频集合表：管理视频的集合，支持灵活的付费模式
type VideoCollections struct {
	BaseModel
	Title           string  `json:"title" gorm:"column:title;size:200;not null" validate:"required,max=200"`                // 集合标题
	Description     string  `json:"description" gorm:"column:description;size:65535" validate:"max=65535"`                  // 集合描述
	CoverImage      string  `json:"cover_image" gorm:"column:cover_image;size:500;not null" validate:"required,max=500"`    // 集合封面图URL
	CategoryID      int     `json:"category_id" gorm:"column:category_id;not null;default:0" validate:"required"`           // 分类ID，关联video_categories.id
	DifficultyLevel int8    `json:"difficulty_level" gorm:"column:difficulty_level;not null;default:1" validate:"required"` // 难度等级 1-5
	AgeGroup        string  `json:"age_group" gorm:"column:age_group;size:20;not null" validate:"required,max=20"`          // 适用年龄段
	IsFree          int8    `json:"is_free" gorm:"column:is_free;not null;default:1" validate:"required"`                   // 是否免费 0:付费 1:免费
	FreeVideoCount  int     `json:"free_video_count" gorm:"column:free_video_count;not null;default:0" validate:"required"` // 免费视频数量
	Price           float64 `json:"price" gorm:"column:price;not null;default:0.00" validate:"required"`                    // 价格（元）
	VideoCount      int     `json:"video_count" gorm:"column:video_count;not null;default:0" validate:"required"`           // 视频总数量
	Status          int8    `json:"status" gorm:"column:status;not null;default:1" validate:"required"`                     // 状态 1:正常 2:下架
}

// TableName 指定表名
func (VideoCollections) TableName() string {
	return "video_collections"
}

// ==================== Requests ====================

// VideoCollectionsCreateRequest 创建视频集合表：管理视频的集合，支持灵活的付费模式请求
type VideoCollectionsCreateRequest struct {
	Title           string  `json:"title" binding:"required,max=200" validate:"required,max=200"`       // 集合标题
	Description     string  `json:"description" binding:"max=65535" validate:"max=65535"`               // 集合描述
	CoverImage      string  `json:"cover_image" binding:"required,max=500" validate:"required,max=500"` // 集合封面图URL
	CategoryID      int     `json:"category_id" binding:"required" validate:"required"`                 // 分类ID，关联video_categories.id
	DifficultyLevel int8    `json:"difficulty_level" binding:"required" validate:"required"`            // 难度等级 1-5
	AgeGroup        string  `json:"age_group" binding:"required,max=20" validate:"required,max=20"`     // 适用年龄段
	IsFree          int8    `json:"is_free" binding:"required" validate:"required"`                     // 是否免费 0:付费 1:免费
	FreeVideoCount  int     `json:"free_video_count" binding:"required" validate:"required"`            // 免费视频数量
	Price           float64 `json:"price" binding:"required" validate:"required"`                       // 价格（元）
	VideoCount      int     `json:"video_count" binding:"required" validate:"required"`                 // 视频总数量
	Status          int8    `json:"status" binding:"required" validate:"required"`                      // 状态 1:正常 2:下架
}

// VideoCollectionsUpdateRequest 更新视频集合表：管理视频的集合，支持灵活的付费模式请求
type VideoCollectionsUpdateRequest struct {
	Title           *string  `json:"title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`       // 集合标题
	Description     *string  `json:"description,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`               // 集合描述
	CoverImage      *string  `json:"cover_image,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"` // 集合封面图URL
	CategoryID      *int     `json:"category_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 分类ID，关联video_categories.id
	DifficultyLevel *int8    `json:"difficulty_level,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 难度等级 1-5
	AgeGroup        *string  `json:"age_group,omitempty" binding:"omitempty,required,max=20" validate:"omitempty,required,max=20"`     // 适用年龄段
	IsFree          *int8    `json:"is_free,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                     // 是否免费 0:付费 1:免费
	FreeVideoCount  *int     `json:"free_video_count,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 免费视频数量
	Price           *float64 `json:"price,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                       // 价格（元）
	VideoCount      *int     `json:"video_count,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                 // 视频总数量
	Status          *int8    `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                      // 状态 1:正常 2:下架
}

// ==================== Responses ====================

// VideoCollectionsResponse 视频集合表：管理视频的集合，支持灵活的付费模式响应
type VideoCollectionsResponse struct {
	ID              uint              `json:"id"`               // 主键ID
	Title           string            `json:"title"`            // 集合标题
	Description     string            `json:"description"`      // 集合描述
	CoverImage      string            `json:"cover_image"`      // 集合封面图URL
	CategoryID      int               `json:"category_id"`      // 分类ID，关联video_categories.id
	DifficultyLevel int8              `json:"difficulty_level"` // 难度等级 1-5
	AgeGroup        string            `json:"age_group"`        // 适用年龄段
	IsFree          int8              `json:"is_free"`          // 是否免费 0:付费 1:免费
	FreeVideoCount  int               `json:"free_video_count"` // 免费视频数量
	Price           float64           `json:"price"`            // 价格（元）
	VideoCount      int               `json:"video_count"`      // 视频总数量
	Status          int8              `json:"status"`           // 状态 1:正常 2:下架
	CreatedAt       time.Time         `json:"created_at"`       // 创建时间
	UpdatedAt       time.Time         `json:"updated_at"`       // 更新时间
	Videos          []*VideosResponse `json:"videos"`           // 集合中的视频列表
}

// VideoCollectionsListResponse 视频集合表：管理视频的集合，支持灵活的付费模式列表响应
type VideoCollectionsListResponse struct {
	List  []*VideoCollectionsResponse `json:"list"`  // 视频集合表：管理视频的集合，支持灵活的付费模式列表
	Total int64                       `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将视频集合表：管理视频的集合，支持灵活的付费模式模型转换为响应结构
func (m *VideoCollections) ToResponse() *VideoCollectionsResponse {
	return &VideoCollectionsResponse{
		ID:              m.ID,
		Title:           m.Title,
		Description:     m.Description,
		CoverImage:      m.CoverImage,
		CategoryID:      m.CategoryID,
		DifficultyLevel: m.DifficultyLevel,
		AgeGroup:        m.AgeGroup,
		IsFree:          m.IsFree,
		FreeVideoCount:  m.FreeVideoCount,
		Price:           m.Price,
		VideoCount:      m.VideoCount,
		Status:          m.Status,
		CreatedAt:       m.CreatedAt,
		UpdatedAt:       m.UpdatedAt,
	}
}

// ToResponseList 将视频集合表：管理视频的集合，支持灵活的付费模式模型列表转换为响应列表
func VideoCollectionsToResponseList(models []*VideoCollections, total int64) *VideoCollectionsListResponse {
	list := make([]*VideoCollectionsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &VideoCollectionsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到视频集合表：管理视频的集合，支持灵活的付费模式模型
func (m *VideoCollections) ApplyUpdateRequest(req *VideoCollectionsUpdateRequest) {
	if req.Title != nil {
		m.Title = *req.Title
	}
	if req.Description != nil {
		m.Description = *req.Description
	}
	if req.CoverImage != nil {
		m.CoverImage = *req.CoverImage
	}
	if req.CategoryID != nil {
		m.CategoryID = *req.CategoryID
	}
	if req.DifficultyLevel != nil {
		m.DifficultyLevel = *req.DifficultyLevel
	}
	if req.AgeGroup != nil {
		m.AgeGroup = *req.AgeGroup
	}
	if req.IsFree != nil {
		m.IsFree = *req.IsFree
	}
	if req.FreeVideoCount != nil {
		m.FreeVideoCount = *req.FreeVideoCount
	}
	if req.Price != nil {
		m.Price = *req.Price
	}
	if req.VideoCount != nil {
		m.VideoCount = *req.VideoCount
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
}
