package models

import (
	"time"

)

// ==================== Model ====================

// Likes 点赞表：记录用户的点赞行为
type Likes struct {
	BaseModel
	UserID int64 `json:"user_id" gorm:"column:user_id;not null;default:0" validate:"required"` // 点赞用户ID，关联users.id
	TargetType int8 `json:"target_type" gorm:"column:target_type;not null;default:1" validate:"required"` // 目标类型 1:打卡记录 2:评论
	TargetID int64 `json:"target_id" gorm:"column:target_id;not null;default:0" validate:"required"` // 目标ID
}

// TableName 指定表名
func (Likes) TableName() string {
	return "likes"
}

// ==================== Requests ====================

// LikesCreateRequest 创建点赞表：记录用户的点赞行为请求
type LikesCreateRequest struct {
	UserID int64 `json:"user_id" binding:"required" validate:"required"` // 点赞用户ID，关联users.id
	TargetType int8 `json:"target_type" binding:"required" validate:"required"` // 目标类型 1:打卡记录 2:评论
	TargetID int64 `json:"target_id" binding:"required" validate:"required"` // 目标ID
}

// LikesUpdateRequest 更新点赞表：记录用户的点赞行为请求
type LikesUpdateRequest struct {
	UserID *int64 `json:"user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 点赞用户ID，关联users.id
	TargetType *int8 `json:"target_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 目标类型 1:打卡记录 2:评论
	TargetID *int64 `json:"target_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 目标ID
}

// ==================== Responses ====================

// LikesResponse 点赞表：记录用户的点赞行为响应
type LikesResponse struct {
	ID uint `json:"id"` // 主键ID
	UserID int64 `json:"user_id"` // 点赞用户ID，关联users.id
	TargetType int8 `json:"target_type"` // 目标类型 1:打卡记录 2:评论
	TargetID int64 `json:"target_id"` // 目标ID
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// LikesListResponse 点赞表：记录用户的点赞行为列表响应
type LikesListResponse struct {
	List  []*LikesResponse `json:"list"`  // 点赞表：记录用户的点赞行为列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将点赞表：记录用户的点赞行为模型转换为响应结构
func (m *Likes) ToResponse() *LikesResponse {
	return &LikesResponse{
		ID: m.ID,
		UserID: m.UserID,
		TargetType: m.TargetType,
		TargetID: m.TargetID,
		CreatedAt: m.CreatedAt,
	}
}

// ToResponseList 将点赞表：记录用户的点赞行为模型列表转换为响应列表
func LikesToResponseList(models []*Likes, total int64) *LikesListResponse {
	list := make([]*LikesResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &LikesListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到点赞表：记录用户的点赞行为模型
func (m *Likes) ApplyUpdateRequest(req *LikesUpdateRequest) {
	if req.UserID != nil {
		m.UserID = *req.UserID
	}
	if req.TargetType != nil {
		m.TargetType = *req.TargetType
	}
	if req.TargetID != nil {
		m.TargetID = *req.TargetID
	}
}
