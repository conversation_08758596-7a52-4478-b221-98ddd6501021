package models

import (
	"time"
)

// ==================== Model ====================

// Comments 评论表：记录用户的评论信息
type Comments struct {
	BaseModel
	UserID     int64  `json:"user_id" gorm:"column:user_id;not null;default:0" validate:"required"`         // 评论用户ID，关联users.id
	TargetType int8   `json:"target_type" gorm:"column:target_type;not null;default:1" validate:"required"` // 目标类型 1:打卡记录
	TargetID   int64  `json:"target_id" gorm:"column:target_id;not null;default:0" validate:"required"`     // 目标ID
	ParentID   int64  `json:"parent_id" gorm:"column:parent_id;not null;default:0" validate:"required"`     // 父评论ID
	Content    string `json:"content" gorm:"column:content;size:65535" validate:"max=65535"`                // 评论内容
	LikeCount  int    `json:"like_count" gorm:"column:like_count;not null;default:0" validate:"required"`   // 点赞数
	Status     int8   `json:"status" gorm:"column:status;not null;default:1" validate:"required"`           // 状态 1:正常 2:隐藏
}

// TableName 指定表名
func (Comments) TableName() string {
	return "comments"
}

// ==================== Requests ====================

// CommentsCreateRequest 创建评论表：记录用户的评论信息请求
type CommentsCreateRequest struct {
	UserID     int64  `json:"user_id" binding:"required" validate:"required"`     // 评论用户ID，关联users.id
	TargetType int8   `json:"target_type" binding:"required" validate:"required"` // 目标类型 1:打卡记录
	TargetID   int64  `json:"target_id" binding:"required" validate:"required"`   // 目标ID
	ParentID   int64  `json:"parent_id" binding:"required" validate:"required"`   // 父评论ID
	Content    string `json:"content" binding:"max=65535" validate:"max=65535"`   // 评论内容
	LikeCount  int    `json:"like_count" binding:"required" validate:"required"`  // 点赞数
	Status     int8   `json:"status" binding:"required" validate:"required"`      // 状态 1:正常 2:隐藏
}

// CommentsUpdateRequest 更新评论表：记录用户的评论信息请求
type CommentsUpdateRequest struct {
	UserID     *int64  `json:"user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`     // 评论用户ID，关联users.id
	TargetType *int8   `json:"target_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 目标类型 1:打卡记录
	TargetID   *int64  `json:"target_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`   // 目标ID
	ParentID   *int64  `json:"parent_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`   // 父评论ID
	Content    *string `json:"content,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`   // 评论内容
	LikeCount  *int    `json:"like_count,omitempty" binding:"omitempty,required" validate:"omitempty,required"`  // 点赞数
	Status     *int8   `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`      // 状态 1:正常 2:隐藏
}

// ==================== Responses ====================

// CommentsResponse 评论表：记录用户的评论信息响应
type CommentsResponse struct {
	ID         uint      `json:"id"`          // 主键ID
	UserID     int64     `json:"user_id"`     // 评论用户ID，关联users.id
	TargetType int8      `json:"target_type"` // 目标类型 1:打卡记录
	TargetID   int64     `json:"target_id"`   // 目标ID
	ParentID   int64     `json:"parent_id"`   // 父评论ID
	Content    string    `json:"content"`     // 评论内容
	LikeCount  int       `json:"like_count"`  // 点赞数
	Status     int8      `json:"status"`      // 状态 1:正常 2:隐藏
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}

// CommentsListResponse 评论表：记录用户的评论信息列表响应
type CommentsListResponse struct {
	List  []*CommentsResponse `json:"list"`  // 评论表：记录用户的评论信息列表
	Total int64               `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将评论表：记录用户的评论信息模型转换为响应结构
func (m *Comments) ToResponse() *CommentsResponse {
	return &CommentsResponse{
		ID:         m.ID,
		UserID:     m.UserID,
		TargetType: m.TargetType,
		TargetID:   m.TargetID,
		ParentID:   m.ParentID,
		Content:    m.Content,
		LikeCount:  m.LikeCount,
		Status:     m.Status,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
	}
}

// ToResponseList 将评论表：记录用户的评论信息模型列表转换为响应列表
func CommentsToResponseList(models []*Comments, total int64) *CommentsListResponse {
	list := make([]*CommentsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &CommentsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到评论表：记录用户的评论信息模型
func (m *Comments) ApplyUpdateRequest(req *CommentsUpdateRequest) {
	if req.UserID != nil {
		m.UserID = *req.UserID
	}
	if req.TargetType != nil {
		m.TargetType = *req.TargetType
	}
	if req.TargetID != nil {
		m.TargetID = *req.TargetID
	}
	if req.ParentID != nil {
		m.ParentID = *req.ParentID
	}
	if req.Content != nil {
		m.Content = *req.Content
	}
	if req.LikeCount != nil {
		m.LikeCount = *req.LikeCount
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
}
