package models

import (
	"time"
)

// ==================== Model ====================

// ChildPoints 孩子积分统计表：统计每个孩子的积分和排行榜相关数据
type ChildPoints struct {
	BaseModel
	ChildID           int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                                                 // 孩子ID，关联children.id
	ParticipationID   int64     `json:"participation_id" gorm:"column:participation_id;not null;default:0;uniqueIndex:uk_participation_id" validate:"required"` // 参与记录ID，关联user_camp_participations.id
	TotalPoints       int64     `json:"total_points" gorm:"column:total_points;not null;default:0" validate:"required"`                                         // 总积分
	WeekPoints        int       `json:"week_points" gorm:"column:week_points;not null;default:0" validate:"required"`                                           // 本周积分
	MonthPoints       int       `json:"month_points" gorm:"column:month_points;not null;default:0" validate:"required"`                                         // 本月积分
	TotalCheckins     int       `json:"total_checkins" gorm:"column:total_checkins;not null;default:0" validate:"required"`                                     // 总打卡次数
	ContinuousDays    int       `json:"continuous_days" gorm:"column:continuous_days;not null;default:0" validate:"required"`                                   // 连续打卡天数
	MaxContinuousDays int       `json:"max_continuous_days" gorm:"column:max_continuous_days;not null;default:0" validate:"required"`                           // 最大连续天数
	WeekRank          int       `json:"week_rank" gorm:"column:week_rank;not null;default:0" validate:"required"`                                               // 本周排名
	LastWeekRank      int       `json:"last_week_rank" gorm:"column:last_week_rank;not null;default:0" validate:"required"`                                     // 上周排名
	LastCheckinDate   time.Time `json:"last_checkin_date" gorm:"column:last_checkin_date;not null;default:1970-01-01" validate:"required"`                      // 最后打卡日期，1970-01-01表示从未打卡
	LastUpdatedAt     time.Time `json:"last_updated_at" gorm:"column:last_updated_at;not null;default:CURRENT_TIMESTAMP" validate:"required"`                   // 最后更新时间
}

// TableName 指定表名
func (ChildPoints) TableName() string {
	return "child_points"
}

// ==================== Requests ====================

// ChildPointsCreateRequest 创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据请求
type ChildPointsCreateRequest struct {
	ChildID           int64     `json:"child_id" binding:"required" validate:"required"`            // 孩子ID，关联children.id
	ParticipationID   int64     `json:"participation_id" binding:"required" validate:"required"`    // 参与记录ID，关联user_camp_participations.id
	TotalPoints       int64     `json:"total_points" binding:"required" validate:"required"`        // 总积分
	WeekPoints        int       `json:"week_points" binding:"required" validate:"required"`         // 本周积分
	MonthPoints       int       `json:"month_points" binding:"required" validate:"required"`        // 本月积分
	TotalCheckins     int       `json:"total_checkins" binding:"required" validate:"required"`      // 总打卡次数
	ContinuousDays    int       `json:"continuous_days" binding:"required" validate:"required"`     // 连续打卡天数
	MaxContinuousDays int       `json:"max_continuous_days" binding:"required" validate:"required"` // 最大连续天数
	WeekRank          int       `json:"week_rank" binding:"required" validate:"required"`           // 本周排名
	LastWeekRank      int       `json:"last_week_rank" binding:"required" validate:"required"`      // 上周排名
	LastCheckinDate   time.Time `json:"last_checkin_date" binding:"required" validate:"required"`   // 最后打卡日期，1970-01-01表示从未打卡
	LastUpdatedAt     time.Time `json:"last_updated_at" binding:"required" validate:"required"`     // 最后更新时间
}

// ChildPointsUpdateRequest 更新孩子积分统计表：统计每个孩子的积分和排行榜相关数据请求
type ChildPointsUpdateRequest struct {
	ChildID           *int64     `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`            // 孩子ID，关联children.id
	ParticipationID   *int64     `json:"participation_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`    // 参与记录ID，关联user_camp_participations.id
	TotalPoints       *int64     `json:"total_points,omitempty" binding:"omitempty,required" validate:"omitempty,required"`        // 总积分
	WeekPoints        *int       `json:"week_points,omitempty" binding:"omitempty,required" validate:"omitempty,required"`         // 本周积分
	MonthPoints       *int       `json:"month_points,omitempty" binding:"omitempty,required" validate:"omitempty,required"`        // 本月积分
	TotalCheckins     *int       `json:"total_checkins,omitempty" binding:"omitempty,required" validate:"omitempty,required"`      // 总打卡次数
	ContinuousDays    *int       `json:"continuous_days,omitempty" binding:"omitempty,required" validate:"omitempty,required"`     // 连续打卡天数
	MaxContinuousDays *int       `json:"max_continuous_days,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 最大连续天数
	WeekRank          *int       `json:"week_rank,omitempty" binding:"omitempty,required" validate:"omitempty,required"`           // 本周排名
	LastWeekRank      *int       `json:"last_week_rank,omitempty" binding:"omitempty,required" validate:"omitempty,required"`      // 上周排名
	LastCheckinDate   *time.Time `json:"last_checkin_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`   // 最后打卡日期，1970-01-01表示从未打卡
	LastUpdatedAt     *time.Time `json:"last_updated_at,omitempty" binding:"omitempty,required" validate:"omitempty,required"`     // 最后更新时间
}

// ==================== Responses ====================

// ChildPointsResponse 孩子积分统计表：统计每个孩子的积分和排行榜相关数据响应
type ChildPointsResponse struct {
	ID                uint      `json:"id"`                  // 主键ID
	ChildID           int64     `json:"child_id"`            // 孩子ID，关联children.id
	ParticipationID   int64     `json:"participation_id"`    // 参与记录ID，关联user_camp_participations.id
	TotalPoints       int64     `json:"total_points"`        // 总积分
	WeekPoints        int       `json:"week_points"`         // 本周积分
	MonthPoints       int       `json:"month_points"`        // 本月积分
	TotalCheckins     int       `json:"total_checkins"`      // 总打卡次数
	ContinuousDays    int       `json:"continuous_days"`     // 连续打卡天数
	MaxContinuousDays int       `json:"max_continuous_days"` // 最大连续天数
	WeekRank          int       `json:"week_rank"`           // 本周排名
	LastWeekRank      int       `json:"last_week_rank"`      // 上周排名
	LastCheckinDate   time.Time `json:"last_checkin_date"`   // 最后打卡日期，1970-01-01表示从未打卡
	LastUpdatedAt     time.Time `json:"last_updated_at"`     // 最后更新时间
	CreatedAt         time.Time `json:"created_at"`          // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`          // 更新时间
}

// ChildPointsListResponse 孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表响应
type ChildPointsListResponse struct {
	List  []*ChildPointsResponse `json:"list"`  // 孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将孩子积分统计表：统计每个孩子的积分和排行榜相关数据模型转换为响应结构
func (m *ChildPoints) ToResponse() *ChildPointsResponse {
	return &ChildPointsResponse{
		ID:                m.ID,
		ChildID:           m.ChildID,
		ParticipationID:   m.ParticipationID,
		TotalPoints:       m.TotalPoints,
		WeekPoints:        m.WeekPoints,
		MonthPoints:       m.MonthPoints,
		TotalCheckins:     m.TotalCheckins,
		ContinuousDays:    m.ContinuousDays,
		MaxContinuousDays: m.MaxContinuousDays,
		WeekRank:          m.WeekRank,
		LastWeekRank:      m.LastWeekRank,
		LastCheckinDate:   m.LastCheckinDate,
		LastUpdatedAt:     m.LastUpdatedAt,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// ToResponseList 将孩子积分统计表：统计每个孩子的积分和排行榜相关数据模型列表转换为响应列表
func ChildPointsToResponseList(models []*ChildPoints, total int64) *ChildPointsListResponse {
	list := make([]*ChildPointsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &ChildPointsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到孩子积分统计表：统计每个孩子的积分和排行榜相关数据模型
func (m *ChildPoints) ApplyUpdateRequest(req *ChildPointsUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.TotalPoints != nil {
		m.TotalPoints = *req.TotalPoints
	}
	if req.WeekPoints != nil {
		m.WeekPoints = *req.WeekPoints
	}
	if req.MonthPoints != nil {
		m.MonthPoints = *req.MonthPoints
	}
	if req.TotalCheckins != nil {
		m.TotalCheckins = *req.TotalCheckins
	}
	if req.ContinuousDays != nil {
		m.ContinuousDays = *req.ContinuousDays
	}
	if req.MaxContinuousDays != nil {
		m.MaxContinuousDays = *req.MaxContinuousDays
	}
	if req.WeekRank != nil {
		m.WeekRank = *req.WeekRank
	}
	if req.LastWeekRank != nil {
		m.LastWeekRank = *req.LastWeekRank
	}
	if req.LastCheckinDate != nil {
		m.LastCheckinDate = *req.LastCheckinDate
	}
	if req.LastUpdatedAt != nil {
		m.LastUpdatedAt = *req.LastUpdatedAt
	}
}
