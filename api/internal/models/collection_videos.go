package models

import (
	"time"

)

// ==================== Model ====================

// CollectionVideos 集合视频关联表：管理视频集合与具体视频的关联关系
type CollectionVideos struct {
	BaseModel
	CollectionID int64 `json:"collection_id" gorm:"column:collection_id;not null" validate:"required"` // 视频集合ID，关联video_collections.id
	VideoID int64 `json:"video_id" gorm:"column:video_id;not null" validate:"required"` // 视频ID，关联videos.id
	SortOrder int `json:"sort_order" gorm:"column:sort_order;not null;default:0" validate:"required"` // 视频排序
	IsFree int8 `json:"is_free" gorm:"column:is_free;not null;default:1" validate:"required"` // 是否免费 0:付费 1:免费
}

// TableName 指定表名
func (CollectionVideos) TableName() string {
	return "collection_videos"
}

// ==================== Requests ====================

// CollectionVideosCreateRequest 创建集合视频关联表：管理视频集合与具体视频的关联关系请求
type CollectionVideosCreateRequest struct {
	CollectionID int64 `json:"collection_id" binding:"required" validate:"required"` // 视频集合ID，关联video_collections.id
	VideoID int64 `json:"video_id" binding:"required" validate:"required"` // 视频ID，关联videos.id
	SortOrder int `json:"sort_order" binding:"required" validate:"required"` // 视频排序
	IsFree int8 `json:"is_free" binding:"required" validate:"required"` // 是否免费 0:付费 1:免费
}

// CollectionVideosUpdateRequest 更新集合视频关联表：管理视频集合与具体视频的关联关系请求
type CollectionVideosUpdateRequest struct {
	CollectionID *int64 `json:"collection_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 视频集合ID，关联video_collections.id
	VideoID *int64 `json:"video_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 视频ID，关联videos.id
	SortOrder *int `json:"sort_order,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 视频排序
	IsFree *int8 `json:"is_free,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 是否免费 0:付费 1:免费
}

// ==================== Responses ====================

// CollectionVideosResponse 集合视频关联表：管理视频集合与具体视频的关联关系响应
type CollectionVideosResponse struct {
	ID uint `json:"id"` // 主键ID
	CollectionID int64 `json:"collection_id"` // 视频集合ID，关联video_collections.id
	VideoID int64 `json:"video_id"` // 视频ID，关联videos.id
	SortOrder int `json:"sort_order"` // 视频排序
	IsFree int8 `json:"is_free"` // 是否免费 0:付费 1:免费
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// CollectionVideosListResponse 集合视频关联表：管理视频集合与具体视频的关联关系列表响应
type CollectionVideosListResponse struct {
	List  []*CollectionVideosResponse `json:"list"`  // 集合视频关联表：管理视频集合与具体视频的关联关系列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将集合视频关联表：管理视频集合与具体视频的关联关系模型转换为响应结构
func (m *CollectionVideos) ToResponse() *CollectionVideosResponse {
	return &CollectionVideosResponse{
		ID: m.ID,
		CollectionID: m.CollectionID,
		VideoID: m.VideoID,
		SortOrder: m.SortOrder,
		IsFree: m.IsFree,
		CreatedAt: m.CreatedAt,
	}
}

// ToResponseList 将集合视频关联表：管理视频集合与具体视频的关联关系模型列表转换为响应列表
func CollectionVideosToResponseList(models []*CollectionVideos, total int64) *CollectionVideosListResponse {
	list := make([]*CollectionVideosResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &CollectionVideosListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到集合视频关联表：管理视频集合与具体视频的关联关系模型
func (m *CollectionVideos) ApplyUpdateRequest(req *CollectionVideosUpdateRequest) {
	if req.CollectionID != nil {
		m.CollectionID = *req.CollectionID
	}
	if req.VideoID != nil {
		m.VideoID = *req.VideoID
	}
	if req.SortOrder != nil {
		m.SortOrder = *req.SortOrder
	}
	if req.IsFree != nil {
		m.IsFree = *req.IsFree
	}
}
