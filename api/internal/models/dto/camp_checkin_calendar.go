package dto

import "kids-platform/internal/models"

// CampInfo 训练营基本信息
type CampInfo struct {
	ID        uint   `json:"id"`         // 训练营ID
	Title     string `json:"title"`      // 训练营标题
	Subtitle  string `json:"subtitle"`   // 训练营副标题
	TotalDays int    `json:"total_days"` // 总天数
}

// CalendarDateInfo 日历日期信息
type CalendarDateInfo struct {
	Date        string                         `json:"date"`         // 日期 YYYY-MM-DD
	DayNumber   int                            `json:"day_number"`   // 天数序号
	Status      string                         `json:"status"`       // 状态：pending/completed/makeup/today/future/missed
	CanMakeup   bool                           `json:"can_makeup"`   // 是否可以补卡
	CheckinData *models.CheckinRecordsResponse `json:"checkin_data"` // 打卡详细数据
}

// CalendarData 日历数据
type CalendarData struct {
	TotalDays  int                 `json:"total_days"`  // 总天数
	CurrentDay int                 `json:"current_day"` // 当前天数
	StartDate  string              `json:"start_date"`  // 开始日期
	EndDate    string              `json:"end_date"`    // 结束日期
	Dates      []*CalendarDateInfo `json:"dates"`       // 日期列表
}

// MakeupInfo 补卡信息
type MakeupInfo struct {
	TotalCount     int `json:"total_count"`     // 总补卡次数
	UsedCount      int `json:"used_count"`      // 已使用次数
	AvailableCount int `json:"available_count"` // 可用次数
}

// CampCheckinCalendarResponse 训练营打卡日历响应
type CampCheckinCalendarResponse struct {
	CampInfo     *CampInfo     `json:"camp_info"`     // 训练营信息
	CalendarData *CalendarData `json:"calendar_data"` // 日历数据
	MakeupInfo   *MakeupInfo   `json:"makeup_info"`   // 补卡信息
}
