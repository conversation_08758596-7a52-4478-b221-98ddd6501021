package models

import (
	"time"
)

// ==================== Model ====================

// Users 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析
type Users struct {
	BaseModel
	OpenID         string    `json:"openid" gorm:"column:openid;size:28" validate:"max=28"`
	UnionID        string    `json:"unionid" gorm:"column:unionid;size:28" validate:"max=28"`             // 微信unionid，28位固定长度，用于跨应用用户识别
	Nickname       string    `json:"nickname" gorm:"column:nickname;size:50" validate:"max=50"`           // 用户昵称，来源于微信昵称或用户自定义，最长50字符
	Avatar         string    `json:"avatar" gorm:"column:avatar;size:255" validate:"max=255"`             // 用户头像URL，存储微信头像或用户上传头像的完整URL地址
	Phone          string    `json:"phone" gorm:"column:phone;size:20" validate:"max=20"`                 // 手机号码，可选填写，用于重要通知和账户安全
	Gender         int8      `json:"gender" gorm:"column:gender;default:0"`                               // 性别 0:未知 1:男 2:女，来源于微信授权信息
	Province       string    `json:"province" gorm:"column:province;size:20" validate:"max=20"`           // 省份，用于地域化推荐和统计分析
	City           string    `json:"city" gorm:"column:city;size:20" validate:"max=20"`                   // 城市，用于本地化服务和用户分组
	Status         int8      `json:"status" gorm:"column:status;default:1"`                               // 账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控
	UserType       int8      `json:"user_type" gorm:"column:user_type;default:1"`                         // 用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制
	CurrentChildID uint64    `json:"current_child_id" gorm:"column:current_child_id;default:0"`           // 当前选择的孩子ID，0表示未选择
	LastLoginAt    time.Time `json:"last_login_at" gorm:"column:last_login_at;default:CURRENT_TIMESTAMP"` // 最后登录时间，用于用户活跃度分析
}

// TableName 指定表名
func (Users) TableName() string {
	return "users"
}

// ==================== Requests ====================

// WechatLoginRequest 微信登录请求
type WechatLoginRequest struct {
	Code          string `json:"code" binding:"required" validate:"required"`   // 微信登录凭证，通过wx.login()获取
	EncryptedData string `json:"encrypted_data,omitempty" validate:"omitempty"` // 加密的用户数据，可选，用于获取用户详细信息
	Iv            string `json:"iv,omitempty" validate:"omitempty"`             // 加密算法的初始向量，与encrypted_data配套使用
	PhoneCode     string `json:"phone_code,omitempty" validate:"omitempty"`     // 手机号授权码，可选，用于绑定手机号
}

// UsersCreateRequest 创建用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析请求
type UsersCreateRequest struct {
	OpenID         string    `json:"openid" binding:"max=28" validate:"max=28"`
	UnionID        string    `json:"unionid" binding:"max=28" validate:"max=28"`  // 微信unionid，28位固定长度，用于跨应用用户识别
	Nickname       string    `json:"nickname" binding:"max=50" validate:"max=50"` // 用户昵称，来源于微信昵称或用户自定义，最长50字符
	Avatar         string    `json:"avatar" binding:"max=255" validate:"max=255"` // 用户头像URL，存储微信头像或用户上传头像的完整URL地址
	Phone          string    `json:"phone" binding:"max=20" validate:"max=20"`    // 手机号码，可选填写，用于重要通知和账户安全
	Gender         int8      `json:"gender"`                                      // 性别 0:未知 1:男 2:女，来源于微信授权信息
	Province       string    `json:"province" binding:"max=20" validate:"max=20"` // 省份，用于地域化推荐和统计分析
	City           string    `json:"city" binding:"max=20" validate:"max=20"`     // 城市，用于本地化服务和用户分组
	Status         int8      `json:"status"`                                      // 账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控
	UserType       int8      `json:"user_type"`                                   // 用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制
	CurrentChildID uint64    `json:"current_child_id"`                            // 当前选择的孩子ID，0表示未选择
	LastLoginAt    time.Time `json:"last_login_at"`                               // 最后登录时间，用于用户活跃度分析
}

// UsersUpdateRequest 更新用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析请求
type UsersUpdateRequest struct {
	OpenID         *string    `json:"openid,omitempty" binding:"omitempty,max=28" validate:"omitempty,max=28"`
	UnionID        *string    `json:"unionid,omitempty" binding:"omitempty,max=28" validate:"omitempty,max=28"`  // 微信unionid，28位固定长度，用于跨应用用户识别
	Nickname       *string    `json:"nickname,omitempty" binding:"omitempty,max=50" validate:"omitempty,max=50"` // 用户昵称，来源于微信昵称或用户自定义，最长50字符
	Avatar         *string    `json:"avatar,omitempty" binding:"omitempty,max=255" validate:"omitempty,max=255"` // 用户头像URL，存储微信头像或用户上传头像的完整URL地址
	Phone          *string    `json:"phone,omitempty" binding:"omitempty,max=20" validate:"omitempty,max=20"`    // 手机号码，可选填写，用于重要通知和账户安全
	Gender         *int8      `json:"gender,omitempty"`                                                          // 性别 0:未知 1:男 2:女，来源于微信授权信息
	Province       *string    `json:"province,omitempty" binding:"omitempty,max=20" validate:"omitempty,max=20"` // 省份，用于地域化推荐和统计分析
	City           *string    `json:"city,omitempty" binding:"omitempty,max=20" validate:"omitempty,max=20"`     // 城市，用于本地化服务和用户分组
	Status         *int8      `json:"status,omitempty"`                                                          // 账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控
	UserType       *int8      `json:"user_type,omitempty"`                                                       // 用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制
	CurrentChildID *uint64    `json:"current_child_id,omitempty"`                                                // 当前选择的孩子ID，0表示未选择
	LastLoginAt    *time.Time `json:"last_login_at,omitempty"`                                                   // 最后登录时间，用于用户活跃度分析
}

// ==================== Responses ====================

// UsersResponse 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析响应
type UsersResponse struct {
	ID             uint      `json:"id"` // 主键ID
	OpenID         string    `json:"openid"`
	UnionID        string    `json:"unionid"`          // 微信unionid，28位固定长度，用于跨应用用户识别
	Nickname       string    `json:"nickname"`         // 用户昵称，来源于微信昵称或用户自定义，最长50字符
	Avatar         string    `json:"avatar"`           // 用户头像URL，存储微信头像或用户上传头像的完整URL地址
	Phone          string    `json:"phone"`            // 手机号码，可选填写，用于重要通知和账户安全
	Gender         int8      `json:"gender"`           // 性别 0:未知 1:男 2:女，来源于微信授权信息
	Province       string    `json:"province"`         // 省份，用于地域化推荐和统计分析
	City           string    `json:"city"`             // 城市，用于本地化服务和用户分组
	Status         int8      `json:"status"`           // 账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控
	UserType       int8      `json:"user_type"`        // 用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制
	CurrentChildID uint64    `json:"current_child_id"` // 当前选择的孩子ID，0表示未选择
	LastLoginAt    time.Time `json:"last_login_at"`    // 最后登录时间，用于用户活跃度分析
	CreatedAt      time.Time `json:"created_at"`       // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`       // 更新时间
}

// UsersListResponse 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析列表响应
type UsersListResponse struct {
	List  []*UsersResponse `json:"list"`  // 用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析列表
	Total int64            `json:"total"` // 总数
}

// WechatLoginResponse 微信登录响应
type WechatLoginResponse struct {
	AccessToken  string         `json:"access_token"`  // JWT访问令牌
	RefreshToken string         `json:"refresh_token"` // 刷新令牌
	ExpiresIn    int64          `json:"expires_in"`    // 访问令牌过期时间（秒）
	TokenType    string         `json:"token_type"`    // 令牌类型，固定为Bearer
	UserInfo     *UsersResponse `json:"user_info"`     // 用户基本信息
	IsNewUser    bool           `json:"is_new_user"`   // 是否为新注册用户
}

// ==================== Converters ====================

// ToResponse 将用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析模型转换为响应结构
func (m *Users) ToResponse() *UsersResponse {
	return &UsersResponse{
		ID:             m.ID,
		OpenID:         m.OpenID,
		UnionID:        m.UnionID,
		Nickname:       m.Nickname,
		Avatar:         m.Avatar,
		Phone:          m.Phone,
		Gender:         m.Gender,
		Province:       m.Province,
		City:           m.City,
		Status:         m.Status,
		UserType:       m.UserType,
		CurrentChildID: m.CurrentChildID,
		LastLoginAt:    m.LastLoginAt,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
	}
}

// ToResponseList 将用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析模型列表转换为响应列表
func UsersToResponseList(models []*Users, total int64) *UsersListResponse {
	list := make([]*UsersResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &UsersListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析模型
func (m *Users) ApplyUpdateRequest(req *UsersUpdateRequest) {
	if req.OpenID != nil {
		m.OpenID = *req.OpenID
	}
	if req.UnionID != nil {
		m.UnionID = *req.UnionID
	}
	if req.Nickname != nil {
		m.Nickname = *req.Nickname
	}
	if req.Avatar != nil {
		m.Avatar = *req.Avatar
	}
	if req.Phone != nil {
		m.Phone = *req.Phone
	}
	if req.Gender != nil {
		m.Gender = *req.Gender
	}
	if req.Province != nil {
		m.Province = *req.Province
	}
	if req.City != nil {
		m.City = *req.City
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
	if req.UserType != nil {
		m.UserType = *req.UserType
	}
	if req.CurrentChildID != nil {
		m.CurrentChildID = *req.CurrentChildID
	}
	if req.LastLoginAt != nil {
		m.LastLoginAt = *req.LastLoginAt
	}
}
