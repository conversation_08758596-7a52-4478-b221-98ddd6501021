package models

import (
	"time"

)

// ==================== Model ====================

// Medals 勋章定义表：定义系统中所有可获得的勋章
type Medals struct {
	BaseModel
	Name string `json:"name" gorm:"column:name;size:100;not null" validate:"required,max=100"` // 勋章名称
	Description string `json:"description" gorm:"column:description;size:300;not null" validate:"required,max=300"` // 勋章描述
	Icon string `json:"icon" gorm:"column:icon;size:50;not null" validate:"required,max=50"` // 勋章图标
	ConditionType int8 `json:"condition_type" gorm:"column:condition_type;not null;default:1" validate:"required"` // 条件类型 1:打卡次数 2:连续天数 3:积分达标
	ConditionValue int `json:"condition_value" gorm:"column:condition_value;not null;default:0" validate:"required"` // 条件数值
	ConditionDescription string `json:"condition_description" gorm:"column:condition_description;size:200;not null" validate:"required,max=200"` // 条件描述
	MedalLevel int8 `json:"medal_level" gorm:"column:medal_level;not null;default:1" validate:"required"` // 勋章等级 1:铜 2:银 3:金 4:钻石
	PointsReward int `json:"points_reward" gorm:"column:points_reward;not null;default:0" validate:"required"` // 奖励积分
	SortOrder int `json:"sort_order" gorm:"column:sort_order;not null;default:0" validate:"required"` // 排序权重
	IsActive int8 `json:"is_active" gorm:"column:is_active;not null;default:1" validate:"required"` // 是否启用 0:否 1:是
}

// TableName 指定表名
func (Medals) TableName() string {
	return "medals"
}

// ==================== Requests ====================

// MedalsCreateRequest 创建勋章定义表：定义系统中所有可获得的勋章请求
type MedalsCreateRequest struct {
	Name string `json:"name" binding:"required,max=100" validate:"required,max=100"` // 勋章名称
	Description string `json:"description" binding:"required,max=300" validate:"required,max=300"` // 勋章描述
	Icon string `json:"icon" binding:"required,max=50" validate:"required,max=50"` // 勋章图标
	ConditionType int8 `json:"condition_type" binding:"required" validate:"required"` // 条件类型 1:打卡次数 2:连续天数 3:积分达标
	ConditionValue int `json:"condition_value" binding:"required" validate:"required"` // 条件数值
	ConditionDescription string `json:"condition_description" binding:"required,max=200" validate:"required,max=200"` // 条件描述
	MedalLevel int8 `json:"medal_level" binding:"required" validate:"required"` // 勋章等级 1:铜 2:银 3:金 4:钻石
	PointsReward int `json:"points_reward" binding:"required" validate:"required"` // 奖励积分
	SortOrder int `json:"sort_order" binding:"required" validate:"required"` // 排序权重
	IsActive int8 `json:"is_active" binding:"required" validate:"required"` // 是否启用 0:否 1:是
}

// MedalsUpdateRequest 更新勋章定义表：定义系统中所有可获得的勋章请求
type MedalsUpdateRequest struct {
	Name *string `json:"name,omitempty" binding:"omitempty,required,max=100" validate:"omitempty,required,max=100"` // 勋章名称
	Description *string `json:"description,omitempty" binding:"omitempty,required,max=300" validate:"omitempty,required,max=300"` // 勋章描述
	Icon *string `json:"icon,omitempty" binding:"omitempty,required,max=50" validate:"omitempty,required,max=50"` // 勋章图标
	ConditionType *int8 `json:"condition_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 条件类型 1:打卡次数 2:连续天数 3:积分达标
	ConditionValue *int `json:"condition_value,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 条件数值
	ConditionDescription *string `json:"condition_description,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"` // 条件描述
	MedalLevel *int8 `json:"medal_level,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 勋章等级 1:铜 2:银 3:金 4:钻石
	PointsReward *int `json:"points_reward,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 奖励积分
	SortOrder *int `json:"sort_order,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 排序权重
	IsActive *int8 `json:"is_active,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 是否启用 0:否 1:是
}

// ==================== Responses ====================

// MedalsResponse 勋章定义表：定义系统中所有可获得的勋章响应
type MedalsResponse struct {
	ID uint `json:"id"` // 主键ID
	Name string `json:"name"` // 勋章名称
	Description string `json:"description"` // 勋章描述
	Icon string `json:"icon"` // 勋章图标
	ConditionType int8 `json:"condition_type"` // 条件类型 1:打卡次数 2:连续天数 3:积分达标
	ConditionValue int `json:"condition_value"` // 条件数值
	ConditionDescription string `json:"condition_description"` // 条件描述
	MedalLevel int8 `json:"medal_level"` // 勋章等级 1:铜 2:银 3:金 4:钻石
	PointsReward int `json:"points_reward"` // 奖励积分
	SortOrder int `json:"sort_order"` // 排序权重
	IsActive int8 `json:"is_active"` // 是否启用 0:否 1:是
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// MedalsListResponse 勋章定义表：定义系统中所有可获得的勋章列表响应
type MedalsListResponse struct {
	List  []*MedalsResponse `json:"list"`  // 勋章定义表：定义系统中所有可获得的勋章列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将勋章定义表：定义系统中所有可获得的勋章模型转换为响应结构
func (m *Medals) ToResponse() *MedalsResponse {
	return &MedalsResponse{
		ID: m.ID,
		Name: m.Name,
		Description: m.Description,
		Icon: m.Icon,
		ConditionType: m.ConditionType,
		ConditionValue: m.ConditionValue,
		ConditionDescription: m.ConditionDescription,
		MedalLevel: m.MedalLevel,
		PointsReward: m.PointsReward,
		SortOrder: m.SortOrder,
		IsActive: m.IsActive,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToResponseList 将勋章定义表：定义系统中所有可获得的勋章模型列表转换为响应列表
func MedalsToResponseList(models []*Medals, total int64) *MedalsListResponse {
	list := make([]*MedalsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &MedalsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到勋章定义表：定义系统中所有可获得的勋章模型
func (m *Medals) ApplyUpdateRequest(req *MedalsUpdateRequest) {
	if req.Name != nil {
		m.Name = *req.Name
	}
	if req.Description != nil {
		m.Description = *req.Description
	}
	if req.Icon != nil {
		m.Icon = *req.Icon
	}
	if req.ConditionType != nil {
		m.ConditionType = *req.ConditionType
	}
	if req.ConditionValue != nil {
		m.ConditionValue = *req.ConditionValue
	}
	if req.ConditionDescription != nil {
		m.ConditionDescription = *req.ConditionDescription
	}
	if req.MedalLevel != nil {
		m.MedalLevel = *req.MedalLevel
	}
	if req.PointsReward != nil {
		m.PointsReward = *req.PointsReward
	}
	if req.SortOrder != nil {
		m.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil {
		m.IsActive = *req.IsActive
	}
}
