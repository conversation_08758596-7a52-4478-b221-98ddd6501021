package models

import (
	"time"

	"gorm.io/gorm"
)

// ==================== Model ====================

// Children 孩子档案表：存储孩子基本信息和学习档案，支持隐私保护和个性化推荐
type Children struct {
	BaseModel
	// 基本信息
	Name      string     `json:"name" gorm:"column:name;size:50"`               // 孩子姓名，必填字段，用于档案识别和个性化显示
	Nickname  string     `json:"nickname" gorm:"column:nickname;size:50"`       // 孩子昵称，可选字段，用于社交展示时的隐私保护
	Gender    int8       `json:"gender" gorm:"column:gender;default:0"`         // 性别 0:未知 1:男 2:女，用于个性化推荐和统计分析
	BirthDate *time.Time `json:"birth_date" gorm:"column:birth_date;type:date"` // 出生日期，用于年龄计算和年龄段分组

	// 学校信息
	School string `json:"school" gorm:"column:school;size:100"` // 学校名称，可选填写，用于同校用户推荐和统计
	Grade  string `json:"grade" gorm:"column:grade;size:20"`    // 年级班级，如"三年级2班"，用于同龄推荐

	// 地理位置
	Province string `json:"province" gorm:"column:province;size:20"` // 省份，用于地域化推荐和本地活动推送
	City     string `json:"city" gorm:"column:city;size:20"`         // 城市，用于本地化服务和线下活动组织

	// 头像和展示
	Avatar string `json:"avatar" gorm:"column:avatar;size:255"` // 孩子头像URL，用于社交展示和个人档案

	// 运动能力评估
	SkillLevel          int8 `json:"skill_level" gorm:"column:skill_level;default:1"`                     // 技能水平 1:新手 2:初级 3:中级 4:高级 5:专业，用于内容推荐
	BestScore1Min       uint `json:"best_score_1min" gorm:"column:best_score_1min;default:0"`             // 一分钟最佳成绩（个），用于能力评估和排行榜
	BestScoreContinuous uint `json:"best_score_continuous" gorm:"column:best_score_continuous;default:0"` // 连续跳最佳成绩（个），用于技能进步跟踪

	// 学习偏好
	PreferredDifficulty int8   `json:"preferred_difficulty" gorm:"column:preferred_difficulty;default:1"`  // 偏好难度 1:新手 2:初级 3:中级 4:高级 5:专业，用于个性化推荐
	LearningGoals       string `json:"learning_goals" gorm:"column:learning_goals;type:json;default:'[]'"` // 学习目标JSON数组，如["提高耐力","学会花式跳法"]

	// 隐私设置
	PrivacyLevel      int8 `json:"privacy_level" gorm:"column:privacy_level;default:2"`             // 隐私级别 1:公开 2:好友可见 3:仅自己，控制信息可见范围
	ShowInLeaderboard int8 `json:"show_in_leaderboard" gorm:"column:show_in_leaderboard;default:1"` // 是否参与排行榜 0:不参与 1:参与，用于排行榜显示控制

	// 统计字段（冗余存储，提升查询性能）
	TotalCheckins   uint       `json:"total_checkins" gorm:"column:total_checkins;default:0"`       // 总打卡次数，用于成就系统和统计分析
	TotalPoints     uint64     `json:"total_points" gorm:"column:total_points;default:0"`           // 总积分，用于等级系统和奖励机制
	ContinuousDays  uint       `json:"continuous_days" gorm:"column:continuous_days;default:0"`     // 连续打卡天数，用于连续性激励
	LastCheckinDate *time.Time `json:"last_checkin_date" gorm:"column:last_checkin_date;type:date"` // 最后打卡日期，用于连续天数计算

	// 补卡功能字段（全局设置，不针对单个训练营）
	MakeupUsedCount  uint `json:"makeup_used_count" gorm:"column:makeup_used_count;default:0"`   // 已使用补卡次数，全局计数
	MakeupTotalCount uint `json:"makeup_total_count" gorm:"column:makeup_total_count;default:3"` // 总补卡次数限制，默认3次

	// 状态字段
	Status int8 `json:"status" gorm:"column:status;default:1"` // 档案状态 1:正常 2:暂停 3:删除，用于软删除和状态管理

	// 软删除
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"column:deleted_at;index:idx_deleted_at"` // 软删除时间戳，支持数据恢复
}

// TableName 指定表名
func (Children) TableName() string {
	return "children"
}

// ==================== Requests ====================

// ChildrenCreateRequest 创建孩子档案请求
type ChildrenCreateRequest struct {
	Name      string `json:"name" binding:"required,min=1,max=50"`
	Nickname  string `json:"nickname" binding:"max=50"`
	Gender    int8   `json:"gender" binding:"oneof=0 1 2"`
	BirthDate string `json:"birth_date" binding:"omitempty"`
	School    string `json:"school" binding:"max=100"`
	Grade     string `json:"grade" binding:"max=20"`
	Province  string `json:"province" binding:"max=20"`
	City      string `json:"city" binding:"max=20"`
	Avatar    string `json:"avatar" binding:"omitempty"`
	Relation  string `json:"relation" binding:"required,max=20"`
}

// ChildrenUpdateRequest 更新孩子档案请求
type ChildrenUpdateRequest struct {
	Name                string `json:"name" binding:"required,min=1,max=50"`
	Nickname            string `json:"nickname" binding:"max=50"`
	Gender              int8   `json:"gender" binding:"oneof=0 1 2"`
	BirthDate           string `json:"birth_date" binding:"omitempty"`
	School              string `json:"school" binding:"max=100"`
	Grade               string `json:"grade" binding:"max=20"`
	Province            string `json:"province" binding:"max=20"`
	City                string `json:"city" binding:"max=20"`
	Avatar              string `json:"avatar" binding:"omitempty"`
	PreferredDifficulty int8   `json:"preferred_difficulty" binding:"oneof=1 2 3 4 5"`
	PrivacyLevel        int8   `json:"privacy_level" binding:"oneof=1 2 3"`
	ShowInLeaderboard   int8   `json:"show_in_leaderboard" binding:"oneof=0 1"`
}

// ChildrenSelectRequest 选择当前孩子请求
type ChildrenSelectRequest struct {
	ChildID uint `json:"child_id" binding:"required,min=1"`
}

// ChildrenAddToUserRequest 将孩子添加到用户管理列表请求
type ChildrenAddToUserRequest struct {
	ChildID  uint   `json:"child_id" binding:"required,min=1"`
	Relation string `json:"relation" binding:"required,max=20"`
}

// ChildrenListRequest 获取孩子列表请求
type ChildrenListRequest struct {
	Page  int `form:"page" binding:"omitempty,min=1"`
	Limit int `form:"limit" binding:"omitempty,min=1,max=100"`
}

// ==================== Responses ====================

// ChildrenResponse 孩子档案响应
type ChildrenResponse struct {
	ID                  uint       `json:"id"`
	Name                string     `json:"name"`
	Nickname            string     `json:"nickname"`
	Gender              int8       `json:"gender"`
	Age                 int        `json:"age"`
	BirthDate           *time.Time `json:"birth_date"`
	School              string     `json:"school"`
	Grade               string     `json:"grade"`
	Province            string     `json:"province"`
	City                string     `json:"city"`
	Avatar              string     `json:"avatar"`
	SkillLevel          int8       `json:"skill_level"`
	BestScore1Min       uint       `json:"best_score_1min"`
	BestScoreContinuous uint       `json:"best_score_continuous"`
	PreferredDifficulty int8       `json:"preferred_difficulty"`
	LearningGoals       string     `json:"learning_goals"`
	PrivacyLevel        int8       `json:"privacy_level"`
	ShowInLeaderboard   int8       `json:"show_in_leaderboard"`
	TotalCheckins       uint       `json:"total_checkins"`
	TotalPoints         uint64     `json:"total_points"`
	ContinuousDays      uint       `json:"continuous_days"`
	LastCheckinDate     *time.Time `json:"last_checkin_date"`
	MakeupUsedCount     uint       `json:"makeup_used_count"`  // 已使用补卡次数
	MakeupTotalCount    uint       `json:"makeup_total_count"` // 总补卡次数限制
	Status              int8       `json:"status"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
	IsCurrent           bool       `json:"is_current,omitempty"`
	Relation            string     `json:"relation,omitempty"`
}

// ChildrenListResponse 孩子列表响应
type ChildrenListResponse struct {
	Children []*ChildrenResponse `json:"children"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	Limit    int                 `json:"limit"`
}

// ChildrenStatsResponse 孩子统计信息响应
type ChildrenStatsResponse struct {
	ChildID         uint     `json:"child_id"`
	ChildName       string   `json:"child_name"`
	TotalCheckins   uint     `json:"total_checkins"`
	TotalPoints     uint64   `json:"total_points"`
	ContinuousDays  uint     `json:"continuous_days"`
	CurrentLevel    int8     `json:"current_level"`
	NextLevelPoints uint64   `json:"next_level_points"`
	Achievements    []string `json:"achievements"`
}

// ==================== Converters ====================

// ToResponse 将孩子模型转换为响应结构
func (c *Children) ToResponse() *ChildrenResponse {
	if c == nil {
		return nil
	}

	// 计算年龄
	age := 0
	if c.BirthDate != nil {
		age = int(time.Since(*c.BirthDate).Hours() / 24 / 365)
	}

	return &ChildrenResponse{
		ID:                  c.ID,
		Name:                c.Name,
		Nickname:            c.Nickname,
		Gender:              c.Gender,
		Age:                 age,
		BirthDate:           c.BirthDate,
		School:              c.School,
		Grade:               c.Grade,
		Province:            c.Province,
		City:                c.City,
		Avatar:              c.Avatar,
		SkillLevel:          c.SkillLevel,
		BestScore1Min:       c.BestScore1Min,
		BestScoreContinuous: c.BestScoreContinuous,
		PreferredDifficulty: c.PreferredDifficulty,
		LearningGoals:       c.LearningGoals,
		PrivacyLevel:        c.PrivacyLevel,
		ShowInLeaderboard:   c.ShowInLeaderboard,
		TotalCheckins:       c.TotalCheckins,
		TotalPoints:         c.TotalPoints,
		ContinuousDays:      c.ContinuousDays,
		LastCheckinDate:     c.LastCheckinDate,
		MakeupUsedCount:     c.MakeupUsedCount,
		MakeupTotalCount:    c.MakeupTotalCount,
		Status:              c.Status,
		CreatedAt:           c.CreatedAt,
		UpdatedAt:           c.UpdatedAt,
	}
}

// ApplyUpdateRequest 应用更新请求到孩子模型
func (c *Children) ApplyUpdateRequest(req *ChildrenUpdateRequest) {
	c.Name = req.Name
	c.Nickname = req.Nickname
	c.Gender = req.Gender
	c.School = req.School
	c.Grade = req.Grade
	c.Province = req.Province
	c.City = req.City
	c.Avatar = req.Avatar
	c.PreferredDifficulty = req.PreferredDifficulty
	c.PrivacyLevel = req.PrivacyLevel
	c.ShowInLeaderboard = req.ShowInLeaderboard

	// 处理生日
	if req.BirthDate != "" {
		if birthDate, err := time.Parse("2006-01-02", req.BirthDate); err == nil {
			c.BirthDate = &birthDate
		}
	}
}
