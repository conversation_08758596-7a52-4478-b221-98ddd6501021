package converters

import (
	"kids-platform/internal/models"
)

// ChildrenConverter 孩子数据转换器
type ChildrenConverter struct{}

// NewChildrenConverter 创建孩子转换器
func NewChildrenConverter() *ChildrenConverter {
	return &ChildrenConverter{}
}

// ToResponseList 将Children列表转换为响应格式
func (c *ChildrenConverter) ToResponseList(children []*models.Children) []*models.ChildrenResponse {
	if children == nil {
		return nil
	}

	responses := make([]*models.ChildrenResponse, len(children))
	for i, child := range children {
		responses[i] = child.ToResponse() // 使用模型的方法
	}

	return responses
}

// ToListResponse 将Children列表转换为分页响应格式
func (c *ChildrenConverter) ToListResponse(children []*models.Children, total int64) *models.ChildrenListResponse {
	return &models.ChildrenListResponse{
		Children: c.ToResponseList(children),
		Total:    total,
	}
}

// UserChildrenToResponse 将UserChildren模型转换为响应格式
func (c *ChildrenConverter) UserChildrenToResponse(userChild *models.UserChildren) *models.UserChildrenResponse {
	if userChild == nil {
		return nil
	}

	response := &models.UserChildrenResponse{
		ID:        userChild.ID,
		UserID:    userChild.UserID,
		ChildID:   userChild.ChildID,
		Relation:  userChild.Relation,
		CreatedAt: userChild.CreatedAt,
	}

	// 如果预加载了孩子信息，转换它
	if userChild.Child != nil {
		response.Child = userChild.Child.ToResponse()
	}

	return response
}

// UserChildrenListToResponse 将UserChildren列表转换为响应格式
func (c *ChildrenConverter) UserChildrenListToResponse(userChildren []*models.UserChildren) []*models.UserChildrenResponse {
	if userChildren == nil {
		return nil
	}

	responses := make([]*models.UserChildrenResponse, len(userChildren))
	for i, userChild := range userChildren {
		responses[i] = c.UserChildrenToResponse(userChild)
	}

	return responses
}
