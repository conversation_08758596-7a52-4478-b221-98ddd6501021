package api

import (
	"strconv"

	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// ContractHandler 契约相关处理器
type ContractHandler struct {
	contractService     api.ContractService
	growthSystemService api.GrowthSystemService
}

// NewContractHandler 创建契约处理器
func NewContractHandler(
	contractService api.ContractService,
	growthSystemService api.GrowthSystemService,
) *ContractHandler {
	return &ContractHandler{
		contractService:     contractService,
		growthSystemService: growthSystemService,
	}
}

// CreateContract 创建家庭契约
// @Summary 创建家庭契约
// @Description 创建新的家庭契约
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param request body api.ContractCreateRequest true "契约创建请求"
// @Success 200 {object} response.Response{data=api.ContractResponse}
// @Router /contracts [post]
func (h *ContractHandler) CreateContract(c *gin.Context) {
	var req api.ContractCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 设置用户ID
	req.UserID = userID

	// 验证孩子ID
	if req.ChildID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.contractService.CreateContract(&req)
	if err != nil {
		logger.Error("Failed to create contract", "user_id", userID, "child_id", req.ChildID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetContracts 获取契约列表
// @Summary 获取契约列表
// @Description 获取用户的契约列表
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param status query int false "契约状态" default(0)
// @Success 200 {object} response.Response{data=[]api.ContractResponse}
// @Router /contracts [get]
func (h *ContractHandler) GetContracts(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取孩子ID
	childIDStr := c.Query("child_id")
	if childIDStr == "" {
		response.BadRequest(c, "请提供孩子ID")
		return
	}

	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的孩子ID")
		return
	}

	// 获取状态参数
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	// 调用服务层
	result, err := h.contractService.GetContracts(uint(childID), int8(status))
	if err != nil {
		logger.Error("Failed to get contracts", "user_id", userID, "child_id", childID, "status", status, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetContractDetail 获取契约详情
// @Summary 获取契约详情
// @Description 获取指定契约的详情信息
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Success 200 {object} response.Response{data=api.ContractDetailResponse}
// @Router /contracts/{id} [get]
func (h *ContractHandler) GetContractDetail(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取契约ID
	contractIDStr := c.Param("id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的契约ID")
		return
	}

	// 调用服务层
	result, err := h.contractService.GetContractDetail(uint(contractID))
	if err != nil {
		logger.Error("Failed to get contract detail", "user_id", userID, "contract_id", contractID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// UpdateContractProgress 更新契约进度
// @Summary 更新契约进度
// @Description 更新契约的完成进度
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Param request body UpdateProgressRequest true "进度更新请求"
// @Success 200 {object} response.Response
// @Router /contracts/{id}/progress [put]
func (h *ContractHandler) UpdateContractProgress(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取契约ID
	contractIDStr := c.Param("id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的契约ID")
		return
	}

	var req UpdateProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 调用服务层
	err = h.contractService.UpdateContractProgress(uint(contractID), req.Progress)
	if err != nil {
		logger.Error("Failed to update contract progress", "user_id", userID, "contract_id", contractID, "progress", req.Progress, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, gin.H{"message": "契约进度更新成功"})
}

// CompleteContract 完成契约
// @Summary 完成契约
// @Description 标记契约为完成状态
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param id path int true "契约ID"
// @Success 200 {object} response.Response{data=api.ContractCompleteResponse}
// @Router /contracts/{id}/complete [post]
func (h *ContractHandler) CompleteContract(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取契约ID
	contractIDStr := c.Param("id")
	contractID, err := strconv.ParseUint(contractIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的契约ID")
		return
	}

	// 调用服务层
	result, err := h.contractService.CompleteContract(uint(contractID), userID)
	if err != nil {
		logger.Error("Failed to complete contract", "user_id", userID, "contract_id", contractID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	// 处理契约完成后的系统更新
	if result.Success {
		// 获取契约详情以获取孩子ID
		contractDetail, err := h.contractService.GetContractDetail(uint(contractID))
		if err == nil {
			if err := h.growthSystemService.HandleContractComplete(uint(contractDetail.ChildID), uint(contractID)); err != nil {
				logger.Error("Failed to handle contract complete", "contract_id", contractID, "error", err)
				// 系统更新失败不影响契约完成，只记录错误
			}
		}
	}

	response.Success(c, result)
}

// GetContractStats 获取契约统计
// @Summary 获取契约统计
// @Description 获取用户的契约统计信息
// @Tags 契约管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Success 200 {object} response.Response{data=api.ContractStatsResponse}
// @Router /contracts/stats [get]
func (h *ContractHandler) GetContractStats(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取孩子ID
	childIDStr := c.Query("child_id")
	if childIDStr == "" {
		response.BadRequest(c, "请提供孩子ID")
		return
	}

	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的孩子ID")
		return
	}

	// 调用服务层
	result, err := h.contractService.GetContractStats(uint(childID))
	if err != nil {
		logger.Error("Failed to get contract stats", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// ==================== 请求结构定义 ====================

// UpdateProgressRequest 更新进度请求
type UpdateProgressRequest struct {
	Progress float64 `json:"progress" binding:"required,min=0,max=100"` // 进度百分比
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *ContractHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}
