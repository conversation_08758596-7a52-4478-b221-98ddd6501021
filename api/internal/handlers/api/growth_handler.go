package api

import (
	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// GrowthHandler 成长数据相关处理器
type GrowthHandler struct {
	growthSystemService api.GrowthSystemService
}

// NewGrowthHandler 创建成长数据处理器
func NewGrowthHandler(growthSystemService api.GrowthSystemService) *GrowthHandler {
	return &GrowthHandler{
		growthSystemService: growthSystemService,
	}
}

// GetGrowthPageData 获取成长页面完整数据
// @Summary 获取成长页面数据
// @Description 获取成长页面的完整数据，包括统计、训练营、契约、成长轨迹等
// @Tags 成长管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=api.GrowthPageResponse}
// @Router /user/growth [get]
func (h *GrowthHandler) GetGrowthPageData(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 从context中获取孩子ID（由ChildrenMiddleware设置）
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.growthSystemService.GetGrowthPageData(userID, childID)
	if err != nil {
		logger.Error("Failed to get growth page data", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetGrowthStats 获取成长统计
// @Summary 获取成长统计
// @Description 获取用户的详细成长统计信息
// @Tags 成长管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=api.GrowthStatsResponse}
// @Router /user/growth/stats [get]
func (h *GrowthHandler) GetGrowthStats(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 从context中获取孩子ID（由ChildrenMiddleware设置）
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.growthSystemService.GetGrowthStats(childID)
	if err != nil {
		logger.Error("Failed to get growth stats", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetGrowthTrack 获取成长轨迹
// @Summary 获取成长轨迹
// @Description 获取用户的成长轨迹记录
// @Tags 成长管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]api.GrowthTrackItem}
// @Router /user/growth/track [get]
func (h *GrowthHandler) GetGrowthTrack(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 从context中获取孩子ID（由ChildrenMiddleware设置）
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用成长系统服务获取成长轨迹
	growthPageData, err := h.growthSystemService.GetGrowthPageData(userID, childID)
	if err != nil {
		logger.Error("Failed to get growth track", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, growthPageData.GrowthTrack)
}

// GetTodayStatus 获取今日状态
// @Summary 获取今日状态
// @Description 获取用户今日的状态信息
// @Tags 成长管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=api.TodayStatusInfo}
// @Router /user/growth/today [get]
func (h *GrowthHandler) GetTodayStatus(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 从context中获取孩子ID（由ChildrenMiddleware设置）
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用成长系统服务获取今日状态
	growthPageData, err := h.growthSystemService.GetGrowthPageData(userID, childID)
	if err != nil {
		logger.Error("Failed to get today status", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, growthPageData.TodayStatus)
}

// GetUserStats 获取用户统计信息
// @Summary 获取用户统计信息
// @Description 获取用户的基础统计信息
// @Tags 成长管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=api.UserStatsInfo}
// @Router /user/stats [get]
func (h *GrowthHandler) GetUserStats(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 从context中获取孩子ID（由ChildrenMiddleware设置）
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用成长系统服务获取用户统计
	growthPageData, err := h.growthSystemService.GetGrowthPageData(userID, childID)
	if err != nil {
		logger.Error("Failed to get user stats", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, growthPageData.UserStats)
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *GrowthHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}

// getChildID 从上下文获取孩子ID
func (h *GrowthHandler) getChildID(c *gin.Context) uint {
	if childID, exists := c.Get("child_id"); exists {
		// ChildrenMiddleware设置的是uint类型
		if id, ok := childID.(uint); ok {
			return id
		}
	}
	return 0
}
