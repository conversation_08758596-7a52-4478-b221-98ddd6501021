package api

import (
	"strconv"

	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// ContentHandler 内容相关处理器
type ContentHandler struct {
	trainingCampService api.TrainingCampService
	growthSystemService api.GrowthSystemService
}

// NewContentHandler 创建内容处理器
func NewContentHandler(
	trainingCampService api.TrainingCampService,
	growthSystemService api.GrowthSystemService,
) *ContentHandler {
	return &ContentHandler{
		trainingCampService: trainingCampService,
		growthSystemService: growthSystemService,
	}
}

// GetCampsList 获取训练营列表
// @Summary 获取训练营列表
// @Description 获取训练营列表，支持分页
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=models.TrainingCampsListResponse}
// @Router /content/camps [get]
func (h *ContentHandler) GetCampsList(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// 调用服务层
	result, err := h.trainingCampService.GetCampsList(offset, limit)
	if err != nil {
		logger.Error("Failed to get camps list", "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetCampDetail 获取训练营详情
// @Summary 获取训练营详情
// @Description 获取训练营详情信息
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param id path int true "训练营ID"
// @Success 200 {object} response.Response{data=api.TrainingCampDetailResponse}
// @Router /content/camps/{id} [get]
func (h *ContentHandler) GetCampDetail(c *gin.Context) {
	// 获取训练营ID
	campIDStr := c.Param("id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的训练营ID")
		return
	}

	// 检查是否需要用户状态
	userID := h.getUserID(c)
	childID := h.getChildID(c)

	var result interface{}
	if userID > 0 && childID > 0 {
		// 获取包含用户状态的详情
		result, err = h.trainingCampService.GetCampDetailWithUserStatus(uint(campID), userID, childID)
	} else {
		// 获取基本详情
		result, err = h.trainingCampService.GetCampDetail(uint(campID))
	}

	if err != nil {
		logger.Error("Failed to get camp detail", "camp_id", campID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// JoinCamp 参与训练营
// @Summary 参与训练营
// @Description 用户参与指定的训练营
// @Tags 内容管理
// @Accept json
// @Produce json
// @Param id path int true "训练营ID"
// @Success 200 {object} response.Response{data=api.CampJoinResponse}
// @Router /content/camps/{id}/join [post]
func (h *ContentHandler) JoinCamp(c *gin.Context) {
	// 获取训练营ID
	campIDStr := c.Param("id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的训练营ID")
		return
	}

	// 获取用户信息
	userID := h.getUserID(c)
	childID := h.getChildID(c)

	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.trainingCampService.JoinCamp(userID, childID, uint(campID))
	if err != nil {
		logger.Error("Failed to join camp", "camp_id", campID, "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}
	logger.Info("userID", childID)

	response.Success(c, result)
}

// GetUserCamps 获取用户参与的训练营列表
// @Summary 获取用户训练营列表
// @Description 获取用户参与的训练营列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]api.UserCampResponse}
// @Router /user/camps [get]
func (h *ContentHandler) GetUserCamps(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	childID := h.getChildID(c)

	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.trainingCampService.GetUserCamps(userID, childID)
	if err != nil {
		logger.Error("Failed to get user camps", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetCampProgress 获取训练营进度
// @Summary 获取训练营进度
// @Description 获取用户在指定训练营的进度
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "训练营ID"
// @Success 200 {object} response.Response{data=api.CampProgressResponse}
// @Router /user/camps/{id}/progress [get]
func (h *ContentHandler) GetCampProgress(c *gin.Context) {
	// 获取训练营ID
	campIDStr := c.Param("id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的训练营ID")
		return
	}

	// 获取用户信息
	userID := h.getUserID(c)
	childID := h.getChildID(c)

	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.trainingCampService.GetCampProgress(userID, childID, uint(campID))
	if err != nil {
		logger.Error("Failed to get camp progress", "camp_id", campID, "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *ContentHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}

// getChildID 从上下文获取孩子ID
func (h *ContentHandler) getChildID(c *gin.Context) uint {
	if childID, exists := c.Get("child_id"); exists {
		if id, ok := childID.(uint); ok {
			return id
		}
	}

	// 也可以从查询参数获取
	if childIDStr := c.Query("child_id"); childIDStr != "" {
		if childID, err := strconv.ParseUint(childIDStr, 10, 32); err == nil {
			return uint(childID)
		}
	}

	return 0
}
