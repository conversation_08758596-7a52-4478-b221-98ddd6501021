package api

import (
	"kids-platform/internal/repositories"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/cache"
	"kids-platform/pkg/config"
	"kids-platform/pkg/jwt"
	"kids-platform/pkg/middleware"
	"kids-platform/pkg/wechat"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes 设置API路由
func SetupRoutes(r *gin.Engine, db *gorm.DB, cfg *config.Config, redisClient cache.RedisClient) {
	// 设置数据库中间件，让健康检查可以访问数据库
	r.Use(func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	})

	// 创建JWT管理器
	jwtManager := jwt.NewJWTManager(
		cfg.JWT.Secret,
		cfg.JWT.ExpireHours,
	)

	// 创建微信服务
	wechatService := wechat.NewMiniprogramService(cfg)

	// 创建仓储层
	userRepo := repositories.NewUsersRepository(db)
	childrenRepo := repositories.NewChildrenRepository(db)
	userChildrenRepo := repositories.NewUserChildrenRepository(db)

	// 成长系统相关仓储
	trainingCampsRepo := repositories.NewTrainingCampsRepository(db)
	userCampParticipationRepo := repositories.NewUserCampParticipationsRepository(db)
	videoCollectionsRepo := repositories.NewVideoCollectionsRepository(db)
	collectionVideosRepo := repositories.NewCollectionVideosRepository(db)
	videosRepo := repositories.NewVideosRepository(db)
	checkinRecordsRepo := repositories.NewCheckinRecordsRepository(db)
	campCheckinDatesRepo := repositories.NewCampCheckinDatesRepository(db)
	childPointsRepo := repositories.NewChildPointsRepository(db)
	pointRecordsRepo := repositories.NewPointRecordsRepository(db)
	familyContractsRepo := repositories.NewFamilyContractsRepository(db)
	contractWitnessesRepo := repositories.NewContractWitnessesRepository(db)
	medalsRepo := repositories.NewMedalsRepository(db)
	childMedalsRepo := repositories.NewChildMedalsRepository(db)
	growthTracksRepo := repositories.NewGrowthTracksRepository(db)

	// 创建服务层
	userService := api.NewUserService(userRepo, wechatService, jwtManager, cfg)
	childrenService := api.NewChildrenService(childrenRepo, userChildrenRepo, userRepo)

	// 成长系统服务层 - 按依赖关系顺序初始化
	// 1. 先创建基础服务（无依赖）
	pointsService := api.NewPointsService(
		childPointsRepo,
		pointRecordsRepo,
	)

	// 2. 创建成长系统服务（依赖基础仓储）
	growthSystemService := api.NewGrowthSystemService(
		nil, // trainingCampService - 稍后设置
		nil, // checkinService - 稍后设置
		pointsService,
		nil, // contractService - 稍后设置
		medalsRepo,
		childMedalsRepo,
		growthTracksRepo,
		userCampParticipationRepo,
	)

	// 3. 创建打卡服务（依赖积分和成长系统服务）
	checkinService := api.NewCheckinService(
		checkinRecordsRepo,
		userCampParticipationRepo,
		trainingCampsRepo,
		campCheckinDatesRepo,
		childrenRepo,
		pointsService,
		growthSystemService,
		db,
		redisClient,
	)

	// 4. 创建训练营服务（依赖打卡服务）
	trainingCampService := api.NewTrainingCampService(
		trainingCampsRepo,
		userCampParticipationRepo,
		videoCollectionsRepo,
		collectionVideosRepo,
		videosRepo,
		checkinService,
	)

	// 5. 创建契约服务（依赖积分服务）
	contractService := api.NewContractService(
		familyContractsRepo,
		contractWitnessesRepo,
		trainingCampsRepo,
		userCampParticipationRepo,
		pointsService,
	)

	// 创建处理器
	userHandler := NewUserHandler(userService, jwtManager)
	childrenHandler := NewChildrenHandler(childrenService)

	// 成长系统处理器
	contentHandler := NewContentHandler(trainingCampService, growthSystemService)
	checkinHandler := NewCheckinHandler(checkinService, growthSystemService)
	pointsHandler := NewPointsHandler(pointsService)
	contractHandler := NewContractHandler(contractService, growthSystemService)
	growthHandler := NewGrowthHandler(growthSystemService)

	// Swagger文档路由
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	apiV1 := r.Group("/api/v1")
	{
		// 健康检查端点（无需认证）
		apiV1.GET("/health", healthCheck)

		// 认证相关路由（无需JWT验证）
		auth := apiV1.Group("/auth")
		{
			// 微信登录
			auth.POST("/wechat/login", userHandler.WechatLogin)
			// 刷新令牌
			auth.POST("/refresh", userHandler.RefreshToken)
		}

		// 内容相关路由（公开访问）
		content := apiV1.Group("/content")
		{
			// 训练营列表和详情
			content.GET("/camps", contentHandler.GetCampsList)
			content.GET("/camps/:id", contentHandler.GetCampDetail)
		}

		// 排行榜（公开访问）
		apiV1.GET("/leaderboard", pointsHandler.GetLeaderboard)

		// 用户基础信息路由（仅需要JWT验证）
		user := apiV1.Group("/user")
		user.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			// 获取当前用户信息
			user.GET("/info", userHandler.GetUserInfo)
			// 更新当前用户信息
			user.PUT("/info", userHandler.UpdateUserInfo)
		}

		// 用户数据相关路由（需要JWT验证和儿童ID）
		userData := apiV1.Group("/user")
		userData.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			// 用户积分相关
			userData.GET("/points", pointsHandler.GetPointsStats)
			userData.GET("/points/history", pointsHandler.GetPointsHistory)

			// 用户成长数据
			userData.GET("/growth", growthHandler.GetGrowthPageData)
			userData.GET("/growth/stats", growthHandler.GetGrowthStats)
			userData.GET("/growth/track", growthHandler.GetGrowthTrack)
			userData.GET("/growth/today", growthHandler.GetTodayStatus)
			userData.GET("/stats", growthHandler.GetUserStats)
		}

		// 用户训练营相关路由（需要JWT验证和儿童ID）
		userCamps := apiV1.Group("/user")
		userCamps.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			userCamps.GET("/camps", contentHandler.GetUserCamps)
			userCamps.GET("/camps/:id/progress", contentHandler.GetCampProgress)
		}

		// 训练营参与（需要JWT验证）
		camps := apiV1.Group("/content/camps")
		camps.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			camps.POST("/:id/join", contentHandler.JoinCamp)
		}

		// 打卡相关路由（需要JWT验证）
		checkins := apiV1.Group("/checkins")
		checkins.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			checkins.POST("", checkinHandler.CreateCheckin)
			checkins.GET("/history", checkinHandler.GetCheckinHistory)
			checkins.GET("/today", checkinHandler.GetTodayCheckinStatus)
			checkins.GET("/stats", checkinHandler.GetCheckinStats)
		}

		// 训练营打卡日历路由（需要JWT验证）
		campCalendar := apiV1.Group("/camps")
		campCalendar.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			campCalendar.GET("/:camp_id/checkin-calendar/:child_id", checkinHandler.GetCampCheckinCalendar)
		}

		// 契约相关路由（需要JWT验证）
		contracts := apiV1.Group("/contracts")
		contracts.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			contracts.POST("", contractHandler.CreateContract)
			contracts.GET("", contractHandler.GetContracts)
			contracts.GET("/:id", contractHandler.GetContractDetail)
			contracts.PUT("/:id/progress", contractHandler.UpdateContractProgress)
			contracts.POST("/:id/complete", contractHandler.CompleteContract)
			contracts.GET("/stats", contractHandler.GetContractStats)
		}

		// 孩子管理相关路由（需要JWT验证）
		children := apiV1.Group("/children")
		children.Use(middleware.JWTAuth(jwtManager), middleware.ChildrenMiddlewareGin())
		{
			// 孩子列表和创建
			children.GET("", childrenHandler.GetChildrenList)
			children.POST("", childrenHandler.CreateChild)

			// 当前选择的孩子
			children.GET("/current", childrenHandler.GetCurrentChild)

			// 特定孩子操作
			children.GET("/:id", childrenHandler.GetChildByID)
			children.PUT("/:id", childrenHandler.UpdateChild)
			children.DELETE("/:id", childrenHandler.DeleteChild)
			children.POST("/:id/select", childrenHandler.SelectCurrentChild)
		}

		// 管理员路由（需要JWT验证和管理员权限）
		admin := apiV1.Group("/admin")
		admin.Use(middleware.JWTAuth(jwtManager))
		admin.Use(middleware.AdminAuth()) // 需要实现管理员权限中间件
		{
			// 根据ID获取用户信息
			admin.GET("/user/:id", userHandler.GetUserByID)

			// 积分管理
			admin.POST("/points/add", pointsHandler.AddPoints)
			admin.POST("/points/update-ranking", pointsHandler.UpdateWeeklyRanking)
		}
	}
}
