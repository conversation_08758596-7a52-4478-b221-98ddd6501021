package api

import (
	"context"
	"net/http"
	"time"

	"kids-platform/pkg/health"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// healthCheck API健康检查处理器
func healthCheck(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// 从上下文获取数据库连接
	db, exists := c.Get("db")
	if !exists {
		// 如果没有数据库连接，返回基本健康状态
		response.Success(c, gin.H{
			"status":    "healthy",
			"service":   "api-server",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
		return
	}

	// 创建健康检查器
	healthChecker := health.NewHealthChecker()

	// 添加数据库检查器
	if gormDB, ok := db.(*gorm.DB); ok {
		healthChecker.AddChecker("database", health.NewDatabaseChecker(gormDB))
	}

	// 执行健康检查
	result := healthChecker.Check(ctx)

	// 根据健康状态设置HTTP状态码
	statusCode := http.StatusOK
	if result.Status == health.StatusUnhealthy {
		statusCode = http.StatusServiceUnavailable
	} else if result.Status == health.StatusDegraded {
		statusCode = http.StatusPartialContent
	}

	c.JSON(statusCode, result)
}
