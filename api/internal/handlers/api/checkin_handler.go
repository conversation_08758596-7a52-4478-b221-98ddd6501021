package api

import (
	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// CheckinHandler 打卡相关处理器
type CheckinHandler struct {
	checkinService      api.CheckinService
	growthSystemService api.GrowthSystemService
}

// NewCheckinHandler 创建打卡处理器
func NewCheckinHandler(
	checkinService api.CheckinService,
	growthSystemService api.GrowthSystemService,
) *CheckinHandler {
	return &CheckinHandler{
		checkinService:      checkinService,
		growthSystemService: growthSystemService,
	}
}

// CreateCheckin 创建打卡记录
// @Summary 创建打卡记录
// @Description 用户创建训练打卡记录
// @Tags 打卡管理
// @Accept json
// @Produce json
// @Param request body api.CheckinCreateRequest true "打卡请求"
// @Success 200 {object} response.Response{data=api.CheckinResponse}
// @Router /checkins [post]
func (h *CheckinHandler) CreateCheckin(c *gin.Context) {
	var req models.CheckinCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 设置用户ID
	req.UserID = userID

	// 验证孩子ID
	req.ChildID = h.getChildID(c)
	if req.ChildID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 调用服务层
	result, err := h.checkinService.CreateCheckin(&req)
	if err != nil {
		logger.Error("Failed to create checkin", "user_id", userID, "child_id", req.ChildID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	// 处理打卡完成后的系统更新
	if err := h.growthSystemService.HandleCheckinComplete(req.ChildID, req.CampID, result.ID); err != nil {
		logger.Error("Failed to handle checkin complete", "checkin_id", result.ID, "error", err)
		// 系统更新失败不影响打卡成功，只记录错误
	}

	response.Success(c, result)
}

// GetCheckinHistory 获取打卡历史
// @Summary 获取打卡历史
// @Description 获取用户在指定训练营的打卡历史记录
// @Tags 打卡管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param camp_id query int true "训练营ID"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=api.CheckinHistoryResponse}
// @Router /checkins/history [get]
func (h *CheckinHandler) GetCheckinHistory(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 验证孩子ID
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 绑定请求参数
	var req models.CheckinHistoryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.Limit < 1 || req.Limit > 100 {
		req.Limit = 10
	}

	offset := (req.Page - 1) * req.Limit

	// 调用服务层
	result, err := h.checkinService.GetCheckinHistory(uint(childID), req.CampID, offset, req.Limit)
	if err != nil {
		logger.Error("Failed to get checkin history", "user_id", userID, "child_id", childID, "camp_id", req.CampID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetTodayCheckinStatus 获取今日打卡状态
// @Summary 获取今日打卡状态
// @Description 获取用户今日的打卡状态
// @Tags 打卡管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param camp_id query int true "训练营ID"
// @Success 200 {object} response.Response{data=api.TodayCheckinStatus}
// @Router /checkins/today [get]
func (h *CheckinHandler) GetTodayCheckinStatus(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 验证孩子ID
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 绑定请求参数
	var req models.CheckinTodayStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 调用服务层
	result, err := h.checkinService.GetTodayCheckinStatus(uint(childID), req.CampID)
	if err != nil {
		logger.Error("Failed to get today checkin status", "user_id", userID, "child_id", childID, "camp_id", req.CampID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetCheckinStats 获取打卡统计
// @Summary 获取打卡统计
// @Description 获取用户在指定训练营的打卡统计信息
// @Tags 打卡管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param camp_id query int true "训练营ID"
// @Success 200 {object} response.Response{data=api.CheckinStatsResponse}
// @Router /checkins/stats [get]
func (h *CheckinHandler) GetCheckinStats(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 验证孩子ID
	childID := h.getChildID(c)
	if childID == 0 {
		response.BadRequest(c, "请选择孩子")
		return
	}

	// 绑定请求参数
	var req models.CheckinStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 调用服务层
	result, err := h.checkinService.GetCheckinStats(uint(childID), req.CampID)
	if err != nil {
		logger.Error("Failed to get checkin stats", "user_id", userID, "child_id", childID, "camp_id", req.CampID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetCampCheckinCalendar 获取训练营打卡日历
// @Summary 获取训练营打卡日历
// @Description 获取指定训练营的打卡日历数据，包含所有日期状态和补卡信息
// @Tags 打卡管理
// @Accept json
// @Produce json
// @Param camp_id path int true "训练营ID"
// @Param child_id path int true "孩子ID"
// @Success 200 {object} response.Response{data=dto.CampCheckinCalendarResponse}
// @Router /camps/{camp_id}/checkin-calendar/{child_id} [get]
func (h *CheckinHandler) GetCampCheckinCalendar(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取训练营ID
	campIDStr := c.Param("camp_id")
	if campIDStr == "" {
		response.BadRequest(c, "请提供训练营ID")
		return
	}

	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的训练营ID")
		return
	}

	// 获取孩子ID
	childIDStr := c.Param("child_id")
	if childIDStr == "" {
		response.BadRequest(c, "请提供孩子ID")
		return
	}

	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的孩子ID")
		return
	}

	// 调用服务层
	result, err := h.checkinService.GetCampCheckinCalendar(uint(childID), uint(campID))
	if err != nil {
		logger.Error("Failed to get camp checkin calendar", "user_id", userID, "child_id", childID, "camp_id", campID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *CheckinHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}

// getChildID 从上下文获取孩子ID
func (h *CheckinHandler) getChildID(c *gin.Context) uint {
	if childID, exists := c.Get("child_id"); exists {
		// ChildrenMiddleware设置的是uint类型
		if id, ok := childID.(uint); ok {
			return id
		}
	}
	return 0
}
