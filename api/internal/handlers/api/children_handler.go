package api

import (
	"net/http"
	"strconv"

	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// ChildrenHandler 孩子管理处理器
type ChildrenHandler struct {
	childrenService api.ChildrenService
}

// NewChildrenHandler 创建孩子管理处理器
func NewChildrenHandler(childrenService api.ChildrenService) *ChildrenHandler {
	return &ChildrenHandler{
		childrenService: childrenService,
	}
}

// CreateChild 创建孩子档案
// @Summary 创建孩子档案
// @Description 为当前用户创建新的孩子档案
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ChildrenCreateRequest true "创建孩子档案请求"
// @Success 200 {object} response.Response{data=models.ChildrenResponse} "创建成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children [post]
func (h *ChildrenHandler) CreateChild(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	var req models.ChildrenCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid create child request", "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 创建孩子档案
	child, err := h.childrenService.CreateChild(uint(uid), &req)
	if err != nil {
		logger.Error("Failed to create child", "user_id", uid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("Child created successfully", "user_id", uid, "child_id", child.ID)
	response.Success(c, child)
}

// GetChildByID 获取孩子详情
// @Summary 获取孩子详情
// @Description 获取指定孩子的详细信息
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "孩子ID"
// @Success 200 {object} response.Response{data=models.ChildrenResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "无权限访问"
// @Failure 404 {object} response.Response "孩子不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children/{id} [get]
func (h *ChildrenHandler) GetChildByID(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取孩子ID
	childIDStr := c.Param("id")
	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid child ID", "child_id", childIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID格式错误")
		return
	}

	// 获取孩子信息
	child, err := h.childrenService.GetChildByID(uint(uid), uint(childID))
	if err != nil {
		logger.Error("Failed to get child", "user_id", uid, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, child)
}

// UpdateChild 更新孩子信息
// @Summary 更新孩子信息
// @Description 更新指定孩子的信息
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "孩子ID"
// @Param request body models.ChildrenUpdateRequest true "更新孩子信息请求"
// @Success 200 {object} response.Response{data=models.ChildrenResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "无权限访问"
// @Failure 404 {object} response.Response "孩子不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children/{id} [put]
func (h *ChildrenHandler) UpdateChild(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取孩子ID
	childIDStr := c.Param("id")
	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid child ID", "child_id", childIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID格式错误")
		return
	}

	var req models.ChildrenUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid update child request", "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 更新孩子信息
	child, err := h.childrenService.UpdateChild(uint(uid), uint(childID), &req)
	if err != nil {
		logger.Error("Failed to update child", "user_id", uid, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("Child updated successfully", "user_id", uid, "child_id", childID)
	response.Success(c, child)
}

// DeleteChild 删除孩子档案
// @Summary 删除孩子档案
// @Description 软删除指定的孩子档案
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "孩子ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "无权限访问"
// @Failure 404 {object} response.Response "孩子不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children/{id} [delete]
func (h *ChildrenHandler) DeleteChild(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取孩子ID
	childIDStr := c.Param("id")
	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid child ID", "child_id", childIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID格式错误")
		return
	}

	// 删除孩子档案
	if err := h.childrenService.DeleteChild(uint(uid), uint(childID)); err != nil {
		logger.Error("Failed to delete child", "user_id", uid, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("Child deleted successfully", "user_id", uid, "child_id", childID)
	response.Success(c, gin.H{"message": "孩子档案删除成功"})
}

// GetChildrenList 获取孩子列表
// @Summary 获取孩子列表
// @Description 获取当前用户管理的所有孩子列表
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=[]models.ChildrenResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children [get]
func (h *ChildrenHandler) GetChildrenList(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取孩子列表
	children, err := h.childrenService.GetChildrenByUserID(uint(uid))
	if err != nil {
		logger.Error("Failed to get children list", "user_id", uid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, children)
}

// GetCurrentChild 获取当前选择的孩子
// @Summary 获取当前选择的孩子
// @Description 获取用户当前选择的孩子信息
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=models.ChildrenResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "当前孩子不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children/current [get]
func (h *ChildrenHandler) GetCurrentChild(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取当前孩子
	child, err := h.childrenService.GetCurrentChild(uint(uid))
	if err != nil {
		logger.Error("Failed to get current child", "user_id", uid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, child)
}

// SelectCurrentChild 选择当前孩子
// @Summary 选择当前孩子
// @Description 设置用户当前选择的孩子
// @Tags 孩子管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "孩子ID"
// @Success 200 {object} response.Response "选择成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "无权限访问"
// @Failure 404 {object} response.Response "孩子不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /children/{id}/select [post]
func (h *ChildrenHandler) SelectCurrentChild(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取孩子ID
	childIDStr := c.Param("id")
	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid child ID", "child_id", childIDStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "孩子ID格式错误")
		return
	}

	// 选择当前孩子
	if err := h.childrenService.SelectCurrentChild(uint(uid), uint(childID)); err != nil {
		logger.Error("Failed to select current child", "user_id", uid, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("Current child selected successfully", "user_id", uid, "child_id", childID)
	response.Success(c, gin.H{"message": "当前孩子选择成功"})
}
