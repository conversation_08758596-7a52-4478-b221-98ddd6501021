package ecode

import "kids-platform/pkg/errcode"

// 业务错误码定义 - 按模块分段，每个模块100个错误码
// 错误码范围分配（业务级错误码 20000+）：
// 用户模块: 20000-20099
// 打卡模块: 20100-20199
// 内容模块: 20200-20299
// 任务模块: 20300-20399
// 社交模块: 20400-20499
// 积分模块: 20500-20599

// NewBusinessError 创建业务错误的便捷函数
// 推荐直接使用 errcode.NewError，这个函数保留是为了向后兼容
func NewBusinessError(code int, message string) error {
	return errcode.NewError(code, message)
}
