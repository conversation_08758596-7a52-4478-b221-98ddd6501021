package ecode

import "kids-platform/pkg/errcode"

// 打卡模块错误码 (20100-20199)
// 业务级错误码，使用pkg/errcode.NewError创建

var (
	// 打卡创建相关 (20100-20119)
	ErrCheckinAlreadyExists     = errcode.NewError(20101, "今日已经打卡")
	ErrCheckinDateInvalid       = errcode.NewError(20102, "打卡日期无效")
	ErrCheckinDataInvalid       = errcode.NewError(20103, "打卡数据无效")
	ErrCheckinPhotoUploadFailed = errcode.NewError(20104, "打卡照片上传失败")
	ErrCheckinDurationInvalid   = errcode.NewError(20105, "练习时长无效")
	ErrCheckinScoreInvalid      = errcode.NewError(20106, "打卡成绩无效")
	ErrCheckinCampNotFound      = errcode.NewError(20107, "训练营不存在")
	ErrCheckinCampNotActive     = errcode.NewError(20108, "训练营未激活")
	ErrCheckinPermissionDenied  = errcode.NewError(20109, "无权限打卡")
	ErrCheckinTooEarly          = errcode.NewError(20110, "打卡时间过早")
	ErrCheckinTooLate           = errcode.NewError(20111, "打卡时间过晚")

	// 打卡查询相关 (20120-20139)
	ErrCheckinRecordNotFound    = errcode.NewError(20121, "打卡记录不存在")
	ErrCheckinStatsNotFound     = errcode.NewError(20122, "打卡统计不存在")
	ErrCheckinHistoryNotFound   = errcode.NewError(20123, "打卡历史不存在")
	ErrCheckinCalendarNotFound  = errcode.NewError(20124, "打卡日历不存在")

	// 补打卡相关 (20140-20159)
	ErrMakeupCheckinNotAllowed  = errcode.NewError(20141, "不允许补打卡")
	ErrMakeupCheckinExpired     = errcode.NewError(20142, "补打卡已过期")
	ErrMakeupCheckinLimitExceeded = errcode.NewError(20143, "补打卡次数超限")

	// 打卡奖励相关 (20160-20179)
	ErrCheckinRewardCalculationFailed = errcode.NewError(20161, "打卡奖励计算失败")
	ErrCheckinPointsUpdateFailed      = errcode.NewError(20162, "打卡积分更新失败")
	ErrCheckinMedalUpdateFailed       = errcode.NewError(20163, "打卡勋章更新失败")

	// 打卡状态相关 (20180-20199)
	ErrCheckinStatusInvalid     = errcode.NewError(20181, "打卡状态无效")
	ErrCheckinStatusUpdateFailed = errcode.NewError(20182, "打卡状态更新失败")
)
