package ecode

import "kids-platform/pkg/errcode"

// 用户模块错误码 (20000-20099)
// 业务级错误码，使用pkg/errcode.NewError创建

var (
	// 用户注册相关 (20000-20019)
	ErrUserAlreadyExists           = errcode.NewError(20001, "用户已存在")
	ErrUserNotFound                = errcode.NewError(20002, "用户不存在")
	ErrInvalidVerificationCode     = errcode.NewError(20003, "验证码错误或已过期")
	ErrPhoneAlreadyRegistered      = errcode.NewError(20004, "手机号已注册")
	ErrEmailAlreadyRegistered      = errcode.NewError(20005, "邮箱已注册")
	ErrWeakPassword                = errcode.NewError(20006, "密码强度不够")
	ErrInvalidPhoneFormat          = errcode.NewError(20007, "手机号格式错误")
	ErrInvalidEmailFormat          = errcode.NewError(20008, "邮箱格式错误")
	ErrVerificationCodeExpired     = errcode.NewError(20009, "验证码已过期")
	ErrTooManyVerificationRequests = errcode.NewError(20010, "验证码请求过于频繁")

	// 用户认证相关 (20020-20039)
	ErrInvalidCredentials      = errcode.NewError(20021, "用户名或密码错误")
	ErrAccountLocked           = errcode.NewError(20022, "账户被锁定")
	ErrAccountDisabled         = errcode.NewError(20023, "账户被禁用")
	ErrAccountNotActivated     = errcode.NewError(20024, "账户未激活")
	ErrInvalidToken            = errcode.NewError(20025, "无效的令牌")
	ErrTokenExpired            = errcode.NewError(20026, "令牌已过期")
	ErrInsufficientPermissions = errcode.NewError(20027, "权限不足")
	ErrLoginRequired           = errcode.NewError(20028, "需要登录")
	ErrTooManyLoginAttempts    = errcode.NewError(20029, "登录尝试次数过多")
	ErrTokenGenerationFailed   = errcode.NewError(20030, "Token生成失败")
	ErrInvalidRefreshToken     = errcode.NewError(20031, "无效的刷新令牌")
	ErrRefreshTokenExpired     = errcode.NewError(20032, "刷新令牌已过期")

	// 用户信息相关 (20040-20059)
	ErrInvalidUserInfo       = errcode.NewError(20041, "用户信息无效")
	ErrNicknameAlreadyExists = errcode.NewError(20042, "昵称已存在")
	ErrInvalidNicknameFormat = errcode.NewError(20043, "昵称格式错误")
	ErrInvalidAvatarFormat   = errcode.NewError(20044, "头像格式错误")
	ErrProfileUpdateFailed   = errcode.NewError(20045, "个人资料更新失败")
	ErrPasswordChangeFailed  = errcode.NewError(20046, "密码修改失败")
	ErrOldPasswordIncorrect  = errcode.NewError(20047, "原密码错误")
	ErrSameAsOldPassword     = errcode.NewError(20048, "新密码与原密码相同")

	// 微信相关 (20060-20079)
	ErrWeChatAuthFailed     = errcode.NewError(20061, "微信授权失败")
	ErrInvalidWeChatCode    = errcode.NewError(20062, "微信授权码无效")
	ErrWeChatUserInfoFailed = errcode.NewError(20063, "获取微信用户信息失败")
	ErrWeChatBindFailed     = errcode.NewError(20064, "微信绑定失败")
	ErrWeChatUnbindFailed   = errcode.NewError(20065, "微信解绑失败")
	ErrWeChatAlreadyBound   = errcode.NewError(20066, "微信已绑定其他账户")
)
