package testutils

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/config"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// InitTestEnvironment 初始化测试环境
// 统一处理logger初始化、Gin模式设置等
func InitTestEnvironment() {
	// 初始化logger（避免重复初始化）
	if !logger.IsInitialized() {
		logger.Init(config.LogConfig{
			Level:  "debug",
			Format: "text",
			Output: "stdout",
		})
	}

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)
}

// CreateTestDB 创建测试数据库
func CreateTestDB() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	return db, nil
}

// MockChildrenRepository 模拟孩子仓储
type MockChildrenRepository struct {
	children map[uint]*models.Children
	deleted  map[uint]bool
	mutex    sync.RWMutex
	nextID   uint
}

// NewMockChildrenRepository 创建模拟孩子仓储
func NewMockChildrenRepository() *MockChildrenRepository {
	return &MockChildrenRepository{
		children: make(map[uint]*models.Children),
		deleted:  make(map[uint]bool),
		nextID:   1,
	}
}

// AddTestChild 添加测试孩子数据
func (m *MockChildrenRepository) AddTestChild(child *models.Children) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if child.ID == 0 {
		child.ID = m.nextID
		m.nextID++
	}
	child.CreatedAt = time.Now()
	child.UpdatedAt = time.Now()
	m.children[child.ID] = child
}

// Create 创建孩子档案
func (m *MockChildrenRepository) Create(child *models.Children) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	child.ID = m.nextID
	m.nextID++
	child.CreatedAt = time.Now()
	child.UpdatedAt = time.Now()
	m.children[child.ID] = child
	return nil
}

// GetByID 根据ID获取孩子档案
func (m *MockChildrenRepository) GetByID(id uint) (*models.Children, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.deleted[id] {
		return nil, errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	child, exists := m.children[id]
	if !exists {
		return nil, errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	// 返回副本
	result := *child
	return &result, nil
}

// Update 更新孩子档案
func (m *MockChildrenRepository) Update(id uint, child *models.Children) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.deleted[id] {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	existing, exists := m.children[id]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	// 更新字段
	if child.Name != "" {
		existing.Name = child.Name
	}
	if child.Nickname != "" {
		existing.Nickname = child.Nickname
	}
	if child.Gender != 0 {
		existing.Gender = child.Gender
	}
	existing.UpdatedAt = time.Now()

	return nil
}

// Delete 软删除孩子档案
func (m *MockChildrenRepository) Delete(id uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.children[id]; !exists {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	m.deleted[id] = true
	return nil
}

// List 获取孩子档案列表
func (m *MockChildrenRepository) List(offset, limit int) ([]*models.Children, int64, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.Children
	for id, child := range m.children {
		if !m.deleted[id] {
			childCopy := *child
			result = append(result, &childCopy)
		}
	}

	return result, int64(len(result)), nil
}

// GetByUserID 获取用户管理的所有孩子
func (m *MockChildrenRepository) GetByUserID(userID uint) ([]*models.Children, error) {
	// 简化实现，返回所有未删除的孩子
	children, _, err := m.List(0, 100)
	return children, err
}

// GetCurrentChildByUserID 获取用户当前选中的孩子
func (m *MockChildrenRepository) GetCurrentChildByUserID(userID uint) (*models.Children, error) {
	// 简化实现，返回第一个未删除的孩子
	children, err := m.GetByUserID(userID)
	if err != nil {
		return nil, err
	}
	if len(children) == 0 {
		return nil, errcode.ErrDataNotFound.WithDetails("没有找到孩子")
	}
	return children[0], nil
}

// MockUserChildrenRepository 模拟用户孩子关联仓储
type MockUserChildrenRepository struct {
	userChildren map[uint]*models.UserChildren
	deleted      map[uint]bool
	mutex        sync.RWMutex
	nextID       uint
}

// NewMockUserChildrenRepository 创建模拟用户孩子关联仓储
func NewMockUserChildrenRepository() *MockUserChildrenRepository {
	return &MockUserChildrenRepository{
		userChildren: make(map[uint]*models.UserChildren),
		deleted:      make(map[uint]bool),
		nextID:       1,
	}
}

// AddTestUserChild 添加测试用户孩子关联数据
func (m *MockUserChildrenRepository) AddTestUserChild(userChild *models.UserChildren) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if userChild.ID == 0 {
		userChild.ID = m.nextID
		m.nextID++
	}
	userChild.CreatedAt = time.Now()
	userChild.UpdatedAt = time.Now()
	m.userChildren[userChild.ID] = userChild
}

// Create 创建用户孩子关联
func (m *MockUserChildrenRepository) Create(userChild *models.UserChildren) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	userChild.ID = m.nextID
	m.nextID++
	userChild.CreatedAt = time.Now()
	userChild.UpdatedAt = time.Now()
	m.userChildren[userChild.ID] = userChild
	return nil
}

// GetByUserAndChild 根据用户ID和孩子ID获取关联
func (m *MockUserChildrenRepository) GetByUserAndChild(userID, childID uint) (*models.UserChildren, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for id, uc := range m.userChildren {
		if !m.deleted[id] && uc.UserID == uint64(userID) && uc.ChildID == uint64(childID) {
			result := *uc
			return &result, nil
		}
	}

	return nil, errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
}

// DeleteByUserAndChild 根据用户ID和孩子ID软删除关联
func (m *MockUserChildrenRepository) DeleteByUserAndChild(userID, childID uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for id, uc := range m.userChildren {
		if !m.deleted[id] && uc.UserID == uint64(userID) && uc.ChildID == uint64(childID) {
			m.deleted[id] = true
			return nil
		}
	}

	return errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
}

// GetByID 根据ID获取用户孩子关联
func (m *MockUserChildrenRepository) GetByID(id uint) (*models.UserChildren, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.deleted[id] {
		return nil, errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
	}

	uc, exists := m.userChildren[id]
	if !exists {
		return nil, errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
	}

	result := *uc
	return &result, nil
}

// Update 更新用户孩子关联
func (m *MockUserChildrenRepository) Update(id uint, userChild *models.UserChildren) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.deleted[id] {
		return errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
	}

	existing, exists := m.userChildren[id]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
	}

	// 更新字段
	if userChild.Relation != "" {
		existing.Relation = userChild.Relation
	}
	existing.UpdatedAt = time.Now()

	return nil
}

// Delete 软删除用户孩子关联
func (m *MockUserChildrenRepository) Delete(id uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.userChildren[id]; !exists {
		return errcode.ErrDataNotFound.WithDetails("用户孩子关联不存在")
	}

	m.deleted[id] = true
	return nil
}

// GetChildrenByUserID 获取用户管理的所有孩子关联
func (m *MockUserChildrenRepository) GetChildrenByUserID(userID uint) ([]*models.UserChildren, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.UserChildren
	for id, uc := range m.userChildren {
		if !m.deleted[id] && uc.UserID == uint64(userID) {
			ucCopy := *uc
			result = append(result, &ucCopy)
		}
	}

	return result, nil
}

// GetUsersByChildID 获取管理某个孩子的所有用户关联
func (m *MockUserChildrenRepository) GetUsersByChildID(childID uint) ([]*models.UserChildren, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.UserChildren
	for id, uc := range m.userChildren {
		if !m.deleted[id] && uc.ChildID == uint64(childID) {
			ucCopy := *uc
			result = append(result, &ucCopy)
		}
	}

	return result, nil
}

// CheckUserCanManageChild 检查用户是否可以管理某个孩子
func (m *MockUserChildrenRepository) CheckUserCanManageChild(userID, childID uint) (bool, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for id, uc := range m.userChildren {
		if !m.deleted[id] && uc.UserID == uint64(userID) && uc.ChildID == uint64(childID) {
			return true, nil
		}
	}

	return false, nil
}

// UpdateMakeupCount 更新补卡使用次数
func (m *MockChildrenRepository) UpdateMakeupCount(childID uint, usedCount uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.deleted[childID] {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	child, exists := m.children[childID]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	child.MakeupUsedCount = usedCount
	child.UpdatedAt = time.Now()
	return nil
}

// GetMakeupInfo 获取补卡信息
func (m *MockChildrenRepository) GetMakeupInfo(childID uint) (usedCount uint, totalCount uint, err error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.deleted[childID] {
		return 0, 0, errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	child, exists := m.children[childID]
	if !exists {
		return 0, 0, errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	return child.MakeupUsedCount, child.MakeupTotalCount, nil
}

// UseMakeupCard 使用补卡：makeup_used_count+1, makeup_total_count-1
func (m *MockChildrenRepository) UseMakeupCard(childID uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.deleted[childID] {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	child, exists := m.children[childID]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
	}

	if child.MakeupTotalCount == 0 {
		return errcode.ErrValidation.WithDetails("补卡次数不足或孩子不存在")
	}

	child.MakeupUsedCount++
	child.MakeupTotalCount--
	child.UpdatedAt = time.Now()
	return nil
}

// MockUsersRepository 模拟用户仓储
type MockUsersRepository struct {
	users  map[uint]*models.Users
	mutex  sync.RWMutex
	nextID uint
}

// NewMockUsersRepository 创建模拟用户仓储
func NewMockUsersRepository() *MockUsersRepository {
	return &MockUsersRepository{
		users:  make(map[uint]*models.Users),
		nextID: 1,
	}
}

// AddTestUser 添加测试用户数据
func (m *MockUsersRepository) AddTestUser(user *models.Users) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if user.ID == 0 {
		user.ID = m.nextID
		m.nextID++
	}
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	m.users[user.ID] = user
}

// GetByID 根据ID获取用户
func (m *MockUsersRepository) GetByID(id uint) (*models.Users, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	user, exists := m.users[id]
	if !exists {
		return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 返回副本
	result := *user
	return &result, nil
}

// Update 更新用户
func (m *MockUsersRepository) Update(id uint, user *models.Users) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	existing, exists := m.users[id]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 更新字段 - 使用GORM的Updates方式，只更新非零值字段
	// 但对于CurrentChildID，我们需要特殊处理，因为0是有效值
	// 在实际的GORM中，我们会使用Select来明确指定要更新的字段
	// 这里简化处理，直接更新所有传入的字段
	existing.CurrentChildID = user.CurrentChildID
	if user.Nickname != "" {
		existing.Nickname = user.Nickname
	}
	if user.Avatar != "" {
		existing.Avatar = user.Avatar
	}
	if user.Phone != "" {
		existing.Phone = user.Phone
	}
	if user.Gender != 0 {
		existing.Gender = user.Gender
	}
	if user.Province != "" {
		existing.Province = user.Province
	}
	if user.City != "" {
		existing.City = user.City
	}
	existing.UpdatedAt = time.Now()

	return nil
}

// Create 创建用户
func (m *MockUsersRepository) Create(user *models.Users) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	user.ID = m.nextID
	m.nextID++
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	m.users[user.ID] = user
	return nil
}

// GetByOpenID 根据微信OpenID获取用户
func (m *MockUsersRepository) GetByOpenID(openid string) (*models.Users, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, user := range m.users {
		if user.OpenID == openid {
			result := *user
			return &result, nil
		}
	}

	return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
}

// Delete 软删除用户
func (m *MockUsersRepository) Delete(id uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.users[id]; !exists {
		return errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 在Mock中直接删除
	delete(m.users, id)
	return nil
}

// List 获取用户列表
func (m *MockUsersRepository) List(offset, limit int) ([]*models.Users, int64, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.Users
	for _, user := range m.users {
		userCopy := *user
		result = append(result, &userCopy)
	}

	total := int64(len(result))

	// 简单的分页处理
	if offset >= len(result) {
		return []*models.Users{}, total, nil
	}

	end := offset + limit
	if end > len(result) {
		end = len(result)
	}

	return result[offset:end], total, nil
}

// GetTestConfig 获取测试配置
func GetTestConfig() *config.Config {
	return &config.Config{
		JWT: config.JWTConfig{
			Secret:      "test_secret_key_for_testing",
			ExpireHours: 24,
		},
		Wechat: config.WechatConfig{
			Miniprogram: config.MiniprogramConfig{
				AppID:          "test_app_id",
				AppSecret:      "test_app_secret",
				MockEnabled:    true,
				MockOpenID:     "test_mock_openid",
				MockSessionKey: "test_mock_session_key",
				MockUnionID:    "test_mock_unionid",
				AuthURL:        "https://api.weixin.qq.com/sns/jscode2session",
				Timeout:        5000,
			},
		},
		Security: config.SecurityConfig{
			ProductionMode: false,
			DebugMode:      true,
		},
	}
}

// GetIntegrationTestConfig 获取集成测试配置
func GetIntegrationTestConfig() *config.Config {
	return &config.Config{
		JWT: config.JWTConfig{
			Secret:      "integration_test_secret_key",
			ExpireHours: 24,
		},
		Wechat: config.WechatConfig{
			Miniprogram: config.MiniprogramConfig{
				AppID:          "integration_test_app_id",
				AppSecret:      "integration_test_app_secret",
				MockEnabled:    true,
				MockOpenID:     "integration_test_openid",
				MockSessionKey: "integration_test_session_key",
				MockUnionID:    "integration_test_unionid",
				AuthURL:        "https://api.weixin.qq.com/sns/jscode2session",
				Timeout:        5000,
			},
		},
		Security: config.SecurityConfig{
			ProductionMode: false,
			DebugMode:      true,
		},
	}
}
