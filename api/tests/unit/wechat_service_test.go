package unit

import (
	"testing"

	"kids-platform/pkg/config"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/wechat"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// WechatServiceTestSuite 微信服务测试套件
type WechatServiceTestSuite struct {
	suite.Suite
	wechatService *wechat.MiniprogramService
	config        *config.Config
}

// SetupTest 设置测试
func (suite *WechatServiceTestSuite) SetupTest() {
	// 初始化logger
	logger.Init(config.LogConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})

	suite.config = &config.Config{
		Wechat: config.WechatConfig{
			Miniprogram: config.MiniprogramConfig{
				AppID:          "test_app_id",
				AppSecret:      "test_app_secret",
				MockEnabled:    true,
				MockOpenID:     "mock_openid_test",
				MockSessionKey: "mock_session_key_test",
				MockUnionID:    "mock_unionid_test",
				AuthURL:        "https://api.weixin.qq.com/sns/jscode2session",
				Timeout:        5000,
			},
		},
	}

	suite.wechatService = wechat.NewMiniprogramService(suite.config)
}

// TestCode2Session_MockMode 测试Mock模式下的code2session
func (suite *WechatServiceTestSuite) TestCode2Session_MockMode() {
	// 执行测试
	result, err := suite.wechatService.Code2Session("test_code")

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "mock_openid_test", result.OpenID)
	assert.Equal(suite.T(), "mock_session_key_test", result.SessionKey)
	assert.Equal(suite.T(), "mock_unionid_test", result.UnionID)
	assert.Equal(suite.T(), 0, result.ErrCode)
	assert.Equal(suite.T(), "", result.ErrMsg)
}

// TestDecryptUserInfo_MockMode 测试Mock模式下的用户信息解密
func (suite *WechatServiceTestSuite) TestDecryptUserInfo_MockMode() {
	// 执行测试
	result, err := suite.wechatService.DecryptUserInfo(
		"encrypted_data",
		"iv",
		"session_key",
	)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "mock_openid_test", result.OpenID)
	assert.Equal(suite.T(), "Mock用户", result.NickName)
	assert.Equal(suite.T(), 1, result.Gender)
	assert.Equal(suite.T(), "深圳", result.City)
	assert.Equal(suite.T(), "广东", result.Province)
	assert.Equal(suite.T(), "中国", result.Country)
	assert.Equal(suite.T(), "mock_unionid_test", result.UnionID)
	assert.Contains(suite.T(), result.AvatarURL, "mock_avatar.png")
}

// TestCode2Session_RealMode 测试真实模式（需要真实的微信配置）
func (suite *WechatServiceTestSuite) TestCode2Session_RealMode() {
	// 禁用Mock模式
	suite.config.Wechat.Miniprogram.MockEnabled = false

	// 由于没有真实的微信配置，这个测试会失败
	// 在实际环境中，需要配置真实的AppID和AppSecret
	result, err := suite.wechatService.Code2Session("invalid_code")

	// 在Mock禁用且没有真实配置的情况下，应该返回错误
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
}

// TestNewMiniprogramService 测试创建微信小程序服务
func (suite *WechatServiceTestSuite) TestNewMiniprogramService() {
	service := wechat.NewMiniprogramService(suite.config)
	assert.NotNil(suite.T(), service)
}

// TestSuite 运行测试套件
func TestWechatServiceSuite(t *testing.T) {
	suite.Run(t, new(WechatServiceTestSuite))
}
