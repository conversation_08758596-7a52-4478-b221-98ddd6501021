package unit

import (
	"testing"

	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/tests/testutils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// ChildrenServiceTestSuite 孩子管理服务测试套件
type ChildrenServiceTestSuite struct {
	suite.Suite
	childrenService api.ChildrenService
	childrenRepo    *testutils.MockChildrenRepository
	userChildrenRepo *testutils.MockUserChildrenRepository
	userRepo        *testutils.MockUsersRepository
}

// SetupTest 设置测试环境
func (suite *ChildrenServiceTestSuite) SetupTest() {
	testutils.InitTestEnvironment()

	// 创建模拟仓储
	suite.childrenRepo = testutils.NewMockChildrenRepository()
	suite.userChildrenRepo = testutils.NewMockUserChildrenRepository()
	suite.userRepo = testutils.NewMockUsersRepository()

	// 创建服务
	suite.childrenService = api.NewChildrenService(
		suite.childrenRepo,
		suite.userChildrenRepo,
		suite.userRepo,
	)
}

// TestCreateChild_Success 测试创建孩子成功
func (suite *ChildrenServiceTestSuite) TestCreateChild_Success() {
	// 准备测试数据
	userID := uint(1)
	req := &models.ChildrenCreateRequest{
		Name:      "小明",
		Nickname:  "明明",
		Gender:    1,
		BirthDate: "2015-06-15",
		Relation:  "爸爸",
	}

	// 准备用户数据
	user := &models.Users{
		BaseModel:      models.BaseModel{ID: userID},
		CurrentChildID: 0,
	}
	suite.userRepo.AddTestUser(user)

	// 执行测试
	result, err := suite.childrenService.CreateChild(userID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "小明", result.Name)
	assert.Equal(suite.T(), "明明", result.Nickname)
	assert.Equal(suite.T(), int8(1), result.Gender)
}

// TestDeleteChild_Success 测试删除孩子成功
func (suite *ChildrenServiceTestSuite) TestDeleteChild_Success() {
	// 准备测试数据
	userID := uint(1)
	childID := uint(100)

	// 准备孩子数据
	child := &models.Children{
		BaseModel: models.BaseModel{ID: childID},
		Name:      "小明",
		Status:    1,
	}
	suite.childrenRepo.AddTestChild(child)

	// 准备用户孩子关联
	userChild := &models.UserChildren{
		UserID:   uint64(userID),
		ChildID:  uint64(childID),
		Relation: "爸爸",
	}
	suite.userChildrenRepo.AddTestUserChild(userChild)

	// 准备用户数据
	user := &models.Users{
		BaseModel:      models.BaseModel{ID: userID},
		CurrentChildID: uint64(childID), // 当前选中的就是要删除的孩子
	}
	suite.userRepo.AddTestUser(user)

	// 执行测试
	err := suite.childrenService.DeleteChild(userID, childID)

	// 验证结果
	assert.NoError(suite.T(), err)

	// 验证孩子已被软删除
	deletedChild, err := suite.childrenRepo.GetByID(childID)
	assert.Error(suite.T(), err) // 应该返回错误，因为已被软删除
	assert.Nil(suite.T(), deletedChild)

	// 验证用户的current_child_id已被重置
	updatedUser, err := suite.userRepo.GetByID(userID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), uint64(0), updatedUser.CurrentChildID)
}

// TestDeleteChild_NotCurrentChild 测试删除非当前选中的孩子
func (suite *ChildrenServiceTestSuite) TestDeleteChild_NotCurrentChild() {
	// 准备测试数据
	userID := uint(1)
	childID := uint(100)
	currentChildID := uint(200)

	// 准备孩子数据
	child := &models.Children{
		BaseModel: models.BaseModel{ID: childID},
		Name:      "小明",
		Status:    1,
	}
	suite.childrenRepo.AddTestChild(child)

	// 准备用户孩子关联
	userChild := &models.UserChildren{
		UserID:   uint64(userID),
		ChildID:  uint64(childID),
		Relation: "爸爸",
	}
	suite.userChildrenRepo.AddTestUserChild(userChild)

	// 准备用户数据（当前选中的是另一个孩子）
	user := &models.Users{
		BaseModel:      models.BaseModel{ID: userID},
		CurrentChildID: uint64(currentChildID),
	}
	suite.userRepo.AddTestUser(user)

	// 执行测试
	err := suite.childrenService.DeleteChild(userID, childID)

	// 验证结果
	assert.NoError(suite.T(), err)

	// 验证用户的current_child_id没有被重置
	updatedUser, err := suite.userRepo.GetByID(userID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), uint64(currentChildID), updatedUser.CurrentChildID)
}

// TestUpdateChild_Success 测试更新孩子信息成功
func (suite *ChildrenServiceTestSuite) TestUpdateChild_Success() {
	// 准备测试数据
	userID := uint(1)
	childID := uint(100)

	// 准备孩子数据
	child := &models.Children{
		BaseModel: models.BaseModel{ID: childID},
		Name:      "小明",
		Nickname:  "明明",
		Gender:    1,
		Status:    1,
	}
	suite.childrenRepo.AddTestChild(child)

	// 准备用户孩子关联
	userChild := &models.UserChildren{
		UserID:   uint64(userID),
		ChildID:  uint64(childID),
		Relation: "爸爸",
	}
	suite.userChildrenRepo.AddTestUserChild(userChild)

	// 准备更新请求
	req := &models.ChildrenUpdateRequest{
		Name:     "小明明",
		Nickname: "明明宝贝",
		Gender:   2,
	}

	// 执行测试
	result, err := suite.childrenService.UpdateChild(userID, childID, req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "小明明", result.Name)
	assert.Equal(suite.T(), "明明宝贝", result.Nickname)
	assert.Equal(suite.T(), int8(2), result.Gender)
}

// TestGetChildrenByUserID_Success 测试获取用户孩子列表成功
func (suite *ChildrenServiceTestSuite) TestGetChildrenByUserID_Success() {
	// 准备测试数据
	userID := uint(1)

	// 准备孩子数据
	child1 := &models.Children{
		BaseModel: models.BaseModel{ID: 100},
		Name:      "小明",
		Status:    1,
	}
	child2 := &models.Children{
		BaseModel: models.BaseModel{ID: 200},
		Name:      "小红",
		Status:    1,
	}
	suite.childrenRepo.AddTestChild(child1)
	suite.childrenRepo.AddTestChild(child2)

	// 执行测试
	result, err := suite.childrenService.GetChildrenByUserID(userID)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Len(suite.T(), result, 2)
	assert.Equal(suite.T(), "小明", result[0].Name)
	assert.Equal(suite.T(), "小红", result[1].Name)
}

// TestSuite 运行测试套件
func TestChildrenServiceSuite(t *testing.T) {
	suite.Run(t, new(ChildrenServiceTestSuite))
}
