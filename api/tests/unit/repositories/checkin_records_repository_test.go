package repositories

import (
	"testing"
	"time"

	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 自动迁移表结构
	err = db.AutoMigrate(&models.CheckinRecords{})
	assert.NoError(t, err)

	return db
}

// TestGetByChildAndDateByCampID 测试按孩子ID、训练营ID和日期查询打卡记录
func TestGetByChildAndDateByCampID(t *testing.T) {
	db := setupTestDB(t)
	repo := repositories.NewCheckinRecordsRepository(db)

	// 准备测试数据
	testDate := time.Date(2025, 7, 30, 10, 0, 0, 0, time.UTC)
	
	// 创建测试记录
	record1 := &models.CheckinRecords{
		ChildID:             11,
		CampID:              456,
		PracticeDuration:    15,
		JumpCount1min:       100,
		JumpCountContinuous: 50,
		FeelingText:         "今天跳得很好",
		FeelingScore:        8,
		CheckinDate:         testDate,
	}
	
	// 同一孩子，不同训练营的记录
	record2 := &models.CheckinRecords{
		ChildID:             11,
		CampID:              789, // 不同的训练营ID
		PracticeDuration:    20,
		JumpCount1min:       120,
		JumpCountContinuous: 60,
		FeelingText:         "另一个训练营的打卡",
		FeelingScore:        9,
		CheckinDate:         testDate,
	}

	// 不同孩子，相同训练营的记录
	record3 := &models.CheckinRecords{
		ChildID:             22, // 不同的孩子ID
		CampID:              456,
		PracticeDuration:    10,
		JumpCount1min:       80,
		JumpCountContinuous: 40,
		FeelingText:         "另一个孩子的打卡",
		FeelingScore:        7,
		CheckinDate:         testDate,
	}

	// 插入测试数据
	err := repo.Create(record1)
	assert.NoError(t, err)
	
	err = repo.Create(record2)
	assert.NoError(t, err)
	
	err = repo.Create(record3)
	assert.NoError(t, err)

	t.Run("查询存在的记录", func(t *testing.T) {
		// 查询child_id=11, camp_id=456的记录
		result, err := repo.GetByChildAndDateByCampID(11, 456, testDate)
		
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, uint(11), result.ChildID)
		assert.Equal(t, uint(456), result.CampID)
		assert.Equal(t, 15, result.PracticeDuration)
		assert.Equal(t, "今天跳得很好", result.FeelingText)
	})

	t.Run("查询不存在的记录_错误的训练营ID", func(t *testing.T) {
		// 查询child_id=11, camp_id=999（不存在）的记录
		result, err := repo.GetByChildAndDateByCampID(11, 999, testDate)
		
		assert.Error(t, err)
		assert.Nil(t, result)
		
		// 验证错误类型
		errCodeErr, ok := err.(*errcode.Error)
		assert.True(t, ok)
		assert.Equal(t, errcode.ErrDataNotFound.Code(), errCodeErr.Code())
	})

	t.Run("查询不存在的记录_错误的孩子ID", func(t *testing.T) {
		// 查询child_id=999（不存在）, camp_id=456的记录
		result, err := repo.GetByChildAndDateByCampID(999, 456, testDate)
		
		assert.Error(t, err)
		assert.Nil(t, result)
		
		// 验证错误类型
		errCodeErr, ok := err.(*errcode.Error)
		assert.True(t, ok)
		assert.Equal(t, errcode.ErrDataNotFound.Code(), errCodeErr.Code())
	})

	t.Run("查询不存在的记录_错误的日期", func(t *testing.T) {
		// 查询不同日期的记录
		differentDate := time.Date(2025, 7, 31, 10, 0, 0, 0, time.UTC)
		result, err := repo.GetByChildAndDateByCampID(11, 456, differentDate)
		
		assert.Error(t, err)
		assert.Nil(t, result)
		
		// 验证错误类型
		errCodeErr, ok := err.(*errcode.Error)
		assert.True(t, ok)
		assert.Equal(t, errcode.ErrDataNotFound.Code(), errCodeErr.Code())
	})

	t.Run("验证训练营过滤功能", func(t *testing.T) {
		// 查询child_id=11, camp_id=789的记录（应该返回record2）
		result, err := repo.GetByChildAndDateByCampID(11, 789, testDate)
		
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, uint(11), result.ChildID)
		assert.Equal(t, uint(789), result.CampID)
		assert.Equal(t, 20, result.PracticeDuration)
		assert.Equal(t, "另一个训练营的打卡", result.FeelingText)
	})
}

// TestGetByChildAndDateByCampID_与原方法对比 测试新方法与原方法的区别
func TestGetByChildAndDateByCampID_与原方法对比(t *testing.T) {
	db := setupTestDB(t)
	repo := repositories.NewCheckinRecordsRepository(db)

	// 准备测试数据
	testDate := time.Date(2025, 7, 30, 10, 0, 0, 0, time.UTC)
	
	// 同一孩子在同一天的多个训练营打卡记录
	record1 := &models.CheckinRecords{
		ChildID:      11,
		CampID:       456,
		CheckinDate:  testDate,
		FeelingText:  "训练营456的打卡",
	}
	
	record2 := &models.CheckinRecords{
		ChildID:      11,
		CampID:       789,
		CheckinDate:  testDate,
		FeelingText:  "训练营789的打卡",
	}

	// 插入测试数据
	err := repo.Create(record1)
	assert.NoError(t, err)
	
	err = repo.Create(record2)
	assert.NoError(t, err)

	t.Run("原方法GetByChildAndDate_返回第一条记录", func(t *testing.T) {
		// 原方法不区分训练营，会返回第一条匹配的记录
		result, err := repo.GetByChildAndDate(11, testDate)
		
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, uint(11), result.ChildID)
		// 不确定返回哪个训练营的记录，因为没有ORDER BY
	})

	t.Run("新方法GetByChildAndDateByCampID_精确过滤", func(t *testing.T) {
		// 新方法可以精确查询特定训练营的记录
		result1, err := repo.GetByChildAndDateByCampID(11, 456, testDate)
		assert.NoError(t, err)
		assert.NotNil(t, result1)
		assert.Equal(t, uint(456), result1.CampID)
		assert.Equal(t, "训练营456的打卡", result1.FeelingText)

		result2, err := repo.GetByChildAndDateByCampID(11, 789, testDate)
		assert.NoError(t, err)
		assert.NotNil(t, result2)
		assert.Equal(t, uint(789), result2.CampID)
		assert.Equal(t, "训练营789的打卡", result2.FeelingText)
	})
}
