package unit

import (
	"testing"

	"kids-platform/internal/ecode"
	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/config"
	"kids-platform/pkg/wechat"
	"kids-platform/tests/mocks"
	"kids-platform/tests/testutils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// UserServiceTestSuite 用户服务测试套件
type UserServiceTestSuite struct {
	suite.Suite
	userService   api.UserService
	userRepo      *mocks.MockUsersRepository
	wechatService *mocks.MockWechatService
	jwtManager    *mocks.MockJWTManager
	config        *config.Config
}

// SetupTest 设置测试
func (suite *UserServiceTestSuite) SetupTest() {
	// 使用统一的测试环境初始化
	testutils.InitTestEnvironment()

	suite.userRepo = mocks.NewMockUsersRepository()
	suite.wechatService = mocks.NewMockWechatService()
	suite.jwtManager = mocks.NewMockJWTManager()
	suite.config = testutils.GetTestConfig()

	suite.userService = api.NewUserService(
		suite.userRepo,
		suite.wechatService,
		suite.jwtManager,
		suite.config,
	)
}

// TearDownTest 清理测试
func (suite *UserServiceTestSuite) TearDownTest() {
	suite.userRepo.Clear()
	suite.wechatService.Clear()
	suite.jwtManager.Clear()
}

// TestWechatLogin_NewUser 测试新用户微信登录
func (suite *UserServiceTestSuite) TestWechatLogin_NewUser() {
	// 准备测试数据
	code := "test_code_new_user"
	openid := "test_openid_new"
	unionid := "test_unionid_new"

	suite.wechatService.SetMockSuccess(code, openid, unionid, &wechat.UserInfo{
		OpenID:    openid,
		NickName:  "测试用户",
		Gender:    1,
		City:      "深圳",
		Province:  "广东",
		Country:   "中国",
		AvatarURL: "https://test.avatar.url/avatar.png",
		UnionID:   unionid,
	})

	req := &models.WechatLoginRequest{
		Code:          code,
		EncryptedData: "encrypted_data",
		Iv:            "iv",
	}

	// 执行测试
	result, err := suite.userService.WechatLogin(req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.True(suite.T(), result.IsNewUser)
	assert.NotEmpty(suite.T(), result.AccessToken)
	assert.NotEmpty(suite.T(), result.RefreshToken)
	assert.Equal(suite.T(), "Bearer", result.TokenType)
	assert.Equal(suite.T(), int64(3600), result.ExpiresIn)

	// 验证用户信息
	assert.NotNil(suite.T(), result.UserInfo)
	assert.Equal(suite.T(), openid, result.UserInfo.OpenID)
	assert.Equal(suite.T(), unionid, result.UserInfo.UnionID)
	assert.Equal(suite.T(), "测试用户", result.UserInfo.Nickname)
	assert.Equal(suite.T(), int8(1), result.UserInfo.Gender)
	assert.Equal(suite.T(), "深圳", result.UserInfo.City)
	assert.Equal(suite.T(), "广东", result.UserInfo.Province)
}

// TestWechatLogin_ExistingUser 测试已存在用户微信登录
func (suite *UserServiceTestSuite) TestWechatLogin_ExistingUser() {
	// 准备已存在的用户
	openid := "existing_openid"
	existingUser := &models.Users{
		OpenID:   openid,
		Nickname: "已存在用户",
		Status:   1,
		UserType: 1,
	}
	suite.userRepo.AddTestUser(existingUser)

	// 准备测试数据
	code := "test_code_existing_user"
	suite.wechatService.SetMockSuccess(code, openid, "unionid", nil)

	req := &models.WechatLoginRequest{
		Code: code,
	}

	// 执行测试
	result, err := suite.userService.WechatLogin(req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.False(suite.T(), result.IsNewUser)
	assert.NotEmpty(suite.T(), result.AccessToken)
	assert.NotZero(suite.T(), result.UserInfo.ID)
	assert.Equal(suite.T(), "已存在用户", result.UserInfo.Nickname)
}

// TestWechatLogin_WechatError 测试微信接口错误
func (suite *UserServiceTestSuite) TestWechatLogin_WechatError() {
	code := "error_code"
	suite.wechatService.SetMockError(code, "微信接口错误")

	req := &models.WechatLoginRequest{
		Code: code,
	}

	// 执行测试
	result, err := suite.userService.WechatLogin(req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), ecode.ErrWeChatAuthFailed, err)
}

// TestWechatLogin_JWTError 测试JWT生成错误
func (suite *UserServiceTestSuite) TestWechatLogin_JWTError() {
	// 设置JWT错误
	suite.jwtManager.SetError(true, "JWT生成失败")

	code := "test_code"
	openid := "test_openid"
	suite.wechatService.SetMockSuccess(code, openid, "unionid", nil)

	req := &models.WechatLoginRequest{
		Code: code,
	}

	// 执行测试
	result, err := suite.userService.WechatLogin(req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Equal(suite.T(), ecode.ErrTokenGenerationFailed, err)
}

// TestGetUserByID_Success 测试根据ID获取用户成功
func (suite *UserServiceTestSuite) TestGetUserByID_Success() {
	// 准备测试用户
	user := &models.Users{
		OpenID:   "test_openid",
		Nickname: "测试用户",
		Status:   1,
		UserType: 1,
	}
	suite.userRepo.AddTestUser(user)

	// 执行测试
	result, err := suite.userService.GetUserByID(user.ID)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), user.ID, result.ID)
	assert.Equal(suite.T(), "测试用户", result.Nickname)
}

// TestGetUserByID_NotFound 测试用户不存在
func (suite *UserServiceTestSuite) TestGetUserByID_NotFound() {
	// 执行测试
	result, err := suite.userService.GetUserByID(999)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
}

// TestUpdateUser_Success 测试更新用户成功
func (suite *UserServiceTestSuite) TestUpdateUser_Success() {
	// 准备测试用户
	user := &models.Users{
		OpenID:   "test_openid",
		Nickname: "原昵称",
		Status:   1,
		UserType: 1,
	}
	suite.userRepo.AddTestUser(user)

	// 准备更新数据
	nickname := "新昵称"
	avatar := "新头像URL"
	phone := "13800138000"
	gender := int8(2)
	province := "北京"
	city := "北京"

	updateReq := &models.UsersUpdateRequest{
		Nickname: &nickname,
		Avatar:   &avatar,
		Phone:    &phone,
		Gender:   &gender,
		Province: &province,
		City:     &city,
	}

	// 执行测试
	result, err := suite.userService.UpdateUser(user.ID, updateReq)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "新昵称", result.Nickname)
	assert.Equal(suite.T(), "新头像URL", result.Avatar)
	assert.Equal(suite.T(), "13800138000", result.Phone)
	assert.Equal(suite.T(), int8(2), result.Gender)
	assert.Equal(suite.T(), "北京", result.Province)
	assert.Equal(suite.T(), "北京", result.City)
}

// TestSuite 运行测试套件
func TestUserServiceSuite(t *testing.T) {
	suite.Run(t, new(UserServiceTestSuite))
}
