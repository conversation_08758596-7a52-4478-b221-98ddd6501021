package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"kids-platform/pkg/errcode"
	"kids-platform/pkg/jwt"
	"kids-platform/pkg/middleware"
	"kids-platform/tests/mocks"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestJWTAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		token          string
		mockError      bool
		mockErrorMsg   string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "有效token",
			token:          "Bearer valid_token",
			mockError:      false,
			expectedStatus: http.StatusOK,
			expectedCode:   0,
		},
		{
			name:           "缺少token",
			token:          "",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errcode.ErrUnauthorized.Code(),
		},
		{
			name:           "无效token格式",
			token:          "Bearer ",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errcode.ErrUnauthorized.Code(),
		},
		{
			name:           "token过期",
			token:          "Bearer expired_token",
			mockError:      true,
			mockErrorMsg:   "token expired",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errcode.ErrTokenExpired.Code(),
		},
		{
			name:           "无效token",
			token:          "Bearer invalid_token",
			mockError:      true,
			mockErrorMsg:   "invalid token",
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errcode.ErrInvalidToken.Code(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock JWT管理器
			mockJWT := mocks.NewMockJWTManager()
			if tt.mockError {
				mockJWT.SetError(true, tt.mockErrorMsg)
			} else {
				// 添加有效token
				claims := &jwt.Claims{
					UserID:   1,
					Username: "testuser",
					UserType: 1,
				}
				mockJWT.AddValidToken("valid_token", claims)
			}

			// 创建路由
			router := gin.New()
			router.Use(middleware.JWTAuth(mockJWT))
			router.GET("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"code": 0, "message": "success"})
			})

			// 创建请求
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.token != "" {
				req.Header.Set("Authorization", tt.token)
			}

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus != http.StatusOK {
				// 验证错误响应
				// 这里可以添加更详细的响应体验证
				assert.Contains(t, w.Body.String(), "code")
			}
		})
	}
}

func TestOptionalAuth(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name    string
		token   string
		hasUser bool
	}{
		{
			name:    "无token",
			token:   "",
			hasUser: false,
		},
		{
			name:    "有效token",
			token:   "Bearer valid_token",
			hasUser: true,
		},
		{
			name:    "无效token",
			token:   "Bearer invalid_token",
			hasUser: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock JWT管理器
			mockJWT := mocks.NewMockJWTManager()
			claims := &jwt.Claims{
				UserID:   1,
				Username: "testuser",
				UserType: 1,
			}
			mockJWT.AddValidToken("valid_token", claims)

			// 创建路由
			router := gin.New()
			router.Use(middleware.OptionalAuth(mockJWT))
			router.GET("/test", func(c *gin.Context) {
				userID, exists := middleware.GetCurrentUserID(c)
				c.JSON(http.StatusOK, gin.H{
					"has_user": exists,
					"user_id":  userID,
				})
			})

			// 创建请求
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.token != "" {
				req.Header.Set("Authorization", tt.token)
			}

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, http.StatusOK, w.Code)

			// 验证用户信息是否正确设置
			if tt.hasUser {
				assert.Contains(t, w.Body.String(), `"has_user":true`)
			} else {
				assert.Contains(t, w.Body.String(), `"has_user":false`)
			}
		})
	}
}
