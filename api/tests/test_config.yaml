# 测试环境配置文件
server:
  port: 8081
  mode: "test"
  read_timeout: 30
  write_timeout: 30

database:
  driver: "sqlite"
  dsn: ":memory:"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime: 3600

mongodb:
  enabled: false

redis:
  enabled: false

jwt:
  secret: "test_jwt_secret_key_for_testing_only"
  expire_hours: 24
  refresh_expire_hours: 168

log:
  level: "debug"
  format: "json"
  output: "stdout"
  file_path: ""
  max_size: 100
  max_backups: 3
  max_age: 7
  compress: true

upload:
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
  max_size: 10485760
  upload_path: "test_uploads/"

# 微信小程序配置（测试环境）
wechat:
  miniprogram:
    app_id: "test_app_id"
    app_secret: "test_app_secret"
    # 测试环境启用Mock
    mock_enabled: true
    mock_openid: "test_mock_openid"
    mock_session_key: "test_mock_session_key"
    mock_unionid: "test_mock_unionid"
    # API配置
    auth_url: "https://api.weixin.qq.com/sns/jscode2session"
    timeout: 5000

# 安全配置
security:
  cors_allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
  rate_limit_requests: 1000
  rate_limit_window: 60
  production_mode: false
  debug_mode: true
  secure_cookies: false
