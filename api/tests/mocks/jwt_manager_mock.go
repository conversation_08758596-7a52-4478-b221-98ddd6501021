package mocks

import (
	"fmt"
	"kids-platform/pkg/jwt"
	"time"
)

// MockJWTManager JWT管理器Mock
type MockJWTManager struct {
	shouldError  bool
	errorMessage string
	tokenPairs   map[uint64]*jwt.TokenPair
	validTokens  map[string]*jwt.Claims
}

// NewMockJWTManager 创建Mock JWT管理器
func NewMockJWTManager() *MockJWTManager {
	return &MockJWTManager{
		tokenPairs:  make(map[uint64]*jwt.TokenPair),
		validTokens: make(map[string]*jwt.Claims),
	}
}

// GenerateTokenPair 生成Token对
func (m *MockJWTManager) GenerateTokenPair(userID uint64, username string, userType uint8) (*jwt.TokenPair, error) {
	if m.shouldError {
		return nil, fmt.Errorf(m.errorMessage)
	}

	accessToken := fmt.Sprintf("mock_access_token_%d_%d", userID, time.Now().Unix())
	refreshToken := fmt.Sprintf("mock_refresh_token_%d_%d", userID, time.Now().Unix())

	tokenPair := &jwt.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    3600, // 1小时
		TokenType:    "Bearer",
	}

	// 存储token对应的claims
	claims := &jwt.Claims{
		UserID:   userID,
		Username: username,
		UserType: userType,
	}

	m.tokenPairs[userID] = tokenPair
	m.validTokens[accessToken] = claims
	m.validTokens[refreshToken] = claims

	return tokenPair, nil
}

// ValidateToken 验证Token
func (m *MockJWTManager) ValidateToken(token string) (*jwt.Claims, error) {
	if m.shouldError {
		return nil, fmt.Errorf(m.errorMessage)
	}

	claims, exists := m.validTokens[token]
	if !exists {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}

// RefreshToken 刷新Token
func (m *MockJWTManager) RefreshToken(refreshToken string) (string, string, error) {
	if m.shouldError {
		return "", "", fmt.Errorf(m.errorMessage)
	}

	claims, exists := m.validTokens[refreshToken]
	if !exists {
		return "", "", fmt.Errorf("invalid refresh token")
	}

	// 生成新的token对
	tokenPair, err := m.GenerateTokenPair(claims.UserID, claims.Username, claims.UserType)
	if err != nil {
		return "", "", err
	}

	return tokenPair.AccessToken, tokenPair.RefreshToken, nil
}

// SetError 设置错误状态
func (m *MockJWTManager) SetError(shouldError bool, errorMessage string) {
	m.shouldError = shouldError
	m.errorMessage = errorMessage
}

// AddValidToken 添加有效token（测试辅助方法）
func (m *MockJWTManager) AddValidToken(token string, claims *jwt.Claims) {
	m.validTokens[token] = claims
}

// RemoveToken 移除token（测试辅助方法）
func (m *MockJWTManager) RemoveToken(token string) {
	delete(m.validTokens, token)
}

// Clear 清空所有数据（测试辅助方法）
func (m *MockJWTManager) Clear() {
	m.tokenPairs = make(map[uint64]*jwt.TokenPair)
	m.validTokens = make(map[string]*jwt.Claims)
	m.shouldError = false
	m.errorMessage = ""
}
