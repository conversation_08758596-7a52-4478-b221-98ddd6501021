package mocks

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"
)

// MockUsersRepository 用户仓储Mock
type MockUsersRepository struct {
	users     map[uint]*models.Users
	openidMap map[string]*models.Users
	nextID    uint
}

// NewMockUsersRepository 创建Mock用户仓储
func NewMockUsersRepository() *MockUsersRepository {
	return &MockUsersRepository{
		users:     make(map[uint]*models.Users),
		openidMap: make(map[string]*models.Users),
		nextID:    1,
	}
}

// Create 创建用户
func (m *MockUsersRepository) Create(user *models.Users) error {
	if user.OpenID != "" {
		if _, exists := m.openidMap[user.OpenID]; exists {
			return errcode.ErrDataExists.WithDetails("用户已存在")
		}
	}

	user.ID = m.nextID
	m.nextID++

	m.users[user.ID] = user
	if user.OpenID != "" {
		m.openidMap[user.OpenID] = user
	}

	return nil
}

// GetByID 根据ID获取用户
func (m *MockUsersRepository) GetByID(id uint) (*models.Users, error) {
	user, exists := m.users[id]
	if !exists {
		return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 返回副本避免修改原数据
	userCopy := *user
	return &userCopy, nil
}

// GetByOpenID 根据OpenID获取用户
func (m *MockUsersRepository) GetByOpenID(openid string) (*models.Users, error) {
	user, exists := m.openidMap[openid]
	if !exists {
		return nil, errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 返回副本避免修改原数据
	userCopy := *user
	return &userCopy, nil
}

// Update 更新用户
func (m *MockUsersRepository) Update(id uint, updates *models.Users) error {
	user, exists := m.users[id]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	// 更新字段
	if updates.Nickname != "" {
		user.Nickname = updates.Nickname
	}
	if updates.Avatar != "" {
		user.Avatar = updates.Avatar
	}
	if updates.Phone != "" {
		user.Phone = updates.Phone
	}
	if updates.Gender != 0 {
		user.Gender = updates.Gender
	}
	if updates.Province != "" {
		user.Province = updates.Province
	}
	if updates.City != "" {
		user.City = updates.City
	}
	if updates.UnionID != "" {
		user.UnionID = updates.UnionID
	}
	if !updates.LastLoginAt.IsZero() {
		user.LastLoginAt = updates.LastLoginAt
	}

	return nil
}

// Delete 删除用户
func (m *MockUsersRepository) Delete(id uint) error {
	user, exists := m.users[id]
	if !exists {
		return errcode.ErrDataNotFound.WithDetails("用户不存在")
	}

	delete(m.users, id)
	if user.OpenID != "" {
		delete(m.openidMap, user.OpenID)
	}

	return nil
}

// List 获取用户列表
func (m *MockUsersRepository) List(offset, limit int) ([]*models.Users, int64, error) {
	var users []*models.Users
	for _, user := range m.users {
		userCopy := *user
		users = append(users, &userCopy)
	}

	total := int64(len(users))

	// 简单的分页处理
	start := offset
	end := offset + limit
	if start > len(users) {
		return []*models.Users{}, total, nil
	}
	if end > len(users) {
		end = len(users)
	}

	return users[start:end], total, nil
}

// AddTestUser 添加测试用户（测试辅助方法）
func (m *MockUsersRepository) AddTestUser(user *models.Users) {
	if user.ID == 0 {
		user.ID = m.nextID
		m.nextID++
	}

	m.users[user.ID] = user
	if user.OpenID != "" {
		m.openidMap[user.OpenID] = user
	}
}

// Clear 清空所有数据（测试辅助方法）
func (m *MockUsersRepository) Clear() {
	m.users = make(map[uint]*models.Users)
	m.openidMap = make(map[string]*models.Users)
	m.nextID = 1
}
