package mocks

import (
	"fmt"
	"kids-platform/pkg/wechat"
)

// WechatServiceInterface 微信服务接口
type WechatServiceInterface interface {
	Code2Session(code string) (*wechat.Code2SessionResponse, error)
	DecryptUserInfo(encryptedData, iv, sessionKey string) (*wechat.UserInfo, error)
}

// MockWechatService 微信服务Mock
type MockWechatService struct {
	mockEnabled bool
	mockData    map[string]*MockWechatData
}

// MockWechatData Mock微信数据
type MockWechatData struct {
	Code2SessionResp *wechat.Code2SessionResponse
	UserInfo         *wechat.UserInfo
	ShouldError      bool
	ErrorMessage     string
}

// NewMockWechatService 创建Mock微信服务
func NewMockWechatService() *MockWechatService {
	return &MockWechatService{
		mockEnabled: true,
		mockData:    make(map[string]*MockWechatData),
	}
}

// Code2Session Mock微信code2session接口
func (m *MockWechatService) Code2Session(code string) (*wechat.Code2SessionResponse, error) {
	if !m.mockEnabled {
		return nil, fmt.Errorf("mock not enabled")
	}

	data, exists := m.mockData[code]
	if !exists {
		// 默认成功响应
		return &wechat.Code2SessionResponse{
			OpenID:     "mock_openid_" + code,
			SessionKey: "mock_session_key",
			UnionID:    "mock_unionid_" + code,
			ErrCode:    0,
			ErrMsg:     "",
		}, nil
	}

	if data.ShouldError {
		return nil, fmt.Errorf(data.ErrorMessage)
	}

	return data.Code2SessionResp, nil
}

// DecryptUserInfo Mock用户信息解密
func (m *MockWechatService) DecryptUserInfo(encryptedData, iv, sessionKey string) (*wechat.UserInfo, error) {
	if !m.mockEnabled {
		return nil, fmt.Errorf("mock not enabled")
	}

	// 根据sessionKey查找对应的用户信息
	for _, data := range m.mockData {
		if data.Code2SessionResp != nil && data.Code2SessionResp.SessionKey == sessionKey {
			if data.ShouldError {
				return nil, fmt.Errorf(data.ErrorMessage)
			}

			if data.UserInfo != nil {
				return data.UserInfo, nil
			}
		}
	}

	// 默认用户信息
	return &wechat.UserInfo{
		OpenID:    "mock_openid",
		NickName:  "Mock用户",
		Gender:    1,
		City:      "深圳",
		Province:  "广东",
		Country:   "中国",
		AvatarURL: "https://mock.avatar.url/avatar.png",
		UnionID:   "mock_unionid",
	}, nil
}

// SetMockData 设置Mock数据
func (m *MockWechatService) SetMockData(code string, data *MockWechatData) {
	m.mockData[code] = data
}

// SetMockSuccess 设置成功的Mock数据
func (m *MockWechatService) SetMockSuccess(code, openid, unionid string, userInfo *wechat.UserInfo) {
	m.mockData[code] = &MockWechatData{
		Code2SessionResp: &wechat.Code2SessionResponse{
			OpenID:     openid,
			SessionKey: "mock_session_key_" + code,
			UnionID:    unionid,
			ErrCode:    0,
			ErrMsg:     "",
		},
		UserInfo:    userInfo,
		ShouldError: false,
	}
}

// SetMockError 设置错误的Mock数据
func (m *MockWechatService) SetMockError(code, errorMsg string) {
	m.mockData[code] = &MockWechatData{
		ShouldError:  true,
		ErrorMessage: errorMsg,
	}
}

// Clear 清空Mock数据
func (m *MockWechatService) Clear() {
	m.mockData = make(map[string]*MockWechatData)
}

// SetEnabled 设置是否启用Mock
func (m *MockWechatService) SetEnabled(enabled bool) {
	m.mockEnabled = enabled
}
