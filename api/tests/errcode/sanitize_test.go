package errcode

import (
	"testing"

	"kids-platform/pkg/config"
	"kids-platform/pkg/errcode"

	"github.com/stretchr/testify/assert"
)

func TestSanitizeError_JWTErrors(t *testing.T) {
	// 模拟生产环境配置
	prodConfig := &config.Config{
		Security: config.SecurityConfig{
			ProductionMode: true,
			DebugMode:      false,
		},
	}

	// 模拟开发环境配置
	devConfig := &config.Config{
		Security: config.SecurityConfig{
			ProductionMode: false,
			DebugMode:      true,
		},
	}

	tests := []struct {
		name           string
		originalError  *errcode.Error
		config         *config.Config
		expectSanitized bool
		expectedCode   int
	}{
		{
			name:           "生产环境-ErrInvalidToken应该保持原样",
			originalError:  errcode.ErrInvalidToken.WithDetails("token验证失败"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrInvalidToken.Code(),
		},
		{
			name:           "生产环境-ErrTokenExpired应该保持原样",
			originalError:  errcode.ErrTokenExpired.WithDetails("token已过期"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrTokenExpired.Code(),
		},
		{
			name:           "生产环境-ErrToken应该保持原样",
			originalError:  errcode.ErrToken.WithDetails("token错误"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrToken.Code(),
		},
		{
			name:           "生产环境-ErrTokenTimeout应该保持原样",
			originalError:  errcode.ErrTokenTimeout.WithDetails("token超时"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrTokenTimeout.Code(),
		},
		{
			name:           "生产环境-ErrTokenGeneration应该保持原样",
			originalError:  errcode.ErrTokenGeneration.WithDetails("token生成失败"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrTokenGeneration.Code(),
		},
		{
			name:           "生产环境-ErrRefreshToken应该保持原样",
			originalError:  errcode.ErrRefreshToken.WithDetails("刷新token失败"),
			config:         prodConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrRefreshToken.Code(),
		},
		{
			name:           "生产环境-其他系统错误应该被脱敏",
			originalError:  errcode.ErrDatabase.WithDetails("数据库连接失败"),
			config:         prodConfig,
			expectSanitized: true,
			expectedCode:   errcode.ErrInternalServer.Code(),
		},
		{
			name:           "开发环境-所有错误都应该保持原样",
			originalError:  errcode.ErrDatabase.WithDetails("数据库连接失败"),
			config:         devConfig,
			expectSanitized: false,
			expectedCode:   errcode.ErrDatabase.Code(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sanitizedErr := errcode.SanitizeError(tt.originalError, tt.config)
			
			// 验证错误类型
			errCodeErr, ok := sanitizedErr.(*errcode.Error)
			assert.True(t, ok, "脱敏后的错误应该是*errcode.Error类型")
			
			// 验证错误码
			assert.Equal(t, tt.expectedCode, errCodeErr.Code(), "错误码应该匹配预期")
			
			if tt.expectSanitized {
				// 如果期望被脱敏，验证错误码已改变
				assert.NotEqual(t, tt.originalError.Code(), errCodeErr.Code(), "错误应该被脱敏")
			} else {
				// 如果期望保持原样，验证错误码未改变
				assert.Equal(t, tt.originalError.Code(), errCodeErr.Code(), "错误应该保持原样")
			}
		})
	}
}

func TestHTTPStatusCodeMapping_JWTErrors(t *testing.T) {
	tests := []struct {
		name         string
		errorCode    int
		expectedHTTP int
	}{
		{
			name:         "ErrInvalidToken应该映射到401",
			errorCode:    errcode.ErrInvalidToken.Code(),
			expectedHTTP: 401,
		},
		{
			name:         "ErrTokenExpired应该映射到401",
			errorCode:    errcode.ErrTokenExpired.Code(),
			expectedHTTP: 401,
		},
		{
			name:         "ErrToken应该映射到401",
			errorCode:    errcode.ErrToken.Code(),
			expectedHTTP: 401,
		},
		{
			name:         "ErrTokenTimeout应该映射到401",
			errorCode:    errcode.ErrTokenTimeout.Code(),
			expectedHTTP: 401,
		},
		{
			name:         "ErrInternalServer应该映射到500",
			errorCode:    errcode.ErrInternalServer.Code(),
			expectedHTTP: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			httpStatus := errcode.ToHTTPStatusCode(tt.errorCode)
			assert.Equal(t, tt.expectedHTTP, httpStatus, "HTTP状态码映射应该正确")
		})
	}
}

func TestEndToEnd_JWTErrorHandling(t *testing.T) {
	// 模拟完整的错误处理流程
	prodConfig := &config.Config{
		Security: config.SecurityConfig{
			ProductionMode: true,
			DebugMode:      false,
		},
	}

	// 模拟JWT认证失败的场景
	originalError := errcode.ErrInvalidToken.WithDetails("token验证失败", "token has invalid claims: token is expired")

	// 1. 脱敏处理
	sanitizedErr := errcode.SanitizeError(originalError, prodConfig)

	// 2. 验证脱敏后仍然是正确的错误类型
	errCodeErr, ok := sanitizedErr.(*errcode.Error)
	assert.True(t, ok, "脱敏后应该仍然是*errcode.Error类型")
	assert.Equal(t, errcode.ErrInvalidToken.Code(), errCodeErr.Code(), "错误码应该保持为10602")

	// 3. 验证HTTP状态码映射
	httpStatus := errcode.ToHTTPStatusCode(errCodeErr.Code())
	assert.Equal(t, 401, httpStatus, "应该返回401状态码而不是500")

	t.Logf("✅ 端到端测试通过: 错误码%d -> HTTP状态码%d", errCodeErr.Code(), httpStatus)
}
