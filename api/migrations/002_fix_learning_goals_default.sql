-- 修复 children 表 learning_goals 字段的默认值问题
-- 执行时间：2025-07-31
-- 目的：解决 JSON 字段不能为空字符串的问题

-- 1. 更新现有的空字符串为空的 JSON 数组
UPDATE children 
SET learning_goals = '[]' 
WHERE learning_goals = '' OR learning_goals IS NULL;

-- 2. 修改字段定义，设置默认值为空的 JSON 数组
ALTER TABLE children 
MODIFY COLUMN learning_goals JSON DEFAULT ('[]') 
COMMENT '学习目标JSON数组，如["提高耐力","学会花式跳法"]，用于个性化推荐';

-- 3. 验证修改结果
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'children' 
AND COLUMN_NAME = 'learning_goals';
