-- 添加 updated_at 字段到 growth_tracks 表
-- 修复问题：Error 1054 (42S22): Unknown column 'updated_at' in 'field list'

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'growth_tracks' 
         AND COLUMN_NAME = 'updated_at') = 0,
        'ALTER TABLE growth_tracks ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间'' AFTER created_at;',
        'SELECT ''Column updated_at already exists in growth_tracks table'';'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
