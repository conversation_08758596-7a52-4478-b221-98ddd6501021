# 📋 项目标准文档

本目录包含项目的开发标准、协作规范和最佳实践，为团队提供统一的工作指南。

## 📁 文档结构

```
standards/
├── README.md              # 📖 本文档（标准概览）
├── collaboration/         # 🤝 协作规范
│   ├── collaboration-guidelines.md    # 项目协作指南
│   ├── communication-principles.md    # 沟通原则
│   ├── git-workflow.md               # Git工作流规范
│   └── workflow-standards.md         # 工作流程标准
├── development/          # 🛠️ 开发标准
│   ├── development-standards-framework.md  # 开发标准建立框架
│   ├── requirements-validation.md         # 需求理解和验证机制
│   ├── standards-enforcement.md           # 标准执行机制
│   ├── standards-optimization.md          # 标准优化方法
│   └── task-module-completion-report.md   # 任务模块开发完成报告
└── templates/            # 📝 模板库（待建设）
```

## 🎯 标准体系概览

### 🤝 协作规范 (collaboration/)

#### 核心原则
- **需求确认优先**: 准确理解需求比快速给出方案更重要
- **升级版协作工作流**: 理解确认 → 需求分析 → 方案设计 → 实施执行
- **持续改进**: 根据协作经验不断优化流程

#### 主要文档
- **[协作指南](collaboration/collaboration-guidelines.md)** - AI与用户协作的核心原则和流程
- **[沟通原则](collaboration/communication-principles.md)** - 有效沟通的方法和技巧
- **[Git工作流](collaboration/git-workflow.md)** - 代码版本管理规范
- **[工作流程标准](collaboration/workflow-standards.md)** - 项目开发的标准流程

### 🛠️ 开发标准 (development/)

#### 核心理念
- **模板化原则**: 每种类型的文件都有标准模板
- **强制执行原则**: 通过工具自动检查和提醒
- **持续优化原则**: 根据问题和经验不断更新标准

#### 主要文档
- **[开发标准框架](development/development-standards-framework.md)** - 标准建立的基础框架
- **[需求验证机制](development/requirements-validation.md)** - 需求理解和确认的方法
- **[标准执行机制](development/standards-enforcement.md)** - 如何确保标准被正确执行
- **[标准优化方法](development/standards-optimization.md)** - 持续改进标准的方法

### 📝 模板库 (templates/)

**状态**: 🚧 建设中

**计划内容**:
- 需求分析模板
- 代码开发模板
- 测试用例模板
- 文档编写模板
- 项目报告模板

## 🚀 快速开始

### 新项目开始前
1. 阅读 [协作指南](collaboration/collaboration-guidelines.md)
2. 了解 [工作流程标准](collaboration/workflow-standards.md)
3. 熟悉 [开发标准框架](development/development-standards-framework.md)

### 开发过程中
1. 使用 [需求验证机制](development/requirements-validation.md) 确认需求
2. 遵循 [Git工作流规范](collaboration/git-workflow.md)
3. 参考相关模板进行开发

### 项目完成后
1. 更新相关标准文档
2. 记录经验和改进建议
3. 为下个项目优化标准

## 🔧 与项目工具集成

### MVS检查工具
标准文档已集成到 `tools/mvs-check.sh` 中，可以自动检查项目是否符合标准要求。

```bash
# 运行标准合规性检查
./tools/mvs-check.sh
```

### 代码生成器
代码生成工具会自动遵循标准文档中定义的模板和规范。

```bash
# 生成符合标准的代码模块
./tools/generate-module.sh module_name api
```

## 📊 标准执行状态

### ✅ 已实施标准
- [x] 协作流程规范
- [x] 需求确认机制
- [x] 代码生成模板
- [x] 文档编写规范

### 🚧 正在建设
- [ ] 测试标准模板
- [ ] 部署流程规范
- [ ] 性能优化标准
- [ ] 安全检查清单

### 📋 计划中标准
- [ ] API设计规范
- [ ] 数据库设计标准
- [ ] 前端开发规范
- [ ] 运维监控标准

## 🎯 使用建议

### 对于开发者
1. **项目开始前**: 必读协作指南和工作流程标准
2. **开发过程中**: 参考开发标准和模板
3. **代码提交前**: 运行标准检查工具
4. **项目完成后**: 更新和优化相关标准

### 对于项目管理者
1. **定期审查**: 检查标准执行情况
2. **持续优化**: 根据项目经验更新标准
3. **培训推广**: 确保团队了解和遵循标准
4. **工具支持**: 提供必要的工具和资源

### 对于AI助手
1. **严格遵循**: 按照协作指南进行交互
2. **主动检查**: 在关键节点提醒标准要求
3. **持续学习**: 根据反馈优化协作方式
4. **标准推广**: 帮助用户理解和应用标准

## 🔄 标准维护

### 更新频率
- **协作规范**: 根据协作经验持续优化
- **开发标准**: 每个项目周期结束后评估更新
- **模板库**: 根据使用情况和反馈及时更新

### 更新流程
1. 收集问题和改进建议
2. 分析和评估改进方案
3. 更新相关标准文档
4. 通知团队并培训新标准
5. 监控新标准的执行效果

---

**让标准成为效率的助推器，而不是束缚！** 🚀

## 📞 联系和反馈

如有任何关于标准文档的问题或建议，请：
1. 在项目中创建Issue
2. 直接修改相关文档并提交PR
3. 在团队会议中讨论

**持续改进，共同成长！** 💪
