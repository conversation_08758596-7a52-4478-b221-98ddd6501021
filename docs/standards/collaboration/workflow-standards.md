# 工作流程标准文档

## 📋 文档概述
**创建时间：** 2025-06-27  
**适用范围：** grew_up 项目及后续相关项目  
**更新频率：** 根据项目经验持续优化  
**关联文档：** [协作指南](./collaboration-guidelines.md) | [沟通原则](./communication-principles.md)

## 🎯 项目开发标准流程

### 1. 需求分析阶段
#### 标准流程：
```
需求收集 → 需求分析 → 需求确认 → 需求文档化 → 需求评审
```

#### 具体步骤：
1. **需求收集**
   - 用户访谈和需求调研
   - 竞品分析和市场调研
   - 技术可行性初步评估

2. **需求分析**
   - 用户画像和场景分析
   - 功能优先级排序
   - 技术架构初步设计

3. **需求确认**
   - 与用户反复确认需求理解
   - 澄清模糊和有歧义的需求
   - 确定项目范围和边界

4. **需求文档化**
   - 更新 `requirements.md`
   - 更新 `features.md`
   - 记录决策过程到日志

5. **需求评审**
   - 检查需求完整性和一致性
   - 评估技术实现难度
   - 确定开发计划和时间表

#### 交付物：
- 需求分析文档
- 功能模块清单
- 用户画像和场景描述
- 项目范围说明

### 2. 技术设计阶段
#### 标准流程：
```
架构设计 → 数据库设计 → API设计 → UI/UX设计 → 技术选型 → 设计评审
```

#### 具体步骤：
1. **架构设计**
   - 系统整体架构设计
   - 模块划分和依赖关系
   - 部署架构和环境规划

2. **数据库设计**
   - 数据模型设计
   - 表结构设计
   - 索引和性能优化

3. **API设计**
   - 接口定义和文档
   - 数据格式和协议
   - 错误处理和状态码

4. **UI/UX设计**
   - 页面结构和布局
   - 交互流程设计
   - 组件库选择

5. **技术选型**
   - 框架和库的选择
   - 开发工具配置
   - 第三方服务集成

#### 交付物：
- 技术架构文档
- 数据库设计文档
- API接口文档
- UI/UX设计说明

### 3. 开发实施阶段
#### 标准流程：
```
环境搭建 → 后端开发 → 前端开发 → 集成测试 → 功能测试 → 代码审查
```

#### 7步功能模块开发流程（优化版）：
```
1. 需求分析与技术设计 → 2. 数据层开发 → 3. 业务逻辑开发 → 4. API接口开发 → 5. 单元测试开发 → 6. 功能测试与修复 → 7. 代码审查与合并
```

**详细步骤说明：**

**第1步：需求分析与技术设计** ⭐
- 📖 **需求理解**：深入分析功能需求，明确业务逻辑和用户场景
- 🏗️ **技术设计**：设计API接口、数据模型、业务流程
- 📝 **设计确认**：与项目负责人确认设计方案，确保理解一致
- 📋 **文档同步**：如有设计变动，同步更新相关需求和技术文档
- 🌿 **创建分支**：`git checkout -b feature/模块名`

**第2步：数据层开发**
- 🗄️ **数据库设计**：检查/更新数据库表结构和字段定义
- 📊 **模型定义**：创建Go struct模型，确保字段映射正确
- 🔧 **Repository层**：实现数据访问接口，使用Repository模式
- 💾 **数据库同步**：运行migration和注释同步工具

**第3步：业务逻辑开发**
- 🧠 **Service层**：实现核心业务逻辑，遵循单一职责原则
- ✅ **数据验证**：输入验证、业务规则检查、数据完整性验证
- 🔒 **权限控制**：用户权限和数据权限验证
- 🎯 **错误处理**：统一错误码和错误处理机制

**第4步：API接口开发**
- 🌐 **Handler层**：实现HTTP处理器，处理请求和响应
- 📋 **路由注册**：注册API路由，遵循RESTful设计原则
- 📤 **响应格式**：统一API响应格式，包含状态码和错误信息
- 🔐 **中间件集成**：集成认证、日志、CORS等中间件

**第5步：单元测试开发** ⭐
- 🧪 **Repository测试**：数据层单元测试，使用Mock数据库
- 🔬 **Service测试**：业务逻辑单元测试，覆盖各种业务场景
- 🌐 **Handler测试**：API接口集成测试，测试完整请求响应流程
- 📊 **覆盖率检查**：确保测试覆盖率 > 80%，关键业务逻辑100%覆盖

**第6步：功能测试与修复** ⭐
- ▶️ **运行所有测试**：确保单元测试和集成测试全部通过
- 🔍 **手动功能测试**：实际API调用测试，验证功能完整性
- 🐛 **Bug修复**：发现问题立即修复，无法修复则停止并报告
- 📝 **测试报告**：生成测试结果报告，记录测试覆盖情况

**第7步：代码审查与合并**
- 📖 **代码自审**：检查代码质量、注释完整性、命名规范
- 📋 **文档更新**：更新API文档、变更日志和相关技术文档
- 🔄 **合并到develop**：`git checkout develop && git merge feature/模块名`
- 🏷️ **打标签**：标记功能完成节点，便于版本管理

#### 文档同步策略 ⭐

**同步原则**：当需求细节和技术沟通导致设计变动时，必须同步更新相关文档

**需要同步的文档类型**：
1. **需求文档层面**
   - 📋 需求规格说明 - 更新功能需求描述
   - 🎯 用户故事 - 调整用户场景和使用流程
   - 📊 业务流程图 - 修正业务逻辑流程

2. **技术设计层面**
   - 🏗️ API接口文档 - 更新接口定义、参数、响应格式
   - 🗄️ 数据库设计 - 同步表结构、字段定义、关系图
   - 📐 架构设计图 - 调整系统架构和模块关系

3. **开发规范层面**
   - 🔧 开发流程文档 - 更新工作流程和开发标准
   - 📏 编码规范 - 调整代码风格和命名约定
   - 🧪 测试规范 - 更新测试策略和覆盖率要求

**执行时机**：
- ✅ 第1步设计确认时：标记需要更新的文档清单
- ✅ 第7步合并前：检查所有相关文档是否已同步
- ✅ 完成合并后：确认文档版本一致性

#### 开发规范：
1. **代码管理**
   - 使用Git进行版本控制
   - 每个功能创建独立分支
   - 提交信息规范化

2. **开发标准**
   - 遵循代码规范和最佳实践
   - **编写完整的注释和文档（必须）**
   - 进行单元测试

3. **包管理规范**
   - 使用适当的包管理器
   - 不手动编辑配置文件
   - 记录依赖变更原因

#### 质量控制：
- 代码提交前自测
- **注释完整性检查**
- 定期进行代码审查
- 持续集成和自动化测试

### 4. 测试验证阶段
#### 测试类型：
1. **功能测试**
   - 核心功能验证
   - 边界条件测试
   - 异常情况处理

2. **性能测试**
   - 响应时间测试
   - 并发用户测试
   - 资源使用监控

3. **用户体验测试**
   - 界面易用性测试
   - 流程完整性验证
   - 用户反馈收集

#### 测试流程：
```
测试计划 → 测试用例设计 → 测试执行 → 缺陷跟踪 → 回归测试
```

### 5. 部署上线阶段
#### 部署流程：
```
环境准备 → 代码部署 → 配置更新 → 服务启动 → 功能验证 → 监控配置
```

#### 部署检查清单：
- [ ] 服务器环境配置
- [ ] 数据库迁移脚本
- [ ] 配置文件更新
- [ ] SSL证书配置
- [ ] 域名解析设置
- [ ] 监控和日志配置

## 📝 文档管理标准

### 1. 文档分类
#### 项目文档：
- `requirements.md` - 需求分析
- `features.md` - 功能清单
- `detailed-design.md` - 功能详细设计
- `architecture.md` - 技术架构
- `api.md` - 接口文档
- `deployment.md` - 部署指南

#### 协作文档：
- `collaboration-guidelines.md` - 协作指南
- `communication-principles.md` - 沟通原则
- `workflow-standards.md` - 工作流程标准

#### 日志文档：
- `chat_logs/` - 沟通记录
- `decision-log.md` - 决策记录
- `change-log.md` - 变更记录

### 2. 文档更新规范
#### 更新频率：
- **实时更新**：重要决策和需求变更
- **每日更新**：开发进展和问题记录
- **每周整理**：文档结构优化和内容补充

#### 更新流程：
1. 识别需要更新的文档
2. 进行内容更新和修改
3. 检查文档一致性
4. 提交变更并记录原因

### 3. 版本控制
- 所有文档纳入Git版本控制
- 重要变更创建标签
- 保留历史版本便于回溯

## 🔄 任务管理标准

### 1. 任务分解原则
#### 任务粒度：
- 每个任务代表约20分钟的工作量
- 避免过于细碎或过于宏大的任务
- 确保任务有明确的完成标准

#### 任务依赖：
- 明确任务间的依赖关系
- 识别关键路径和瓶颈
- 合理安排任务执行顺序

### 2. 进度跟踪
#### 状态定义：
- `NOT_STARTED` - 未开始
- `IN_PROGRESS` - 进行中
- `CANCELLED` - 已取消
- `COMPLETE` - 已完成

#### 更新频率：
- 任务状态变化时立即更新
- 每日检查和同步进展
- 每周进行进度回顾

### 3. 问题管理
#### 问题分类：
- **阻塞问题**：影响项目进展的关键问题
- **技术问题**：需要技术解决的问题
- **需求问题**：需求不清楚或有变更

#### 处理流程：
```
问题识别 → 问题分析 → 解决方案 → 实施验证 → 结果记录
```

## 🛠️ 技术开发规范

### 1. 代码规范
#### 命名规范：
- 使用有意义的变量和函数名
- 遵循语言特定的命名约定
- 保持命名的一致性

#### 代码结构：
- 合理的文件和目录组织
- 清晰的模块划分
- **完整的代码注释（强制要求）**

### 2. 注释规范 ⭐
#### 数据库层面：
- **表级注释**：每个表必须有注释说明用途和业务含义
- **字段级注释**：每个字段必须有注释说明含义、用途、数据类型
- **枚举值注释**：枚举类型必须注释每个选项的具体含义
- **索引注释**：重要索引必须注释用途和性能考虑
- **外键注释**：外键关系必须注释关联逻辑

#### 代码层面：
- **结构体/模型注释**：每个数据结构必须有注释说明用途
- **公共方法注释**：必须包含功能说明、参数说明、返回值说明
- **复杂业务逻辑注释**：关键算法和业务规则必须有行内注释
- **API接口注释**：必须有完整的Swagger/OpenAPI注释
- **配置项注释**：所有配置参数必须有用途说明

#### 文档层面：
- **README文件**：项目根目录和重要子目录必须有README
- **变更日志**：重要修改必须记录到CHANGELOG
- **部署文档**：部署步骤和配置必须有详细说明

#### 注释质量标准：
- **清晰性**：注释内容清晰易懂，避免技术黑话
- **准确性**：注释内容与代码实际功能保持一致
- **完整性**：覆盖所有重要的代码和配置
- **及时性**：代码修改时同步更新相关注释

### 3. 数据库结构管理规范 ⭐
#### 单一权威数据源原则：
- **`database/init.sql`** 是唯一权威的完整数据库结构文件
- 任何数据库结构变更都必须同步更新此文件
- 禁止在其他文件中重复定义表结构

#### 数据库文件组织：
```
database/
├── init.sql                    # 权威的完整数据库结构（必须）
├── seed.sql                    # 初始化数据
├── migrations/                 # 数据库迁移脚本（可选）
└── docs/                       # 数据库文档
    ├── schema-overview.md      # 数据库结构概览
    └── schema-changelog.md     # 结构变更记录
```

#### 结构变更流程：
1. **变更前**：在 `schema-changelog.md` 记录计划变更
2. **变更中**：同步更新 `init.sql` 和相关代码
3. **变更后**：更新变更日志和结构概览文档
4. **注释同步**：运行 `./scripts/sync-db-comments.sh` 同步数据库注释
5. **验证**：确保 `init.sql` 可以正常创建完整数据库

#### 避免重复定义：
- 开发前必须检查 `database/docs/schema-overview.md` 了解现有表结构
- 禁止创建重复的表定义文件
- 如需增量变更，使用 migrations 但不替代 `init.sql`

### 3. Git工作流程
#### 分支策略：
```
main (生产环境)
├── develop (开发环境)
├── feature/功能名 (功能开发)
├── hotfix/修复名 (紧急修复)
└── release/版本号 (发布准备)
```

#### 提交规范：
```
类型(范围): 简短描述

详细描述（可选）

相关问题编号（可选）
```

#### 提交类型：
- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建或工具相关

### 4. 代码审查
#### 审查要点：
- 代码逻辑正确性
- 性能和安全考虑
- 代码规范遵循
- **注释完整性和质量**
- 测试覆盖率

#### 审查流程：
1. 开发者自检
2. **注释完整性自检**
3. 提交Pull Request
4. 代码审查和讨论
5. 修改和完善
6. 合并到主分支

## 📊 质量保证标准

### 1. 测试标准
#### 测试覆盖率：
- 核心功能100%覆盖
- 边界条件充分测试
- 异常情况处理验证

#### 测试类型：
- 单元测试：函数和模块级别
- 集成测试：模块间交互
- 端到端测试：完整流程验证

### 2. 性能标准
#### 响应时间：
- API接口：< 500ms
- 页面加载：< 2s
- 数据库查询：< 100ms

#### 资源使用：
- 内存使用合理
- CPU占用可控
- 网络带宽优化

### 3. 安全标准
#### 数据安全：
- 敏感数据加密
- 用户隐私保护
- 数据备份和恢复

#### 接口安全：
- 身份认证和授权
- 输入验证和过滤
- 防止常见攻击

## � 自动化测试流水线（待实施优化）

### 1. 自动化测试流水线概念
自动化测试流水线是一个自动化的测试执行和质量保证系统，当代码发生变更时自动执行预定义的测试流程。

#### 核心价值：
- **质量保证**：早期发现问题，确保代码质量
- **效率提升**：自动化执行，快速反馈
- **开发体验**：增强开发信心，统一质量标准

### 2. 流水线阶段设计

#### 阶段1：代码质量检查
```yaml
触发条件: 代码提交、PR创建
检查内容:
  - 代码格式检查 (gofmt)
  - 静态分析 (golint, go vet)
  - 安全扫描 (gosec)
  - 注释完整性检查
```

#### 阶段2：单元测试
```yaml
测试层级:
  - Repository层测试
  - Service层测试
  - Handler层测试
质量要求:
  - 测试覆盖率 > 80%
  - 核心业务逻辑 100% 覆盖
```

#### 阶段3：集成测试
```yaml
测试范围:
  - API端到端测试
  - 数据库集成测试
  - 第三方服务集成测试
环境要求:
  - 独立测试数据库
  - Mock外部依赖
```

#### 阶段4：性能测试（可选）
```yaml
测试类型:
  - 负载测试
  - 压力测试
  - 内存泄漏检查
触发条件:
  - 定时执行
  - 包含 [perf-test] 标签的提交
```

#### 阶段5：构建验证
```yaml
验证内容:
  - API服务构建
  - 管理后台构建
  - 构建产物验证
```

### 3. 实施方案

#### 方案A：GitHub Actions（推荐）
- **文件位置**：`.github/workflows/test-pipeline.yml`
- **适用场景**：使用GitHub托管的项目
- **优势**：云端执行，无需本地资源

#### 方案B：本地自动化脚本
- **文件位置**：`scripts/test-pipeline.sh`
- **适用场景**：本地开发和私有部署
- **优势**：完全可控，自定义程度高

#### 方案C：Git Hooks集成
- **文件位置**：`scripts/setup-git-hooks.sh`
- **适用场景**：开发阶段质量控制
- **优势**：提交前自动检查，防止问题代码进入仓库

### 4. 测试数据标准化优化建议

#### 当前问题：
- 测试数据重复设置
- Mock期望复杂难维护
- 权限验证数据不一致

#### 优化方案：
```go
// 标准化测试数据工厂
type TestDataFactory struct {
    UserID    int
    ChildID   int
    Timestamp time.Time
}

// Mock设置器模式
type MockSetup struct {
    CheckinRepo     *mocks.MockCheckinRepository
    ChildRepo       *mocks.MockChildRepository
    CommunityRepo   *mocks.MockCommunityRepository
}

func (m *MockSetup) SetupSuccessfulCheckinFlow(checkin *models.Checkin) {
    // 一次性设置完整的成功流程Mock
}
```

### 5. 7步开发流程优化建议

#### 当前流程回顾：
```
1. 需求分析 → 2. 设计规划 → 3. Repository层 → 4. Service层 → 5. Handler层 → 6. 测试开发 → 7. 集成验证
```

#### 优化建议：
1. **提前测试设计**：在设计阶段就规划测试用例
2. **增量开发验证**：每完成一层立即进行测试
3. **依赖分析前置**：开发前分析完整依赖链
4. **质量门禁**：每层必须通过测试才能进入下一层

#### 修订后的流程：
```
1. 需求分析 + 测试用例设计
2. 架构设计 + 依赖关系分析
3. Repository层开发 + Repository测试
4. Service层开发 + Service测试
5. Handler层开发 + Handler测试
6. 集成测试 + 端到端验证
7. 性能测试 + 文档更新
```

### 6. 工具和基础设施建议

#### 测试工具改进：
- 测试数据生成器（如 `gofakeit`）
- 测试断言增强
- 测试覆盖率监控

#### 开发工具链：
- 代码生成工具（减少重复Mock代码）
- 静态分析工具
- 自动化测试运行（git hooks）

### 7. 实施计划

#### 立即可实施：
- [ ] 创建本地自动化脚本 `scripts/test-pipeline.sh`
- [ ] 设置Git Hooks `scripts/setup-git-hooks.sh`
- [ ] 建立测试数据标准化模式

#### 中期目标：
- [ ] GitHub Actions集成（如使用GitHub）
- [ ] 测试代码重构，减少重复
- [ ] 建立自动化测试流水线

#### 长期优化：
- [ ] 性能基准测试
- [ ] 监控和日志系统完善
- [ ] 测试框架升级

---

## �🔄 持续改进

### 1. 流程优化
- 定期回顾工作流程效果
- 识别瓶颈和改进点
- 调整和优化流程
- **实施自动化测试流水线优化**

### 2. 工具升级
- 评估新工具和技术
- 升级开发和协作工具
- 提高工作效率
- **集成自动化测试工具链**

### 3. 经验总结
- 记录成功的实践经验
- 总结失败的教训
- 建立最佳实践库
- **完善测试和质量保证规范**

---
*本文档将根据项目实践经验持续更新和完善*
*最后更新：2025-07-02 - 新增自动化测试流水线优化建议*
