# 项目协作指南

## 📋 文档概述
**创建时间：** 2025-06-27  
**适用范围：** grew_up 项目及后续相关项目  
**更新频率：** 根据协作经验持续优化  

## 🎯 核心协作原则

### 1. 需求确认优先原则
> **"准确理解需求比快速给出方案更重要"**

- 遇到重要的、不清楚的地方，必须反复确认
- 不要基于假设或臆测进行设计
- 概念不清楚时，立即停下来询问

### 2. 升级版协作工作流程
当用户提出问题或解决方案时，AI应该按以下流程处理：

#### 第一步：理解确认（新增）
**触发机制：智能判断为主 + 关键词兜底**
- **智能判断标准：** 涉及多个概念选择、可能影响重要决策、需求不够明确、涉及核心功能设计
- **关键词触发：** "确认"、"理解确认"、"重要问题"、"详细分析"
- **确认内容：** 理解用户的问题场景、边界、约束条件
- **等待确认：** 停止并等待用户二次确认后再继续

#### 第二步：需求分析
- 理解用户的核心需求和目标
- 询问具体的使用场景和约束条件
- 确认重要性和优先级

#### 第三步：方案分析（强制要求）
- **必须提供2-3个不同方案选择**
- 分析每个方案的优势和劣势
- 说明每个方案的适用场景
- 对比不同方案的差异

#### 第四步：推荐优化
- 给出明确的推荐方案
- 详细说明推荐理由
- 解释为什么这个方案最适合当前情况

### 3. 升级版标准回复模式

#### 复杂问题回复模式：
```markdown
## 🎯 我理解的问题
[详细描述理解的问题场景、边界、约束等]

---
## ⏸️ 请确认
我的理解是否准确？还有哪些方面需要补充或修正？
确认后我再提供方案建议。
```

#### 确认后的方案建议模式：
```markdown
## 💡 方案建议

### 方案1：[方案名称]
**优势：** ...
**劣势：** ...
**适用场景：** ...

### 方案2：[方案名称]
**优势：** ...
**劣势：** ...
**适用场景：** ...

### 方案3：[方案名称]
**优势：** ...
**劣势：** ...
**适用场景：** ...

## 🎯 我的推荐
**推荐方案X**，理由：...
```

#### 简单问题直接回复模式：
```markdown
## ✅ [直接回答内容]
```

## 📝 沟通规范

### 1. 文档记录原则
- 所有重要决策都要记录到项目文档中
- 使用结构化的Markdown格式
- 保持文档的实时更新

### 2. 概念澄清机制
- 重要概念必须明确定义
- 发现理解偏差时立即澄清
- 记录概念演进过程

### 3. 经验总结习惯
- 每次重要讨论后总结经验教训
- 记录成功的协作模式
- 持续优化工作流程

## 🛠️ 技术协作规范

### 1. 代码开发原则
- 先进行需求分析，再开始编码
- 使用适当的包管理器，不手动编辑配置文件
- 遵循项目的技术栈选择

### 2. 架构设计原则
- 考虑项目的资源约束
- 优先选择成熟稳定的技术方案
- 保持架构的可扩展性

### 3. 测试和部署
- 建议编写测试用例验证功能
- 提供详细的部署文档
- 考虑成本控制和性能优化

## 📊 项目管理规范

### 1. 任务管理
- 使用任务管理工具跟踪复杂工作
- 明确任务的优先级和依赖关系
- 定期更新任务状态

### 2. 进度同步
- 定期确认完成情况和下一步计划
- 及时沟通遇到的问题和困难
- 保持透明的项目进展记录

### 3. 质量控制
- 重视用户体验和产品质量
- 进行充分的功能测试
- 收集用户反馈并持续改进

## 🎯 成功案例

### 案例1：日志系统设计
**用户需求：** "按日期记录对话日志"
**AI处理：**
1. ✅ 确认了基本需求和详细要求
2. ✅ 提供了5个不同方案选择
3. ✅ 给出了明确推荐和实施计划

**结果：** 建立了完善的交互式日志系统

### 案例2：打卡任务概念澄清
**问题：** AI混淆了"打卡任务"和"活动"概念
**处理：** 立即停止设计，重新确认概念定义
**结果：** 避免了功能设计的重大偏差

### 案例3：家庭共享安全机制
**用户方案：** 为每个孩子添加安全key
**AI处理：**
1. ✅ 理解确认安全需求
2. ✅ 提供多种验证方案
3. ✅ 推荐家庭邀请码方案

**结果：** 设计了符合用户习惯的安全机制

## 🔄 持续改进

### 1. 定期回顾
- 每周回顾协作效果
- 识别可以改进的地方
- 更新协作指南

### 2. 经验积累
- 记录成功的协作模式
- 总结失败的教训
- 建立最佳实践库

### 3. 工具优化
- 根据需要调整工作流程
- 引入新的协作工具
- 提高工作效率

## 📞 使用方法

### 新对话开始时
用户只需说："按照协作指南工作"，AI即会按照本指南的原则进行协作。

### 遇到问题时
参考本指南的相关章节，或者直接询问具体的协作方式。

### 指南更新
根据项目进展和协作经验，持续更新和完善本指南。

---
*本指南将根据项目进展和协作经验持续更新*
*最后更新：2025-06-27*
