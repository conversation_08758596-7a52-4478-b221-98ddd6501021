# Git工作流程规范

## 🌳 分支策略

### 主要分支
- **main** - 生产环境分支，只接受经过测试的稳定代码
- **develop** - 开发主分支，日常开发在此进行
- **feature/xxx** - 功能分支，开发具体功能时创建

### 分支命名规范
```bash
# 功能开发
feature/user-auth          # 用户认证功能
feature/checkin-system     # 打卡系统
feature/community-mgmt     # 社团管理

# 修复bug
fix/login-error           # 修复登录错误
fix/api-timeout          # 修复API超时

# 重构
refactor/database-layer   # 重构数据库层
refactor/api-structure   # 重构API结构
```

## 📝 提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整(不影响功能)
- **refactor**: 重构(既不是新功能也不是修复bug)
- **test**: 测试相关
- **chore**: 构建/工具相关

### 提交示例
```bash
# 好的提交示例
git commit -m "feat(auth): 实现微信小程序用户登录

- 添加微信授权登录接口
- 实现JWT token生成和验证
- 添加用户信息存储逻辑

Closes #123"

git commit -m "fix(api): 修复打卡接口参数验证错误

修复了打卡时间参数格式验证问题，现在支持标准ISO时间格式"

git commit -m "docs: 更新API文档和开发指南"
```

## 🔄 开发工作流

### 1. 开始新功能开发
```bash
# 切换到develop分支并拉取最新代码
git checkout develop
git pull origin develop

# 创建功能分支
git checkout -b feature/user-management

# 开发功能...
# 提交代码
git add .
git commit -m "feat(user): 实现用户管理基础功能"

# 推送到远程
git push origin feature/user-management
```

### 2. 功能开发完成
```bash
# 切换到develop分支
git checkout develop
git pull origin develop

# 合并功能分支
git merge feature/user-management

# 推送到远程
git push origin develop

# 删除功能分支
git branch -d feature/user-management
git push origin --delete feature/user-management
```

### 3. 发布到生产环境
```bash
# 切换到main分支
git checkout main
git pull origin main

# 合并develop分支
git merge develop

# 创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"

# 推送到远程
git push origin main
git push origin v1.0.0
```

## 🚀 日常开发建议

### 提交频率
- **小步快跑**: 每完成一个小功能就提交
- **功能完整**: 确保每次提交都是可运行的状态
- **描述清晰**: 提交信息要清楚说明做了什么

### 代码审查
- 重要功能开发完成后，创建Pull Request进行代码审查
- 至少一人审查通过后才能合并到develop分支

### 冲突处理
```bash
# 如果遇到合并冲突
git status                    # 查看冲突文件
# 手动解决冲突后
git add .
git commit -m "resolve: 解决合并冲突"
```

## 📋 Git配置建议

### 全局配置
```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 设置默认编辑器
git config --global core.editor "code --wait"

# 设置默认分支名
git config --global init.defaultBranch main
```

### 项目配置
```bash
# 设置换行符处理
git config core.autocrlf input

# 设置忽略文件权限变化
git config core.filemode false
```

## 🔧 常用命令

### 查看状态
```bash
git status                    # 查看工作区状态
git log --oneline            # 查看提交历史
git branch -a                # 查看所有分支
git diff                     # 查看工作区变化
```

### 撤销操作
```bash
git checkout -- <file>      # 撤销工作区修改
git reset HEAD <file>       # 撤销暂存区修改
git reset --soft HEAD~1     # 撤销最后一次提交(保留修改)
git reset --hard HEAD~1     # 撤销最后一次提交(丢弃修改)
```

### 分支操作
```bash
git branch <branch-name>     # 创建分支
git checkout <branch-name>   # 切换分支
git checkout -b <branch>     # 创建并切换分支
git branch -d <branch>       # 删除分支
git push origin --delete <branch>  # 删除远程分支
```

## 📊 项目当前状态

- ✅ Git仓库已初始化
- ✅ 初始提交已完成 (121个文件，38313行代码)
- ✅ develop分支已创建
- ✅ .gitignore配置完善
- 🔄 准备开始功能开发

**下一步**: 开始实现具体的MVP功能模块
