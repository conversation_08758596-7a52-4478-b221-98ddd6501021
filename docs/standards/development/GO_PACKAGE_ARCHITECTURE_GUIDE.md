# Go包架构设计指南

## 🎯 目标
避免循环依赖，建立清晰的包层次结构，遵循Go语言最佳实践。

## ❌ 常见错误模式

### 1. pkg包依赖internal包（严重违规）
```
❌ 错误：
pkg/testutil → internal/services  # pkg不应该依赖internal
```

### 2. 循环依赖
```
❌ 错误：
internal/testutil → internal/services
       ↑                    ↓
internal/services ← 测试文件
```

## ✅ 正确的架构模式

### 方案A：internal/pkg模式（推荐）
```
✅ 正确的层次结构：
internal/
├── pkg/                    # 内部业务工具包
│   └── testutil/          # 业务测试工具
│       ├── testutil.go    # 业务测试基础设施
│       ├── fixtures.go    # 测试数据夹具
│       └── helpers.go     # 业务测试辅助函数
├── services/              # 业务逻辑层
├── repositories/          # 数据访问层
└── models/               # 数据模型

依赖关系：
internal/pkg/testutil → internal/services ✅ 合法
internal/pkg/testutil → internal/repositories ✅ 合法
internal/pkg/testutil → internal/models ✅ 合法
```

### 方案B：分层testutil模式
```
✅ 分层架构：
pkg/testutil/              # 通用测试工具（无业务依赖）
├── database.go           # 数据库测试工具
├── redis.go             # Redis测试工具
└── http.go              # HTTP测试工具

internal/testutil/         # 业务测试工具
├── fixtures.go          # 业务数据夹具
├── services.go          # 服务层测试工具
└── repositories.go      # 数据层测试工具
```

## 🏗️ 实施步骤

### 步骤1：创建internal/pkg/testutil
```bash
mkdir -p internal/pkg/testutil
```

### 步骤2：迁移业务相关测试工具
将需要依赖internal包的测试工具移动到`internal/pkg/testutil/`

### 步骤3：保持pkg/testutil纯净
`pkg/testutil/`只保留通用的、无业务依赖的测试工具

### 步骤4：更新导入路径
```go
// 测试文件中的导入
import (
    "go-api-framework/pkg/testutil"        // 通用测试工具
    "go-api-framework/internal/pkg/testutil" // 业务测试工具
)
```

## 📋 包职责划分

### pkg/testutil（通用测试工具）
- ✅ 数据库连接管理
- ✅ Redis连接管理  
- ✅ HTTP测试工具
- ✅ 随机数据生成
- ❌ 不依赖任何internal包

### internal/pkg/testutil（业务测试工具）
- ✅ 业务数据夹具
- ✅ 服务层测试辅助
- ✅ 业务场景模拟
- ✅ 可以依赖internal包

## 🔍 依赖检查规则

### 1. pkg包依赖检查
```bash
# 检查pkg包是否依赖internal包
grep -r "internal/" pkg/ && echo "❌ 违规：pkg包不应依赖internal包"
```

### 2. 循环依赖检查
```bash
# 使用go mod graph检查循环依赖
go mod graph | grep -E "(A → B.*B → A)"
```

## 🛠️ 预防措施

### 1. 代码审查检查点
- [ ] pkg包是否导入了internal包？
- [ ] 是否存在循环依赖？
- [ ] 测试工具是否放在正确的位置？

### 2. 自动化检查
在CI/CD中添加包依赖检查：
```bash
#!/bin/bash
# scripts/check-package-deps.sh
if grep -r "internal/" pkg/; then
    echo "❌ 错误：pkg包不应依赖internal包"
    exit 1
fi
echo "✅ 包依赖检查通过"
```

### 3. IDE配置
配置IDE在导入internal包到pkg包时给出警告。

## 📚 最佳实践总结

1. **严格分层**：pkg → internal，不能反向依赖
2. **业务隔离**：业务相关的测试工具放在internal/pkg/
3. **通用工具**：通用测试工具放在pkg/testutil/
4. **定期检查**：使用自动化工具检查包依赖
5. **团队约定**：建立明确的包使用规范

## 🎯 下一步行动

1. 创建`internal/pkg/testutil/`目录
2. 迁移业务相关测试工具
3. 更新所有测试文件的导入路径
4. 添加包依赖检查脚本
5. 更新团队开发规范
