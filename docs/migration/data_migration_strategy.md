# 数据迁移策略和风险控制方案

## 📋 迁移概述

### 目标
将现有的基于孩子的积分和勋章体系改造为基于训练营参与记录的体系，实现更精细的激励机制。

### 核心变更
1. **child_points 表**：从 `child_id` 唯一改为 `participation_id` 唯一
2. **child_medals 表**：从 `(child_id, medal_id)` 唯一改为 `(participation_id, medal_id)` 唯一
3. **数据重构**：将现有数据按训练营参与记录重新分配

## 🎯 数据分配策略

### 积分记录分配策略

#### 策略1：基于历史记录重新计算（推荐）
```sql
-- 根据 point_records 表的历史记录，按 participation_id 重新计算每个训练营的积分
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    ucp.camp_id,
    COALESCE(SUM(CASE WHEN pr.points_change > 0 THEN pr.points_change ELSE 0 END), 0) as total_points,
    COUNT(DISTINCT cr.checkin_date) as total_checkins
FROM user_camp_participations ucp
LEFT JOIN point_records pr ON pr.participation_id = ucp.id
LEFT JOIN checkin_records cr ON cr.participation_id = ucp.id
GROUP BY ucp.id;
```

**优点**：
- 数据准确性高，基于真实的历史记录
- 能够正确反映每个训练营的实际贡献

**缺点**：
- 如果历史记录中 participation_id 字段不完整，可能丢失部分数据
- 计算复杂度较高

#### 策略2：平均分配（备选方案）
```sql
-- 将孩子的总积分平均分配到各个训练营参与记录
SELECT 
    cp.child_id,
    ucp.id as participation_id,
    ROUND(cp.total_points / participation_count.count) as allocated_points
FROM child_points cp
JOIN user_camp_participations ucp ON cp.child_id = ucp.child_id
JOIN (
    SELECT child_id, COUNT(*) as count 
    FROM user_camp_participations 
    WHERE deleted_at IS NULL 
    GROUP BY child_id
) participation_count ON cp.child_id = participation_count.child_id;
```

**优点**：
- 实施简单，不会丢失积分数据
- 适用于历史记录不完整的情况

**缺点**：
- 数据准确性较低，无法反映真实的训练营贡献
- 可能导致不公平的积分分配

### 勋章记录分配策略

#### 策略1：只迁移已解锁勋章（推荐）
```sql
-- 只为已解锁的勋章在每个训练营中创建记录
INSERT INTO child_medals (child_id, participation_id, medal_id, is_unlocked, ...)
SELECT cm.child_id, ucp.id, cm.medal_id, cm.is_unlocked, ...
FROM child_medals cm
CROSS JOIN user_camp_participations ucp
WHERE cm.child_id = ucp.child_id AND cm.is_unlocked = 1;
```

**优点**：
- 避免创建大量未解锁的勋章记录
- 数据量相对较小，性能较好

**缺点**：
- 未解锁勋章的进度信息可能丢失
- 需要重新初始化勋章进度

#### 策略2：全量迁移（备选方案）
```sql
-- 为所有勋章在每个训练营中创建记录
INSERT INTO child_medals (child_id, participation_id, medal_id, ...)
SELECT cm.child_id, ucp.id, cm.medal_id, ...
FROM child_medals cm
CROSS JOIN user_camp_participations ucp
WHERE cm.child_id = ucp.child_id;
```

**优点**：
- 保留所有勋章进度信息
- 数据完整性高

**缺点**：
- 数据量激增，可能影响性能
- 存储成本增加

## ⚠️ 风险评估和控制措施

### 高风险点

#### 1. 数据丢失风险 🔴
**风险描述**：迁移过程中可能丢失现有的积分和勋章数据

**控制措施**：
- **完整备份**：迁移前创建完整的数据库备份
- **表级备份**：为关键表创建备份表
- **分步验证**：每个步骤后进行数据一致性检查
- **回滚方案**：准备完整的回滚脚本

```sql
-- 备份关键表
CREATE TABLE child_points_backup_20250102 AS SELECT * FROM child_points;
CREATE TABLE child_medals_backup_20250102 AS SELECT * FROM child_medals;

-- 验证备份完整性
SELECT 
    (SELECT COUNT(*) FROM child_points) as original_points,
    (SELECT COUNT(*) FROM child_points_backup_20250102) as backup_points,
    (SELECT COUNT(*) FROM child_medals) as original_medals,
    (SELECT COUNT(*) FROM child_medals_backup_20250102) as backup_medals;
```

#### 2. 业务中断风险 🟡
**风险描述**：迁移期间可能影响用户正常使用

**控制措施**：
- **维护窗口**：选择用户活跃度最低的时间段执行
- **分批迁移**：将大表分批处理，减少锁表时间
- **监控告警**：实时监控迁移进度和系统状态
- **快速回滚**：准备快速回滚方案

#### 3. 性能影响风险 🟡
**风险描述**：迁移后查询性能可能下降

**控制措施**：
- **索引优化**：为新的查询模式创建合适的索引
- **查询优化**：重写相关查询语句
- **性能测试**：迁移后进行全面的性能测试

### 数据一致性检查

#### 迁移前检查
```sql
-- 检查现有数据状态
SELECT 
    'Pre-migration check' as check_type,
    (SELECT COUNT(*) FROM child_points WHERE deleted_at IS NULL) as child_points_count,
    (SELECT COUNT(*) FROM child_medals WHERE deleted_at IS NULL) as child_medals_count,
    (SELECT COUNT(*) FROM user_camp_participations WHERE deleted_at IS NULL) as participations_count,
    (SELECT COUNT(DISTINCT child_id) FROM child_points WHERE deleted_at IS NULL) as unique_children;
```

#### 迁移后检查
```sql
-- 检查迁移后数据状态
SELECT 
    'Post-migration check' as check_type,
    (SELECT COUNT(*) FROM child_points WHERE deleted_at IS NULL) as new_child_points_count,
    (SELECT COUNT(*) FROM child_medals WHERE deleted_at IS NULL) as new_child_medals_count,
    (SELECT COUNT(DISTINCT child_id) FROM child_points WHERE deleted_at IS NULL) as unique_children_in_points,
    (SELECT COUNT(DISTINCT participation_id) FROM child_points WHERE deleted_at IS NULL) as unique_participations;

-- 检查孤立记录
SELECT 
    'Orphaned records check' as check_type,
    (SELECT COUNT(*) FROM child_points cp 
     LEFT JOIN user_camp_participations ucp ON cp.participation_id = ucp.id 
     WHERE cp.deleted_at IS NULL AND ucp.id IS NULL) as orphaned_points,
    (SELECT COUNT(*) FROM child_medals cm 
     LEFT JOIN user_camp_participations ucp ON cm.participation_id = ucp.id 
     WHERE cm.deleted_at IS NULL AND ucp.id IS NULL) as orphaned_medals;
```

## 📅 实施计划

### 阶段1：准备阶段（预计1天）
1. **环境准备**
   - 在测试环境完整模拟生产数据
   - 验证迁移脚本的正确性
   - 进行性能测试

2. **备份准备**
   - 创建完整的数据库备份
   - 创建关键表的备份表
   - 验证备份的完整性

3. **代码准备**
   - 准备新版本的代码
   - 进行充分的单元测试和集成测试
   - 准备回滚版本的代码

### 阶段2：迁移执行（预计2-4小时）
1. **系统维护**（30分钟）
   - 开启维护模式，停止用户访问
   - 停止相关的后台任务
   - 确保数据库连接稳定

2. **表结构修改**（30分钟）
   - 删除现有约束
   - 添加新字段
   - 创建新约束和索引

3. **数据迁移**（1-3小时）
   - 执行积分记录迁移
   - 执行勋章记录迁移
   - 进行数据一致性检查

4. **代码部署**（30分钟）
   - 部署新版本代码
   - 重启相关服务
   - 进行功能验证

### 阶段3：验证阶段（预计1小时）
1. **功能验证**
   - 验证积分统计功能
   - 验证勋章系统功能
   - 验证排行榜功能

2. **性能验证**
   - 检查关键接口的响应时间
   - 监控数据库查询性能
   - 检查系统资源使用情况

3. **用户验证**
   - 开放部分用户进行测试
   - 收集用户反馈
   - 修复发现的问题

### 阶段4：上线阶段（预计30分钟）
1. **全量开放**
   - 关闭维护模式
   - 恢复所有用户访问
   - 启动后台任务

2. **监控告警**
   - 密切监控系统状态
   - 关注错误日志和性能指标
   - 准备应急响应

## 🔄 回滚方案

### 触发条件
- 数据迁移失败或数据不一致
- 新功能出现严重bug影响用户使用
- 系统性能严重下降
- 用户反馈强烈负面

### 回滚步骤
1. **立即停止服务**（5分钟）
   - 开启维护模式
   - 停止所有相关服务

2. **恢复数据库**（30-60分钟）
   - 恢复表结构到原始状态
   - 从备份表恢复数据
   - 验证数据完整性

3. **回滚代码**（15分钟）
   - 部署原版本代码
   - 重启服务
   - 验证功能正常

4. **恢复服务**（15分钟）
   - 关闭维护模式
   - 监控系统状态
   - 通知用户服务恢复

### 回滚脚本
```sql
-- 紧急回滚脚本
-- 1. 恢复表结构
ALTER TABLE child_points DROP INDEX uk_participation_id;
ALTER TABLE child_points DROP COLUMN participation_id;
ALTER TABLE child_points ADD UNIQUE KEY uk_child_id (child_id);

ALTER TABLE child_medals DROP INDEX uk_participation_id_medal;
ALTER TABLE child_medals DROP COLUMN participation_id;
ALTER TABLE child_medals ADD UNIQUE KEY uk_child_medal (child_id, medal_id);

-- 2. 恢复数据
DELETE FROM child_points;
INSERT INTO child_points SELECT * FROM child_points_backup_20250102;

DELETE FROM child_medals;
INSERT INTO child_medals SELECT * FROM child_medals_backup_20250102;
```

## 📊 成功标准

### 数据完整性标准
- 迁移后的积分记录数量 = 参与记录数量
- 迁移后的勋章记录数量 >= 原勋章记录数量（已解锁）
- 无孤立记录（所有记录都有对应的参与记录）
- 积分总和保持一致（允许5%的误差）

### 性能标准
- 关键接口响应时间 < 500ms
- 数据库查询时间 < 100ms
- 系统可用性 > 99.9%

### 功能标准
- 积分统计功能正常
- 勋章系统功能正常
- 排行榜功能正常
- 打卡功能正常

## 📞 应急联系

### 技术团队
- 数据库管理员：负责数据库操作和监控
- 后端开发：负责代码部署和功能验证
- 运维工程师：负责系统监控和应急响应
- 测试工程师：负责功能验证和用户测试

### 沟通机制
- 实时沟通群：技术团队实时沟通
- 状态报告：每30分钟报告一次进度
- 问题升级：发现问题立即升级
- 用户通知：及时通知用户系统状态
