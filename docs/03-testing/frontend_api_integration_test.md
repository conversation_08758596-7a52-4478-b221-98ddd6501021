# 前端API集成测试指南

## 测试目标
验证微信小程序前端页面能够正确调用新的训练营打卡日历API，并正确显示日历数据。

## 测试环境准备

### 1. 后端API服务
确保后端API服务正在运行：
```bash
cd api
go run main.go
```
服务应该在 `http://localhost:8080` 运行。

### 2. 微信开发者工具
1. 打开微信开发者工具
2. 导入项目：选择 `miniprogram` 目录
3. 确保开发者工具的网络设置允许访问本地服务器

### 3. 测试数据
确保数据库中有测试数据：
```bash
cd api
go run tools/insert_test_data.go
```

## 测试步骤

### 第一步：检查API配置
1. 打开 `miniprogram/utils/constants.js`
2. 确认 `API.BASE_URL` 指向正确的后端服务地址：
   ```javascript
   API: {
     BASE_URL: "http://localhost:8080/api/v1"
   }
   ```

### 第二步：测试页面导航
1. 在微信开发者工具中启动小程序
2. 导航到打卡详情页面：
   - URL: `/pages/growth/detail/detail?camp_id=1`
   - 或通过其他页面的链接进入

### 第三步：验证API调用
1. 打开开发者工具的控制台
2. 查看网络请求：
   - 应该看到对 `/api/v1/camps/1/checkin-calendar/1` 的请求
   - 请求应该返回200状态码
   - 响应数据应该包含 `camp_info`, `calendar_data`, `makeup_info`

### 第四步：验证数据显示
检查以下内容是否正确显示：

#### 训练营信息
- [ ] 训练营标题：`21天跳绳养成计划`
- [ ] 副标题：`让孩子从不会跳到连续100个`
- [ ] 总天数：`21天`
- [ ] 当前进度

#### 日历显示
- [ ] 周视图显示7天
- [ ] 月视图显示完整月份
- [ ] 日期状态正确显示：
  - ✓ 已完成（绿色背景）
  - ↻ 补打卡（蓝色背景）
  - ○ 待打卡（橙色背景）
  - - 已错过（红色背景）

#### 补卡信息
- [ ] 总补卡次数显示
- [ ] 已使用次数显示
- [ ] 剩余次数显示

## 测试用例

### 用例1：正常加载
**步骤：**
1. 进入页面 `/pages/growth/detail/detail?camp_id=1`
2. 等待数据加载完成

**预期结果：**
- 页面正常显示
- 无错误信息
- 日历数据正确渲染

### 用例2：网络错误处理
**步骤：**
1. 关闭后端服务
2. 刷新页面或重新进入

**预期结果：**
- 显示网络错误提示
- 提供重试按钮
- 不会崩溃

### 用例3：参数错误处理
**步骤：**
1. 进入页面 `/pages/growth/detail/detail?camp_id=999`（不存在的ID）

**预期结果：**
- 显示相应的错误信息
- 页面不会崩溃

## 调试技巧

### 1. 控制台日志
查看以下关键日志：
```
📅 loadCampCheckinCalendar 调用参数: {campId: 1, childId: 1}
✅ 训练营打卡日历: {data: {...}}
✅ 数据加载完成
```

### 2. 网络面板
检查API请求：
- 请求URL是否正确
- 请求头是否包含认证信息
- 响应状态码和数据

### 3. 数据面板
在微信开发者工具的AppData面板中检查：
- `campInfo` 对象
- `currentWeek` 数组
- `currentMonthDays` 数组
- `makeupInfo` 对象

## 常见问题

### 问题1：API调用失败
**可能原因：**
- 后端服务未启动
- 网络配置问题
- 认证token过期

**解决方案：**
1. 检查后端服务状态
2. 检查网络设置
3. 重新生成测试token

### 问题2：日历显示异常
**可能原因：**
- 数据格式不匹配
- 状态映射错误
- CSS样式问题

**解决方案：**
1. 检查API响应数据格式
2. 验证状态映射逻辑
3. 检查CSS样式定义

### 问题3：页面加载缓慢
**可能原因：**
- API响应时间长
- 数据量过大
- 网络延迟

**解决方案：**
1. 优化API查询
2. 添加加载状态提示
3. 实现数据缓存

## 测试完成标准
- [ ] 页面能正常加载和显示
- [ ] API调用成功，数据正确
- [ ] 日历状态显示正确
- [ ] 错误处理机制正常
- [ ] 用户交互响应正常
- [ ] 性能表现良好（加载时间<3秒）
