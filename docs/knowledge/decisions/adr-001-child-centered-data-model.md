# ADR-001: 以孩子为中心的数据模型设计

## 状态
已接受 - 2025-07-06

## 背景
在初始设计中，我们错误地将孩子的学习数据（打卡、积分、任务）与用户（家长）关联，这导致了以下问题：
1. 数据模型不符合业务逻辑：孩子才是学习的主体
2. 统计数据冗余存储在多个表中，违反了数据规范化原则
3. 查询逻辑复杂，需要同时关联用户和孩子

## 决策
重新设计数据模型，采用**以孩子为中心**的设计原则：

### 核心原则
1. **用户表**：只存储家长信息，不包含任何孩子相关数据
2. **孩子表**：只存储孩子基本信息，不包含统计数据
3. **业务表**：只关联孩子ID，不关联用户ID
4. **统计数据**：通过实时计算或定时任务生成，不冗余存储

### 数据关系
```
用户表 (Users) - 家长信息
    ↓ 通过 user_children 表关联
孩子表 (Children) - 孩子基本信息  
    ↓ 一对多关联
打卡表 (Checkins) - 只关联 child_id
积分表 (Points) - 只关联 child_id  
任务表 (Tasks) - 只关联 child_id
```

## 影响范围
- `users` 表：保持现状，无需修改
- `children` 表：移除统计字段
- `checkin_records` 表：移除 user_id 字段
- `user_points` 表：移除 user_id 字段，重命名为 `child_points`
- `point_records` 表：移除 user_id 字段
- 所有任务相关表：只关联 child_id

## 优势
1. **业务逻辑清晰**：数据模型直接反映业务现实
2. **数据一致性**：避免冗余数据的同步问题
3. **查询简化**：减少复杂的多表关联
4. **扩展性好**：便于后续功能扩展

## 风险
1. **查询性能**：统计数据需要实时计算，可能影响性能
2. **开发复杂度**：需要重新设计相关的业务逻辑

## 缓解措施
1. 对于频繁查询的统计数据，使用Redis缓存
2. 设计定时任务定期更新缓存
3. 为关键查询添加合适的数据库索引

## 实施计划
1. 重新设计表结构
2. 更新数据模型代码
3. 修改相关的业务逻辑
4. 更新API接口
5. 编写数据迁移脚本
