# ADR-003: 通用Go API框架架构设计

## 状态
- [x] 已接受

## 决策者
- 开发者：用户
- AI助手：Augment Agent
- 日期：2025-01-03

## 背景和问题
### 当前状况
- 需要创建一个通用的Go后端API框架
- 要求支持API/Admin双服务分离
- 需要内置代码生成器提高开发效率
- 要求遵循MVS规则确保代码质量

### 需要解决的问题
- 如何设计清晰的项目结构？
- 如何实现API/Admin服务分离？
- 如何设计统一的分层架构？
- 如何集成代码生成器？

## 决策

### 选择的方案：分层架构 + 双服务分离

## 🏗️ 架构设计

### 1. 目录结构设计
```
api/
├── cmd/                     # 应用入口
│   ├── api-server/         # API服务入口
│   └── admin-server/       # 管理后台入口
├── internal/               # 内部代码
│   ├── handlers/           # HTTP处理器
│   │   ├── api/           # API处理器
│   │   └── admin/         # 管理后台处理器
│   ├── services/          # 业务逻辑层
│   ├── repositories/      # 数据访问层
│   └── models/           # 数据模型
├── pkg/                   # 可复用包
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接
│   ├── logger/           # 日志管理
│   ├── response/         # 响应格式
│   └── errors/           # 错误处理
├── configs/              # 配置文件
├── database/            # 数据库相关
└── scripts/             # 脚本工具
```

### 2. 分层架构设计
- **Handler层**：HTTP请求处理，参数验证，调用Service
- **Service层**：业务逻辑，事务管理，调用Repository
- **Repository层**：数据访问，数据库操作
- **Model层**：数据模型，请求/响应结构

### 3. 双服务分离策略
- **API服务**：面向用户端，端口8080
- **Admin服务**：面向管理端，端口8081
- **共享层**：Service/Repository/Model层共享

### 4. 技术栈选择
- **Web框架**：Gin（轻量级，性能好）
- **ORM**：GORM（功能完整，社区活跃）
- **数据库**：MySQL/PostgreSQL（可配置）
- **配置**：Viper（功能强大）
- **日志**：Logrus（结构化日志）

## 决策原因

### 1. 目录结构原因
- **cmd分离**：支持多个服务入口，便于独立部署
- **internal包**：Go语言最佳实践，防止外部导入
- **pkg包**：可复用组件，便于其他项目使用
- **分层清晰**：职责明确，便于维护和测试

### 2. 双服务分离原因
- **独立部署**：API和Admin可以独立扩展
- **安全隔离**：不同的认证和权限策略
- **性能优化**：针对不同场景优化
- **开发效率**：团队可以并行开发

### 3. 技术栈原因
- **Gin**：性能优秀，中间件丰富，社区活跃
- **GORM**：功能完整，支持多种数据库，代码生成友好
- **Viper**：配置管理强大，支持多种格式
- **Logrus**：结构化日志，便于监控和分析

## 影响范围
- 所有新项目都将基于此架构
- 现有项目可以逐步迁移到此架构
- 开发流程和规范需要相应调整

## 实施要求

### 1. 代码组织要求
- 严格按照分层架构组织代码
- Handler层只处理HTTP相关逻辑
- Service层处理业务逻辑和事务
- Repository层只处理数据访问

### 2. 命名规范要求
- 文件名使用snake_case
- 结构体使用PascalCase
- 方法名使用camelCase
- 常量使用UPPER_CASE

### 3. 错误处理要求
- 使用统一的BusinessError结构
- Repository层返回业务错误
- Handler层转换为HTTP响应

### 4. 测试要求
- 每层都要有对应的测试
- Repository层测试数据库操作
- Service层测试业务逻辑
- Handler层测试HTTP接口

## 代码生成器设计

### 1. 生成器功能
- 自动生成Model/Repository/Service/Handler
- 支持自定义字段和关联关系
- 生成标准的CRUD操作
- 生成对应的测试文件

### 2. 生成器使用
```bash
# 生成基础模块
./generators/generate.sh user --comment '用户'

# 生成带字段的模块
./generators/generate.sh product --fields 'name:string:产品名称,price:float64:价格'
```

## 配置管理设计

### 1. 配置文件结构
```yaml
server:
  mode: debug
  api:
    host: "0.0.0.0"
    port: "8080"
  admin:
    host: "0.0.0.0"
    port: "8081"

database:
  driver: mysql
  host: localhost
  port: 3306
  # ...其他配置
```

### 2. 环境变量支持
- 支持通过环境变量覆盖配置
- 生产环境敏感信息使用环境变量
- 开发环境使用配置文件

## 部署策略

### 1. 构建脚本
- 提供Makefile和shell脚本
- 支持一键构建和部署
- 集成代码检查和测试

### 2. 服务管理
- 提供启动/停止脚本
- 支持进程管理和监控
- 日志文件管理

## 监控和维护

### 1. 健康检查
- 每个服务提供健康检查端点
- 数据库连接状态检查
- 依赖服务状态检查

### 2. 日志管理
- 结构化日志输出
- 可配置日志级别
- 支持文件和控制台输出

## 风险和缓解措施

### 1. 性能风险
- **风险**：双服务可能增加资源消耗
- **缓解**：通过配置优化，按需启动服务

### 2. 复杂性风险
- **风险**：架构复杂度增加
- **缓解**：提供详细文档和示例

### 3. 学习成本风险
- **风险**：团队需要学习新架构
- **缓解**：提供培训和最佳实践指南

## 后续计划

### 1. 短期计划（1-2周）
- 完善代码生成器功能
- 添加认证和权限模块
- 完善测试覆盖

### 2. 中期计划（1个月）
- 添加缓存支持
- 集成监控和指标
- 优化性能

### 3. 长期计划（3个月）
- 支持微服务架构
- 添加分布式追踪
- 完善CI/CD流程

---

**此架构设计为通用Go API框架提供了坚实的基础，确保了可扩展性、可维护性和开发效率。**
