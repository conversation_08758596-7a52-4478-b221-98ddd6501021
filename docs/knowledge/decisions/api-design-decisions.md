# API设计决策记录

## 📋 决策记录格式
每个决策包含：决策内容、决策原因、影响范围、实施时间

---

## 决策001: Service方法参数顺序统一
**决策时间**: 2025-07-02  
**决策内容**: 所有Service层方法参数顺序统一为 `(request, userID)`  
**决策原因**: 
- request是主要的业务参数，应该放在第一位
- userID是通用的权限验证参数，放在第二位
- 统一顺序避免开发时的混淆和错误

**影响范围**: 所有Service层接口和实现  
**实施要求**: 
- 所有新开发的Service方法必须遵循此顺序
- 现有方法在重构时逐步调整
- 测试代码也要遵循相同顺序

**代码示例**:
```go
// ✅ 正确的参数顺序
func (s *taskService) CreateTask(req *models.TaskCreateRequest, userID int) (*models.TaskResponse, error)
func (s *taskService) UpdateTask(id int, req *models.TaskUpdateRequest, userID int) (*models.TaskResponse, error)

// ❌ 错误的参数顺序  
func (s *taskService) CreateTask(userID int, req *models.TaskCreateRequest) (*models.TaskResponse, error)
```

---

## 决策002: 统一错误处理模式
**决策时间**: 2025-07-02  
**决策内容**: 使用 `pkg/errors` 包统一错误码和错误处理  
**决策原因**:
- 前端需要统一的错误码进行处理
- 便于错误日志的分析和监控
- 提供更好的用户体验

**影响范围**: 所有API接口的错误返回  
**实施要求**:
- 所有业务错误必须使用预定义的错误码
- 错误信息要对用户友好
- 系统错误要记录详细日志但不暴露给用户

**代码示例**:
```go
// ✅ 正确的错误处理
if task == nil {
    return nil, errors.NewBusinessError(errors.ErrCodeTaskNotFound, "任务不存在")
}

// ❌ 错误的错误处理
if task == nil {
    return nil, fmt.Errorf("task not found")
}
```

---

## 决策003: RESTful API路径设计规范
**决策时间**: 2025-07-02  
**决策内容**: 统一使用RESTful风格的API路径设计  
**决策原因**:
- 符合行业标准，便于理解和维护
- 路径语义清晰，便于前端开发
- 支持标准的HTTP方法语义

**影响范围**: 所有API端点设计  
**实施要求**:
- 资源名称使用复数形式
- 使用标准的HTTP方法(GET/POST/PUT/DELETE)
- 子资源使用嵌套路径

**路径规范**:
```
GET    /api/v1/tasks           # 获取任务列表
POST   /api/v1/tasks           # 创建任务
GET    /api/v1/tasks/:id       # 获取任务详情
PUT    /api/v1/tasks/:id       # 更新任务
DELETE /api/v1/tasks/:id       # 删除任务

# 子资源
GET    /api/v1/tasks/:id/progress        # 获取任务进度
POST   /api/v1/tasks/:id/complete        # 完成任务
GET    /api/v1/children/:id/tasks        # 获取孩子的任务
```

---

## 决策004: 数据库字段命名规范
**决策时间**: 2025-07-02  
**决策内容**: 数据库字段使用snake_case命名，Go结构体使用PascalCase  
**决策原因**:
- 数据库字段遵循SQL标准命名规范
- Go结构体遵循Go语言命名规范
- 使用db tag进行映射

**影响范围**: 所有数据库表设计和Go模型定义  
**实施要求**:
- 数据库字段：user_id, created_at, task_type
- Go字段：UserID, CreatedAt, TaskType
- 必须使用db tag进行映射

**代码示例**:
```go
type Task struct {
    ID          int       `json:"id" db:"id"`
    UserID      int       `json:"user_id" db:"user_id"`
    TaskType    string    `json:"task_type" db:"task_type"`
    CreatedAt   time.Time `json:"created_at" db:"created_at"`
}
```

---

## 决策005: API响应格式统一
**决策时间**: 2025-07-02  
**决策内容**: 所有API响应使用统一的格式结构  
**决策原因**:
- 前端可以统一处理响应
- 便于错误处理和状态判断
- 提供一致的用户体验

**影响范围**: 所有API接口响应  
**实施要求**:
- 成功响应包含code、message、data字段
- 错误响应包含code、message字段
- 分页响应额外包含pagination字段

**响应格式**:
```json
// 成功响应
{
  "code": 0,
  "message": "success",
  "data": { ... }
}

// 错误响应
{
  "code": 1001,
  "message": "参数错误"
}

// 分页响应
{
  "code": 0,
  "message": "success", 
  "data": [...],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 100
  }
}
```

---

## 决策模板
```markdown
## 决策XXX: [决策标题]
**决策时间**: YYYY-MM-DD  
**决策内容**: [具体的决策内容]  
**决策原因**: 
- [原因1]
- [原因2]

**影响范围**: [影响的代码范围]  
**实施要求**: 
- [要求1]
- [要求2]

**代码示例**:
```go
// ✅ 正确示例
// ❌ 错误示例
```
```

## 📊 决策执行统计
| 决策编号 | 决策内容 | 执行状态 | 遵守率 | 备注 |
|----------|----------|----------|--------|------|
| 001 | Service参数顺序 | ✅ 已执行 | 100% | Task模块已应用 |
| 002 | 错误处理模式 | 🔄 部分执行 | 80% | 需要完善错误码 |
| 003 | RESTful路径 | ✅ 已执行 | 100% | 所有API已遵循 |
| 004 | 字段命名规范 | ✅ 已执行 | 100% | 数据库设计已遵循 |
| 005 | 响应格式统一 | 🔄 部分执行 | 70% | 需要统一Handler层 |
