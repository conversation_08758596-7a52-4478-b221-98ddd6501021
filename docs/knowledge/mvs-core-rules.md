# 最小可行标准 (MVS) - 核心规则

## 🎯 核心理念
只包含最关键的5条规则，确保每条都能严格执行。随着项目发展逐步扩展。

## 📋 6条核心规则

### 规则1: 开发前必须完成需求确认
**要求**: 任何功能开发前，必须完成结构化需求确认对话
**检查点**: 
- [ ] 功能目标明确
- [ ] 用户角色确定
- [ ] 功能边界清晰
- [ ] 数据库设计验证

**模板**: 使用 `docs/knowledge/templates/requirements-template.md`
**强制执行**: AI助手每次开发前必须执行需求确认对话

### 规则2: 使用统一的代码模板
**要求**: 所有代码必须基于标准模板开发
**检查点**:
- [ ] Repository层使用标准接口
- [ ] Service层使用标准接口
- [ ] Handler层使用标准模式
- [ ] Model层使用标准结构

**模板位置**: `docs/knowledge/patterns/`
**强制执行**: 开发前必须复制对应模板

### 规则3: 每个功能必须有基础测试
**要求**: 每个功能至少要有Repository层和Service层的基础测试
**检查点**:
- [ ] Repository层CRUD测试
- [ ] Service层业务逻辑测试
- [ ] 错误处理测试
- [ ] 编译通过测试

**模板**: 使用 `docs/knowledge/templates/testing-template.md`
**强制执行**: 合并前必须通过测试

### 规则4: 提交前运行检查清单
**要求**: 每次提交前必须运行完整的检查清单
**检查点**:
- [ ] 代码编译通过
- [ ] 基础测试通过
- [ ] 代码格式化完成
- [ ] 提交信息规范

**清单位置**: `docs/knowledge/templates/checklists/pre-commit-checklist.md`
**强制执行**: Git pre-commit hook自动检查

### 规则5: 合并前更新知识库
**要求**: 每次功能完成后必须更新相关知识库
**检查点**:
- [ ] 记录重要决策到decisions/
- [ ] 记录遇到的问题到problems/
- [ ] 更新代码模式到patterns/
- [ ] 完善开发模板

**强制执行**: 合并前必须确认知识库更新

### 规则6: 架构变更管理
**要求**: 任何影响框架、模板或共享组件的变更必须遵循架构变更流程
**检查点**:
- [ ] 明确变更类型（功能开发 vs 架构变更）
- [ ] 创建ADR文档记录变更决策
- [ ] 评估对现有代码的影响范围
- [ ] 先更新模板和文档，再更新代码

**适用场景**:
- 修改代码模板（Repository、Service、Handler等）
- 添加新的共享服务或中间件
- 改变项目目录结构
- 修改数据库架构设计
- 更新技术栈或依赖

**执行流程**:
1. **变更识别**: 确认这是架构变更而非普通功能开发
2. **影响分析**: 列出所有受影响的模块和文件
3. **方案设计**: 制定详细的实施方案和迁移计划
4. **模板优先**: 先更新 `docs/knowledge/patterns/` 中的模板
5. **批量更新**: 使用生成器或脚本批量更新现有代码
6. **验证测试**: 确保所有模块仍能正常工作

**ADR模板**: 使用 `docs/knowledge/templates/adr-template.md`
**强制执行**: 架构变更前必须完成ADR文档并获得确认

## 🔧 执行机制

### AI助手执行规则
```markdown
## AI助手内置检查逻辑

### 开发启动时
0. 询问本次开发是"功能开发"还是"架构变更"
1. 自动查阅 docs/knowledge/decisions/ 相关决策
2. 自动查阅 docs/knowledge/patterns/ 相关模式
3. 强制执行需求确认对话（功能开发）或ADR流程（架构变更）
4. 确认用户理解后才开始编码

### 开发过程中
1. 严格按照代码模板开发
2. 遇到问题先查阅 docs/knowledge/problems/
3. 新问题解决后立即记录
4. 定期提醒检查进度

### 开发完成后
1. 强制运行检查清单
2. 强制更新知识库
3. 确认所有规则遵守后才能合并
```

### 工具强制机制
```bash
# Git pre-commit hook
#!/bin/bash
echo "🔍 执行MVS核心规则检查..."

# 规则4: 检查代码编译
echo "📋 检查代码编译..."
cd api && go build ./cmd/api-server
if [ $? -ne 0 ]; then
    echo "❌ 代码编译失败，请修复后再提交"
    exit 1
fi

# 规则4: 检查代码格式
echo "📋 检查代码格式..."
go fmt ./...

# 规则4: 检查基础测试
echo "📋 检查基础测试..."
go test ./... -short
if [ $? -ne 0 ]; then
    echo "❌ 基础测试失败，请修复后再提交"
    exit 1
fi

echo "✅ MVS核心规则检查通过"
```

## 📊 规则执行统计

### 统计模板
```markdown
## MVS规则执行统计 - [日期]

| 规则 | 执行次数 | 通过次数 | 通过率 | 问题记录 |
|------|----------|----------|--------|----------|
| 需求确认 | 0 | 0 | 0% | 待开始统计 |
| 代码模板 | 0 | 0 | 0% | 待开始统计 |
| 基础测试 | 0 | 0 | 0% | 待开始统计 |
| 检查清单 | 0 | 0 | 0% | 待开始统计 |
| 知识库更新 | 0 | 0 | 0% | 待开始统计 |
| 架构变更管理 | 0 | 0 | 0% | 待开始统计 |

### 改进建议
- [ ] 待收集使用反馈
- [ ] 待分析执行问题
- [ ] 待优化规则内容
```

## 🔄 规则演进计划

### 第一阶段 (当前)
- 建立6条核心规则
- 创建基础模板和检查清单
- 开始执行和统计

### 第二阶段 (2周后)
- 根据执行情况优化现有规则
- 增加1-2条新规则
- 完善自动化检查

### 第三阶段 (1个月后)
- 建立完整的标准体系
- 增加前端开发标准
- 建立跨端协作规范

## ⚠️ 重要提醒

**这6条规则是底线，必须100%执行！**
- 任何情况下都不能跳过需求确认
- 任何代码都必须基于标准模板
- 任何功能都必须有基础测试
- 任何提交都必须通过检查清单
- 任何合并都必须更新知识库
- 任何架构变更都必须遵循ADR流程

**违反规则的后果**:
- 立即停止开发，回到规则要求的步骤
- 分析违反原因，更新规则或流程
- 记录到问题库，避免再次发生
