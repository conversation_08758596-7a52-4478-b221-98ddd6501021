# AI助手强制提醒机制

## 🎯 提醒触发词

### 当我忘记执行规则时，您可以使用以下关键词提醒我：

### 1. 开发启动提醒
**触发词**: `"执行MVS规则"` 或 `"按标准开发"`
**我的响应**: 立即停止当前操作，执行以下步骤：
1. 查阅 `docs/knowledge/mvs-core-rules.md`
2. 查阅相关的 `docs/knowledge/decisions/` 文件
3. 使用 `docs/knowledge/templates/requirements-template.md` 进行需求确认
4. 确认完成后才开始编码

### 2. 需求确认提醒
**触发词**: `"需求确认"` 或 `"先确认需求"`
**我的响应**: 立即执行结构化需求确认对话：
1. 功能概述确认
2. 功能边界确认  
3. 数据设计确认
4. 接口设计确认
5. 反向验证
6. 获得最终确认

### 3. 代码模式提醒
**触发词**: `"使用标准模式"` 或 `"查阅代码模式"`
**我的响应**: 立即查阅并应用标准模式：
1. 查阅 `docs/knowledge/patterns/repository-patterns.md`
2. 查阅 `docs/knowledge/patterns/service-patterns.md`
3. 查阅 `docs/knowledge/patterns/handler-patterns.md`
4. 严格按照模板开发

### 4. 问题解决提醒
**触发词**: `"查问题手册"` 或 `"先查知识库"`
**我的响应**: 立即查阅问题解决手册：
1. 查阅 `docs/knowledge/problems/` 相关文件
2. 寻找类似问题的解决方案
3. 如果没找到，解决后立即记录

### 5. 检查清单提醒
**触发词**: `"执行检查清单"` 或 `"提交前检查"`
**我的响应**: 立即执行完整检查：
1. 运行 `docs/knowledge/templates/checklists/pre-commit-checklist.md`
2. 逐项确认所有检查点
3. 确保100%通过才能继续

### 6. 知识库更新提醒
**触发词**: `"更新知识库"` 或 `"记录决策"`
**我的响应**: 立即更新相关知识库：
1. 记录重要决策到 `docs/knowledge/decisions/`
2. 记录问题解决方案到 `docs/knowledge/problems/`
3. 更新代码模式到 `docs/knowledge/patterns/`

---

## 🔄 自动提醒机制

### 我应该在以下情况下自动提醒自己：

### 1. 收到开发任务时
**自动执行**:
```
🤖 AI助手自检：收到开发任务，执行MVS规则
1. ✅ 查阅知识库
2. ⏳ 开始需求确认对话
3. ⏸️  等待用户确认后再编码
```

### 2. 开始编码前
**自动执行**:
```
🤖 AI助手自检：准备编码，检查标准模式
1. ✅ 查阅相关代码模式
2. ✅ 复制标准模板
3. ✅ 确认开发规范
```

### 3. 遇到问题时
**自动执行**:
```
🤖 AI助手自检：遇到问题，查阅解决手册
1. ✅ 查阅 docs/knowledge/problems/
2. ⏳ 寻找解决方案
3. 📝 准备记录新问题
```

### 4. 完成功能后
**自动执行**:
```
🤖 AI助手自检：功能完成，执行检查清单
1. ✅ 运行提交前检查
2. ✅ 更新知识库
3. ✅ 记录重要决策
```

---

## 📋 紧急重置指令

### 当我完全偏离标准时，您可以使用：

### 🚨 完全重置指令
**触发词**: `"重置AI标准"` 或 `"回到MVS规则"`
**我的响应**: 
1. 立即停止所有操作
2. 重新阅读 `docs/knowledge/mvs-core-rules.md`
3. 重新阅读 `docs/knowledge/README.md`
4. 从需求确认开始重新执行
5. 向您确认理解后再继续

### 🔧 部分纠正指令
**触发词**: `"检查第X条规则"` 
**我的响应**:
1. 立即查阅MVS第X条规则
2. 检查当前操作是否符合
3. 如不符合，立即纠正
4. 继续执行标准流程

---

## 💡 预防性提醒

### 您可以在对话开始时设置提醒：

### 1. 开发会话开始时
**您说**: `"今天按MVS标准开发，记得执行所有规则"`
**我回复**: `"✅ 收到！我将严格执行MVS 5条核心规则，每个步骤都会确认。"`

### 2. 复杂功能开发时
**您说**: `"这是复杂功能，务必严格按标准执行"`
**我回复**: `"✅ 明白！我会特别注意需求确认和代码模式，每个决策都会记录。"`

### 3. 发现我偏离时
**您说**: `"你偏离了标准，重新来"`
**我回复**: `"❌ 抱歉！我立即重新执行标准流程，从需求确认开始。"`

---

## 🎯 标准执行检查表

### 我应该在每个阶段自问：

### 开发前
- [ ] 我是否查阅了相关知识库？
- [ ] 我是否完成了需求确认对话？
- [ ] 我是否获得了用户的明确确认？

### 开发中  
- [ ] 我是否使用了标准代码模式？
- [ ] 我是否遇到问题先查阅了解决手册？
- [ ] 我是否记录了重要决策？

### 开发后
- [ ] 我是否执行了完整的检查清单？
- [ ] 我是否更新了知识库？
- [ ] 我是否确认所有规则都已遵守？

---

## 📞 紧急联系方式

### 如果我持续忘记规则：

1. **立即使用**: `"执行MVS规则"` 强制重置
2. **检查原因**: 是否需要更新提醒机制
3. **改进措施**: 更新自动提醒逻辑
4. **记录问题**: 在知识库中记录遗忘原因

---

## 🔄 提醒机制优化

### 根据使用情况持续改进：

1. **收集遗忘模式**: 什么情况下最容易忘记
2. **优化触发词**: 让提醒更简单有效
3. **增强自动检查**: 减少人工提醒需求
4. **完善知识库**: 让查阅更快速准确

### 提醒效果统计
```markdown
| 提醒类型 | 使用次数 | 有效次数 | 有效率 | 改进建议 |
|----------|----------|----------|--------|----------|
| 需求确认提醒 | 0 | 0 | 0% | 待统计 |
| 代码模式提醒 | 0 | 0 | 0% | 待统计 |
| 检查清单提醒 | 0 | 0 | 0% | 待统计 |
```

## ⚡ 快速提醒卡片

**保存这些关键词，随时提醒我**：
- `执行MVS规则` - 完全重置到标准流程
- `需求确认` - 强制执行需求确认对话  
- `使用标准模式` - 查阅并应用代码模式
- `查问题手册` - 先查知识库再解决问题
- `执行检查清单` - 运行完整检查流程
- `更新知识库` - 记录决策和经验
- `重置AI标准` - 紧急重置指令
