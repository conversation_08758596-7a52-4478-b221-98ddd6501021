# 🧠 知识库 - 通用Go API框架核心资产

## 📋 概述
**本目录是通用Go API框架的核心知识库**，包含了所有开发经验、规则、模板和最佳实践。

## 🎯 核心价值
- **开发质量保证**：MVS规则确保代码质量
- **协作效率提升**：标准化的协作流程和场景模式
- **决策可追溯**：完整的架构决策记录
- **经验可复用**：模板化的最佳实践
- **持续改进**：系统化的改进跟踪机制

## 📁 当前目录结构

### 🎯 核心规则（必备）⭐
- `mvs-core-rules.md` - **MVS核心规则（6条）**
  - 开发质量标准的基石
  - 包含代码规范、测试要求、文档标准等
  - 所有项目必须严格遵守

### 🤖 协作指南（必备）⭐
- `ai-collaboration-guide.md` - **AI助手协作指南**
  - AI助手的操作清单和观察点
  - 提醒机制和协作流程
  - 快速上下文恢复机制

- `collaboration-scenarios.md` - **场景化协作模式**
  - 6种不同场景的协作策略
  - 需求分析、产品设计、数据库设计等
  - 自动场景识别和切换机制

### 📋 决策记录（必备）⭐
- `decisions/` - **架构决策记录（ADR）目录**
  - `adr-001-intelligent-collaboration-workflow.md` - 智能协作流程
  - `adr-002-project-development-workflow.md` - 项目开发流程
  - `api-design-decisions.md` - API设计相关决策
  - 所有重要技术决策的完整记录

### 📝 模板库（必备）⭐
- `templates/` - **各种文档和代码模板**
  - `adr-template.md` - ADR模板
  - `requirements-template.md` - 需求文档模板
  - `checklists/pre-commit-checklist.md` - 提交前检查清单
  - 标准化的文档格式和结构

### 📊 改进跟踪（必备）⭐
- `improvement-backlog.md` - **改进事项跟踪清单**
  - 持续改进的事项管理
  - 改进效果统计和分析
  - 协作流程优化记录

### 🔧 设计模式（可选）
- `patterns/` - **设计模式和最佳实践**
  - `repository-patterns.md` - 仓储模式实践
  - 经过验证的代码模式和架构实践

### 🐛 问题解决（可选）
- `problems/` - **问题记录和解决方案**
  - `compilation-errors.md` - 编译错误解决方案
  - 常见问题的解决方案库

## 🚀 项目开发流程

### 1. AI助手启动检查清单
- [ ] 读取 `mvs-core-rules.md` 理解开发标准
- [ ] 读取 `ai-collaboration-guide.md` 了解协作方式
- [ ] 读取 `collaboration-scenarios.md` 掌握场景模式
- [ ] 查阅 `decisions/` 了解重要决策
- [ ] 熟悉 `templates/` 中的各种模板

### 2. 开发过程中
- **严格遵守**：MVS核心规则
- **主动应用**：场景化协作模式
- **及时记录**：重要决策通过ADR流程
- **使用模板**：提高文档和代码质量
- **持续改进**：记录问题和改进机会

## 🎯 质量标准

### 文档质量
- **完整性**：覆盖所有重要方面
- **准确性**：信息准确无误
- **时效性**：及时更新维护
- **可用性**：结构清晰易用

### 复用性
- **标准化**：统一的格式和结构
- **模板化**：可直接复制使用
- **参数化**：支持项目特定配置

## 📈 持续进化

### 改进来源
- **项目实践**：开发过程中的经验总结
- **问题解决**：遇到问题后的改进措施
- **协作优化**：协作过程中的流程改进
- **技术演进**：新技术和最佳实践的引入

---

**使用说明**：
1. AI助手按照协作指南快速恢复上下文
2. 开发过程中严格遵守MVS规则
3. 重要决策通过ADR流程记录
4. 持续更新和改进知识库内容
