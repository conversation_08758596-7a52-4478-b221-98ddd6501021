# 需求确认模板

## 🎯 使用说明
每次开发新功能前，AI助手必须使用此模板进行结构化需求确认对话。

---

## 📋 需求确认对话流程

### 第一步：功能概述确认
**AI助手问题**:
```
我理解您要开发 [功能名称] 功能，让我确认几个关键点：

1. 这个功能主要解决什么问题？
2. 哪些用户会使用这个功能？
3. 用户在什么场景下会使用这个功能？
4. 这个功能的核心价值是什么？
```

**确认要点**:
- [ ] 问题定义清晰
- [ ] 用户角色明确
- [ ] 使用场景具体
- [ ] 价值主张明确

### 第二步：功能边界确认
**AI助手问题**:
```
现在让我确认功能的具体边界：

1. 这个功能包含哪些具体的操作？
2. 这个功能明确不包含哪些内容？
3. 有什么约束条件需要考虑？（性能、安全、兼容性等）
4. 这个功能依赖哪些其他功能或数据？
```

**确认要点**:
- [ ] 包含功能列表清晰
- [ ] 排除功能明确说明
- [ ] 约束条件具体
- [ ] 依赖关系明确

### 第三步：数据设计确认
**AI助手问题**:
```
让我确认数据设计相关的问题：

1. 这个功能需要哪些核心数据实体？
2. 这些实体之间是什么关系？
3. 每个实体需要哪些关键字段？
4. 这些数据如何支持功能的查询需求？
```

**确认要点**:
- [ ] 核心实体识别正确
- [ ] 实体关系设计合理
- [ ] 字段设计完整
- [ ] 查询场景覆盖

### 第四步：接口设计确认
**AI助手问题**:
```
最后确认接口设计：

1. 这个功能需要哪些API端点？
2. 每个端点的输入输出是什么？
3. 需要什么权限验证？
4. 有什么特殊的业务规则？
```

**确认要点**:
- [ ] API端点设计合理
- [ ] 输入输出格式明确
- [ ] 权限控制清晰
- [ ] 业务规则完整

---

## 📝 需求确认文档模板

### 功能需求确认文档
```markdown
# 功能需求确认: [功能名称]

## 1. 功能概述
**功能名称**: 
**解决问题**: 
**目标用户**: 
**使用场景**: 
**核心价值**: 

## 2. 功能边界
### 包含功能
- [ ] 功能1: [具体描述]
- [ ] 功能2: [具体描述]
- [ ] 功能3: [具体描述]

### 不包含功能
- [ ] 排除1: [说明原因]
- [ ] 排除2: [说明原因]

### 约束条件
- [ ] 性能约束: [具体指标]
- [ ] 安全约束: [具体要求]
- [ ] 兼容约束: [具体要求]

### 依赖关系
- [ ] 前置功能: [依赖的功能]
- [ ] 数据依赖: [依赖的数据]
- [ ] 外部依赖: [第三方服务]

## 3. 数据设计
### 核心实体
**实体1: [实体名称]**
- 字段1: [类型] - [说明]
- 字段2: [类型] - [说明]
- 字段3: [类型] - [说明]

**实体2: [实体名称]**
- 字段1: [类型] - [说明]
- 字段2: [类型] - [说明]

### 实体关系
- [实体1] 与 [实体2]: [关系类型] - [关系说明]
- [实体2] 与 [实体3]: [关系类型] - [关系说明]

### 查询场景
- [ ] 场景1: [查询描述] - [支持的实体和字段]
- [ ] 场景2: [查询描述] - [支持的实体和字段]

## 4. 接口设计
### API端点
- **POST /api/v1/[resource]**: [创建资源]
  - 输入: [JSON格式]
  - 输出: [JSON格式]
  - 权限: [权限要求]

- **GET /api/v1/[resource]**: [获取资源列表]
  - 输入: [查询参数]
  - 输出: [JSON格式]
  - 权限: [权限要求]

- **GET /api/v1/[resource]/:id**: [获取资源详情]
  - 输入: [路径参数]
  - 输出: [JSON格式]
  - 权限: [权限要求]

### 业务规则
- [ ] 规则1: [具体规则描述]
- [ ] 规则2: [具体规则描述]
- [ ] 规则3: [具体规则描述]

## 5. 验收标准
### 功能验收
- [ ] 所有包含功能正常工作
- [ ] 边界条件处理正确
- [ ] 错误情况处理合理

### 数据验收
- [ ] 数据创建正确
- [ ] 数据查询准确
- [ ] 数据更新有效
- [ ] 数据删除安全

### 接口验收
- [ ] API响应格式正确
- [ ] 权限验证有效
- [ ] 错误处理完善
- [ ] 性能满足要求

## 6. 确认签字
**需求提出方确认**: [ ] 已确认
**开发方确认**: [ ] 已理解
**确认时间**: [YYYY-MM-DD HH:MM]
```

---

## 🔍 反向验证模板

### 用例验证
**AI助手验证**:
```
基于您的需求，我设计了以下用例，请确认是否正确：

**用例1**: [用例名称]
- 前置条件: [条件描述]
- 操作步骤: 
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- 预期结果: [结果描述]

这个用例是否符合您的期望？
```

### 边界探测
**AI助手验证**:
```
让我确认几个边界情况的处理：

1. 如果用户输入无效数据，系统应该如何处理？
2. 如果系统负载很高，这个功能如何保证性能？
3. 如果用户没有权限，应该返回什么错误？
4. 如果依赖的服务不可用，功能如何降级？

请确认这些边界情况的处理方式。
```

### 数据流验证
**AI助手验证**:
```
让我确认数据流是否正确：

数据输入 → [处理步骤1] → [处理步骤2] → 数据输出

1. 输入数据: [格式和来源]
2. 处理逻辑: [业务规则]
3. 输出数据: [格式和去向]

这个数据流是否符合业务逻辑？
```

---

## ✅ 确认完成检查清单

### 需求确认完成标准
- [ ] 功能概述得到用户明确确认
- [ ] 功能边界清晰定义并确认
- [ ] 数据设计通过验证
- [ ] 接口设计得到认可
- [ ] 用例验证通过
- [ ] 边界情况处理明确
- [ ] 验收标准定义清楚
- [ ] 需求确认文档完整

### AI助手执行检查
- [ ] 已完成结构化对话
- [ ] 已记录所有确认内容
- [ ] 已进行反向验证
- [ ] 已获得用户最终确认
- [ ] 已保存需求确认文档

**只有通过所有检查项，才能开始编码开发！**
