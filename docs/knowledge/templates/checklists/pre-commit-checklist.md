# 提交前检查清单

## 🎯 使用说明
每次代码提交前，必须完成此检查清单的所有项目。AI助手和开发者都要严格执行。

---

## 📋 代码质量检查

### 1. 编译检查
- [ ] 代码能够成功编译 (`go build ./...`)
- [ ] 没有语法错误
- [ ] 没有类型错误
- [ ] 所有依赖都能正确解析

**检查命令**:
```bash
cd api
go build ./cmd/api-server
go build ./cmd/admin-server
```

### 2. 代码格式检查
- [ ] 代码已格式化 (`go fmt ./...`)
- [ ] 代码通过静态检查 (`go vet ./...`)
- [ ] 导入语句整理正确
- [ ] 代码风格一致

**检查命令**:
```bash
go fmt ./...
go vet ./...
goimports -w .
```

### 3. 代码规范检查
- [ ] 变量命名符合Go规范
- [ ] 函数命名清晰明确
- [ ] 注释完整准确
- [ ] 错误处理正确

**检查要点**:
```go
// ✅ 正确的命名和注释
// CreateTask 创建新任务
func (s *taskService) CreateTask(req *models.TaskCreateRequest, userID int) (*models.TaskResponse, error) {
    if req == nil {
        return nil, errors.NewBusinessError(errors.ErrCodeInvalidParam, "请求参数不能为空")
    }
    // ... 业务逻辑
}

// ❌ 错误的命名和注释
func (s *taskService) create(r *models.TaskCreateRequest, u int) (*models.TaskResponse, error) {
    // ... 没有注释的业务逻辑
}
```

---

## 🧪 测试检查

### 4. 单元测试检查
- [ ] 新增代码有对应的单元测试
- [ ] 所有单元测试通过 (`go test ./...`)
- [ ] 测试覆盖率达到要求 (>80%)
- [ ] 测试用例覆盖主要场景

**检查命令**:
```bash
go test ./... -v
go test ./... -cover
go test ./... -race
```

### 5. 集成测试检查
- [ ] 相关的集成测试通过
- [ ] 数据库操作测试正常
- [ ] API接口测试通过
- [ ] 错误处理测试完整

**检查要点**:
- Repository层测试覆盖CRUD操作
- Service层测试覆盖业务逻辑
- Handler层测试覆盖HTTP接口
- 错误场景测试完整

---

## 📊 功能检查

### 6. 功能完整性检查
- [ ] 所有需求功能都已实现
- [ ] 功能边界处理正确
- [ ] 业务规则实现准确
- [ ] 数据验证完整

### 7. 错误处理检查
- [ ] 所有错误都有适当处理
- [ ] 错误信息对用户友好
- [ ] 错误码使用统一标准
- [ ] 系统错误有详细日志

**错误处理模式检查**:
```go
// ✅ 正确的错误处理
if task == nil {
    return nil, errors.NewBusinessError(errors.ErrCodeTaskNotFound, "任务不存在")
}

// ❌ 错误的错误处理
if task == nil {
    return nil, fmt.Errorf("task not found")
}
```

### 8. 安全检查
- [ ] 输入参数验证完整
- [ ] SQL注入防护到位
- [ ] 权限验证正确
- [ ] 敏感信息保护

---

## 📝 文档检查

### 9. 代码文档检查
- [ ] 公共函数有完整注释
- [ ] 复杂逻辑有说明注释
- [ ] 接口文档更新
- [ ] README文件更新

### 10. 知识库更新检查
- [ ] 重要决策记录到 `docs/knowledge/decisions/`
- [ ] 遇到的问题记录到 `docs/knowledge/problems/`
- [ ] 新的代码模式记录到 `docs/knowledge/patterns/`
- [ ] 相关模板更新

---

## 🔧 技术检查

### 11. 性能检查
- [ ] 数据库查询优化
- [ ] 没有明显的性能问题
- [ ] 内存使用合理
- [ ] 并发安全考虑

### 12. 兼容性检查
- [ ] 向后兼容性保持
- [ ] API接口版本正确
- [ ] 数据库迁移安全
- [ ] 配置文件兼容

---

## 📋 提交信息检查

### 13. Git提交信息检查
- [ ] 提交信息格式规范
- [ ] 提交信息描述清晰
- [ ] 相关issue引用正确
- [ ] 提交粒度合适

**提交信息格式**:
```
feat: 添加任务管理功能

- 实现任务创建、查询、更新、删除功能
- 添加任务状态管理
- 完善错误处理和参数验证
- 添加完整的单元测试

Closes #123
```

---

## ✅ 最终确认

### 14. 整体检查
- [ ] 所有MVS核心规则都已遵守
- [ ] 代码质量达到标准
- [ ] 功能测试通过
- [ ] 文档更新完整
- [ ] 知识库同步更新

### 15. 合并准备
- [ ] 分支代码最新
- [ ] 冲突已解决
- [ ] 准备合并到develop分支
- [ ] 合并信息准备完整

---

## 🚨 检查失败处理

### 如果检查失败
1. **立即停止提交流程**
2. **记录失败原因**
3. **修复问题后重新检查**
4. **更新相关文档和知识库**

### 常见失败原因
- 编译错误 → 查阅 `docs/knowledge/problems/compilation-errors.md`
- 测试失败 → 查阅 `docs/knowledge/problems/testing-issues.md`
- 代码规范 → 查阅 `docs/knowledge/patterns/`
- 功能缺失 → 回到需求确认阶段

---

## 📊 检查统计

### 检查执行记录
```markdown
## 检查执行统计 - [日期]

| 检查项 | 通过 | 失败 | 通过率 | 常见问题 |
|--------|------|------|--------|----------|
| 编译检查 | 0 | 0 | 0% | 待统计 |
| 格式检查 | 0 | 0 | 0% | 待统计 |
| 测试检查 | 0 | 0 | 0% | 待统计 |
| 文档检查 | 0 | 0 | 0% | 待统计 |

### 改进建议
- [ ] 待收集执行反馈
- [ ] 待分析失败模式
- [ ] 待优化检查流程
```

## 🎯 自动化脚本

### 一键检查脚本
```bash
#!/bin/bash
# pre-commit-check.sh

echo "🔍 开始执行提交前检查..."

# 1. 编译检查
echo "📋 1. 编译检查..."
cd api && go build ./cmd/api-server
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 2. 格式检查
echo "📋 2. 格式检查..."
go fmt ./...
go vet ./...

# 3. 测试检查
echo "📋 3. 测试检查..."
go test ./... -short
if [ $? -ne 0 ]; then
    echo "❌ 测试失败"
    exit 1
fi

# 4. 覆盖率检查
echo "📋 4. 覆盖率检查..."
go test ./... -cover | grep -E "coverage: [0-9]+\.[0-9]+%" | awk '{if($2 < 80) exit 1}'
if [ $? -ne 0 ]; then
    echo "⚠️  测试覆盖率不足80%"
fi

echo "✅ 所有检查通过，可以提交代码"
```

**使用方法**: 每次提交前运行 `./scripts/pre-commit-check.sh`
