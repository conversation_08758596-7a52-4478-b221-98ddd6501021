# ADR-XXX: [架构变更标题]

## 状态
- [ ] 提议中
- [ ] 已接受
- [ ] 已拒绝
- [ ] 已废弃

## 决策者
- 开发者：[你的名字]
- AI助手：Augment Agent
- 日期：[YYYY-MM-DD]

## 背景和问题
### 当前状况
描述当前的架构状态和遇到的问题...

### 需要解决的问题
- 问题1：...
- 问题2：...
- 问题3：...

### 触发因素
什么促使了这次架构变更的需求？

## 决策
### 选择的方案
详细描述选择的解决方案...

### 考虑的替代方案
1. **方案A**：...
   - 优点：...
   - 缺点：...
   
2. **方案B**：...
   - 优点：...
   - 缺点：...

### 选择理由
为什么选择当前方案而不是其他方案？

## 影响分析
### 受影响的组件
- [ ] 代码模板（`docs/knowledge/patterns/`）
- [ ] 共享服务（`internal/services/shared/`）
- [ ] 中间件（`internal/middleware/`）
- [ ] 数据库结构
- [ ] API接口
- [ ] 配置文件
- [ ] 部署脚本
- [ ] 文档

### 具体影响范围
| 组件 | 影响类型 | 影响程度 | 需要的操作 |
|------|----------|----------|------------|
| Service模板 | 修改 | 高 | 更新模板，重新生成代码 |
| 现有Service | 重构 | 中 | 批量更新现有代码 |
| 测试代码 | 修改 | 低 | 更新测试用例 |

### 迁移计划
1. **阶段1**：更新模板和文档
   - [ ] 更新代码模板
   - [ ] 更新生成器脚本
   - [ ] 更新文档

2. **阶段2**：验证新模板
   - [ ] 生成测试模块验证
   - [ ] 运行测试确保正常工作
   - [ ] 修复发现的问题

3. **阶段3**：迁移现有代码
   - [ ] 批量更新现有模块
   - [ ] 逐个验证更新结果
   - [ ] 运行完整测试套件

## 实施细节
### 技术实现
具体的技术实现方案...

### 代码变更
需要修改的具体文件和代码片段...

### 配置变更
需要修改的配置项...

## 风险和缓解措施
### 主要风险
1. **风险1**：现有代码可能不兼容
   - **缓解措施**：分阶段迁移，保持向后兼容
   
2. **风险2**：性能可能受影响
   - **缓解措施**：性能测试验证
   
3. **风险3**：开发效率可能暂时下降
   - **缓解措施**：提供详细的迁移指南

### 回滚计划
如果变更失败，如何回滚到之前的状态？

## 验收标准
### 功能验收
- [ ] 所有现有功能正常工作
- [ ] 新功能按预期工作
- [ ] 性能没有明显下降

### 质量验收
- [ ] 代码编译通过
- [ ] 所有测试通过
- [ ] 代码质量检查通过
- [ ] 文档更新完成

### 开发体验验收
- [ ] 新的开发流程更高效
- [ ] 代码生成器正常工作
- [ ] 开发者能够快速上手

## 后续行动
### 立即行动
- [ ] 更新相关文档
- [ ] 通知相关人员
- [ ] 开始实施计划

### 后续监控
- [ ] 监控性能指标
- [ ] 收集开发者反馈
- [ ] 持续优化改进

## 相关资源
### 参考文档
- [相关ADR链接]
- [技术文档链接]
- [最佳实践指南]

### 相关代码
- [关键代码文件路径]
- [测试代码路径]
- [配置文件路径]

## 备注
其他需要记录的信息...

---

**模板使用说明**：
1. 复制此模板到 `docs/knowledge/decisions/` 目录
2. 将文件名改为 `adr-001-具体变更名称.md`
3. 填写所有相关信息
4. 在开始实施前获得确认
5. 实施完成后更新状态为"已接受"
