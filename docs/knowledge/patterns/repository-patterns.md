# Repository层标准模式

## 🎯 Repository层设计原则
- 负责数据访问逻辑，不包含业务逻辑
- 提供统一的数据操作接口
- 隔离数据库实现细节
- 支持单元测试和Mock

---

## 📋 标准接口模式

### 基础CRUD接口模板
```go
// 接口定义模板
type [Entity]Repository interface {
    // 创建
    Create(entity *models.[Entity]) error
    
    // 查询
    GetByID(id int) (*models.[Entity], error)
    GetList(req *models.[Entity]ListRequest) ([]*models.[Entity], int, error)
    
    // 更新
    Update(entity *models.[Entity]) error
    
    // 删除
    Delete(id int) error
    
    // 业务特定查询方法
    GetByUserID(userID int) ([]*models.[Entity], error)
    GetByStatus(status string) ([]*models.[Entity], error)
}
```

### 具体实现示例 - Task Repository
```go
package repository

import (
    "database/sql"
    "grew_up/api/internal/models"
    "grew_up/api/pkg/database"
)

// 接口定义
type TaskRepository interface {
    Create(task *models.Task) error
    GetByID(id int) (*models.Task, error)
    GetList(req *models.TaskListRequest) ([]*models.Task, int, error)
    Update(task *models.Task) error
    Delete(id int) error
    GetByUserID(userID int) ([]*models.Task, error)
    GetByChildID(childID int) ([]*models.Task, error)
}

// 实现结构体
type taskRepository struct {
    db *sql.DB
}

// 构造函数
func NewTaskRepository(db *sql.DB) TaskRepository {
    return &taskRepository{db: db}
}

// Create 创建任务
func (r *taskRepository) Create(task *models.Task) error {
    query := `
        INSERT INTO tasks (user_id, child_id, title, description, task_type, target_count, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    result, err := r.db.Exec(query,
        task.UserID, task.ChildID, task.Title, task.Description,
        task.TaskType, task.TargetCount, task.Status,
        task.CreatedAt, task.UpdatedAt,
    )
    if err != nil {
        return err
    }
    
    id, err := result.LastInsertId()
    if err != nil {
        return err
    }
    task.ID = int(id)
    return nil
}

// GetByID 根据ID获取任务
func (r *taskRepository) GetByID(id int) (*models.Task, error) {
    query := `
        SELECT id, user_id, child_id, title, description, task_type, 
               target_count, current_count, status, created_at, updated_at
        FROM tasks WHERE id = ?
    `
    task := &models.Task{}
    err := r.db.QueryRow(query, id).Scan(
        &task.ID, &task.UserID, &task.ChildID, &task.Title, &task.Description,
        &task.TaskType, &task.TargetCount, &task.CurrentCount, &task.Status,
        &task.CreatedAt, &task.UpdatedAt,
    )
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, nil
        }
        return nil, err
    }
    return task, nil
}

// GetList 获取任务列表
func (r *taskRepository) GetList(req *models.TaskListRequest) ([]*models.Task, int, error) {
    // 构建查询条件
    whereClause := "WHERE 1=1"
    args := []interface{}{}
    
    if req.UserID > 0 {
        whereClause += " AND user_id = ?"
        args = append(args, req.UserID)
    }
    if req.ChildID > 0 {
        whereClause += " AND child_id = ?"
        args = append(args, req.ChildID)
    }
    if req.Status != "" {
        whereClause += " AND status = ?"
        args = append(args, req.Status)
    }
    
    // 查询总数
    countQuery := "SELECT COUNT(*) FROM tasks " + whereClause
    var total int
    err := r.db.QueryRow(countQuery, args...).Scan(&total)
    if err != nil {
        return nil, 0, err
    }
    
    // 查询数据
    query := `
        SELECT id, user_id, child_id, title, description, task_type,
               target_count, current_count, status, created_at, updated_at
        FROM tasks ` + whereClause + `
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    `
    args = append(args, req.PageSize, (req.Page-1)*req.PageSize)
    
    rows, err := r.db.Query(query, args...)
    if err != nil {
        return nil, 0, err
    }
    defer rows.Close()
    
    var tasks []*models.Task
    for rows.Next() {
        task := &models.Task{}
        err := rows.Scan(
            &task.ID, &task.UserID, &task.ChildID, &task.Title, &task.Description,
            &task.TaskType, &task.TargetCount, &task.CurrentCount, &task.Status,
            &task.CreatedAt, &task.UpdatedAt,
        )
        if err != nil {
            return nil, 0, err
        }
        tasks = append(tasks, task)
    }
    
    return tasks, total, nil
}

// Update 更新任务
func (r *taskRepository) Update(task *models.Task) error {
    query := `
        UPDATE tasks 
        SET title = ?, description = ?, task_type = ?, target_count = ?, 
            current_count = ?, status = ?, updated_at = ?
        WHERE id = ?
    `
    _, err := r.db.Exec(query,
        task.Title, task.Description, task.TaskType, task.TargetCount,
        task.CurrentCount, task.Status, task.UpdatedAt, task.ID,
    )
    return err
}

// Delete 删除任务
func (r *taskRepository) Delete(id int) error {
    query := "DELETE FROM tasks WHERE id = ?"
    _, err := r.db.Exec(query, id)
    return err
}

// GetByUserID 根据用户ID获取任务
func (r *taskRepository) GetByUserID(userID int) ([]*models.Task, error) {
    query := `
        SELECT id, user_id, child_id, title, description, task_type,
               target_count, current_count, status, created_at, updated_at
        FROM tasks WHERE user_id = ?
        ORDER BY created_at DESC
    `
    rows, err := r.db.Query(query, userID)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var tasks []*models.Task
    for rows.Next() {
        task := &models.Task{}
        err := rows.Scan(
            &task.ID, &task.UserID, &task.ChildID, &task.Title, &task.Description,
            &task.TaskType, &task.TargetCount, &task.CurrentCount, &task.Status,
            &task.CreatedAt, &task.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }
        tasks = append(tasks, task)
    }
    
    return tasks, nil
}

// GetByChildID 根据孩子ID获取任务
func (r *taskRepository) GetByChildID(childID int) ([]*models.Task, error) {
    query := `
        SELECT id, user_id, child_id, title, description, task_type,
               target_count, current_count, status, created_at, updated_at
        FROM tasks WHERE child_id = ?
        ORDER BY created_at DESC
    `
    rows, err := r.db.Query(query, childID)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var tasks []*models.Task
    for rows.Next() {
        task := &models.Task{}
        err := rows.Scan(
            &task.ID, &task.UserID, &task.ChildID, &task.Title, &task.Description,
            &task.TaskType, &task.TargetCount, &task.CurrentCount, &task.Status,
            &task.CreatedAt, &task.UpdatedAt,
        )
        if err != nil {
            return nil, err
        }
        tasks = append(tasks, task)
    }
    
    return tasks, nil
}
```

---

## 📋 Repository层最佳实践

### 1. 错误处理模式
```go
// ✅ 正确的错误处理
func (r *taskRepository) GetByID(id int) (*models.Task, error) {
    // ... query logic
    err := r.db.QueryRow(query, id).Scan(...)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, nil  // 没找到返回nil，不是错误
        }
        return nil, err  // 其他错误直接返回
    }
    return task, nil
}

// ❌ 错误的错误处理
func (r *taskRepository) GetByID(id int) (*models.Task, error) {
    // ... query logic
    err := r.db.QueryRow(query, id).Scan(...)
    if err != nil {
        return nil, fmt.Errorf("failed to get task: %v", err)  // 不应该包装数据库错误
    }
    return task, nil
}
```

### 2. 查询构建模式
```go
// ✅ 动态查询构建
func (r *taskRepository) GetList(req *models.TaskListRequest) ([]*models.Task, int, error) {
    whereClause := "WHERE 1=1"
    args := []interface{}{}
    
    if req.UserID > 0 {
        whereClause += " AND user_id = ?"
        args = append(args, req.UserID)
    }
    // ... 其他条件
    
    query := "SELECT ... FROM tasks " + whereClause
    // ... 执行查询
}
```

### 3. 事务处理模式
```go
// ✅ 事务处理模式
func (r *taskRepository) CreateWithProgress(task *models.Task, progress *models.TaskProgress) error {
    tx, err := r.db.Begin()
    if err != nil {
        return err
    }
    defer tx.Rollback()
    
    // 创建任务
    err = r.createTaskInTx(tx, task)
    if err != nil {
        return err
    }
    
    // 创建进度记录
    err = r.createProgressInTx(tx, progress)
    if err != nil {
        return err
    }
    
    return tx.Commit()
}
```

---

## 🧪 Repository层测试模式

### 测试结构模板
```go
package repository

import (
    "testing"
    "grew_up/api/pkg/database"
    "grew_up/api/internal/models"
)

func TestTaskRepository_Create(t *testing.T) {
    // 设置测试数据库
    db := database.SetupTestDB(t)
    defer database.CleanupTestDB(t, db)
    
    repo := NewTaskRepository(db)
    
    // 准备测试数据
    task := &models.Task{
        UserID:      1,
        ChildID:     1,
        Title:       "测试任务",
        Description: "测试描述",
        TaskType:    "jump_rope",
        TargetCount: 100,
        Status:      "active",
    }
    
    // 执行测试
    err := repo.Create(task)
    
    // 验证结果
    if err != nil {
        t.Fatalf("Create failed: %v", err)
    }
    if task.ID == 0 {
        t.Error("Expected task ID to be set")
    }
    
    // 验证数据是否正确保存
    saved, err := repo.GetByID(task.ID)
    if err != nil {
        t.Fatalf("GetByID failed: %v", err)
    }
    if saved.Title != task.Title {
        t.Errorf("Expected title %s, got %s", task.Title, saved.Title)
    }
}
```

## 📊 Repository层检查清单
- [ ] 接口定义清晰，方法命名规范
- [ ] 实现了所有CRUD基础方法
- [ ] 错误处理正确，区分业务错误和系统错误
- [ ] 查询条件构建安全，防止SQL注入
- [ ] 分页查询实现正确
- [ ] 事务处理合理
- [ ] 单元测试覆盖主要方法
- [ ] 代码格式化和注释完整
