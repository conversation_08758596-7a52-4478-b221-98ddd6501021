# 路径管理操作规范

## 🎯 目标
避免因路径错误导致的文件操作失误，确保所有文件和目录操作的准确性。

## 📋 强制性检查清单

### 执行任何文件/目录操作前必须完成：

#### 1. 路径状态确认 (MANDATORY)
```bash
# 第一步：确认当前工作目录
pwd
```

#### 2. 环境信息解读 (MANDATORY)
仔细阅读 `<supervisor>` 信息中的关键内容：
- `The interactive terminal's current working directory is XXX`
- `The user's workspace is opened at XXX`
- `Use the repository root directory to resolve relative paths`

#### 3. 路径规划验证 (MANDATORY)
在执行命令前，心理模拟：
- 当前位置：`/current/path`
- 目标操作：`mkdir -p target/dir`
- 预期结果：`/current/path/target/dir`
- 是否符合预期：✅/❌

#### 4. 命令执行后验证 (RECOMMENDED)
```bash
# 验证操作结果
ls -la
# 或者
find . -name "target_name" -type d
```

## 🚨 高风险操作警告

以下操作必须额外小心：

### 文件/目录移动操作
```bash
# ❌ 错误示例
mv generators api/tools/  # 当前已在api目录下

# ✅ 正确做法
pwd                       # 确认当前位置
mv generators tools/      # 使用正确的相对路径
```

### 目录创建操作
```bash
# ❌ 错误示例
mkdir -p api/tools        # 当前已在api目录下

# ✅ 正确做法
pwd                       # 确认当前位置
mkdir -p tools            # 使用正确的相对路径
```

### 文件编辑操作
```bash
# ❌ 错误示例
str-replace-editor api/pkg/config/config.go  # 当前已在api目录下

# ✅ 正确做法
str-replace-editor pkg/config/config.go      # 使用正确的相对路径
```

## 📖 标准操作模板

### 模板1: 目录创建
```bash
# Step 1: 确认当前位置
pwd

# Step 2: 规划路径
# 当前: /Users/<USER>/data/project/prograrm/api
# 目标: 创建 tools 目录
# 命令: mkdir -p tools
# 结果: /Users/<USER>/data/project/prograrm/api/tools

# Step 3: 执行命令
mkdir -p tools

# Step 4: 验证结果
ls -la tools
```

### 模板2: 文件移动
```bash
# Step 1: 确认当前位置和目标文件位置
pwd
ls -la source_file

# Step 2: 规划移动路径
# 当前: /Users/<USER>/data/project/prograrm/api
# 源文件: ./source_file
# 目标: ./target_dir/source_file

# Step 3: 执行移动
mv source_file target_dir/

# Step 4: 验证结果
ls -la target_dir/source_file
```

### 模板3: 跨目录操作
```bash
# Step 1: 确认当前位置
pwd

# Step 2: 如果需要操作其他目录的文件
# 选项A: 切换目录
cd ../target_dir
pwd  # 再次确认

# 选项B: 使用绝对路径
operation /full/path/to/target

# 选项C: 使用明确的相对路径
operation ../target_dir/file
```

## 🔧 工具和辅助方法

### 路径验证函数概念
```bash
# 伪代码：执行前验证
function verify_path_operation() {
    echo "当前目录: $(pwd)"
    echo "计划操作: $1"
    echo "预期结果: $(pwd)/$2"
    read -p "确认执行? (y/n): " confirm
}
```

### 常用路径检查命令
```bash
# 显示当前完整路径
pwd

# 显示目录结构
tree -L 2

# 查找特定文件/目录
find . -name "target" -type d
find . -name "*.go" -type f

# 显示相对路径关系
realpath --relative-to=. target_path
```

## 📚 常见错误案例

### 案例1: 重复路径
```bash
# 错误：当前在 /project/api，执行
mkdir -p api/tools
# 结果：创建了 /project/api/api/tools

# 正确：
mkdir -p tools
# 结果：创建了 /project/api/tools
```

### 案例2: 路径混淆
```bash
# 错误：混淆工作目录和项目根目录
mv generators api/tools/  # 当前已在api目录

# 正确：明确当前位置
pwd  # 确认在 /project/api
mv generators tools/
```

## 🎯 执行原则

1. **永远先确认位置**：任何文件操作前必须 `pwd`
2. **仔细读取环境信息**：重视 supervisor 提供的路径信息
3. **使用相对路径时要明确**：避免假设当前目录
4. **操作后验证结果**：确保操作达到预期效果
5. **有疑问时使用绝对路径**：避免路径歧义

## 🚀 集成到工作流程

### 在每个文件操作任务开始时：
1. 阅读 supervisor 信息
2. 执行 `pwd` 确认位置
3. 规划操作路径
4. 执行操作
5. 验证结果

### 在复杂操作序列中：
- 每个子步骤都重复上述流程
- 特别注意目录切换后的路径变化
- 使用注释记录路径变化过程

这套规范将帮助避免路径相关的操作错误，确保文件系统操作的准确性和可预测性。
