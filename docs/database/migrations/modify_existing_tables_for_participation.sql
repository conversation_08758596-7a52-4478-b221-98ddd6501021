-- =====================================================
-- 修改现有表结构支持基于训练营的积分和勋章体系
-- 版本: V2.3
-- 策略: 兼容性改进方案 - 既支持全局统计又支持训练营统计
-- =====================================================

-- =====================================================
-- 方案说明
-- =====================================================
-- participation_id = 0: 表示全局统计记录（保持现有逻辑兼容）
-- participation_id > 0: 表示特定训练营的统计记录（新功能）
-- 
-- 这样设计的优点：
-- 1. 向后兼容：现有代码可以继续工作
-- 2. 逐步迁移：可以逐步将业务逻辑迁移到训练营级别
-- 3. 功能完整：同时支持全局和训练营级别的统计
-- 4. 风险较低：数据迁移相对简单

-- =====================================================
-- 修改 child_points 表
-- =====================================================

-- 1. 删除现有的唯一约束
ALTER TABLE child_points DROP INDEX uk_child_id;

-- 2. 添加 participation_id 字段
ALTER TABLE child_points 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，0表示全局统计，>0表示特定训练营统计，关联user_camp_participations.id';

-- 3. 添加新的唯一约束
ALTER TABLE child_points 
ADD UNIQUE KEY uk_child_participation (child_id, participation_id) 
COMMENT '孩子和参与记录的唯一组合，支持全局(0)和训练营级别统计';

-- 4. 添加索引优化查询
ALTER TABLE child_points 
ADD INDEX idx_participation_id (participation_id) 
COMMENT '参与记录查询索引';

ALTER TABLE child_points 
ADD INDEX idx_participation_week_points (participation_id, week_points DESC) 
COMMENT '训练营内周积分排行索引';

ALTER TABLE child_points 
ADD INDEX idx_participation_total_points (participation_id, total_points DESC) 
COMMENT '训练营内总积分排行索引';

-- =====================================================
-- 修改 child_medals 表
-- =====================================================

-- 1. 删除现有的唯一约束
ALTER TABLE child_medals DROP INDEX uk_child_medal;

-- 2. 添加 participation_id 字段
ALTER TABLE child_medals 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，0表示全局勋章，>0表示特定训练营勋章，关联user_camp_participations.id';

-- 3. 添加新的唯一约束
ALTER TABLE child_medals 
ADD UNIQUE KEY uk_child_medal_participation (child_id, medal_id, participation_id) 
COMMENT '孩子、勋章和参与记录的唯一组合，支持全局(0)和训练营级别勋章';

-- 4. 添加索引优化查询
ALTER TABLE child_medals 
ADD INDEX idx_participation_id (participation_id) 
COMMENT '参与记录查询索引';

ALTER TABLE child_medals 
ADD INDEX idx_participation_child_unlocked (participation_id, child_id, is_unlocked) 
COMMENT '训练营内孩子解锁查询索引';

-- =====================================================
-- 数据迁移脚本
-- =====================================================

-- 现有数据的 participation_id 默认为 0，表示全局统计
-- 这样现有代码可以继续工作，查询条件加上 participation_id = 0 即可

-- 为现有的用户训练营参与记录创建对应的积分统计记录
INSERT INTO child_points (
    child_id, 
    participation_id, 
    total_points, 
    week_points,
    month_points,
    total_checkins, 
    continuous_days, 
    max_continuous_days,
    week_rank,
    last_week_rank,
    last_checkin_date,
    last_updated_at,
    created_at,
    updated_at
)
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    COALESCE(SUM(pr.points_change), 0) as total_points,
    0 as week_points, -- 需要根据时间范围重新计算
    0 as month_points, -- 需要根据时间范围重新计算
    COUNT(DISTINCT cr.checkin_date) as total_checkins,
    0 as continuous_days, -- 需要通过复杂逻辑计算
    0 as max_continuous_days, -- 需要通过复杂逻辑计算
    0 as week_rank,
    0 as last_week_rank,
    COALESCE(MAX(cr.checkin_date), '1970-01-01') as last_checkin_date,
    NOW() as last_updated_at,
    NOW() as created_at,
    NOW() as updated_at
FROM user_camp_participations ucp
LEFT JOIN point_records pr ON pr.participation_id = ucp.id AND pr.deleted_at IS NULL
LEFT JOIN checkin_records cr ON cr.participation_id = ucp.id AND cr.deleted_at IS NULL
WHERE ucp.deleted_at IS NULL
  AND NOT EXISTS (
      -- 避免重复插入
      SELECT 1 FROM child_points cp 
      WHERE cp.child_id = ucp.child_id 
        AND cp.participation_id = ucp.id
  )
GROUP BY ucp.id, ucp.child_id;

-- 为现有的勋章记录创建对应的训练营级别记录
-- 策略：为每个孩子的每个训练营参与都创建一份勋章记录
INSERT INTO child_medals (
    child_id,
    participation_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
)
SELECT 
    cm.child_id,
    ucp.id as participation_id,
    cm.medal_id,
    cm.is_unlocked,
    cm.current_progress,
    cm.target_progress,
    cm.unlocked_at,
    cm.points_earned,
    cm.created_at,
    cm.updated_at
FROM child_medals cm
CROSS JOIN user_camp_participations ucp
WHERE cm.deleted_at IS NULL 
  AND ucp.deleted_at IS NULL
  AND cm.child_id = ucp.child_id
  AND cm.participation_id = 0  -- 只处理全局勋章记录
  AND NOT EXISTS (
      -- 避免重复插入
      SELECT 1 FROM child_medals cm2 
      WHERE cm2.child_id = cm.child_id 
        AND cm2.medal_id = cm.medal_id
        AND cm2.participation_id = ucp.id
  );

-- =====================================================
-- 数据一致性检查脚本
-- =====================================================

-- 检查全局积分统计记录
SELECT 
    'Global child_points records' as check_type,
    COUNT(*) as total_records
FROM child_points 
WHERE participation_id = 0 AND deleted_at IS NULL;

-- 检查训练营积分统计记录
SELECT 
    'Camp child_points records' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT child_id) as unique_children,
    COUNT(DISTINCT participation_id) as unique_participations
FROM child_points 
WHERE participation_id > 0 AND deleted_at IS NULL;

-- 检查全局勋章记录
SELECT 
    'Global child_medals records' as check_type,
    COUNT(*) as total_records
FROM child_medals 
WHERE participation_id = 0 AND deleted_at IS NULL;

-- 检查训练营勋章记录
SELECT 
    'Camp child_medals records' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT child_id) as unique_children,
    COUNT(DISTINCT participation_id) as unique_participations
FROM child_medals 
WHERE participation_id > 0 AND deleted_at IS NULL;

-- 检查是否有孤立的记录
SELECT 
    'Orphaned camp child_points' as check_type,
    COUNT(*) as orphaned_count
FROM child_points cp
LEFT JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
WHERE cp.participation_id > 0 
  AND cp.deleted_at IS NULL 
  AND ucp.id IS NULL;

SELECT 
    'Orphaned camp child_medals' as check_type,
    COUNT(*) as orphaned_count
FROM child_medals cm
LEFT JOIN user_camp_participations ucp ON cm.participation_id = ucp.id
WHERE cm.participation_id > 0 
  AND cm.deleted_at IS NULL 
  AND ucp.id IS NULL;

-- =====================================================
-- 性能优化建议
-- =====================================================

-- 如果数据量很大，建议分批执行插入操作
-- 可以使用以下脚本分批处理：

/*
-- 分批插入积分记录示例
SET @batch_size = 1000;
SET @offset = 0;

WHILE (SELECT COUNT(*) FROM user_camp_participations WHERE id > @offset AND deleted_at IS NULL) > 0 DO
    INSERT INTO child_points (child_id, participation_id, total_points, ...)
    SELECT ...
    FROM user_camp_participations ucp
    WHERE ucp.id > @offset AND ucp.deleted_at IS NULL
    LIMIT @batch_size;
    
    SET @offset = @offset + @batch_size;
    
    -- 添加延迟避免锁表
    SELECT SLEEP(0.1);
END WHILE;
*/

-- =====================================================
-- 回滚脚本（紧急情况使用）
-- =====================================================

/*
-- 如果需要回滚到原始状态，可以执行以下脚本：

-- 删除训练营级别的记录
DELETE FROM child_points WHERE participation_id > 0;
DELETE FROM child_medals WHERE participation_id > 0;

-- 恢复原始约束
ALTER TABLE child_points DROP INDEX uk_child_participation;
ALTER TABLE child_points DROP INDEX idx_participation_id;
ALTER TABLE child_points DROP INDEX idx_participation_week_points;
ALTER TABLE child_points DROP INDEX idx_participation_total_points;
ALTER TABLE child_points DROP COLUMN participation_id;
ALTER TABLE child_points ADD UNIQUE KEY uk_child_id (child_id) COMMENT '孩子唯一';

ALTER TABLE child_medals DROP INDEX uk_child_medal_participation;
ALTER TABLE child_medals DROP INDEX idx_participation_id;
ALTER TABLE child_medals DROP INDEX idx_participation_child_unlocked;
ALTER TABLE child_medals DROP COLUMN participation_id;
ALTER TABLE child_medals ADD UNIQUE KEY uk_child_medal (child_id, medal_id) COMMENT '防重复记录';
*/
