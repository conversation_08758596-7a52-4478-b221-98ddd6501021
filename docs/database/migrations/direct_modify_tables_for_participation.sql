-- =====================================================
-- 直接修改现有表结构支持基于训练营的积分和勋章体系
-- 版本: V2.4
-- 策略: 直接修改现有表，完全基于训练营的积分统计和勋章体系
-- 风险等级: 高 - 破坏性变更，需要充分测试和备份
-- =====================================================

-- =====================================================
-- 执行前检查和备份
-- =====================================================

-- 1. 检查当前数据状态
SELECT 
    'Pre-migration check' as check_type,
    (SELECT COUNT(*) FROM child_points WHERE deleted_at IS NULL) as child_points_count,
    (SELECT COUNT(*) FROM child_medals WHERE deleted_at IS NULL) as child_medals_count,
    (SELECT COUNT(*) FROM user_camp_participations WHERE deleted_at IS NULL) as participations_count;

-- 2. 创建备份表（强烈建议在执行前创建）
CREATE TABLE child_points_backup_20250102 AS SELECT * FROM child_points;
CREATE TABLE child_medals_backup_20250102 AS SELECT * FROM child_medals;

-- 记录备份信息（如果有migration_logs表的话）
-- INSERT INTO migration_logs (migration_name, backup_tables, created_at)
-- VALUES ('direct_modify_tables_for_participation', 'child_points_backup_20250102,child_medals_backup_20250102', NOW());

-- =====================================================
-- 第一阶段：表结构修改
-- =====================================================

-- 修改 child_points 表
-- 1. 删除现有唯一约束
ALTER TABLE child_points DROP INDEX uk_child_id;

-- 2. 添加 participation_id 字段
ALTER TABLE child_points 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，关联user_camp_participations.id，每个参与记录对应一条积分统计';

-- 3. 添加新的唯一约束
ALTER TABLE child_points 
ADD UNIQUE KEY uk_participation_id (participation_id) 
COMMENT '参与记录唯一，每个训练营参与记录对应一条积分统计记录';

-- 4. 添加索引优化查询
ALTER TABLE child_points 
ADD INDEX idx_child_id (child_id) 
COMMENT '孩子查询索引';

-- 修改 child_medals 表
-- 1. 删除现有唯一约束
ALTER TABLE child_medals DROP INDEX uk_child_medal;

-- 2. 添加 participation_id 字段
ALTER TABLE child_medals 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，关联user_camp_participations.id';

-- 3. 添加新的唯一约束
ALTER TABLE child_medals 
ADD UNIQUE KEY uk_participation_id_medal (participation_id, medal_id) 
COMMENT '参与记录和勋章的唯一组合，每个训练营参与记录中每个勋章只能有一条记录';

-- 4. 添加索引优化查询
ALTER TABLE child_medals 
ADD INDEX idx_child_id (child_id) 
COMMENT '孩子查询索引',
ADD INDEX idx_participation_id (participation_id) 
COMMENT '参与记录查询索引';

-- =====================================================
-- 第二阶段：数据迁移策略
-- =====================================================

-- 创建临时表存储迁移映射关系
CREATE TEMPORARY TABLE temp_migration_mapping (
    old_child_points_id BIGINT UNSIGNED,
    child_id BIGINT UNSIGNED,
    participation_id BIGINT UNSIGNED,
    camp_id BIGINT UNSIGNED,
    total_points BIGINT UNSIGNED DEFAULT 0,
    week_points INT UNSIGNED DEFAULT 0,
    month_points INT UNSIGNED DEFAULT 0,
    total_checkins INT UNSIGNED DEFAULT 0,
    continuous_days INT UNSIGNED DEFAULT 0,
    max_continuous_days INT UNSIGNED DEFAULT 0,
    last_checkin_date DATE DEFAULT '1970-01-01',
    INDEX idx_child_id (child_id),
    INDEX idx_participation_id (participation_id)
);

-- 生成迁移映射：为每个孩子的每个训练营参与创建积分记录
INSERT INTO temp_migration_mapping (
    child_id, 
    participation_id, 
    camp_id,
    total_points,
    total_checkins,
    last_checkin_date
)
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    ucp.camp_id,
    -- 根据该参与记录的积分变动历史计算总积分
    COALESCE(SUM(CASE WHEN pr.points_change > 0 THEN pr.points_change ELSE 0 END), 0) as total_points,
    -- 根据该参与记录的打卡历史计算总打卡次数
    COUNT(DISTINCT cr.checkin_date) as total_checkins,
    -- 最后打卡日期
    COALESCE(MAX(cr.checkin_date), '1970-01-01') as last_checkin_date
FROM user_camp_participations ucp
LEFT JOIN point_records pr ON pr.participation_id = ucp.id AND pr.deleted_at IS NULL
LEFT JOIN checkin_records cr ON cr.participation_id = ucp.id AND cr.deleted_at IS NULL
WHERE ucp.deleted_at IS NULL
GROUP BY ucp.id, ucp.child_id, ucp.camp_id;

-- 对于没有训练营参与记录的孩子，为他们创建一个默认的参与记录
-- 这里需要根据业务逻辑决定如何处理，暂时跳过

-- =====================================================
-- 第三阶段：执行数据迁移
-- =====================================================

-- 清空现有的 child_points 表数据（高风险操作！）
-- 注意：这会删除所有现有数据，请确保已经备份
DELETE FROM child_points;

-- 重置自增ID
ALTER TABLE child_points AUTO_INCREMENT = 1;

-- 插入新的基于训练营的积分记录
INSERT INTO child_points (
    child_id,
    participation_id,
    total_points,
    week_points,
    month_points,
    total_checkins,
    continuous_days,
    max_continuous_days,
    week_rank,
    last_week_rank,
    last_checkin_date,
    last_updated_at,
    created_at,
    updated_at
)
SELECT 
    tmm.child_id,
    tmm.participation_id,
    tmm.total_points,
    0 as week_points,  -- 需要根据时间范围重新计算
    0 as month_points, -- 需要根据时间范围重新计算
    tmm.total_checkins,
    0 as continuous_days,     -- 需要通过复杂逻辑计算
    0 as max_continuous_days, -- 需要通过复杂逻辑计算
    0 as week_rank,
    0 as last_week_rank,
    tmm.last_checkin_date,
    NOW() as last_updated_at,
    NOW() as created_at,
    NOW() as updated_at
FROM temp_migration_mapping tmm;

-- 处理 child_medals 表的数据迁移
-- 策略：为每个孩子的每个训练营参与都创建一份勋章记录

-- 创建临时表存储勋章迁移数据
CREATE TEMPORARY TABLE temp_medal_migration (
    child_id BIGINT UNSIGNED,
    participation_id BIGINT UNSIGNED,
    medal_id INT UNSIGNED,
    is_unlocked TINYINT UNSIGNED DEFAULT 0,
    current_progress INT UNSIGNED DEFAULT 0,
    target_progress INT UNSIGNED DEFAULT 0,
    unlocked_at TIMESTAMP NULL,
    points_earned INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_child_participation_medal (child_id, participation_id, medal_id)
);

-- 为每个训练营参与记录创建勋章记录
-- 策略1：只为已解锁的勋章创建训练营级别的记录
INSERT INTO temp_medal_migration (
    child_id,
    participation_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
)
SELECT 
    cm.child_id,
    tmm.participation_id,
    cm.medal_id,
    cm.is_unlocked,
    cm.current_progress,
    cm.target_progress,
    cm.unlocked_at,
    cm.points_earned,
    cm.created_at,
    cm.updated_at
FROM child_medals cm
CROSS JOIN temp_migration_mapping tmm
WHERE cm.deleted_at IS NULL 
  AND cm.child_id = tmm.child_id
  AND cm.is_unlocked = 1;  -- 只迁移已解锁的勋章

-- 清空现有的 child_medals 表数据
DELETE FROM child_medals;

-- 重置自增ID
ALTER TABLE child_medals AUTO_INCREMENT = 1;

-- 插入新的基于训练营的勋章记录
INSERT INTO child_medals (
    child_id,
    participation_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
)
SELECT 
    child_id,
    participation_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
FROM temp_medal_migration;

-- =====================================================
-- 第四阶段：数据一致性检查
-- =====================================================

-- 检查迁移后的数据状态
SELECT 
    'Post-migration check' as check_type,
    (SELECT COUNT(*) FROM child_points WHERE deleted_at IS NULL) as child_points_count,
    (SELECT COUNT(*) FROM child_medals WHERE deleted_at IS NULL) as child_medals_count,
    (SELECT COUNT(DISTINCT child_id) FROM child_points WHERE deleted_at IS NULL) as unique_children_in_points,
    (SELECT COUNT(DISTINCT participation_id) FROM child_points WHERE deleted_at IS NULL) as unique_participations_in_points;

-- 检查是否有孤立的记录
SELECT 
    'Orphaned records check' as check_type,
    (SELECT COUNT(*) FROM child_points cp 
     LEFT JOIN user_camp_participations ucp ON cp.participation_id = ucp.id 
     WHERE cp.deleted_at IS NULL AND ucp.id IS NULL) as orphaned_points,
    (SELECT COUNT(*) FROM child_medals cm 
     LEFT JOIN user_camp_participations ucp ON cm.participation_id = ucp.id 
     WHERE cm.deleted_at IS NULL AND ucp.id IS NULL) as orphaned_medals;

-- 检查数据完整性
SELECT 
    'Data integrity check' as check_type,
    (SELECT COUNT(*) FROM child_points WHERE participation_id = 0) as points_with_zero_participation,
    (SELECT COUNT(*) FROM child_medals WHERE participation_id = 0) as medals_with_zero_participation;

-- =====================================================
-- 第五阶段：清理临时数据
-- =====================================================

DROP TEMPORARY TABLE temp_migration_mapping;
DROP TEMPORARY TABLE temp_medal_migration;

-- 记录迁移完成（如果有migration_logs表的话）
-- UPDATE migration_logs
-- SET completed_at = NOW(),
--     status = 'completed',
--     notes = 'Direct modification migration completed successfully'
-- WHERE migration_name = 'direct_modify_tables_for_participation';

-- =====================================================
-- 紧急回滚脚本（仅在出现严重问题时使用）
-- =====================================================

/*
-- 警告：以下回滚脚本会恢复到迁移前的状态，会丢失迁移后的所有数据变更

-- 1. 恢复表结构
ALTER TABLE child_points DROP INDEX uk_participation_id;
ALTER TABLE child_points DROP INDEX idx_child_id;
ALTER TABLE child_points DROP COLUMN participation_id;
ALTER TABLE child_points ADD UNIQUE KEY uk_child_id (child_id) COMMENT '孩子唯一';

ALTER TABLE child_medals DROP INDEX uk_participation_id_medal;
ALTER TABLE child_medals DROP INDEX idx_child_id;
ALTER TABLE child_medals DROP INDEX idx_participation_id;
ALTER TABLE child_medals DROP COLUMN participation_id;
ALTER TABLE child_medals ADD UNIQUE KEY uk_child_medal (child_id, medal_id) COMMENT '防重复记录';

-- 2. 恢复数据
DELETE FROM child_points;
INSERT INTO child_points SELECT * FROM child_points_backup_20250102;

DELETE FROM child_medals;
INSERT INTO child_medals SELECT * FROM child_medals_backup_20250102;

-- 3. 记录回滚（如果有migration_logs表的话）
-- UPDATE migration_logs
-- SET status = 'rolled_back',
--     rollback_at = NOW(),
--     notes = 'Migration rolled back due to issues'
-- WHERE migration_name = 'direct_modify_tables_for_participation';
*/
