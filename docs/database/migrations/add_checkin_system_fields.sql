-- =====================================================
-- 打卡系统业务闭环完善 - 数据库表结构修改
-- 创建时间: 2025-08-02
-- 目的: 完善打卡系统的数据关联性和业务闭环
-- =====================================================

-- 1. 为 user_camp_participations 表添加 last_checkin_date 字段
ALTER TABLE `user_camp_participations` 
ADD COLUMN `last_checkin_date` DATE NULL COMMENT '最后打卡日期，NULL表示未打卡' 
AFTER `makeup_total_count`;

-- 2. 为 family_contracts 表添加 participation_id 字段
ALTER TABLE `family_contracts` 
ADD COLUMN `participation_id` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id' 
AFTER `camp_id`;

-- 3. 为 family_contracts 表添加索引
ALTER TABLE `family_contracts` 
ADD INDEX `idx_participation_id` (`participation_id`) COMMENT '参与记录ID索引';

-- =====================================================
-- 数据迁移说明
-- =====================================================

/*
1. last_checkin_date 字段：
   - 新字段默认为 NULL，表示未打卡
   - 后续通过业务逻辑更新为实际的最后打卡日期

2. participation_id 字段：
   - 新字段默认为 0，需要通过数据迁移脚本更新
   - 可以通过 camp_id 和 child_id 关联到对应的 participation_id

3. 数据一致性：
   - 执行此迁移后，需要运行数据修复脚本
   - 更新现有记录的 last_checkin_date 和 participation_id
*/

-- =====================================================
-- 数据修复脚本（可选执行）
-- =====================================================

-- 更新 user_camp_participations 表的 last_checkin_date
UPDATE `user_camp_participations` ucp
SET `last_checkin_date` = (
    SELECT MAX(DATE(checkin_date))
    FROM `checkin_records` cr
    WHERE cr.participation_id = ucp.id
)
WHERE EXISTS (
    SELECT 1 FROM `checkin_records` cr 
    WHERE cr.participation_id = ucp.id
);

-- 更新 family_contracts 表的 participation_id
UPDATE `family_contracts` fc
SET `participation_id` = (
    SELECT ucp.id
    FROM `user_camp_participations` ucp
    WHERE ucp.camp_id = fc.camp_id 
    AND ucp.child_id = fc.child_id
    LIMIT 1
)
WHERE fc.participation_id = 0
AND EXISTS (
    SELECT 1 FROM `user_camp_participations` ucp
    WHERE ucp.camp_id = fc.camp_id 
    AND ucp.child_id = fc.child_id
);
