-- =====================================================
-- 纯净的直接修改表结构迁移脚本
-- 完全移除向后兼容性设计，彻底的破坏性变更
-- =====================================================

-- 设置安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- =====================================================
-- 第一步：备份现有数据（仅用于紧急回滚）
-- =====================================================

-- 备份 child_points 表
DROP TABLE IF EXISTS child_points_backup_final;
CREATE TABLE child_points_backup_final AS SELECT * FROM child_points;

-- 备份 child_medals 表  
DROP TABLE IF EXISTS child_medals_backup_final;
CREATE TABLE child_medals_backup_final AS SELECT * FROM child_medals;

-- 记录备份信息
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'backup_completed', 'success', NOW());

-- =====================================================
-- 第二步：child_points 表结构彻底改造
-- =====================================================

-- 删除原有的唯一约束
ALTER TABLE child_points DROP INDEX uk_child_id;

-- 添加 participation_id 字段
ALTER TABLE child_points 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
AFTER child_id;

-- 创建新的唯一约束
ALTER TABLE child_points 
ADD UNIQUE KEY uk_participation_id (participation_id);

-- 添加外键约束（确保数据完整性）
ALTER TABLE child_points 
ADD CONSTRAINT fk_child_points_participation 
FOREIGN KEY (participation_id) REFERENCES user_camp_participations(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- 记录进度
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'child_points_structure_modified', 'success', NOW());

-- =====================================================
-- 第三步：child_medals 表结构彻底改造
-- =====================================================

-- 删除原有的唯一约束
ALTER TABLE child_medals DROP INDEX uk_child_medal;

-- 添加 participation_id 字段
ALTER TABLE child_medals 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
AFTER child_id;

-- 创建新的唯一约束
ALTER TABLE child_medals 
ADD UNIQUE KEY uk_participation_id_medal (participation_id, medal_id);

-- 添加外键约束
ALTER TABLE child_medals 
ADD CONSTRAINT fk_child_medals_participation 
FOREIGN KEY (participation_id) REFERENCES user_camp_participations(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- 记录进度
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'child_medals_structure_modified', 'success', NOW());

-- =====================================================
-- 第四步：数据迁移 - 基于训练营参与记录重新分配
-- =====================================================

-- 清空现有数据（因为结构已经改变，原数据无效）
DELETE FROM child_points;
DELETE FROM child_medals;

-- 为每个训练营参与记录创建积分记录
INSERT INTO child_points (
    child_id, 
    participation_id, 
    total_points, 
    week_points, 
    month_points, 
    total_checkins, 
    continuous_days, 
    max_continuous_days,
    week_rank,
    last_week_rank,
    last_checkin_date,
    last_updated_at,
    created_at,
    updated_at
)
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    COALESCE(SUM(pr.points), 0) as total_points,
    COALESCE(SUM(CASE 
        WHEN pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
        THEN pr.points ELSE 0 END), 0) as week_points,
    COALESCE(SUM(CASE 
        WHEN pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
        THEN pr.points ELSE 0 END), 0) as month_points,
    COALESCE(COUNT(DISTINCT DATE(ccr.created_at)), 0) as total_checkins,
    0 as continuous_days, -- 需要重新计算
    0 as max_continuous_days, -- 需要重新计算
    0 as week_rank,
    0 as last_week_rank,
    COALESCE(MAX(ccr.created_at), '1970-01-01') as last_checkin_date,
    NOW() as last_updated_at,
    ucp.created_at,
    NOW() as updated_at
FROM user_camp_participations ucp
LEFT JOIN point_records pr ON pr.child_id = ucp.child_id 
    AND pr.source_type = 'camp' 
    AND pr.source_id = ucp.camp_id
    AND pr.deleted_at IS NULL
LEFT JOIN child_checkin_records ccr ON ccr.child_id = ucp.child_id 
    AND ccr.participation_id = ucp.id
    AND ccr.deleted_at IS NULL
WHERE ucp.deleted_at IS NULL
GROUP BY ucp.id, ucp.child_id, ucp.created_at;

-- 为每个训练营参与记录创建勋章记录（只创建已解锁的勋章）
INSERT INTO child_medals (
    child_id,
    participation_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
)
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    m.id as medal_id,
    0 as is_unlocked, -- 默认未解锁，需要重新检查
    0 as current_progress,
    m.required_value as target_progress,
    NULL as unlocked_at,
    0 as points_earned,
    NOW() as created_at,
    NOW() as updated_at
FROM user_camp_participations ucp
CROSS JOIN medals m
WHERE ucp.deleted_at IS NULL
    AND m.deleted_at IS NULL
    AND m.is_active = 1;

-- 记录数据迁移完成
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'data_migration_completed', 'success', NOW());

-- =====================================================
-- 第五步：重新计算连续打卡天数
-- =====================================================

-- 创建临时存储过程来计算连续天数
DELIMITER //

CREATE PROCEDURE CalculateContinuousDays()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_participation_id BIGINT;
    DECLARE v_continuous_days INT DEFAULT 0;
    DECLARE v_max_continuous_days INT DEFAULT 0;
    
    -- 声明游标
    DECLARE participation_cursor CURSOR FOR 
        SELECT DISTINCT participation_id FROM child_points;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN participation_cursor;
    
    read_loop: LOOP
        FETCH participation_cursor INTO v_participation_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 计算该参与记录的连续打卡天数
        SELECT 
            COALESCE(MAX(consecutive_days), 0),
            COALESCE(MAX(max_consecutive), 0)
        INTO v_continuous_days, v_max_continuous_days
        FROM (
            SELECT 
                participation_id,
                COUNT(*) as consecutive_days,
                MAX(COUNT(*)) OVER() as max_consecutive
            FROM (
                SELECT 
                    participation_id,
                    DATE(created_at) as checkin_date,
                    ROW_NUMBER() OVER (ORDER BY DATE(created_at)) as rn,
                    DATE_SUB(DATE(created_at), INTERVAL ROW_NUMBER() OVER (ORDER BY DATE(created_at)) DAY) as grp
                FROM child_checkin_records 
                WHERE participation_id = v_participation_id 
                    AND deleted_at IS NULL
                GROUP BY participation_id, DATE(created_at)
            ) t
            GROUP BY participation_id, grp
        ) consecutive_groups
        WHERE participation_id = v_participation_id;
        
        -- 更新积分记录
        UPDATE child_points 
        SET 
            continuous_days = v_continuous_days,
            max_continuous_days = v_max_continuous_days
        WHERE participation_id = v_participation_id;
        
    END LOOP;
    
    CLOSE participation_cursor;
END //

DELIMITER ;

-- 执行连续天数计算
CALL CalculateContinuousDays();

-- 删除临时存储过程
DROP PROCEDURE CalculateContinuousDays;

-- 记录连续天数计算完成
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'continuous_days_calculated', 'success', NOW());

-- =====================================================
-- 第六步：数据完整性验证
-- =====================================================

-- 验证 child_points 表
SELECT 
    'child_points_validation' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN participation_id = 0 THEN 1 END) as invalid_participation_records,
    COUNT(CASE WHEN child_id = 0 THEN 1 END) as invalid_child_records
FROM child_points;

-- 验证 child_medals 表
SELECT 
    'child_medals_validation' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN participation_id = 0 THEN 1 END) as invalid_participation_records,
    COUNT(CASE WHEN child_id = 0 THEN 1 END) as invalid_child_records
FROM child_medals;

-- 验证外键关系
SELECT 
    'foreign_key_validation' as validation_type,
    COUNT(*) as orphaned_points_records
FROM child_points cp
LEFT JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
WHERE ucp.id IS NULL;

SELECT 
    'foreign_key_validation' as validation_type,
    COUNT(*) as orphaned_medals_records
FROM child_medals cm
LEFT JOIN user_camp_participations ucp ON cm.participation_id = ucp.id
WHERE ucp.id IS NULL;

-- 记录验证完成
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'validation_completed', 'success', NOW());

-- =====================================================
-- 第七步：创建必要的索引优化性能
-- =====================================================

-- child_points 表索引
ALTER TABLE child_points ADD INDEX idx_child_id (child_id);
ALTER TABLE child_points ADD INDEX idx_total_points (total_points);
ALTER TABLE child_points ADD INDEX idx_week_points (week_points);
ALTER TABLE child_points ADD INDEX idx_last_checkin_date (last_checkin_date);

-- child_medals 表索引
ALTER TABLE child_medals ADD INDEX idx_child_id (child_id);
ALTER TABLE child_medals ADD INDEX idx_medal_id (medal_id);
ALTER TABLE child_medals ADD INDEX idx_is_unlocked (is_unlocked);
ALTER TABLE child_medals ADD INDEX idx_unlocked_at (unlocked_at);

-- 记录索引创建完成
INSERT INTO migration_logs (migration_name, step, status, created_at) 
VALUES ('final_clean_migration', 'indexes_created', 'success', NOW());

-- =====================================================
-- 第八步：最终状态确认
-- =====================================================

-- 恢复安全设置
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- 记录迁移完成
INSERT INTO migration_logs (migration_name, step, status, created_at, notes) 
VALUES ('final_clean_migration', 'migration_completed', 'success', NOW(), 
        'Clean migration completed - all backward compatibility removed');

-- 显示最终统计
SELECT 
    'MIGRATION_SUMMARY' as summary_type,
    (SELECT COUNT(*) FROM child_points) as total_points_records,
    (SELECT COUNT(*) FROM child_medals) as total_medals_records,
    (SELECT COUNT(*) FROM user_camp_participations WHERE deleted_at IS NULL) as total_participations,
    NOW() as completed_at;

-- =====================================================
-- 紧急回滚脚本（仅在严重问题时使用）
-- =====================================================

/*
-- 紧急回滚步骤（请谨慎使用）

-- 1. 停止应用服务
-- 2. 执行以下回滚命令

SET FOREIGN_KEY_CHECKS = 0;

-- 删除新表数据
TRUNCATE TABLE child_points;
TRUNCATE TABLE child_medals;

-- 恢复表结构
ALTER TABLE child_points DROP FOREIGN KEY fk_child_points_participation;
ALTER TABLE child_points DROP INDEX uk_participation_id;
ALTER TABLE child_points DROP COLUMN participation_id;
ALTER TABLE child_points ADD UNIQUE KEY uk_child_id (child_id);

ALTER TABLE child_medals DROP FOREIGN KEY fk_child_medals_participation;
ALTER TABLE child_medals DROP INDEX uk_participation_id_medal;
ALTER TABLE child_medals DROP COLUMN participation_id;
ALTER TABLE child_medals ADD UNIQUE KEY uk_child_medal (child_id, medal_id);

-- 恢复备份数据
INSERT INTO child_points SELECT * FROM child_points_backup_final;
INSERT INTO child_medals SELECT * FROM child_medals_backup_final;

SET FOREIGN_KEY_CHECKS = 1;

-- 3. 重启应用服务
-- 4. 验证功能正常

*/
