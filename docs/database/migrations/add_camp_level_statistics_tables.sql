-- =====================================================
-- 添加训练营级别的积分和勋章统计表
-- 版本: V2.2
-- 目标: 支持基于训练营的积分统计和勋章体系
-- =====================================================

-- 训练营孩子积分统计表
CREATE TABLE camp_child_points (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '积分记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id（冗余字段，便于查询）',
    total_points BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内总积分',
    week_points INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内本周积分',
    month_points INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内本月积分',
    total_checkins INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内总打卡次数',
    continuous_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内连续打卡天数',
    max_continuous_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内最大连续天数',
    week_rank INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内本周排名',
    last_week_rank INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营内上周排名',
    last_checkin_date DATE NOT NULL DEFAULT '1970-01-01' COMMENT '训练营内最后打卡日期',
    last_updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',

    UNIQUE KEY uk_child_participation (child_id, participation_id) COMMENT '孩子在特定训练营参与中的唯一积分记录',
    INDEX idx_camp_week_points (camp_id, week_points DESC) COMMENT '训练营内周积分排行',
    INDEX idx_camp_total_points (camp_id, total_points DESC) COMMENT '训练营内总积分排行',
    INDEX idx_camp_continuous_days (camp_id, continuous_days DESC) COMMENT '训练营内连续天数排行',
    INDEX idx_participation_id (participation_id) COMMENT '参与记录查询',
    INDEX idx_child_id (child_id) COMMENT '孩子查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='训练营孩子积分统计表：统计每个孩子在特定训练营中的积分和排行榜数据';

-- 训练营孩子勋章记录表
CREATE TABLE camp_child_medals (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '勋章记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id（冗余字段，便于查询）',
    medal_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '勋章ID，关联medals.id',
    is_unlocked TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已解锁 0:否 1:是',
    current_progress INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前进度',
    target_progress INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标进度',
    unlocked_at TIMESTAMP NULL COMMENT '解锁时间，NULL表示未解锁',
    points_earned INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '获得积分',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',

    UNIQUE KEY uk_child_medal_participation (child_id, medal_id, participation_id) COMMENT '孩子在特定训练营参与中的特定勋章唯一记录',
    INDEX idx_camp_child_unlocked (camp_id, child_id, is_unlocked) COMMENT '训练营内孩子解锁查询',
    INDEX idx_camp_unlocked_at (camp_id, unlocked_at DESC) COMMENT '训练营内解锁时间排序',
    INDEX idx_participation_id (participation_id) COMMENT '参与记录查询',
    INDEX idx_child_id (child_id) COMMENT '孩子查询',
    INDEX idx_medal_id (medal_id) COMMENT '勋章查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='训练营孩子勋章记录表：记录每个孩子在特定训练营中的勋章获得情况';

-- =====================================================
-- 数据迁移脚本
-- =====================================================

-- 为现有的用户训练营参与记录创建对应的积分统计记录
INSERT INTO camp_child_points (
    child_id, 
    participation_id, 
    camp_id, 
    total_points, 
    total_checkins, 
    continuous_days, 
    max_continuous_days,
    last_checkin_date,
    created_at,
    updated_at
)
SELECT 
    ucp.child_id,
    ucp.id as participation_id,
    ucp.camp_id,
    COALESCE(SUM(pr.points_change), 0) as total_points,
    COUNT(DISTINCT cr.checkin_date) as total_checkins,
    0 as continuous_days, -- 需要通过复杂逻辑计算，暂时设为0
    0 as max_continuous_days, -- 需要通过复杂逻辑计算，暂时设为0
    COALESCE(MAX(cr.checkin_date), '1970-01-01') as last_checkin_date,
    NOW() as created_at,
    NOW() as updated_at
FROM user_camp_participations ucp
LEFT JOIN point_records pr ON pr.participation_id = ucp.id AND pr.deleted_at IS NULL
LEFT JOIN checkin_records cr ON cr.participation_id = ucp.id AND cr.deleted_at IS NULL
WHERE ucp.deleted_at IS NULL
GROUP BY ucp.id, ucp.child_id, ucp.camp_id;

-- 为现有的勋章记录创建对应的训练营级别记录
-- 注意：这里假设现有的勋章记录需要分配到孩子的第一个训练营参与记录中
-- 实际业务中可能需要更复杂的迁移逻辑
INSERT INTO camp_child_medals (
    child_id,
    participation_id,
    camp_id,
    medal_id,
    is_unlocked,
    current_progress,
    target_progress,
    unlocked_at,
    points_earned,
    created_at,
    updated_at
)
SELECT 
    cm.child_id,
    first_participation.id as participation_id,
    first_participation.camp_id,
    cm.medal_id,
    cm.is_unlocked,
    cm.current_progress,
    cm.target_progress,
    cm.unlocked_at,
    cm.points_earned,
    cm.created_at,
    cm.updated_at
FROM child_medals cm
JOIN (
    -- 获取每个孩子的第一个训练营参与记录
    SELECT 
        child_id,
        MIN(id) as id,
        camp_id
    FROM user_camp_participations 
    WHERE deleted_at IS NULL
    GROUP BY child_id
) first_participation ON cm.child_id = first_participation.child_id
WHERE cm.deleted_at IS NULL;

-- =====================================================
-- 数据一致性检查脚本
-- =====================================================

-- 检查训练营积分统计记录的一致性
SELECT 
    'camp_child_points consistency check' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT child_id) as unique_children,
    COUNT(DISTINCT participation_id) as unique_participations
FROM camp_child_points 
WHERE deleted_at IS NULL;

-- 检查训练营勋章记录的一致性
SELECT 
    'camp_child_medals consistency check' as check_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT child_id) as unique_children,
    COUNT(DISTINCT participation_id) as unique_participations,
    COUNT(DISTINCT medal_id) as unique_medals
FROM camp_child_medals 
WHERE deleted_at IS NULL;

-- 检查是否有孤立的记录（参与记录不存在）
SELECT 
    'orphaned camp_child_points' as check_type,
    COUNT(*) as orphaned_count
FROM camp_child_points ccp
LEFT JOIN user_camp_participations ucp ON ccp.participation_id = ucp.id
WHERE ccp.deleted_at IS NULL AND ucp.id IS NULL;

SELECT 
    'orphaned camp_child_medals' as check_type,
    COUNT(*) as orphaned_count
FROM camp_child_medals ccm
LEFT JOIN user_camp_participations ucp ON ccm.participation_id = ucp.id
WHERE ccm.deleted_at IS NULL AND ucp.id IS NULL;
