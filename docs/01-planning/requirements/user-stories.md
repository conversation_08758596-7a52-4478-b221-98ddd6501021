# 用户故事文档

## 📋 文档信息
- **项目名称**：kids-platform 儿童跳绳学习平台
- **创建时间**：2025-07-04
- **版本**：v1.0
- **状态**：🔄 用户故事梳理中

## 👥 用户角色定义

### 主要角色
- **宝妈**：主要操作者，27-40岁，关注孩子成长
- **孩子**：学习者，5-12岁，幼儿园大班到小学6年级
- **家庭成员**：爸爸、爷爷奶奶等其他参与者

## 📱 用户故事集合

## 1. 新用户注册流程

### 故事1.1：首次使用注册
**作为** 一位宝妈  
**我希望** 能够快速注册并创建孩子档案  
**以便于** 开始使用平台记录孩子的跳绳练习  

#### 验收标准：
- [ ] 支持微信一键登录
- [ ] 引导创建孩子基本档案
- [ ] 填写必要信息：姓名、年龄、性别、省市地区
- [ ] 可选填写学校、班级信息
- [ ] 完成注册后自动进入主界面

#### 用户操作流程：
```
1. 打开小程序
2. 点击"微信登录"
3. 授权微信信息
4. 填写孩子档案信息
   - 姓名 *
   - 年龄 *
   - 性别 *
   - 省市地区 *
   - 学校（可选）
   - 班级（可选）
5. 点击"完成注册"
6. 进入主界面，显示欢迎信息
```

### 故事1.2：了解平台功能
**作为** 新注册的宝妈  
**我希望** 能够快速了解平台的主要功能  
**以便于** 知道如何使用这个平台帮助孩子学习跳绳  

#### 验收标准：
- [ ] 显示功能介绍引导页
- [ ] 介绍打卡、学习、社交等核心功能
- [ ] 提供跳过选项
- [ ] 引导观看第一个教学视频

## 2. 日常打卡流程

### 故事2.1：完成基础打卡
**作为** 宝妈  
**我希望** 能够快速记录孩子今天的跳绳练习  
**以便于** 跟踪孩子的练习情况和进步  

#### 验收标准：
- [ ] 一键进入打卡界面
- [ ] 必填练习时长
- [ ] 可选填写跳绳成绩
- [ ] 提交后显示打卡成功
- [ ] 更新连续打卡天数

#### 用户操作流程：
```
1. 孩子完成跳绳练习
2. 打开小程序，点击"今日打卡"
3. 填写练习时长（必填）
4. 选择性填写：
   - 一分钟最多跳数
   - 不断最多跳数
5. 点击"提交打卡"
6. 显示打卡成功页面
7. 显示获得积分和连续天数
```

### 故事2.2：分享打卡内容
**作为** 宝妈  
**我希望** 能够分享孩子的练习成果  
**以便于** 获得朋友们的鼓励，同时吸引更多朋友关注  

#### 验收标准：
- [ ] 支持上传视频号视频ID
- [ ] 支持上传朋友圈截图
- [ ] 支持填写经验感悟
- [ ] 分享内容获得额外积分奖励
- [ ] 自动生成精美分享卡片

#### 用户操作流程：
```
1. 在打卡界面选择分享选项
2. 上传视频号视频ID（可选）
3. 上传朋友圈截图（可选）
4. 填写经验感悟（可选，200字内）
5. 提交完整打卡
6. 获得额外积分奖励
7. 生成分享卡片
8. 一键分享到朋友圈
```

### 故事2.3：查看打卡历史
**作为** 宝妈  
**我希望** 能够查看孩子的打卡历史和成长轨迹  
**以便于** 了解孩子的进步情况和坚持程度  

#### 验收标准：
- [ ] 日历视图显示打卡记录
- [ ] 显示连续打卡天数
- [ ] 显示历史最佳成绩
- [ ] 成长曲线图展示
- [ ] 总练习时长统计

## 3. 社交互动流程

### 故事3.1：浏览他人打卡
**作为** 宝妈  
**我希望** 能够看到其他孩子的打卡内容  
**以便于** 学习经验，给孩子树立榜样  

#### 验收标准：
- [ ] 显示其他用户的打卡内容
- [ ] 按时间顺序排列
- [ ] 显示用户昵称和孩子信息
- [ ] 显示练习数据和分享内容
- [ ] 支持点赞和评论

#### 用户操作流程：
```
1. 进入"社区"页面
2. 浏览其他用户打卡内容
3. 查看练习时长和成绩
4. 阅读经验感悟分享
5. 观看分享的练习视频
6. 给优秀内容点赞
7. 留言鼓励和交流
```

### 故事3.2：互动交流
**作为** 宝妈  
**我希望** 能够与其他家长互动交流  
**以便于** 分享经验，获得支持和鼓励  

#### 验收标准：
- [ ] 支持快速标签评价
- [ ] 支持文字评论（50字内）
- [ ] 支持@其他用户
- [ ] 显示点赞数和评论数
- [ ] 热门评论置顶显示

#### 用户操作流程：
```
1. 在打卡内容下方点击评价
2. 选择快速标签：
   - 👍 棒棒哒
   - 💪 好厉害
   - 🔥 太燃了
   - ⭐ 进步很大
   - 💯 完美打卡
3. 或者输入文字评论
4. 可以@其他用户
5. 提交评论
6. 收到回复时获得通知
```

## 4. 学习成长流程

### 故事4.1：观看教学视频
**作为** 宝妈  
**我希望** 能够找到适合孩子水平的教学视频  
**以便于** 指导孩子正确学习跳绳技巧  

#### 验收标准：
- [ ] 根据孩子水平自动推荐视频
- [ ] 视频按难度分类
- [ ] 显示适合年龄范围
- [ ] 记录观看进度
- [ ] 支持收藏和分享

#### 用户操作流程：
```
1. 进入"学习"页面
2. 查看推荐的教学视频
3. 选择适合的难度等级
4. 点击观看视频
5. 跟着视频学习动作
6. 收藏有用的视频
7. 分享给其他家长
```

### 故事4.2：自动升级圈子
**作为** 宝妈  
**我希望** 系统能够根据孩子的进步自动推荐合适的学习内容  
**以便于** 孩子能够持续进步，不会因为内容太简单或太难而失去兴趣  

#### 验收标准：
- [ ] 系统自动检测跳绳成绩
- [ ] 达到升级标准时自动提醒
- [ ] 推荐新的学习内容
- [ ] 推荐加入对应水平的微信群
- [ ] 升级庆祝和奖励

#### 用户操作流程：
```
1. 孩子跳绳成绩持续提升
2. 系统检测到达到进阶圈标准
3. 弹出升级提醒：
   "恭喜！您的孩子已达到进阶圈水平！"
4. 显示新的学习内容推荐
5. 推荐加入进阶挑战群
6. 获得升级奖励积分
7. 更新个人资料显示新等级
```

## 5. 激励系统流程

### 故事5.1：积分获得和使用
**作为** 孩子  
**我希望** 通过打卡和分享获得积分奖励  
**以便于** 兑换喜欢的虚拟奖励，增加练习的乐趣  

#### 验收标准：
- [ ] 打卡后立即显示获得积分
- [ ] 积分获得规则清晰透明
- [ ] 积分商城展示可兑换奖励
- [ ] 兑换后立即生效
- [ ] 积分历史记录可查

#### 用户操作流程：
```
1. 完成打卡获得积分
2. 查看积分余额和获得记录
3. 进入积分商城
4. 浏览可兑换的虚拟奖励：
   - 头像装饰框
   - 个性称号
   - 专属勋章
   - 主题皮肤
5. 选择喜欢的奖励
6. 确认兑换
7. 装备新获得的奖励
```

### 故事5.2：查看排行榜
**作为** 孩子  
**我希望** 能够看到自己在排行榜上的位置  
**以便于** 了解自己的水平，激发继续努力的动力  

#### 验收标准：
- [ ] 显示多种类型排行榜
- [ ] 保护用户隐私（部分姓名显示）
- [ ] 显示自己的排名位置
- [ ] 可选择是否参与排行榜
- [ ] 按年龄段分组显示

#### 用户操作流程：
```
1. 进入"排行榜"页面
2. 查看不同类型排行榜：
   - 本周跳绳排行榜
   - 连续打卡排行榜
   - 积分排行榜
3. 找到自己的排名位置
4. 查看前几名的成绩
5. 设定超越目标
6. 在设置中选择是否显示在排行榜
```

## 6. 内容管理和推荐系统

### 故事6.1：观看教学视频
**作为** 宝妈
**我希望** 能够找到适合孩子年龄和水平的教学视频
**以便于** 指导孩子正确学习跳绳技巧

#### 验收标准：
- [ ] 按年龄分类显示教学视频
- [ ] 按技能难度筛选内容
- [ ] 显示视频时长、观看次数、评分
- [ ] 支持收藏和分享功能
- [ ] 记录观看进度和完成状态

#### 用户操作流程：
```
1. 进入"学习"页面
2. 选择孩子年龄段（如：小学低年级）
3. 选择技能类别（如：基础入门）
4. 浏览推荐视频列表
5. 点击观看感兴趣的视频
6. 观看完成后可以点赞、收藏、分享
7. 系统记录学习进度
```

### 故事6.2：创建学习专辑
**作为** 宝妈
**我希望** 能够将相关的教学视频组织成专辑
**以便于** 系统性地指导孩子学习

#### 验收标准：
- [ ] 创建个人学习专辑
- [ ] 添加视频到专辑
- [ ] 设置专辑学习顺序
- [ ] 跟踪专辑学习进度
- [ ] 分享专辑给其他家长

### 故事6.3：获得个性化推荐
**作为** 宝妈
**我希望** 系统能够根据孩子的学习情况推荐合适内容
**以便于** 更高效地提升孩子的跳绳技能

#### 验收标准：
- [ ] 基于孩子年龄和水平推荐内容
- [ ] 根据学习历史推荐相关视频
- [ ] 推荐热门和优质内容
- [ ] 推荐同龄孩子喜欢的内容

## 7. 家庭共享功能

### 故事7.1：创建家庭
**作为** 宝妈
**我希望** 能够创建家庭并邀请其他家庭成员
**以便于** 大家一起关注和指导孩子的跳绳学习

#### 验收标准：
- [ ] 创建家庭并设置家庭名称
- [ ] 生成邀请码或邀请链接
- [ ] 通过微信邀请家庭成员
- [ ] 设置成员角色和权限
- [ ] 管理家庭成员列表

#### 用户操作流程：
```
1. 进入"我的"页面
2. 点击"家庭管理"
3. 点击"创建家庭"
4. 设置家庭名称和头像
5. 生成邀请码
6. 通过微信分享邀请码
7. 家庭成员扫码加入
8. 设置成员角色权限
```

### 故事7.2：共享孩子档案
**作为** 家庭成员
**我希望** 能够查看孩子的学习记录和成长数据
**以便于** 了解孩子的学习情况并提供支持

#### 验收标准：
- [ ] 查看孩子基本信息和成长数据
- [ ] 查看打卡记录和学习进度
- [ ] 查看积分变化和成就获得
- [ ] 查看社交互动情况
- [ ] 接收孩子学习状态通知

### 故事7.3：协作指导孩子
**作为** 家庭成员
**我希望** 能够与其他家庭成员协作指导孩子
**以便于** 更好地支持孩子的学习成长

#### 验收标准：
- [ ] 设置打卡提醒和学习计划
- [ ] 家庭成员间沟通讨论
- [ ] 分工负责不同时段的指导
- [ ] 共同庆祝孩子的进步和成就
- [ ] 协调解决学习中的问题

## 8. 任务管理流程

### 故事8.1：参与系统任务
**作为** 宝妈
**我希望** 能够参与平台的大型挑战活动
**以便于** 与成千上万的网友一起努力，获得团队归属感

#### 验收标准：
- [ ] 显示可参与的系统任务列表
- [ ] 显示任务参与人数和完成情况
- [ ] 一键参与任务
- [ ] 查看全平台排行榜
- [ ] 与其他参与者互动交流

#### 用户操作流程：
```
1. 进入"挑战"页面
2. 查看系统推荐任务：
   - 21天跳绳养成计划（2,847人参与）
   - 新手入门挑战（892人参与）
   - 技能突破挑战（1,523人参与）
3. 选择适合的任务
4. 点击"加入挑战"
5. 查看任务详情和我的进度
6. 查看全平台排行榜
7. 在社区与其他参与者交流
8. 完成每日打卡，贡献团队目标
```

### 故事8.2：创建组队任务
**作为** 宝妈
**我希望** 能够创建小团队任务邀请好友一起参与
**以便于** 与好友深度互动，互相监督和鼓励

#### 验收标准：
- [ ] 创建组队任务界面
- [ ] 设置任务基本信息
- [ ] 选择任务类型（私密/公开）
- [ ] 邀请好友参与
- [ ] 管理团队成员

#### 用户操作流程：
```
1. 进入"任务"页面
2. 点击"创建组队任务"
3. 填写任务信息：
   - 任务名称："一起突破100个"
   - 任务描述：目标和要求
   - 开始/结束时间
   - 目标人数：3-5人
   - 任务类型：私密任务
4. 点击"创建任务"
5. 邀请好友：
   - 发送邀请链接
   - 微信分享邀请
6. 等待好友确认加入
7. 任务开始后一起打卡
```

### 故事8.3：参与组队任务
**作为** 宝妈
**我希望** 能够参与好友创建的组队任务
**以便于** 与好友一起努力，增进友谊

#### 验收标准：
- [ ] 接收任务邀请
- [ ] 查看任务详情
- [ ] 确认参与任务
- [ ] 查看队友进度
- [ ] 队友互动鼓励

#### 用户操作流程：
```
1. 收到好友任务邀请通知
2. 点击查看任务详情：
   - 任务名称和描述
   - 任务时间和要求
   - 已加入的队友
3. 点击"加入任务"
4. 进入任务界面：
   - 查看队友今日完成情况
   - 查看队内排行榜
   - 队友互动区域
5. 完成每日打卡
6. 给队友点赞鼓励
7. 在队内分享经验
```

### 故事6.4：任务进度管理
**作为** 宝妈
**我希望** 能够清楚地看到所有参与任务的进度
**以便于** 合理安排时间，确保任务完成

#### 验收标准：
- [ ] 显示所有参与的任务
- [ ] 显示任务完成进度
- [ ] 显示剩余时间
- [ ] 任务状态提醒
- [ ] 最多参与3个任务的限制

#### 用户操作流程：
```
1. 进入"我的任务"页面
2. 查看当前参与的任务：
   - 系统任务：21天跳绳养成（15/21天）
   - 组队任务：一起突破100个（3/7天）
   - 组队任务：家庭健身计划（1/14天）
3. 点击任务查看详细进度
4. 查看今日需要完成的任务
5. 优先完成即将到期的任务
6. 任务完成后可参与新任务
```

## 7. 家庭共享流程

### 故事7.1：邀请家庭成员
**作为** 宝妈
**我希望** 能够邀请爸爸、爷爷奶奶一起参与
**以便于** 全家人共同关注和支持孩子的成长

#### 验收标准：
- [ ] 生成家庭邀请链接
- [ ] 设置家庭成员角色权限
- [ ] 多设备数据同步
- [ ] 家庭成长报告
- [ ] 成员互动记录

#### 用户操作流程：
```
1. 进入"家庭"页面
2. 点击"邀请家庭成员"
3. 生成邀请链接或二维码
4. 发送给家庭成员
5. 家庭成员扫码加入
6. 设置成员角色和权限
7. 查看家庭成长报告
8. 家庭成员可以查看打卡记录
```

## 8. 问题解决流程

### 故事8.1：寻求帮助
**作为** 宝妈  
**我希望** 在遇到问题时能够快速获得帮助  
**以便于** 解决使用中的困难，更好地指导孩子  

#### 验收标准：
- [ ] 常见问题FAQ
- [ ] 在线客服支持
- [ ] 微信群答疑
- [ ] 视频教程指导
- [ ] 问题反馈渠道

#### 用户操作流程：
```
1. 遇到使用问题
2. 查看"帮助中心"
3. 搜索相关问题
4. 查看解决方案
5. 如未解决，联系在线客服
6. 或在微信群中求助
7. 获得其他家长和专家解答
```

## 📊 用户故事优先级

### 高优先级（MVP必须）
- 新用户注册流程
- 基础打卡流程
- 基本社交互动
- 积分奖励系统
- 排行榜查看
- 家庭共享功能（基础版）
- 内容管理和推荐系统
- 系统任务参与
- 组队任务创建和参与

### 中优先级（第二版本）
- 高级分享功能
- 家庭共享功能（高级版）
- 任务管理增强功能
- 数据分析功能

### 低优先级（后续版本）
- 高级社交功能
- 线下活动组织
- 专业指导服务
- 任务系统高级功能

### 未来功能（长期规划）
- 自动分圈机制
- 多设备同步
- 家庭成长报告

---

## 🔄 更新记录
- 2025-07-04：创建文档，梳理核心用户故事
- 2025-07-05：添加内容管理和家庭共享用户故事，调整功能优先级
- 待续：补充更多详细场景和边界情况
