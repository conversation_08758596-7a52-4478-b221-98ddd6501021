# 功能规格说明书

## 📋 文档信息
- **项目名称**：kids-platform 儿童跳绳学习平台
- **创建时间**：2025-07-04
- **版本**：v1.0
- **状态**：🔄 功能细化中

## 🎯 功能概览

### 核心功能模块
1. **用户管理系统** - 用户注册、登录、档案管理
2. **打卡系统** - 核心功能，记录练习和分享
3. **社交互动系统** - 点赞、评论、分享功能
4. **内容管理和推荐系统** - 教学视频、用户分享、专辑管理、智能推荐
5. **激励系统** - 积分、勋章、排行榜
6. **社团系统** - 社区组织和管理
7. **多用户孩子管理** - 多用户管理同一孩子

## 📱 1. 用户管理系统

### 1.1 用户注册登录
**功能描述**：支持微信授权登录，创建用户档案

#### 功能点：
- 微信一键登录
- 用户基本信息收集
- 孩子档案创建
- 隐私协议确认

#### 数据字段：
```
用户信息：
├── 微信用户信息（昵称、头像、openid）
├── 手机号（可选）
├── 注册时间
└── 最后登录时间

孩子档案：
├── 姓名（必填）
├── 年龄（必填）
├── 性别（必填）
├── 学校（可选）
├── 班级（可选）
└── 省市地区（必填）
```

### 1.2 档案管理
**功能描述**：管理孩子的基本信息和学习档案

#### 功能点：
- 孩子信息编辑
- 头像上传更换
- 学习偏好设置
- 隐私设置管理

## 🏃‍♀️ 2. 打卡系统（核心功能）

### 2.1 打卡内容录入
**功能描述**：用户完成练习后进行打卡记录

#### 必填项：
- **练习时长**：分钟数输入（数字输入框）

#### 可选项：
- **跳绳成绩**：
  - 一分钟最多跳多少个（数字输入）
  - 不断最多跳多少个（数字输入）
- **视频分享**：发布到视频号的视频ID
- **朋友圈截图**：上传朋友圈分享截图
- **经验感悟**：文字输入（200字以内）

#### 界面设计：
```
今日打卡
┌─────────────────────────┐
│ 练习时长 * [___] 分钟    │
│                         │
│ 跳绳成绩（可选）         │
│ 一分钟最多：[___] 个     │
│ 不断最多：[___] 个       │
│                         │
│ 视频分享（可选）         │
│ [上传视频号ID]          │
│                         │
│ 朋友圈截图（可选）       │
│ [上传截图]              │
│                         │
│ 经验感悟（可选）         │
│ [文字输入框 200字]       │
│                         │
│ [提交打卡] [保存草稿]    │
└─────────────────────────┘
```

### 2.2 打卡限制规则
- **频率限制**：每天只能打卡1次
- **时间限制**：每日00:00重置打卡状态
- **数据验证**：跳绳数据合理性检查

### 2.3 打卡历史记录
**功能描述**：查看个人打卡历史和成长轨迹

#### 功能点：
- 日历视图显示打卡记录
- 成长数据统计图表
- 历史最佳记录展示
- 连续打卡天数统计

## 💬 3. 社交互动系统

### 3.1 打卡内容展示
**功能描述**：展示用户打卡内容，支持互动

#### 展示内容：
- 用户头像、昵称
- 练习时长和成绩
- 视频内容（如有）
- 朋友圈截图（如有）
- 经验感悟文字
- 发布时间

### 3.2 互动功能
**功能描述**：用户间的社交互动

#### 点赞功能：
- 一键点赞/取消点赞
- 点赞数量显示
- 点赞用户列表

#### 评价功能：
**快速评价标签**：
- 👍 棒棒哒
- 💪 好厉害  
- 🔥 太燃了
- ⭐ 进步很大
- 💯 完美打卡

**文字评论**：
- 50字以内文字评论
- 支持@其他用户
- 评论时间显示
- 热门评论置顶

### 3.3 分享功能
**功能描述**：生成分享卡片，支持裂变传播

#### 分享卡片内容：
- 孩子头像和姓名
- 今日练习成果
- 连续打卡天数
- 成长进步数据
- 平台邀请二维码

#### 分享渠道：
- 微信朋友圈
- 微信好友
- 微信群聊
- 保存到相册

## 🏆 4. 激励系统

### 4.1 积分系统
**功能描述**：通过积分激励用户参与

#### 积分获得规则：
```
基础打卡（时长）：10积分
+ 跳绳数据记录：+5积分
+ 视频分享：+15积分
+ 朋友圈截图：+10积分
+ 经验感悟：+8积分
完整打卡奖励：+10积分
连续打卡奖励：连续7天+50积分
```

#### 积分消耗：
- 虚拟奖励兑换
- 特殊功能解锁
- 排行榜竞争

### 4.2 虚拟奖励系统
**功能描述**：积分兑换虚拟奖励

#### 奖励类型：
```
积分商城：
├── 头像装饰框（50积分）
├── 个性称号（100积分）
├── 专属勋章（200积分）
├── 主题皮肤（300积分）
└── 限时特殊奖励
```

### 4.3 排行榜系统
**功能描述**：展示用户成绩排名，激发竞争

#### 排行榜类型：
- **本周跳绳排行榜**：按最佳成绩排序
- **连续打卡排行榜**：按连续天数排序
- **积分排行榜**：按总积分排序

#### 隐私保护：
- 姓名部分显示（小*明）
- 用户可选择退出排行榜
- 年龄段分组显示

#### 排行榜界面：
```
本周跳绳排行榜
┌─────────────────────────┐
│ 1. 小*明  156个/分钟 ⭐⭐⭐│
│ 2. 小*华  142个/分钟 ⭐⭐ │
│ 3. 小*丽  138个/分钟 ⭐⭐ │
│ ...                     │
│ 15. 我  89个/分钟  ⭐    │
│                         │
│ [设置] 参与排行榜 ☑️     │
└─────────────────────────┘
```

## 📚 5. 内容管理和推荐系统

### 5.1 教学视频管理
**功能描述**：系统提供的官方教学内容，按类别和级别组织

#### 内容分类：
```yaml
按技能类别:
  - 基础入门: 基本跳法、姿势纠正
  - 技巧提升: 双摇、交叉、花式跳法
  - 高级挑战: 竞技技巧、高难度动作
  - 体能训练: 耐力训练、力量训练

按年龄分级:
  - 幼儿园 (3-6岁): 趣味启蒙
  - 小学低年级 (6-9岁): 基础技能
  - 小学高年级 (9-12岁): 技巧进阶
  - 中学生 (12-18岁): 竞技训练
```

#### 视频信息：
- 视频标题、描述、封面
- 难度等级标识（1-5星）
- 适合年龄范围
- 时长和观看次数
- 点赞、收藏、分享数
- 教练认证标识

### 5.2 用户分享视频
**功能描述**：用户上传的经验分享、技巧展示、成长记录视频

#### 分享类型：
```yaml
技巧分享:
  - 个人技巧展示
  - 学习心得分享
  - 训练方法介绍

成长记录:
  - 学习进步对比
  - 突破瓶颈经历
  - 阶段性成果展示

互动教学:
  - 家长指导技巧
  - 同龄人互相学习
  - 问题解答视频
```

#### 内容审核：
- 自动内容检测
- 人工审核机制
- 用户举报处理
- 优质内容推荐

### 5.3 专辑功能
**功能描述**：将相关视频组织成系列专辑，便于系统学习

#### 专辑类型：
```yaml
官方专辑:
  - 《零基础跳绳入门》(10集)
  - 《花式跳绳技巧大全》(15集)
  - 《亲子跳绳指南》(8集)
  - 《跳绳体能训练》(12集)

用户专辑:
  - 用户可创建个人专辑
  - 收藏喜欢的视频到专辑
  - 分享专辑给其他用户
  - 专辑评分和评论
```

#### 专辑管理：
- 专辑创建和编辑
- 视频排序和分组
- 学习进度跟踪
- 完成度统计

### 5.4 智能推荐系统
**功能描述**：基于用户行为和水平，智能推荐合适内容

#### 推荐算法：
```yaml
基于用户画像:
  - 年龄、性别、技能水平
  - 学习偏好和兴趣点
  - 历史观看和互动记录

基于行为分析:
  - 观看时长和完成率
  - 点赞、收藏、分享行为
  - 搜索和浏览历史

基于社交关系:
  - 关注用户的喜好
  - 同圈子用户推荐
  - 热门内容推荐
```

#### 推荐场景：
- 首页个性化推荐
- 观看完成后相关推荐
- 搜索结果智能排序
- 专题活动内容推荐

## 🏘️ 6. 社团系统

### 6.1 自动分圈机制
**功能描述**：根据用户成绩自动分配到合适圈子

#### 分圈规则：
```
新手圈：0-30个/分钟
├── 推荐内容：基础教学视频
├── 推荐群聊：新手成长群
└── 圈子氛围：鼓励入门，消除恐惧

进阶圈：31-80个/分钟  
├── 推荐内容：技巧提升视频
├── 推荐群聊：进阶挑战群
└── 圈子氛围：技巧交流，互相切磋

高手圈：81个/分钟以上
├── 推荐内容：高难度挑战视频
├── 推荐群聊：高手分享群
└── 圈子氛围：经验分享，带新指导
```

#### 升级机制：
- 系统自动检测用户最近7天最佳成绩
- 达到升级标准自动提醒
- 升级庆祝和奖励机制

### 6.2 圈子展示功能
**功能描述**：展示圈子信息和推荐内容

#### 圈子首页：
```
进阶圈
┌─────────────────────────┐
│ 圈子成员：1,234人        │
│ 平均水平：45-80个/分钟   │
│                         │
│ 推荐学习：               │
│ • 《双摇技巧详解》       │
│ • 《节奏感训练法》       │
│ • 《突破瓶颈的5个方法》  │
│                         │
│ 加入交流群：             │
│ [进阶挑战群] 点击加入    │
└─────────────────────────┘
```

## � 7. 任务管理系统

### 7.1 系统任务（全平台大挑战）
**功能描述**：平台运营创建的大型挑战活动，强调与全平台用户的团队归属感

#### 核心特点：
- **参与规模**：数百到数千人同时参与
- **社交属性**：与陌生网友一起努力，弱社交但强归属感
- **团队氛围**：营造"我们都在一起努力"的大团队感
- **运营价值**：提升平台整体活跃度和用户凝聚力

#### 功能设计：
```
系统任务展示：
├── 任务名称：如"21天跳绳养成计划"
├── 参与人数：实时显示总参与人数
├── 完成进度：今日完成人数/总参与人数
├── 我的进度：个人完成情况
├── 排行榜：全平台排名
└── 社区动态：参与者互动交流
```

#### 默认系统任务：
```
新手入门挑战（7天）：
├── 目标：连续7天打卡，每天≥10分钟
├── 奖励：200积分 + 新手勋章
└── 适合：刚注册的新用户

跳绳养成计划（21天）：
├── 目标：21天内完成18次打卡，每天≥15分钟
├── 奖励：500积分 + 坚持勋章
└── 适合：有一定基础的用户

技能突破挑战（30天）：
├── 目标：达到指定跳绳成绩（一分钟≥60个）
├── 奖励：800积分 + 突破勋章
└── 适合：想要提升技能的用户
```

#### 界面设计：
```
系统任务详情页
┌─────────────────────────┐
│ 🏃‍♀️ 21天跳绳养成计划    │
│ 👥 参与人数：2,847人     │
│ 📊 今日完成：856人 (30%) │
│ 🏆 总完成率：68%         │
│                         │
│ 🎯 我的进度：            │
│ 完成：15/21天 (71%)      │
│ 排名：第342名            │
│ 连续：3天               │
│                         │
│ 🔥 今日活跃榜：          │
│ 1. 小*明 ⭐⭐⭐          │
│ 2. 小*华 ⭐⭐⭐          │
│ 3. 小*丽 ⭐⭐⭐          │
│                         │
│ 💬 大家都在说：          │
│ "第15天了，坚持！"       │
│ "今天突破80个了！"       │
│                         │
│ [立即打卡] [查看全榜]    │
└─────────────────────────┘
```

### 7.2 组队任务（小团队协作）
**功能描述**：用户自主创建的小团队任务，强调与好友的深度社交和协作

#### 核心特点：
- **参与规模**：2-10人的小团队
- **社交属性**：与好友/家人一起，强社交深度连接
- **创建方式**：用户自主创建，邀请好友参与
- **社交价值**：深度社交互动和用户裂变

#### 任务创建流程：
```
1. 选择创建组队任务
2. 设置任务信息：
   - 任务名称
   - 任务描述
   - 开始/结束时间
   - 目标人数（2-10人）
   - 任务类型（私密/公开）
3. 邀请好友参与
4. 等待好友确认
5. 任务正式开始
```

#### 任务类型设计：
```
私密任务：
├── 仅通过邀请链接加入
├── 不在公开列表显示
├── 适合：家庭成员、亲密好友
└── 隐私保护更好

公开任务：
├── 在发现页面展示
├── 其他用户可申请加入
├── 创建者审核申请
└── 适合：寻找志同道合的伙伴
```

#### 组队任务界面：
```
我的组队任务
┌─────────────────────────┐
│ 🤝 "一起突破100个"       │
│ 👥 队员：3/5人           │
│ 📅 剩余：12天            │
│                         │
│ 队员进度：               │
│ 👤 小明妈妈 ✅ 已完成     │
│ 👤 小华妈妈 ⏰ 进行中     │
│ 👤 我 ❌ 未完成          │
│                         │
│ 今日队内排行：           │
│ 1. 小明妈妈 85个/分钟    │
│ 2. 小华妈妈 78个/分钟    │
│ 3. 我 待完成             │
│                         │
│ 💬 队友加油：            │
│ "大家一起冲！"          │
│                         │
│ [立即打卡] [邀请好友]    │
└─────────────────────────┘
```

### 7.3 任务管理规则
**功能描述**：任务参与和管理的基本规则

#### 参与限制：
- 每个用户最多同时参与3个任务
- 系统任务和组队任务都计入限制
- 任务完成或结束后可参与新任务

#### 退出机制：
- 加入任务后不主动退出
- 不打卡只是影响个人和团队进度
- 避免频繁进出影响团队稳定性

#### 奖励机制：
```
任务奖励 > 普通打卡奖励

系统任务奖励：
├── 基础积分：200-800分
├── 特殊勋章：任务专属勋章
├── 称号奖励：如"坚持达人"
└── 虚拟奖励：专属头像框

组队任务奖励：
├── 团队奖励：全员完成额外奖励
├── 个人奖励：个人完成基础奖励
├── 社交奖励：邀请好友额外积分
└── 创建者奖励：成功组队奖励
```

## �👨‍👩‍👧‍👦 8. 多用户孩子管理

> **📋 设计方案**：用户/孩子映射表实现多对多关系
> **🎯 核心需求**：不同用户可以管理相同孩子
> **⚡ 实现策略**：极简设计，只增加一个关联表

### 8.1 设计方案
**功能描述**：通过用户孩子关联表实现多用户管理同一孩子

#### 核心思路：
```yaml
数据关系:
  - users表 ←→ user_children表 ←→ children表
  - 多对多关系：一个用户可以管理多个孩子，一个孩子可以被多个用户管理

设计原则:
  - 极简设计：只增加一个关联表
  - 无复杂权限：有关联就能管理
  - 无家庭概念：直接用户孩子关联
  - 记录关系：爸爸/妈妈/爷爷/奶奶等
```

### 8.2 数据表设计
**功能描述**：用户孩子关联表设计

#### 表结构：
```sql
CREATE TABLE user_children (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID',
    relation VARCHAR(20) DEFAULT '' COMMENT '关系：爸爸/妈妈/爷爷/奶奶等',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_user_child (user_id, child_id),
    INDEX idx_user_id (user_id),
    INDEX idx_child_id (child_id)
) COMMENT='用户孩子关联表';
```

### 8.3 功能实现
**功能描述**：基于关联表的功能实现

#### 核心操作：
```yaml
添加关联:
  - 用户添加孩子到自己的管理列表
  - 邀请其他用户管理自己的孩子
  - 记录用户与孩子的关系

查询功能:
  - 查询用户管理的所有孩子
  - 查询孩子的所有管理者
  - 权限检查：是否可以管理某个孩子

管理功能:
  - 所有关联用户都可以查看孩子信息
  - 所有关联用户都可以管理孩子打卡
  - 所有关联用户都可以查看孩子积分记录
```

### 8.4 实现优势
**功能描述**：极简方案的优势分析

#### 设计优势：
```yaml
简单高效:
  - 只需要一个关联表
  - 逻辑清晰，易于理解
  - 开发和维护成本低

功能完整:
  - 完全满足多用户管理孩子的需求
  - 支持灵活的关系描述
  - 查询和权限检查简单直接

性能优秀:
  - 查询路径短，效率高
  - 索引简单，优化容易
  - 存储空间占用小

扩展性好:
  - 可以轻松添加新的关系类型
  - 可以根据需要增加字段
  - 不影响现有功能
```

---

## 📊 功能优先级

### P0（核心功能 - MVP必须）
- 用户注册登录
- 打卡系统（完整功能）
- 基础社交互动（点赞、评论）
- 积分和虚拟奖励系统
- 排行榜系统
- 多用户孩子管理（用户孩子关联表）
- 内容管理和推荐系统
- 系统任务（基础版）
- 组队任务（基础版）

### P1（重要功能 - 第二版本）
- 高级社交功能
- 数据分析功能
- 任务管理增强功能
- 家庭共享功能（高级版）

### P2（增强功能 - 后续版本）
- 线下活动组织
- 高级激励机制
- 运营管理工具
- 任务系统高级功能（实时互动、分层归属感等）

### P3（未来功能 - 长期规划）
- 自动分圈机制
- 多设备同步
- 家庭成长报告

---

## 🔄 更新记录
- 2025-07-04：创建文档，录入打卡系统详细功能
- 2025-07-05：重新定义内容管理和推荐系统，扩展家庭共享功能，调整功能优先级（自动分圈→P3，家庭共享基础版→P0，内容管理→P0）
- 待续：其他功能模块详细补充
