# 信息架构文档

## 📋 文档信息
- **项目名称**：kids-platform 儿童跳绳学习平台
- **创建时间**：2025-07-04
- **版本**：v1.0
- **状态**：🔄 架构设计中

## 🏗️ 数据结构设计

### 1. 用户数据结构

#### 1.1 用户基础信息 (User)
```json
{
  "id": "用户唯一ID",
  "openid": "微信openid",
  "unionid": "微信unionid",
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "phone": "手机号（可选）",
  "created_at": "注册时间",
  "last_login": "最后登录时间",
  "status": "用户状态（active/inactive/banned）"
}
```

#### 1.2 孩子档案信息 (Child)
```json
{
  "id": "孩子档案ID",
  "user_id": "关联用户ID",
  "name": "孩子姓名",
  "age": "年龄",
  "gender": "性别（male/female）",
  "school": "学校名称（可选）",
  "class": "班级（可选）",
  "province": "省份",
  "city": "城市",
  "district": "区县",
  "avatar": "孩子头像URL",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

### 2. 打卡数据结构

#### 2.1 打卡记录 (CheckIn)
```json
{
  "id": "打卡记录ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "date": "打卡日期（YYYY-MM-DD）",
  "duration": "练习时长（分钟）",
  "jump_per_minute": "一分钟最多跳数（可选）",
  "max_continuous": "不断最多跳数（可选）",
  "video_id": "视频号视频ID（可选）",
  "moments_screenshot": "朋友圈截图URL（可选）",
  "experience_text": "经验感悟文字（可选）",
  "created_at": "打卡时间",
  "status": "状态（normal/deleted）"
}
```

#### 2.2 打卡统计数据 (CheckInStats)
```json
{
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "total_days": "总打卡天数",
  "continuous_days": "连续打卡天数",
  "max_continuous_days": "历史最长连续天数",
  "total_duration": "总练习时长（分钟）",
  "best_per_minute": "历史最佳一分钟成绩",
  "best_continuous": "历史最佳连续跳数",
  "last_checkin_date": "最后打卡日期",
  "updated_at": "更新时间"
}
```

### 3. 社交互动数据结构

#### 3.1 点赞记录 (Like)
```json
{
  "id": "点赞ID",
  "user_id": "点赞用户ID",
  "target_type": "目标类型（checkin/comment）",
  "target_id": "目标ID",
  "created_at": "点赞时间"
}
```

#### 3.2 评论记录 (Comment)
```json
{
  "id": "评论ID",
  "user_id": "评论用户ID",
  "checkin_id": "打卡记录ID",
  "content": "评论内容",
  "comment_type": "评论类型（quick_tag/text）",
  "quick_tag": "快速标签（如：thumbs_up, strong, fire等）",
  "mentioned_users": "提及的用户ID列表",
  "like_count": "点赞数",
  "created_at": "评论时间",
  "status": "状态（normal/deleted）"
}
```

### 4. 激励系统数据结构

#### 4.1 积分记录 (Points)
```json
{
  "id": "积分记录ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "points": "积分变化（正数为获得，负数为消耗）",
  "source_type": "积分来源类型",
  "source_id": "来源记录ID",
  "description": "积分说明",
  "created_at": "获得时间"
}
```

#### 4.2 积分来源类型定义
```json
{
  "basic_checkin": "基础打卡（10分）",
  "with_data": "记录跳绳数据（+5分）",
  "with_video": "分享视频（+15分）",
  "with_screenshot": "朋友圈截图（+10分）",
  "with_experience": "经验感悟（+8分）",
  "complete_checkin": "完整打卡奖励（+10分）",
  "continuous_7days": "连续7天打卡（+50分）",
  "invite_friend": "邀请好友（+30分）",
  "quality_content": "优质内容奖励（+20分）"
}
```

#### 4.3 虚拟奖励 (VirtualReward)
```json
{
  "id": "奖励ID",
  "name": "奖励名称",
  "type": "奖励类型（avatar_frame/title/badge/theme）",
  "description": "奖励描述",
  "cost": "兑换所需积分",
  "image_url": "奖励图片URL",
  "is_limited": "是否限时",
  "start_time": "开始时间",
  "end_time": "结束时间",
  "status": "状态（active/inactive）"
}
```

#### 4.4 用户奖励记录 (UserReward)
```json
{
  "id": "记录ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "reward_id": "奖励ID",
  "obtained_at": "获得时间",
  "is_equipped": "是否装备中",
  "equipped_at": "装备时间"
}
```

### 5. 排行榜数据结构

#### 5.1 排行榜记录 (Leaderboard)
```json
{
  "id": "排行榜ID",
  "type": "排行榜类型（weekly_jump/continuous_days/total_points）",
  "period": "统计周期（2025-W01）",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "score": "排行分数",
  "rank": "排名",
  "created_at": "创建时间"
}
```

#### 5.2 排行榜配置 (LeaderboardConfig)
```json
{
  "type": "排行榜类型",
  "name": "排行榜名称",
  "description": "排行榜描述",
  "update_frequency": "更新频率（daily/weekly/monthly）",
  "is_active": "是否启用",
  "privacy_level": "隐私级别（public/partial/private）"
}
```

### 6. 圈子系统数据结构

#### 6.1 圈子信息 (Circle)
```json
{
  "id": "圈子ID",
  "name": "圈子名称",
  "type": "圈子类型（beginner/intermediate/advanced）",
  "min_score": "最低分数要求",
  "max_score": "最高分数要求",
  "description": "圈子描述",
  "wechat_group_qr": "微信群二维码URL",
  "member_count": "成员数量",
  "is_active": "是否启用"
}
```

#### 6.2 用户圈子关系 (UserCircle)
```json
{
  "id": "关系ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "circle_id": "圈子ID",
  "joined_at": "加入时间",
  "current_score": "当前分数",
  "last_score_update": "最后分数更新时间"
}
```

### 7. 内容管理数据结构

#### 7.1 教学视频 (Video)
```json
{
  "id": "视频ID",
  "title": "视频标题",
  "description": "视频描述",
  "video_url": "视频URL",
  "thumbnail_url": "缩略图URL",
  "duration": "视频时长（秒）",
  "difficulty_level": "难度等级（1-5）",
  "target_age_min": "适合最小年龄",
  "target_age_max": "适合最大年龄",
  "category": "视频分类",
  "tags": "标签列表",
  "view_count": "观看次数",
  "like_count": "点赞次数",
  "created_at": "创建时间",
  "status": "状态（published/draft/archived）"
}
```

#### 7.2 视频观看记录 (VideoView)
```json
{
  "id": "观看记录ID",
  "user_id": "用户ID",
  "video_id": "视频ID",
  "watch_duration": "观看时长（秒）",
  "completion_rate": "完成率（0-1）",
  "created_at": "观看时间"
}
```

### 8. 内容管理数据结构

#### 8.1 教学视频 (Video)
```json
{
  "id": "视频ID",
  "title": "视频标题",
  "description": "视频描述",
  "cover_url": "封面图片URL",
  "video_url": "视频文件URL",
  "duration": "视频时长（秒）",
  "category": "内容分类（basic/skill/advanced/fitness）",
  "age_group": "适合年龄段（preschool/primary_low/primary_high/middle）",
  "difficulty": "难度等级（1-5星）",
  "tags": "标签数组",
  "instructor": "教练信息",
  "is_certified": "是否认证内容",
  "view_count": "观看次数",
  "like_count": "点赞数",
  "share_count": "分享数",
  "status": "状态（published/draft/reviewing）",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

#### 8.2 用户分享视频 (UserVideo)
```json
{
  "id": "分享视频ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "title": "视频标题",
  "description": "视频描述",
  "video_url": "视频文件URL",
  "cover_url": "封面图片URL",
  "duration": "视频时长",
  "share_type": "分享类型（skill/progress/experience）",
  "tags": "标签数组",
  "view_count": "观看次数",
  "like_count": "点赞数",
  "comment_count": "评论数",
  "share_count": "分享数",
  "audit_status": "审核状态（pending/approved/rejected）",
  "is_featured": "是否精选推荐",
  "created_at": "创建时间"
}
```

#### 8.3 专辑 (Album)
```json
{
  "id": "专辑ID",
  "user_id": "创建用户ID（系统专辑为null）",
  "title": "专辑标题",
  "description": "专辑描述",
  "cover_url": "专辑封面URL",
  "type": "专辑类型（official/user）",
  "category": "专辑分类",
  "video_count": "视频数量",
  "total_duration": "总时长",
  "view_count": "观看次数",
  "like_count": "点赞数",
  "collect_count": "收藏数",
  "is_public": "是否公开",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

#### 8.4 专辑视频关联 (AlbumVideo)
```json
{
  "id": "关联ID",
  "album_id": "专辑ID",
  "video_id": "视频ID",
  "video_type": "视频类型（official/user）",
  "sort_order": "排序顺序",
  "created_at": "添加时间"
}
```

#### 8.5 用户观看记录 (ViewHistory)
```json
{
  "id": "观看记录ID",
  "user_id": "用户ID",
  "video_id": "视频ID",
  "video_type": "视频类型（official/user）",
  "watch_duration": "观看时长（秒）",
  "completion_rate": "完成率（0-1）",
  "last_position": "最后观看位置（秒）",
  "created_at": "观看时间",
  "updated_at": "更新时间"
}
```

### 9. 家庭共享数据结构

#### 9.1 家庭 (Family)
```json
{
  "id": "家庭ID",
  "name": "家庭名称",
  "avatar": "家庭头像URL",
  "invite_code": "邀请码",
  "creator_id": "创建者用户ID",
  "member_count": "成员数量",
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

#### 9.2 家庭成员 (FamilyMember)
```json
{
  "id": "成员关系ID",
  "family_id": "家庭ID",
  "user_id": "用户ID",
  "role": "角色（creator/parent/guardian/child）",
  "nickname": "家庭内昵称",
  "permissions": "权限列表",
  "joined_at": "加入时间",
  "status": "状态（active/inactive）"
}
```

#### 9.3 家庭孩子关联 (FamilyChild)
```json
{
  "id": "关联ID",
  "family_id": "家庭ID",
  "child_id": "孩子档案ID",
  "primary_guardian_id": "主要监护人ID",
  "created_at": "关联时间"
}
```

#### 9.4 家庭消息 (FamilyMessage)
```json
{
  "id": "消息ID",
  "family_id": "家庭ID",
  "sender_id": "发送者用户ID",
  "message_type": "消息类型（text/checkin_share/achievement_share）",
  "content": "消息内容",
  "related_id": "关联数据ID（如打卡记录ID）",
  "created_at": "发送时间"
}
```

### 10. 任务管理数据结构

#### 10.1 系统任务 (SystemTask)
```json
{
  "id": "系统任务ID",
  "name": "任务名称",
  "description": "任务描述",
  "type": "任务类型（beginner/intermediate/advanced）",
  "duration_days": "任务周期（天数）",
  "target_checkins": "目标打卡次数",
  "min_duration": "最低练习时长（分钟）",
  "target_score": "目标成绩（可选）",
  "reward_points": "奖励积分",
  "reward_badge": "奖励勋章ID",
  "start_date": "开始日期",
  "end_date": "结束日期",
  "is_active": "是否启用",
  "created_at": "创建时间"
}
```

#### 10.2 组队任务 (TeamTask)
```json
{
  "id": "组队任务ID",
  "creator_id": "创建者用户ID",
  "name": "任务名称",
  "description": "任务描述",
  "start_date": "开始日期",
  "end_date": "结束日期",
  "max_members": "最大成员数",
  "current_members": "当前成员数",
  "task_type": "任务类型（private/public）",
  "target_checkins": "目标打卡次数",
  "min_duration": "最低练习时长",
  "reward_points": "奖励积分",
  "status": "状态（recruiting/active/completed/cancelled）",
  "created_at": "创建时间"
}
```

#### 10.3 任务参与记录 (TaskParticipation)
```json
{
  "id": "参与记录ID",
  "user_id": "用户ID",
  "child_id": "孩子ID",
  "task_type": "任务类型（system/team）",
  "task_id": "任务ID",
  "joined_at": "加入时间",
  "completed_checkins": "已完成打卡次数",
  "completion_rate": "完成率",
  "is_completed": "是否完成任务",
  "completed_at": "完成时间",
  "status": "状态（active/completed/failed）"
}
```

#### 10.4 任务打卡关联 (TaskCheckin)
```json
{
  "id": "关联记录ID",
  "checkin_id": "打卡记录ID",
  "task_participation_id": "任务参与记录ID",
  "task_type": "任务类型",
  "task_id": "任务ID",
  "bonus_points": "任务奖励积分",
  "created_at": "关联时间"
}
```

#### 10.5 任务统计数据 (TaskStats)
```json
{
  "task_type": "任务类型",
  "task_id": "任务ID",
  "total_participants": "总参与人数",
  "active_participants": "活跃参与人数",
  "completed_participants": "完成人数",
  "completion_rate": "完成率",
  "today_checkins": "今日打卡人数",
  "total_checkins": "总打卡次数",
  "updated_at": "更新时间"
}
```

### 9. 家庭共享数据结构

#### 9.1 家庭信息 (Family)
```json
{
  "id": "家庭ID",
  "name": "家庭名称",
  "created_by": "创建者用户ID",
  "created_at": "创建时间",
  "status": "状态（active/inactive）"
}
```

#### 9.2 家庭成员关系 (FamilyMember)
```json
{
  "id": "关系ID",
  "family_id": "家庭ID",
  "user_id": "用户ID",
  "role": "角色（parent/guardian/other）",
  "permissions": "权限列表",
  "joined_at": "加入时间",
  "status": "状态（active/inactive）"
}
```

## 🔗 数据关系图

### 核心实体关系
```
User (用户)
├── Child (孩子档案) [1:N]
├── CheckIn (打卡记录) [1:N]
├── Points (积分记录) [1:N]
├── UserReward (奖励记录) [1:N]
├── Like (点赞记录) [1:N]
├── Comment (评论记录) [1:N]
├── UserCircle (圈子关系) [1:N]
├── TaskParticipation (任务参与) [1:N]
└── TeamTask (创建的组队任务) [1:N]

CheckIn (打卡记录)
├── Like (点赞) [1:N]
├── Comment (评论) [1:N]
├── Points (积分来源) [1:N]
└── TaskCheckin (任务关联) [1:N]

SystemTask (系统任务)
├── TaskParticipation (参与记录) [1:N]
├── TaskCheckin (打卡关联) [1:N]
└── TaskStats (统计数据) [1:1]

TeamTask (组队任务)
├── TaskParticipation (参与记录) [1:N]
├── TaskCheckin (打卡关联) [1:N]
└── TaskStats (统计数据) [1:1]

Circle (圈子)
├── UserCircle (用户关系) [1:N]
└── Video (推荐内容) [N:N]
```

## 📊 索引设计

### 主要索引
```sql
-- 用户相关
CREATE INDEX idx_user_openid ON users(openid);
CREATE INDEX idx_child_user_id ON children(user_id);

-- 打卡相关
CREATE INDEX idx_checkin_user_date ON checkins(user_id, date);
CREATE INDEX idx_checkin_child_date ON checkins(child_id, date);
CREATE INDEX idx_checkin_date ON checkins(date);

-- 社交相关
CREATE INDEX idx_like_target ON likes(target_type, target_id);
CREATE INDEX idx_comment_checkin ON comments(checkin_id);

-- 积分相关
CREATE INDEX idx_points_user ON points(user_id);
CREATE INDEX idx_points_source ON points(source_type, source_id);

-- 任务相关
CREATE INDEX idx_task_participation_user ON task_participation(user_id);
CREATE INDEX idx_task_participation_task ON task_participation(task_type, task_id);
CREATE INDEX idx_task_checkin_task ON task_checkin(task_type, task_id);
CREATE INDEX idx_team_task_creator ON team_task(creator_id);
CREATE INDEX idx_team_task_status ON team_task(status);

-- 排行榜相关
CREATE INDEX idx_leaderboard_type_period ON leaderboard(type, period, rank);
```

## 🔄 数据流转

### 打卡数据流
```
用户打卡 → CheckIn记录 → 检查任务关联 → 创建TaskCheckin记录 → 触发积分计算 → 更新CheckInStats → 更新TaskStats → 更新排行榜 → 检查圈子升级
```

### 任务管理流
```
系统任务：
平台创建SystemTask → 用户参与 → 创建TaskParticipation → 打卡时关联TaskCheckin → 更新TaskStats

组队任务：
用户创建TeamTask → 邀请好友 → 好友加入TaskParticipation → 打卡时关联TaskCheckin → 更新TaskStats → 检查任务完成
```

### 社交互动流
```
用户点赞/评论 → 创建Like/Comment记录 → 更新统计数据 → 触发消息通知
```

### 激励系统流
```
用户行为 → 积分计算（普通+任务奖励） → Points记录 → 检查奖励解锁 → 更新用户状态
```

---

## 📝 设计原则

1. **数据一致性**：关键数据冗余存储，确保查询性能
2. **扩展性**：预留字段支持未来功能扩展
3. **隐私保护**：敏感数据加密存储
4. **性能优化**：合理设计索引，支持高频查询
5. **数据完整性**：外键约束和数据验证

---

## 🔄 更新记录
- 2025-07-04：创建文档，定义核心数据结构
- 2025-07-05：添加内容管理和家庭共享数据结构，调整功能优先级
- 待续：补充缓存策略和数据迁移方案
