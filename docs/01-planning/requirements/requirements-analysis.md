# 项目需求分析文档

## 📋 文档信息
- **项目名称**：kids-platform 儿童跳绳学习平台
- **创建时间**：2025-07-04
- **版本**：v2.0（基于文档驱动重新梳理）
- **状态**：✅ 需求分析完成

## 🎯 项目定位

### 产品定位
为小学生家庭提供一个集素质教育学习、打卡记录、社交分享于一体的综合性平台，旨在激发孩子的学习兴趣，培养健康的生活习惯。以跳绳为起点，逐步扩展到多品类素质教育内容。

### 目标用户
- **主要用户**：小学生家庭，尤其是宝妈为主的操作者（27-40岁）
- **次要用户**：孩子（幼儿园大班-小学6年级，5-12岁）
- **参与用户**：其他家长（爸爸、爷爷奶奶等）

### 核心价值主张
为小学生家庭提供**"低成本、高确定性、有情感"**的素质教育学习解决方案。

#### 对宝妈的价值
- **解决育儿焦虑**：怕孩子落后的根本焦虑
- **应对现实需求**：体育测试、学校跳绳比赛等具体场景
- **增强孩子体质**：长远的健康价值

#### 对孩子的价值
- **技能提升 → 自信建立**：掌握技能带来成就感
- **社交认可 → 荣誉满足**：视频打卡获得点赞和评价

### 独特竞争优势
1. **保姆级详细教程**：解决"不会教"的问题
2. **社群答疑互动**：宝妈之间的经验分享
3. **集体打卡成长**：营造学校式的集体氛围
4. **身份认同**：强化社团归属感和用户粘性
5. **良好社区氛围**：愿景级的核心竞争力

### 产品底层动机
1. 宝妈们本质上，是在为自己 **'如何更好地陪伴孩子成长'**这个复杂且焦虑的难题，寻找一个 **"低成本、高确定性、有情感"** 的解决方案。
2. 宝妈的根本诉求，并不是陪伴孩子成长，而是确保孩子不会被落下，确保自己是"好妈妈"这一社会角色的胜利者。
3. 陪伴只是手段，不是目的。降低焦虑、赢得孩子成长竞争、获得社会认可才是本质。

## 🏃‍♀️ 运营模式

### 三位一体策略
- **视频号**：流量入口，吸引新用户
- **小程序**：核心服务平台（教程+打卡+社交+裂变）
- **微信群**：用户运营阵地，社群答疑，后续变现用户池

### 资源约束
- **团队规模**：个人项目（1人开发+运营）
- **资金约束**：尽量压低成本
- **种子用户**：开发者两个小孩的同学/朋友
- **用户特点**：面临6年级升学体育考试压力

## 📱 核心需求

### 用户管理需求
- 微信授权登录
- 多孩子管理（最多3个孩子）
- 孩子基本信息：姓名、年龄、性别、学校、班级，省市地区
- 家庭共享机制（多个家长管理同一孩子）
- 孩子唯一标识（系统自动生成UUID）

### 打卡系统需求

#### 自由打卡模式
- 用户自主进行练习活动
- **视频处理方案**：打卡视频发布到用户自己的视频号，复制视频ID到打卡记录（节约成本）
- 可选记录练习数据（一分钟跳绳次数、 不断最多跳多少个、练习时长分钟）
- 添加练习感受

#### 学习打卡模式
- 社团内推荐教学视频列表
- 用户可选择"跟视频学习"模式打卡
- 标记学习打卡与自由打卡的区别
- 支持不同品类的学习内容
- **注**：MVP阶段采用简化实现，复杂的进度管理和学习路径功能将根据用户反馈在后续版本中完善

### 任务管理需求

#### 个人打卡任务
- 创建个人打卡任务
- 设置任务周期（7/14/21/30天）
- 显示"和大家一起打卡"氛围
- 全平台排行榜展示

#### 组队打卡任务
- 创建组队打卡任务
- 邀请好友参与任务
- 队友打卡状态查看
- 队内排行榜

### 激励系统需求

#### 勋章系统
- **成长类勋章**：连续打卡天数
- **挑战类勋章**：完成任务数量
- **社交类勋章**：邀请好友成功
- **特殊类勋章**：稀有成就
- 勋章分享功能（朋友圈裂变）

#### 家庭奖励系统
- 任务创建时奖励设置功能
- 奖励描述文字输入（200字以内）
- 奖励类型选择（亲子活动/小礼物/特殊体验）
- 任务完成后奖励提醒和领奖证书生成
- 家长奖励兑现确认机制
- 奖励兑现记录追踪
- 社团奖励榜展示功能

#### 基础数据记录系统（为V1.1 PB系统做准备）
- 打卡时练习数据记录（次数、时长、完成度等）
- 打卡时长记录
- 连续打卡天数自动统计
- 个人中心基础数据展示
- 支持不同品类的数据记录格式
- 为V1.1完整PB系统预留数据结构

#### 数据埋点与分析系统
- 用户行为数据埋点
- 关键业务指标统计
- 用户留存和活跃度分析
- 功能使用情况统计
- 分享转化效果追踪
- 基础数据可视化展示

#### 双轨制等级系统
- 综合等级（基于参与度）
- 技能等级（基于练习数据和完成度）
- 支持不同品类的等级体系
- 分级社区验证机制

### 排行榜需求
- 全平台最近7天打卡排行
- 队内排行榜
- 隐私保护（部分姓名显示）
- 不显示具体练习数据（避免攀比）

### 学习内容管理需求

#### 视频内容分类
- **官方内容**：平台官方制作的教学视频
- **大神打卡**：优秀用户的打卡视频展示
- **跟练视频**：适合跟着练习的指导视频
- **用户分享**：普通用户分享的内容
- **挑战示范**：挑战任务的示范视频

#### 视频合集功能
- 视频合集创建和管理（后台管理）
- 合集标题、封面图、简介说明
- 合集内视频按学习顺序排列
- 合集统计数据：视频数量、观看次数、点赞数、推荐数
- 合集分类：按难度等级、适合年龄分类
- 精选合集推荐机制
- 用户合集互动：观看、点赞、收藏、分享

#### 基础内容管理
- 教学视频管理（微信视频号）
- 视频列表展示和推荐
- 多品类内容分类和标签管理
- 观看统计和互动数据
- 支持不同品类的内容组织方式
- **注**：复杂的学习路径编排和进度控制功能在MVP阶段简化实现，后续根据运营情况迭代优化

### 社团系统需求

#### 社团分类管理
- 多社团支持（跳绳社团、国学社团、魔方社团等）
- 社团详细介绍页面
- 社团特色和学习目标展示
- 社团难度等级和适合年龄说明

#### 社团社区功能
- 社团相关教学视频列表
- 最新加入该社团的成员展示
- 其他成员优秀打卡视频推荐
- 社团内成员互动和点赞

#### 社团社交功能
- "和大家一起打卡"（社团内全体成员氛围）
- "组队打卡"（社团内小团队挑战）
- 社团内排行榜和成就展示
- 社团专属勋章和等级系统

#### 未来规划：地区分社团（V2.0+）
- **三级社团体系**：全国社团 → 地区分社团 → 精细化小组
- **线下活动组织**：同城用户线下聚会和活动
- **本地化运营**：针对不同地区的特色运营
- **商业化机会**：本地商家合作、线下课程、装备销售

### 社交分享需求
- 勋章分享卡片生成
- 成长记录分享
- 任务邀请分享
- 项目加入邀请分享
- 一键分享到朋友圈/微信群

## 💻 技术需求

### 技术架构
- **项目结构**：Monorepo模式
- **前端**：微信小程序（原生开发）
- **后端**：Golang + Gin框架
- **数据库**：MySQL
- **管理后台**：Vue 3 + Element Plus

### 安全需求
- 用户隐私数据保护
- 家庭邀请码安全机制
- 用户隐私保护
- 数据传输安全

### 性能需求
- 小程序响应速度 < 2秒
- 支持并发用户数 > 1000
- 数据库查询优化
- 图片和视频加载优化

## 📊 业务约束

### MVP范围
**P0（核心功能）**：
- 用户管理、打卡系统、任务管理
- 基础勋章系统、排行榜
- 学习内容管理、家庭共享机制
- 社团系统（单一全国社团）

**P1（重要功能）**：
- 积分等级系统、补打卡功能
- 更多勋章类型、队友互动功能

**P2（增强功能）**：
- 技能验证系统、地区分社团
- 线下活动组织、高级功能、运营工具

### 成本控制
- **开发阶段**：约400元（服务器+域名）
- **运营阶段**：50-100元/月
- 使用免费/低成本的设计资源

### 合规要求
- 符合微信小程序规范
- 遵守个人信息保护法
- 儿童隐私保护
- 内容安全审核

---

**需求分析完成！** 本文档基于文档驱动方式重新梳理，融合了前期成果和新的思考。
