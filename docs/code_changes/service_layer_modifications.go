// =====================================================
// Service层业务逻辑重构方案
// 支持基于训练营的积分统计和勋章管理
// =====================================================

package services

import (
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"time"
)

// ==================== 更新后的 PointsService 接口 ====================

// PointsService 积分服务接口
type PointsService interface {
	// 核心积分操作（基于participation_id）
	AddPoints(participationID uint, points int, reason string, sourceType string, sourceID uint) error
	DeductPoints(participationID uint, points int, reason string) error
	GetPointsByParticipationID(participationID uint) (*models.ChildPoints, error)

	// 兼容性方法（为了减少现有代码的修改）
	GetGlobalPointsByChildID(childID uint) (*models.ChildPointsGlobalStats, error)
	GetAllCampPointsByChildID(childID uint) ([]*models.ChildPoints, error)

	// 排行榜相关
	GetCampRanking(campID uint, rankType string, limit int) ([]*models.ChildPoints, error)
	GetChildRankInCamp(participationID uint, rankType string) (int, error)
	UpdateCampRanking(campID uint) error

	// 统计相关
	GetCampStatistics(campID uint) (*models.CampStatistics, error)
	CalculateWeeklyPoints(participationID uint) error
	CalculateMonthlyPoints(participationID uint) error

	// 积分记录
	GetPointsHistory(participationID uint, offset, limit int) ([]*models.PointRecord, int64, error)
	GetPointsHistoryByTimeRange(participationID uint, startTime, endTime time.Time) ([]*models.PointRecord, error)
}

// ==================== PointsService 实现 ====================

type pointsService struct {
	childPointsRepo           repositories.ChildPointsRepository
	pointRecordsRepo          repositories.PointRecordsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	childrenRepo              repositories.ChildrenRepository
}

func NewPointsService(
	childPointsRepo repositories.ChildPointsRepository,
	pointRecordsRepo repositories.PointRecordsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	childrenRepo repositories.ChildrenRepository,
) PointsService {
	return &pointsService{
		childPointsRepo:           childPointsRepo,
		pointRecordsRepo:          pointRecordsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		childrenRepo:              childrenRepo,
	}
}

// ==================== 核心积分操作 ====================

// AddPoints 为训练营参与记录添加积分
func (s *pointsService) AddPoints(participationID uint, points int, reason string, sourceType string, sourceID uint) error {
	// 1. 获取参与记录信息
	participation, err := s.userCampParticipationRepo.GetByID(participationID)
	if err != nil {
		return errcode.ErrDataNotFound.WithDetails("参与记录不存在")
	}

	// 2. 获取或创建积分记录
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil && err != errcode.ErrDataNotFound {
		return err
	}

	if childPoints == nil {
		// 创建新的积分记录
		childPoints = &models.ChildPoints{
			ChildID:           participation.ChildID,
			ParticipationID:   int64(participationID),
			TotalPoints:       0,
			WeekPoints:        0,
			MonthPoints:       0,
			TotalCheckins:     0,
			ContinuousDays:    0,
			MaxContinuousDays: 0,
			WeekRank:          0,
			LastWeekRank:      0,
			LastCheckinDate:   time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
			LastUpdatedAt:     time.Now(),
		}

		if err := s.childPointsRepo.Create(childPoints); err != nil {
			return err
		}
	}

	// 3. 更新积分
	childPoints.TotalPoints += int64(points)
	childPoints.WeekPoints += points  // 需要根据时间判断是否在本周
	childPoints.MonthPoints += points // 需要根据时间判断是否在本月
	childPoints.LastUpdatedAt = time.Now()

	if err := s.childPointsRepo.Update(childPoints.ID, childPoints); err != nil {
		return err
	}

	// 4. 创建积分记录
	pointRecord := &models.PointRecord{
		ChildID:         participation.ChildID,
		ParticipationID: int64(participationID),
		PointsChange:    points,
		Reason:          reason,
		SourceType:      sourceType,
		SourceID:        int64(sourceID),
		CreatedAt:       time.Now(),
	}

	if err := s.pointRecordsRepo.Create(pointRecord); err != nil {
		return err
	}

	// 5. 更新孩子表的全局积分（聚合所有训练营的积分）
	if err := s.updateChildGlobalPoints(participation.ChildID); err != nil {
		// 这里可以记录日志，但不影响主流程
		// log.Error("更新孩子全局积分失败", err)
	}

	return nil
}

// DeductPoints 扣除训练营参与记录的积分
func (s *pointsService) DeductPoints(participationID uint, points int, reason string) error {
	// 扣除积分就是添加负积分
	return s.AddPoints(participationID, -points, reason, "deduction", 0)
}

// GetPointsByParticipationID 获取训练营参与记录的积分信息
func (s *pointsService) GetPointsByParticipationID(participationID uint) (*models.ChildPoints, error) {
	return s.childPointsRepo.GetByParticipationID(participationID)
}

// ==================== 兼容性方法 ====================

// GetGlobalPointsByChildID 获取孩子的全局积分统计
func (s *pointsService) GetGlobalPointsByChildID(childID uint) (*models.ChildPointsGlobalStats, error) {
	return s.childPointsRepo.GetGlobalStatsByChildID(childID)
}

// GetAllCampPointsByChildID 获取孩子在所有训练营的积分记录
func (s *pointsService) GetAllCampPointsByChildID(childID uint) ([]*models.ChildPoints, error) {
	return s.childPointsRepo.GetByChildID(childID)
}

// ==================== 排行榜相关 ====================

// GetCampRanking 获取训练营排行榜
func (s *pointsService) GetCampRanking(campID uint, rankType string, limit int) ([]*models.ChildPoints, error) {
	return s.childPointsRepo.GetCampRanking(campID, rankType, limit)
}

// GetChildRankInCamp 获取孩子在训练营中的排名
func (s *pointsService) GetChildRankInCamp(participationID uint, rankType string) (int, error) {
	// 获取训练营排行榜
	ranking, err := s.childPointsRepo.GetCampRankingByParticipationID(participationID, rankType, 0) // 0表示不限制数量
	if err != nil {
		return 0, err
	}

	// 查找当前参与记录的排名
	for i, points := range ranking {
		if uint(points.ParticipationID) == participationID {
			return i + 1, nil // 排名从1开始
		}
	}

	return 0, errcode.ErrDataNotFound.WithDetails("未找到排名信息")
}

// UpdateCampRanking 更新训练营排名
func (s *pointsService) UpdateCampRanking(campID uint) error {
	return s.childPointsRepo.UpdateCampRanking(campID)
}

// ==================== 统计相关 ====================

// GetCampStatistics 获取训练营统计信息
func (s *pointsService) GetCampStatistics(campID uint) (*models.CampStatistics, error) {
	return s.childPointsRepo.GetCampStatistics(campID)
}

// CalculateWeeklyPoints 计算周积分
func (s *pointsService) CalculateWeeklyPoints(participationID uint) error {
	// 获取本周的开始和结束时间
	now := time.Now()
	weekStart := now.AddDate(0, 0, -int(now.Weekday()))
	weekStart = time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, weekStart.Location())
	weekEnd := weekStart.AddDate(0, 0, 7)

	// 查询本周的积分记录
	weeklyRecords, err := s.pointRecordsRepo.GetByParticipationIDAndTimeRange(participationID, weekStart, weekEnd)
	if err != nil {
		return err
	}

	// 计算本周总积分
	var weekPoints int
	for _, record := range weeklyRecords {
		if record.PointsChange > 0 {
			weekPoints += record.PointsChange
		}
	}

	// 更新积分记录
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		return err
	}

	childPoints.WeekPoints = weekPoints
	return s.childPointsRepo.Update(childPoints.ID, childPoints)
}

// CalculateMonthlyPoints 计算月积分
func (s *pointsService) CalculateMonthlyPoints(participationID uint) error {
	// 获取本月的开始和结束时间
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, 0)

	// 查询本月的积分记录
	monthlyRecords, err := s.pointRecordsRepo.GetByParticipationIDAndTimeRange(participationID, monthStart, monthEnd)
	if err != nil {
		return err
	}

	// 计算本月总积分
	var monthPoints int
	for _, record := range monthlyRecords {
		if record.PointsChange > 0 {
			monthPoints += record.PointsChange
		}
	}

	// 更新积分记录
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		return err
	}

	childPoints.MonthPoints = monthPoints
	return s.childPointsRepo.Update(childPoints.ID, childPoints)
}

// ==================== 积分记录 ====================

// GetPointsHistory 获取积分历史记录
func (s *pointsService) GetPointsHistory(participationID uint, offset, limit int) ([]*models.PointRecord, int64, error) {
	return s.pointRecordsRepo.GetByParticipationID(participationID, offset, limit)
}

// GetPointsHistoryByTimeRange 根据时间范围获取积分历史记录
func (s *pointsService) GetPointsHistoryByTimeRange(participationID uint, startTime, endTime time.Time) ([]*models.PointRecord, error) {
	return s.pointRecordsRepo.GetByParticipationIDAndTimeRange(participationID, startTime, endTime)
}

// ==================== 私有方法 ====================

// updateChildGlobalPoints 更新孩子的全局积分统计
func (s *pointsService) updateChildGlobalPoints(childID int64) error {
	// 获取孩子在所有训练营的积分记录
	allCampPoints, err := s.childPointsRepo.GetByChildID(uint(childID))
	if err != nil {
		return err
	}

	// 计算全局统计
	var totalPoints int64
	var totalCheckins int
	for _, points := range allCampPoints {
		totalPoints += points.TotalPoints
		totalCheckins += points.TotalCheckins
	}

	// 更新孩子表的全局统计字段
	child := &models.Children{
		TotalPoints:   totalPoints,
		TotalCheckins: totalCheckins,
	}

	return s.childrenRepo.Update(uint(childID), child)
}

// ==================== 更新后的 MedalsService 接口 ====================

// MedalsService 勋章服务接口
type MedalsService interface {
	// 核心勋章操作（基于participation_id）
	CheckAndUnlockMedals(participationID uint) error
	UnlockMedal(participationID uint, medalID uint, pointsEarned int) error
	UpdateMedalProgress(participationID uint, medalID uint, progress int) error

	// 查询方法
	GetMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
	GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
	GetMedalProgress(participationID uint, medalID uint) (*models.ChildMedals, error)

	// 兼容性方法
	GetAllMedalsByChildID(childID uint) ([]*models.ChildMedals, error)
	GetGlobalMedalStatsByChildID(childID uint) (*models.ChildMedalsGlobalStats, error)

	// 统计方法
	GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error)
	GetMedalLeaderboard(campID uint, medalID uint, limit int) ([]*models.ChildMedals, error)

	// 批量操作
	BatchUpdateMedalProgress(updates []models.MedalProgressUpdate) error
	InitializeMedalsForParticipation(participationID uint) error
}

// ==================== MedalsService 实现 ====================

type medalsService struct {
	childMedalsRepo           repositories.ChildMedalsRepository
	medalsRepo                repositories.MedalsRepository
	childPointsRepo           repositories.ChildPointsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	pointsService             PointsService
}

func NewMedalsService(
	childMedalsRepo repositories.ChildMedalsRepository,
	medalsRepo repositories.MedalsRepository,
	childPointsRepo repositories.ChildPointsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	pointsService PointsService,
) MedalsService {
	return &medalsService{
		childMedalsRepo:           childMedalsRepo,
		medalsRepo:                medalsRepo,
		childPointsRepo:           childPointsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		pointsService:             pointsService,
	}
}

// ==================== 核心勋章操作 ====================

// CheckAndUnlockMedals 检查并解锁勋章
func (s *medalsService) CheckAndUnlockMedals(participationID uint) error {
	// 获取参与记录信息
	participation, err := s.userCampParticipationRepo.GetByID(participationID)
	if err != nil {
		return err
	}

	// 获取积分信息
	childPoints, err := s.childPointsRepo.GetByParticipationID(participationID)
	if err != nil {
		return err
	}

	// 获取所有可用的勋章
	allMedals, err := s.medalsRepo.GetAll()
	if err != nil {
		return err
	}

	// 检查每个勋章的解锁条件
	for _, medal := range allMedals {
		// 获取当前勋章进度
		childMedal, err := s.childMedalsRepo.GetByParticipationIDAndMedalID(participationID, uint(medal.ID))
		if err != nil && err != errcode.ErrDataNotFound {
			continue
		}

		// 如果勋章记录不存在，创建一个
		if childMedal == nil {
			childMedal = &models.ChildMedals{
				ChildID:         participation.ChildID,
				ParticipationID: int64(participationID),
				MedalID:         medal.ID,
				IsUnlocked:      0,
				CurrentProgress: 0,
				TargetProgress:  medal.RequiredValue,
				PointsEarned:    0,
			}
			if err := s.childMedalsRepo.Create(childMedal); err != nil {
				continue
			}
		}

		// 如果已经解锁，跳过
		if childMedal.IsUnlocked == 1 {
			continue
		}

		// 根据勋章类型检查解锁条件
		var currentProgress int
		switch medal.Type {
		case "points":
			currentProgress = int(childPoints.TotalPoints)
		case "checkins":
			currentProgress = childPoints.TotalCheckins
		case "continuous":
			currentProgress = childPoints.ContinuousDays
		default:
			continue
		}

		// 更新进度
		childMedal.CurrentProgress = currentProgress

		// 检查是否达到解锁条件
		if currentProgress >= medal.RequiredValue {
			childMedal.IsUnlocked = 1
			childMedal.UnlockedAt = &time.Time{}
			*childMedal.UnlockedAt = time.Now()
			childMedal.PointsEarned = medal.PointsReward

			// 奖励积分
			if medal.PointsReward > 0 {
				err := s.pointsService.AddPoints(participationID, medal.PointsReward,
					"勋章奖励", "medal", uint(medal.ID))
				if err != nil {
					// 记录日志但不影响勋章解锁
					// log.Error("勋章积分奖励失败", err)
				}
			}
		}

		// 更新勋章记录
		if err := s.childMedalsRepo.Update(childMedal.ID, childMedal); err != nil {
			// 记录日志但继续处理其他勋章
			// log.Error("更新勋章记录失败", err)
		}
	}

	return nil
}
