// =====================================================
// API接口完全重构 - 纯净版本
// 删除所有基于child_id的旧接口，只保留基于participation_id的新接口
// =====================================================

package handlers

import (
	"kids-platform/internal/models"
	"kids-platform/internal/services"
	"kids-platform/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ==================== PointsHandler 纯净版本 ====================

type PointsHandler struct {
	pointsService services.PointsService
	medalsService services.MedalsService
}

func NewPointsHandler(pointsService services.PointsService, medalsService services.MedalsService) *PointsHandler {
	return &PointsHandler{
		pointsService: pointsService,
		medalsService: medalsService,
	}
}

// ==================== 积分核心接口 ====================

// GetPointsByParticipationID 获取训练营参与记录的积分信息
// GET /api/points/participation/:participation_id
func (h *PointsHandler) GetPointsByParticipationID(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	points, err := h.pointsService.GetPointsByParticipationID(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询失败", err.Error())
		return
	}

	response.Success(c, points)
}

// AddPoints 为训练营参与记录添加积分
// POST /api/points/add
func (h *PointsHandler) AddPoints(c *gin.Context) {
	var req models.AddPointsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 验证必要参数
	if req.ParticipationID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID不能为空")
		return
	}

	if req.Points <= 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "积分必须大于0")
		return
	}

	if req.Reason == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "积分原因不能为空")
		return
	}

	// 添加积分
	err := h.pointsService.AddPoints(req.ParticipationID, req.Points, req.Reason, req.SourceType, req.SourceID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "添加积分失败", err.Error())
		return
	}

	// 检查并解锁勋章
	if err := h.medalsService.CheckAndUnlockMedals(req.ParticipationID); err != nil {
		// 勋章检查失败不影响积分添加，只记录日志
		// log.Error("检查勋章失败", err)
	}

	response.Success(c, gin.H{"message": "积分添加成功"})
}

// DeductPoints 扣除训练营参与记录的积分
// POST /api/points/deduct
func (h *PointsHandler) DeductPoints(c *gin.Context) {
	var req models.DeductPointsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 验证必要参数
	if req.ParticipationID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID不能为空")
		return
	}

	if req.Points <= 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "扣除积分必须大于0")
		return
	}

	if req.Reason == "" {
		response.Error(c, http.StatusBadRequest, "参数错误", "扣除原因不能为空")
		return
	}

	// 扣除积分
	err := h.pointsService.DeductPoints(req.ParticipationID, req.Points, req.Reason)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "扣除积分失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "积分扣除成功"})
}

// ==================== 排行榜接口 ====================

// GetCampRanking 获取训练营排行榜
// GET /api/points/ranking/camp/:camp_id
func (h *PointsHandler) GetCampRanking(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	// 获取查询参数
	rankType := c.DefaultQuery("type", "total") // total, week, month, continuous
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	ranking, err := h.pointsService.GetCampRanking(uint(campID), rankType, limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询排行榜失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"ranking": ranking,
		"type":    rankType,
		"limit":   limit,
	})
}

// GetParticipationRankInCamp 获取参与记录在训练营中的排名
// GET /api/points/ranking/participation/:participation_id
func (h *PointsHandler) GetParticipationRankInCamp(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	rankType := c.DefaultQuery("type", "total")

	rank, err := h.pointsService.GetParticipationRankInCamp(uint(participationID), rankType)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询排名失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"rank": rank,
		"type": rankType,
	})
}

// ==================== 统计接口 ====================

// GetCampStatistics 获取训练营统计信息
// GET /api/points/statistics/camp/:camp_id
func (h *PointsHandler) GetCampStatistics(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	stats, err := h.pointsService.GetCampStatistics(uint(campID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询统计信息失败", err.Error())
		return
	}

	response.Success(c, stats)
}

// GetPointsHistory 获取积分历史记录
// GET /api/points/history/participation/:participation_id
func (h *PointsHandler) GetPointsHistory(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	history, total, err := h.pointsService.GetPointsHistory(uint(participationID), offset, pageSize)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询积分历史失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"history":   history,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// ==================== 管理接口 ====================

// UpdateCampRankings 批量更新训练营排名
// POST /api/points/ranking/camp/:camp_id/update
func (h *PointsHandler) UpdateCampRankings(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	err = h.pointsService.UpdateCampRankings(uint(campID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新排名失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "排名更新成功"})
}

// RecalculateWeeklyPoints 重新计算周积分
// POST /api/points/recalculate/weekly/camp/:camp_id
func (h *PointsHandler) RecalculateWeeklyPoints(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	err = h.pointsService.RecalculateWeeklyPoints(uint(campID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "重新计算周积分失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "周积分重新计算成功"})
}

// RecalculateMonthlyPoints 重新计算月积分
// POST /api/points/recalculate/monthly/camp/:camp_id
func (h *PointsHandler) RecalculateMonthlyPoints(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	err = h.pointsService.RecalculateMonthlyPoints(uint(campID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "重新计算月积分失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "月积分重新计算成功"})
}

// InitializePointsForParticipation 为参与记录初始化积分
// POST /api/points/initialize/:participation_id
func (h *PointsHandler) InitializePointsForParticipation(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	err = h.pointsService.InitializePointsForParticipation(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "初始化积分失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "积分初始化成功"})
}

// ==================== MedalsHandler 纯净版本 ====================

type MedalsHandler struct {
	medalsService services.MedalsService
}

func NewMedalsHandler(medalsService services.MedalsService) *MedalsHandler {
	return &MedalsHandler{
		medalsService: medalsService,
	}
}

// ==================== 勋章核心接口 ====================

// GetMedalsByParticipationID 获取参与记录的所有勋章
// GET /api/medals/participation/:participation_id
func (h *MedalsHandler) GetMedalsByParticipationID(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	medals, err := h.medalsService.GetMedalsByParticipationID(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询勋章失败", err.Error())
		return
	}

	response.Success(c, medals)
}

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
// GET /api/medals/participation/:participation_id/unlocked
func (h *MedalsHandler) GetUnlockedMedalsByParticipationID(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	medals, err := h.medalsService.GetUnlockedMedalsByParticipationID(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询已解锁勋章失败", err.Error())
		return
	}

	response.Success(c, medals)
}

// GetMedalProgress 获取特定勋章的进度
// GET /api/medals/participation/:participation_id/medal/:medal_id
func (h *MedalsHandler) GetMedalProgress(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	medalIDStr := c.Param("medal_id")
	medalID, err := strconv.ParseUint(medalIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "勋章ID格式不正确")
		return
	}

	medal, err := h.medalsService.GetMedalProgress(uint(participationID), uint(medalID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询勋章进度失败", err.Error())
		return
	}

	response.Success(c, medal)
}

// UnlockMedal 手动解锁勋章
// POST /api/medals/unlock
func (h *MedalsHandler) UnlockMedal(c *gin.Context) {
	var req models.UnlockMedalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 验证必要参数
	if req.ParticipationID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID不能为空")
		return
	}

	if req.MedalID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "勋章ID不能为空")
		return
	}

	err := h.medalsService.UnlockMedal(req.ParticipationID, req.MedalID, req.PointsEarned)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "解锁勋章失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "勋章解锁成功"})
}

// UpdateMedalProgress 更新勋章进度
// POST /api/medals/progress/update
func (h *MedalsHandler) UpdateMedalProgress(c *gin.Context) {
	var req models.UpdateMedalProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 验证必要参数
	if req.ParticipationID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID不能为空")
		return
	}

	if req.MedalID == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "勋章ID不能为空")
		return
	}

	err := h.medalsService.UpdateMedalProgress(req.ParticipationID, req.MedalID, req.Progress)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新勋章进度失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "勋章进度更新成功"})
}

// CheckAndUnlockMedals 检查并解锁勋章
// POST /api/medals/check/:participation_id
func (h *MedalsHandler) CheckAndUnlockMedals(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	err = h.medalsService.CheckAndUnlockMedals(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "检查勋章失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "勋章检查完成"})
}

// ==================== 统计和排行榜接口 ====================

// GetCampMedalStatistics 获取训练营勋章统计
// GET /api/medals/statistics/camp/:camp_id
func (h *MedalsHandler) GetCampMedalStatistics(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	stats, err := h.medalsService.GetCampMedalStatistics(uint(campID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询训练营勋章统计失败", err.Error())
		return
	}

	response.Success(c, stats)
}

// GetMedalLeaderboard 获取特定勋章的排行榜
// GET /api/medals/leaderboard/camp/:camp_id/medal/:medal_id
func (h *MedalsHandler) GetMedalLeaderboard(c *gin.Context) {
	campIDStr := c.Param("camp_id")
	campID, err := strconv.ParseUint(campIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
		return
	}

	medalIDStr := c.Param("medal_id")
	medalID, err := strconv.ParseUint(medalIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "勋章ID格式不正确")
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	leaderboard, err := h.medalsService.GetMedalLeaderboard(uint(campID), uint(medalID), limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询勋章排行榜失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"leaderboard": leaderboard,
		"limit":       limit,
	})
}

// ==================== 管理接口 ====================

// InitializeMedalsForParticipation 为参与记录初始化勋章
// POST /api/medals/initialize/:participation_id
func (h *MedalsHandler) InitializeMedalsForParticipation(c *gin.Context) {
	participationIDStr := c.Param("participation_id")
	participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
		return
	}

	err = h.medalsService.InitializeMedalsForParticipation(uint(participationID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "初始化勋章失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "勋章初始化成功"})
}

// BatchUpdateMedalProgress 批量更新勋章进度
// POST /api/medals/progress/batch
func (h *MedalsHandler) BatchUpdateMedalProgress(c *gin.Context) {
	var updates []models.MedalProgressUpdate
	if err := c.ShouldBindJSON(&updates); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	if len(updates) == 0 {
		response.Error(c, http.StatusBadRequest, "参数错误", "更新列表不能为空")
		return
	}

	err := h.medalsService.BatchUpdateMedalProgress(updates)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "批量更新勋章进度失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"message": "批量更新勋章进度成功",
		"count":   len(updates),
	})
}

// ==================== 路由配置 ====================

// RegisterPointsRoutes 注册积分相关路由
func RegisterPointsRoutes(router *gin.RouterGroup, pointsHandler *PointsHandler) {
	points := router.Group("/points")
	{
		// 核心积分接口
		points.GET("/participation/:participation_id", pointsHandler.GetPointsByParticipationID)
		points.POST("/add", pointsHandler.AddPoints)
		points.POST("/deduct", pointsHandler.DeductPoints)

		// 排行榜接口
		points.GET("/ranking/camp/:camp_id", pointsHandler.GetCampRanking)
		points.GET("/ranking/participation/:participation_id", pointsHandler.GetParticipationRankInCamp)

		// 统计接口
		points.GET("/statistics/camp/:camp_id", pointsHandler.GetCampStatistics)
		points.GET("/history/participation/:participation_id", pointsHandler.GetPointsHistory)

		// 管理接口
		points.POST("/ranking/camp/:camp_id/update", pointsHandler.UpdateCampRankings)
		points.POST("/recalculate/weekly/camp/:camp_id", pointsHandler.RecalculateWeeklyPoints)
		points.POST("/recalculate/monthly/camp/:camp_id", pointsHandler.RecalculateMonthlyPoints)
		points.POST("/initialize/:participation_id", pointsHandler.InitializePointsForParticipation)
	}
}

// RegisterMedalsRoutes 注册勋章相关路由
func RegisterMedalsRoutes(router *gin.RouterGroup, medalsHandler *MedalsHandler) {
	medals := router.Group("/medals")
	{
		// 核心勋章接口
		medals.GET("/participation/:participation_id", medalsHandler.GetMedalsByParticipationID)
		medals.GET("/participation/:participation_id/unlocked", medalsHandler.GetUnlockedMedalsByParticipationID)
		medals.GET("/participation/:participation_id/medal/:medal_id", medalsHandler.GetMedalProgress)

		// 勋章操作接口
		medals.POST("/unlock", medalsHandler.UnlockMedal)
		medals.POST("/progress/update", medalsHandler.UpdateMedalProgress)
		medals.POST("/check/:participation_id", medalsHandler.CheckAndUnlockMedals)

		// 统计和排行榜接口
		medals.GET("/statistics/camp/:camp_id", medalsHandler.GetCampMedalStatistics)
		medals.GET("/leaderboard/camp/:camp_id/medal/:medal_id", medalsHandler.GetMedalLeaderboard)

		// 管理接口
		medals.POST("/initialize/:participation_id", medalsHandler.InitializeMedalsForParticipation)
		medals.POST("/progress/batch", medalsHandler.BatchUpdateMedalProgress)
	}
}
