// =====================================================
// Repository层代码调整方案
// 支持基于participation_id的积分和勋章管理
// =====================================================

package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ==================== 更新后的 ChildPointsRepository 接口 ====================

// ChildPointsRepository 孩子积分统计仓储接口
type ChildPointsRepository interface {
	// 基础 CRUD 操作
	Create(childPoints *models.ChildPoints) error
	GetByID(id uint) (*models.ChildPoints, error)
	Update(id uint, childPoints *models.ChildPoints) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.ChildPoints, int64, error)

	// 核心查询方法（基于participation_id）
	GetByParticipationID(participationID uint) (*models.ChildPoints, error)
	CreateOrUpdateByParticipationID(participationID uint, childPoints *models.ChildPoints) error

	// 兼容性方法（为了减少现有代码的修改）
	GetByChildID(childID uint) ([]*models.ChildPoints, error)                     // 返回该孩子所有训练营的积分记录
	GetGlobalStatsByChildID(childID uint) (*models.ChildPointsGlobalStats, error) // 计算全局统计

	// 排行榜方法（基于训练营）
	GetCampRanking(campID uint, rankType string, limit int) ([]*models.ChildPoints, error)
	GetCampRankingByParticipationID(participationID uint, rankType string, limit int) ([]*models.ChildPoints, error)

	// 批量操作
	GetByParticipationIDs(participationIDs []uint) ([]*models.ChildPoints, error)
	GetByCampID(campID uint) ([]*models.ChildPoints, error)

	// 统计方法
	UpdateCampRanking(campID uint) error
	GetCampStatistics(campID uint) (*models.CampStatistics, error)
}

// ==================== ChildPointsRepository 实现 ====================

type childPointsRepository struct {
	db *gorm.DB
}

func NewChildPointsRepository(db *gorm.DB) ChildPointsRepository {
	return &childPointsRepository{db: db}
}

// Create 创建积分记录
func (r *childPointsRepository) Create(childPoints *models.ChildPoints) error {
	if err := r.db.Create(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建积分记录失败")
	}
	return nil
}

// GetByID 根据ID获取积分记录
func (r *childPointsRepository) GetByID(id uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.First(&childPoints, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("积分记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询积分记录失败")
	}
	return &childPoints, nil
}

// Update 更新积分记录
func (r *childPointsRepository) Update(id uint, childPoints *models.ChildPoints) error {
	if err := r.db.Model(&models.ChildPoints{}).Where("id = ?", id).Updates(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新积分记录失败")
	}
	return nil
}

// Delete 删除积分记录
func (r *childPointsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.ChildPoints{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除积分记录失败")
	}
	return nil
}

// List 获取积分记录列表
func (r *childPointsRepository) List(offset, limit int) ([]*models.ChildPoints, int64, error) {
	var childPointss []*models.ChildPoints
	var total int64

	if err := r.db.Model(&models.ChildPoints{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录总数失败")
	}

	if err := r.db.Offset(offset).Limit(limit).Find(&childPointss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录列表失败")
	}

	return childPointss, total, nil
}

// ==================== 核心查询方法 ====================

// GetByParticipationID 根据参与记录ID获取积分记录
func (r *childPointsRepository) GetByParticipationID(participationID uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.Where("participation_id = ?", participationID).First(&childPoints).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营积分记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询训练营积分记录失败")
	}
	return &childPoints, nil
}

// CreateOrUpdateByParticipationID 根据参与记录ID创建或更新积分记录
func (r *childPointsRepository) CreateOrUpdateByParticipationID(participationID uint, childPoints *models.ChildPoints) error {
	// 先尝试查找现有记录
	existing, err := r.GetByParticipationID(participationID)
	if err != nil && err != errcode.ErrDataNotFound {
		return err
	}

	if existing != nil {
		// 更新现有记录
		return r.Update(existing.ID, childPoints)
	} else {
		// 创建新记录
		childPoints.ParticipationID = int64(participationID)
		return r.Create(childPoints)
	}
}

// ==================== 兼容性方法 ====================

// GetByChildID 根据孩子ID获取所有训练营的积分记录
func (r *childPointsRepository) GetByChildID(childID uint) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Where("child_id = ?", childID).Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询孩子积分记录失败")
	}
	return childPoints, nil
}

// GetGlobalStatsByChildID 计算孩子的全局积分统计
func (r *childPointsRepository) GetGlobalStatsByChildID(childID uint) (*models.ChildPointsGlobalStats, error) {
	var stats models.ChildPointsGlobalStats

	// 聚合查询计算全局统计
	err := r.db.Table("child_points").
		Select(`
            ? as child_id,
            COALESCE(SUM(total_points), 0) as total_points,
            COALESCE(SUM(week_points), 0) as week_points,
            COALESCE(SUM(month_points), 0) as month_points,
            COALESCE(SUM(total_checkins), 0) as total_checkins,
            COALESCE(MAX(continuous_days), 0) as max_continuous_days,
            COALESCE(MAX(last_checkin_date), '1970-01-01') as last_checkin_date
        `, childID).
		Where("child_id = ? AND deleted_at IS NULL", childID).
		Scan(&stats).Error

	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("计算全局积分统计失败")
	}

	return &stats, nil
}

// ==================== 排行榜方法 ====================

// GetCampRanking 获取训练营排行榜
func (r *childPointsRepository) GetCampRanking(campID uint, rankType string, limit int) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints

	// 根据排行榜类型确定排序字段
	var orderBy string
	switch rankType {
	case "week":
		orderBy = "week_points DESC"
	case "month":
		orderBy = "month_points DESC"
	case "total":
		orderBy = "total_points DESC"
	case "continuous":
		orderBy = "continuous_days DESC"
	default:
		orderBy = "total_points DESC"
	}

	// 查询同一训练营的所有参与者积分记录
	err := r.db.Table("child_points cp").
		Select("cp.*").
		Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
		Order(orderBy).
		Limit(limit).
		Find(&childPoints).Error

	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营排行榜失败")
	}

	return childPoints, nil
}

// GetCampRankingByParticipationID 根据参与记录ID获取所在训练营的排行榜
func (r *childPointsRepository) GetCampRankingByParticipationID(participationID uint, rankType string, limit int) ([]*models.ChildPoints, error) {
	// 先获取训练营ID
	var campID uint
	if err := r.db.Table("user_camp_participations").
		Select("camp_id").
		Where("id = ?", participationID).
		Scan(&campID).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营信息失败")
	}

	return r.GetCampRanking(campID, rankType, limit)
}

// ==================== 批量操作 ====================

// GetByParticipationIDs 根据参与记录ID列表批量获取积分记录
func (r *childPointsRepository) GetByParticipationIDs(participationIDs []uint) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Where("participation_id IN ?", participationIDs).Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("批量查询积分记录失败")
	}
	return childPoints, nil
}

// GetByCampID 获取训练营的所有积分记录
func (r *childPointsRepository) GetByCampID(campID uint) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints

	err := r.db.Table("child_points cp").
		Select("cp.*").
		Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
		Find(&childPoints).Error

	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营积分记录失败")
	}

	return childPoints, nil
}

// ==================== 统计方法 ====================

// UpdateCampRanking 更新训练营排名
func (r *childPointsRepository) UpdateCampRanking(campID uint) error {
	// 获取训练营的所有积分记录，按周积分排序
	weekRanking, err := r.GetCampRanking(campID, "week", 0) // 0表示不限制数量
	if err != nil {
		return err
	}

	// 更新周排名
	for i, points := range weekRanking {
		rank := i + 1
		if err := r.db.Model(&models.ChildPoints{}).
			Where("id = ?", points.ID).
			Update("week_rank", rank).Error; err != nil {
			return errcode.ErrDatabase.WithDetails("更新周排名失败")
		}
	}

	return nil
}

// GetCampStatistics 获取训练营统计信息
func (r *childPointsRepository) GetCampStatistics(campID uint) (*models.CampStatistics, error) {
	var stats models.CampStatistics

	err := r.db.Table("child_points cp").
		Select(`
            ? as camp_id,
            COUNT(*) as total_participants,
            COALESCE(SUM(cp.total_points), 0) as total_points,
            COALESCE(AVG(cp.total_points), 0) as avg_points,
            COALESCE(MAX(cp.total_points), 0) as max_points,
            COALESCE(SUM(cp.total_checkins), 0) as total_checkins,
            COALESCE(AVG(cp.total_checkins), 0) as avg_checkins
        `, campID).
		Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
		Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
		Scan(&stats).Error

	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询训练营统计信息失败")
	}

	return &stats, nil
}

// ==================== 更新后的 ChildMedalsRepository 接口 ====================

// ChildMedalsRepository 孩子勋章记录仓储接口
type ChildMedalsRepository interface {
	// 基础 CRUD 操作
	Create(childMedals *models.ChildMedals) error
	GetByID(id uint) (*models.ChildMedals, error)
	Update(id uint, childMedals *models.ChildMedals) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.ChildMedals, int64, error)

	// 核心查询方法（基于participation_id）
	GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error)
	GetByParticipationID(participationID uint) ([]*models.ChildMedals, error)
	CreateOrUpdateByParticipationIDAndMedalID(participationID uint, medalID uint, childMedals *models.ChildMedals) error

	// 兼容性方法
	GetByChildID(childID uint) ([]*models.ChildMedals, error)                         // 返回该孩子所有训练营的勋章记录
	GetByChildIDAndMedalID(childID uint, medalID uint) ([]*models.ChildMedals, error) // 返回该孩子在所有训练营中的特定勋章记录

	// 勋章相关查询
	GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
	GetMedalProgressByParticipationID(participationID uint, medalID uint) (*models.ChildMedals, error)
	GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error)

	// 批量操作
	GetByParticipationIDs(participationIDs []uint) ([]*models.ChildMedals, error)
	GetByCampID(campID uint) ([]*models.ChildMedals, error)
	BatchUpdateProgress(updates []models.MedalProgressUpdate) error
}
