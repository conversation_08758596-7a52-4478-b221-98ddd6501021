// =====================================================
// Service层彻底重构 - 纯净版本
// 移除所有聚合查询和兼容性逻辑，确保所有业务逻辑都是训练营级别的
// =====================================================

package services

import (
    "time"
    "kids-platform/internal/models"
    "kids-platform/internal/repositories"
    "kids-platform/pkg/errcode"
    "gorm.io/gorm"
)

// ==================== PointsService 纯净接口 ====================

type PointsService interface {
    // 核心积分操作（基于participation_id）
    GetPointsByParticipationID(participationID uint) (*models.ChildPoints, error)
    AddPoints(participationID uint, points int, reason string, sourceType string, sourceID uint) error
    DeductPoints(participationID uint, points int, reason string) error
    
    // 训练营级别的查询和统计
    GetCampRanking(campID uint, rankType string, limit int) ([]*models.RankingResponse, error)
    GetParticipationRankInCamp(participationID uint, rankType string) (int, error)
    GetCampStatistics(campID uint) (*models.CampStatistics, error)
    
    // 积分历史和记录
    GetPointsHistory(participationID uint, offset int, limit int) ([]*models.PointsHistoryResponse, int64, error)
    
    // 批量操作和维护
    UpdateCampRankings(campID uint) error
    RecalculateWeeklyPoints(campID uint) error
    RecalculateMonthlyPoints(campID uint) error
    
    // 初始化操作
    InitializePointsForParticipation(participationID uint) error
}

// ==================== PointsService 实现 ====================

type pointsService struct {
    pointsRepo       repositories.ChildPointsRepository
    pointRecordsRepo repositories.PointRecordsRepository
    participationRepo repositories.UserCampParticipationRepository
    db               *gorm.DB
}

func NewPointsService(
    pointsRepo repositories.ChildPointsRepository,
    pointRecordsRepo repositories.PointRecordsRepository,
    participationRepo repositories.UserCampParticipationRepository,
    db *gorm.DB,
) PointsService {
    return &pointsService{
        pointsRepo:       pointsRepo,
        pointRecordsRepo: pointRecordsRepo,
        participationRepo: participationRepo,
        db:               db,
    }
}

// GetPointsByParticipationID 获取训练营参与记录的积分信息
func (s *pointsService) GetPointsByParticipationID(participationID uint) (*models.ChildPoints, error) {
    return s.pointsRepo.GetByParticipationID(participationID)
}

// AddPoints 为训练营参与记录添加积分
func (s *pointsService) AddPoints(participationID uint, points int, reason string, sourceType string, sourceID uint) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 获取参与记录信息
        participation, err := s.participationRepo.GetByID(participationID)
        if err != nil {
            return err
        }
        
        // 获取或创建积分记录
        childPoints, err := s.pointsRepo.GetByParticipationID(participationID)
        if err != nil && err != gorm.ErrRecordNotFound {
            return err
        }
        
        if childPoints == nil {
            // 创建新的积分记录
            childPoints = &models.ChildPoints{
                ChildID:         participation.ChildID,
                ParticipationID: int64(participationID),
                TotalPoints:     0,
                WeekPoints:      0,
                MonthPoints:     0,
                TotalCheckins:   0,
                ContinuousDays:  0,
                MaxContinuousDays: 0,
                WeekRank:        0,
                LastWeekRank:    0,
                LastCheckinDate: time.Time{},
                LastUpdatedAt:   time.Now(),
            }
            if err := s.pointsRepo.Create(childPoints); err != nil {
                return err
            }
        }
        
        // 更新积分
        childPoints.TotalPoints += int64(points)
        
        // 如果是本周的积分，更新周积分
        weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday()))
        if time.Now().After(weekStart) {
            childPoints.WeekPoints += points
        }
        
        // 如果是本月的积分，更新月积分
        monthStart := time.Date(time.Now().Year(), time.Now().Month(), 1, 0, 0, 0, 0, time.Now().Location())
        if time.Now().After(monthStart) {
            childPoints.MonthPoints += points
        }
        
        childPoints.LastUpdatedAt = time.Now()
        
        // 更新积分记录
        if err := s.pointsRepo.Update(childPoints.ID, childPoints); err != nil {
            return err
        }
        
        // 创建积分记录
        pointRecord := &models.PointRecords{
            ChildID:    participation.ChildID,
            Points:     points,
            Reason:     reason,
            SourceType: sourceType,
            SourceID:   sourceID,
        }
        
        return s.pointRecordsRepo.Create(pointRecord)
    })
}

// DeductPoints 扣除训练营参与记录的积分
func (s *pointsService) DeductPoints(participationID uint, points int, reason string) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 获取积分记录
        childPoints, err := s.pointsRepo.GetByParticipationID(participationID)
        if err != nil {
            return err
        }
        
        // 检查积分是否足够
        if childPoints.TotalPoints < int64(points) {
            return errcode.ErrInsufficientPoints
        }
        
        // 扣除积分
        childPoints.TotalPoints -= int64(points)
        childPoints.LastUpdatedAt = time.Now()
        
        // 更新积分记录
        if err := s.pointsRepo.Update(childPoints.ID, childPoints); err != nil {
            return err
        }
        
        // 创建扣除记录
        pointRecord := &models.PointRecords{
            ChildID:    childPoints.ChildID,
            Points:     -points, // 负数表示扣除
            Reason:     reason,
            SourceType: "deduction",
            SourceID:   0,
        }
        
        return s.pointRecordsRepo.Create(pointRecord)
    })
}

// GetCampRanking 获取训练营排行榜
func (s *pointsService) GetCampRanking(campID uint, rankType string, limit int) ([]*models.RankingResponse, error) {
    childPoints, err := s.pointsRepo.GetRankingByCampID(campID, rankType, limit)
    if err != nil {
        return nil, err
    }
    
    var rankings []*models.RankingResponse
    for i, points := range childPoints {
        // 获取孩子信息
        child, err := s.getChildInfo(points.ChildID)
        if err != nil {
            continue // 跳过错误的记录
        }
        
        ranking := &models.RankingResponse{
            Rank:            i + 1,
            ParticipationID: uint(points.ParticipationID),
            ChildID:         points.ChildID,
            ChildName:       child.Name,
            Avatar:          child.Avatar,
            Points:          points.TotalPoints,
            Checkins:        points.TotalCheckins,
            ContinuousDays:  points.ContinuousDays,
            RankChange:      points.LastWeekRank - points.WeekRank,
        }
        
        // 根据排行榜类型设置对应的积分
        switch rankType {
        case "week":
            ranking.Points = int64(points.WeekPoints)
        case "month":
            ranking.Points = int64(points.MonthPoints)
        case "continuous":
            ranking.Points = int64(points.ContinuousDays)
        }
        
        rankings = append(rankings, ranking)
    }
    
    return rankings, nil
}

// GetParticipationRankInCamp 获取参与记录在训练营中的排名
func (s *pointsService) GetParticipationRankInCamp(participationID uint, rankType string) (int, error) {
    return s.pointsRepo.GetParticipationRankInCamp(participationID, rankType)
}

// GetCampStatistics 获取训练营统计信息
func (s *pointsService) GetCampStatistics(campID uint) (*models.CampStatistics, error) {
    return s.pointsRepo.GetCampStatistics(campID)
}

// GetPointsHistory 获取积分历史记录
func (s *pointsService) GetPointsHistory(participationID uint, offset int, limit int) ([]*models.PointsHistoryResponse, int64, error) {
    // 获取参与记录信息
    participation, err := s.participationRepo.GetByID(participationID)
    if err != nil {
        return nil, 0, err
    }
    
    // 获取积分历史记录
    pointRecords, total, err := s.pointRecordsRepo.GetByChildIDWithPagination(
        uint(participation.ChildID), offset, limit)
    if err != nil {
        return nil, 0, err
    }
    
    var history []*models.PointsHistoryResponse
    for _, record := range pointRecords {
        historyItem := &models.PointsHistoryResponse{
            ID:         record.ID,
            Points:     record.Points,
            Reason:     record.Reason,
            SourceType: record.SourceType,
            CreatedAt:  record.CreatedAt,
            IsPositive: record.Points > 0,
        }
        
        // 根据来源类型设置来源名称
        if record.SourceType == "checkin" {
            historyItem.SourceName = "每日打卡"
        } else if record.SourceType == "task" {
            historyItem.SourceName = "任务完成"
        } else if record.SourceType == "medal" {
            historyItem.SourceName = "勋章奖励"
        } else {
            historyItem.SourceName = record.SourceType
        }
        
        history = append(history, historyItem)
    }
    
    return history, total, nil
}

// UpdateCampRankings 更新训练营排名
func (s *pointsService) UpdateCampRankings(campID uint) error {
    return s.pointsRepo.BatchUpdateRankings(campID)
}

// RecalculateWeeklyPoints 重新计算训练营周积分
func (s *pointsService) RecalculateWeeklyPoints(campID uint) error {
    return s.pointsRepo.BatchUpdateWeeklyPoints(campID)
}

// RecalculateMonthlyPoints 重新计算训练营月积分
func (s *pointsService) RecalculateMonthlyPoints(campID uint) error {
    return s.pointsRepo.BatchUpdateMonthlyPoints(campID)
}

// InitializePointsForParticipation 为新的参与记录初始化积分
func (s *pointsService) InitializePointsForParticipation(participationID uint) error {
    // 获取参与记录信息
    participation, err := s.participationRepo.GetByID(participationID)
    if err != nil {
        return err
    }
    
    // 检查是否已存在积分记录
    existing, err := s.pointsRepo.GetByParticipationID(participationID)
    if err != nil && err != gorm.ErrRecordNotFound {
        return err
    }
    
    if existing != nil {
        return nil // 已存在，无需初始化
    }
    
    // 创建初始积分记录
    childPoints := &models.ChildPoints{
        ChildID:         participation.ChildID,
        ParticipationID: int64(participationID),
        TotalPoints:     0,
        WeekPoints:      0,
        MonthPoints:     0,
        TotalCheckins:   0,
        ContinuousDays:  0,
        MaxContinuousDays: 0,
        WeekRank:        0,
        LastWeekRank:    0,
        LastCheckinDate: time.Time{},
        LastUpdatedAt:   time.Now(),
    }
    
    return s.pointsRepo.Create(childPoints)
}

// getChildInfo 获取孩子信息（辅助方法）
func (s *pointsService) getChildInfo(childID int64) (*models.Child, error) {
    // 这里需要调用ChildRepository获取孩子信息
    // 为了简化，这里返回一个模拟的结果
    return &models.Child{
        ID:     childID,
        Name:   "孩子姓名", // 实际应该从数据库获取
        Avatar: "",
    }, nil
}

// ==================== MedalsService 纯净接口 ====================

type MedalsService interface {
    // 核心勋章操作（基于participation_id）
    GetMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
    GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
    GetMedalProgress(participationID uint, medalID uint) (*models.ChildMedals, error)
    
    // 勋章解锁和进度更新
    CheckAndUnlockMedals(participationID uint) error
    UnlockMedal(participationID uint, medalID uint, pointsEarned int) error
    UpdateMedalProgress(participationID uint, medalID uint, progress int) error
    
    // 训练营级别的统计
    GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error)
    GetMedalLeaderboard(campID uint, medalID uint, limit int) ([]*models.ChildMedals, error)
    
    // 批量操作
    BatchUpdateMedalProgress(updates []models.MedalProgressUpdate) error
    InitializeMedalsForParticipation(participationID uint) error
}

// ==================== MedalsService 实现 ====================

type medalsService struct {
    medalsRepo        repositories.ChildMedalsRepository
    medalConfigRepo   repositories.MedalsRepository
    participationRepo repositories.UserCampParticipationRepository
    pointsService     PointsService
    db                *gorm.DB
}

func NewMedalsService(
    medalsRepo repositories.ChildMedalsRepository,
    medalConfigRepo repositories.MedalsRepository,
    participationRepo repositories.UserCampParticipationRepository,
    pointsService PointsService,
    db *gorm.DB,
) MedalsService {
    return &medalsService{
        medalsRepo:        medalsRepo,
        medalConfigRepo:   medalConfigRepo,
        participationRepo: participationRepo,
        pointsService:     pointsService,
        db:                db,
    }
}

// GetMedalsByParticipationID 获取参与记录的所有勋章
func (s *medalsService) GetMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    return s.medalsRepo.GetByParticipationID(participationID)
}

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
func (s *medalsService) GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    return s.medalsRepo.GetUnlockedMedalsByParticipationID(participationID)
}

// GetMedalProgress 获取特定勋章的进度
func (s *medalsService) GetMedalProgress(participationID uint, medalID uint) (*models.ChildMedals, error) {
    return s.medalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
}

// CheckAndUnlockMedals 检查并解锁勋章
func (s *medalsService) CheckAndUnlockMedals(participationID uint) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 获取参与记录的积分信息
        points, err := s.pointsService.GetPointsByParticipationID(participationID)
        if err != nil {
            return err
        }
        
        // 获取所有勋章配置
        allMedals, err := s.medalConfigRepo.GetAll()
        if err != nil {
            return err
        }
        
        // 检查每个勋章的解锁条件
        for _, medal := range allMedals {
            childMedal, err := s.medalsRepo.GetByParticipationIDAndMedalID(participationID, uint(medal.ID))
            if err != nil && err != gorm.ErrRecordNotFound {
                continue
            }
            
            // 如果勋章记录不存在，创建一个
            if childMedal == nil {
                childMedal = &models.ChildMedals{
                    ChildID:         points.ChildID,
                    ParticipationID: int64(participationID),
                    MedalID:         medal.ID,
                    IsUnlocked:      0,
                    CurrentProgress: 0,
                    TargetProgress:  medal.RequiredValue,
                    PointsEarned:    0,
                }
                if err := s.medalsRepo.Create(childMedal); err != nil {
                    continue
                }
            }
            
            // 如果已经解锁，跳过
            if childMedal.IsUnlocked == 1 {
                continue
            }
            
            // 检查解锁条件
            shouldUnlock := false
            currentProgress := 0
            
            switch medal.Type {
            case "total_points":
                currentProgress = int(points.TotalPoints)
                shouldUnlock = points.TotalPoints >= int64(medal.RequiredValue)
            case "continuous_days":
                currentProgress = points.ContinuousDays
                shouldUnlock = points.ContinuousDays >= medal.RequiredValue
            case "total_checkins":
                currentProgress = points.TotalCheckins
                shouldUnlock = points.TotalCheckins >= medal.RequiredValue
            }
            
            // 更新进度
            childMedal.CurrentProgress = currentProgress
            
            // 如果满足解锁条件，解锁勋章
            if shouldUnlock {
                childMedal.IsUnlocked = 1
                now := time.Now()
                childMedal.UnlockedAt = &now
                childMedal.PointsEarned = medal.RewardPoints
                
                // 奖励积分
                if medal.RewardPoints > 0 {
                    err := s.pointsService.AddPoints(participationID, medal.RewardPoints, 
                        "勋章奖励", "medal", uint(medal.ID))
                    if err != nil {
                        // 记录日志但不影响勋章解锁
                    }
                }
            }
            
            // 更新勋章记录
            if err := s.medalsRepo.Update(childMedal.ID, childMedal); err != nil {
                continue
            }
        }
        
        return nil
    })
}

// UnlockMedal 手动解锁勋章
func (s *medalsService) UnlockMedal(participationID uint, medalID uint, pointsEarned int) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 获取参与记录信息
        participation, err := s.participationRepo.GetByID(participationID)
        if err != nil {
            return err
        }
        
        // 获取或创建勋章记录
        childMedal, err := s.medalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
        if err != nil && err != gorm.ErrRecordNotFound {
            return err
        }
        
        if childMedal == nil {
            // 创建新的勋章记录
            childMedal = &models.ChildMedals{
                ChildID:         participation.ChildID,
                ParticipationID: int64(participationID),
                MedalID:         int(medalID),
                IsUnlocked:      0,
                CurrentProgress: 0,
                TargetProgress:  0,
                PointsEarned:    0,
            }
            if err := s.medalsRepo.Create(childMedal); err != nil {
                return err
            }
        }
        
        // 解锁勋章
        childMedal.IsUnlocked = 1
        now := time.Now()
        childMedal.UnlockedAt = &now
        childMedal.PointsEarned = pointsEarned
        
        // 更新勋章记录
        if err := s.medalsRepo.Update(childMedal.ID, childMedal); err != nil {
            return err
        }
        
        // 奖励积分
        if pointsEarned > 0 {
            return s.pointsService.AddPoints(participationID, pointsEarned, "勋章奖励", "medal", medalID)
        }
        
        return nil
    })
}

// UpdateMedalProgress 更新勋章进度
func (s *medalsService) UpdateMedalProgress(participationID uint, medalID uint, progress int) error {
    childMedal, err := s.medalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
    if err != nil {
        return err
    }
    
    childMedal.CurrentProgress = progress
    return s.medalsRepo.Update(childMedal.ID, childMedal)
}

// GetCampMedalStatistics 获取训练营勋章统计
func (s *medalsService) GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error) {
    return s.medalsRepo.GetCampMedalStatistics(campID)
}

// GetMedalLeaderboard 获取特定勋章的排行榜
func (s *medalsService) GetMedalLeaderboard(campID uint, medalID uint, limit int) ([]*models.ChildMedals, error) {
    // 获取训练营的所有勋章记录
    allMedals, err := s.medalsRepo.GetByCampID(campID)
    if err != nil {
        return nil, err
    }
    
    // 筛选特定勋章且已解锁的记录
    var medalLeaderboard []*models.ChildMedals
    for _, medal := range allMedals {
        if medal.MedalID == int(medalID) && medal.IsUnlocked == 1 {
            medalLeaderboard = append(medalLeaderboard, medal)
        }
    }
    
    // 限制返回数量
    if limit > 0 && len(medalLeaderboard) > limit {
        medalLeaderboard = medalLeaderboard[:limit]
    }
    
    return medalLeaderboard, nil
}

// BatchUpdateMedalProgress 批量更新勋章进度
func (s *medalsService) BatchUpdateMedalProgress(updates []models.MedalProgressUpdate) error {
    return s.medalsRepo.BatchUpdateProgress(updates)
}

// InitializeMedalsForParticipation 为新的参与记录初始化勋章
func (s *medalsService) InitializeMedalsForParticipation(participationID uint) error {
    // 获取参与记录信息
    participation, err := s.participationRepo.GetByID(participationID)
    if err != nil {
        return err
    }
    
    // 获取所有可用的勋章
    allMedals, err := s.medalConfigRepo.GetAll()
    if err != nil {
        return err
    }
    
    // 为每个勋章创建初始记录
    for _, medal := range allMedals {
        // 检查是否已存在记录
        existing, err := s.medalsRepo.GetByParticipationIDAndMedalID(participationID, uint(medal.ID))
        if err != nil && err != gorm.ErrRecordNotFound {
            continue
        }
        
        if existing != nil {
            continue // 已存在，跳过
        }
        
        // 创建初始勋章记录
        childMedal := &models.ChildMedals{
            ChildID:         participation.ChildID,
            ParticipationID: int64(participationID),
            MedalID:         medal.ID,
            IsUnlocked:      0,
            CurrentProgress: 0,
            TargetProgress:  medal.RequiredValue,
            PointsEarned:    0,
        }
        
        if err := s.medalsRepo.Create(childMedal); err != nil {
            // 记录日志但继续处理其他勋章
        }
    }
    
    return nil
}
