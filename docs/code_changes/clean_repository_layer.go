// =====================================================
// Repository层彻底重构 - 纯净版本
// 完全移除向后兼容性设计，只保留基于participation_id的核心方法
// =====================================================

package repositories

import (
    "kids-platform/internal/models"
    "gorm.io/gorm"
)

// ==================== ChildPointsRepository 纯净接口 ====================

type ChildPointsRepository interface {
    // 核心CRUD操作（基于participation_id）
    GetByParticipationID(participationID uint) (*models.ChildPoints, error)
    Create(childPoints *models.ChildPoints) error
    Update(id uint, childPoints *models.ChildPoints) error
    Delete(id uint) error
    
    // 训练营级别的查询操作
    GetByCampID(campID uint) ([]*models.ChildPoints, error)
    GetRankingByCampID(campID uint, rankType string, limit int) ([]*models.ChildPoints, error)
    GetParticipationRankInCamp(participationID uint, rankType string) (int, error)
    
    // 批量操作
    BatchUpdateWeeklyPoints(campID uint) error
    BatchUpdateMonthlyPoints(campID uint) error
    BatchUpdateRankings(campID uint) error
    
    // 统计操作
    GetCampStatistics(campID uint) (*models.CampStatistics, error)
    CountByCampID(campID uint) (int64, error)
}

// ==================== ChildPointsRepository 实现 ====================

type childPointsRepository struct {
    db *gorm.DB
}

func NewChildPointsRepository(db *gorm.DB) ChildPointsRepository {
    return &childPointsRepository{db: db}
}

// GetByParticipationID 根据参与记录ID获取积分记录
func (r *childPointsRepository) GetByParticipationID(participationID uint) (*models.ChildPoints, error) {
    var childPoints models.ChildPoints
    err := r.db.Where("participation_id = ? AND deleted_at IS NULL", participationID).First(&childPoints).Error
    if err != nil {
        return nil, err
    }
    return &childPoints, nil
}

// Create 创建积分记录
func (r *childPointsRepository) Create(childPoints *models.ChildPoints) error {
    return r.db.Create(childPoints).Error
}

// Update 更新积分记录
func (r *childPointsRepository) Update(id uint, childPoints *models.ChildPoints) error {
    return r.db.Where("id = ? AND deleted_at IS NULL", id).Updates(childPoints).Error
}

// Delete 软删除积分记录
func (r *childPointsRepository) Delete(id uint) error {
    return r.db.Where("id = ?", id).Update("deleted_at", gorm.Expr("NOW()")).Error
}

// GetByCampID 获取训练营的所有积分记录
func (r *childPointsRepository) GetByCampID(campID uint) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    err := r.db.Table("child_points cp").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Find(&childPoints).Error
    return childPoints, err
}

// GetRankingByCampID 获取训练营排行榜
func (r *childPointsRepository) GetRankingByCampID(campID uint, rankType string, limit int) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    
    orderField := "total_points"
    switch rankType {
    case "week":
        orderField = "week_points"
    case "month":
        orderField = "month_points"
    case "continuous":
        orderField = "continuous_days"
    default:
        orderField = "total_points"
    }
    
    err := r.db.Table("child_points cp").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Order(orderField + " DESC").
        Limit(limit).
        Find(&childPoints).Error
    
    return childPoints, err
}

// GetParticipationRankInCamp 获取参与记录在训练营中的排名
func (r *childPointsRepository) GetParticipationRankInCamp(participationID uint, rankType string) (int, error) {
    orderField := "total_points"
    switch rankType {
    case "week":
        orderField = "week_points"
    case "month":
        orderField = "month_points"
    case "continuous":
        orderField = "continuous_days"
    default:
        orderField = "total_points"
    }
    
    var rank int
    err := r.db.Raw(`
        SELECT ranking FROM (
            SELECT 
                cp.participation_id,
                ROW_NUMBER() OVER (ORDER BY cp.`+orderField+` DESC) as ranking
            FROM child_points cp
            JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
            WHERE ucp.camp_id = (
                SELECT camp_id FROM user_camp_participations 
                WHERE id = ? AND deleted_at IS NULL
            )
            AND cp.deleted_at IS NULL 
            AND ucp.deleted_at IS NULL
        ) ranked
        WHERE participation_id = ?
    `, participationID, participationID).Scan(&rank).Error
    
    return rank, err
}

// BatchUpdateWeeklyPoints 批量更新周积分
func (r *childPointsRepository) BatchUpdateWeeklyPoints(campID uint) error {
    return r.db.Exec(`
        UPDATE child_points cp
        JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
        SET cp.week_points = (
            SELECT COALESCE(SUM(pr.points), 0)
            FROM point_records pr
            WHERE pr.child_id = cp.child_id
                AND pr.source_type = 'camp'
                AND pr.source_id = ucp.camp_id
                AND pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND pr.deleted_at IS NULL
        )
        WHERE ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL
    `, campID).Error
}

// BatchUpdateMonthlyPoints 批量更新月积分
func (r *childPointsRepository) BatchUpdateMonthlyPoints(campID uint) error {
    return r.db.Exec(`
        UPDATE child_points cp
        JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
        SET cp.month_points = (
            SELECT COALESCE(SUM(pr.points), 0)
            FROM point_records pr
            WHERE pr.child_id = cp.child_id
                AND pr.source_type = 'camp'
                AND pr.source_id = ucp.camp_id
                AND pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND pr.deleted_at IS NULL
        )
        WHERE ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL
    `, campID).Error
}

// BatchUpdateRankings 批量更新排名
func (r *childPointsRepository) BatchUpdateRankings(campID uint) error {
    // 更新周排名
    err := r.db.Exec(`
        UPDATE child_points cp
        JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
        JOIN (
            SELECT 
                cp2.participation_id,
                ROW_NUMBER() OVER (ORDER BY cp2.week_points DESC) as new_rank
            FROM child_points cp2
            JOIN user_camp_participations ucp2 ON cp2.participation_id = ucp2.id
            WHERE ucp2.camp_id = ? AND cp2.deleted_at IS NULL AND ucp2.deleted_at IS NULL
        ) rankings ON cp.participation_id = rankings.participation_id
        SET 
            cp.last_week_rank = cp.week_rank,
            cp.week_rank = rankings.new_rank
        WHERE ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL
    `, campID, campID).Error
    
    return err
}

// GetCampStatistics 获取训练营统计信息
func (r *childPointsRepository) GetCampStatistics(campID uint) (*models.CampStatistics, error) {
    var stats models.CampStatistics
    err := r.db.Raw(`
        SELECT 
            ? as camp_id,
            COUNT(*) as total_participants,
            COALESCE(SUM(cp.total_points), 0) as total_points,
            COALESCE(AVG(cp.total_points), 0) as avg_points,
            COALESCE(MAX(cp.total_points), 0) as max_points,
            COALESCE(SUM(cp.total_checkins), 0) as total_checkins,
            COALESCE(AVG(cp.total_checkins), 0) as avg_checkins
        FROM child_points cp
        JOIN user_camp_participations ucp ON cp.participation_id = ucp.id
        WHERE ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL
    `, campID, campID).Scan(&stats).Error
    
    return &stats, err
}

// CountByCampID 统计训练营参与人数
func (r *childPointsRepository) CountByCampID(campID uint) (int64, error) {
    var count int64
    err := r.db.Table("child_points cp").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Count(&count).Error
    return count, err
}

// ==================== ChildMedalsRepository 纯净接口 ====================

type ChildMedalsRepository interface {
    // 核心CRUD操作（基于participation_id）
    GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error)
    GetByParticipationID(participationID uint) ([]*models.ChildMedals, error)
    Create(childMedal *models.ChildMedals) error
    Update(id uint, childMedal *models.ChildMedals) error
    Delete(id uint) error
    
    // 训练营级别的查询操作
    GetByCampID(campID uint) ([]*models.ChildMedals, error)
    GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error)
    GetUnlockedMedalsByCampID(campID uint) ([]*models.ChildMedals, error)
    
    // 批量操作
    BatchUpdateProgress(updates []models.MedalProgressUpdate) error
    BatchCreateForParticipation(participationID uint, medalIDs []uint) error
    
    // 统计操作
    GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error)
    CountUnlockedByCampID(campID uint) (int64, error)
}

// ==================== ChildMedalsRepository 实现 ====================

type childMedalsRepository struct {
    db *gorm.DB
}

func NewChildMedalsRepository(db *gorm.DB) ChildMedalsRepository {
    return &childMedalsRepository{db: db}
}

// GetByParticipationIDAndMedalID 根据参与记录ID和勋章ID获取勋章记录
func (r *childMedalsRepository) GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error) {
    var childMedal models.ChildMedals
    err := r.db.Where("participation_id = ? AND medal_id = ? AND deleted_at IS NULL", 
        participationID, medalID).First(&childMedal).Error
    if err != nil {
        return nil, err
    }
    return &childMedal, nil
}

// GetByParticipationID 根据参与记录ID获取所有勋章记录
func (r *childMedalsRepository) GetByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    err := r.db.Where("participation_id = ? AND deleted_at IS NULL", participationID).
        Find(&childMedals).Error
    return childMedals, err
}

// Create 创建勋章记录
func (r *childMedalsRepository) Create(childMedal *models.ChildMedals) error {
    return r.db.Create(childMedal).Error
}

// Update 更新勋章记录
func (r *childMedalsRepository) Update(id uint, childMedal *models.ChildMedals) error {
    return r.db.Where("id = ? AND deleted_at IS NULL", id).Updates(childMedal).Error
}

// Delete 软删除勋章记录
func (r *childMedalsRepository) Delete(id uint) error {
    return r.db.Where("id = ?", id).Update("deleted_at", gorm.Expr("NOW()")).Error
}

// GetByCampID 获取训练营的所有勋章记录
func (r *childMedalsRepository) GetByCampID(campID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    err := r.db.Table("child_medals cm").
        Joins("JOIN user_camp_participations ucp ON cm.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Find(&childMedals).Error
    return childMedals, err
}

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
func (r *childMedalsRepository) GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    err := r.db.Where("participation_id = ? AND is_unlocked = 1 AND deleted_at IS NULL", 
        participationID).Find(&childMedals).Error
    return childMedals, err
}

// GetUnlockedMedalsByCampID 获取训练营的所有已解锁勋章
func (r *childMedalsRepository) GetUnlockedMedalsByCampID(campID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    err := r.db.Table("child_medals cm").
        Joins("JOIN user_camp_participations ucp ON cm.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cm.is_unlocked = 1 AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL", 
            campID).Find(&childMedals).Error
    return childMedals, err
}

// BatchUpdateProgress 批量更新勋章进度
func (r *childMedalsRepository) BatchUpdateProgress(updates []models.MedalProgressUpdate) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        for _, update := range updates {
            err := tx.Model(&models.ChildMedals{}).
                Where("participation_id = ? AND medal_id = ? AND deleted_at IS NULL", 
                    update.ParticipationID, update.MedalID).
                Updates(map[string]interface{}{
                    "current_progress": update.CurrentProgress,
                    "is_unlocked":      update.IsUnlocked,
                    "unlocked_at":      update.UnlockedAt,
                    "points_earned":    update.PointsEarned,
                }).Error
            if err != nil {
                return err
            }
        }
        return nil
    })
}

// BatchCreateForParticipation 为参与记录批量创建勋章记录
func (r *childMedalsRepository) BatchCreateForParticipation(participationID uint, medalIDs []uint) error {
    // 获取参与记录信息
    var participation models.UserCampParticipation
    err := r.db.Where("id = ? AND deleted_at IS NULL", participationID).First(&participation).Error
    if err != nil {
        return err
    }
    
    // 批量创建勋章记录
    var childMedals []models.ChildMedals
    for _, medalID := range medalIDs {
        childMedals = append(childMedals, models.ChildMedals{
            ChildID:         participation.ChildID,
            ParticipationID: int64(participationID),
            MedalID:         int(medalID),
            IsUnlocked:      0,
            CurrentProgress: 0,
            TargetProgress:  0, // 需要从medals表获取
            PointsEarned:    0,
        })
    }
    
    return r.db.CreateInBatches(childMedals, 100).Error
}

// GetCampMedalStatistics 获取训练营勋章统计
func (r *childMedalsRepository) GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error) {
    var stats models.CampMedalStatistics
    err := r.db.Raw(`
        SELECT 
            ? as camp_id,
            COUNT(DISTINCT cm.participation_id) as total_participants,
            COUNT(DISTINCT CASE WHEN cm.is_unlocked = 1 THEN cm.participation_id END) as participants_with_medals,
            COUNT(CASE WHEN cm.is_unlocked = 1 THEN 1 END) as total_unlocked_medals,
            COUNT(DISTINCT CASE WHEN cm.is_unlocked = 1 THEN cm.medal_id END) as unique_medals_unlocked,
            COALESCE(AVG(CASE WHEN cm.is_unlocked = 1 THEN cm.points_earned END), 0) as avg_points_per_medal
        FROM child_medals cm
        JOIN user_camp_participations ucp ON cm.participation_id = ucp.id
        WHERE ucp.camp_id = ? AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL
    `, campID, campID).Scan(&stats).Error
    
    return &stats, err
}

// CountUnlockedByCampID 统计训练营已解锁勋章数量
func (r *childMedalsRepository) CountUnlockedByCampID(campID uint) (int64, error) {
    var count int64
    err := r.db.Table("child_medals cm").
        Joins("JOIN user_camp_participations ucp ON cm.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cm.is_unlocked = 1 AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL", 
            campID).Count(&count).Error
    return count, err
}
