// =====================================================
// 更新后的模型和请求结构体
// 支持基于训练营的积分和勋章管理
// =====================================================

package models

import (
    "time"
)

// ==================== 更新后的 ChildPoints 模型 ====================

// ChildPoints 孩子积分统计模型（基于训练营参与）
type ChildPoints struct {
    ID              uint      `gorm:"primaryKey;autoIncrement" json:"id"`
    ChildID         int64     `gorm:"column:child_id;not null;index" json:"child_id"`
    ParticipationID int64     `gorm:"column:participation_id;not null;uniqueIndex" json:"participation_id"` // 关联user_camp_participations.id
    TotalPoints     int64     `gorm:"column:total_points;not null;default:0" json:"total_points"`
    WeekPoints      int       `gorm:"column:week_points;not null;default:0" json:"week_points"`
    MonthPoints     int       `gorm:"column:month_points;not null;default:0" json:"month_points"`
    TotalCheckins   int       `gorm:"column:total_checkins;not null;default:0" json:"total_checkins"`
    ContinuousDays  int       `gorm:"column:continuous_days;not null;default:0" json:"continuous_days"`
    MaxContinuousDays int     `gorm:"column:max_continuous_days;not null;default:0" json:"max_continuous_days"`
    WeekRank        int       `gorm:"column:week_rank;not null;default:0" json:"week_rank"`
    LastWeekRank    int       `gorm:"column:last_week_rank;not null;default:0" json:"last_week_rank"`
    LastCheckinDate time.Time `gorm:"column:last_checkin_date;not null;default:'1970-01-01'" json:"last_checkin_date"`
    LastUpdatedAt   time.Time `gorm:"column:last_updated_at;not null;default:CURRENT_TIMESTAMP" json:"last_updated_at"`
    CreatedAt       time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
    UpdatedAt       time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
    DeletedAt       *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty"`
}

func (ChildPoints) TableName() string {
    return "child_points"
}

// ==================== 更新后的 ChildMedals 模型 ====================

// ChildMedals 孩子勋章记录模型（基于训练营参与）
type ChildMedals struct {
    ID              uint       `gorm:"primaryKey;autoIncrement" json:"id"`
    ChildID         int64      `gorm:"column:child_id;not null;index" json:"child_id"`
    ParticipationID int64      `gorm:"column:participation_id;not null;index" json:"participation_id"` // 关联user_camp_participations.id
    MedalID         int        `gorm:"column:medal_id;not null" json:"medal_id"`
    IsUnlocked      int        `gorm:"column:is_unlocked;not null;default:0" json:"is_unlocked"`
    CurrentProgress int        `gorm:"column:current_progress;not null;default:0" json:"current_progress"`
    TargetProgress  int        `gorm:"column:target_progress;not null;default:0" json:"target_progress"`
    UnlockedAt      *time.Time `gorm:"column:unlocked_at" json:"unlocked_at,omitempty"`
    PointsEarned    int        `gorm:"column:points_earned;not null;default:0" json:"points_earned"`
    CreatedAt       time.Time  `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
    UpdatedAt       time.Time  `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
    DeletedAt       *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty"`
}

func (ChildMedals) TableName() string {
    return "child_medals"
}

// ==================== 新增的统计模型 ====================

// ChildPointsGlobalStats 孩子全局积分统计
type ChildPointsGlobalStats struct {
    ChildID           int64     `json:"child_id"`
    TotalPoints       int64     `json:"total_points"`
    WeekPoints        int       `json:"week_points"`
    MonthPoints       int       `json:"month_points"`
    TotalCheckins     int       `json:"total_checkins"`
    MaxContinuousDays int       `json:"max_continuous_days"`
    LastCheckinDate   time.Time `json:"last_checkin_date"`
    CampCount         int       `json:"camp_count"` // 参与的训练营数量
}

// ChildMedalsGlobalStats 孩子全局勋章统计
type ChildMedalsGlobalStats struct {
    ChildID           int64 `json:"child_id"`
    TotalMedals       int   `json:"total_medals"`
    UnlockedMedals    int   `json:"unlocked_medals"`
    TotalPointsEarned int   `json:"total_points_earned"`
    CompletionRate    float64 `json:"completion_rate"`
}

// CampStatistics 训练营统计信息
type CampStatistics struct {
    CampID            uint    `json:"camp_id"`
    TotalParticipants int     `json:"total_participants"`
    TotalPoints       int64   `json:"total_points"`
    AvgPoints         float64 `json:"avg_points"`
    MaxPoints         int64   `json:"max_points"`
    TotalCheckins     int     `json:"total_checkins"`
    AvgCheckins       float64 `json:"avg_checkins"`
}

// CampMedalStatistics 训练营勋章统计信息
type CampMedalStatistics struct {
    CampID                uint    `json:"camp_id"`
    TotalParticipants     int     `json:"total_participants"`
    ParticipantsWithMedals int    `json:"participants_with_medals"`
    TotalUnlockedMedals   int     `json:"total_unlocked_medals"`
    UniqueMedalsUnlocked  int     `json:"unique_medals_unlocked"`
    AvgPointsPerMedal     float64 `json:"avg_points_per_medal"`
}

// MedalEarnerStats 勋章获得者统计
type MedalEarnerStats struct {
    ParticipationID   uint  `json:"participation_id"`
    ChildID           int64 `json:"child_id"`
    UnlockedMedals    int   `json:"unlocked_medals"`
    TotalPointsEarned int   `json:"total_points_earned"`
}

// ==================== 请求结构体 ====================

// AddPointsRequest 添加积分请求
type AddPointsRequest struct {
    ParticipationID uint   `json:"participation_id" binding:"required"` // 参与记录ID
    Points          int    `json:"points" binding:"required,gt=0"`       // 积分数量
    Reason          string `json:"reason" binding:"required"`            // 积分原因
    SourceType      string `json:"source_type"`                         // 来源类型：checkin, task, manual等
    SourceID        uint   `json:"source_id"`                           // 来源ID
}

// DeductPointsRequest 扣除积分请求
type DeductPointsRequest struct {
    ParticipationID uint   `json:"participation_id" binding:"required"` // 参与记录ID
    Points          int    `json:"points" binding:"required,gt=0"`       // 扣除积分数量
    Reason          string `json:"reason" binding:"required"`            // 扣除原因
}

// UnlockMedalRequest 解锁勋章请求
type UnlockMedalRequest struct {
    ParticipationID uint `json:"participation_id" binding:"required"` // 参与记录ID
    MedalID         uint `json:"medal_id" binding:"required"`         // 勋章ID
    PointsEarned    int  `json:"points_earned"`                       // 获得积分
}

// UpdateMedalProgressRequest 更新勋章进度请求
type UpdateMedalProgressRequest struct {
    ParticipationID uint `json:"participation_id" binding:"required"` // 参与记录ID
    MedalID         uint `json:"medal_id" binding:"required"`         // 勋章ID
    Progress        int  `json:"progress" binding:"required,gte=0"`   // 当前进度
}

// MedalProgressUpdate 勋章进度更新（批量操作用）
type MedalProgressUpdate struct {
    ParticipationID uint       `json:"participation_id"`
    MedalID         uint       `json:"medal_id"`
    CurrentProgress int        `json:"current_progress"`
    IsUnlocked      int        `json:"is_unlocked"`
    UnlockedAt      *time.Time `json:"unlocked_at,omitempty"`
    PointsEarned    int        `json:"points_earned"`
}

// ==================== 响应结构体 ====================

// ChildPointsResponse 积分记录响应
type ChildPointsResponse struct {
    *ChildPoints
    CampName        string `json:"camp_name,omitempty"`        // 训练营名称
    ChildName       string `json:"child_name,omitempty"`       // 孩子姓名
    CurrentRank     int    `json:"current_rank,omitempty"`     // 当前排名
    RankChange      int    `json:"rank_change,omitempty"`      // 排名变化
    CompletionRate  float64 `json:"completion_rate,omitempty"` // 完成率
}

// ChildMedalsResponse 勋章记录响应
type ChildMedalsResponse struct {
    *ChildMedals
    MedalName       string  `json:"medal_name,omitempty"`       // 勋章名称
    MedalIcon       string  `json:"medal_icon,omitempty"`       // 勋章图标
    MedalDescription string `json:"medal_description,omitempty"` // 勋章描述
    ProgressRate    float64 `json:"progress_rate,omitempty"`    // 进度百分比
    CampName        string  `json:"camp_name,omitempty"`        // 训练营名称
    ChildName       string  `json:"child_name,omitempty"`       // 孩子姓名
}

// RankingResponse 排行榜响应
type RankingResponse struct {
    Rank            int    `json:"rank"`                       // 排名
    ParticipationID uint   `json:"participation_id"`          // 参与记录ID
    ChildID         int64  `json:"child_id"`                  // 孩子ID
    ChildName       string `json:"child_name"`                // 孩子姓名
    Avatar          string `json:"avatar,omitempty"`          // 头像
    Points          int64  `json:"points"`                    // 积分
    Checkins        int    `json:"checkins,omitempty"`        // 打卡次数
    ContinuousDays  int    `json:"continuous_days,omitempty"` // 连续天数
    RankChange      int    `json:"rank_change,omitempty"`     // 排名变化
}

// PointsHistoryResponse 积分历史响应
type PointsHistoryResponse struct {
    ID           uint      `json:"id"`
    Points       int       `json:"points"`
    Reason       string    `json:"reason"`
    SourceType   string    `json:"source_type"`
    SourceName   string    `json:"source_name,omitempty"`
    CreatedAt    time.Time `json:"created_at"`
    IsPositive   bool      `json:"is_positive"` // 是否为正积分
}

// ==================== 兼容性处理 ====================

// 为了保持向后兼容，保留原有的请求结构体，但标记为废弃

// ChildPointsCreateRequest 创建积分记录请求（废弃，使用AddPointsRequest）
// Deprecated: 使用 AddPointsRequest 替代
type ChildPointsCreateRequest struct {
    ChildID         int64 `json:"child_id" binding:"required"`
    ParticipationID int64 `json:"participation_id" binding:"required"`
    TotalPoints     int64 `json:"total_points"`
    WeekPoints      int   `json:"week_points"`
    MonthPoints     int   `json:"month_points"`
    TotalCheckins   int   `json:"total_checkins"`
}

// ChildPointsUpdateRequest 更新积分记录请求（废弃）
// Deprecated: 积分更新应通过AddPoints/DeductPoints接口
type ChildPointsUpdateRequest struct {
    TotalPoints     *int64 `json:"total_points,omitempty"`
    WeekPoints      *int   `json:"week_points,omitempty"`
    MonthPoints     *int   `json:"month_points,omitempty"`
    TotalCheckins   *int   `json:"total_checkins,omitempty"`
    ContinuousDays  *int   `json:"continuous_days,omitempty"`
    MaxContinuousDays *int `json:"max_continuous_days,omitempty"`
}

// ChildMedalsCreateRequest 创建勋章记录请求（废弃，使用UnlockMedalRequest）
// Deprecated: 使用 UnlockMedalRequest 替代
type ChildMedalsCreateRequest struct {
    ChildID         int64 `json:"child_id" binding:"required"`
    ParticipationID int64 `json:"participation_id" binding:"required"`
    MedalID         int   `json:"medal_id" binding:"required"`
    IsUnlocked      int   `json:"is_unlocked"`
    CurrentProgress int   `json:"current_progress"`
    TargetProgress  int   `json:"target_progress"`
}

// ChildMedalsUpdateRequest 更新勋章记录请求（废弃，使用UpdateMedalProgressRequest）
// Deprecated: 使用 UpdateMedalProgressRequest 替代
type ChildMedalsUpdateRequest struct {
    IsUnlocked      *int `json:"is_unlocked,omitempty"`
    CurrentProgress *int `json:"current_progress,omitempty"`
    TargetProgress  *int `json:"target_progress,omitempty"`
    PointsEarned    *int `json:"points_earned,omitempty"`
}
