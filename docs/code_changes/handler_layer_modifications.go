// =====================================================
// Handler层接口调整方案
// 支持基于训练营的积分和勋章管理
// =====================================================

package handlers

import (
    "net/http"
    "strconv"
    "kids-platform/internal/services"
    "kids-platform/internal/models"
    "kids-platform/pkg/response"
    "github.com/gin-gonic/gin"
)

// ==================== 更新后的 PointsHandler ====================

type PointsHandler struct {
    pointsService services.PointsService
    medalsService services.MedalsService
}

func NewPointsHandler(pointsService services.PointsService, medalsService services.MedalsService) *PointsHandler {
    return &PointsHandler{
        pointsService: pointsService,
        medalsService: medalsService,
    }
}

// ==================== 积分相关接口 ====================

// GetPointsByParticipationID 获取训练营参与记录的积分信息
// GET /api/points/participation/:participation_id
func (h *PointsHandler) GetPointsByParticipationID(c *gin.Context) {
    participationIDStr := c.Param("participation_id")
    participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
        return
    }
    
    points, err := h.pointsService.GetPointsByParticipationID(uint(participationID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询失败", err.Error())
        return
    }
    
    response.Success(c, points)
}

// GetGlobalPointsByChildID 获取孩子的全局积分统计（兼容性接口）
// GET /api/points/child/:child_id/global
func (h *PointsHandler) GetGlobalPointsByChildID(c *gin.Context) {
    childIDStr := c.Param("child_id")
    childID, err := strconv.ParseUint(childIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "孩子ID格式不正确")
        return
    }
    
    globalStats, err := h.pointsService.GetGlobalPointsByChildID(uint(childID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询失败", err.Error())
        return
    }
    
    response.Success(c, globalStats)
}

// GetAllCampPointsByChildID 获取孩子在所有训练营的积分记录
// GET /api/points/child/:child_id/camps
func (h *PointsHandler) GetAllCampPointsByChildID(c *gin.Context) {
    childIDStr := c.Param("child_id")
    childID, err := strconv.ParseUint(childIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "孩子ID格式不正确")
        return
    }
    
    campPoints, err := h.pointsService.GetAllCampPointsByChildID(uint(childID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询失败", err.Error())
        return
    }
    
    response.Success(c, campPoints)
}

// AddPoints 为训练营参与记录添加积分
// POST /api/points/add
func (h *PointsHandler) AddPoints(c *gin.Context) {
    var req models.AddPointsRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", err.Error())
        return
    }
    
    // 验证必要参数
    if req.ParticipationID == 0 {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID不能为空")
        return
    }
    
    if req.Points <= 0 {
        response.Error(c, http.StatusBadRequest, "参数错误", "积分必须大于0")
        return
    }
    
    if req.Reason == "" {
        response.Error(c, http.StatusBadRequest, "参数错误", "积分原因不能为空")
        return
    }
    
    // 添加积分
    err := h.pointsService.AddPoints(req.ParticipationID, req.Points, req.Reason, req.SourceType, req.SourceID)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "添加积分失败", err.Error())
        return
    }
    
    // 检查并解锁勋章
    if err := h.medalsService.CheckAndUnlockMedals(req.ParticipationID); err != nil {
        // 勋章检查失败不影响积分添加，只记录日志
        // log.Error("检查勋章失败", err)
    }
    
    response.Success(c, gin.H{"message": "积分添加成功"})
}

// ==================== 排行榜相关接口 ====================

// GetCampRanking 获取训练营排行榜
// GET /api/points/ranking/camp/:camp_id
func (h *PointsHandler) GetCampRanking(c *gin.Context) {
    campIDStr := c.Param("camp_id")
    campID, err := strconv.ParseUint(campIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
        return
    }
    
    // 获取查询参数
    rankType := c.DefaultQuery("type", "total") // total, week, month, continuous
    limitStr := c.DefaultQuery("limit", "10")
    limit, err := strconv.Atoi(limitStr)
    if err != nil {
        limit = 10
    }
    
    ranking, err := h.pointsService.GetCampRanking(uint(campID), rankType, limit)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询排行榜失败", err.Error())
        return
    }
    
    response.Success(c, gin.H{
        "ranking": ranking,
        "type":    rankType,
        "limit":   limit,
    })
}

// GetChildRankInCamp 获取孩子在训练营中的排名
// GET /api/points/ranking/participation/:participation_id
func (h *PointsHandler) GetChildRankInCamp(c *gin.Context) {
    participationIDStr := c.Param("participation_id")
    participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
        return
    }
    
    rankType := c.DefaultQuery("type", "total")
    
    rank, err := h.pointsService.GetChildRankInCamp(uint(participationID), rankType)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询排名失败", err.Error())
        return
    }
    
    response.Success(c, gin.H{
        "rank": rank,
        "type": rankType,
    })
}

// ==================== 统计相关接口 ====================

// GetCampStatistics 获取训练营统计信息
// GET /api/points/statistics/camp/:camp_id
func (h *PointsHandler) GetCampStatistics(c *gin.Context) {
    campIDStr := c.Param("camp_id")
    campID, err := strconv.ParseUint(campIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
        return
    }
    
    stats, err := h.pointsService.GetCampStatistics(uint(campID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询统计信息失败", err.Error())
        return
    }
    
    response.Success(c, stats)
}

// GetPointsHistory 获取积分历史记录
// GET /api/points/history/participation/:participation_id
func (h *PointsHandler) GetPointsHistory(c *gin.Context) {
    participationIDStr := c.Param("participation_id")
    participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
        return
    }
    
    // 获取分页参数
    pageStr := c.DefaultQuery("page", "1")
    pageSizeStr := c.DefaultQuery("page_size", "20")
    
    page, err := strconv.Atoi(pageStr)
    if err != nil || page < 1 {
        page = 1
    }
    
    pageSize, err := strconv.Atoi(pageSizeStr)
    if err != nil || pageSize < 1 || pageSize > 100 {
        pageSize = 20
    }
    
    offset := (page - 1) * pageSize
    
    history, total, err := h.pointsService.GetPointsHistory(uint(participationID), offset, pageSize)
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询积分历史失败", err.Error())
        return
    }
    
    response.Success(c, gin.H{
        "history":   history,
        "total":     total,
        "page":      page,
        "page_size": pageSize,
    })
}

// ==================== 更新后的 MedalsHandler ====================

type MedalsHandler struct {
    medalsService services.MedalsService
}

func NewMedalsHandler(medalsService services.MedalsService) *MedalsHandler {
    return &MedalsHandler{
        medalsService: medalsService,
    }
}

// ==================== 勋章相关接口 ====================

// GetMedalsByParticipationID 获取参与记录的所有勋章
// GET /api/medals/participation/:participation_id
func (h *MedalsHandler) GetMedalsByParticipationID(c *gin.Context) {
    participationIDStr := c.Param("participation_id")
    participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
        return
    }
    
    medals, err := h.medalsService.GetMedalsByParticipationID(uint(participationID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询勋章失败", err.Error())
        return
    }
    
    response.Success(c, medals)
}

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
// GET /api/medals/participation/:participation_id/unlocked
func (h *MedalsHandler) GetUnlockedMedalsByParticipationID(c *gin.Context) {
    participationIDStr := c.Param("participation_id")
    participationID, err := strconv.ParseUint(participationIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "参与记录ID格式不正确")
        return
    }
    
    medals, err := h.medalsService.GetUnlockedMedalsByParticipationID(uint(participationID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询已解锁勋章失败", err.Error())
        return
    }
    
    response.Success(c, medals)
}

// GetAllMedalsByChildID 获取孩子在所有训练营的勋章记录（兼容性接口）
// GET /api/medals/child/:child_id/all
func (h *MedalsHandler) GetAllMedalsByChildID(c *gin.Context) {
    childIDStr := c.Param("child_id")
    childID, err := strconv.ParseUint(childIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "孩子ID格式不正确")
        return
    }
    
    medals, err := h.medalsService.GetAllMedalsByChildID(uint(childID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询勋章记录失败", err.Error())
        return
    }
    
    response.Success(c, medals)
}

// GetGlobalMedalStatsByChildID 获取孩子的全局勋章统计（兼容性接口）
// GET /api/medals/child/:child_id/global-stats
func (h *MedalsHandler) GetGlobalMedalStatsByChildID(c *gin.Context) {
    childIDStr := c.Param("child_id")
    childID, err := strconv.ParseUint(childIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "孩子ID格式不正确")
        return
    }
    
    stats, err := h.medalsService.GetGlobalMedalStatsByChildID(uint(childID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询全局勋章统计失败", err.Error())
        return
    }
    
    response.Success(c, stats)
}

// GetCampMedalStatistics 获取训练营勋章统计
// GET /api/medals/statistics/camp/:camp_id
func (h *MedalsHandler) GetCampMedalStatistics(c *gin.Context) {
    campIDStr := c.Param("camp_id")
    campID, err := strconv.ParseUint(campIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "参数错误", "训练营ID格式不正确")
        return
    }
    
    stats, err := h.medalsService.GetCampMedalStatistics(uint(campID))
    if err != nil {
        response.Error(c, http.StatusInternalServerError, "查询训练营勋章统计失败", err.Error())
        return
    }
    
    response.Success(c, stats)
}
