// =====================================================
// 模型层调整示例
// 为支持基于训练营的积分和勋章体系
// =====================================================

package models

import (
    "time"
)

// ==================== 更新后的 ChildPoints 模型 ====================

// ChildPoints 孩子积分统计表：支持全局统计和训练营级别统计
type ChildPoints struct {
    BaseModel
    ChildID         int64 `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`         // 孩子ID，关联children.id
    ParticipationID int64 `json:"participation_id" gorm:"column:participation_id;not null;default:0"`            // 参与记录ID，0表示全局统计，>0表示特定训练营统计
    TotalPoints     int64 `json:"total_points" gorm:"column:total_points;not null;default:0" validate:"required"` // 总积分
    WeekPoints      int   `json:"week_points" gorm:"column:week_points;not null;default:0" validate:"required"`   // 本周积分
    MonthPoints     int   `json:"month_points" gorm:"column:month_points;not null;default:0" validate:"required"` // 本月积分
    TotalCheckins   int   `json:"total_checkins" gorm:"column:total_checkins;not null;default:0" validate:"required"` // 总打卡次数
    ContinuousDays  int   `json:"continuous_days" gorm:"column:continuous_days;not null;default:0" validate:"required"` // 连续打卡天数
    MaxContinuousDays int `json:"max_continuous_days" gorm:"column:max_continuous_days;not null;default:0" validate:"required"` // 最大连续天数
    WeekRank        int   `json:"week_rank" gorm:"column:week_rank;not null;default:0" validate:"required"`       // 本周排名
    LastWeekRank    int   `json:"last_week_rank" gorm:"column:last_week_rank;not null;default:0" validate:"required"` // 上周排名
    LastCheckinDate time.Time `json:"last_checkin_date" gorm:"column:last_checkin_date;type:date;not null;default:'1970-01-01'" validate:"required"` // 最后打卡日期
    LastUpdatedAt   time.Time `json:"last_updated_at" gorm:"column:last_updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" validate:"required"` // 最后更新时间
}

// TableName 指定表名
func (ChildPoints) TableName() string {
    return "child_points"
}

// IsGlobalStats 判断是否为全局统计记录
func (cp *ChildPoints) IsGlobalStats() bool {
    return cp.ParticipationID == 0
}

// IsCampStats 判断是否为训练营统计记录
func (cp *ChildPoints) IsCampStats() bool {
    return cp.ParticipationID > 0
}

// ==================== 更新后的 ChildMedals 模型 ====================

// ChildMedals 孩子勋章记录表：支持全局勋章和训练营级别勋章
type ChildMedals struct {
    BaseModel
    ChildID         int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`         // 孩子ID，关联children.id
    ParticipationID int64     `json:"participation_id" gorm:"column:participation_id;not null;default:0"`            // 参与记录ID，0表示全局勋章，>0表示特定训练营勋章
    MedalID         int       `json:"medal_id" gorm:"column:medal_id;not null;default:0" validate:"required"`         // 勋章ID，关联medals.id
    IsUnlocked      int8      `json:"is_unlocked" gorm:"column:is_unlocked;not null;default:0" validate:"required"`   // 是否已解锁 0:否 1:是
    CurrentProgress int       `json:"current_progress" gorm:"column:current_progress;not null;default:0" validate:"required"` // 当前进度
    TargetProgress  int       `json:"target_progress" gorm:"column:target_progress;not null;default:0" validate:"required"`   // 目标进度
    UnlockedAt      *time.Time `json:"unlocked_at" gorm:"column:unlocked_at;type:timestamp"`                          // 解锁时间，NULL表示未解锁
    PointsEarned    int       `json:"points_earned" gorm:"column:points_earned;not null;default:0" validate:"required"` // 获得积分
}

// TableName 指定表名
func (ChildMedals) TableName() string {
    return "child_medals"
}

// IsGlobalMedal 判断是否为全局勋章记录
func (cm *ChildMedals) IsGlobalMedal() bool {
    return cm.ParticipationID == 0
}

// IsCampMedal 判断是否为训练营勋章记录
func (cm *ChildMedals) IsCampMedal() bool {
    return cm.ParticipationID > 0
}

// ==================== 新增的请求和响应模型 ====================

// ChildPointsCreateRequest 创建孩子积分统计请求
type ChildPointsCreateRequest struct {
    ChildID         int64 `json:"child_id" validate:"required"`         // 孩子ID
    ParticipationID int64 `json:"participation_id" validate:"min:0"`    // 参与记录ID，0表示全局统计
    TotalPoints     int64 `json:"total_points" validate:"min:0"`        // 总积分
    WeekPoints      int   `json:"week_points" validate:"min:0"`         // 本周积分
    MonthPoints     int   `json:"month_points" validate:"min:0"`        // 本月积分
    TotalCheckins   int   `json:"total_checkins" validate:"min:0"`      // 总打卡次数
    ContinuousDays  int   `json:"continuous_days" validate:"min:0"`     // 连续打卡天数
    MaxContinuousDays int `json:"max_continuous_days" validate:"min:0"` // 最大连续天数
}

// ChildPointsUpdateRequest 更新孩子积分统计请求
type ChildPointsUpdateRequest struct {
    TotalPoints       *int64     `json:"total_points,omitempty" validate:"omitempty,min:0"`        // 总积分
    WeekPoints        *int       `json:"week_points,omitempty" validate:"omitempty,min:0"`         // 本周积分
    MonthPoints       *int       `json:"month_points,omitempty" validate:"omitempty,min:0"`        // 本月积分
    TotalCheckins     *int       `json:"total_checkins,omitempty" validate:"omitempty,min:0"`      // 总打卡次数
    ContinuousDays    *int       `json:"continuous_days,omitempty" validate:"omitempty,min:0"`     // 连续打卡天数
    MaxContinuousDays *int       `json:"max_continuous_days,omitempty" validate:"omitempty,min:0"` // 最大连续天数
    WeekRank          *int       `json:"week_rank,omitempty" validate:"omitempty,min:0"`           // 本周排名
    LastWeekRank      *int       `json:"last_week_rank,omitempty" validate:"omitempty,min:0"`      // 上周排名
    LastCheckinDate   *time.Time `json:"last_checkin_date,omitempty"`                              // 最后打卡日期
    LastUpdatedAt     *time.Time `json:"last_updated_at,omitempty"`                                // 最后更新时间
}

// ChildPointsResponse 孩子积分统计响应
type ChildPointsResponse struct {
    ID                uint      `json:"id"`                  // 积分记录ID
    ChildID           int64     `json:"child_id"`            // 孩子ID
    ParticipationID   int64     `json:"participation_id"`    // 参与记录ID
    TotalPoints       int64     `json:"total_points"`        // 总积分
    WeekPoints        int       `json:"week_points"`         // 本周积分
    MonthPoints       int       `json:"month_points"`        // 本月积分
    TotalCheckins     int       `json:"total_checkins"`      // 总打卡次数
    ContinuousDays    int       `json:"continuous_days"`     // 连续打卡天数
    MaxContinuousDays int       `json:"max_continuous_days"` // 最大连续天数
    WeekRank          int       `json:"week_rank"`           // 本周排名
    LastWeekRank      int       `json:"last_week_rank"`      // 上周排名
    LastCheckinDate   time.Time `json:"last_checkin_date"`   // 最后打卡日期
    LastUpdatedAt     time.Time `json:"last_updated_at"`     // 最后更新时间
    CreatedAt         time.Time `json:"created_at"`          // 创建时间
    UpdatedAt         time.Time `json:"updated_at"`          // 更新时间
    
    // 扩展字段
    IsGlobal bool `json:"is_global"` // 是否为全局统计
    CampName string `json:"camp_name,omitempty"` // 训练营名称（如果是训练营统计）
}

// ChildMedalsCreateRequest 创建孩子勋章记录请求
type ChildMedalsCreateRequest struct {
    ChildID         int64 `json:"child_id" validate:"required"`      // 孩子ID
    ParticipationID int64 `json:"participation_id" validate:"min:0"` // 参与记录ID，0表示全局勋章
    MedalID         int   `json:"medal_id" validate:"required"`      // 勋章ID
    IsUnlocked      int8  `json:"is_unlocked" validate:"oneof=0 1"`  // 是否已解锁
    CurrentProgress int   `json:"current_progress" validate:"min:0"` // 当前进度
    TargetProgress  int   `json:"target_progress" validate:"min:0"`  // 目标进度
    PointsEarned    int   `json:"points_earned" validate:"min:0"`    // 获得积分
}

// ChildMedalsUpdateRequest 更新孩子勋章记录请求
type ChildMedalsUpdateRequest struct {
    IsUnlocked      *int8      `json:"is_unlocked,omitempty" validate:"omitempty,oneof=0 1"` // 是否已解锁
    CurrentProgress *int       `json:"current_progress,omitempty" validate:"omitempty,min:0"` // 当前进度
    TargetProgress  *int       `json:"target_progress,omitempty" validate:"omitempty,min:0"`  // 目标进度
    UnlockedAt      *time.Time `json:"unlocked_at,omitempty"`                                 // 解锁时间
    PointsEarned    *int       `json:"points_earned,omitempty" validate:"omitempty,min:0"`    // 获得积分
}

// ChildMedalsResponse 孩子勋章记录响应
type ChildMedalsResponse struct {
    ID              uint       `json:"id"`               // 勋章记录ID
    ChildID         int64      `json:"child_id"`         // 孩子ID
    ParticipationID int64      `json:"participation_id"` // 参与记录ID
    MedalID         int        `json:"medal_id"`         // 勋章ID
    IsUnlocked      int8       `json:"is_unlocked"`      // 是否已解锁
    CurrentProgress int        `json:"current_progress"` // 当前进度
    TargetProgress  int        `json:"target_progress"`  // 目标进度
    UnlockedAt      *time.Time `json:"unlocked_at"`      // 解锁时间
    PointsEarned    int        `json:"points_earned"`    // 获得积分
    CreatedAt       time.Time  `json:"created_at"`       // 创建时间
    UpdatedAt       time.Time  `json:"updated_at"`       // 更新时间
    
    // 扩展字段
    IsGlobal  bool   `json:"is_global"`            // 是否为全局勋章
    CampName  string `json:"camp_name,omitempty"`  // 训练营名称（如果是训练营勋章）
    MedalName string `json:"medal_name,omitempty"` // 勋章名称
    MedalIcon string `json:"medal_icon,omitempty"` // 勋章图标
}

// ==================== 查询参数模型 ====================

// PointsQueryParams 积分查询参数
type PointsQueryParams struct {
    ChildID         *int64 `json:"child_id,omitempty" form:"child_id"`                 // 孩子ID
    ParticipationID *int64 `json:"participation_id,omitempty" form:"participation_id"` // 参与记录ID
    IsGlobal        *bool  `json:"is_global,omitempty" form:"is_global"`               // 是否查询全局统计
    CampID          *int64 `json:"camp_id,omitempty" form:"camp_id"`                   // 训练营ID
    Offset          int    `json:"offset" form:"offset" validate:"min:0"`              // 偏移量
    Limit           int    `json:"limit" form:"limit" validate:"min:1,max:100"`        // 限制数量
}

// MedalsQueryParams 勋章查询参数
type MedalsQueryParams struct {
    ChildID         *int64 `json:"child_id,omitempty" form:"child_id"`                 // 孩子ID
    ParticipationID *int64 `json:"participation_id,omitempty" form:"participation_id"` // 参与记录ID
    MedalID         *int   `json:"medal_id,omitempty" form:"medal_id"`                 // 勋章ID
    IsUnlocked      *int8  `json:"is_unlocked,omitempty" form:"is_unlocked"`           // 是否已解锁
    IsGlobal        *bool  `json:"is_global,omitempty" form:"is_global"`               // 是否查询全局勋章
    CampID          *int64 `json:"camp_id,omitempty" form:"camp_id"`                   // 训练营ID
    Offset          int    `json:"offset" form:"offset" validate:"min:0"`              // 偏移量
    Limit           int    `json:"limit" form:"limit" validate:"min:1,max:100"`        // 限制数量
}
