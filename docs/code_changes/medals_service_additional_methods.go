// =====================================================
// MedalsService 剩余方法实现
// 支持基于训练营的勋章管理
// =====================================================

package services

import (
    "kids-platform/internal/models"
    "kids-platform/pkg/errcode"
    "time"
)

// ==================== MedalsService 剩余方法实现 ====================

// UnlockMedal 手动解锁勋章
func (s *medalsService) UnlockMedal(participationID uint, medalID uint, pointsEarned int) error {
    // 获取参与记录信息
    participation, err := s.userCampParticipationRepo.GetByID(participationID)
    if err != nil {
        return err
    }
    
    // 获取或创建勋章记录
    childMedal, err := s.childMedalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
    if err != nil && err != errcode.ErrDataNotFound {
        return err
    }
    
    if childMedal == nil {
        // 创建新的勋章记录
        childMedal = &models.ChildMedals{
            ChildID:         participation.ChildID,
            ParticipationID: int64(participationID),
            MedalID:         int(medalID),
            IsUnlocked:      0,
            CurrentProgress: 0,
            TargetProgress:  0,
            PointsEarned:    0,
        }
        if err := s.childMedalsRepo.Create(childMedal); err != nil {
            return err
        }
    }
    
    // 解锁勋章
    childMedal.IsUnlocked = 1
    childMedal.UnlockedAt = &time.Time{}
    *childMedal.UnlockedAt = time.Now()
    childMedal.PointsEarned = pointsEarned
    
    // 更新勋章记录
    if err := s.childMedalsRepo.Update(childMedal.ID, childMedal); err != nil {
        return err
    }
    
    // 奖励积分
    if pointsEarned > 0 {
        return s.pointsService.AddPoints(participationID, pointsEarned, "勋章奖励", "medal", medalID)
    }
    
    return nil
}

// UpdateMedalProgress 更新勋章进度
func (s *medalsService) UpdateMedalProgress(participationID uint, medalID uint, progress int) error {
    // 获取勋章记录
    childMedal, err := s.childMedalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
    if err != nil {
        return err
    }
    
    // 更新进度
    childMedal.CurrentProgress = progress
    
    return s.childMedalsRepo.Update(childMedal.ID, childMedal)
}

// ==================== 查询方法 ====================

// GetMedalsByParticipationID 获取参与记录的所有勋章
func (s *medalsService) GetMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    return s.childMedalsRepo.GetByParticipationID(participationID)
}

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
func (s *medalsService) GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    return s.childMedalsRepo.GetUnlockedMedalsByParticipationID(participationID)
}

// GetMedalProgress 获取特定勋章的进度
func (s *medalsService) GetMedalProgress(participationID uint, medalID uint) (*models.ChildMedals, error) {
    return s.childMedalsRepo.GetByParticipationIDAndMedalID(participationID, medalID)
}

// ==================== 兼容性方法 ====================

// GetAllMedalsByChildID 获取孩子在所有训练营的勋章记录
func (s *medalsService) GetAllMedalsByChildID(childID uint) ([]*models.ChildMedals, error) {
    return s.childMedalsRepo.GetByChildID(childID)
}

// GetGlobalMedalStatsByChildID 计算孩子的全局勋章统计
func (s *medalsService) GetGlobalMedalStatsByChildID(childID uint) (*models.ChildMedalsGlobalStats, error) {
    // 获取孩子在所有训练营的勋章记录
    allMedals, err := s.childMedalsRepo.GetByChildID(childID)
    if err != nil {
        return nil, err
    }
    
    // 计算全局统计
    stats := &models.ChildMedalsGlobalStats{
        ChildID:           int64(childID),
        TotalMedals:       len(allMedals),
        UnlockedMedals:    0,
        TotalPointsEarned: 0,
    }
    
    medalMap := make(map[int]bool) // 用于去重，避免同一勋章在多个训练营中重复计算
    
    for _, medal := range allMedals {
        if medal.IsUnlocked == 1 {
            if !medalMap[medal.MedalID] {
                stats.UnlockedMedals++
                stats.TotalPointsEarned += medal.PointsEarned
                medalMap[medal.MedalID] = true
            }
        }
    }
    
    return stats, nil
}

// ==================== 统计方法 ====================

// GetCampMedalStatistics 获取训练营勋章统计
func (s *medalsService) GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error) {
    return s.childMedalsRepo.GetCampMedalStatistics(campID)
}

// GetMedalLeaderboard 获取特定勋章的排行榜
func (s *medalsService) GetMedalLeaderboard(campID uint, medalID uint, limit int) ([]*models.ChildMedals, error) {
    // 获取训练营的所有勋章记录
    allMedals, err := s.childMedalsRepo.GetByCampID(campID)
    if err != nil {
        return nil, err
    }
    
    // 筛选特定勋章且已解锁的记录
    var medalLeaderboard []*models.ChildMedals
    for _, medal := range allMedals {
        if medal.MedalID == int(medalID) && medal.IsUnlocked == 1 {
            medalLeaderboard = append(medalLeaderboard, medal)
        }
    }
    
    // 按解锁时间排序（最早解锁的排在前面）
    // 这里简化处理，实际应该使用排序算法
    
    // 限制返回数量
    if limit > 0 && len(medalLeaderboard) > limit {
        medalLeaderboard = medalLeaderboard[:limit]
    }
    
    return medalLeaderboard, nil
}

// ==================== 批量操作 ====================

// BatchUpdateMedalProgress 批量更新勋章进度
func (s *medalsService) BatchUpdateMedalProgress(updates []models.MedalProgressUpdate) error {
    return s.childMedalsRepo.BatchUpdateProgress(updates)
}

// InitializeMedalsForParticipation 为新的参与记录初始化勋章
func (s *medalsService) InitializeMedalsForParticipation(participationID uint) error {
    // 获取参与记录信息
    participation, err := s.userCampParticipationRepo.GetByID(participationID)
    if err != nil {
        return err
    }
    
    // 获取所有可用的勋章
    allMedals, err := s.medalsRepo.GetAll()
    if err != nil {
        return err
    }
    
    // 为每个勋章创建初始记录
    for _, medal := range allMedals {
        // 检查是否已存在记录
        existing, err := s.childMedalsRepo.GetByParticipationIDAndMedalID(participationID, uint(medal.ID))
        if err != nil && err != errcode.ErrDataNotFound {
            continue
        }
        
        if existing != nil {
            continue // 已存在，跳过
        }
        
        // 创建初始勋章记录
        childMedal := &models.ChildMedals{
            ChildID:         participation.ChildID,
            ParticipationID: int64(participationID),
            MedalID:         medal.ID,
            IsUnlocked:      0,
            CurrentProgress: 0,
            TargetProgress:  medal.RequiredValue,
            PointsEarned:    0,
        }
        
        if err := s.childMedalsRepo.Create(childMedal); err != nil {
            // 记录日志但继续处理其他勋章
            // log.Error("初始化勋章记录失败", err)
        }
    }
    
    return nil
}

// ==================== 辅助方法 ====================

// GetMedalCompletionRate 获取训练营的勋章完成率
func (s *medalsService) GetMedalCompletionRate(campID uint) (float64, error) {
    stats, err := s.GetCampMedalStatistics(campID)
    if err != nil {
        return 0, err
    }
    
    if stats.TotalParticipants == 0 {
        return 0, nil
    }
    
    return float64(stats.ParticipantsWithMedals) / float64(stats.TotalParticipants), nil
}

// GetTopMedalEarners 获取训练营勋章获得者排行榜
func (s *medalsService) GetTopMedalEarners(campID uint, limit int) ([]*models.MedalEarnerStats, error) {
    // 获取训练营的所有勋章记录
    allMedals, err := s.childMedalsRepo.GetByCampID(campID)
    if err != nil {
        return nil, err
    }
    
    // 按参与记录统计勋章数量
    participationStats := make(map[uint]*models.MedalEarnerStats)
    
    for _, medal := range allMedals {
        participationID := uint(medal.ParticipationID)
        
        if _, exists := participationStats[participationID]; !exists {
            participationStats[participationID] = &models.MedalEarnerStats{
                ParticipationID:   participationID,
                ChildID:           medal.ChildID,
                UnlockedMedals:    0,
                TotalPointsEarned: 0,
            }
        }
        
        if medal.IsUnlocked == 1 {
            participationStats[participationID].UnlockedMedals++
            participationStats[participationID].TotalPointsEarned += medal.PointsEarned
        }
    }
    
    // 转换为切片并排序（这里简化处理）
    var result []*models.MedalEarnerStats
    for _, stats := range participationStats {
        result = append(result, stats)
    }
    
    // 限制返回数量
    if limit > 0 && len(result) > limit {
        result = result[:limit]
    }
    
    return result, nil
}
