// =====================================================
// 路由更新方案
// 支持基于训练营的积分和勋章管理
// =====================================================

package routers

import (
    "kids-platform/internal/handlers"
    "github.com/gin-gonic/gin"
)

// ==================== 更新后的路由配置 ====================

// SetupPointsRoutes 设置积分相关路由
func SetupPointsRoutes(r *gin.RouterGroup, pointsHandler *handlers.PointsHandler) {
    points := r.Group("/points")
    {
        // ==================== 核心积分接口（基于participation_id） ====================
        
        // 获取训练营参与记录的积分信息
        points.GET("/participation/:participation_id", pointsHandler.GetPointsByParticipationID)
        
        // 添加积分
        points.POST("/add", pointsHandler.AddPoints)
        
        // 获取积分历史记录
        points.GET("/history/participation/:participation_id", pointsHandler.GetPointsHistory)
        
        // ==================== 兼容性接口（基于child_id） ====================
        
        // 获取孩子的全局积分统计
        points.GET("/child/:child_id/global", pointsHandler.GetGlobalPointsByChildID)
        
        // 获取孩子在所有训练营的积分记录
        points.GET("/child/:child_id/camps", pointsHandler.GetAllCampPointsByChildID)
        
        // ==================== 排行榜接口 ====================
        
        // 获取训练营排行榜
        // 查询参数: type=total|week|month|continuous, limit=10
        points.GET("/ranking/camp/:camp_id", pointsHandler.GetCampRanking)
        
        // 获取孩子在训练营中的排名
        // 查询参数: type=total|week|month|continuous
        points.GET("/ranking/participation/:participation_id", pointsHandler.GetChildRankInCamp)
        
        // ==================== 统计接口 ====================
        
        // 获取训练营统计信息
        points.GET("/statistics/camp/:camp_id", pointsHandler.GetCampStatistics)
        
        // ==================== 管理接口（可选，用于后台管理） ====================
        
        // 批量更新训练营排名
        points.POST("/ranking/camp/:camp_id/update", pointsHandler.UpdateCampRanking)
        
        // 重新计算周积分
        points.POST("/recalculate/weekly/:participation_id", pointsHandler.RecalculateWeeklyPoints)
        
        // 重新计算月积分
        points.POST("/recalculate/monthly/:participation_id", pointsHandler.RecalculateMonthlyPoints)
    }
}

// SetupMedalsRoutes 设置勋章相关路由
func SetupMedalsRoutes(r *gin.RouterGroup, medalsHandler *handlers.MedalsHandler) {
    medals := r.Group("/medals")
    {
        // ==================== 核心勋章接口（基于participation_id） ====================
        
        // 获取参与记录的所有勋章
        medals.GET("/participation/:participation_id", medalsHandler.GetMedalsByParticipationID)
        
        // 获取参与记录的已解锁勋章
        medals.GET("/participation/:participation_id/unlocked", medalsHandler.GetUnlockedMedalsByParticipationID)
        
        // 获取特定勋章的进度
        medals.GET("/participation/:participation_id/medal/:medal_id", medalsHandler.GetMedalProgress)
        
        // 手动解锁勋章（管理员功能）
        medals.POST("/unlock", medalsHandler.UnlockMedal)
        
        // 更新勋章进度
        medals.POST("/progress/update", medalsHandler.UpdateMedalProgress)
        
        // 检查并解锁勋章
        medals.POST("/check/:participation_id", medalsHandler.CheckAndUnlockMedals)
        
        // ==================== 兼容性接口（基于child_id） ====================
        
        // 获取孩子在所有训练营的勋章记录
        medals.GET("/child/:child_id/all", medalsHandler.GetAllMedalsByChildID)
        
        // 获取孩子的全局勋章统计
        medals.GET("/child/:child_id/global-stats", medalsHandler.GetGlobalMedalStatsByChildID)
        
        // ==================== 统计和排行榜接口 ====================
        
        // 获取训练营勋章统计
        medals.GET("/statistics/camp/:camp_id", medalsHandler.GetCampMedalStatistics)
        
        // 获取特定勋章的排行榜
        // 查询参数: limit=10
        medals.GET("/leaderboard/camp/:camp_id/medal/:medal_id", medalsHandler.GetMedalLeaderboard)
        
        // 获取训练营勋章获得者排行榜
        medals.GET("/earners/camp/:camp_id", medalsHandler.GetTopMedalEarners)
        
        // ==================== 管理接口 ====================
        
        // 为新参与记录初始化勋章
        medals.POST("/initialize/:participation_id", medalsHandler.InitializeMedalsForParticipation)
        
        // 批量更新勋章进度
        medals.POST("/progress/batch", medalsHandler.BatchUpdateMedalProgress)
    }
}

// ==================== 兼容性路由（保持现有接口工作） ====================

// SetupLegacyPointsRoutes 设置兼容性积分路由
// 这些路由保持现有的API接口不变，但内部实现会调用新的服务方法
func SetupLegacyPointsRoutes(r *gin.RouterGroup, pointsHandler *handlers.PointsHandler) {
    // 保持原有的路由路径，但内部实现会适配新的业务逻辑
    
    // 原有的获取孩子积分接口 - 现在返回全局统计
    r.GET("/child_points/child/:child_id", pointsHandler.GetGlobalPointsByChildID)
    
    // 原有的积分排行榜接口 - 需要指定训练营ID
    // 这个接口可能需要前端传递额外的camp_id参数
    r.GET("/child_points/ranking", pointsHandler.GetLegacyRanking)
    
    // 原有的添加积分接口 - 需要适配新的参数结构
    r.POST("/child_points/add", pointsHandler.AddPointsLegacy)
}

// SetupLegacyMedalsRoutes 设置兼容性勋章路由
func SetupLegacyMedalsRoutes(r *gin.RouterGroup, medalsHandler *handlers.MedalsHandler) {
    // 原有的获取孩子勋章接口 - 现在返回所有训练营的勋章
    r.GET("/child_medals/child/:child_id", medalsHandler.GetAllMedalsByChildID)
    
    // 原有的勋章解锁接口 - 需要适配新的参数结构
    r.POST("/child_medals/unlock", medalsHandler.UnlockMedalLegacy)
}

// ==================== 完整的路由设置函数 ====================

// SetupAPIRoutes 设置所有API路由
func SetupAPIRoutes(r *gin.Engine, 
    pointsHandler *handlers.PointsHandler, 
    medalsHandler *handlers.MedalsHandler) {
    
    // API v1 路由组
    v1 := r.Group("/api/v1")
    {
        // 新的基于训练营的接口
        SetupPointsRoutes(v1, pointsHandler)
        SetupMedalsRoutes(v1, medalsHandler)
    }
    
    // 兼容性路由组（保持原有接口）
    legacy := r.Group("/api")
    {
        SetupLegacyPointsRoutes(legacy, pointsHandler)
        SetupLegacyMedalsRoutes(legacy, medalsHandler)
    }
}

// ==================== 中间件建议 ====================

// ParticipationValidationMiddleware 参与记录验证中间件
func ParticipationValidationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 验证participation_id是否存在且有效
        participationID := c.Param("participation_id")
        if participationID == "" {
            c.JSON(400, gin.H{"error": "participation_id is required"})
            c.Abort()
            return
        }
        
        // 这里可以添加更多的验证逻辑
        // 比如检查participation_id是否存在于数据库中
        // 检查当前用户是否有权限访问该参与记录等
        
        c.Next()
    }
}

// CampAccessMiddleware 训练营访问权限中间件
func CampAccessMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 验证用户是否有权限访问指定的训练营
        campID := c.Param("camp_id")
        if campID == "" {
            c.JSON(400, gin.H{"error": "camp_id is required"})
            c.Abort()
            return
        }
        
        // 这里可以添加权限验证逻辑
        // 比如检查用户是否是训练营的参与者或管理员
        
        c.Next()
    }
}

// ==================== 路由使用示例 ====================

/*
使用示例：

// 在main.go或router.go中
func main() {
    r := gin.Default()
    
    // 初始化handlers
    pointsHandler := handlers.NewPointsHandler(pointsService, medalsService)
    medalsHandler := handlers.NewMedalsHandler(medalsService)
    
    // 设置路由
    SetupAPIRoutes(r, pointsHandler, medalsHandler)
    
    // 启动服务
    r.Run(":8080")
}

// 前端调用示例：

// 1. 获取训练营积分信息
GET /api/v1/points/participation/123

// 2. 添加积分
POST /api/v1/points/add
{
    "participation_id": 123,
    "points": 10,
    "reason": "完成打卡",
    "source_type": "checkin",
    "source_id": 456
}

// 3. 获取训练营排行榜
GET /api/v1/points/ranking/camp/789?type=week&limit=20

// 4. 获取勋章信息
GET /api/v1/medals/participation/123

// 5. 获取孩子全局统计（兼容性接口）
GET /api/v1/points/child/456/global
GET /api/v1/medals/child/456/global-stats

*/
