// =====================================================
// Repository 层调整示例
// 为支持基于训练营的积分和勋章体系
// =====================================================

package repositories

import (
    "kids-platform/internal/models"
    "kids-platform/pkg/errcode"
    "gorm.io/gorm"
)

// ==================== 更新后的 ChildPointsRepository 接口 ====================

// ChildPointsRepository 孩子积分统计仓储接口
type ChildPointsRepository interface {
    // 基础 CRUD 操作
    Create(childPoints *models.ChildPoints) error
    GetByID(id uint) (*models.ChildPoints, error)
    Update(id uint, childPoints *models.ChildPoints) error
    Delete(id uint) error
    List(offset, limit int) ([]*models.ChildPoints, int64, error)
    
    // 兼容性方法（保持现有代码工作）
    GetByChildID(childID uint) (*models.ChildPoints, error) // 获取全局统计记录
    GetWeeklyRanking(limit int) ([]*models.ChildPoints, error) // 全局周排行榜
    GetMonthlyRanking(limit int) ([]*models.ChildPoints, error) // 全局月排行榜
    GetAllOrderByWeekPoints() ([]*models.ChildPoints, error) // 全局周积分排序
    
    // 新增方法（支持训练营级别）
    GetByChildIDAndParticipationID(childID uint, participationID uint) (*models.ChildPoints, error)
    GetByParticipationID(participationID uint) (*models.ChildPoints, error)
    GetCampWeeklyRanking(participationID uint, limit int) ([]*models.ChildPoints, error)
    GetCampMonthlyRanking(participationID uint, limit int) ([]*models.ChildPoints, error)
    GetCampAllOrderByWeekPoints(participationID uint) ([]*models.ChildPoints, error)
    
    // 批量查询方法
    GetByChildIDs(childIDs []uint, isGlobal bool) ([]*models.ChildPoints, error)
    GetByParticipationIDs(participationIDs []uint) ([]*models.ChildPoints, error)
    
    // 统计方法
    GetGlobalStats(childID uint) (*models.ChildPoints, error)
    GetCampStats(childID uint, participationID uint) (*models.ChildPoints, error)
    GetAllCampStatsByChildID(childID uint) ([]*models.ChildPoints, error)
}

// ==================== 更新后的 ChildPointsRepository 实现 ====================

type childPointsRepository struct {
    db *gorm.DB
}

func NewChildPointsRepository(db *gorm.DB) ChildPointsRepository {
    return &childPointsRepository{db: db}
}

// Create 创建积分记录
func (r *childPointsRepository) Create(childPoints *models.ChildPoints) error {
    if err := r.db.Create(childPoints).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("创建孩子积分统计记录失败")
    }
    return nil
}

// GetByID 根据ID获取积分记录
func (r *childPointsRepository) GetByID(id uint) (*models.ChildPoints, error) {
    var childPoints models.ChildPoints
    if err := r.db.First(&childPoints, id).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("积分记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询积分记录失败")
    }
    return &childPoints, nil
}

// Update 更新积分记录
func (r *childPointsRepository) Update(id uint, childPoints *models.ChildPoints) error {
    if err := r.db.Model(&models.ChildPoints{}).Where("id = ?", id).Updates(childPoints).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("更新积分记录失败")
    }
    return nil
}

// Delete 删除积分记录
func (r *childPointsRepository) Delete(id uint) error {
    if err := r.db.Delete(&models.ChildPoints{}, id).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("删除积分记录失败")
    }
    return nil
}

// List 获取积分记录列表
func (r *childPointsRepository) List(offset, limit int) ([]*models.ChildPoints, int64, error) {
    var childPointss []*models.ChildPoints
    var total int64

    if err := r.db.Model(&models.ChildPoints{}).Count(&total).Error; err != nil {
        return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录总数失败")
    }

    if err := r.db.Offset(offset).Limit(limit).Find(&childPointss).Error; err != nil {
        return nil, 0, errcode.ErrDatabase.WithDetails("查询积分记录列表失败")
    }

    return childPointss, total, nil
}

// ==================== 兼容性方法 ====================

// GetByChildID 根据孩子ID获取全局积分记录（保持兼容性）
func (r *childPointsRepository) GetByChildID(childID uint) (*models.ChildPoints, error) {
    var childPoints models.ChildPoints
    if err := r.db.Where("child_id = ? AND participation_id = 0", childID).First(&childPoints).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("孩子全局积分记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询孩子全局积分记录失败")
    }
    return &childPoints, nil
}

// GetWeeklyRanking 获取全局周排行榜（保持兼容性）
func (r *childPointsRepository) GetWeeklyRanking(limit int) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    if err := r.db.Where("participation_id = 0").Order("week_points DESC").Limit(limit).Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询全局周排行榜失败")
    }
    return childPoints, nil
}

// GetMonthlyRanking 获取全局月排行榜（保持兼容性）
func (r *childPointsRepository) GetMonthlyRanking(limit int) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    if err := r.db.Where("participation_id = 0").Order("month_points DESC").Limit(limit).Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询全局月排行榜失败")
    }
    return childPoints, nil
}

// GetAllOrderByWeekPoints 获取全局积分记录按周积分排序（保持兼容性）
func (r *childPointsRepository) GetAllOrderByWeekPoints() ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    if err := r.db.Where("participation_id = 0").Order("week_points DESC").Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询全局积分记录失败")
    }
    return childPoints, nil
}

// ==================== 新增方法（支持训练营级别） ====================

// GetByChildIDAndParticipationID 根据孩子ID和参与记录ID获取积分记录
func (r *childPointsRepository) GetByChildIDAndParticipationID(childID uint, participationID uint) (*models.ChildPoints, error) {
    var childPoints models.ChildPoints
    if err := r.db.Where("child_id = ? AND participation_id = ?", childID, participationID).First(&childPoints).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("积分记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询积分记录失败")
    }
    return &childPoints, nil
}

// GetByParticipationID 根据参与记录ID获取积分记录
func (r *childPointsRepository) GetByParticipationID(participationID uint) (*models.ChildPoints, error) {
    var childPoints models.ChildPoints
    if err := r.db.Where("participation_id = ?", participationID).First(&childPoints).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("训练营积分记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询训练营积分记录失败")
    }
    return &childPoints, nil
}

// GetCampWeeklyRanking 获取训练营内周排行榜
func (r *childPointsRepository) GetCampWeeklyRanking(participationID uint, limit int) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    
    // 首先获取训练营ID
    var campID uint
    if err := r.db.Table("user_camp_participations").
        Select("camp_id").
        Where("id = ?", participationID).
        Scan(&campID).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营信息失败")
    }
    
    // 查询同一训练营的所有参与者的积分记录
    if err := r.db.Table("child_points cp").
        Select("cp.*").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.participation_id > 0", campID).
        Order("cp.week_points DESC").
        Limit(limit).
        Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营周排行榜失败")
    }
    
    return childPoints, nil
}

// GetCampMonthlyRanking 获取训练营内月排行榜
func (r *childPointsRepository) GetCampMonthlyRanking(participationID uint, limit int) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    
    // 首先获取训练营ID
    var campID uint
    if err := r.db.Table("user_camp_participations").
        Select("camp_id").
        Where("id = ?", participationID).
        Scan(&campID).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营信息失败")
    }
    
    // 查询同一训练营的所有参与者的积分记录
    if err := r.db.Table("child_points cp").
        Select("cp.*").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.participation_id > 0", campID).
        Order("cp.month_points DESC").
        Limit(limit).
        Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营月排行榜失败")
    }
    
    return childPoints, nil
}

// GetCampAllOrderByWeekPoints 获取训练营内所有积分记录按周积分排序
func (r *childPointsRepository) GetCampAllOrderByWeekPoints(participationID uint) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    
    // 首先获取训练营ID
    var campID uint
    if err := r.db.Table("user_camp_participations").
        Select("camp_id").
        Where("id = ?", participationID).
        Scan(&campID).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营信息失败")
    }
    
    // 查询同一训练营的所有参与者的积分记录
    if err := r.db.Table("child_points cp").
        Select("cp.*").
        Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cp.participation_id > 0", campID).
        Order("cp.week_points DESC").
        Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营积分记录失败")
    }
    
    return childPoints, nil
}

// ==================== 批量查询方法 ====================

// GetByChildIDs 根据孩子ID列表批量获取积分记录
func (r *childPointsRepository) GetByChildIDs(childIDs []uint, isGlobal bool) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    query := r.db.Where("child_id IN ?", childIDs)
    
    if isGlobal {
        query = query.Where("participation_id = 0")
    } else {
        query = query.Where("participation_id > 0")
    }
    
    if err := query.Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("批量查询积分记录失败")
    }
    
    return childPoints, nil
}

// GetByParticipationIDs 根据参与记录ID列表批量获取积分记录
func (r *childPointsRepository) GetByParticipationIDs(participationIDs []uint) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    if err := r.db.Where("participation_id IN ?", participationIDs).Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("批量查询训练营积分记录失败")
    }
    return childPoints, nil
}

// ==================== 统计方法 ====================

// GetGlobalStats 获取孩子的全局积分统计
func (r *childPointsRepository) GetGlobalStats(childID uint) (*models.ChildPoints, error) {
    return r.GetByChildID(childID)
}

// GetCampStats 获取孩子在特定训练营的积分统计
func (r *childPointsRepository) GetCampStats(childID uint, participationID uint) (*models.ChildPoints, error) {
    return r.GetByChildIDAndParticipationID(childID, participationID)
}

// GetAllCampStatsByChildID 获取孩子在所有训练营的积分统计
func (r *childPointsRepository) GetAllCampStatsByChildID(childID uint) ([]*models.ChildPoints, error) {
    var childPoints []*models.ChildPoints
    if err := r.db.Where("child_id = ? AND participation_id > 0", childID).Find(&childPoints).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询孩子训练营积分记录失败")
    }
    return childPoints, nil
}
