// =====================================================
// ChildMedalsRepository 实现代码
// 支持基于participation_id的勋章管理
// =====================================================

package repositories

import (
    "kids-platform/internal/models"
    "kids-platform/pkg/errcode"
    "gorm.io/gorm"
)

// ==================== ChildMedalsRepository 实现 ====================

type childMedalsRepository struct {
    db *gorm.DB
}

func NewChildMedalsRepository(db *gorm.DB) ChildMedalsRepository {
    return &childMedalsRepository{db: db}
}

// Create 创建勋章记录
func (r *childMedalsRepository) Create(childMedals *models.ChildMedals) error {
    if err := r.db.Create(childMedals).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("创建勋章记录失败")
    }
    return nil
}

// GetByID 根据ID获取勋章记录
func (r *childMedalsRepository) GetByID(id uint) (*models.ChildMedals, error) {
    var childMedals models.ChildMedals
    if err := r.db.First(&childMedals, id).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("勋章记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询勋章记录失败")
    }
    return &childMedals, nil
}

// Update 更新勋章记录
func (r *childMedalsRepository) Update(id uint, childMedals *models.ChildMedals) error {
    if err := r.db.Model(&models.ChildMedals{}).Where("id = ?", id).Updates(childMedals).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("更新勋章记录失败")
    }
    return nil
}

// Delete 删除勋章记录
func (r *childMedalsRepository) Delete(id uint) error {
    if err := r.db.Delete(&models.ChildMedals{}, id).Error; err != nil {
        return errcode.ErrDatabase.WithDetails("删除勋章记录失败")
    }
    return nil
}

// List 获取勋章记录列表
func (r *childMedalsRepository) List(offset, limit int) ([]*models.ChildMedals, int64, error) {
    var childMedalss []*models.ChildMedals
    var total int64

    if err := r.db.Model(&models.ChildMedals{}).Count(&total).Error; err != nil {
        return nil, 0, errcode.ErrDatabase.WithDetails("查询勋章记录总数失败")
    }

    if err := r.db.Offset(offset).Limit(limit).Find(&childMedalss).Error; err != nil {
        return nil, 0, errcode.ErrDatabase.WithDetails("查询勋章记录列表失败")
    }

    return childMedalss, total, nil
}

// ==================== 核心查询方法 ====================

// GetByParticipationIDAndMedalID 根据参与记录ID和勋章ID获取勋章记录
func (r *childMedalsRepository) GetByParticipationIDAndMedalID(participationID uint, medalID uint) (*models.ChildMedals, error) {
    var childMedals models.ChildMedals
    if err := r.db.Where("participation_id = ? AND medal_id = ?", participationID, medalID).First(&childMedals).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            return nil, errcode.ErrDataNotFound.WithDetails("勋章记录不存在")
        }
        return nil, errcode.ErrDatabase.WithDetails("查询勋章记录失败")
    }
    return &childMedals, nil
}

// GetByParticipationID 根据参与记录ID获取所有勋章记录
func (r *childMedalsRepository) GetByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    if err := r.db.Where("participation_id = ?", participationID).Find(&childMedals).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询参与记录勋章失败")
    }
    return childMedals, nil
}

// CreateOrUpdateByParticipationIDAndMedalID 根据参与记录ID和勋章ID创建或更新勋章记录
func (r *childMedalsRepository) CreateOrUpdateByParticipationIDAndMedalID(participationID uint, medalID uint, childMedals *models.ChildMedals) error {
    // 先尝试查找现有记录
    existing, err := r.GetByParticipationIDAndMedalID(participationID, medalID)
    if err != nil && err != errcode.ErrDataNotFound {
        return err
    }
    
    if existing != nil {
        // 更新现有记录
        return r.Update(existing.ID, childMedals)
    } else {
        // 创建新记录
        childMedals.ParticipationID = int64(participationID)
        childMedals.MedalID = int(medalID)
        return r.Create(childMedals)
    }
}

// ==================== 兼容性方法 ====================

// GetByChildID 根据孩子ID获取所有训练营的勋章记录
func (r *childMedalsRepository) GetByChildID(childID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    if err := r.db.Where("child_id = ?", childID).Find(&childMedals).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询孩子勋章记录失败")
    }
    return childMedals, nil
}

// GetByChildIDAndMedalID 返回该孩子在所有训练营中的特定勋章记录
func (r *childMedalsRepository) GetByChildIDAndMedalID(childID uint, medalID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    if err := r.db.Where("child_id = ? AND medal_id = ?", childID, medalID).Find(&childMedals).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询孩子特定勋章记录失败")
    }
    return childMedals, nil
}

// ==================== 勋章相关查询 ====================

// GetUnlockedMedalsByParticipationID 获取参与记录的已解锁勋章
func (r *childMedalsRepository) GetUnlockedMedalsByParticipationID(participationID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    if err := r.db.Where("participation_id = ? AND is_unlocked = 1", participationID).Find(&childMedals).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询已解锁勋章失败")
    }
    return childMedals, nil
}

// GetMedalProgressByParticipationID 获取特定勋章的进度
func (r *childMedalsRepository) GetMedalProgressByParticipationID(participationID uint, medalID uint) (*models.ChildMedals, error) {
    return r.GetByParticipationIDAndMedalID(participationID, medalID)
}

// GetCampMedalStatistics 获取训练营勋章统计信息
func (r *childMedalsRepository) GetCampMedalStatistics(campID uint) (*models.CampMedalStatistics, error) {
    var stats models.CampMedalStatistics
    
    err := r.db.Table("child_medals cm").
        Select(`
            ? as camp_id,
            COUNT(DISTINCT cm.participation_id) as total_participants,
            COUNT(DISTINCT CASE WHEN cm.is_unlocked = 1 THEN cm.participation_id END) as participants_with_medals,
            COUNT(CASE WHEN cm.is_unlocked = 1 THEN 1 END) as total_unlocked_medals,
            COUNT(DISTINCT CASE WHEN cm.is_unlocked = 1 THEN cm.medal_id END) as unique_medals_unlocked,
            COALESCE(AVG(CASE WHEN cm.is_unlocked = 1 THEN cm.points_earned END), 0) as avg_points_per_medal
        `, campID).
        Joins("JOIN user_camp_participations ucp ON cm.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Scan(&stats).Error
    
    if err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营勋章统计失败")
    }
    
    return &stats, nil
}

// ==================== 批量操作 ====================

// GetByParticipationIDs 根据参与记录ID列表批量获取勋章记录
func (r *childMedalsRepository) GetByParticipationIDs(participationIDs []uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    if err := r.db.Where("participation_id IN ?", participationIDs).Find(&childMedals).Error; err != nil {
        return nil, errcode.ErrDatabase.WithDetails("批量查询勋章记录失败")
    }
    return childMedals, nil
}

// GetByCampID 获取训练营的所有勋章记录
func (r *childMedalsRepository) GetByCampID(campID uint) ([]*models.ChildMedals, error) {
    var childMedals []*models.ChildMedals
    
    err := r.db.Table("child_medals cm").
        Select("cm.*").
        Joins("JOIN user_camp_participations ucp ON cm.participation_id = ucp.id").
        Where("ucp.camp_id = ? AND cm.deleted_at IS NULL AND ucp.deleted_at IS NULL", campID).
        Find(&childMedals).Error
    
    if err != nil {
        return nil, errcode.ErrDatabase.WithDetails("查询训练营勋章记录失败")
    }
    
    return childMedals, nil
}

// BatchUpdateProgress 批量更新勋章进度
func (r *childMedalsRepository) BatchUpdateProgress(updates []models.MedalProgressUpdate) error {
    // 使用事务批量更新
    return r.db.Transaction(func(tx *gorm.DB) error {
        for _, update := range updates {
            if err := tx.Model(&models.ChildMedals{}).
                Where("participation_id = ? AND medal_id = ?", update.ParticipationID, update.MedalID).
                Updates(map[string]interface{}{
                    "current_progress": update.CurrentProgress,
                    "is_unlocked":      update.IsUnlocked,
                    "unlocked_at":      update.UnlockedAt,
                    "points_earned":    update.PointsEarned,
                }).Error; err != nil {
                return errcode.ErrDatabase.WithDetails("批量更新勋章进度失败")
            }
        }
        return nil
    })
}
