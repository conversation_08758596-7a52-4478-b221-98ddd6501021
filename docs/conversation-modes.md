# 对话模式系统

## 📋 文档信息
- **创建时间**：2025-07-04
- **版本**：v1.0
- **状态**：✅ 已定义，可执行

## 🎯 模式概述

为了提高对话效率和针对性，建立四种不同场景下的对话模式，每种模式有特定的交互方式和适用场景。

## 🔍 探索模式（开放场景）

### 模式特点
- **目标**：自由讨论，头脑风暴，问题分析
- **风格**：开放性，多选项，启发性
- **节奏**：相对宽松，允许发散思维

### 交互方式
- 我主动提供多个方案选项
- **分析每个方案的优缺点**
- **给出明确的推荐和理由**
- 您选择感兴趣的方向
- 我根据您的选择深入探讨
- 鼓励创意和新想法

### 适用场景
- 项目初期探讨
- 创意讨论
- 问题分析
- 技术选型讨论
- 架构思路探索

### 示例对话
```
我：基于您的需求，我们可以从几个角度来分析：
1. 用户体验角度 - 如何让操作更简单
2. 技术实现角度 - 如何保证性能
3. 商业价值角度 - 如何实现增长
您更想从哪个角度开始讨论？

您：我们先看用户体验吧

我：好的！用户体验方面，我注意到几个关键点...
```

## 📋 需求整理模式

### 模式特点
- **目标**：系统化梳理需求，明确功能范围
- **风格**：结构化，确认导向，细致深入
- **节奏**：有序推进，逐步确认

### 交互方式
- 我提出结构化问题
- 您提供具体回答
- 我整理和归纳
- 您确认理解正确性
- 循环直到需求清晰

### 适用场景
- 功能需求梳理
- 用户故事编写
- 优先级排序
- 验收标准定义
- 需求变更讨论

### 示例对话
```
我：让我们来梳理用户管理模块的需求。首先确认几个基础问题：
1. 用户注册方式有哪些？
2. 用户信息包含哪些字段？
3. 用户状态有哪些类型？

您：注册方式主要是微信授权...

我：明白了，让我整理一下：
- 注册方式：微信授权登录
- 用户信息：昵称、头像、手机号...
这样理解对吗？
```

## 🎨 设计模式

### 模式特点
- **目标**：具体化设计方案，细化实现细节
- **风格**：方案导向，技术聚焦，迭代优化
- **节奏**：聚焦具体，快速迭代

### 交互方式
- 我提供具体设计方案
- 您给出反馈和建议
- 我根据反馈调整方案
- 迭代直到方案满意

### 适用场景
- 系统架构设计
- 数据库设计
- API接口设计
- 界面设计
- 技术方案选择

### 示例对话
```
我：基于需求，我设计了用户表结构：
- id: 主键
- openid: 微信标识
- nickname: 用户昵称
- created_at: 创建时间

您：字段看起来不错，但是缺少用户状态字段

我：您说得对！我补充一个status字段，用枚举类型...
```

## ⚡ 执行模式

### 模式特点
- **目标**：完成具体任务，产出实际结果
- **风格**：任务导向，步骤清晰，结果确认
- **节奏**：高效执行，及时反馈

### 交互方式
- 明确具体任务
- 按步骤执行
- 及时反馈进度
- 验证执行结果

### 适用场景
- 代码实现
- 文档编写
- 环境搭建
- 测试执行
- 部署操作

### 示例对话
```
我：现在开始创建用户管理API，按以下步骤执行：
1. 创建用户模型
2. 实现CRUD操作
3. 添加路由配置
4. 编写测试用例

正在执行第1步...

您：好的，继续

我：第1步完成，用户模型已创建。开始第2步...
```

## 🔄 模式切换机制

### 智能识别触发
我会根据您的话语自动识别意图并建议合适模式：

**触发词识别：**
- 探索模式：分析、讨论、考虑、思考、如何、为什么
- 需求模式：需求、功能、要求、应该、必须、梳理
- 设计模式：设计、方案、架构、结构、如何实现
- 执行模式：开始、实现、创建、执行、做、写

### 便利触发方式

#### 方式1：数字快捷键
```
1 - 探索模式 🔍
2 - 需求模式 📋  
3 - 设计模式 🎨
4 - 执行模式 ⚡
```

#### 方式2：表情符号
```
🔍 - 探索模式
📋 - 需求模式
🎨 - 设计模式  
⚡ - 执行模式
```

#### 方式3：自然语言
```
"换个模式" - 我会询问要切换到哪种
"讨论模式" - 自动进入探索模式
"整理需求" - 自动进入需求模式
"开始设计" - 自动进入设计模式
"开始做" - 自动进入执行模式
```

### 模式提醒机制

#### 对话开始提醒
```
💡 可用模式：🔍探索 📋需求 🎨设计 ⚡执行
当前模式：📋需求整理模式
输入数字或表情可快速切换模式
```

#### 长对话提醒
每20轮对话后提醒当前模式，询问是否需要切换。

#### 模式帮助
输入"模式帮助"或"?"显示所有可用模式和切换方法。

## 📝 模式执行规范

### 模式声明
每次切换模式时明确声明：
```
🔍 已切换到探索模式
让我们开放地讨论这个问题...
```

### 行为调整
- **探索模式**：多提供选项，鼓励发散
- **需求模式**：结构化提问，逐步确认
- **设计模式**：提供具体方案，快速迭代
- **执行模式**：明确步骤，及时反馈

### 模式保持
在同一模式下保持一致的交互风格，直到明确切换。

## 🔧 使用建议

### 模式选择指南
- **不确定方向时** → 探索模式
- **明确要整理内容时** → 需求模式
- **需要具体方案时** → 设计模式
- **准备动手实施时** → 执行模式

### 模式组合使用
可以在一次对话中使用多种模式：
1. 探索模式 - 分析问题
2. 需求模式 - 明确要求
3. 设计模式 - 制定方案
4. 执行模式 - 实施方案

### 模式优化
根据使用效果持续优化：
- 调整触发词
- 完善交互方式
- 增加便利功能

---

**模式系统已就绪！** 您可以随时使用数字、表情或自然语言来切换模式。
