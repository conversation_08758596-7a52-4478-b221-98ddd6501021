# 开发策略：高效、高质量、需求对齐

## 总体策略

### 核心原则
1. **API优先开发** - 以OpenAPI规范为权威参考
2. **测试驱动开发** - 先写测试，再写实现
3. **增量迭代** - 按模块优先级逐步实现
4. **持续集成** - 每个功能完成后立即集成测试

### 开发流程
```
API规范 → 生成存根 → 编写测试 → 实现业务逻辑 → 集成测试 → 部署验证
```

## 1. 开发环境搭建

### 项目结构
```
kids-platform/
├── cmd/                    # 应用入口
│   ├── api/               # API服务入口
│   └── admin/             # 管理后台入口
├── internal/              # 内部包
│   ├── generated/         # 自动生成的代码
│   ├── handler/           # HTTP处理器
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   ├── middleware/        # 中间件
│   └── config/            # 配置管理
├── pkg/                   # 公共包
│   ├── database/          # 数据库连接
│   ├── auth/              # 认证工具
│   ├── logger/            # 日志工具
│   └── validator/         # 验证工具
├── api/                   # API文档
├── scripts/               # 脚本工具
├── tests/                 # 测试文件
└── deployments/           # 部署配置
```

### 开发工具链
```bash
# 代码生成
openapi-generator generate -i api/openapi.yaml -g go-gin-server

# 代码格式化
gofmt -w .
goimports -w .

# 代码检查
golangci-lint run

# 测试运行
go test ./... -v -cover

# 依赖管理
go mod tidy
```

## 2. 分层架构实现

### Handler层 (HTTP处理)
```go
// internal/handler/auth_handler.go
type AuthHandler struct {
    authService service.AuthService
}

func (h *AuthHandler) Login(c *gin.Context) {
    var req LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, ErrorResponse{Code: 400, Message: "参数错误"})
        return
    }
    
    result, err := h.authService.Login(c.Request.Context(), req)
    if err != nil {
        c.JSON(500, ErrorResponse{Code: 500, Message: err.Error()})
        return
    }
    
    c.JSON(200, SuccessResponse{Code: 200, Data: result})
}
```

### Service层 (业务逻辑)
```go
// internal/service/auth_service.go
type AuthService interface {
    Login(ctx context.Context, req LoginRequest) (*LoginResponse, error)
    Register(ctx context.Context, req RegisterRequest) (*RegisterResponse, error)
}

type authService struct {
    userRepo repository.UserRepository
    jwtUtil  pkg.JWTUtil
}

func (s *authService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
    // 1. 验证用户凭据
    user, err := s.userRepo.GetByPhone(ctx, req.Phone)
    if err != nil {
        return nil, errors.New("用户不存在")
    }
    
    // 2. 验证密码
    if !s.verifyPassword(req.Password, user.PasswordHash) {
        return nil, errors.New("密码错误")
    }
    
    // 3. 生成JWT Token
    token, err := s.jwtUtil.GenerateToken(user.ID)
    if err != nil {
        return nil, err
    }
    
    return &LoginResponse{
        Token: token,
        User:  user,
    }, nil
}
```

### Repository层 (数据访问)
```go
// internal/repository/user_repository.go
type UserRepository interface {
    GetByID(ctx context.Context, id int64) (*User, error)
    GetByPhone(ctx context.Context, phone string) (*User, error)
    Create(ctx context.Context, user *User) error
    Update(ctx context.Context, user *User) error
}

type userRepository struct {
    db *sql.DB
}

func (r *userRepository) GetByPhone(ctx context.Context, phone string) (*User, error) {
    query := `SELECT id, phone, password_hash, nickname, avatar_url, created_at 
              FROM users WHERE phone = ? AND deleted_at IS NULL`
    
    var user User
    err := r.db.QueryRowContext(ctx, query, phone).Scan(
        &user.ID, &user.Phone, &user.PasswordHash, 
        &user.Nickname, &user.AvatarURL, &user.CreatedAt,
    )
    
    if err == sql.ErrNoRows {
        return nil, errors.New("用户不存在")
    }
    
    return &user, err
}
```

## 3. 测试策略

### 单元测试
```go
// internal/service/auth_service_test.go
func TestAuthService_Login(t *testing.T) {
    // 准备测试数据
    mockRepo := &MockUserRepository{}
    mockJWT := &MockJWTUtil{}
    service := NewAuthService(mockRepo, mockJWT)
    
    // 测试用例
    tests := []struct {
        name    string
        request LoginRequest
        want    *LoginResponse
        wantErr bool
    }{
        {
            name: "成功登录",
            request: LoginRequest{
                Phone:    "13800138000",
                Password: "password123",
            },
            want: &LoginResponse{
                Token: "mock-jwt-token",
                User:  &User{ID: 1, Phone: "13800138000"},
            },
            wantErr: false,
        },
        {
            name: "密码错误",
            request: LoginRequest{
                Phone:    "13800138000",
                Password: "wrong-password",
            },
            want:    nil,
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := service.Login(context.Background(), tt.request)
            if (err != nil) != tt.wantErr {
                t.Errorf("Login() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("Login() got = %v, want %v", got, tt.want)
            }
        })
    }
}
```

### 集成测试
```go
// tests/integration/auth_test.go
func TestAuthAPI_Integration(t *testing.T) {
    // 启动测试服务器
    server := setupTestServer()
    defer server.Close()
    
    // 测试注册
    registerReq := RegisterRequest{
        Phone:    "13800138000",
        Password: "password123",
        Nickname: "测试用户",
    }
    
    resp, err := http.Post(server.URL+"/api/v1/auth/register", 
        "application/json", 
        bytes.NewBuffer(toJSON(registerReq)))
    
    assert.NoError(t, err)
    assert.Equal(t, 201, resp.StatusCode)
    
    // 测试登录
    loginReq := LoginRequest{
        Phone:    "13800138000",
        Password: "password123",
    }
    
    resp, err = http.Post(server.URL+"/api/v1/auth/login",
        "application/json",
        bytes.NewBuffer(toJSON(loginReq)))
    
    assert.NoError(t, err)
    assert.Equal(t, 200, resp.StatusCode)
    
    var loginResp LoginResponse
    json.NewDecoder(resp.Body).Decode(&loginResp)
    assert.NotEmpty(t, loginResp.Token)
}
```

## 4. 质量保障措施

### 代码质量
1. **静态分析**: golangci-lint检查代码质量
2. **代码覆盖率**: 要求测试覆盖率 > 80%
3. **代码审查**: 所有代码必须经过审查
4. **格式化**: 使用gofmt和goimports统一格式

### API质量
1. **规范一致性**: 实现必须严格遵循OpenAPI规范
2. **响应格式**: 统一的成功/错误响应格式
3. **错误处理**: 完善的错误码和错误信息
4. **性能要求**: API响应时间 < 200ms

### 数据质量
1. **参数验证**: 严格的输入参数验证
2. **数据完整性**: 数据库约束和业务规则验证
3. **事务管理**: 确保数据一致性
4. **审计日志**: 记录关键操作日志

## 5. 需求对齐机制

### 开发前对齐
1. **需求澄清**: 每个功能开发前与产品确认需求
2. **API审查**: 实现前审查API设计是否满足需求
3. **测试用例**: 基于需求编写测试用例

### 开发中对齐
1. **每日同步**: 每日汇报开发进度和问题
2. **功能演示**: 每个功能完成后进行演示
3. **问题反馈**: 及时反馈需求理解偏差

### 开发后验证
1. **功能测试**: 按照需求文档进行功能测试
2. **用户验收**: 邀请目标用户进行验收测试
3. **性能验证**: 验证性能指标是否满足要求

## 6. 开发优先级

### P0 核心功能 (第1周)
1. **认证模块**: 用户注册、登录、JWT认证
2. **用户管理**: 用户资料、孩子档案管理
3. **基础设施**: 数据库连接、日志、配置管理

### P1 主要功能 (第2-3周)
1. **打卡系统**: 视频上传、打卡记录
2. **内容管理**: 视频内容、播放列表
3. **任务系统**: 系统任务、团队任务

### P2 扩展功能 (第4-5周)
1. **社交功能**: 点赞、评论、关注
2. **排行榜**: 个人排行、团队排行
3. **推荐系统**: 内容推荐算法

## 7. 风险控制

### 技术风险
- **依赖管理**: 锁定依赖版本，避免兼容性问题
- **性能风险**: 提前进行性能测试和优化
- **安全风险**: 安全审查和渗透测试

### 进度风险
- **任务分解**: 将大任务分解为小任务
- **并行开发**: 不同模块可以并行开发
- **缓冲时间**: 预留20%的缓冲时间

### 质量风险
- **自动化测试**: 建立完善的自动化测试体系
- **持续集成**: 每次提交都进行自动化测试
- **代码审查**: 严格的代码审查流程

## 8. 测试策略详解

### 测试金字塔
```
    /\     E2E测试 (5%)
   /  \    集成测试 (15%)
  /____\   单元测试 (80%)
```

### 单元测试 (Unit Tests)
**目标**: 测试单个函数/方法的逻辑正确性
**覆盖率要求**: > 80%

```go
// 示例：Service层单元测试
func TestUserService_CreateUser(t *testing.T) {
    // 使用testify/mock创建模拟对象
    mockRepo := new(MockUserRepository)
    mockValidator := new(MockValidator)

    service := NewUserService(mockRepo, mockValidator)

    // 测试数据
    user := &User{
        Phone:    "13800138000",
        Nickname: "测试用户",
        Password: "password123",
    }

    // 设置模拟行为
    mockValidator.On("ValidateUser", user).Return(nil)
    mockRepo.On("Create", mock.Anything, user).Return(nil)

    // 执行测试
    err := service.CreateUser(context.Background(), user)

    // 断言结果
    assert.NoError(t, err)
    mockRepo.AssertExpectations(t)
    mockValidator.AssertExpectations(t)
}
```

### 集成测试 (Integration Tests)
**目标**: 测试多个组件协作的正确性
**重点**: 数据库交互、外部API调用

```go
// 示例：数据库集成测试
func TestUserRepository_Integration(t *testing.T) {
    // 使用测试数据库
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)

    repo := NewUserRepository(db)

    // 测试创建用户
    user := &User{
        Phone:    "13800138000",
        Nickname: "集成测试用户",
    }

    err := repo.Create(context.Background(), user)
    assert.NoError(t, err)
    assert.NotZero(t, user.ID)

    // 测试查询用户
    found, err := repo.GetByPhone(context.Background(), "13800138000")
    assert.NoError(t, err)
    assert.Equal(t, user.Phone, found.Phone)
    assert.Equal(t, user.Nickname, found.Nickname)
}
```

### API测试 (API Tests)
**目标**: 测试HTTP接口的完整性
**工具**: httptest包 + 真实数据库

```go
// 示例：API端到端测试
func TestAuthAPI_E2E(t *testing.T) {
    // 启动测试服务器
    app := setupTestApp(t)
    server := httptest.NewServer(app)
    defer server.Close()

    client := &http.Client{}

    // 测试用户注册
    registerData := map[string]interface{}{
        "phone":    "13800138000",
        "password": "password123",
        "nickname": "E2E测试用户",
    }

    resp := makeRequest(t, client, "POST", server.URL+"/api/v1/auth/register", registerData)
    assert.Equal(t, 201, resp.StatusCode)

    var registerResp RegisterResponse
    json.NewDecoder(resp.Body).Decode(&registerResp)
    assert.NotEmpty(t, registerResp.Token)

    // 测试用户登录
    loginData := map[string]interface{}{
        "phone":    "13800138000",
        "password": "password123",
    }

    resp = makeRequest(t, client, "POST", server.URL+"/api/v1/auth/login", loginData)
    assert.Equal(t, 200, resp.StatusCode)

    var loginResp LoginResponse
    json.NewDecoder(resp.Body).Decode(&loginResp)
    assert.NotEmpty(t, loginResp.Token)

    // 测试受保护的接口
    req, _ := http.NewRequest("GET", server.URL+"/api/v1/users/profile", nil)
    req.Header.Set("Authorization", "Bearer "+loginResp.Token)

    resp, err := client.Do(req)
    assert.NoError(t, err)
    assert.Equal(t, 200, resp.StatusCode)
}
```

### 性能测试 (Performance Tests)
**目标**: 验证系统性能指标
**工具**: Go benchmark + 压力测试工具

```go
// 基准测试
func BenchmarkUserService_Login(b *testing.B) {
    service := setupBenchmarkService()
    req := LoginRequest{
        Phone:    "13800138000",
        Password: "password123",
    }

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.Login(context.Background(), req)
        if err != nil {
            b.Fatal(err)
        }
    }
}

// 压力测试脚本
func TestAPI_LoadTest(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过压力测试")
    }

    server := setupTestServer()
    defer server.Close()

    // 并发100个用户，持续10秒
    concurrency := 100
    duration := 10 * time.Second

    var wg sync.WaitGroup
    results := make(chan time.Duration, concurrency*100)

    start := time.Now()
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for time.Since(start) < duration {
                reqStart := time.Now()
                resp, err := http.Get(server.URL + "/api/v1/health")
                if err != nil {
                    t.Errorf("请求失败: %v", err)
                    continue
                }
                resp.Body.Close()
                results <- time.Since(reqStart)
            }
        }()
    }

    wg.Wait()
    close(results)

    // 统计结果
    var total time.Duration
    var count int
    var max time.Duration

    for duration := range results {
        total += duration
        count++
        if duration > max {
            max = duration
        }
    }

    avg := total / time.Duration(count)
    t.Logf("平均响应时间: %v", avg)
    t.Logf("最大响应时间: %v", max)
    t.Logf("总请求数: %d", count)

    // 断言性能要求
    assert.Less(t, avg, 200*time.Millisecond, "平均响应时间应小于200ms")
    assert.Less(t, max, 1*time.Second, "最大响应时间应小于1s")
}
```

### 测试数据管理
```go
// 测试数据工厂
type TestDataFactory struct {
    db *sql.DB
}

func (f *TestDataFactory) CreateUser(overrides ...func(*User)) *User {
    user := &User{
        Phone:       fmt.Sprintf("138%08d", rand.Intn(100000000)),
        Nickname:    "测试用户" + strconv.Itoa(rand.Intn(1000)),
        PasswordHash: "$2a$10$...", // 固定的测试密码哈希
        CreatedAt:   time.Now(),
    }

    // 应用覆盖设置
    for _, override := range overrides {
        override(user)
    }

    // 保存到数据库
    err := f.db.QueryRow(
        "INSERT INTO users (phone, nickname, password_hash, created_at) VALUES (?, ?, ?, ?) RETURNING id",
        user.Phone, user.Nickname, user.PasswordHash, user.CreatedAt,
    ).Scan(&user.ID)

    if err != nil {
        panic(err)
    }

    return user
}

// 使用示例
func TestSomething(t *testing.T) {
    factory := NewTestDataFactory(testDB)

    // 创建标准测试用户
    user1 := factory.CreateUser()

    // 创建特定属性的用户
    user2 := factory.CreateUser(func(u *User) {
        u.Phone = "13800138000"
        u.Nickname = "特定用户"
    })
}
```

### 测试环境管理
```bash
# 测试脚本 scripts/test.sh
#!/bin/bash

# 设置测试环境变量
export GO_ENV=test
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=kids_platform_test
export REDIS_HOST=localhost:6379

# 清理并重建测试数据库
mysql -u root -p -e "DROP DATABASE IF EXISTS kids_platform_test"
mysql -u root -p -e "CREATE DATABASE kids_platform_test"
mysql -u root -p kids_platform_test < migrations/schema.sql

# 运行不同类型的测试
case "$1" in
    "unit")
        echo "运行单元测试..."
        go test ./internal/... -v -short -cover
        ;;
    "integration")
        echo "运行集成测试..."
        go test ./tests/integration/... -v
        ;;
    "api")
        echo "运行API测试..."
        go test ./tests/api/... -v
        ;;
    "all")
        echo "运行所有测试..."
        go test ./... -v -cover
        ;;
    "benchmark")
        echo "运行性能测试..."
        go test ./... -bench=. -benchmem
        ;;
    *)
        echo "用法: $0 {unit|integration|api|all|benchmark}"
        exit 1
        ;;
esac
```

### 持续集成测试
```yaml
# .github/workflows/test.yml
name: 测试流水线

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: kids_platform_test
        ports:
          - 3306:3306

      redis:
        image: redis:7
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: 设置Go环境
      uses: actions/setup-go@v3
      with:
        go-version: 1.23.10

    - name: 安装依赖
      run: go mod download

    - name: 运行单元测试
      run: go test ./internal/... -v -short -cover

    - name: 运行集成测试
      run: go test ./tests/... -v
      env:
        DB_HOST: localhost
        DB_PORT: 3306
        DB_NAME: kids_platform_test
        DB_USER: root
        DB_PASSWORD: password
        REDIS_HOST: localhost:6379

    - name: 生成测试报告
      run: |
        go test ./... -coverprofile=coverage.out
        go tool cover -html=coverage.out -o coverage.html

    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      with:
        name: test-coverage
        path: coverage.html
```

这个策略确保我们能够高效、高质量地完成开发，同时与需求保持一致。
