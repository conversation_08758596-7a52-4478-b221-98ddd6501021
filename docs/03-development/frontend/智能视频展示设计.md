# 📹 智能视频展示设计方案

## 📋 文档信息
- **创建时间**：2025-01-09
- **设计目标**：根据视频数量智能调整展示方式，提供最佳用户体验
- **状态**：✅ 方案确认完成

## 🎯 设计原则

### 智能展示规则
```
视频数量 = 1个：不显示相关视频区域
视频数量 = 2-4个：横向展示所有视频
视频数量 ≥ 5个：展示4个 + "查看更多(+N)"按钮
```

### 视频类型定义
- **课程视频**：用户学习后可打卡练习
- **示范视频**：用户随时参考观看

## 📱 页面展示方案

### **1. 成长页面 - 观看教学**
```
点击"观看教学"按钮 → 跳转到对应训练营的视频列表页
```

### **2. 训练营详情页 - 教学视频预览**
```
[教学视频区域 - 智能展示]
📹 教学视频预览 (共12个)
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 基础姿势        [缩略图] 手腕技巧        [缩略图] 连续跳绳 │
│ 2分钟 | 免费预览        3分钟 | 需参与          4分钟 | 需参与   │
│                                                             │
│ [缩略图] 常见错误                        [ 查看全部视频 ]     │
│ 1分钟 | 免费预览                                             │
└─────────────────────────────────────────────────────────────┘

展示逻辑：
- 总是显示4个视频缩略图
- 前2个为免费预览视频（吸引用户）
- 后2个为课程视频（需要参与训练营）
- 右下角显示"查看全部视频"按钮
```

### **3. 视频播放页 - 相关视频推荐**
```
[相关视频区域 - 智能展示]
📹 相关视频 (共8个)
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 基础跳绳姿势    [缩略图] 跳绳常见错误    [缩略图] 进阶技巧 │
│ 2分钟 | 示范视频        1分钟 | 示范视频         4分钟 | 课程视频 │
│                                                             │
│ [缩略图] 节奏训练                        [ 查看更多 (+4) ]   │
│ 3分钟 | 课程视频                                             │
└─────────────────────────────────────────────────────────────┘

展示逻辑：
- 1个视频：不显示此区域
- 2-4个视频：横向展示所有
- 5个以上：展示4个 + "查看更多(+N)"
```

### **4. 视频列表页 - 完整列表**
```
[视频列表区域]
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 第1课：跳绳基础姿势                    [播放 ▶]     │
│ 👨‍🏫 教练小王 | 2分钟 | 👀 2,345 | 🏷️ 课程视频              │
│ 学习正确的握绳方法和基本站姿                                 │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 第2课：手腕发力技巧                    [播放 ▶]     │
│ 👨‍🏫 教练小王 | 3分钟 | 👀 1,876 | 🏷️ 课程视频              │
│ 掌握正确的手腕转动方法                                       │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 常见错误纠正                          [播放 ▶]     │
│ 👨‍🏫 教练小王 | 1分钟 | 👀 3,421 | 🏷️ 示范视频              │
│ 避免初学者常犯的5个错误                                      │
└─────────────────────────────────────────────────────────────┘

特点：
- 垂直列表展示所有视频
- 清晰标注视频类型（课程/示范）
- 显示基本信息：时长、观看数、类型
- 简洁的描述文字
```

## 🔄 页面跳转逻辑

### 跳转路径图
```
成长页面 → 点击"观看教学" → 训练营视频列表页
    ↓
训练营详情页 → 点击"查看全部视频" → 训练营视频列表页
    ↓
视频播放页 → 点击"查看更多" → 相关视频列表页
    ↓
视频列表页 → 点击视频 → 视频播放页
```

### 具体跳转说明

**1. 成长页面 → 视频列表**
- 点击"观看教学"按钮
- 跳转到该训练营的完整视频列表
- 列表标题显示训练营名称

**2. 训练营详情页 → 视频列表**
- 点击"查看全部视频"按钮
- 跳转到该训练营的完整视频列表
- 突出显示免费预览视频

**3. 视频播放页 → 相关视频列表**
- 点击"查看更多(+N)"按钮
- 跳转到相关视频的完整列表
- 按相关度排序展示

**4. 视频列表页 → 视频播放**
- 点击任意视频项
- 跳转到视频播放页
- 自动加载相关视频推荐

## 🎨 视觉设计要点

### 视频缩略图设计
- **尺寸比例**：16:9标准视频比例
- **圆角设计**：符合整体UI风格
- **播放图标**：中央显示播放按钮
- **时长标签**：右下角显示视频时长
- **类型标签**：左上角显示视频类型

### 信息层级设计
```
主要信息：视频标题（大字体）
次要信息：讲师、时长、观看数（中字体）
辅助信息：视频描述（小字体）
标识信息：类型标签（彩色标签）
```

### 交互反馈
- **点击反馈**：缩略图点击时有轻微缩放效果
- **加载状态**：视频加载时显示骨架屏
- **播放状态**：正在播放的视频有特殊标识

## 🛠️ 技术实现考虑

### 数据结构设计
```json
{
  "video": {
    "id": "视频ID",
    "title": "视频标题",
    "description": "视频描述",
    "duration": "视频时长(秒)",
    "thumbnail": "缩略图URL",
    "type": "视频类型(course/demo)",
    "instructor": "讲师信息",
    "view_count": "观看次数",
    "camp_id": "所属训练营ID",
    "order": "排序权重"
  }
}
```

### 智能展示逻辑
```javascript
function getVideoDisplayConfig(videoCount) {
  if (videoCount <= 1) {
    return { showRelated: false };
  } else if (videoCount <= 4) {
    return { 
      showRelated: true, 
      displayCount: videoCount, 
      showMoreButton: false 
    };
  } else {
    return { 
      showRelated: true, 
      displayCount: 4, 
      showMoreButton: true,
      moreCount: videoCount - 4
    };
  }
}
```

### 性能优化
- **懒加载**：视频缩略图使用懒加载
- **预加载**：预加载下一个可能播放的视频
- **缓存策略**：缓存视频列表数据
- **分页加载**：视频列表页支持分页

## 📊 用户体验优化

### 内容发现
- **智能推荐**：基于用户观看历史推荐相关视频
- **进度记录**：记录用户观看进度
- **收藏功能**：允许用户收藏重要视频
- **搜索功能**：在视频列表页提供搜索

### 学习路径
- **课程顺序**：课程视频按学习顺序排列
- **前置要求**：标注视频的前置学习要求
- **难度标识**：用颜色或图标标识视频难度
- **完成状态**：显示用户的学习完成状态

---

**注意**：本设计方案已考虑了不同视频数量场景下的最佳展示方式，确保用户在任何情况下都能获得良好的浏览体验。
