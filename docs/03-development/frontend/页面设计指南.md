# 🎨 儿童跳绳平台 - 页面设计指南

## 📋 设计概述
**项目**: 儿童跳绳学习平台微信小程序  
**目标用户**: K-6年级儿童 + 家长(27-40岁)  
**设计原则**: 简洁、有趣、易用、安全

## 🎯 导航架构设计

### 最终确定的3导航结构
```
🏠 首页 (发现选择中心)     📊 成长 (学习行动中心)     👤 我的 (个人管理中心)
```

#### 🏠 首页 (发现选择中心)
**核心价值**: 训练营发现和选择
- 训练营列表展示
- 训练营详情查看
- 参与决策功能

#### 📊 成长 (学习行动中心)
**核心价值**: 学习和打卡行动
- 今日任务显示
- 打卡功能入口
- 学习进度跟踪
- 打卡记录查看
- 排行榜展示

#### 👤 我的 (个人管理中心)
**核心价值**: 个人信息管理
- 孩子档案管理
- 个人设置
- 帮助客服

## 🎨 视觉设计规范

### 色彩系统
- **主色调**: 活力橙 #FF7A45 (按钮、焦点、进度)
- **功能色**: 跳绳绿 #7ED321 (运动相关)
- **背景色**: 米白色 #F7F8FA (主背景)
- **辅助色**: 青蓝色 #4A90E2 (信息、标签)

### 组件设计原则
- **按钮**: 圆角12px，最小点击区域44px
- **卡片**: 圆角8px，阴影2px，间距16px
- **字体**: 标题16-20px加粗，正文14px常规
- **图标**: 24px标准尺寸，线面结合风格

## 📱 核心用户流程

### 新用户流程
```
选择训练营 → 加入 → 观看视频学习 → 每日打卡 → 获得勋章 → 分享 → 吸引新用户
```

### 老用户流程
```
打开小程序 → 成长页面 → 看到今日任务 → 直接打卡
```

**详细页面设计请参考：《完整页面线框图.md》**



## 🎯 交互设计要点

### 儿童友好设计
- **大按钮**: 最小44px点击区域
- **清晰反馈**: 点击有明显视觉/触觉反馈
- **简单操作**: 减少复杂手势，多用点击
- **防误触**: 重要操作需要二次确认

### 家长管理功能
- **快速切换**: 多孩子档案快速切换
- **数据总览**: 一屏看完核心数据
- **设置简单**: 常用设置一键开关
- **隐私保护**: 敏感信息脱敏显示

## 📋 页面清单

**详细页面设计请参考：《完整页面线框图.md》**

### 核心页面 (MVP)
- [x] 首页 (训练营选择)
- [x] 成长页面 (今日任务+数据)
- [x] 我的页面 (个人管理)
- [x] 训练营详情页
- [x] 打卡页面
- [x] 打卡成功页
- [x] 排行榜页面

## 🛠️ 开发建议

### 技术选型
- **框架**: 微信小程序原生开发
- **UI库**: WeUI + 自定义组件
- **状态管理**: 小程序全局数据
- **网络请求**: wx.request + 封装

### 性能优化
- **图片**: WebP格式，懒加载
- **页面**: 按需加载，预加载关键页面
- **缓存**: 合理使用本地缓存
- **体验**: 骨架屏，加载动画

---

**设计原则**: 以儿童为中心，以家长为决策者，以简洁为美，以有趣为魂

**主要设计文档**: 《完整页面线框图.md》包含所有页面的详细设计
