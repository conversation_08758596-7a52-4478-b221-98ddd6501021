# 🎨 儿童跳绳平台 - 完整页面线框图

## � **0. 微信登录页**

```
[顶部状态栏]
----------------------------------------------------------------------

[页面主体 - 居中布局]
+-------------------------------------------------------------+
|                                                             |
|  [大Logo: 跳跳兔子 + 平台名称]                               |
|  儿童跳绳学习平台                                            |
|                                                             |
|  [插画: 孩子们开心跳绳的场景]                                |
|                                                             |
|  🎯 让孩子爱上运动，健康成长                                 |
|                                                             |
|  • 专业跳绳教学                                             |
|  • 21天养成习惯                                             |
|  • 微信群指导交流                                           |
|                                                             |
+-------------------------------------------------------------+

----------------------------------------------------------------------
[底部固定按钮]
[ 微信授权登录 ]
[小字] 登录即表示同意《用户协议》和《隐私政策》
----------------------------------------------------------------------
```

## �📱 **1. 首页 (发现选择中心)**

```
[顶部状态栏]
----------------------------------------------------------------------
[左上角] 小明 ▼                    
----------------------------------------------------------------------

[页面主体 - 垂直滚动]

    +-------------------------------------------------------------+
    |                                                             |
    |  [插画: "跳跳"兔子在跳绳]                                      |
    |                                                             |
    |  《零基础21天跳绳挑战营》                                      |
    |  从零开始，21天养成跳绳好习惯                                   |
    |                                                             |
    |  #保姆级教程 #社群交流 #零基础 #最系统教学                               |
    |                                                             |
    |  [ 立即查看 ] ▶                                              |
    |                                                             |
    +-------------------------------------------------------------+

    +-------------------------------------------------------------+
    |                                                             |
    |  [插画: 孩子们在跳绳]                                      |
    |                                                             |
    |  《暑假跳绳追高计划》                                          |
    |  科学训练，暑假长高3厘米                                       |
    |                                                             |
    |  #长高计划 #暑假专属 #进阶                                     |
    |                                                             |
    |  [ 立即查看 ] ▶                                              |
    |                                                             |
    +-------------------------------------------------------------+

    [卡片标题] 🤫 更多训练营，敬请期待

    [水平滚动区域]
    +----------+   +----------+   +----------+
    | 《冲刺200》|   | 《进阶150》|   | 《花样跳》|
    | [置灰状态] |   | [置灰状态] |   | [置灰状态] |
    | 敬请期待   |   | 敬请期待   |   | 敬请期待   |
    +----------+   +----------+   +----------+

----------------------------------------------------------------------
[底部导航栏 - 首页选中]
[🏠 首页]     [📊 成长]     [👤 我的]
  首页          成长          我的
----------------------------------------------------------------------
```

## 📊 **2. 成长页面 (学习行动中心)**

### **2.1 已参与训练营状态 (简化版)**
```
[顶部状态栏]
----------------------------------------------------------------------
[左上角] 小明 ▼
----------------------------------------------------------------------

[任务列表区域]
+-------------------------------------------------------------+
| 🎯 进行中的训练营                                            |
+-------------------------------------------------------------+
| 《零基础21天跳绳挑战营》第8次打卡                             |
| 任务：连续跳绳50个                                           |
| [ 立即打卡 ]    [ 观看教学 ]                                 |
+-------------------------------------------------------------+
| 《进阶花样跳绳营》第3次打卡                                   |
| 任务：单脚跳30个                                             |
| [ 立即打卡 ]    [ 观看教学 ]                                 |
+-------------------------------------------------------------+

[快捷入口区域]
┌─────────┐ ┌─────────┐ ┌─────────┐
│ 📈 数据  │ │ 🏆 排行榜 │ │ 👥 邀请  │
│   统计   │ │         │ │  推广   │
└─────────┘ └─────────┘ └─────────┘

----------------------------------------------------------------------
[底部导航栏 - 成长选中]
[🏠 首页]     [📊 成长]     [👤 我的]
  首页          成长          我的
----------------------------------------------------------------------
```

### **2.2 未参与训练营状态**
```
[顶部状态栏]
----------------------------------------------------------------------
[左上角] 小明 ▼                    
----------------------------------------------------------------------

[空状态区域]
+-------------------------------------------------------------+
|                                                             |
|  [插画: "跳跳"兔子在等待]                                      |
|                                                             |
|  🎯 还没有参与训练营                                          |
|  去首页选择适合的训练营开始学习吧！                             |
|                                                             |
|  [ 去选择训练营 ]                                            |
|                                                             |
+-------------------------------------------------------------+

----------------------------------------------------------------------
[底部导航栏 - 成长选中]
[🏠 首页]     [📊 成长]     [👤 我的]
  首页          成长          我的
----------------------------------------------------------------------
```

## 👤 **3. 我的页面 (个人管理中心)**

```
[顶部状态栏]
----------------------------------------------------------------------
[左上角] 我的                      [右上角] ⚙️
----------------------------------------------------------------------

[用户信息区域]
+-------------------------------------------------------------+
| 👧 小明                                    [切换孩子 ▼]     |
| 6岁 | 已学习8天 | 积分120                                   |
+-------------------------------------------------------------+

[功能菜单区域]
┌─────────────────────────────────────────────────────────────┐
│ 👶 孩子管理                                          >      │
├─────────────────────────────────────────────────────────────┤
│ 📚 我的训练营                                        >      │
├─────────────────────────────────────────────────────────────┤
│ 🏆 成就徽章                                          >      │
├─────────────────────────────────────────────────────────────┤
│ ⚙️ 设置                                             >      │
├─────────────────────────────────────────────────────────────┤
│ 💬 意见反馈                                          >      │
├─────────────────────────────────────────────────────────────┤
│ ❓ 帮助中心                                          >      │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部导航栏 - 我的选中]
[🏠 首页]     [📊 成长]     [👤 我的]
  首页          成长          我的
----------------------------------------------------------------------
```

## 📋 **4. 训练营详情页**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              训练营详情              [分享 📤]
----------------------------------------------------------------------

[头部区域]
+-------------------------------------------------------------+
|  [大图: 训练营封面图]                                        |
|                                                             |
|  《零基础21天跳绳挑战营》                                    |
|  从零开始，21天养成跳绳好习惯                                 |
|                                                             |
|  💰 免费 | 👥 1,234人参与 | ⭐⭐⭐⭐⭐ 4.9分              |
+-------------------------------------------------------------+

[介绍视频区域]
+-------------------------------------------------------------+
| 📹 [视频播放器]                                             |
| 训练营介绍视频 (2分钟)                                       |
+-------------------------------------------------------------+

[详细介绍区域]
📝 训练营介绍
• 适合完全不会跳绳的孩子
• 21天循序渐进的训练计划
• 专业教练视频指导
• 微信群答疑交流

📚 课程大纲
第1-7天：基础动作学习
第8-14天：连续跳绳练习
第15-21天：挑战100个

[教学视频区域 - 智能展示]
📹 教学视频预览 (共12个)
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 基础姿势        [缩略图] 手腕技巧        [缩略图] 连续跳绳 │
│ 2分钟 | 免费预览        3分钟 | 需参与          4分钟 | 需参与   │
│                                                             │
│ [缩略图] 常见错误                        [ 查看全部视频 ]     │
│ 1分钟 | 免费预览                                             │
└─────────────────────────────────────────────────────────────┘

👥 学员评价
⭐⭐⭐⭐⭐ 小红妈妈：孩子很喜欢，坚持了15天
⭐⭐⭐⭐⭐ 小明爸爸：教学很专业，进步明显

----------------------------------------------------------------------
[底部固定按钮]
[ 免费参与 ]                    [ 了解更多 ]
----------------------------------------------------------------------
```

## 🎯 **5. 打卡页面**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]                今日打卡                [帮助 ?]
----------------------------------------------------------------------

[任务信息区域-默认折叠]
+-------------------------------------------------------------+
| 《零基础21天跳绳挑战营》                                 |
| 任务：连续跳绳50个                                           |
+-------------------------------------------------------------+

[打卡记录区域 - 可左右滑动]
📅 8/21次                               [ 查看全部 > ]
← 8次  7次  6次  5次  4次  3次  2次 →
  ✅   ✅   ✅   ⭕   ⭕   ⭕   ⭕
  

[打卡表单区域]
📝 打卡信息
┌─────────────────────────────────────────────────────────────┐
│ 视频号ID: [输入框]                                          │
│ 或朋友圈链接: [输入框]                                       │
│                                                             │
│ 完成数量: [50] 个                                           │
│                                                             │
│ 今日感受: [文本输入框]                                       │
│ 分享今天的练习心得...                                        │
│                                                             │
│ ☑️ 已分享到朋友圈/视频号                                     │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部按钮]
[ 提交打卡 ]
----------------------------------------------------------------------
```

## 🎉 **6. 打卡成功页 (优化版)**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              打卡成功                [分享 📤]
----------------------------------------------------------------------

[成功反馈区域 - 全屏沉浸式效果]
+-------------------------------------------------------------+
|  [背景: 橙色渐变 + 飘落的星星动画]                            |
|  [音效: 成功提示音 + 掌声]                                   |
|                                                             |
|  🎉 恭喜完成第8次打卡！                                       |
|  [大字体 + 弹跳动画]                                         |
|                                                             |
|  ⭐ 获得 20 积分 ⭐                                           |
|  🏅 连续打卡8次勋章 🏅                                        |
|  [数字滚动动画 + 徽章闪烁效果]                                |
|                                                             |
|  [3秒后自动显示分享按钮]                                     |
+-------------------------------------------------------------+

[成就卡片预览区域]
┌─────────────────────────────────────┐
│ 🎯 小明的跳绳成就                    │
│                                     │
│ [小明头像] 小明 6岁                  │
│ 🏃 已完成第8次打卡                   │
│ 💪 连续跳绳50个 ✅                   │
│ 🌟 获得"坚持之星"勋章                │
│                                     │
│ 一起来跳绳，让孩子更健康！            │
│ [小程序二维码]                       │
└─────────────────────────────────────┘

[分享引流区域]
[ � 生成朋友圈图片 ]  [ 📱 分享到视频号 ]

----------------------------------------------------------------------
[底部按钮]
[ 继续加油 ]
----------------------------------------------------------------------

## 🏆 **7. 排行榜页面**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]                排行榜                [筛选 🔽]
----------------------------------------------------------------------

[切换标签]
[ 好友榜 ]    [ 全服榜 ]    [ 本周榜 ]

[我的排名区域]
+-------------------------------------------------------------+
| 🎯 我的排名                                                  |
| 第12名 | 连续8天 | 积分80                                    |
| 超越了68%的用户，继续加油！                                   |
+-------------------------------------------------------------+

[排行榜列表]
┌─────────────────────────────────────────────────────────────┐
│ 1. 🥇 小红      连续15天    积分: 150    [查看详情]          │
├─────────────────────────────────────────────────────────────┤
│ 2. 🥈 小刚      连续12天    积分: 120    [查看详情]          │
├─────────────────────────────────────────────────────────────┤
│ 3. 🥉 小丽      连续10天    积分: 100    [查看详情]          │
├─────────────────────────────────────────────────────────────┤
│ 4.    小明      连续9天     积分: 90     [查看详情]          │
├─────────────────────────────────────────────────────────────┤
│ ...                                                         │
├─────────────────────────────────────────────────────────────┤
│ 12. 👧 小明(我)  连续8天     积分: 80     [我的数据]         │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------

## 👶 **8. 孩子档案创建页**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              创建孩子档案              [跳过]
----------------------------------------------------------------------

[页面主体]
+-------------------------------------------------------------+
|  [插画: 可爱的孩子形象]                                      |
|                                                             |
|  👶 为孩子创建专属档案                                       |
|  让我们更好地为孩子定制学习计划                               |
+-------------------------------------------------------------+

[表单区域]
📝 基本信息
┌─────────────────────────────────────────────────────────────┐
│ 孩子姓名: [输入框] 小明                                      │
│                                                             │
│ 性别: ○ 男孩  ○ 女孩                                        │
│                                                             │
│ 年龄: [选择器] 6岁                                          │
│                                                             │
│ 跳绳基础:                                                   │
│ ○ 完全不会  ○ 会一点  ○ 比较熟练                            │
│                                                             │
│ 运动目标:                                                   │
│ ○ 学会跳绳  ○ 提高技巧  ○ 增强体质  ○ 长高                  │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部按钮]
[ 创建档案 ]
----------------------------------------------------------------------
```

## 👶 **9. 孩子管理页**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              孩子管理                [+ 添加]
----------------------------------------------------------------------

[孩子列表]
┌─────────────────────────────────────────────────────────────┐
│ 👧 小明 (当前)                                   [编辑]     │
│ 6岁 | 男孩 | 学习8天                                        │
│ 目标: 学会跳绳 | 基础: 会一点                                │
├─────────────────────────────────────────────────────────────┤
│ 👦 小红                                         [编辑]     │
│ 8岁 | 女孩 | 学习15天                                       │
│ 目标: 提高技巧 | 基础: 比较熟练                              │
└─────────────────────────────────────────────────────────────┘

[操作区域]
[ + 添加新孩子 ]

----------------------------------------------------------------------
[底部导航栏]
[🏠 首页]     [📊 成长]     [👤 我的]
  首页          成长          我的
----------------------------------------------------------------------
```

## 📹 **10. 视频播放页 (智能展示)**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              视频详情                [分享 📤]
----------------------------------------------------------------------

[视频播放区域]
+-------------------------------------------------------------+
| 📹 [全屏视频播放器]                                         |
| 连续跳绳技巧详解                                             |
| [播放控制条] 00:45 / 03:20                                  |
+-------------------------------------------------------------+

[视频信息区域]
📝 连续跳绳技巧详解
👨‍🏫 教练小王 | 📅 2024-12-30 | 👀 1,234次观看
🏷️ 课程视频 - 学习后可打卡练习

📖 视频介绍
本视频详细讲解连续跳绳的技巧要点，包括：
• 正确的手腕发力方法
• 脚步节奏的掌握
• 常见错误的纠正

[相关视频区域 - 智能展示]
📹 相关视频 (共8个)
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 基础跳绳姿势    [缩略图] 跳绳常见错误    [缩略图] 进阶技巧 │
│ 2分钟 | 示范视频        1分钟 | 示范视频         4分钟 | 课程视频 │
│                                                             │
│ [缩略图] 节奏训练                        [ 查看更多 (+4) ]   │
│ 3分钟 | 课程视频                                             │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部按钮]
[ 收藏视频 ]              [ 开始练习 ]
----------------------------------------------------------------------
```

## 📹 **11. 视频列表页**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              视频列表                [搜索 🔍]
----------------------------------------------------------------------

[列表标题]
《零基础21天跳绳挑战营》- 教学视频

[视频列表区域]
┌─────────────────────────────────────────────────────────────┐
│ [缩略图] 第1课：跳绳基础姿势                    [播放 ▶]     │
│ 👨‍🏫 教练小王 | 2分钟 | 👀 2,345 | 🏷️ 课程视频              │
│ 学习正确的握绳方法和基本站姿                                 │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 第2课：手腕发力技巧                    [播放 ▶]     │
│ 👨‍🏫 教练小王 | 3分钟 | 👀 1,876 | 🏷️ 课程视频              │
│ 掌握正确的手腕转动方法                                       │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 常见错误纠正                          [播放 ▶]     │
│ 👨‍🏫 教练小王 | 1分钟 | 👀 3,421 | 🏷️ 示范视频              │
│ 避免初学者常犯的5个错误                                      │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 第3课：连续跳绳练习                    [播放 ▶]     │
│ 👨‍🏫 教练小王 | 4分钟 | 👀 1,654 | 🏷️ 课程视频              │
│ 从单次跳跃到连续跳绳的过渡                                   │
├─────────────────────────────────────────────────────────────┤
│ [缩略图] 节奏感训练                            [播放 ▶]     │
│ 👨‍🏫 教练小王 | 3分钟 | 👀 987 | 🏷️ 示范视频               │
│ 培养跳绳的节奏感和协调性                                     │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部提示]
💡 课程视频：学习后可打卡练习 | 示范视频：随时参考观看
----------------------------------------------------------------------
```

## 🏆 **11. 成就徽章页 (完整勋章体系)**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              我的徽章                [分享 📤]
----------------------------------------------------------------------

[统计区域]
+-------------------------------------------------------------+
| 🏆 徽章收集进度                                              |
| 已获得 8/20 个徽章                                          |
| 继续努力，解锁更多成就！                                     |
+-------------------------------------------------------------+

[徽章分类展示区域 - 网格布局]

📈 成长类勋章
┌─────────────────────────────────────────────────────────────┐
│ [✅ 跳绳新手]    [✅ 坚持之星]   [🔒 跳绳达人]               │
│ 完成第1次打卡    连续打卡7次     完成第50次打卡              │
│ 2024-12-25      2024-12-30      未解锁                     │
│                                                             │
│ [🔒 跳绳王者]                                               │
│ 完成第100次打卡                                             │
│ 未解锁                                                      │
└─────────────────────────────────────────────────────────────┘

🎯 挑战类勋章
┌─────────────────────────────────────────────────────────────┐
│ [✅ 任务完成者]  [🔒 挑战者]     [🔒 速度之星]               │
│ 完成1个训练营    参与高难度营    跳绳速度达标                │
│ 2024-12-28      未解锁          未解锁                      │
└─────────────────────────────────────────────────────────────┘

👥 社交类勋章
┌─────────────────────────────────────────────────────────────┐
│ [✅ 推广大使铜牌] [🔒 推广大使银牌] [🔒 推广大使金牌]         │
│ 邀请5人成功      邀请10人成功     邀请20人成功               │
│ 2024-12-29      未解锁           未解锁                     │
│                                                             │
│ [✅ 分享达人]    [🔒 社区贡献者]                             │
│ 分享朋友圈10次   获得点赞100次                              │
│ 2024-12-30      未解锁                                      │
└─────────────────────────────────────────────────────────────┘

🌈 特殊类勋章
┌─────────────────────────────────────────────────────────────┐
│ [✅ 首发用户]    [🔒 全勤宝宝]   [🔒 节日特别]               │
│ 平台前100名用户  某月全勤打卡    节日活动参与                │
│ 2024-12-25      未解锁          未解锁                      │
└─────────────────────────────────────────────────────────────┘

[即将解锁提示]
🎯 距离"跳绳达人"勋章还差 42 次打卡

----------------------------------------------------------------------
[底部按钮]
[ 🎨 生成成就卡片 ]              [ 继续努力 ]
----------------------------------------------------------------------
```

## 🎨 **12. 分享图片生成页**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]              分享成就                [保存图片]
----------------------------------------------------------------------

[图片预览区域]
+-------------------------------------------------------------+
|  [卡通可爱风格成就卡片]                                      |
|                                                             |
|  🎯 小明的跳绳成就                                           |
|                                                             |
|  [小明头像圆形] 小明 6岁                                     |
|  🏃 已完成第8次打卡                                          |
|  💪 连续跳绳50个 ✅                                          |
|  🌟 获得"坚持之星"勋章                                       |
|                                                             |
|  [进度条] ████████░░░░░░░░░░ 8/21次                         |
|                                                             |
|  一起来跳绳，让孩子更健康！                                   |
|  [小程序二维码]                                             |
|                                                             |
+-------------------------------------------------------------+

[智能文案区域]
📝 推荐分享文案 (自动生成)
┌─────────────────────────────────────────────────────────────┐
│ 🌟 小明获得"坚持之星"勋章啦！                                │
│ 连续8次打卡，进步真的很大！                                  │
│ 看着孩子一天天成长，当妈妈的太骄傲了！                        │
│ #跳绳小达人 #坚持的力量                                      │
│                                                             │
│ [ 复制文案 ]                                                │
└─────────────────────────────────────────────────────────────┘

[模板选择区域]
🎨 选择卡片样式
[模板1: 活泼橙色] [模板2: 清新蓝色] [模板3: 温馨粉色]

----------------------------------------------------------------------
[底部按钮]
[ 保存到相册 ]    [ 分享到朋友圈 ]    [ 分享到微信群 ]
----------------------------------------------------------------------
```

## ⚙️ **13. 设置页面**

```
[顶部导航栏]
----------------------------------------------------------------------
[< 返回]                设置
----------------------------------------------------------------------

[设置列表]
┌─────────────────────────────────────────────────────────────┐
│ 🔔 消息通知                                          >      │
│ 打卡提醒、活动通知                                           │
├─────────────────────────────────────────────────────────────┤
│ 🔒 隐私设置                                          >      │
│ 数据分享、排行榜显示                                         │
├─────────────────────────────────────────────────────────────┤
│ 📱 关于我们                                          >      │
│ 版本信息、联系方式                                           │
├─────────────────────────────────────────────────────────────┤
│ 📋 用户协议                                          >      │
│ 服务条款、隐私政策                                           │
├─────────────────────────────────────────────────────────────┤
│ 💬 意见反馈                                          >      │
│ 问题反馈、功能建议                                           │
├─────────────────────────────────────────────────────────────┤
│ 🆘 帮助中心                                          >      │
│ 常见问题、使用指南                                           │
└─────────────────────────────────────────────────────────────┘

----------------------------------------------------------------------
[底部区域]
版本号: v1.0.0
----------------------------------------------------------------------
```
