# 🏗️ 项目架构设计文档

## 📋 文档概述
**创建时间**: 2025-07-03  
**版本**: v1.0  
**适用项目**: Go API框架  
**最后更新**: 2025-07-03

## 🎯 架构概览

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        客户端层                              │
├─────────────────────────────────────────────────────────────┤
│  Web客户端  │  移动端APP  │  第三方系统  │  管理后台        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      负载均衡层                              │
├─────────────────────────────────────────────────────────────┤
│              Nginx / ALB / API Gateway                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层                              │
├─────────────────────────────────────────────────────────────┤
│  API服务器(8080)     │     Admin服务器(8081)                │
│  ┌─────────────────┐ │ ┌─────────────────────────────────┐   │
│  │ 中间件栈        │ │ │ 中间件栈                        │   │
│  │ - JWT认证       │ │ │ - JWT认证                       │   │
│  │ - CORS          │ │ │ - CORS                          │   │
│  │ - 限流          │ │ │ - 限流                          │   │
│  │ - 监控          │ │ │ - 监控                          │   │
│  │ - 日志          │ │ │ - 日志                          │   │
│  └─────────────────┘ │ └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│  Handler层  │  Service层  │  Repository层  │  Model层       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层                              │
├─────────────────────────────────────────────────────────────┤
│     MySQL/PostgreSQL     │        Redis缓存                │
└─────────────────────────────────────────────────────────────┘
```

## 🏛️ 分层架构设计

### 1. Handler层 (HTTP处理层)
**职责**: HTTP请求处理、参数验证、响应格式化

**目录结构**:
```
internal/handlers/
├── api/                    # API服务处理器
│   ├── routes.go          # 路由注册
│   ├── user_handlers.go   # 用户相关处理器
│   └── auth_handlers.go   # 认证相关处理器
└── admin/                 # 管理后台处理器
    ├── routes.go          # 管理后台路由
    └── user_handlers.go   # 用户管理处理器
```

**核心功能**:
- 请求参数绑定和验证
- 调用Service层业务逻辑
- 统一响应格式处理
- 错误处理和状态码设置

### 2. Service层 (业务逻辑层)
**职责**: 业务逻辑处理、事务管理、缓存策略

**目录结构**:
```
internal/services/
├── api/                   # API业务逻辑
├── admin/                 # 管理后台业务逻辑
├── shared/                # 共享业务逻辑
└── user_service.go        # 用户服务实现
```

**核心功能**:
- 复杂业务逻辑处理
- 数据验证和转换
- 缓存策略实现
- 事务管理
- 第三方服务集成

### 3. Repository层 (数据访问层)
**职责**: 数据库操作、数据持久化

**目录结构**:
```
internal/repositories/
└── user_repository.go     # 用户数据访问
```

**核心功能**:
- CRUD操作封装
- 数据库查询优化
- 数据模型转换
- 错误处理

### 4. Model层 (数据模型层)
**职责**: 数据结构定义、请求响应模型

**目录结构**:
```
internal/models/
└── user.go               # 用户模型定义
```

**核心功能**:
- 数据库模型定义
- 请求/响应结构体
- 数据验证规则
- 模型转换方法

## 🔧 基础设施层

### 1. 配置管理 (pkg/config)
**特性**:
- 支持YAML配置文件
- 环境变量覆盖
- 配置热重载
- 默认值设置

**配置项**:
- 服务器配置 (端口、模式)
- 数据库配置 (MySQL/PostgreSQL)
- Redis配置 (可选)
- JWT配置 (密钥、过期时间)
- 日志配置 (级别、格式、输出)
- 文件上传配置

### 2. 数据库管理 (pkg/database)
**特性**:
- GORM ORM集成
- 多数据库支持 (MySQL/PostgreSQL)
- 连接池管理
- 自动迁移
- 健康检查

### 3. 缓存系统 (pkg/cache)
**特性**:
- Redis集成
- Mock客户端 (开发环境)
- 对象序列化/反序列化
- 过期时间管理
- 健康检查

### 4. 中间件系统 (pkg/middleware)
**已实现中间件**:
- **JWT认证**: 用户身份验证
- **CORS**: 跨域请求支持
- **限流**: API访问频率控制
- **监控**: Prometheus指标收集
- **日志**: 请求响应日志记录
- **请求ID**: 链路追踪支持

### 5. 错误处理 (pkg/errors)
**特性**:
- 统一错误码定义
- 业务错误封装
- 错误链追踪
- 多语言错误消息

### 6. 响应格式 (pkg/response)
**特性**:
- 统一响应格式
- 成功/失败响应封装
- 分页响应支持
- 错误信息标准化

## 🚀 服务架构

### 双服务设计
```
API服务 (端口8080)          Admin服务 (端口8081)
├── 用户认证                ├── 管理员认证
├── 业务API接口             ├── 用户管理
├── 数据查询                ├── 系统配置
└── 文件上传                └── 数据统计
```

### 服务启动流程
```
1. 加载配置文件
2. 初始化日志系统
3. 初始化验证器
4. 连接数据库
5. 执行数据库迁移
6. 初始化Redis缓存
7. 设置Gin模式
8. 注册路由和中间件
9. 启动HTTP服务器
10. 监听关闭信号
11. 优雅关闭服务
```

## 📊 技术栈

### 核心框架
- **Web框架**: Gin (v1.9.1)
- **ORM**: GORM (v1.25.4)
- **配置管理**: Viper (v1.16.0)
- **日志**: Logrus (v1.9.3)

### 数据库
- **关系型数据库**: MySQL 8.0+ / PostgreSQL 13+
- **缓存数据库**: Redis 6.0+

### 认证和安全
- **JWT**: golang-jwt/jwt/v5 (v5.0.0)
- **密码加密**: bcrypt
- **参数验证**: go-playground/validator/v10

### 监控和运维
- **指标收集**: Prometheus
- **健康检查**: 自定义健康检查器
- **优雅关闭**: context超时控制

## 🔄 数据流向

### 请求处理流程
```
1. 客户端请求 → Nginx/负载均衡器
2. 负载均衡器 → API/Admin服务器
3. 中间件栈处理 (认证、限流、日志等)
4. Handler层 → 参数验证和绑定
5. Service层 → 业务逻辑处理
6. Repository层 → 数据库操作
7. 缓存层 → Redis读写 (可选)
8. 响应返回 → 统一格式化
9. 中间件后处理 → 日志记录
10. 返回客户端 → JSON响应
```

### 缓存策略
```
读取流程:
1. 检查Redis缓存
2. 缓存命中 → 直接返回
3. 缓存未命中 → 查询数据库
4. 将结果存入缓存
5. 返回数据

写入流程:
1. 更新数据库
2. 删除相关缓存
3. 返回结果
```

## 🛡️ 安全设计

### 认证机制
- **JWT Token**: 无状态认证
- **Refresh Token**: 长期认证
- **角色权限**: 基于角色的访问控制

### 安全措施
- **密码加密**: bcrypt哈希
- **参数验证**: 严格的输入验证
- **SQL注入防护**: GORM参数化查询
- **CORS配置**: 跨域请求控制
- **限流保护**: API访问频率限制

## 📈 性能优化

### 缓存策略
- **用户信息缓存**: 30分钟过期
- **查询结果缓存**: 根据业务需求设置
- **缓存预热**: 系统启动时预加载热点数据

### 数据库优化
- **连接池**: 最大100个连接
- **索引优化**: 关键字段建立索引
- **查询优化**: 避免N+1查询问题

### 并发处理
- **Goroutine**: 异步处理非关键任务
- **Channel**: 安全的并发通信
- **Context**: 请求超时控制

## 🔧 部署架构

### 开发环境
```
本地开发 → Docker Compose → 本地数据库
```

### 生产环境
```
代码仓库 → CI/CD → Docker镜像 → Kubernetes → 生产环境
```

### 监控体系
- **应用监控**: Prometheus + Grafana
- **日志收集**: ELK Stack
- **链路追踪**: Jaeger (计划中)
- **告警系统**: AlertManager

---

**架构设计原则**: 高内聚、低耦合、可扩展、可维护、高性能、高可用
