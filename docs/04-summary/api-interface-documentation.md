# API接口详细文档

## 接口概览

本文档详细描述了成长系统的所有API接口，包括请求参数、响应格式和使用示例。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: JWT <PERSON> (部分接口需要)
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 0,
  "message": "success",
  "data": { ... }
}
```

### 错误响应
```json
{
  "code": 1001,
  "message": "参数错误",
  "data": {
    "details": "具体错误信息"
  }
}
```

## 1. 系统接口

### 1.1 健康检查
- **接口**: `GET /health`
- **描述**: 检查服务健康状态
- **认证**: 无需认证
- **响应示例**:
```json
{
  "status": "healthy",
  "checks": {
    "database": {
      "status": "healthy",
      "latency": 18869802
    }
  },
  "metadata": {
    "timestamp": "2025-07-27T10:42:39.168017Z",
    "version": "1.0.0"
  }
}
```

## 2. 认证接口

### 2.1 微信登录
- **接口**: `POST /auth/wechat/login`
- **描述**: 微信小程序登录
- **认证**: 无需认证
- **请求参数**:
```json
{
  "code": "微信授权码",
  "user_info": {
    "nickname": "用户昵称",
    "avatar_url": "头像URL"
  }
}
```

## 3. 训练营接口

### 3.1 训练营列表
- **接口**: `GET /content/camps`
- **描述**: 获取训练营列表
- **认证**: 无需认证
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量 (默认: 10)
- **响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "21天跳绳养成计划",
        "subtitle": "让孩子从不会跳到连续100个",
        "hero_number": "0→100个",
        "hero_text": "21天技能飞跃",
        "duration_days": 21,
        "daily_minutes": 15,
        "difficulty_level": 1,
        "age_group": "3-12岁",
        "price": 0,
        "is_free": 1,
        "total_participants": 1235,
        "completion_rate": 78.5,
        "average_rating": 4.8,
        "tags": ["保姆级教程", "社群交流", "零基础", "免费体验"]
      }
    ],
    "total": 3
  }
}
```

### 3.2 训练营详情
- **接口**: `GET /content/camps/:id`
- **描述**: 获取训练营详细信息
- **认证**: 无需认证
- **路径参数**:
  - `id`: 训练营ID
- **响应**: 包含完整训练营信息和关联的视频集合

### 3.3 参与训练营
- **接口**: `POST /content/camps/:id/join`
- **描述**: 用户参与训练营
- **认证**: 需要JWT认证
- **请求参数**:
```json
{
  "child_id": 123
}
```

### 3.4 用户训练营列表
- **接口**: `GET /user/camps`
- **描述**: 获取用户参与的训练营
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID

## 4. 打卡接口

### 4.1 创建打卡
- **接口**: `POST /checkins`
- **描述**: 创建打卡记录
- **认证**: 需要JWT认证
- **请求参数**:
```json
{
  "child_id": 123,
  "camp_id": 1,
  "practice_duration": 15,
  "jump_count_1min": 80,
  "jump_count_continuous": 50,
  "feeling_text": "今天练习很棒",
  "feeling_score": 8,
  "photos": ["photo_url1", "photo_url2"]
}
```

### 4.2 打卡历史
- **接口**: `GET /checkins/history`
- **描述**: 获取指定训练营的打卡历史记录
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
  - `camp_id`: 训练营ID
  - `page`: 页码
  - `limit`: 每页数量

### 4.3 今日打卡状态
- **接口**: `GET /checkins/today`
- **描述**: 获取今日打卡状态
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
  - `camp_id`: 训练营ID

### 4.4 打卡统计
- **接口**: `GET /checkins/stats`
- **描述**: 获取指定训练营的打卡统计信息
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
  - `camp_id`: 训练营ID

## 5. 积分接口

### 5.1 积分统计
- **接口**: `GET /user/points`
- **描述**: 获取用户积分统计
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID

### 5.2 积分历史
- **接口**: `GET /user/points/history`
- **描述**: 获取积分历史记录
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
  - `page`: 页码
  - `limit`: 每页数量

### 5.3 排行榜
- **接口**: `GET /leaderboard`
- **描述**: 获取积分排行榜
- **认证**: 无需认证
- **查询参数**:
  - `limit`: 返回数量 (默认: 10)

## 6. 契约接口

### 6.1 创建契约
- **接口**: `POST /contracts`
- **描述**: 创建家庭契约
- **认证**: 需要JWT认证
- **请求参数**:
```json
{
  "child_id": 123,
  "title": "21天跳绳挑战",
  "description": "每天跳绳15分钟",
  "target_days": 21,
  "reward_description": "完成后获得新跳绳",
  "witness_name": "妈妈",
  "witness_relation": "母亲"
}
```

### 6.2 契约列表
- **接口**: `GET /contracts`
- **描述**: 获取契约列表
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
  - `status`: 契约状态 (可选)

### 6.3 契约详情
- **接口**: `GET /contracts/:id`
- **描述**: 获取契约详细信息
- **认证**: 需要JWT认证

### 6.4 更新契约进度
- **接口**: `PUT /contracts/:id/progress`
- **描述**: 更新契约完成进度
- **认证**: 需要JWT认证
- **请求参数**:
```json
{
  "progress": 75.5
}
```

### 6.5 完成契约
- **接口**: `POST /contracts/:id/complete`
- **描述**: 标记契约为完成状态
- **认证**: 需要JWT认证

### 6.6 契约统计
- **接口**: `GET /contracts/stats`
- **描述**: 获取契约统计信息
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID

## 7. 成长数据接口

### 7.1 成长页面数据
- **接口**: `GET /user/growth`
- **描述**: 获取成长页面完整数据
- **认证**: 需要JWT认证
- **查询参数**:
  - `child_id`: 孩子ID
- **响应**: 包含训练营进度、打卡统计、积分信息、契约状态等

### 7.2 成长统计
- **接口**: `GET /user/growth/stats`
- **描述**: 获取详细成长统计
- **认证**: 需要JWT认证

### 7.3 成长轨迹
- **接口**: `GET /user/growth/track`
- **描述**: 获取成长轨迹记录
- **认证**: 需要JWT认证

### 7.4 今日状态
- **接口**: `GET /user/growth/today`
- **描述**: 获取今日状态信息
- **认证**: 需要JWT认证

### 7.5 用户统计
- **接口**: `GET /user/stats`
- **描述**: 获取用户基础统计
- **认证**: 需要JWT认证

## 8. 管理员接口

### 8.1 添加积分
- **接口**: `POST /admin/points/add`
- **描述**: 管理员添加积分
- **认证**: 需要管理员权限
- **请求参数**:
```json
{
  "child_id": 123,
  "points": 100,
  "source_type": "manual",
  "description": "管理员奖励"
}
```

### 8.2 更新排名
- **接口**: `POST /admin/points/update-ranking`
- **描述**: 更新周排名数据
- **认证**: 需要管理员权限

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 通用错误 |
| 1001 | 参数错误 |
| 1002 | 未授权 |
| 1003 | 禁止访问 |
| 1004 | 资源未找到 |
| 1005 | 服务器内部错误 |

## 认证说明

### JWT Token
- **获取方式**: 通过微信登录接口获取
- **使用方式**: 在请求头中添加 `Authorization: Bearer <token>`
- **有效期**: 24小时 (可配置)

### 权限级别
- **普通用户**: 可访问用户相关接口
- **管理员**: 可访问所有接口，包括管理员专用接口

## 使用示例

### JavaScript (小程序)
```javascript
// 获取训练营列表
const response = await wx.request({
  url: 'http://localhost:8080/api/v1/content/camps',
  method: 'GET'
});

// 创建打卡记录 (需要认证)
const checkinResponse = await wx.request({
  url: 'http://localhost:8080/api/v1/checkins',
  method: 'POST',
  header: {
    'Authorization': `Bearer ${token}`
  },
  data: {
    child_id: 123,
    camp_id: 1,
    practice_duration: 15
  }
});
```

### cURL
```bash
# 获取训练营列表
curl -X GET http://localhost:8080/api/v1/content/camps

# 创建打卡记录
curl -X POST http://localhost:8080/api/v1/checkins \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"child_id": 123, "camp_id": 1, "practice_duration": 15}'
```

## 测试验证

### 已验证的接口
- ✅ `GET /health` - 健康检查正常
- ✅ `GET /content/camps` - 训练营列表返回3条数据
- ✅ `GET /content/camps/1` - 训练营详情返回完整信息
- ✅ 所有接口编译通过，服务正常启动

### 测试数据
系统已预置测试数据：
- 3个训练营 (跳绳、篮球、足球)
- 关联的视频集合和视频内容
- 完整的数据库表结构
