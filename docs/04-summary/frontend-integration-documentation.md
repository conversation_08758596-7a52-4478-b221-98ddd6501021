# 前端集成功能文档

## 概述

本文档详细描述了微信小程序前端与后端API的集成情况，包括页面功能、API调用和数据流转。

## 前端架构

### 技术栈
- **平台**: 微信小程序
- **语言**: JavaScript
- **UI**: 微信小程序原生组件
- **状态管理**: 自定义状态管理系统
- **工具库**: 封装的HTTP请求、登录守卫、工具函数

### 目录结构
```
miniprogram/
├── apis/                    # API调用封装
│   ├── base.js             # 基础HTTP请求工具
│   ├── content.js          # 训练营相关API
│   └── checkin.js          # 打卡相关API
├── pages/                  # 页面文件
│   ├── home/               # 首页
│   ├── camp-detail/        # 训练营详情页
│   ├── growth/             # 成长页面
│   └── checkin/            # 打卡页面
├── utils/                  # 工具函数
│   ├── constants.js        # 常量配置
│   ├── http.js            # HTTP请求封装
│   └── index.js           # 工具函数集合
└── components/             # 自定义组件
```

## 页面功能集成

### 1. 首页 (home)

#### 功能描述
- 展示训练营列表
- 支持训练营筛选和搜索
- 点击跳转到详情页

#### API集成
- **调用接口**: `GET /api/v1/content/camps`
- **实现文件**: `pages/home/<USER>
- **API封装**: `apis/content.js#getCampList()`

#### 核心代码
```javascript
// 加载训练营列表
async loadHomeData() {
  try {
    console.log("🏕️ 开始加载训练营列表");
    
    const response = await contentAPI.getCampList({
      page: 1,
      limit: 20
    });

    if (response && response.data && response.data.list) {
      this.setData({
        activeCamps: response.data.list,
        imageCamps: response.data.list,
        lastUpdateTime: Date.now()
      });
    }
  } catch (error) {
    console.error("❌ 加载训练营列表失败:", error);
    // 使用模拟数据作为后备
  }
}
```

#### 数据流转
1. 页面加载时调用 `loadHomeData()`
2. 调用 `contentAPI.getCampList()` 获取训练营列表
3. 更新页面数据 `activeCamps` 和 `imageCamps`
4. 渲染训练营卡片列表

### 2. 训练营详情页 (camp-detail)

#### 功能描述
- 展示训练营详细信息
- 显示视频集合和课程内容
- 支持参与训练营功能

#### API集成
- **详情接口**: `GET /api/v1/content/camps/:id`
- **参与接口**: `POST /api/v1/content/camps/:id/join`
- **实现文件**: `pages/camp-detail/camp-detail.js`
- **API封装**: `apis/content.js#getCampDetail()`, `apis/content.js#joinCamp()`

#### 核心代码
```javascript
// 加载训练营详情
async loadCampDetail() {
  try {
    const response = await contentAPI.getCampDetail(this.data.campId);
    
    if (response && response.data) {
      this.setData({
        campDetail: response.data,
        'taskInfo.title': response.data.title,
        'taskInfo.subtitle': response.data.subtitle,
        'taskInfo.keyBenefits': this.parseKeyBenefits(response.data.key_benefits_list),
      });
    }
  } catch (error) {
    console.error("❌ 加载训练营详情失败:", error);
  }
}

// 参与训练营
async joinCamp() {
  try {
    const response = await contentAPI.joinCamp(
      this.data.campId, 
      this.data.currentChild.id
    );
    
    if (response && response.data && response.data.success) {
      wx.showToast({ title: "挑战开始！", icon: "success" });
      setTimeout(() => {
        wx.switchTab({ url: "/pages/growth/growth" });
      }, 2000);
    }
  } catch (error) {
    console.error("❌ 参与训练营失败:", error);
  }
}
```

#### 数据流转
1. 页面加载时获取训练营ID参数
2. 调用 `loadCampDetail()` 获取详情数据
3. 解析JSON字段 (key_benefits, promises, tags)
4. 用户点击参与时调用 `joinCamp()`
5. 参与成功后跳转到成长页面

### 3. 成长页面 (growth)

#### 功能描述
- 展示用户参与的训练营
- 显示训练营进度和统计
- 展示打卡状态和成长轨迹

#### API集成
- **用户训练营**: `GET /api/v1/user/camps`
- **成长数据**: `GET /api/v1/user/growth`
- **实现文件**: `pages/growth/growth.js`
- **API封装**: `apis/content.js#getUserCamps()`

#### 核心代码
```javascript
// 加载用户训练营
async loadActiveTasks() {
  const currentChild = childrenActions.getCurrentChild();
  if (!currentChild || !currentChild.id) return;

  try {
    const response = await contentAPI.getUserCamps(currentChild.id);
    
    let activeTasks = [];
    if (response && response.data && Array.isArray(response.data)) {
      activeTasks = response.data.map((camp) => ({
        id: camp.camp_id,
        campTitle: camp.title,
        checkin_count: camp.total_checkins || 0,
        totalCount: camp.duration_days || 21,
        todayTask: camp.today_status === "completed" ? "今日已完成" : "待完成训练",
        status: camp.participation_status === 1 ? "active" : "completed",
        progressPercentage: camp.progress_percentage || 0
      }));
    }

    // 更新页面数据
    businessActions.setActiveTasks(activeTasks);
    this.updateMainProgress();
  } catch (error) {
    console.error("❌ 加载用户训练营列表失败:", error);
  }
}
```

#### 数据流转
1. 页面显示时检查当前选择的孩子
2. 调用 `loadActiveTasks()` 获取用户训练营
3. 转换API数据为页面需要的格式
4. 更新状态管理器中的业务数据
5. 计算和显示主要进度信息

### 4. 打卡页面 (checkin)

#### 功能描述
- 创建打卡记录
- 填写练习数据和感受
- 上传照片和视频

#### API集成
- **创建打卡**: `POST /api/v1/checkins`
- **实现文件**: `pages/checkin/checkin.js`
- **API封装**: `apis/checkin.js#createCheckin()`

#### 核心代码
```javascript
// 提交打卡
async submitCheckin() {
  const checkinData = {
    child_id: this.data.currentChild.id,
    camp_id: this.data.campId,
    practice_duration: this.data.practiceDuration,
    jump_count_1min: this.data.jumpCount1min,
    jump_count_continuous: this.data.jumpCountContinuous,
    feeling_text: this.data.checkinNote || "今日训练完成",
    feeling_score: this.data.feelingScore,
    photos: [this.data.videoId]
  };

  try {
    const response = await checkinAPI.createCheckin(checkinData);
    
    if (response && response.data) {
      // 跳转到打卡成功页面
      const params = {
        checkin_id: response.data.id,
        points: response.data.points_earned,
        // ... 其他参数
      };
      
      wx.redirectTo({
        url: `/pages/checkin-success/checkin-success?${queryString}`
      });
    }
  } catch (error) {
    console.error("❌ 打卡提交失败:", error);
    wx.showToast({ title: "打卡失败，请重试", icon: "none" });
  }
}
```

#### 数据流转
1. 页面加载时获取训练营和孩子信息
2. 用户填写打卡数据 (练习时长、跳绳个数、感受等)
3. 点击提交时调用 `submitCheckin()`
4. 构建打卡数据对象并发送API请求
5. 成功后跳转到打卡成功页面

## API封装层

### 1. 基础HTTP工具 (apis/base.js)

#### 功能
- 统一的HTTP请求封装
- 自动添加认证头
- 错误处理和重试机制
- 请求和响应日志

#### 核心代码
```javascript
const http = {
  async request(url, options = {}) {
    const token = wx.getStorageSync('token');
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${API.BASE_URL}${url}`,
        method: options.method || 'GET',
        data: options.data,
        header: headers,
        success: resolve,
        fail: reject
      });
    });
  }
};
```

### 2. 训练营API (apis/content.js)

#### 封装的方法
- `getCampList(params)` - 获取训练营列表
- `getCampDetail(campId)` - 获取训练营详情
- `joinCamp(campId, childId)` - 参与训练营
- `getUserCamps(childId)` - 获取用户训练营

#### 特点
- 统一的错误处理
- 详细的日志记录
- 参数验证和格式化

### 3. 打卡API (apis/checkin.js)

#### 封装的方法
- `createCheckin(checkinData)` - 创建打卡记录
- `getCheckinHistory(childId, page, limit)` - 获取打卡历史
- `getTodayCheckinStatus(childId, campId)` - 获取今日状态
- `getCheckinStats(childId)` - 获取打卡统计

## 状态管理

### 业务状态管理
- **activeTasks** - 用户参与的训练营列表
- **recentRecords** - 最近的打卡记录
- **currentChild** - 当前选择的孩子
- **userStats** - 用户统计数据

### 页面状态同步
- 使用事件机制同步跨页面状态
- 登录状态变化时自动更新相关数据
- 孩子切换时重新加载业务数据

## 错误处理

### 网络错误处理
```javascript
try {
  const response = await api.call();
  // 处理成功响应
} catch (error) {
  console.error("API调用失败:", error);
  
  // 显示用户友好的错误提示
  wx.showToast({
    title: "网络异常，请重试",
    icon: "none"
  });
  
  // 使用模拟数据作为后备 (如果适用)
  this.useFallbackData();
}
```

### 降级策略
- API调用失败时使用模拟数据
- 网络异常时显示缓存数据
- 关键功能提供离线模式

## 性能优化

### 数据缓存
- 训练营列表数据缓存
- 用户信息本地存储
- 图片资源预加载

### 请求优化
- 避免重复请求
- 请求去重和防抖
- 分页加载大数据集

### 用户体验
- 加载状态提示
- 骨架屏占位
- 错误重试机制

## 测试验证

### 已验证功能
- ✅ 首页训练营列表加载
- ✅ 训练营详情页展示
- ✅ 训练营参与功能
- ✅ 成长页面数据展示
- ✅ 打卡功能完整流程

### 数据流验证
- ✅ API响应数据正确解析
- ✅ 页面状态正确更新
- ✅ 跨页面数据同步
- ✅ 错误处理机制有效

## 部署配置

### API地址配置
```javascript
// utils/constants.js
const API = {
  BASE_URL: "http://localhost:8080/api/v1", // 开发环境
  // BASE_URL: "https://api.example.com/api/v1", // 生产环境
};
```

### 环境切换
- 开发环境: 本地API服务
- 测试环境: 测试服务器API
- 生产环境: 正式服务器API

## 下一步优化

### 功能增强
1. **离线支持** - 关键数据离线缓存
2. **实时更新** - WebSocket推送
3. **图片上传** - 打卡照片上传功能
4. **分享功能** - 成长数据分享

### 性能优化
1. **懒加载** - 图片和组件懒加载
2. **虚拟列表** - 长列表性能优化
3. **预加载** - 关键页面预加载
4. **CDN加速** - 静态资源CDN

### 用户体验
1. **动画效果** - 页面切换动画
2. **手势操作** - 滑动和手势支持
3. **无障碍** - 无障碍访问支持
4. **多语言** - 国际化支持
