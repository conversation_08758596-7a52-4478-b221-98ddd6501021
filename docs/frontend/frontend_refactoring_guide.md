# 前端改造指南 - 纯净的直接修改表结构方案

## 概述

本指南详细说明了前端如何适配新的基于 `participation_id` 的积分和勋章系统。这是一个**破坏性变更**，需要完全重构前端的相关功能。

## 核心变更概述

### 1. 数据模型变更
- **旧模式**: 基于 `child_id` 的全局统计
- **新模式**: 基于 `participation_id` 的训练营级别统计
- **影响**: 所有积分和勋章相关的API调用都需要修改

### 2. API接口变更
- **删除**: 所有 `/api/child_points/child/:child_id` 和 `/api/child_medals/child/:child_id` 接口
- **新增**: 基于 `/api/points/participation/:participation_id` 和 `/api/medals/participation/:participation_id` 的接口

### 3. 业务逻辑变更
- 积分和勋章现在是训练营特定的，不再是全局的
- 需要在进入训练营时获取 `participation_id`
- 排行榜、统计等都是训练营级别的

## 详细改造步骤

### 第一步：获取 participation_id

#### 1.1 在训练营入口页面获取 participation_id

```javascript
// 旧代码 - 只需要 child_id
const childId = getCurrentChildId();

// 新代码 - 需要获取 participation_id
const getParticipationId = async (childId, campId) => {
  try {
    const response = await wx.request({
      url: `${API_BASE}/api/participations/child/${childId}/camp/${campId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data.id; // participation_id
    } else {
      throw new Error('获取参与记录失败');
    }
  } catch (error) {
    console.error('获取参与记录失败:', error);
    throw error;
  }
};

// 使用示例
const childId = getCurrentChildId();
const campId = getCurrentCampId();
const participationId = await getParticipationId(childId, campId);

// 存储到全局状态或本地存储
wx.setStorageSync('current_participation_id', participationId);
```

#### 1.2 创建全局状态管理

```javascript
// utils/participationManager.js
class ParticipationManager {
  constructor() {
    this.currentParticipationId = null;
    this.currentCampId = null;
    this.currentChildId = null;
  }
  
  // 初始化参与记录
  async initializeParticipation(childId, campId) {
    try {
      this.currentChildId = childId;
      this.currentCampId = campId;
      
      // 获取或创建参与记录
      const participationId = await this.getOrCreateParticipation(childId, campId);
      this.currentParticipationId = participationId;
      
      // 初始化积分和勋章
      await this.initializePointsAndMedals(participationId);
      
      return participationId;
    } catch (error) {
      console.error('初始化参与记录失败:', error);
      throw error;
    }
  }
  
  // 获取或创建参与记录
  async getOrCreateParticipation(childId, campId) {
    try {
      // 先尝试获取现有参与记录
      let response = await wx.request({
        url: `${API_BASE}/api/participations/child/${childId}/camp/${campId}`,
        method: 'GET',
        header: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      if (response.data.code === 200) {
        return response.data.data.id;
      }
      
      // 如果不存在，创建新的参与记录
      response = await wx.request({
        url: `${API_BASE}/api/participations`,
        method: 'POST',
        data: {
          child_id: childId,
          camp_id: campId
        },
        header: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      if (response.data.code === 200) {
        return response.data.data.id;
      } else {
        throw new Error('创建参与记录失败');
      }
    } catch (error) {
      console.error('获取或创建参与记录失败:', error);
      throw error;
    }
  }
  
  // 初始化积分和勋章
  async initializePointsAndMedals(participationId) {
    try {
      // 初始化积分
      await wx.request({
        url: `${API_BASE}/api/points/initialize/${participationId}`,
        method: 'POST',
        header: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      // 初始化勋章
      await wx.request({
        url: `${API_BASE}/api/medals/initialize/${participationId}`,
        method: 'POST',
        header: { 'Authorization': `Bearer ${getToken()}` }
      });
    } catch (error) {
      console.error('初始化积分和勋章失败:', error);
      // 不抛出错误，因为这不是致命错误
    }
  }
  
  // 获取当前参与记录ID
  getCurrentParticipationId() {
    return this.currentParticipationId;
  }
  
  // 清除当前参与记录
  clearCurrentParticipation() {
    this.currentParticipationId = null;
    this.currentCampId = null;
    this.currentChildId = null;
  }
}

// 导出单例
export default new ParticipationManager();
```

### 第二步：积分相关API调用改造

#### 2.1 获取积分信息

```javascript
// 旧代码
const getChildPoints = async (childId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_points/child/${childId}`,
    method: 'GET',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 新代码
const getParticipationPoints = async (participationId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/points/participation/${participationId}`,
    method: 'GET',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 使用示例
const participationId = participationManager.getCurrentParticipationId();
const points = await getParticipationPoints(participationId);
```

#### 2.2 添加积分

```javascript
// 旧代码
const addPoints = async (childId, points, reason) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_points/add`,
    method: 'POST',
    data: {
      child_id: childId,
      points: points,
      reason: reason
    },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data;
};

// 新代码
const addPoints = async (participationId, points, reason, sourceType = 'manual', sourceId = 0) => {
  const response = await wx.request({
    url: `${API_BASE}/api/points/add`,
    method: 'POST',
    data: {
      participation_id: participationId,
      points: points,
      reason: reason,
      source_type: sourceType,
      source_id: sourceId
    },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data;
};

// 使用示例
const participationId = participationManager.getCurrentParticipationId();
await addPoints(participationId, 10, '每日打卡', 'checkin', 0);
```

#### 2.3 获取排行榜

```javascript
// 旧代码 - 全局排行榜
const getGlobalRanking = async (rankType = 'total', limit = 10) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_points/ranking`,
    method: 'GET',
    data: { type: rankType, limit: limit },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 新代码 - 训练营排行榜
const getCampRanking = async (campId, rankType = 'total', limit = 10) => {
  const response = await wx.request({
    url: `${API_BASE}/api/points/ranking/camp/${campId}`,
    method: 'GET',
    data: { type: rankType, limit: limit },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 获取当前参与记录的排名
const getMyRankInCamp = async (participationId, rankType = 'total') => {
  const response = await wx.request({
    url: `${API_BASE}/api/points/ranking/participation/${participationId}`,
    method: 'GET',
    data: { type: rankType },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data.rank;
};

// 使用示例
const campId = participationManager.currentCampId;
const participationId = participationManager.getCurrentParticipationId();

const ranking = await getCampRanking(campId, 'week', 20);
const myRank = await getMyRankInCamp(participationId, 'week');
```

#### 2.4 获取积分历史

```javascript
// 旧代码
const getPointsHistory = async (childId, page = 1, pageSize = 20) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_points/history/${childId}`,
    method: 'GET',
    data: { page: page, page_size: pageSize },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 新代码
const getPointsHistory = async (participationId, page = 1, pageSize = 20) => {
  const response = await wx.request({
    url: `${API_BASE}/api/points/history/participation/${participationId}`,
    method: 'GET',
    data: { page: page, page_size: pageSize },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 使用示例
const participationId = participationManager.getCurrentParticipationId();
const history = await getPointsHistory(participationId, 1, 20);
```

### 第三步：勋章相关API调用改造

#### 3.1 获取勋章信息

```javascript
// 旧代码
const getChildMedals = async (childId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_medals/child/${childId}`,
    method: 'GET',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 新代码
const getParticipationMedals = async (participationId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/medals/participation/${participationId}`,
    method: 'GET',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 获取已解锁的勋章
const getUnlockedMedals = async (participationId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/medals/participation/${participationId}/unlocked`,
    method: 'GET',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data.data;
};

// 使用示例
const participationId = participationManager.getCurrentParticipationId();
const allMedals = await getParticipationMedals(participationId);
const unlockedMedals = await getUnlockedMedals(participationId);
```

#### 3.2 检查和解锁勋章

```javascript
// 旧代码
const checkAndUnlockMedals = async (childId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/child_medals/check/${childId}`,
    method: 'POST',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data;
};

// 新代码
const checkAndUnlockMedals = async (participationId) => {
  const response = await wx.request({
    url: `${API_BASE}/api/medals/check/${participationId}`,
    method: 'POST',
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data;
};

// 手动解锁勋章
const unlockMedal = async (participationId, medalId, pointsEarned = 0) => {
  const response = await wx.request({
    url: `${API_BASE}/api/medals/unlock`,
    method: 'POST',
    data: {
      participation_id: participationId,
      medal_id: medalId,
      points_earned: pointsEarned
    },
    header: { 'Authorization': `Bearer ${getToken()}` }
  });
  return response.data;
};

// 使用示例
const participationId = participationManager.getCurrentParticipationId();
await checkAndUnlockMedals(participationId);
```

### 第四步：页面组件改造

#### 4.1 训练营入口页面改造

```javascript
// pages/camp/index.js
import participationManager from '../../utils/participationManager';

Page({
  data: {
    campId: null,
    participationId: null,
    isLoading: true,
    points: null,
    medals: []
  },
  
  async onLoad(options) {
    try {
      const campId = options.camp_id;
      const childId = getCurrentChildId();
      
      if (!campId || !childId) {
        wx.showToast({ title: '参数错误', icon: 'error' });
        return;
      }
      
      this.setData({ campId: campId });
      
      // 初始化参与记录
      const participationId = await participationManager.initializeParticipation(childId, campId);
      this.setData({ participationId: participationId });
      
      // 加载数据
      await this.loadData();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      this.setData({ isLoading: false });
    }
  },
  
  async loadData() {
    try {
      const participationId = this.data.participationId;
      
      // 并行加载积分和勋章数据
      const [points, medals] = await Promise.all([
        getParticipationPoints(participationId),
        getParticipationMedals(participationId)
      ]);
      
      this.setData({
        points: points,
        medals: medals
      });
      
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({ title: '加载数据失败', icon: 'error' });
    }
  },
  
  // 打卡功能
  async onCheckin() {
    try {
      const participationId = this.data.participationId;
      
      // 执行打卡
      await addPoints(participationId, 10, '每日打卡', 'checkin', 0);
      
      // 检查勋章
      await checkAndUnlockMedals(participationId);
      
      // 重新加载数据
      await this.loadData();
      
      wx.showToast({ title: '打卡成功', icon: 'success' });
      
    } catch (error) {
      console.error('打卡失败:', error);
      wx.showToast({ title: '打卡失败', icon: 'error' });
    }
  },
  
  onUnload() {
    // 页面卸载时清除参与记录
    participationManager.clearCurrentParticipation();
  }
});
```

#### 4.2 排行榜页面改造

```javascript
// pages/ranking/index.js
Page({
  data: {
    campId: null,
    participationId: null,
    rankType: 'total',
    ranking: [],
    myRank: 0,
    isLoading: true
  },
  
  async onLoad(options) {
    try {
      const campId = options.camp_id || participationManager.currentCampId;
      const participationId = participationManager.getCurrentParticipationId();
      
      if (!campId || !participationId) {
        wx.showToast({ title: '请先进入训练营', icon: 'error' });
        wx.navigateBack();
        return;
      }
      
      this.setData({
        campId: campId,
        participationId: participationId
      });
      
      await this.loadRanking();
      
    } catch (error) {
      console.error('排行榜加载失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      this.setData({ isLoading: false });
    }
  },
  
  async loadRanking() {
    try {
      const { campId, participationId, rankType } = this.data;
      
      // 并行加载排行榜和我的排名
      const [ranking, myRank] = await Promise.all([
        getCampRanking(campId, rankType, 50),
        getMyRankInCamp(participationId, rankType)
      ]);
      
      this.setData({
        ranking: ranking.ranking,
        myRank: myRank
      });
      
    } catch (error) {
      console.error('加载排行榜失败:', error);
      throw error;
    }
  },
  
  // 切换排行榜类型
  async onRankTypeChange(e) {
    const rankType = e.detail.value;
    this.setData({ 
      rankType: rankType,
      isLoading: true 
    });
    
    try {
      await this.loadRanking();
    } catch (error) {
      wx.showToast({ title: '切换失败', icon: 'error' });
    } finally {
      this.setData({ isLoading: false });
    }
  }
});
```

### 第五步：数据缓存策略

#### 5.1 本地缓存管理

```javascript
// utils/cacheManager.js
class CacheManager {
  constructor() {
    this.cachePrefix = 'camp_data_';
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟过期
  }
  
  // 生成缓存键
  getCacheKey(participationId, dataType) {
    return `${this.cachePrefix}${participationId}_${dataType}`;
  }
  
  // 设置缓存
  setCache(participationId, dataType, data) {
    const cacheKey = this.getCacheKey(participationId, dataType);
    const cacheData = {
      data: data,
      timestamp: Date.now()
    };
    wx.setStorageSync(cacheKey, cacheData);
  }
  
  // 获取缓存
  getCache(participationId, dataType) {
    try {
      const cacheKey = this.getCacheKey(participationId, dataType);
      const cacheData = wx.getStorageSync(cacheKey);
      
      if (!cacheData) {
        return null;
      }
      
      // 检查是否过期
      if (Date.now() - cacheData.timestamp > this.cacheExpiry) {
        wx.removeStorageSync(cacheKey);
        return null;
      }
      
      return cacheData.data;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }
  
  // 清除特定参与记录的缓存
  clearParticipationCache(participationId) {
    const keys = wx.getStorageInfoSync().keys;
    const prefix = `${this.cachePrefix}${participationId}_`;
    
    keys.forEach(key => {
      if (key.startsWith(prefix)) {
        wx.removeStorageSync(key);
      }
    });
  }
  
  // 清除所有缓存
  clearAllCache() {
    const keys = wx.getStorageInfoSync().keys;
    
    keys.forEach(key => {
      if (key.startsWith(this.cachePrefix)) {
        wx.removeStorageSync(key);
      }
    });
  }
}

export default new CacheManager();
```

#### 5.2 带缓存的API调用

```javascript
// utils/apiWithCache.js
import cacheManager from './cacheManager';

// 带缓存的获取积分信息
export const getParticipationPointsWithCache = async (participationId, forceRefresh = false) => {
  const cacheKey = 'points';
  
  // 如果不强制刷新，先尝试从缓存获取
  if (!forceRefresh) {
    const cachedData = cacheManager.getCache(participationId, cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }
  
  // 从API获取数据
  const data = await getParticipationPoints(participationId);
  
  // 缓存数据
  cacheManager.setCache(participationId, cacheKey, data);
  
  return data;
};

// 带缓存的获取勋章信息
export const getParticipationMedalsWithCache = async (participationId, forceRefresh = false) => {
  const cacheKey = 'medals';
  
  if (!forceRefresh) {
    const cachedData = cacheManager.getCache(participationId, cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }
  
  const data = await getParticipationMedals(participationId);
  cacheManager.setCache(participationId, cacheKey, data);
  
  return data;
};
```

### 第六步：错误处理和用户体验

#### 6.1 统一错误处理

```javascript
// utils/errorHandler.js
export const handleApiError = (error, context = '') => {
  console.error(`${context} 错误:`, error);
  
  let message = '操作失败，请重试';
  
  if (error.response) {
    // API返回的错误
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        message = data.message || '请求参数错误';
        break;
      case 401:
        message = '登录已过期，请重新登录';
        // 跳转到登录页
        wx.redirectTo({ url: '/pages/login/index' });
        return;
      case 403:
        message = '没有权限执行此操作';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 500:
        message = '服务器错误，请稍后重试';
        break;
      default:
        message = data.message || '网络错误，请检查网络连接';
    }
  } else if (error.message) {
    message = error.message;
  }
  
  wx.showToast({
    title: message,
    icon: 'error',
    duration: 3000
  });
};
```

#### 6.2 加载状态管理

```javascript
// utils/loadingManager.js
class LoadingManager {
  constructor() {
    this.loadingCount = 0;
  }
  
  // 显示加载
  showLoading(title = '加载中...') {
    this.loadingCount++;
    if (this.loadingCount === 1) {
      wx.showLoading({ title: title, mask: true });
    }
  }
  
  // 隐藏加载
  hideLoading() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      wx.hideLoading();
    }
  }
  
  // 强制隐藏加载
  forceHideLoading() {
    this.loadingCount = 0;
    wx.hideLoading();
  }
}

export default new LoadingManager();
```

## 测试验证清单

### 1. 功能测试
- [ ] 训练营入口正确获取 participation_id
- [ ] 积分显示和添加功能正常
- [ ] 勋章显示和解锁功能正常
- [ ] 排行榜显示正确的训练营数据
- [ ] 积分历史记录正确显示

### 2. 边界情况测试
- [ ] 网络异常时的错误处理
- [ ] 参与记录不存在时的处理
- [ ] 缓存过期时的数据刷新
- [ ] 多个训练营切换时的数据隔离

### 3. 性能测试
- [ ] 页面加载速度
- [ ] 数据缓存效果
- [ ] 内存使用情况

## 注意事项

1. **数据隔离**: 确保不同训练营的数据完全隔离
2. **缓存管理**: 合理使用缓存，避免显示过期数据
3. **错误处理**: 提供友好的错误提示和降级方案
4. **性能优化**: 使用并行请求和数据缓存提升用户体验
5. **向后兼容**: 这是破坏性变更，需要确保所有相关功能都已更新

## 发布计划

1. **开发环境测试**: 完成所有功能测试
2. **内测版本**: 小范围用户测试
3. **灰度发布**: 逐步扩大用户范围
4. **全量发布**: 确认无问题后全量发布
5. **监控和回滚**: 密切监控，必要时快速回滚
