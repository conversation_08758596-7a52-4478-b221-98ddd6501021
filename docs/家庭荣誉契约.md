好的，我们现在就将“家庭荣誉契约”这个经过反复打磨、作为我们产品“情感价值”核心引擎的独特功能，进行一次全面、系统的梳理和整理。

这份文档可以作为您未来交付给开发者的详细功能说明。

---

### **“家庭荣誉契约”功能模块详细说明**

**1. 战略定位与核心价值**

“家庭荣誉契约”不是一个简单的任务或奖励功能，它的战略定位是：

* **一个亲子关系的“粘合剂”**：将原本可能因“教与学”产生矛盾的亲子关系，转变为“并肩作战”的团队关系。
* **一个激励机制的“升华器”**：将家长习惯的“物质奖励”，包装和升华为充满仪式感的“家庭荣誉事件”，引导更积极的教育方式。
* **一个产品粘性的“超级锚点”**：通过将线下的承诺与线上的核心荣誉（稀有勋章）深度绑定，创造了用户必须返回平台完成闭环的强理由。

**2. 用户旅程 (User Journey)**

用户从了解到创建，再到完成契约的完整路径如下：

`在训练营详情页了解该功能 -> 加入挑战后被引导创建 -> 设置契约内容 -> 邀请家人见证 -> 在个人主页展示并追踪进度 -> 达成目标 -> 家长点击确认履约 -> 孩子获得最终荣誉勋章`

**3. 页面与功能详细设计 (Page & Functional Design)**

#### **模块一：功能预告与发现 (在训练营详情页)**

* **UI表现**：在详情页（如您提供的图片所示）的一个独立模块中，用醒目的图标（如🏆或🚩）和标题进行展示。
* **标题**：`独家功能：创建你们的“家庭荣誉契约”`
* **文案**：`全家总动员，见证孩子的每一次进步！`
* **交互**：点击“了解详情”可弹出简要图文介绍，说明该功能的作用和价值。
* **目的**：在用户加入前就“种草”，提升训练营的吸引力。

#### **模块二：契约创建流程 (用户加入挑战后)**

这是功能的核心交互流程，设计上强调“仪式感”和“可选性”。

* **触发时机**：用户点击“立即参加21天挑战”并成功加入后，系统会弹出一个**非强制性**的引导浮层。
* **引导弹窗 (UI)**：
    * **插画**：可爱的“跳跳”兔子递出一个奖杯或绶带。
    * **标题**：`要不要和家人立下“荣誉契约”？`
    * **文案**：`一个小小的约定，一份大大的动力！`
    * **按钮**：
        * **主按钮 (活力橙)**：`[ 立即创建 ]`
        * **次要链接**：`下次再说，直接开始挑战` (必须保留此出口)

* **创建页面 (UI)**：
    1.  **选择孩子**：(若有多个孩子)
    2.  **选择契约目标 (模板化)**：
        * `◎ 挑战“连续打卡21天”`
        * `◎ 挑战“学会5个新花样”`
        * `◎ 挑战“1分钟跳绳达到XX个”`
    3.  **约定荣誉奖励 (引导式)**：
        * **标题**：`当挑战成功时，我们将共同庆祝并奖励...`
        * **推荐选项 (多选)**：
            * `□ 一次“家庭电影之夜”`
            * `□ 周末去一次“科技馆”`
            * `□ 增加30分钟“自由游戏时间”`
            * `□ 孩子最想要的一个“神秘礼物”`
        * **自定义输入框**：`[ 或者，自定义我们的约定... ]`
    4.  **邀请见证人**：
        * **标题**：`邀请一位家人作为“首席见证官”吧！`
        * **按钮**：`[+]` 点击后可通过微信分享邀请链接给家人。被邀请者点击后，其头像和昵称会显示在契约上。
    5.  **确认创建**：一个巨大的、充满仪式感的按钮 `[ 我们的荣誉契约，即刻生效！ ]`

#### **模块三：契约的展示与追踪 (在“成长”页与“我的”页)**

* **展示形式**：一张精美的电子卡片，作为独立的、高优先级的模块。
* **卡片要素**：
    * 孩子的头像和昵称。
    * 契约目标（如：“21天挑战”）。
    * 荣誉奖励（如：“奖励：一次科技馆之旅”）。
    * 见证人列表（展示所有已加入家人的小头像）。
    * 一个醒目的**实时进度条**（如：`进度：14/21天`）。
* **展示位置**：
    * **“成长”页顶部**：作为当前最重要的任务，时刻提醒。
    * **“我的”个人主页**：作为一份荣誉资产进行陈列。

#### **模块四：契约达成与授勋仪式 (核心闭环)**

这是整个功能的“高光时刻”，也是绑定平台价值的关键。

* **触发条件**：当契约目标（如连续打卡21天）在系统中达成时。
* **系统行为**：
    1.  **为孩子弹出庆祝动画**：一个全屏的、盛大的祝贺动画，告知他已成功完成契约挑战。
    2.  **向家长（创建者/见证人）发送通知**：通过小程序内的消息红点或服务通知，发送：“恭喜！小明的【21天挑战契约】已达成，请您为他兑现奖励，并亲自为他颁发荣誉勋章！”
* **家长端操作 (UI)**：
    * 家长在小程序中会看到，原来的契约卡片状态已变为“已达成，待授勋”。
    * 卡片上出现一个唯一的、高亮的按钮：**`[ 我已兑现奖励，为TA授勋！ ]`**
* **最终奖励**：
    * 只有当家长点击了**“为TA授勋”**按钮后：
        * 那枚代表最高荣誉的、独一无二的**【家庭契约成就勋章】**才会被**正式点亮**在孩子的勋章墙上。
        * 系统会生成一张最终的“契约达成荣誉海报”，供全家分享。

通过这一整套闭环设计，“家庭荣誉契约”不再是一个简单的功能，而是深度融入了家庭关系、仪式感和平台核心激励体系的“超级功能”，它将有效提升用户的情感投入和长期留存。