# 📚 儿童跳绳学习平台文档

## 🎯 项目概述
专为儿童跳绳学习设计的后端平台，支持API/Admin双服务架构，内置代码生成器和完整工具链。

## 📁 文档结构

```
docs/
├── README.md                    # 📚 文档导航
├── ARCHITECTURE.md              # 🏗️ 项目架构
├── PROJECT_DIRECTORY_STRUCTURE.md  # 📁 目录结构规范
├── PATH_MANAGEMENT_RULES.md     # 🛣️ 路径管理规则
├── conversation-modes.md        # 💬 对话模式系统
├── 01-planning/                 # 📋 项目规划
│   ├── requirements/           # 需求分析
│   ├── architecture/           # 架构设计
│   └── roadmap/                # 开发路线图
├── 02-design/                   # 🎨 详细设计
│   ├── database/               # 数据库设计
│   ├── api/                    # API设计
│   └── frontend/               # 前端设计
├── 03-modules/                  # 🧩 功能模块文档
│   ├── user/                   # 用户模块
│   ├── checkin/                # 打卡模块
│   └── community/              # 社团模块
├── 04-operations/               # ⚙️ 运维文档
│   ├── deployment/             # 部署文档
│   └── maintenance/            # 维护文档
├── standards/                   # 📋 项目标准（主要）
│   ├── README.md
│   ├── collaboration/          # 协作规范
│   ├── development/            # 开发标准
│   └── templates/              # 模板库
├── knowledge/                   # 🧠 核心知识库
│   ├── README.md
│   ├── mvs-core-rules.md       # MVS核心规则
│   ├── ai-collaboration-guide.md  # AI协作指南
│   ├── decisions/              # 架构决策记录
│   ├── patterns/               # 设计模式
│   ├── problems/               # 问题解决
│   └── templates/              # 文档模板
└── reports/                     # 📊 项目报告
    ├── README.md
    ├── OPTIMIZATION_COMPLETION_REPORT.md
    ├── OPTIMIZATION_RECOMMENDATIONS.md
    ├── PKG_BUG_FIXES_REPORT.md
    └── REFACTORING_REPORT.md
```

### 📋 项目规划 ⭐ **新增**
- [`01-planning/`](01-planning/) - **项目规划文档**
  - [需求分析](01-planning/requirements/) - 用户需求和功能规划
  - [架构设计](01-planning/architecture/) - 系统架构和技术选型
  - [开发路线图](01-planning/roadmap/) - 项目里程碑和时间规划

### 🎨 详细设计
- [`02-design/`](02-design/) - **系统详细设计**
  - [数据库设计](02-design/database/) - 数据模型和表结构
  - [API设计](02-design/api/) - 接口规范和文档
  - [前端设计](02-design/frontend/) - UI/UX设计和组件规范

### 🧩 功能模块
- [`03-modules/`](03-modules/) - **业务模块文档**
  - [用户模块](03-modules/user/) - 用户管理相关功能
  - [打卡模块](03-modules/checkin/) - 跳绳打卡系统
  - [社团模块](03-modules/community/) - 社团管理功能

### ⚙️ 运维部署
- [`04-operations/`](04-operations/) - **运维和部署**
  - [部署文档](04-operations/deployment/) - 环境部署和配置
  - [维护文档](04-operations/maintenance/) - 系统维护和监控

### 💬 协作工具
- [对话模式系统](conversation-modes.md) - AI协作的对话模式规范

### 📋 项目标准 ⭐ **主要**
- [`standards/`](standards/) - **项目标准文档**
  - [协作规范](standards/collaboration/) - 团队协作和沟通标准
  - [开发标准](standards/development/) - 代码开发和质量标准
  - [模板库](standards/templates/) - 各类开发模板

### 🧠 核心知识库
- [`knowledge/`](knowledge/) - **项目核心资产**
  - [MVS核心规则](knowledge/mvs-core-rules.md) - 开发规范
  - [AI协作指南](knowledge/ai-collaboration-guide.md) - 协作方式
  - [架构决策记录](knowledge/decisions/) - 重要决策
  - [设计模式](knowledge/patterns/) - 最佳实践
  - [问题解决](knowledge/problems/) - 问题记录

### 📊 项目报告
- [`reports/`](reports/) - **分析报告和总结**
  - [优化报告](reports/OPTIMIZATION_COMPLETION_REPORT.md) - 优化任务完成情况
  - [修复报告](reports/PKG_BUG_FIXES_REPORT.md) - 包依赖修复记录
  - [重构报告](reports/REFACTORING_REPORT.md) - 代码重构总结

### 📋 核心文档
- [项目架构](ARCHITECTURE.md) - 系统架构设计
- [目录结构](PROJECT_DIRECTORY_STRUCTURE.md) - 项目结构规范
- [路径管理](PATH_MANAGEMENT_RULES.md) - 路径管理规则

## 🚀 快速开始

### AI助手启动检查清单
1. **必读标准文档** 📋
   - 阅读 [`standards/README.md`](standards/README.md) 了解项目标准体系
   - 阅读 [`standards/collaboration/collaboration-guidelines.md`](standards/collaboration/collaboration-guidelines.md) 理解协作规范
2. **核心知识库** 🧠
   - 阅读 [`knowledge/README.md`](knowledge/README.md) 了解项目核心资产
   - 阅读 [`knowledge/mvs-core-rules.md`](knowledge/mvs-core-rules.md) 理解开发标准
   - 阅读 [`knowledge/ai-collaboration-guide.md`](knowledge/ai-collaboration-guide.md) 了解协作方式
3. **重要决策** 📊
   - 查阅 [`knowledge/decisions/`](knowledge/decisions/) 了解重要决策

### 开发者快速开始
1. **了解标准** 📋
   - 阅读 [`standards/collaboration/workflow-standards.md`](standards/collaboration/workflow-standards.md) 了解工作流程
   - 阅读 [`standards/development/development-standards-framework.md`](standards/development/development-standards-framework.md) 了解开发标准
2. **项目架构** 🏗️
   - 了解项目架构和技术栈
   - 熟悉开发规范和流程
3. **开始开发** 🚀
   - 查看相关模板和示例
   - 使用标准工具进行开发

## 🎯 核心工具集成

### 标准检查工具
```bash
# 运行标准合规性检查
./tools/mvs-check.sh
```

### 代码生成工具
```bash
# 生成符合标准的代码模块
./tools/generate-module.sh module_name api
```

---

**核心原则**：严格遵守MVS规则，使用场景化协作模式，及时记录重要决策，遵循项目标准文档
