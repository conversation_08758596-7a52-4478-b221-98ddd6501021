# 儿童学习平台数据库设计文档

## 📋 项目概述

**项目名称**: 儿童学习平台  
**技术栈**: Go 1.23.10 + GORM + MySQL + Redis  
**设计理念**: 基于混合设计方案，结合temp_video_camp_design.sql和09-training-camps-simple.sql的优点，补充核心业务功能  
**测试环境**: 用户ID=2, 当前孩子ID=11

## 🎯 设计原则

1. **遵循第三范式**: 避免数据冗余，但保留必要的性能优化字段
2. **一致性命名**: 统一使用snake_case，避免NULL值，使用默认值
3. **软删除设计**: 核心表支持deleted_at字段，保证数据可恢复性
4. **索引优化**: 基于查询模式设计合理索引，提升查询性能
5. **应用层验证**: 外键关联在应用层验证，数据库层不设置外键约束

## 📊 表结构总览

### 系统分类

| 系统模块 | 表数量 | 核心表 | 数据来源 |
|----------|--------|--------|----------|
| **基础内容系统** | 4张 | videos, video_collections | temp_video_camp_design.sql |
| **训练营系统** | 2张 | training_camps, user_camp_participations | 09-training-camps-simple.sql |
| **打卡积分系统** | 3张 | checkin_records, child_points | 新设计 |
| **家庭契约系统** | 2张 | family_contracts, contract_witnesses | 新设计 |
| **成长系统** | 3张 | growth_tracks, medals, child_medals | 新设计 |
| **社交系统** | 2张 | likes, comments | 新设计 |
| **总计** | **16张** | - | 混合设计 |

## 🔗 核心关联关系

### 主要数据流

```mermaid
graph TD
    A[用户选择训练营] --> B[user_camp_participations]
    B --> C[每日打卡]
    C --> D[checkin_records]
    D --> E[积分奖励]
    E --> F[child_points]
    F --> G[排行榜]
    
    D --> H[家庭契约进度]
    H --> I[family_contracts]
    I --> J[见证人系统]
    J --> K[contract_witnesses]
    
    F --> L[成长里程碑]
    L --> M[growth_tracks]
    M --> N[勋章解锁]
    N --> O[child_medals]
    
    D --> P[社交互动]
    P --> Q[likes/comments]
```

### 关键关联字段

| 关联类型 | 主表 | 外键字段 | 关联表 | 说明 |
|----------|------|----------|--------|------|
| 用户-孩子 | users | current_child_id | children.id | 当前选择的孩子 |
| 训练营参与 | user_camp_participations | camp_id, child_id | training_camps.id, children.id | 参与关系 |
| 打卡记录 | checkin_records | child_id, camp_id | children.id, training_camps.id | 打卡归属 |
| 积分统计 | child_points | child_id | children.id | 积分归属 |
| 家庭契约 | family_contracts | child_id, camp_id | children.id, training_camps.id | 契约关系 |
| 成长记录 | growth_tracks | child_id | children.id | 成长归属 |
| 社交互动 | likes/comments | target_id | checkin_records.id | 社交目标 |

## 📈 业务流程支持

### 1. 训练营学习流程
1. **选择训练营** → training_camps表查询
2. **参与训练营** → user_camp_participations表记录
3. **观看视频** → videos + video_collections关联
4. **进度跟踪** → participation_status字段更新

### 2. 每日打卡流程
1. **记录打卡** → checkin_records表插入
2. **计算积分** → point_records表记录变动
3. **更新统计** → child_points表更新总积分
4. **检查里程碑** → 触发growth_tracks记录
5. **勋章解锁** → child_medals表更新进度

### 3. 家庭契约流程
1. **创建契约** → family_contracts表插入
2. **邀请见证人** → contract_witnesses表记录
3. **进度跟踪** → 基于打卡记录自动更新
4. **完成授勋** → contract_status状态变更

### 4. 社交互动流程
1. **点赞评论** → likes/comments表记录
2. **更新统计** → 相关计数字段更新
3. **消息通知** → 后续可扩展notifications表

## 🎯 性能优化策略

### 索引设计

| 表名 | 索引类型 | 索引字段 | 用途 |
|------|----------|----------|------|
| checkin_records | 唯一索引 | (child_id, checkin_date) | 防止重复打卡 |
| child_points | 复合索引 | (total_points DESC) | 排行榜查询 |
| family_contracts | 复合索引 | (child_id, contract_status) | 契约状态查询 |
| likes | 复合索引 | (target_type, target_id) | 社交查询 |
| growth_tracks | 时间索引 | (created_at DESC) | 成长记录时间排序 |

### 冗余字段策略

| 表名 | 冗余字段 | 来源 | 更新策略 |
|------|----------|------|----------|
| child_points | total_points, week_points | point_records聚合 | 实时更新 |
| training_camps | total_participants | user_camp_participations计数 | 定时同步 |
| videos | view_count, like_count | 用户行为统计 | 异步更新 |
| comments | like_count | likes表计数 | 实时更新 |

## 🔒 数据一致性保证

### 事务处理
- **打卡流程**: checkin_records + child_points + point_records 三表事务
- **契约完成**: family_contracts + growth_tracks + child_medals 联动更新
- **积分变动**: child_points + point_records 强一致性

### 软删除处理
- 所有查询自动添加 `deleted_at IS NULL` 条件
- 删除操作只更新deleted_at字段，不物理删除
- 定期清理过期的软删除数据

### 数据校验
- 应用层验证外键关联的有效性
- 业务规则校验（如打卡日期不能重复）
- 数据范围校验（如积分不能为负数）

## 📊 测试数据说明

### 测试环境配置
- **用户ID**: 2
- **当前孩子ID**: 11
- **测试场景**: 完整的学习成长流程

### 数据覆盖范围
- ✅ 5个视频分类，5个教学视频
- ✅ 3个视频集合，3个训练营
- ✅ 7天打卡记录，590总积分
- ✅ 2个家庭契约（1个进行中，1个已完成）
- ✅ 4条成长轨迹，5个勋章定义
- ✅ 完整的社交互动数据

## 🚀 扩展性考虑

### 预留扩展点
1. **消息通知系统**: 可新增notifications表
2. **会员权益系统**: 可扩展user_memberships表
3. **课程评价系统**: 可新增course_reviews表
4. **学习报告系统**: 可新增learning_reports表

### 性能扩展
1. **读写分离**: 支持主从数据库配置
2. **分表策略**: 大数据量表可按时间分表
3. **缓存策略**: 热点数据Redis缓存
4. **搜索优化**: 可集成Elasticsearch

## 📝 维护说明

### 定期维护任务
1. **统计数据同步**: 每日凌晨同步冗余统计字段
2. **排行榜更新**: 每周一更新week_rank字段
3. **软删除清理**: 每月清理超过6个月的软删除数据
4. **索引优化**: 定期分析慢查询，优化索引

### 监控指标
- 数据库连接数和查询性能
- 核心表的数据增长趋势
- 索引使用效率和命中率
- 事务执行时间和成功率

---

**文档版本**: v1.0  
**创建时间**: 2024-01-08  
**维护人员**: 开发团队  
**更新记录**: 初始版本，完整数据库设计方案
