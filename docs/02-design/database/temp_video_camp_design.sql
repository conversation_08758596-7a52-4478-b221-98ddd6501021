-- =====================================================
-- 视频内容和训练营系统数据库设计
-- 支持视频集合和灵活的付费模式
-- =====================================================

-- 1. 视频分类表
CREATE TABLE video_categories (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID，主键自增',
    
    -- 分类基本信息
    name VARCHAR(50) NOT NULL DEFAULT '' COMMENT '分类名称，如：跳绳、魔术、国学、故事、书法、数学',
    description VARCHAR(200) DEFAULT '' COMMENT '分类描述，详细说明分类内容',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '分类封面图URL',
    
    -- 分类属性
    parent_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父分类ID，支持二级分类，0表示顶级分类',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重，数值越大越靠前',
    
    -- 状态字段
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '分类状态 1:启用 2:禁用',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_parent_id (parent_id),
    INDEX idx_status_sort (status, sort_order DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';

-- 2. 视频表
CREATE TABLE videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频ID，主键自增',
    
    -- 视频基本信息
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '视频标题，最多200字符',
    description TEXT COMMENT '视频描述，详细介绍视频内容',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '视频封面图URL',
    
    -- 视频文件信息
    video_url VARCHAR(500) DEFAULT '' COMMENT '视频文件URL，微信视频号链接或其他平台链接',
    video_source TINYINT UNSIGNED DEFAULT 1 COMMENT '视频来源 1:微信视频号 2:本地上传 3:第三方平台',
    video_id_external VARCHAR(100) DEFAULT '' COMMENT '外部视频ID，微信视频号等平台的视频标识',
    duration INT UNSIGNED DEFAULT 0 COMMENT '视频时长（秒）',
    
    -- 视频分类和属性
    category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '主分类ID，关联video_categories.id',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1:新手 2:初级 3:中级 4:高级 5:专业',
    age_group VARCHAR(20) DEFAULT '' COMMENT '适用年龄段，如"3-6岁"、"7-12岁"',
    tags VARCHAR(500) DEFAULT '' COMMENT '视频标签，逗号分隔',
    
    -- 创作者信息
    creator_type TINYINT UNSIGNED DEFAULT 1 COMMENT '创作者类型 1:官方 2:用户 3:合作方',
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创作者用户ID',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创作者名称',
    
    -- 统计数据（冗余字段）
    view_count BIGINT UNSIGNED DEFAULT 0 COMMENT '播放次数',
    like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数量',
    comment_count INT UNSIGNED DEFAULT 0 COMMENT '评论数量',
    share_count INT UNSIGNED DEFAULT 0 COMMENT '分享次数',
    
    -- 推荐权重
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分 0.00-5.00',
    popularity_score DECIMAL(8,2) DEFAULT 0.00 COMMENT '热度评分',
    recommendation_weight INT UNSIGNED DEFAULT 0 COMMENT '推荐权重',
    
    -- 审核状态
    audit_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:待审核 2:审核通过 3:审核拒绝',
    audit_reason VARCHAR(200) DEFAULT '' COMMENT '审核原因',
    
    -- 视频状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '视频状态 1:正常 2:下架 3:删除',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐',
    
    -- 时间记录
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_category_id (category_id),
    INDEX idx_creator_user_id (creator_user_id),
    INDEX idx_status_published (status, published_at DESC),
    INDEX idx_difficulty_age (difficulty_level, age_group),
    INDEX idx_audit_status (audit_status),
    FULLTEXT idx_search (title, description, tags)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频表';

-- 3. 视频集合表（核心新增）
CREATE TABLE video_collections (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频集合ID，主键自增',
    
    -- 集合基本信息
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '集合标题，如"跳绳基础教程集"',
    description TEXT COMMENT '集合描述，详细介绍集合内容和学习目标',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '集合封面图URL',
    
    -- 集合属性
    category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '主分类ID，关联video_categories.id',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1:新手 2:初级 3:中级 4:高级 5:专业',
    age_group VARCHAR(20) DEFAULT '' COMMENT '适用年龄段',
    tags VARCHAR(500) DEFAULT '' COMMENT '集合标签，逗号分隔',
    
    -- 付费设置（核心功能）
    is_free TINYINT UNSIGNED DEFAULT 1 COMMENT '是否完全免费 0:付费 1:免费',
    free_video_count INT UNSIGNED DEFAULT 0 COMMENT '免费视频数量，前N集免费',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '集合价格，0.00表示免费',
    
    -- 创作者信息
    creator_type TINYINT UNSIGNED DEFAULT 1 COMMENT '创作者类型 1:官方 2:用户 3:合作方',
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创作者用户ID',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创作者名称',
    
    -- 统计数据
    video_count INT UNSIGNED DEFAULT 0 COMMENT '视频总数量',
    total_duration INT UNSIGNED DEFAULT 0 COMMENT '总时长（秒）',
    view_count BIGINT UNSIGNED DEFAULT 0 COMMENT '总播放次数',
    like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数量',
    collect_count INT UNSIGNED DEFAULT 0 COMMENT '收藏数量',
    
    -- 推荐权重
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分 0.00-5.00',
    popularity_score DECIMAL(8,2) DEFAULT 0.00 COMMENT '热度评分',
    recommendation_weight INT UNSIGNED DEFAULT 0 COMMENT '推荐权重',
    
    -- 状态字段
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '集合状态 1:正常 2:下架 3:删除',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐',
    
    -- 时间记录
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_category_id (category_id),
    INDEX idx_creator_user_id (creator_user_id),
    INDEX idx_status_published (status, published_at DESC),
    INDEX idx_difficulty_age (difficulty_level, age_group),
    INDEX idx_price_free (is_free, price),
    FULLTEXT idx_search (title, description, tags)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频集合表';

-- 4. 集合视频关联表
CREATE TABLE collection_videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID，主键自增',

    -- 关联信息
    collection_id BIGINT UNSIGNED NOT NULL COMMENT '视频集合ID，关联video_collections.id',
    video_id BIGINT UNSIGNED NOT NULL COMMENT '视频ID，关联videos.id',

    -- 排序和属性
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '视频在集合中的排序，从1开始',
    is_required TINYINT UNSIGNED DEFAULT 1 COMMENT '是否必看 0:可选 1:必看',
    is_free TINYINT UNSIGNED DEFAULT 1 COMMENT '是否免费 0:付费 1:免费，覆盖集合设置',

    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_collection_video (collection_id, video_id),
    INDEX idx_collection_sort (collection_id, sort_order),
    INDEX idx_video_id (video_id),

    -- 外键约束
    FOREIGN KEY (collection_id) REFERENCES video_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='集合视频关联表';

-- 5. 训练营表
CREATE TABLE camps (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '训练营ID，主键自增',

    -- 训练营基本信息
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '训练营标题',
    subtitle VARCHAR(200) DEFAULT '' COMMENT '训练营副标题',
    description TEXT COMMENT '训练营详细描述',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '训练营封面图URL',

    -- 训练营属性
    category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '主分类ID，关联video_categories.id',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1:新手 2:初级 3:中级 4:高级 5:专业',
    age_group VARCHAR(20) DEFAULT '' COMMENT '适用年龄段',
    tags VARCHAR(500) DEFAULT '' COMMENT '训练营标签，逗号分隔',

    -- 训练营设置
    total_days INT UNSIGNED DEFAULT 21 COMMENT '训练营总天数',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '训练营价格，0.00表示免费',
    is_free TINYINT UNSIGNED DEFAULT 1 COMMENT '是否免费 0:付费 1:免费',

    -- 时间设置
    start_date DATE DEFAULT NULL COMMENT '开始日期，NULL表示随时开始',
    end_date DATE DEFAULT NULL COMMENT '结束日期，NULL表示无限期',

    -- 创作者信息
    creator_type TINYINT UNSIGNED DEFAULT 1 COMMENT '创作者类型 1:官方 2:用户 3:合作方',
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创作者用户ID',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创作者名称',

    -- 统计数据
    participant_count INT UNSIGNED DEFAULT 0 COMMENT '参与人数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分 0.00-5.00',

    -- 推荐权重
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分 0.00-5.00',
    popularity_score DECIMAL(8,2) DEFAULT 0.00 COMMENT '热度评分',
    recommendation_weight INT UNSIGNED DEFAULT 0 COMMENT '推荐权重',

    -- 状态字段
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '训练营状态 1:活跃 2:即将开始 3:已结束 4:暂停',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐',

    -- 时间记录
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_category_id (category_id),
    INDEX idx_creator_user_id (creator_user_id),
    INDEX idx_status_published (status, published_at DESC),
    INDEX idx_difficulty_age (difficulty_level, age_group),
    INDEX idx_price_free (is_free, price),
    INDEX idx_dates (start_date, end_date),
    FULLTEXT idx_search (title, subtitle, description, tags)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营表';

-- 6. 训练营集合关联表
CREATE TABLE camp_collections (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID，主键自增',

    -- 关联信息
    camp_id BIGINT UNSIGNED NOT NULL COMMENT '训练营ID，关联camps.id',
    collection_id BIGINT UNSIGNED NOT NULL COMMENT '视频集合ID，关联video_collections.id',

    -- 排序和属性
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '集合在训练营中的排序',
    is_required TINYINT UNSIGNED DEFAULT 1 COMMENT '是否必学 0:可选 1:必学',
    unlock_day INT UNSIGNED DEFAULT 1 COMMENT '解锁天数，第几天解锁该集合',

    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_camp_collection (camp_id, collection_id),
    INDEX idx_camp_sort (camp_id, sort_order),
    INDEX idx_collection_id (collection_id),
    INDEX idx_unlock_day (unlock_day),

    -- 外键约束
    FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE CASCADE,
    FOREIGN KEY (collection_id) REFERENCES video_collections(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营集合关联表';

-- 7. 用户训练营参与表
CREATE TABLE user_camps (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '参与记录ID，主键自增',

    -- 用户信息
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID，关联users.id',
    child_id BIGINT UNSIGNED NOT NULL COMMENT '孩子ID，关联children.id',
    camp_id BIGINT UNSIGNED NOT NULL COMMENT '训练营ID，关联camps.id',

    -- 参与状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '参与状态 1:进行中 2:已完成 3:已暂停 4:已退出',
    progress_days INT UNSIGNED DEFAULT 0 COMMENT '已完成天数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比',

    -- 时间记录
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '参加时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    completed_at TIMESTAMP NULL DEFAULT NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_child_camp (user_id, child_id, camp_id),
    INDEX idx_user_id (user_id),
    INDEX idx_child_id (child_id),
    INDEX idx_camp_id (camp_id),
    INDEX idx_status (status),
    INDEX idx_joined_at (joined_at),

    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE CASCADE,
    FOREIGN KEY (camp_id) REFERENCES camps(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户训练营参与表';

-- 8. 视频观看记录表
CREATE TABLE video_views (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '观看记录ID，主键自增',

    -- 用户信息
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID，关联users.id',
    child_id BIGINT UNSIGNED NOT NULL COMMENT '孩子ID，关联children.id',
    video_id BIGINT UNSIGNED NOT NULL COMMENT '视频ID，关联videos.id',
    collection_id BIGINT UNSIGNED DEFAULT 0 COMMENT '视频集合ID，关联video_collections.id',
    camp_id BIGINT UNSIGNED DEFAULT 0 COMMENT '训练营ID，关联camps.id，通过训练营观看时记录',

    -- 观看数据
    watch_duration INT UNSIGNED DEFAULT 0 COMMENT '观看时长（秒）',
    total_duration INT UNSIGNED DEFAULT 0 COMMENT '视频总时长（秒）',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比',
    watch_count INT UNSIGNED DEFAULT 1 COMMENT '观看次数',

    -- 观看环境
    device_type VARCHAR(20) DEFAULT '' COMMENT '设备类型',
    platform VARCHAR(20) DEFAULT '' COMMENT '平台类型',

    -- 时间记录
    first_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次观看时间',
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后观看时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_child_video (user_id, child_id, video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_child_id (child_id),
    INDEX idx_video_id (video_id),
    INDEX idx_collection_id (collection_id),
    INDEX idx_camp_id (camp_id),
    INDEX idx_completion_rate (completion_rate),
    INDEX idx_watched_at (last_watched_at),

    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频观看记录表';

-- =====================================================
-- 示例数据插入
-- =====================================================

-- 插入视频分类
INSERT INTO video_categories (name, description, sort_order, status) VALUES
('跳绳', '跳绳相关教学视频', 100, 1),
('魔术', '魔术表演和教学视频', 90, 1),
('国学', '国学经典和传统文化', 80, 1),
('故事', '儿童故事和寓言', 70, 1),
('书法', '书法练习和技巧', 60, 1),
('数学', '数学思维和计算', 50, 1);

-- 插入示例视频集合
INSERT INTO video_collections (title, description, category_id, difficulty_level, age_group, is_free, free_video_count, price, video_count, creator_name) VALUES
('跳绳基础入门教程', '零基础学跳绳，从握绳到基本动作', 1, 1, '6-12岁', 0, 3, 29.90, 8, '跳绳教练小王'),
('花样跳绳进阶', '学会各种花样跳绳技巧', 1, 3, '8-14岁', 0, 2, 59.90, 12, '跳绳教练小王'),
('儿童魔术启蒙', '简单易学的儿童魔术表演', 2, 1, '5-10岁', 1, 0, 0.00, 6, '魔术师小李'),
('国学经典诵读', '唐诗宋词经典篇目', 3, 2, '7-12岁', 0, 5, 39.90, 20, '国学老师');

-- 插入示例训练营
INSERT INTO camps (title, subtitle, description, category_id, difficulty_level, age_group, total_days, price, is_free, creator_name, participant_count, rating) VALUES
('零基础21天跳绳挑战营', '从零开始，21天养成跳绳好习惯', '专为零基础孩子设计的跳绳训练营，通过21天系统训练，让孩子掌握基本跳绳技巧', 1, 1, '6-12岁', 21, 0.00, 1, '跳绳教练小王', 1234, 4.9),
('暑假跳绳追高计划', '科学训练，暑假长高3厘米', '结合身高发育规律，制定科学的跳绳训练计划', 1, 2, '8-14岁', 60, 99.00, 0, '跳绳教练小王', 856, 4.8),
('魔术小达人养成记', '30天成为魔术小达人', '从简单魔术开始，逐步提升表演技巧和自信心', 2, 1, '5-10岁', 30, 0.00, 1, '魔术师小李', 567, 4.7);

-- =====================================================
-- 数据库设计说明
-- =====================================================

/*
核心设计思路：
1. 视频集合作为内容组织的核心单位
2. 训练营通过关联视频集合来组织学习内容
3. 支持灵活的付费模式（集合级别和训练营级别）
4. 完整的用户学习进度跟踪
5. 预留权限验证接口设计

付费模式设计：
1. 视频集合级别：前N集免费，后续付费
2. 训练营级别：可包含免费和付费集合
3. 用户权限：通过user_camps表验证是否有权限观看

扩展性考虑：
1. 支持视频集合的独立销售
2. 支持训练营的灵活组合
3. 支持多种付费策略
4. 便于后期添加会员制度
*/
