-- 数据库迁移脚本：添加 participation_id 字段到相关表
-- 执行时间：2024-01-XX
-- 目的：支持用户多次参与同一训练营的场景

-- 1. 为 checkin_records 表添加 participation_id 字段
ALTER TABLE checkin_records 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，关联user_camp_participations.id' AFTER camp_id;

-- 2. 为 point_records 表添加 participation_id 字段  
ALTER TABLE point_records 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，关联user_camp_participations.id' AFTER child_id;

-- 3. 为 growth_tracks 表添加 participation_id 字段
ALTER TABLE growth_tracks 
ADD COLUMN participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 
COMMENT '参与记录ID，关联user_camp_participations.id' AFTER child_id;

-- 4. 更新 checkin_records 表的唯一约束
-- 删除原有的唯一约束（如果存在）
ALTER TABLE checkin_records DROP INDEX IF EXISTS uk_child_date;

-- 添加新的唯一约束，支持多次参与同一训练营
ALTER TABLE checkin_records 
ADD UNIQUE KEY uk_child_participation_date (child_id, participation_id, checkin_date);

-- 5. 添加查询优化索引
-- checkin_records 表索引
ALTER TABLE checkin_records 
ADD INDEX idx_participation_date (participation_id, checkin_date);

ALTER TABLE checkin_records 
ADD INDEX idx_child_participation (child_id, participation_id);

-- point_records 表索引
ALTER TABLE point_records 
ADD INDEX idx_participation_source (participation_id, source_type, source_id);

-- growth_tracks 表索引
ALTER TABLE growth_tracks 
ADD INDEX idx_participation_milestone (participation_id, milestone_type);

-- 6. 添加外键约束（可选，根据性能需求决定是否启用）
-- 注意：在高并发场景下，外键约束可能影响性能，建议在应用层保证数据完整性

-- ALTER TABLE checkin_records 
-- ADD CONSTRAINT fk_checkin_participation 
-- FOREIGN KEY (participation_id) REFERENCES user_camp_participations(id) ON DELETE CASCADE;

-- ALTER TABLE point_records 
-- ADD CONSTRAINT fk_point_participation 
-- FOREIGN KEY (participation_id) REFERENCES user_camp_participations(id) ON DELETE CASCADE;

-- ALTER TABLE growth_tracks 
-- ADD CONSTRAINT fk_growth_participation 
-- FOREIGN KEY (participation_id) REFERENCES user_camp_participations(id) ON DELETE CASCADE;

-- 7. 数据迁移脚本（将现有数据的 participation_id 设置为对应的最新参与记录ID）
-- 注意：这个脚本需要根据实际数据情况调整

-- 更新 checkin_records 表的 participation_id
UPDATE checkin_records cr
SET participation_id = (
    SELECT ucp.id 
    FROM user_camp_participations ucp 
    WHERE ucp.child_id = cr.child_id 
    AND ucp.camp_id = cr.camp_id 
    AND ucp.deleted_at IS NULL
    ORDER BY ucp.created_at DESC 
    LIMIT 1
)
WHERE cr.participation_id = 0;

-- 更新 point_records 表的 participation_id
-- 这里需要根据 source_type 和 source_id 来确定对应的 participation_id
UPDATE point_records pr
SET participation_id = (
    SELECT cr.participation_id 
    FROM checkin_records cr 
    WHERE cr.id = pr.source_id 
    AND pr.source_type = 1  -- 1表示来源是打卡
    AND cr.deleted_at IS NULL
    LIMIT 1
)
WHERE pr.participation_id = 0 AND pr.source_type = 1;

-- 对于其他类型的积分记录，需要根据具体业务逻辑设置 participation_id
-- 这里暂时设置为对应孩子的最新参与记录
UPDATE point_records pr
SET participation_id = (
    SELECT ucp.id 
    FROM user_camp_participations ucp 
    WHERE ucp.child_id = pr.child_id 
    AND ucp.deleted_at IS NULL
    ORDER BY ucp.created_at DESC 
    LIMIT 1
)
WHERE pr.participation_id = 0 AND pr.source_type != 1;

-- 更新 growth_tracks 表的 participation_id
UPDATE growth_tracks gt
SET participation_id = (
    SELECT ucp.id 
    FROM user_camp_participations ucp 
    WHERE ucp.child_id = gt.child_id 
    AND ucp.deleted_at IS NULL
    ORDER BY ucp.created_at DESC 
    LIMIT 1
)
WHERE gt.participation_id = 0;

-- 8. 验证数据迁移结果
-- 检查是否还有 participation_id 为 0 的记录
SELECT 'checkin_records' as table_name, COUNT(*) as zero_participation_count 
FROM checkin_records WHERE participation_id = 0
UNION ALL
SELECT 'point_records' as table_name, COUNT(*) as zero_participation_count 
FROM point_records WHERE participation_id = 0
UNION ALL
SELECT 'growth_tracks' as table_name, COUNT(*) as zero_participation_count 
FROM growth_tracks WHERE participation_id = 0;

-- 如果上述查询结果都为 0，说明数据迁移成功
-- 如果有非零结果，需要手动处理这些记录

-- 9. 回滚脚本（如果需要回滚，请谨慎执行）
/*
-- 删除添加的索引
ALTER TABLE checkin_records DROP INDEX uk_child_participation_date;
ALTER TABLE checkin_records DROP INDEX idx_participation_date;
ALTER TABLE checkin_records DROP INDEX idx_child_participation;
ALTER TABLE point_records DROP INDEX idx_participation_source;
ALTER TABLE growth_tracks DROP INDEX idx_participation_milestone;

-- 恢复原有的唯一约束
ALTER TABLE checkin_records ADD UNIQUE KEY uk_child_date (child_id, checkin_date);

-- 删除 participation_id 字段
ALTER TABLE checkin_records DROP COLUMN participation_id;
ALTER TABLE point_records DROP COLUMN participation_id;
ALTER TABLE growth_tracks DROP COLUMN participation_id;
*/
