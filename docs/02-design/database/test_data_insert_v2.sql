-- =====================================================
-- 儿童学习平台测试数据插入脚本 V2.0
-- 基于complete_database_schema_v2.sql表结构
-- 用户ID=2, 当前孩子ID=11 的测试环境
-- =====================================================

-- =====================================================
-- 1. 基础内容系统测试数据
-- =====================================================

-- 插入视频分类
INSERT INTO video_categories (id, name, description, cover_image, parent_id, sort_order, status) VALUES
(1, '跳绳', '跳绳相关教学视频，提升身体协调性', '/images/categories/jump-rope.jpg', 0, 100, 1),
(2, '魔术', '儿童魔术表演和教学，培养表演能力', '/images/categories/magic.jpg', 0, 90, 1),
(3, '国学', '国学经典和传统文化学习', '/images/categories/guoxue.jpg', 0, 80, 1),
(4, '故事', '儿童故事和寓言，培养阅读兴趣', '/images/categories/story.jpg', 0, 70, 1),
(5, '书法', '书法练习和技巧，提升专注力', '/images/categories/calligraphy.jpg', 0, 60, 1);

-- 插入视频数据
INSERT INTO videos (id, title, description, cover_image, video_url, video_source, duration, category_id, difficulty_level, age_group, tags, view_count, like_count, status) VALUES
(1, '跳绳基础动作教学', '从握绳姿势到基本跳跃，零基础入门必看', '/images/videos/jump-basic-1.jpg', 'https://video.example.com/jump-basic-1.mp4', 1, 180, 1, 1, '3-8岁', '基础,入门,跳绳', 1250, 89, 1),
(2, '单脚跳绳技巧', '学会单脚跳绳，提升协调性和平衡感', '/images/videos/jump-basic-2.jpg', 'https://video.example.com/jump-basic-2.mp4', 1, 240, 1, 2, '5-10岁', '进阶,单脚,技巧', 980, 67, 1),
(3, '花样跳绳入门', '简单花样跳绳动作，让跳绳更有趣', '/images/videos/jump-fancy-1.jpg', 'https://video.example.com/jump-fancy-1.mp4', 1, 300, 1, 3, '7-12岁', '花样,创意,进阶', 756, 45, 1),
(4, '简单魔术表演', '3个简单易学的魔术技巧', '/images/videos/magic-1.jpg', 'https://video.example.com/magic-1.mp4', 1, 420, 2, 1, '4-10岁', '魔术,表演,简单', 2100, 156, 1),
(5, '硬币魔术教学', '用硬币表演神奇魔术', '/images/videos/magic-2.jpg', 'https://video.example.com/magic-2.mp4', 1, 360, 2, 2, '6-12岁', '魔术,硬币,技巧', 1680, 123, 1);

-- 插入视频集合
INSERT INTO video_collections (id, title, description, cover_image, category_id, difficulty_level, age_group, is_free, free_video_count, price, video_count, status) VALUES
(1, '跳绳基础入门教程', '零基础学跳绳，从握绳到基本动作，适合初学者', '/images/collections/jump-basic.jpg', 1, 1, '3-8岁', 0, 2, 29.90, 5, 1),
(2, '花样跳绳进阶课程', '学会各种花样跳绳技巧，让运动更有趣', '/images/collections/jump-fancy.jpg', 1, 3, '7-12岁', 0, 1, 59.90, 8, 1),
(3, '儿童魔术启蒙', '简单易学的儿童魔术表演技巧', '/images/collections/magic-basic.jpg', 2, 1, '4-10岁', 1, 0, 0.00, 6, 1);

-- 插入集合视频关联
INSERT INTO collection_videos (collection_id, video_id, sort_order, is_free) VALUES
(1, 1, 1, 1),  -- 跳绳基础教程第1集免费
(1, 2, 2, 1),  -- 跳绳基础教程第2集免费
(1, 3, 3, 0),  -- 跳绳基础教程第3集付费
(2, 3, 1, 1),  -- 花样跳绳第1集免费
(3, 4, 1, 1),  -- 魔术启蒙第1集
(3, 5, 2, 1);  -- 魔术启蒙第2集

-- =====================================================
-- 2. 训练营系统测试数据
-- =====================================================

-- 插入训练营数据
INSERT INTO training_camps (id, title, subtitle, hero_number, hero_text, pain_solution, duration_days, daily_minutes, difficulty_level, age_group, price, original_price, is_free, video_collection_id, key_benefits, promises, tags, share_title, share_desc, wechat_helper, total_participants, completion_rate, average_rating, status, is_featured, sort_order) VALUES
(1, '21天跳绳养成计划', '让孩子从不会跳到连续100个', '0→100个', '21天技能飞跃', '解决不会教、没方法、没效果的困扰', 21, 15, 1, '3-12岁', 0.00, 99.00, 1, 1, 
'[{"icon":"🎯","text":"掌握跳绳技能"},{"icon":"💪","text":"养成运动习惯"},{"icon":"📈","text":"提升身体协调"},{"icon":"🏆","text":"建立自信心"}]',
'[{"text":"完全免费"},{"text":"随时可退"},{"text":"专业指导"},{"text":"社群支持"}]',
'["保姆级教程","社群交流","零基础","免费体验"]',
'21天跳绳养成计划 - 专业指导，21天见效果',
'让孩子从不会跳到连续100个，专业教练指导，完全免费',
'jump_helper_2024', 1234, 78.50, 4.8, 1, 1, 100),

(2, '暑假跳绳追高计划', '科学训练助力孩子长高5-8cm', '5-8cm', '暑假黄金增高期', '抓住暑假黄金期，科学运动促进身高增长', 30, 25, 2, '6-12岁', 99.00, 199.00, 0, 2,
'[{"icon":"📏","text":"促进身高增长"},{"icon":"🏃","text":"提升运动能力"},{"icon":"💪","text":"增强体质"},{"icon":"🧠","text":"提高专注力"}]',
'[{"text":"专业教练指导"},{"text":"科学训练计划"},{"text":"效果保证"},{"text":"7天无理由退款"}]',
'["科学增高","专业指导","暑假专属","效果保证"]',
'暑假跳绳追高计划 - 科学训练助力孩子长高',
'抓住暑假黄金期，30天科学跳绳训练，助力孩子长高5-8cm',
'jump_coach_pro', 456, 85.20, 4.9, 1, 1, 90),

(3, '魔术小达人养成记', '30天成为魔术小达人', '30天', '魔术技能养成', '培养孩子表演能力和自信心', 30, 20, 1, '4-10岁', 0.00, 0.00, 1, 3,
'[{"icon":"🎭","text":"学会魔术表演"},{"icon":"🌟","text":"提升自信心"},{"icon":"🎪","text":"培养表演能力"},{"icon":"🧠","text":"锻炼思维能力"}]',
'[{"text":"完全免费"},{"text":"趣味教学"},{"text":"专业指导"},{"text":"表演机会"}]',
'["魔术表演","趣味学习","免费体验","自信培养"]',
'魔术小达人养成记 - 30天成为小魔术师',
'趣味魔术教学，培养孩子表演能力和自信心',
'magic_helper_2024', 567, 72.30, 4.7, 1, 0, 80);

-- 插入用户训练营参与记录 (用户ID=2, 孩子ID=11)
INSERT INTO user_camp_participations (id, camp_id, user_id, child_id, participation_status, participation_date, current_day, progress_percentage, total_checkins, consecutive_days, total_study_minutes, rating, review_text) VALUES
(1, 1, 2, 11, 1, '2024-01-01', 8, 38.10, 7, 3, 105, 0, NULL),  -- 正在进行21天跳绳计划
(2, 3, 2, 11, 2, '2023-12-01', 30, 100.00, 28, 15, 560, 5, '孩子很喜欢魔术，现在能表演好几个魔术了！');  -- 已完成魔术训练营

-- =====================================================
-- 3. 打卡积分系统测试数据
-- =====================================================

-- 插入打卡记录 (最近7天的打卡记录)
INSERT INTO checkin_records (id, child_id, camp_id, user_id, checkin_date, practice_duration, jump_count_1min, jump_count_continuous, feeling_text, feeling_score, photos, points_earned, status) VALUES
(1, 11, 1, 2, '2024-01-01', 15, 45, 12, '第一次打卡，有点累但很开心！', 8, '["https://images.example.com/checkin/1_1.jpg"]', 10, 1),
(2, 11, 1, 2, '2024-01-02', 15, 52, 18, '今天跳得比昨天好一些', 7, '["https://images.example.com/checkin/1_2.jpg"]', 10, 1),
(3, 11, 1, 2, '2024-01-03', 20, 48, 15, '增加了练习时间，感觉不错', 8, '["https://images.example.com/checkin/1_3.jpg","https://images.example.com/checkin/1_3_2.jpg"]', 15, 1),
(4, 11, 1, 2, '2024-01-05', 15, 55, 22, '昨天忘记打卡了，今天补上', 6, '["https://images.example.com/checkin/1_5.jpg"]', 10, 2),
(5, 11, 1, 2, '2024-01-06', 15, 58, 25, '连续跳绳有进步！', 9, '["https://images.example.com/checkin/1_6.jpg"]', 10, 1),
(6, 11, 1, 2, '2024-01-07', 20, 62, 28, '感觉越来越熟练了', 9, '["https://images.example.com/checkin/1_7.jpg"]', 15, 1),
(7, 11, 1, 2, '2024-01-08', 15, 65, 30, '今天状态很好，跳了30个连续！', 10, '["https://images.example.com/checkin/1_8.jpg","https://images.example.com/checkin/1_8_2.jpg"]', 20, 1);

-- 插入孩子积分统计
INSERT INTO child_points (id, child_id, total_points, week_points, month_points, total_checkins, continuous_days, max_continuous_days, week_rank, last_week_rank, last_checkin_date) VALUES
(1, 11, 590, 90, 590, 35, 3, 15, 5, 8, '2024-01-08');

-- 插入积分变动记录
INSERT INTO point_records (id, child_id, source_type, source_id, points_change, points_after, description) VALUES
(1, 11, 1, 1, 10, 10, '完成第1天打卡'),
(2, 11, 1, 2, 10, 20, '完成第2天打卡'),
(3, 11, 1, 3, 15, 35, '完成第3天打卡（练习时间20分钟）'),
(4, 11, 1, 4, 10, 45, '补卡第4天'),
(5, 11, 1, 5, 10, 55, '完成第5天打卡'),
(6, 11, 1, 6, 15, 70, '完成第6天打卡（练习时间20分钟）'),
(7, 11, 1, 7, 20, 90, '完成第7天打卡（连续跳绳30个，额外奖励）'),
(8, 11, 2, 1, 100, 190, '完成魔术训练营契约'),
(9, 11, 3, 0, 50, 240, '连续打卡7天奖励');

-- =====================================================
-- 4. 家庭契约系统测试数据
-- =====================================================

-- 插入家庭荣誉契约
INSERT INTO family_contracts (id, child_id, camp_id, creator_user_id, title, goal_description, reward_description, goal_type, goal_value, current_progress, contract_status, start_date, target_date, completed_date, awarded_date) VALUES
(1, 11, 1, 2, '21天跳绳挑战契约', '连续21天完成跳绳打卡，每天至少练习15分钟', '完成后奖励新的跳绳和100元奖学金', 1, 21, 8, 1, '2024-01-01', '2024-01-21', NULL, NULL),
(2, 11, 3, 2, '魔术小达人契约', '30天内学会5个魔术表演', '完成后可以在家庭聚会上表演，获得魔术道具套装', 2, 30, 30, 4, '2023-12-01', '2023-12-30', '2023-12-28', '2023-12-30');

-- 插入契约见证人
INSERT INTO contract_witnesses (id, contract_id, user_id, witness_role, invitation_status, joined_at) VALUES
(1, 1, 2, 1, 2, '2024-01-01 10:00:00'),  -- 用户2作为首席见证官
(2, 2, 2, 1, 2, '2023-12-01 09:00:00');  -- 用户2作为首席见证官

-- =====================================================
-- 5. 成长系统测试数据
-- =====================================================

-- 插入成长轨迹记录
INSERT INTO growth_tracks (id, child_id, milestone_type, milestone_title, milestone_description, milestone_icon, related_id, related_type, achievement_value, achievement_details, is_shareable) VALUES
(1, 11, 1, '首次完成跳绳打卡', '恭喜完成人生第一次跳绳打卡！', '🎯', 1, 'checkin', '第1天', '{"day": 1, "duration": 15, "count": 45}', 1),
(2, 11, 2, '连续打卡3天', '坚持就是胜利！连续3天完成打卡', '🔥', 0, 'streak', '连续3天', '{"streak_days": 3}', 1),
(3, 11, 3, '完成魔术训练营', '成功完成30天魔术训练营，成为小魔术师！', '🎭', 3, 'camp', '30天完成', '{"camp_id": 3, "completion_rate": 93.3}', 1),
(4, 11, 4, '获得首个勋章', '获得"坚持新手"勋章，开启成长之路', '🏅', 1, 'medal', '坚持新手', '{"medal_id": 1, "medal_name": "坚持新手"}', 1);

-- 插入勋章定义
INSERT INTO medals (id, name, description, icon, condition_type, condition_value, condition_description, medal_level, points_reward, sort_order, is_active) VALUES
(1, '坚持新手', '完成首次打卡，开启成长之路', '🥉', 1, 1, '完成1次打卡', 1, 10, 100, 1),
(2, '坚持达人', '连续打卡7天，养成好习惯', '🥈', 2, 7, '连续打卡7天', 2, 50, 90, 1),
(3, '坚持专家', '连续打卡21天，习惯已成自然', '🥇', 2, 21, '连续打卡21天', 3, 100, 80, 1),
(4, '积分小富翁', '累计获得500积分', '💰', 3, 500, '累计积分达到500分', 2, 50, 70, 1),
(5, '跳绳小能手', '单次1分钟跳绳达到100个', '🏃', 1, 100, '1分钟跳绳100个', 3, 100, 60, 1);

-- 插入孩子勋章记录
INSERT INTO child_medals (id, child_id, medal_id, is_unlocked, current_progress, target_progress, unlocked_at, points_earned) VALUES
(1, 11, 1, 1, 1, 1, '2024-01-01 15:30:00', 10),  -- 已解锁坚持新手
(2, 11, 2, 0, 3, 7, NULL, 0),  -- 坚持达人进度3/7
(3, 11, 3, 0, 3, 21, NULL, 0),  -- 坚持专家进度3/21
(4, 11, 4, 1, 590, 500, '2024-01-07 20:00:00', 50),  -- 已解锁积分小富翁
(5, 11, 5, 0, 65, 100, NULL, 0);  -- 跳绳小能手进度65/100

-- =====================================================
-- 6. 社交系统测试数据
-- =====================================================

-- 插入点赞记录
INSERT INTO likes (id, user_id, target_type, target_id) VALUES
(1, 2, 1, 7),  -- 用户2给打卡记录7点赞
(2, 2, 1, 6),  -- 用户2给打卡记录6点赞
(3, 2, 1, 5);  -- 用户2给打卡记录5点赞

-- 插入评论记录
INSERT INTO comments (id, user_id, target_type, target_id, parent_id, content, like_count, status) VALUES
(1, 2, 1, 7, 0, '今天表现真棒！连续跳了30个，继续加油！', 0, 1),
(2, 2, 1, 6, 0, '进步很明显，妈妈为你骄傲！', 0, 1),
(3, 2, 1, 3, 0, '增加练习时间是个好想法，坚持下去！', 0, 1);

-- =====================================================
-- 7. 更新统计数据
-- =====================================================

-- 更新训练营参与人数
UPDATE training_camps SET total_participants = total_participants + 1 WHERE id IN (1, 3);

-- 更新视频观看数据
UPDATE videos SET view_count = view_count + 15 WHERE id IN (1, 2, 4, 5);

-- 更新视频集合数据
UPDATE video_collections SET video_count = (
    SELECT COUNT(*) FROM collection_videos WHERE collection_id = video_collections.id
) WHERE id IN (1, 2, 3);

-- =====================================================
-- 数据插入完成提示
-- =====================================================
-- 测试数据插入完成！
-- 用户ID=2, 孩子ID=11 的测试环境已准备就绪
-- 包含：
-- - 5个视频分类，5个视频，3个视频集合
-- - 3个训练营（1个进行中，1个已完成）
-- - 7条打卡记录，590总积分
-- - 2个家庭契约（1个进行中，1个已完成）
-- - 4条成长轨迹，5个勋章定义，2个已解锁勋章
-- - 3条点赞，3条评论
--
-- V2.0版本改进：
-- - 使用合理的NULL值策略
-- - 优化的数据类型选择
-- - 简化的注释风格
-- - 更清晰的业务逻辑
