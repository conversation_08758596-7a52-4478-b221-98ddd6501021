# 数据库表设计总览

## 概述

本文档提供儿童多品类学习平台数据库设计的完整总览，包含所有P0核心功能的表结构设计、关系图谱和业务逻辑说明。

## 设计原则

### 数据库设计标准
- 遵循 [数据库设计标准](./database-design-standards.md) 中定义的所有规范
- 避免NULL值，使用合适的默认值
- 优先使用UNSIGNED整数类型
- 使用TIMESTAMP替代DATETIME
- 限制表字段数量在20个以内
- 为所有表、字段、索引、约束添加详细注释

### 架构原则
- MySQL为主要业务数据存储（95%）
- MongoDB用于日志存储和统计分析
- Redis用于缓存和会话管理
- 支持读写分离配置
- 外键约束保证数据完整性

## 功能模块划分

### P0 核心功能模块
1. **用户管理系统** - 用户注册、登录、档案管理
2. **打卡系统** - 视频打卡、分享、记录管理
3. **社交互动系统** - 点赞、评论、社区互动
4. **积分奖励系统** - 积分管理、虚拟奖励、成就系统
5. **排行榜系统** - 多维度排行、历史记录
6. **内容管理系统** - 视频内容、分类、播放列表、推荐
7. **系统任务系统** - 官方任务、参与记录、奖励发放
8. **团队任务系统** - 用户创建团队、成员管理、团队打卡

## 数据库表结构

### 1. 用户管理系统 (User Management)
**文件**: `table-designs/01-user-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `users` | 用户基础信息 | id, phone, nickname, avatar | 手机号唯一，支持微信登录 |
| `children` | 孩子档案信息 | id, nickname, birth_date, gender | 年龄自动计算，隐私控制 |
| `user_children` | 用户孩子关联 | user_id, child_id, relationship | 多对多关系，简化家庭共享 |

**核心关系**:
- 用户与孩子：多对多关系，支持多用户管理同一孩子
- 简化的家庭共享机制，无复杂权限管理

### 2. 打卡系统 (Check-in System)
**文件**: `table-designs/02-checkin-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `checkin_records` | 打卡记录 | id, user_id, child_id, video_url | 支持视频、截图、分享到朋友圈 |

**核心功能**:
- 每日限制1次打卡
- 支持微信视频号发布
- 朋友圈分享截图功能
- 详细的打卡数据记录

### 3. 社交互动系统 (Social Interaction)
**文件**: `table-designs/03-social-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `likes` | 点赞记录 | id, user_id, target_id, target_type | 支持多种内容类型点赞 |
| `comments` | 评论系统 | id, user_id, target_id, content | 多级评论，审核机制 |

**核心功能**:
- 统一的点赞系统，支持打卡、视频等多种内容
- 多级评论回复系统
- 内容审核和举报机制

### 4. 积分奖励系统 (Points & Rewards)
**文件**: `table-designs/04-points-rewards-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `user_points` | 用户积分 | user_id, child_id, total_points | 积分余额管理 |
| `point_records` | 积分记录 | id, user_id, points_change, reason | 详细的积分变动记录 |
| `rewards` | 虚拟奖励 | id, name, rarity, unlock_condition | 奖励配置管理 |
| `user_rewards` | 用户奖励 | user_id, reward_id, obtained_at | 用户获得的奖励 |

**核心功能**:
- 完整的积分管理系统
- 虚拟奖励和成就系统
- 详细的积分变动追踪

### 5. 排行榜系统 (Leaderboard)
**文件**: `table-designs/05-leaderboard-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `leaderboards` | 排行榜配置 | id, name, type, scope | 多维度排行榜配置 |
| `leaderboard_records` | 排行记录 | leaderboard_id, user_id, score, rank | 历史排名数据 |

**核心功能**:
- 支持全球、地区、年龄段等多维度排行
- 历史排名数据保存
- 灵活的排行榜配置

### 6. 内容管理系统 (Content Management)
**文件**: `table-designs/06-content-system.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `video_categories` | 视频分类 | id, name, parent_id, level | 多级分类结构 |
| `videos` | 视频内容 | id, title, video_url, category_id | 多源视频支持，推荐算法 |
| `playlists` | 播放列表 | id, title, type, creator_id | 系统课程、用户收藏 |
| `playlist_videos` | 播放列表关联 | playlist_id, video_id, sort_order | 视频排序管理 |
| `user_video_interactions` | 用户视频互动 | user_id, video_id, interaction_type | 播放记录、收藏、评分 |

**核心功能**:
- 多级视频分类管理
- 支持微信视频号、本地上传等多种视频源
- 完整的播放列表系统
- 详细的用户行为记录，支持推荐算法

### 7. 系统任务系统 (System Tasks)
**文件**: `table-designs/07-system-tasks.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `system_tasks` | 系统任务 | id, title, task_type, completion_type | 官方任务配置 |
| `user_task_participations` | 用户任务参与 | user_id, task_id, participation_status | 任务进度跟踪 |

**核心功能**:
- 灵活的任务类型和完成条件配置
- 详细的任务进度跟踪
- 自动奖励发放机制
- 支持连续性任务和周期性任务

### 8. 团队任务系统 (Team Tasks)
**文件**: `table-designs/08-team-tasks.sql`

| 表名 | 用途 | 主要字段 | 关键特性 |
|------|------|----------|----------|
| `team_tasks` | 团队任务 | id, title, creator_user_id, max_members | 用户创建团队任务 |
| `team_members` | 团队成员 | team_task_id, user_id, member_role | 团队成员管理 |
| `team_task_records` | 团队打卡记录 | team_task_id, team_member_id, checkin_date | 团队打卡记录 |

**核心功能**:
- 用户自主创建团队任务（最多3个并发）
- 灵活的邀请机制：公开、邀请码、审核
- 团队成员角色管理
- 团队协作和互动功能

## 表关系图谱

### 核心实体关系
```
用户系统:
users (1) ←→ (N) user_children (N) ←→ (1) children

打卡系统:
users (1) → (N) checkin_records (N) ← (1) children

社交系统:
users (1) → (N) likes/comments
checkin_records (1) ← (N) likes/comments

积分系统:
users (1) → (N) user_points/point_records
children (1) → (N) user_points/point_records

内容系统:
video_categories (1) → (N) videos
videos (N) ←→ (N) playlists (through playlist_videos)
users/children (1) → (N) user_video_interactions

任务系统:
system_tasks (1) → (N) user_task_participations
team_tasks (1) → (N) team_members → (N) team_task_records
```

### 关键外键关系
- 所有用户相关表都关联到 `users.id`
- 所有孩子相关表都关联到 `children.id`
- 社交互动支持多种目标类型（打卡、视频等）
- 团队系统形成完整的层级关系

## 索引策略

### 主要索引类型
1. **主键索引**: 所有表的自增ID主键
2. **唯一索引**: 防止重复数据（如每日打卡限制）
3. **复合索引**: 支持复杂查询条件
4. **排序索引**: 支持各种排序需求
5. **外键索引**: 保证关联查询性能

### 关键索引示例
- `uk_user_child_date` - 防止重复打卡
- `idx_user_child` - 用户孩子关联查询
- `idx_created_at` - 时间排序查询
- `idx_status` - 状态筛选查询

## 数据完整性

### 外键约束策略
- **CASCADE**: 主记录删除时自动删除关联记录
- **SET NULL**: 引用记录删除时设置为NULL
- **RESTRICT**: 防止删除有关联的记录

### 数据验证
- 枚举值约束确保状态字段正确性
- 数值范围约束确保数据合理性
- 唯一约束防止重复数据
- 非空约束确保关键字段完整

## 性能优化

### 查询优化
- 合理的索引设计覆盖常用查询场景
- 冗余字段减少复杂JOIN操作
- 分页查询支持大数据量处理

### 存储优化
- 选择合适的数据类型减少存储空间
- 避免NULL值提高查询效率
- 合理的字段长度设置

### 扩展性考虑
- 预留扩展字段支持功能迭代
- 分区策略应对数据增长
- 读写分离支持高并发

## 业务规则总结

### 核心业务约束
1. **用户系统**: 手机号唯一，支持多用户管理同一孩子
2. **打卡系统**: 每日限制1次打卡，支持多种分享方式
3. **任务系统**: 用户最多3个并发团队任务
4. **积分系统**: 详细的积分变动记录，支持多种获取方式
5. **内容系统**: 多级分类，支持多种视频源和推荐算法

### 数据一致性规则
- 外键约束保证关联数据完整性
- 事务处理保证复杂操作的原子性
- 定期数据校验确保统计数据准确性
- 软删除机制保护重要历史数据

## 后续扩展计划

### P1功能支持
- 课程体系表结构
- 支付订单系统
- 消息通知系统

### P2功能支持
- 直播互动系统
- 高级分析统计
- 第三方集成接口

### 技术优化
- 数据分区策略
- 缓存层设计
- 搜索引擎集成

---

**注意**: 本文档基于P0核心功能设计，随着产品功能迭代，表结构可能需要相应调整和扩展。所有变更都应遵循既定的数据库设计标准，并更新相关文档。
