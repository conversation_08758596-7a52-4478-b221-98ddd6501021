-- =====================================================
-- 内容管理和推荐系统表设计
-- =====================================================
-- 模块: 内容管理和推荐系统 (Content Management and Recommendation System)
-- 包含表: videos, video_categories, playlists, user_video_interactions
-- 功能: 教学视频管理、用户分享视频、播放列表、个性化推荐
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 视频分类表 (video_categories)
-- =====================================================
-- 用途: 定义视频内容的分类体系
-- 业务场景: 视频分类管理、内容组织、分类浏览
-- 设计原则: 支持多级分类，灵活的分类管理
-- =====================================================

CREATE TABLE video_categories (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID，主键自增，全平台唯一标识',

    -- 分类基本信息
    name VARCHAR(50) DEFAULT '' COMMENT '分类名称，如"基础教学"、"花式跳法"、"体能训练"',
    description TEXT COMMENT '分类描述，详细说明该分类的内容和适用对象',
    icon VARCHAR(255) DEFAULT '' COMMENT '分类图标URL，用于展示的图标图片地址',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '分类封面图URL，分类页面的封面图片',

    -- 分类层级关系
    parent_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父分类ID，0表示顶级分类，支持多级分类结构',
    level TINYINT UNSIGNED DEFAULT 1 COMMENT '分类层级，1表示一级分类，2表示二级分类，最多支持3级',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重，数值越大排序越靠前，用于分类展示顺序',

    -- 分类属性
    category_type TINYINT UNSIGNED DEFAULT 1 COMMENT '分类类型 1:教学视频 2:用户分享 3:活动视频，不同类型的内容分类',
    difficulty_level TINYINT UNSIGNED DEFAULT 0 COMMENT '难度等级 0:不限 1:新手 2:初级 3:中级 4:高级 5:专业，分类难度标识',
    age_group VARCHAR(20) DEFAULT '' COMMENT '适用年龄段，如"3-6岁"、"7-12岁"，用于年龄推荐',

    -- 统计信息（冗余字段）
    video_count INT UNSIGNED DEFAULT 0 COMMENT '视频数量，该分类下的视频总数，冗余字段提升查询性能',
    view_count BIGINT UNSIGNED DEFAULT 0 COMMENT '总播放量，该分类下所有视频的播放量总和',

    -- 分类状态
    is_active TINYINT UNSIGNED DEFAULT 1 COMMENT '是否启用 0:禁用 1:启用，控制分类是否对外展示',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐，推荐分类会优先展示',

    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录分类创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录分类最后修改时间',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 分类层级索引
    INDEX idx_parent_id (parent_id) COMMENT '父分类索引：查询子分类时使用',
    INDEX idx_level (level) COMMENT '层级索引：按层级查询分类时使用',
    INDEX idx_parent_level (parent_id, level) COMMENT '父分类层级复合索引：构建分类树时使用',

    -- 分类类型索引
    INDEX idx_category_type (category_type) COMMENT '分类类型索引：按类型查询分类时使用',
    INDEX idx_difficulty_level (difficulty_level) COMMENT '难度等级索引：按难度筛选分类时使用',

    -- 排序和状态索引
    INDEX idx_sort_order (sort_order DESC) COMMENT '排序权重索引：分类排序展示时使用',
    INDEX idx_active_featured (is_active, is_featured) COMMENT '状态推荐复合索引：查询有效推荐分类时使用',

    -- 统计索引
    INDEX idx_video_count (video_count DESC) COMMENT '视频数量排序索引：按内容丰富度排序时使用',
    INDEX idx_view_count (view_count DESC) COMMENT '播放量排序索引：按热门程度排序时使用',

    -- 时间索引
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按时间查询分类时使用'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频分类表：定义视频内容的分类体系，支持多级分类和灵活的分类管理，包含统计信息用于推荐排序';

-- =====================================================
-- 视频内容表 (videos)
-- =====================================================
-- 用途: 存储平台的所有视频内容信息
-- 业务场景: 教学视频管理、用户分享视频、视频播放、推荐算法
-- 设计原则: 支持多种视频来源，丰富的元数据，高效的查询性能
-- =====================================================

CREATE TABLE videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频ID，主键自增，全平台唯一标识',

    -- 视频基本信息
    title VARCHAR(200) DEFAULT '' COMMENT '视频标题，视频的名称标题，最多200字符',
    description TEXT COMMENT '视频描述，详细介绍视频内容、教学要点等',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '视频封面图URL，视频的缩略图地址',

    -- 视频文件信息
    video_url VARCHAR(500) DEFAULT '' COMMENT '视频文件URL，视频的播放地址或微信视频号链接',
    video_source TINYINT UNSIGNED DEFAULT 1 COMMENT '视频来源 1:微信视频号 2:本地上传 3:第三方平台，标识视频存储位置',
    video_id_external VARCHAR(100) DEFAULT '' COMMENT '外部视频ID，微信视频号等外部平台的视频标识',
    duration INT UNSIGNED DEFAULT 0 COMMENT '视频时长（秒），视频的播放时长',
    file_size BIGINT UNSIGNED DEFAULT 0 COMMENT '文件大小（字节），视频文件的大小',

    -- 视频分类和标签
    category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '主分类ID，关联video_categories.id，视频的主要分类',
    secondary_category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '次分类ID，视频的次要分类，可选',
    tags VARCHAR(500) DEFAULT '' COMMENT '视频标签，逗号分隔的标签列表，用于搜索和推荐',

    -- 视频属性
    video_type TINYINT UNSIGNED DEFAULT 1 COMMENT '视频类型 1:教学视频 2:用户分享 3:活动视频 4:广告视频',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1:新手 2:初级 3:中级 4:高级 5:专业，教学视频的难度',
    age_group VARCHAR(20) DEFAULT '' COMMENT '适用年龄段，如"3-6岁"、"7-12岁"，推荐的观看年龄',

    -- 创作者信息
    creator_type TINYINT UNSIGNED DEFAULT 1 COMMENT '创作者类型 1:官方 2:用户 3:合作方，标识视频来源',
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创作者用户ID，用户上传视频时关联users.id',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创作者名称，显示的创作者姓名',

    -- 统计数据（冗余字段）
    view_count BIGINT UNSIGNED DEFAULT 0 COMMENT '播放次数，视频的总播放量',
    like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数量，视频获得的点赞总数',
    comment_count INT UNSIGNED DEFAULT 0 COMMENT '评论数量，视频的评论总数',
    share_count INT UNSIGNED DEFAULT 0 COMMENT '分享次数，视频被分享的次数',
    collect_count INT UNSIGNED DEFAULT 0 COMMENT '收藏数量，视频被收藏的次数',

    -- 推荐权重
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分，0.00-5.00，视频质量评估分数',
    popularity_score DECIMAL(8,2) DEFAULT 0.00 COMMENT '热度评分，综合播放、点赞等计算的热度分数',
    recommendation_weight INT UNSIGNED DEFAULT 0 COMMENT '推荐权重，算法推荐时的权重值，越高越容易被推荐',

    -- 审核状态
    audit_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:待审核 2:审核通过 3:审核拒绝，内容审核状态',
    audit_reason VARCHAR(200) DEFAULT '' COMMENT '审核原因，审核拒绝时的具体原因说明',

    -- 视频状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '视频状态 1:正常 2:下架 3:删除，视频的发布状态',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐，推荐视频会优先展示',
    is_free TINYINT UNSIGNED DEFAULT 1 COMMENT '是否免费 0:付费 1:免费，视频的收费状态',

    -- 时间记录
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间，视频正式发布的时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，视频记录创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，视频信息最后修改时间',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 分类索引
    INDEX idx_category_id (category_id) COMMENT '主分类索引：按分类查询视频时使用',
    INDEX idx_secondary_category (secondary_category_id) COMMENT '次分类索引：按次分类查询视频时使用',
    INDEX idx_category_type (category_id, video_type) COMMENT '分类类型复合索引：分类内按类型筛选时使用',

    -- 创作者索引
    INDEX idx_creator_type (creator_type) COMMENT '创作者类型索引：按创作者类型查询时使用',
    INDEX idx_creator_user (creator_user_id) COMMENT '创作者用户索引：查询用户上传视频时使用',

    -- 属性索引
    INDEX idx_difficulty_level (difficulty_level) COMMENT '难度等级索引：按难度筛选视频时使用',
    INDEX idx_video_type (video_type) COMMENT '视频类型索引：按类型查询视频时使用',

    -- 统计排序索引
    INDEX idx_view_count (view_count DESC) COMMENT '播放量排序索引：按播放量排序时使用',
    INDEX idx_like_count (like_count DESC) COMMENT '点赞数排序索引：按点赞数排序时使用',
    INDEX idx_popularity_score (popularity_score DESC) COMMENT '热度评分排序索引：热门推荐时使用',
    INDEX idx_quality_score (quality_score DESC) COMMENT '质量评分排序索引：优质内容筛选时使用',

    -- 推荐索引
    INDEX idx_recommendation (recommendation_weight DESC, is_featured DESC) COMMENT '推荐权重索引：推荐算法时使用',
    INDEX idx_featured_status (is_featured, status) COMMENT '推荐状态复合索引：查询推荐有效视频时使用',

    -- 审核状态索引
    INDEX idx_audit_status (audit_status) COMMENT '审核状态索引：内容审核管理时使用',
    INDEX idx_status (status) COMMENT '视频状态索引：查询有效视频时使用',

    -- 时间索引
    INDEX idx_published_at (published_at DESC) COMMENT '发布时间排序索引：按时间排序视频时使用',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按创建时间查询时使用',

    -- 全文搜索索引
    FULLTEXT KEY ft_title_tags (title, tags) COMMENT '标题标签全文索引：视频搜索时使用',

    -- =====================================================
    -- 外键约束
    -- =====================================================

    CONSTRAINT fk_videos_category_id
        FOREIGN KEY (category_id) REFERENCES video_categories(id)
        ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_videos_secondary_category_id
        FOREIGN KEY (secondary_category_id) REFERENCES video_categories(id)
        ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_videos_creator_user_id
        FOREIGN KEY (creator_user_id) REFERENCES users(id)
        ON DELETE SET NULL ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频内容表：存储平台所有视频信息，支持多种视频来源和丰富的元数据，包含统计数据用于推荐算法';

-- =====================================================
-- 播放列表表 (playlists)
-- =====================================================
-- 用途: 管理视频播放列表，支持系统和用户创建的播放列表
-- 业务场景: 课程系列、专题合集、用户收藏夹
-- 设计原则: 灵活的播放列表管理，支持多种列表类型
-- =====================================================

CREATE TABLE playlists (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '播放列表ID，主键自增，全平台唯一标识',

    -- 播放列表基本信息
    name VARCHAR(100) DEFAULT '' COMMENT '播放列表名称，如"跳绳入门教程"、"我的收藏"',
    description TEXT COMMENT '播放列表描述，详细说明播放列表的内容和用途',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '播放列表封面图URL，列表的封面图片',

    -- 播放列表类型
    playlist_type TINYINT UNSIGNED DEFAULT 1 COMMENT '播放列表类型 1:系统课程 2:专题合集 3:用户收藏 4:推荐列表',
    category_id BIGINT UNSIGNED DEFAULT 0 COMMENT '关联分类ID，播放列表所属的分类',

    -- 创建者信息
    creator_type TINYINT UNSIGNED DEFAULT 1 COMMENT '创建者类型 1:系统 2:用户，标识播放列表创建者',
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创建者用户ID，用户创建时关联users.id',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创建者名称，显示的创建者姓名',

    -- 播放列表属性
    is_public TINYINT UNSIGNED DEFAULT 1 COMMENT '是否公开 0:私有 1:公开，控制播放列表是否对外可见',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐，推荐播放列表会优先展示',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重，数值越大排序越靠前',

    -- 统计信息（冗余字段）
    video_count INT UNSIGNED DEFAULT 0 COMMENT '视频数量，播放列表中的视频总数',
    total_duration INT UNSIGNED DEFAULT 0 COMMENT '总时长（秒），播放列表中所有视频的总时长',
    play_count BIGINT UNSIGNED DEFAULT 0 COMMENT '播放次数，播放列表的总播放量',
    collect_count INT UNSIGNED DEFAULT 0 COMMENT '收藏数量，播放列表被收藏的次数',

    -- 播放列表状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '播放列表状态 1:正常 2:下架 3:删除',

    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，播放列表创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，播放列表最后修改时间',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 播放列表类型索引
    INDEX idx_playlist_type (playlist_type) COMMENT '播放列表类型索引：按类型查询播放列表时使用',
    INDEX idx_category_id (category_id) COMMENT '分类索引：按分类查询播放列表时使用',

    -- 创建者索引
    INDEX idx_creator_type (creator_type) COMMENT '创建者类型索引：按创建者类型查询时使用',
    INDEX idx_creator_user (creator_user_id) COMMENT '创建者用户索引：查询用户创建的播放列表时使用',

    -- 状态和属性索引
    INDEX idx_public_status (is_public, status) COMMENT '公开状态复合索引：查询公开有效播放列表时使用',
    INDEX idx_featured_status (is_featured, status) COMMENT '推荐状态复合索引：查询推荐播放列表时使用',

    -- 排序索引
    INDEX idx_sort_order (sort_order DESC) COMMENT '排序权重索引：播放列表排序时使用',
    INDEX idx_play_count (play_count DESC) COMMENT '播放量排序索引：按热门程度排序时使用',
    INDEX idx_collect_count (collect_count DESC) COMMENT '收藏数排序索引：按收藏数排序时使用',

    -- 时间索引
    INDEX idx_created_at (created_at DESC) COMMENT '创建时间排序索引：按时间排序播放列表时使用',

    -- =====================================================
    -- 外键约束
    -- =====================================================

    CONSTRAINT fk_playlists_category_id
        FOREIGN KEY (category_id) REFERENCES video_categories(id)
        ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_playlists_creator_user_id
        FOREIGN KEY (creator_user_id) REFERENCES users(id)
        ON DELETE CASCADE ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='播放列表表：管理视频播放列表，支持系统课程、专题合集、用户收藏等多种类型的播放列表';

-- =====================================================
-- 播放列表视频关联表 (playlist_videos)
-- =====================================================
-- 用途: 管理播放列表与视频的关联关系
-- 业务场景: 播放列表内容管理、视频排序、播放顺序
-- 设计原则: 多对多关系，支持视频在播放列表中的排序
-- =====================================================

CREATE TABLE playlist_videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联记录ID，主键自增，全平台唯一标识',
    playlist_id BIGINT UNSIGNED DEFAULT 0 COMMENT '播放列表ID，关联playlists.id',
    video_id BIGINT UNSIGNED DEFAULT 0 COMMENT '视频ID，关联videos.id',

    -- 排序信息
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序序号，视频在播放列表中的播放顺序，从1开始',

    -- 添加信息
    added_by_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '添加者用户ID，记录是谁将视频添加到播放列表',

    -- 时间记录
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间，视频添加到播放列表的时间戳',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 播放列表关联索引
    UNIQUE KEY uk_playlist_video (playlist_id, video_id) COMMENT '唯一约束：同一播放列表中不能重复添加同一视频',
    INDEX idx_playlist_id (playlist_id) COMMENT '播放列表索引：查询播放列表内容时使用',
    INDEX idx_video_id (video_id) COMMENT '视频索引：查询视频所在播放列表时使用',

    -- 排序索引
    INDEX idx_playlist_sort (playlist_id, sort_order) COMMENT '播放列表排序索引：按顺序播放视频时使用',

    -- 添加者索引
    INDEX idx_added_by_user (added_by_user_id) COMMENT '添加者索引：查询用户添加的视频时使用',

    -- 时间索引
    INDEX idx_added_at (added_at) COMMENT '添加时间索引：按添加时间查询时使用',

    -- =====================================================
    -- 外键约束
    -- =====================================================

    CONSTRAINT fk_playlist_videos_playlist_id
        FOREIGN KEY (playlist_id) REFERENCES playlists(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_playlist_videos_video_id
        FOREIGN KEY (video_id) REFERENCES videos(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_playlist_videos_added_by_user_id
        FOREIGN KEY (added_by_user_id) REFERENCES users(id)
        ON DELETE SET NULL ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='播放列表视频关联表：管理播放列表与视频的多对多关系，支持视频在播放列表中的排序';

-- =====================================================
-- 用户视频互动表 (user_video_interactions)
-- =====================================================
-- 用途: 记录用户与视频的各种互动行为
-- 业务场景: 播放记录、收藏、推荐算法、个性化推荐
-- 设计原则: 详细记录用户行为，支持推荐算法分析
-- =====================================================

CREATE TABLE user_video_interactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '互动记录ID，主键自增，全平台唯一标识',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id，标识互动用户',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识互动的孩子档案',
    video_id BIGINT UNSIGNED DEFAULT 0 COMMENT '视频ID，关联videos.id，标识互动的视频',

    -- 互动类型和行为
    interaction_type TINYINT UNSIGNED DEFAULT 1 COMMENT '互动类型 1:播放 2:收藏 3:分享 4:完成观看 5:跳过',

    -- 播放相关数据
    play_duration INT UNSIGNED DEFAULT 0 COMMENT '播放时长（秒），用户实际观看的时长',
    play_progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '播放进度百分比，0.00-100.00，观看完成度',
    play_count INT UNSIGNED DEFAULT 1 COMMENT '播放次数，用户播放该视频的总次数',

    -- 评分和反馈
    rating TINYINT UNSIGNED DEFAULT 0 COMMENT '用户评分，1-5分，用户对视频的评价',
    is_liked TINYINT UNSIGNED DEFAULT 0 COMMENT '是否点赞 0:未点赞 1:已点赞',
    is_collected TINYINT UNSIGNED DEFAULT 0 COMMENT '是否收藏 0:未收藏 1:已收藏',

    -- 设备和环境信息
    device_type TINYINT UNSIGNED DEFAULT 1 COMMENT '设备类型 1:手机 2:平板 3:电脑，用户观看设备',
    network_type TINYINT UNSIGNED DEFAULT 1 COMMENT '网络类型 1:WiFi 2:4G 3:5G，观看时的网络环境',

    -- 时间记录
    first_play_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次播放时间，用户第一次播放该视频的时间',
    last_play_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后播放时间，用户最近一次播放该视频的时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，互动记录创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，互动记录最后修改时间',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 用户和视频关联索引
    UNIQUE KEY uk_user_child_video_type (user_id, child_id, video_id, interaction_type)
        COMMENT '唯一约束：同一用户同一孩子对同一视频的同一类型互动只能有一条记录',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户互动记录时使用',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子互动记录时使用',
    INDEX idx_video_id (video_id) COMMENT '视频ID索引：查询视频互动统计时使用',
    INDEX idx_user_child (user_id, child_id) COMMENT '用户孩子复合索引：查询特定用户特定孩子的互动',

    -- 互动类型索引
    INDEX idx_interaction_type (interaction_type) COMMENT '互动类型索引：按互动类型统计时使用',
    INDEX idx_video_interaction (video_id, interaction_type) COMMENT '视频互动复合索引：统计视频各类互动时使用',

    -- 播放数据索引
    INDEX idx_play_progress (play_progress) COMMENT '播放进度索引：分析用户观看习惯时使用',
    INDEX idx_play_count (play_count DESC) COMMENT '播放次数索引：查找热门视频时使用',

    -- 评分和反馈索引
    INDEX idx_rating (rating) COMMENT '评分索引：按评分统计和筛选时使用',
    INDEX idx_is_liked (is_liked) COMMENT '点赞状态索引：统计点赞数据时使用',
    INDEX idx_is_collected (is_collected) COMMENT '收藏状态索引：统计收藏数据时使用',

    -- 时间索引
    INDEX idx_first_play_at (first_play_at) COMMENT '首次播放时间索引：分析用户发现视频时间时使用',
    INDEX idx_last_play_at (last_play_at DESC) COMMENT '最后播放时间索引：分析用户活跃度时使用',

    -- 设备环境索引
    INDEX idx_device_type (device_type) COMMENT '设备类型索引：设备使用统计时使用',
    INDEX idx_network_type (network_type) COMMENT '网络类型索引：网络环境分析时使用',

    -- =====================================================
    -- 外键约束
    -- =====================================================

    CONSTRAINT fk_user_video_interactions_user_id
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_user_video_interactions_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_user_video_interactions_video_id
        FOREIGN KEY (video_id) REFERENCES videos(id)
        ON DELETE CASCADE ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户视频互动表：记录用户与视频的各种互动行为，支持播放统计、收藏管理和推荐算法分析';

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询分类下的视频列表（分页）
-- SELECT v.*, vc.name as category_name FROM videos v
-- JOIN video_categories vc ON v.category_id = vc.id
-- WHERE v.category_id = ? AND v.status = 1 AND v.audit_status = 2
-- ORDER BY v.recommendation_weight DESC, v.published_at DESC
-- LIMIT 20 OFFSET 0;

-- 查询用户收藏的视频
-- SELECT v.*, uvi.created_at as collected_at FROM videos v
-- JOIN user_video_interactions uvi ON v.id = uvi.video_id
-- WHERE uvi.user_id = ? AND uvi.child_id = ? AND uvi.is_collected = 1
-- ORDER BY uvi.created_at DESC;

-- 查询播放列表内容
-- SELECT v.*, pv.sort_order FROM videos v
-- JOIN playlist_videos pv ON v.id = pv.video_id
-- WHERE pv.playlist_id = ? AND v.status = 1
-- ORDER BY pv.sort_order ASC;

-- 查询热门视频（按播放量）
-- SELECT * FROM videos
-- WHERE status = 1 AND audit_status = 2
-- ORDER BY view_count DESC, popularity_score DESC
-- LIMIT 50;

-- 查询用户观看历史
-- SELECT v.*, uvi.last_play_at, uvi.play_progress FROM videos v
-- JOIN user_video_interactions uvi ON v.id = uvi.video_id
-- WHERE uvi.user_id = ? AND uvi.child_id = ? AND uvi.interaction_type = 1
-- ORDER BY uvi.last_play_at DESC LIMIT 100;

-- 推荐算法：基于用户行为的相似视频
-- SELECT v.*, COUNT(uvi2.user_id) as similar_users FROM videos v
-- JOIN user_video_interactions uvi2 ON v.id = uvi2.video_id
-- WHERE uvi2.user_id IN (
--     SELECT DISTINCT uvi1.user_id FROM user_video_interactions uvi1
--     WHERE uvi1.video_id IN (用户已观看的视频ID列表)
--     AND uvi1.user_id != ? AND uvi1.is_liked = 1
-- ) AND v.status = 1 AND v.audit_status = 2
-- GROUP BY v.id ORDER BY similar_users DESC, v.popularity_score DESC
-- LIMIT 20;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 内容分类体系：
   - 支持多级分类结构，最多3级分类
   - 分类包含统计信息，便于内容管理
   - 支持分类的启用/禁用和推荐设置
   - 分类删除时视频分类设置为NULL

2. 视频内容管理：
   - 支持多种视频来源：微信视频号、本地上传、第三方平台
   - 完整的审核流程：待审核、审核通过、审核拒绝
   - 丰富的元数据：标题、描述、标签、难度等级、年龄段
   - 统计数据冗余存储，提升查询性能

3. 播放列表系统：
   - 支持系统课程、专题合集、用户收藏等多种类型
   - 播放列表与视频多对多关系，支持视频排序
   - 用户可创建私有或公开的播放列表
   - 播放列表包含统计信息用于推荐

4. 用户互动记录：
   - 详细记录用户观看行为：播放时长、进度、次数
   - 支持用户反馈：评分、点赞、收藏
   - 记录设备和网络环境，用于体验优化
   - 为推荐算法提供丰富的用户行为数据

5. 推荐算法支持：
   - 质量评分和热度评分用于内容排序
   - 推荐权重控制内容曝光优先级
   - 用户互动数据支持协同过滤推荐
   - 内容标签支持基于内容的推荐

6. 性能优化策略：
   - 合理的索引设计支持各种查询场景
   - 统计数据冗余存储减少复杂计算
   - 全文搜索索引支持视频搜索功能
   - 分区策略应对大数据量（可选）

7. 数据一致性保证：
   - 外键约束保证数据完整性
   - 唯一约束防止重复数据
   - 级联删除和更新保持数据一致
   - 定期统计数据校验和更新
*/