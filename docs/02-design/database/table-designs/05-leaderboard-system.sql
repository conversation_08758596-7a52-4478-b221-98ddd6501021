-- =====================================================
-- 排行榜系统表设计
-- =====================================================
-- 模块: 排行榜系统 (Leaderboard System)
-- 包含表: leaderboards, leaderboard_records
-- 功能: 多维度排行榜、历史排名记录、排行榜管理
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 排行榜配置表 (leaderboards)
-- =====================================================
-- 用途: 定义系统中的各种排行榜类型和配置
-- 业务场景: 排行榜管理、排行榜展示、排名计算
-- 设计原则: 灵活的排行榜配置，支持多种排名维度
-- =====================================================

CREATE TABLE leaderboards (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '排行榜ID，主键自增，全平台唯一标识',
    
    -- 排行榜基本信息
    name VARCHAR(100) DEFAULT '' COMMENT '排行榜名称，如"积分排行榜"、"一分钟跳绳排行榜"',
    description TEXT COMMENT '排行榜描述，详细说明排行榜的规则和意义',
    icon VARCHAR(255) DEFAULT '' COMMENT '排行榜图标URL，用于展示的图标图片地址',
    
    -- 排行榜类型和维度
    board_type TINYINT UNSIGNED DEFAULT 1 COMMENT '排行榜类型 1:积分榜 2:成绩榜 3:连续榜 4:活跃榜，不同类型的排行榜',
    metric_type TINYINT UNSIGNED DEFAULT 1 COMMENT '排名指标 1:总积分 2:一分钟成绩 3:连续天数 4:打卡次数 5:练习时长',
    sort_order TINYINT UNSIGNED DEFAULT 1 COMMENT '排序方式 1:降序(高到低) 2:升序(低到高)，排名计算方式',
    
    -- 排行榜范围
    scope_type TINYINT UNSIGNED DEFAULT 1 COMMENT '排行榜范围 1:全平台 2:同城 3:同校 4:同年龄段，排行榜参与范围',
    age_min TINYINT UNSIGNED DEFAULT 0 COMMENT '最小年龄，年龄段排行榜的最小年龄限制',
    age_max TINYINT UNSIGNED DEFAULT 100 COMMENT '最大年龄，年龄段排行榜的最大年龄限制',
    
    -- 排行榜周期
    period_type TINYINT UNSIGNED DEFAULT 1 COMMENT '统计周期 1:实时 2:日榜 3:周榜 4:月榜 5:年榜，排行榜更新周期',
    period_start DATE DEFAULT '1900-01-01' COMMENT '周期开始日期，周期性排行榜的开始时间',
    period_end DATE DEFAULT '2099-12-31' COMMENT '周期结束日期，周期性排行榜的结束时间',
    
    -- 排行榜设置
    max_rank INT UNSIGNED DEFAULT 100 COMMENT '最大排名数，排行榜显示的最大名次',
    min_score INT UNSIGNED DEFAULT 0 COMMENT '最低分数要求，参与排行榜的最低分数门槛',
    is_public TINYINT UNSIGNED DEFAULT 1 COMMENT '是否公开 0:不公开 1:公开，控制排行榜是否对外展示',
    
    -- 奖励设置
    has_rewards TINYINT UNSIGNED DEFAULT 0 COMMENT '是否有奖励 0:无奖励 1:有奖励，排行榜是否设置奖励',
    reward_config JSON COMMENT '奖励配置JSON，详细的奖励规则和奖品设置',
    
    -- 排行榜状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '排行榜状态 1:正常 2:暂停 3:结束，排行榜运行状态',
    
    -- 更新信息
    last_update_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间，排行榜数据最后更新时间',
    next_update_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下次更新时间，下次计划更新排行榜的时间',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录排行榜配置创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录排行榜配置最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 排行榜类型索引
    INDEX idx_board_type (board_type) COMMENT '排行榜类型索引：按类型查询排行榜时使用',
    INDEX idx_metric_type (metric_type) COMMENT '排名指标索引：按指标查询排行榜时使用',
    
    -- 范围和周期索引
    INDEX idx_scope_type (scope_type) COMMENT '范围类型索引：按范围查询排行榜时使用',
    INDEX idx_period_type (period_type) COMMENT '周期类型索引：按周期查询排行榜时使用',
    INDEX idx_period_range (period_start, period_end) COMMENT '周期范围索引：查询有效期内排行榜时使用',
    
    -- 状态索引
    INDEX idx_status (status) COMMENT '状态索引：查询有效排行榜时使用',
    INDEX idx_is_public (is_public) COMMENT '公开状态索引：查询公开排行榜时使用',
    
    -- 更新时间索引
    INDEX idx_last_update (last_update_at) COMMENT '最后更新时间索引：排行榜更新管理时使用',
    INDEX idx_next_update (next_update_at) COMMENT '下次更新时间索引：定时更新任务时使用'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='排行榜配置表：定义系统中的各种排行榜类型和配置，支持多维度排名和灵活的排行榜管理';

-- =====================================================
-- 排行榜记录表 (leaderboard_records)
-- =====================================================
-- 用途: 存储排行榜的具体排名数据和历史记录
-- 业务场景: 排名展示、历史排名查询、排名变化分析
-- 设计原则: 高效的排名存储，支持历史数据查询
-- =====================================================

CREATE TABLE leaderboard_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，主键自增，全平台唯一标识',
    leaderboard_id BIGINT UNSIGNED DEFAULT 0 COMMENT '排行榜ID，关联leaderboards.id，标识所属排行榜',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id，标识排名用户',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识排名的孩子档案',
    
    -- 排名信息
    current_rank INT UNSIGNED DEFAULT 0 COMMENT '当前排名，用户在该排行榜中的当前名次',
    previous_rank INT UNSIGNED DEFAULT 0 COMMENT '上次排名，用户在上次更新时的排名',
    rank_change INT DEFAULT 0 COMMENT '排名变化，正数表示上升，负数表示下降，0表示不变',
    
    -- 分数信息
    current_score BIGINT DEFAULT 0 COMMENT '当前分数，用户在该排行榜中的当前得分',
    previous_score BIGINT DEFAULT 0 COMMENT '上次分数，用户在上次更新时的得分',
    score_change BIGINT DEFAULT 0 COMMENT '分数变化，本次更新的分数变化量',
    
    -- 统计信息
    best_rank INT UNSIGNED DEFAULT 0 COMMENT '历史最佳排名，用户在该排行榜中达到过的最高排名',
    best_score BIGINT DEFAULT 0 COMMENT '历史最高分数，用户在该排行榜中达到过的最高分数',
    total_weeks_in_top10 INT UNSIGNED DEFAULT 0 COMMENT '进入前10次数，用户进入前10名的总次数',
    total_weeks_in_top100 INT UNSIGNED DEFAULT 0 COMMENT '进入前100次数，用户进入前100名的总次数',
    
    -- 周期信息
    period_start DATE DEFAULT '1900-01-01' COMMENT '统计周期开始日期，该排名记录对应的统计周期开始时间',
    period_end DATE DEFAULT '2099-12-31' COMMENT '统计周期结束日期，该排名记录对应的统计周期结束时间',
    
    -- 地域信息（用于同城排行榜）
    province VARCHAR(20) DEFAULT '' COMMENT '省份，用于地域性排行榜统计',
    city VARCHAR(20) DEFAULT '' COMMENT '城市，用于同城排行榜统计',
    
    -- 年龄信息（用于年龄段排行榜）
    age_at_record TINYINT UNSIGNED DEFAULT 0 COMMENT '记录时年龄，生成排名记录时用户的年龄',
    
    -- 记录状态
    is_valid TINYINT UNSIGNED DEFAULT 1 COMMENT '记录有效性 0:无效 1:有效，用于过滤异常排名数据',
    
    -- 时间记录
    record_date DATE DEFAULT '1900-01-01' COMMENT '记录日期，排名记录生成的日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录排名数据生成的时间戳',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 排行榜关联索引
    INDEX idx_leaderboard_id (leaderboard_id) COMMENT '排行榜ID索引：查询特定排行榜记录时使用',
    
    -- 用户和孩子关联索引
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户所有排名记录时使用',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子所有排名记录时使用',
    INDEX idx_user_child (user_id, child_id) COMMENT '用户孩子复合索引：查询特定用户特定孩子的排名',
    
    -- 排名查询索引
    UNIQUE KEY uk_leaderboard_user_period (leaderboard_id, user_id, child_id, period_start) 
        COMMENT '唯一约束：同一排行榜同一用户同一周期只能有一条记录',
    INDEX idx_current_rank (leaderboard_id, current_rank) COMMENT '当前排名索引：按排名顺序查询时使用',
    INDEX idx_current_score (leaderboard_id, current_score DESC) COMMENT '当前分数索引：按分数排序时使用',
    
    -- 历史最佳索引
    INDEX idx_best_rank (leaderboard_id, best_rank) COMMENT '最佳排名索引：查询历史最佳排名时使用',
    INDEX idx_best_score (leaderboard_id, best_score DESC) COMMENT '最佳分数索引：查询历史最高分时使用',
    
    -- 地域索引
    INDEX idx_province_city (province, city) COMMENT '地域复合索引：地域性排行榜时使用',
    
    -- 年龄索引
    INDEX idx_age_at_record (age_at_record) COMMENT '年龄索引：年龄段排行榜时使用',
    
    -- 周期索引
    INDEX idx_period_range (period_start, period_end) COMMENT '周期范围索引：按周期查询排名时使用',
    INDEX idx_record_date (record_date) COMMENT '记录日期索引：按日期查询排名时使用',
    
    -- 有效性索引
    INDEX idx_is_valid (is_valid) COMMENT '有效性索引：查询有效排名记录时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_leaderboard_records_leaderboard_id 
        FOREIGN KEY (leaderboard_id) REFERENCES leaderboards(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    CONSTRAINT fk_leaderboard_records_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    CONSTRAINT fk_leaderboard_records_child_id 
        FOREIGN KEY (child_id) REFERENCES children(id) 
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='排行榜记录表：存储排行榜的具体排名数据和历史记录，支持多维度排名统计和历史数据分析';

-- =====================================================
-- 分区策略（大表优化）
-- =====================================================
-- 按记录日期分区，提升大数据量下的查询性能
-- ALTER TABLE leaderboard_records 
-- PARTITION BY RANGE (YEAR(record_date)) (
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p2026 VALUES LESS THAN (2027),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询积分排行榜前100名
-- SELECT lr.*, c.nickname, c.avatar FROM leaderboard_records lr
-- JOIN children c ON lr.child_id = c.id
-- WHERE lr.leaderboard_id = ? AND lr.is_valid = 1
-- AND c.show_in_leaderboard = 1 AND c.privacy_level <= 2
-- ORDER BY lr.current_rank ASC LIMIT 100;

-- 查询用户在各个排行榜中的排名
-- SELECT lr.*, l.name, l.board_type FROM leaderboard_records lr
-- JOIN leaderboards l ON lr.leaderboard_id = l.id
-- WHERE lr.user_id = ? AND lr.child_id = ? AND lr.is_valid = 1
-- ORDER BY lr.current_rank ASC;

-- 查询排名变化最大的用户（本周上升最快）
-- SELECT lr.*, c.nickname, c.avatar FROM leaderboard_records lr
-- JOIN children c ON lr.child_id = c.id
-- WHERE lr.leaderboard_id = ? AND lr.rank_change < 0 AND lr.is_valid = 1
-- ORDER BY lr.rank_change ASC LIMIT 20;

-- 查询同城排行榜
-- SELECT lr.*, c.nickname, c.avatar FROM leaderboard_records lr
-- JOIN children c ON lr.child_id = c.id
-- WHERE lr.leaderboard_id = ? AND lr.province = ? AND lr.city = ?
-- AND lr.is_valid = 1 AND c.show_in_leaderboard = 1
-- ORDER BY lr.current_rank ASC LIMIT 50;

-- 查询年龄段排行榜
-- SELECT lr.*, c.nickname, c.avatar FROM leaderboard_records lr
-- JOIN children c ON lr.child_id = c.id
-- WHERE lr.leaderboard_id = ? AND lr.age_at_record BETWEEN ? AND ?
-- AND lr.is_valid = 1 AND c.show_in_leaderboard = 1
-- ORDER BY lr.current_rank ASC LIMIT 50;

-- 查询用户历史排名趋势
-- SELECT record_date, current_rank, current_score FROM leaderboard_records
-- WHERE leaderboard_id = ? AND user_id = ? AND child_id = ? AND is_valid = 1
-- ORDER BY record_date DESC LIMIT 30;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 排行榜类型设计：
   - 支持多种排行榜：积分榜、成绩榜、连续榜、活跃榜
   - 支持多种范围：全平台、同城、同校、同年龄段
   - 支持多种周期：实时、日榜、周榜、月榜、年榜
   - 灵活的排名指标和排序方式

2. 排名计算规则：
   - 实时排行榜：数据变化时立即更新
   - 周期性排行榜：定时批量计算更新
   - 排名变化追踪：记录排名上升下降情况
   - 历史最佳记录：激励用户持续努力

3. 隐私保护机制：
   - 用户可选择是否参与排行榜
   - 支持隐私级别控制排名展示
   - 地域和年龄信息用于分组排名
   - 敏感信息脱敏处理

4. 性能优化策略：
   - 合理的索引设计支持各种查询
   - 分区策略应对大数据量
   - 冗余字段减少复杂计算
   - 定时任务批量更新排名

5. 激励机制设计：
   - 多维度排行榜增加参与机会
   - 排名变化展示激发竞争意识
   - 历史最佳记录提供成就感
   - 奖励机制增加参与动力

6. 数据一致性保证：
   - 唯一约束防止重复记录
   - 外键约束保证数据完整性
   - 有效性标记过滤异常数据
   - 定期数据校验确保准确性
*/
