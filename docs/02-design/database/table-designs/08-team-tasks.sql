-- =====================================================
-- 团队任务表设计
-- =====================================================
-- 模块: 团队任务 (Team Tasks)
-- 包含表: team_tasks, team_members, team_task_records
-- 功能: 用户创建团队打卡任务、邀请机制、团队成员管理、团队打卡记录
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 团队任务表 (team_tasks)
-- =====================================================
-- 用途: 存储用户创建的团队打卡任务信息
-- 业务场景: 团队任务创建、任务管理、团队协作
-- 设计原则: 支持用户自主创建，灵活的团队任务配置，最多3个并发任务限制
-- =====================================================

CREATE TABLE team_tasks (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '团队任务ID，主键自增，全平台唯一标识',
    
    -- 任务基本信息
    title VARCHAR(100) DEFAULT '' COMMENT '任务标题，如"一起跳绳21天挑战"、"寒假运动小组"',
    description TEXT COMMENT '任务描述，详细说明任务内容、规则和目标',
    cover_image VARCHAR(255) DEFAULT '' COMMENT '任务封面图URL，团队任务的封面图片',
    
    -- 创建者信息
    creator_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创建者用户ID，关联users.id，标识任务创建者',
    creator_child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创建者孩子ID，关联children.id，创建者的孩子档案',
    creator_name VARCHAR(100) DEFAULT '' COMMENT '创建者显示名称，团队中显示的创建者姓名',
    
    -- 任务类型和设置
    task_type TINYINT UNSIGNED DEFAULT 1 COMMENT '任务类型 1:打卡挑战 2:学习小组 3:运动团队 4:技能练习',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '任务难度 1:简单 2:普通 3:困难，任务完成难度',
    
    -- 完成条件
    target_type TINYINT UNSIGNED DEFAULT 1 COMMENT '目标类型 1:连续打卡 2:累计次数 3:累计时长 4:平均分数',
    target_value INT UNSIGNED DEFAULT 7 COMMENT '目标数值，如连续7天、累计30次等',
    target_unit VARCHAR(20) DEFAULT '天' COMMENT '目标单位，如"天"、"次"、"分钟"',
    
    -- 团队设置
    max_members INT UNSIGNED DEFAULT 10 COMMENT '最大成员数，团队最多允许的成员数量',
    current_members INT UNSIGNED DEFAULT 1 COMMENT '当前成员数，团队当前的成员数量（包含创建者）',
    min_age TINYINT UNSIGNED DEFAULT 0 COMMENT '最小年龄限制，0表示不限制',
    max_age TINYINT UNSIGNED DEFAULT 100 COMMENT '最大年龄限制，100表示不限制',
    
    -- 邀请设置
    invite_mode TINYINT UNSIGNED DEFAULT 1 COMMENT '邀请模式 1:公开加入 2:邀请码加入 3:审核加入',
    invite_code VARCHAR(20) DEFAULT '' COMMENT '邀请码，邀请码加入模式的邀请码',
    auto_approve TINYINT UNSIGNED DEFAULT 1 COMMENT '自动审核 0:需要审核 1:自动通过，审核加入模式的设置',
    
    -- 任务时间
    start_date DATE NOT NULL COMMENT '任务开始日期，团队任务开始的日期',
    end_date DATE NULL COMMENT '任务结束日期，团队任务结束的日期，NULL表示无限期',
    duration_days INT UNSIGNED DEFAULT 7 COMMENT '任务持续天数，任务的总持续时间',
    
    -- 奖励设置
    has_rewards TINYINT UNSIGNED DEFAULT 0 COMMENT '是否有奖励 0:无奖励 1:有奖励',
    reward_config JSON COMMENT '奖励配置JSON，团队完成奖励的详细配置',
    
    -- 任务状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '任务状态 1:招募中 2:进行中 3:已完成 4:已取消 5:已过期',
    is_public TINYINT UNSIGNED DEFAULT 1 COMMENT '是否公开 0:私有 1:公开，控制任务是否在公开列表中显示',
    
    -- 统计信息（冗余字段）
    total_checkins BIGINT UNSIGNED DEFAULT 0 COMMENT '总打卡次数，团队所有成员的打卡总次数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比，0.00-100.00，团队任务完成率',
    active_members INT UNSIGNED DEFAULT 0 COMMENT '活跃成员数，最近有打卡活动的成员数量',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，团队任务创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，团队任务最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 创建者索引
    INDEX idx_creator_user_id (creator_user_id) COMMENT '创建者用户索引：查询用户创建的团队任务时使用',
    INDEX idx_creator_child_id (creator_child_id) COMMENT '创建者孩子索引：查询孩子创建的团队任务时使用',
    
    -- 任务类型索引
    INDEX idx_task_type (task_type) COMMENT '任务类型索引：按类型查询团队任务时使用',
    INDEX idx_difficulty_level (difficulty_level) COMMENT '难度等级索引：按难度筛选任务时使用',
    
    -- 团队设置索引
    INDEX idx_members (current_members, max_members) COMMENT '成员数量索引：查询可加入团队时使用',
    INDEX idx_invite_mode (invite_mode) COMMENT '邀请模式索引：按加入方式筛选时使用',
    INDEX idx_invite_code (invite_code) COMMENT '邀请码索引：通过邀请码查找团队时使用',
    
    -- 状态索引
    INDEX idx_status (status) COMMENT '任务状态索引：查询有效团队任务时使用',
    INDEX idx_public_status (is_public, status) COMMENT '公开状态复合索引：查询公开有效任务时使用',
    
    -- 时间范围索引
    INDEX idx_date_range (start_date, end_date) COMMENT '时间范围索引：查询有效期内任务时使用',
    INDEX idx_start_date (start_date) COMMENT '开始日期索引：任务调度时使用',
    INDEX idx_end_date (end_date) COMMENT '结束日期索引：任务清理时使用',
    
    -- 统计排序索引
    INDEX idx_completion_rate (completion_rate DESC) COMMENT '完成率排序索引：按完成率排序时使用',
    INDEX idx_active_members (active_members DESC) COMMENT '活跃成员排序索引：按活跃度排序时使用',
    
    -- 时间记录索引
    INDEX idx_created_at (created_at DESC) COMMENT '创建时间排序索引：按创建时间排序任务时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_team_tasks_creator_user_id 
        FOREIGN KEY (creator_user_id) REFERENCES users(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    CONSTRAINT fk_team_tasks_creator_child_id 
        FOREIGN KEY (creator_child_id) REFERENCES children(id) 
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='团队任务表：存储用户创建的团队打卡任务，支持灵活的团队配置和邀请机制，包含完整的任务管理功能';

-- =====================================================
-- 团队成员表 (team_members)
-- =====================================================
-- 用途: 管理团队任务的成员信息和状态
-- 业务场景: 团队成员管理、权限控制、成员统计
-- 设计原则: 记录成员加入过程，支持成员状态管理
-- =====================================================

CREATE TABLE team_members (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '成员记录ID，主键自增，全平台唯一标识',
    team_task_id BIGINT UNSIGNED DEFAULT 0 COMMENT '团队任务ID，关联team_tasks.id，标识所属团队任务',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id，标识团队成员用户',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识团队成员的孩子档案',
    
    -- 成员角色
    member_role TINYINT UNSIGNED DEFAULT 2 COMMENT '成员角色 1:创建者 2:普通成员 3:管理员，团队中的角色权限',
    
    -- 加入信息
    join_method TINYINT UNSIGNED DEFAULT 1 COMMENT '加入方式 1:公开加入 2:邀请码 3:邀请链接 4:好友邀请',
    invite_code_used VARCHAR(20) DEFAULT '' COMMENT '使用的邀请码，通过邀请码加入时记录',
    invited_by_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '邀请者用户ID，被邀请加入时记录邀请者',
    
    -- 成员状态
    member_status TINYINT UNSIGNED DEFAULT 1 COMMENT '成员状态 1:正常 2:已退出 3:被移除 4:待审核',
    approval_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:已通过 2:待审核 3:已拒绝，需要审核的团队使用',
    
    -- 成员昵称（团队内显示）
    display_name VARCHAR(50) DEFAULT '' COMMENT '团队内显示名称，成员在团队中的昵称',
    avatar VARCHAR(255) DEFAULT '' COMMENT '头像URL，成员在团队中显示的头像',
    
    -- 成员统计（冗余字段）
    total_checkins INT UNSIGNED DEFAULT 0 COMMENT '总打卡次数，该成员在团队任务中的打卡总次数',
    consecutive_days INT UNSIGNED DEFAULT 0 COMMENT '连续打卡天数，当前连续打卡的天数',
    last_checkin_date DATE NULL COMMENT '最后打卡日期，最近一次打卡的日期',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '个人完成率，该成员的任务完成率',
    
    -- 时间记录
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间，成员加入团队的时间戳',
    approved_at TIMESTAMP NULL COMMENT '审核通过时间，审核通过的时间戳',
    left_at TIMESTAMP NULL COMMENT '退出时间，成员退出团队的时间戳',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 团队和成员关联索引
    UNIQUE KEY uk_team_user_child (team_task_id, user_id, child_id) 
        COMMENT '唯一约束：同一团队中同一用户的同一孩子只能有一条成员记录',
    INDEX idx_team_task_id (team_task_id) COMMENT '团队任务索引：查询团队成员时使用',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户参与的团队时使用',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子参与的团队时使用',
    
    -- 成员角色和状态索引
    INDEX idx_member_role (member_role) COMMENT '成员角色索引：按角色查询成员时使用',
    INDEX idx_member_status (member_status) COMMENT '成员状态索引：查询有效成员时使用',
    INDEX idx_approval_status (approval_status) COMMENT '审核状态索引：审核管理时使用',
    INDEX idx_team_status (team_task_id, member_status) COMMENT '团队状态复合索引：查询团队有效成员时使用',
    
    -- 邀请信息索引
    INDEX idx_invite_code_used (invite_code_used) COMMENT '邀请码索引：统计邀请码使用情况时使用',
    INDEX idx_invited_by_user (invited_by_user_id) COMMENT '邀请者索引：查询邀请关系时使用',
    
    -- 统计排序索引
    INDEX idx_total_checkins (total_checkins DESC) COMMENT '打卡次数排序索引：团队成员排行时使用',
    INDEX idx_consecutive_days (consecutive_days DESC) COMMENT '连续天数排序索引：连续打卡排行时使用',
    INDEX idx_completion_rate (completion_rate DESC) COMMENT '完成率排序索引：按完成率排行时使用',
    
    -- 时间索引
    INDEX idx_joined_at (joined_at) COMMENT '加入时间索引：按加入时间查询时使用',
    INDEX idx_last_checkin_date (last_checkin_date) COMMENT '最后打卡日期索引：活跃度分析时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_team_members_team_task_id
        FOREIGN KEY (team_task_id) REFERENCES team_tasks(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_members_user_id
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_members_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_members_invited_by_user_id
        FOREIGN KEY (invited_by_user_id) REFERENCES users(id)
        ON DELETE SET NULL ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='团队成员表：管理团队任务的成员信息，支持成员角色管理、邀请机制和成员统计，包含详细的加入和活动记录';

-- =====================================================
-- 团队任务打卡记录表 (team_task_records)
-- =====================================================
-- 用途: 记录团队成员的打卡记录和活动详情
-- 业务场景: 团队打卡记录、进度跟踪、团队互动
-- 设计原则: 详细记录团队成员打卡活动，支持团队协作和互动
-- =====================================================

CREATE TABLE team_task_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '打卡记录ID，主键自增，全平台唯一标识',
    team_task_id BIGINT UNSIGNED DEFAULT 0 COMMENT '团队任务ID，关联team_tasks.id，标识所属团队任务',
    team_member_id BIGINT UNSIGNED DEFAULT 0 COMMENT '团队成员ID，关联team_members.id，标识打卡的团队成员',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id，标识打卡用户（冗余字段）',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识打卡的孩子档案（冗余字段）',

    -- 打卡基本信息
    checkin_date DATE NOT NULL COMMENT '打卡日期，打卡活动的日期',
    checkin_type TINYINT UNSIGNED DEFAULT 1 COMMENT '打卡类型 1:视频打卡 2:图片打卡 3:文字打卡 4:数据打卡',

    -- 打卡内容
    title VARCHAR(200) DEFAULT '' COMMENT '打卡标题，打卡记录的标题描述',
    content TEXT COMMENT '打卡内容，详细的打卡描述或心得体会',
    media_urls JSON COMMENT '媒体文件URLs，打卡的图片、视频等媒体文件链接',

    -- 打卡数据
    exercise_duration INT UNSIGNED DEFAULT 0 COMMENT '运动时长（分钟），运动类打卡的时长记录',
    exercise_count INT UNSIGNED DEFAULT 0 COMMENT '运动次数，如跳绳次数、俯卧撑次数等',
    score DECIMAL(5,2) DEFAULT 0.00 COMMENT '打卡得分，0.00-100.00，打卡质量评分',

    -- 位置信息
    location VARCHAR(100) DEFAULT '' COMMENT '打卡地点，打卡时的地理位置描述',
    latitude DECIMAL(10,7) DEFAULT 0.0000000 COMMENT '纬度，打卡位置的纬度坐标',
    longitude DECIMAL(10,7) DEFAULT 0.0000000 COMMENT '经度，打卡位置的经度坐标',

    -- 团队互动
    like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数量，团队成员对该打卡的点赞总数',
    comment_count INT UNSIGNED DEFAULT 0 COMMENT '评论数量，团队成员对该打卡的评论总数',
    encourage_count INT UNSIGNED DEFAULT 0 COMMENT '鼓励数量，团队成员给予的鼓励总数',

    -- 打卡状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '打卡状态 1:正常 2:已删除 3:被举报 4:审核中',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否精选 0:普通 1:精选，优秀打卡记录标记',

    -- 连续性信息
    is_consecutive TINYINT UNSIGNED DEFAULT 0 COMMENT '是否连续 0:非连续 1:连续，标记是否为连续打卡',
    consecutive_day_number INT UNSIGNED DEFAULT 1 COMMENT '连续天数序号，在连续打卡中的天数序号',

    -- 设备信息
    device_info VARCHAR(200) DEFAULT '' COMMENT '设备信息，打卡时使用的设备信息',
    app_version VARCHAR(50) DEFAULT '' COMMENT '应用版本，打卡时的应用版本号',

    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，打卡记录创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，打卡记录最后修改时间',

    -- =====================================================
    -- 索引设计
    -- =====================================================

    -- 团队和成员关联索引
    INDEX idx_team_task_id (team_task_id) COMMENT '团队任务索引：查询团队打卡记录时使用',
    INDEX idx_team_member_id (team_member_id) COMMENT '团队成员索引：查询成员打卡记录时使用',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户打卡记录时使用',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子打卡记录时使用',

    -- 打卡日期索引
    UNIQUE KEY uk_team_member_date (team_task_id, team_member_id, checkin_date)
        COMMENT '唯一约束：同一团队同一成员每天只能有一条打卡记录',
    INDEX idx_checkin_date (checkin_date) COMMENT '打卡日期索引：按日期查询打卡记录时使用',
    INDEX idx_team_date (team_task_id, checkin_date) COMMENT '团队日期复合索引：查询团队特定日期打卡时使用',

    -- 打卡类型索引
    INDEX idx_checkin_type (checkin_type) COMMENT '打卡类型索引：按类型筛选打卡记录时使用',

    -- 打卡数据索引
    INDEX idx_score (score DESC) COMMENT '得分排序索引：按打卡得分排序时使用',
    INDEX idx_exercise_duration (exercise_duration DESC) COMMENT '运动时长排序索引：按时长排序时使用',
    INDEX idx_exercise_count (exercise_count DESC) COMMENT '运动次数排序索引：按次数排序时使用',

    -- 互动统计索引
    INDEX idx_like_count (like_count DESC) COMMENT '点赞数排序索引：按点赞数排序时使用',
    INDEX idx_comment_count (comment_count DESC) COMMENT '评论数排序索引：按评论数排序时使用',
    INDEX idx_encourage_count (encourage_count DESC) COMMENT '鼓励数排序索引：按鼓励数排序时使用',

    -- 状态索引
    INDEX idx_status (status) COMMENT '打卡状态索引：查询有效打卡记录时使用',
    INDEX idx_featured_status (is_featured, status) COMMENT '精选状态复合索引：查询精选打卡时使用',

    -- 连续性索引
    INDEX idx_consecutive (is_consecutive) COMMENT '连续性索引：查询连续打卡记录时使用',
    INDEX idx_consecutive_day (consecutive_day_number) COMMENT '连续天数索引：连续打卡统计时使用',

    -- 时间索引
    INDEX idx_created_at (created_at DESC) COMMENT '创建时间排序索引：按创建时间排序打卡时使用',

    -- =====================================================
    -- 外键约束
    -- =====================================================

    CONSTRAINT fk_team_task_records_team_task_id
        FOREIGN KEY (team_task_id) REFERENCES team_tasks(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_task_records_team_member_id
        FOREIGN KEY (team_member_id) REFERENCES team_members(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_task_records_user_id
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_team_task_records_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='团队任务打卡记录表：记录团队成员的详细打卡信息，支持多种打卡类型和团队互动功能，包含位置信息和连续性跟踪';

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询用户创建的团队任务（限制最多3个并发）
-- SELECT * FROM team_tasks
-- WHERE creator_user_id = ? AND status IN (1,2)
-- ORDER BY created_at DESC LIMIT 3;

-- 查询可加入的公开团队任务
-- SELECT * FROM team_tasks
-- WHERE is_public = 1 AND status = 1
-- AND current_members < max_members
-- AND (min_age = 0 OR ? >= min_age) AND (max_age = 100 OR ? <= max_age)
-- ORDER BY created_at DESC LIMIT 20;

-- 查询团队成员列表
-- SELECT tm.*, u.nickname, c.nickname as child_name
-- FROM team_members tm
-- JOIN users u ON tm.user_id = u.id
-- JOIN children c ON tm.child_id = c.id
-- WHERE tm.team_task_id = ? AND tm.member_status = 1
-- ORDER BY tm.member_role ASC, tm.total_checkins DESC;

-- 查询团队打卡记录（按日期）
-- SELECT ttr.*, tm.display_name, c.nickname as child_name
-- FROM team_task_records ttr
-- JOIN team_members tm ON ttr.team_member_id = tm.id
-- JOIN children c ON ttr.child_id = c.id
-- WHERE ttr.team_task_id = ? AND ttr.checkin_date = ?
-- AND ttr.status = 1
-- ORDER BY ttr.created_at DESC;

-- 查询团队成员打卡统计
-- SELECT
--     tm.display_name,
--     COUNT(ttr.id) as total_checkins,
--     MAX(ttr.checkin_date) as last_checkin,
--     AVG(ttr.score) as avg_score
-- FROM team_members tm
-- LEFT JOIN team_task_records ttr ON tm.id = ttr.team_member_id AND ttr.status = 1
-- WHERE tm.team_task_id = ? AND tm.member_status = 1
-- GROUP BY tm.id ORDER BY total_checkins DESC;

-- 查询团队连续打卡排行
-- SELECT tm.display_name, tm.consecutive_days, tm.last_checkin_date
-- FROM team_members tm
-- WHERE tm.team_task_id = ? AND tm.member_status = 1
-- ORDER BY tm.consecutive_days DESC, tm.last_checkin_date DESC
-- LIMIT 10;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 团队任务创建规则：
   - 每个用户最多同时创建3个团队任务
   - 支持多种邀请模式：公开加入、邀请码、审核加入
   - 灵活的团队设置：成员数量、年龄限制、任务周期
   - 任务状态自动管理：招募中、进行中、已完成等

2. 团队成员管理：
   - 创建者自动成为团队管理员
   - 支持成员角色管理：创建者、管理员、普通成员
   - 完整的加入流程：邀请、审核、通过
   - 成员统计数据实时更新

3. 团队打卡机制：
   - 每个成员每天只能打卡一次
   - 支持多种打卡类型：视频、图片、文字、数据
   - 详细的打卡数据记录：时长、次数、得分
   - 团队互动功能：点赞、评论、鼓励

4. 连续性跟踪：
   - 自动计算连续打卡天数
   - 连续性中断检测和重置
   - 连续打卡排行榜支持
   - 里程碑成就记录

5. 团队协作功能：
   - 团队打卡记录共享
   - 成员互相鼓励和支持
   - 团队完成率统计
   - 团队成就和奖励

6. 数据统计分析：
   - 团队活跃度分析
   - 成员参与度统计
   - 打卡质量评估
   - 团队效果评价

7. 性能优化策略：
   - 合理的索引设计支持复杂查询
   - 冗余字段减少统计计算
   - 分区策略应对大数据量
   - 定时任务处理过期数据

8. 数据一致性保证：
   - 外键约束保证数据完整性
   - 唯一约束防止重复打卡
   - 事务处理保证状态一致性
   - 定期数据校验和同步
*/
