-- =====================================================
-- 社交互动系统表设计
-- =====================================================
-- 模块: 社交互动系统 (Social Interaction System)
-- 包含表: likes, comments
-- 功能: 点赞、评论、社交互动记录
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 点赞表 (likes)
-- =====================================================
-- 用途: 记录用户对打卡记录的点赞行为
-- 业务场景: 点赞/取消点赞、热门内容统计、用户互动分析
-- 设计原则: 防重复点赞，支持快速查询和统计
-- =====================================================

CREATE TABLE likes (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '点赞记录ID，主键自增，全平台唯一标识',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '点赞用户ID，关联users.id，标识执行点赞操作的用户',
    target_type TINYINT UNSIGNED DEFAULT 1 COMMENT '点赞目标类型 1:打卡记录 2:评论 3:视频内容，支持多种内容类型点赞',
    target_id BIGINT UNSIGNED DEFAULT 0 COMMENT '目标内容ID，根据target_type关联不同表的主键',
    
    -- 点赞状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '点赞状态 1:有效点赞 0:已取消，支持点赞/取消点赞操作',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间，记录用户执行点赞操作的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录点赞状态变更时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 防重复点赞的唯一约束
    UNIQUE KEY uk_user_target (user_id, target_type, target_id) COMMENT '唯一约束：同一用户对同一内容只能有一条点赞记录',
    
    -- 目标内容索引
    INDEX idx_target (target_type, target_id) COMMENT '目标内容索引：查询特定内容的所有点赞时使用',
    INDEX idx_target_status (target_type, target_id, status) COMMENT '目标状态复合索引：统计有效点赞数时使用',
    
    -- 用户行为索引
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户所有点赞记录时使用',
    INDEX idx_user_status (user_id, status) COMMENT '用户状态复合索引：查询用户有效点赞时使用',
    
    -- 时间索引
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按时间范围查询点赞记录时使用'
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='点赞表：记录用户对各种内容的点赞行为，支持点赞/取消点赞操作，防止重复点赞';

-- =====================================================
-- 评论表 (comments)
-- =====================================================
-- 用途: 记录用户对打卡记录的评论内容
-- 业务场景: 发表评论、回复评论、评论管理、互动统计
-- 设计原则: 支持多级回复，内容审核，快速查询
-- =====================================================

CREATE TABLE comments (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID，主键自增，全平台唯一标识',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '评论用户ID，关联users.id，标识发表评论的用户',
    target_type TINYINT UNSIGNED DEFAULT 1 COMMENT '评论目标类型 1:打卡记录 2:视频内容，支持多种内容类型评论',
    target_id BIGINT UNSIGNED DEFAULT 0 COMMENT '目标内容ID，根据target_type关联不同表的主键',
    
    -- 回复关系
    parent_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父评论ID，0表示顶级评论，非0表示回复某条评论',
    reply_to_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '回复目标用户ID，回复评论时标识被回复的用户',
    
    -- 评论内容
    content TEXT COMMENT '评论内容，用户发表的评论文字，最多500字符',
    
    -- 审核状态
    audit_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:待审核 2:审核通过 3:审核拒绝，内容审核使用',
    audit_reason VARCHAR(200) DEFAULT '' COMMENT '审核原因，审核拒绝时的具体原因说明',
    
    -- 互动统计（冗余字段）
    like_count INT UNSIGNED DEFAULT 0 COMMENT '评论点赞数，冗余字段，提升查询性能',
    reply_count INT UNSIGNED DEFAULT 0 COMMENT '回复数量，冗余字段，用于评论排序',
    
    -- IP和设备信息（用于风控）
    ip_address INT UNSIGNED DEFAULT 0 COMMENT 'IP地址（整数存储），用于风控和地域分析',
    device_info VARCHAR(100) DEFAULT '' COMMENT '设备信息，记录评论时使用的设备',
    
    -- 评论状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '评论状态 1:正常 2:隐藏 3:删除，用于评论管理',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间，记录用户发表评论的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录评论最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 目标内容索引
    INDEX idx_target (target_type, target_id) COMMENT '目标内容索引：查询特定内容的所有评论时使用',
    INDEX idx_target_status (target_type, target_id, status, audit_status) COMMENT '目标状态复合索引：查询有效评论时使用',
    
    -- 用户评论索引
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户所有评论时使用',
    INDEX idx_user_status (user_id, status) COMMENT '用户状态复合索引：查询用户有效评论时使用',
    
    -- 回复关系索引
    INDEX idx_parent_id (parent_id) COMMENT '父评论索引：查询某条评论的所有回复时使用',
    INDEX idx_reply_to_user (reply_to_user_id) COMMENT '回复用户索引：查询用户收到的所有回复时使用',
    
    -- 审核管理索引
    INDEX idx_audit_status (audit_status) COMMENT '审核状态索引：内容审核管理时使用',
    INDEX idx_audit_created (audit_status, created_at) COMMENT '审核时间复合索引：按时间处理待审核内容',
    
    -- 热门评论索引
    INDEX idx_like_count (like_count DESC) COMMENT '点赞数排序索引：热门评论排序时使用',
    INDEX idx_reply_count (reply_count DESC) COMMENT '回复数排序索引：活跃评论排序时使用',
    
    -- 时间索引
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按时间范围查询评论时使用',
    
    -- 风控索引
    INDEX idx_ip_address (ip_address) COMMENT 'IP地址索引：风控分析和IP统计时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_comments_user_id
        FOREIGN KEY (user_id) REFERENCES users(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_comments_reply_to_user
        FOREIGN KEY (reply_to_user_id) REFERENCES users(id)
        ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_comments_parent
        FOREIGN KEY (parent_id) REFERENCES comments(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='评论表：记录用户对各种内容的评论和回复，支持多级回复结构，包含审核机制和互动统计';

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询打卡记录的所有点赞用户
-- SELECT l.*, u.nickname, u.avatar FROM likes l
-- JOIN users u ON l.user_id = u.id
-- WHERE l.target_type = 1 AND l.target_id = ? AND l.status = 1
-- ORDER BY l.created_at DESC;

-- 统计打卡记录的点赞数
-- SELECT COUNT(*) as like_count FROM likes 
-- WHERE target_type = 1 AND target_id = ? AND status = 1;

-- 查询打卡记录的评论列表（分页）
-- SELECT c.*, u.nickname, u.avatar FROM comments c
-- JOIN users u ON c.user_id = u.id
-- WHERE c.target_type = 1 AND c.target_id = ? 
-- AND c.status = 1 AND c.audit_status = 2
-- ORDER BY c.created_at DESC LIMIT 20 OFFSET 0;

-- 查询评论的回复列表
-- SELECT c.*, u.nickname, u.avatar, ru.nickname as reply_to_nickname FROM comments c
-- JOIN users u ON c.user_id = u.id
-- LEFT JOIN users ru ON c.reply_to_user_id = ru.id
-- WHERE c.parent_id = ? AND c.status = 1 AND c.audit_status = 2
-- ORDER BY c.created_at ASC;

-- 查询用户的所有点赞记录
-- SELECT l.*, cr.practice_duration, cr.score_1min FROM likes l
-- JOIN checkin_records cr ON l.target_id = cr.id
-- WHERE l.user_id = ? AND l.target_type = 1 AND l.status = 1
-- ORDER BY l.created_at DESC LIMIT 50;

-- 查询热门评论（按点赞数排序）
-- SELECT c.*, u.nickname, u.avatar FROM comments c
-- JOIN users u ON c.user_id = u.id
-- WHERE c.target_type = 1 AND c.target_id = ? 
-- AND c.status = 1 AND c.audit_status = 2
-- ORDER BY c.like_count DESC, c.created_at DESC LIMIT 10;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 点赞系统规则：
   - 同一用户对同一内容只能点赞一次（唯一约束保证）
   - 支持点赞和取消点赞操作（通过status字段控制）
   - 支持多种内容类型：打卡记录、评论、视频等
   - 点赞数据用于热门内容排序和用户互动分析

2. 评论系统规则：
   - 支持多级回复结构（parent_id实现）
   - 评论内容需要审核（audit_status控制）
   - 支持@回复功能（reply_to_user_id标识）
   - 评论长度限制500字符，防止刷屏

3. 内容审核机制：
   - 评论发表后进入待审核状态
   - 支持自动审核和人工审核
   - 审核拒绝需要提供具体原因
   - 审核通过的内容才会公开显示

4. 社交互动统计：
   - 冗余存储点赞数、回复数等统计数据
   - 定期同步统计数据保证准确性
   - 支持热门内容和活跃用户排序

5. 风控和安全：
   - 记录IP地址和设备信息用于风控
   - 支持内容举报和违规处理
   - 防止恶意刷赞和垃圾评论

6. 性能优化：
   - 合理的索引设计支持各种查询场景
   - 冗余统计字段减少实时计算
   - 分页查询支持大量评论数据
   - 外键约束保证数据一致性
*/
