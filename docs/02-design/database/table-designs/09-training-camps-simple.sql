-- =====================================================
-- 训练营系统数据库设计 - 极简版
-- =====================================================
-- 设计理念：基于页面实际需求，避免过度设计
-- 适用场景：2-3个训练营，不频繁变化，布局基本一致
-- 扩展策略：渐进式设计，需要时再扩展
-- =====================================================

-- =====================================================
-- 训练营主表 (training_camps)
-- =====================================================
-- 用途：存储训练营的基本信息，支持页面动态渲染
-- 设计原则：满足当前页面需求，预留扩展空间
-- =====================================================

CREATE TABLE training_camps (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '训练营ID，主键自增，全平台唯一标识',
    
    -- ==================== 页面展示基本信息 ====================
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '训练营标题，如"21天跳绳养成计划"，页面顶部展示',
    subtitle VARCHAR(300) NOT NULL DEFAULT '' COMMENT '训练营副标题，如"让孩子从不会跳到连续100个"，标题下方展示',
    
    -- ==================== 核心价值区域数据 ====================
    hero_number VARCHAR(50) NOT NULL DEFAULT '' COMMENT '核心数字展示，如"0→100个"，突出训练效果',
    hero_text VARCHAR(100) NOT NULL DEFAULT '' COMMENT '核心文案展示，如"21天技能飞跃"，强调时间价值',
    pain_solution VARCHAR(200) NOT NULL DEFAULT '' COMMENT '解决痛点描述，如"解决不会教、没方法、没效果的困扰"',
    
    -- ==================== 训练配置信息 ====================
    duration_days INT UNSIGNED NOT NULL DEFAULT 21 COMMENT '训练营总天数，如7天、21天、30天',
    daily_minutes INT UNSIGNED NOT NULL DEFAULT 15 COMMENT '每日建议训练时长（分钟）',
    difficulty_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '难度等级 1:新手 2:初级 3:中级 4:高级 5:专业',
    age_group VARCHAR(20) NOT NULL DEFAULT '' COMMENT '适用年龄段，如"3-6岁"、"7-12岁"，用于推荐匹配',
    
    -- ==================== 商业信息 ====================
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格（元），0.00表示免费',
    original_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '原价（元），用于显示优惠幅度',
    is_free TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否免费 0:付费 1:免费',
    
    -- ==================== 视频关联 ====================
    video_collection_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联视频集合ID，关联video_collections.id',
    featured_video_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重点推荐视频ID，关联videos.id，详情页重点展示',
    
    -- ==================== 页面配置数据（JSON存储，灵活扩展） ====================
    key_benefits JSON COMMENT '核心收益点，如[{"icon":"🎯","text":"掌握跳绳技能"},{"icon":"💪","text":"养成运动习惯"}]',
    promises JSON COMMENT '服务承诺，如[{"text":"完全免费"},{"text":"随时可退"},{"text":"专业指导"}]',
    tags JSON COMMENT '训练营标签，如["保姆级教程","社群交流","零基础"]，用于筛选和展示',
    
    -- ==================== 社交功能配置 ====================
    share_title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '分享标题，微信分享时使用',
    share_desc VARCHAR(300) NOT NULL DEFAULT '' COMMENT '分享描述，微信分享时使用',
    share_image VARCHAR(255) NOT NULL DEFAULT '' COMMENT '分享图片URL，微信分享时使用',
    wechat_helper VARCHAR(100) NOT NULL DEFAULT '' COMMENT '微信助手号，如"jump_helper_2024"',
    wechat_group_benefits JSON COMMENT '加群收益描述，如["个性化训练计划","1000+家长交流","每日训练提醒","专业进度分析"]',
    
    -- ==================== 统计信息（冗余字段，提升查询性能） ====================
    total_participants INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总参与人数，用于社会证明展示',
    total_completions INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总完成人数，用于计算完成率',
    completion_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '完成率百分比，0.00-100.00',
    average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00 COMMENT '平均评分，0.00-5.00，用户评价的平均分',
    total_reviews INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '评价总数，用于展示评价数量',
    
    -- ==================== 状态管理 ====================
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '训练营状态 1:正常 2:暂停 3:下线，控制是否对外展示',
    is_featured TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐，推荐训练营优先展示',
    is_coming_soon TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否即将推出 0:正常 1:即将推出，用于预热展示',
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重，数值越大排序越靠前',
    
    -- ==================== 时间记录 ====================
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录训练营创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录最后修改时间',
    deleted_at TIMESTAMP NULL COMMENT '软删除时间，NULL表示未删除，支持数据恢复',
    
    -- ==================== 索引设计（基础索引，按需扩展） ====================
    INDEX idx_status_featured (status, is_featured, sort_order DESC) COMMENT '状态和推荐排序索引，用于首页列表查询',
    INDEX idx_difficulty_age (difficulty_level, age_group) COMMENT '难度和年龄筛选索引，用于个性化推荐',
    INDEX idx_price (is_free, price) COMMENT '价格筛选索引，用于价格相关查询',
    INDEX idx_video_collection (video_collection_id) COMMENT '视频集合关联索引',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除索引'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='训练营主表：存储训练营的基本信息和配置，支持页面动态渲染，采用渐进式设计理念';

-- =====================================================
-- 视频集合表 (video_collections) - 复用视频资源
-- =====================================================
-- 用途：管理视频的集合，支持训练营关联和视频复用
-- 设计原则：简单实用，支持视频资源的灵活组合
-- =====================================================

CREATE TABLE video_collections (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频集合ID，主键自增',
    
    -- 基本信息
    name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '集合名称，如"21天跳绳基础教程"',
    description TEXT COMMENT '集合描述，说明该集合的内容和用途',
    cover_image VARCHAR(255) NOT NULL DEFAULT '' COMMENT '集合封面图URL',
    
    -- 集合类型
    collection_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '集合类型 1:训练营课程 2:专题教程 3:技能合集',
    
    -- 统计信息
    total_videos INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频总数，冗余字段',
    total_duration INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总时长（秒），所有视频时长之和',
    
    -- 状态
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '集合状态 1:正常 2:禁用',
    
    -- 时间记录
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type_status (collection_type, status) COMMENT '类型状态索引'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频集合表：管理视频的集合，支持训练营关联和视频复用';

-- =====================================================
-- 视频集合关联表 (video_collection_items)
-- =====================================================
-- 用途：管理视频集合与具体视频的关联关系
-- 设计原则：支持排序和个性化配置，为后期扩展预留空间
-- =====================================================

CREATE TABLE video_collection_items (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联记录ID，主键自增',
    collection_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频集合ID，关联video_collections.id',
    video_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频ID，关联videos.id',
    
    -- 排序和展示
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频在集合中的排序，数值越小越靠前',
    
    -- 个性化配置
    custom_title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '自定义标题，可覆盖原视频标题',
    custom_subtitle VARCHAR(200) NOT NULL DEFAULT '' COMMENT '自定义副标题，补充说明',
    
    -- 解锁条件（为后期扩展预留）
    unlock_day INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '解锁天数，训练营第几天解锁该视频',
    is_required TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否必看 0:选看 1:必看',
    
    -- 时间记录
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_collection_sort (collection_id, sort_order) COMMENT '集合内视频排序索引',
    INDEX idx_video_collections (video_id) COMMENT '视频所属集合查询索引',
    
    -- 外键约束
    FOREIGN KEY (collection_id) REFERENCES video_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    
    -- 唯一约束：同一集合中不能重复添加同一视频
    UNIQUE KEY uk_collection_video (collection_id, video_id)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频集合关联表：管理视频集合与具体视频的关联关系，支持排序和个性化配置';

-- =====================================================
-- 用户训练营参与记录表 (user_camp_participations)
-- =====================================================
-- 用途：记录用户参与训练营的状态和进度
-- 业务场景：用户报名、学习进度跟踪、完成状态管理
-- 设计原则：详细的进度跟踪，支持暂停恢复等状态管理
-- =====================================================

CREATE TABLE user_camp_participations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '参与记录ID，主键自增',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID，关联users.id',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',

    -- ==================== 参与信息 ====================
    participation_date DATE NOT NULL COMMENT '参与日期，用户报名或开始训练营的日期',
    start_date DATE NULL COMMENT '实际开始日期，用户实际开始训练的日期',
    expected_end_date DATE NULL COMMENT '预期结束日期，根据训练营天数计算',
    actual_end_date DATE NULL COMMENT '实际结束日期，用户实际完成训练营的日期',

    -- ==================== 进度信息 ====================
    current_day INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '当前训练天数，用户进行到第几天',
    completed_videos INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已完成视频数',
    total_videos INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总视频数，冗余字段',
    progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '完成进度百分比，0.00-100.00',

    -- ==================== 状态管理 ====================
    participation_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '参与状态 1:进行中 2:已完成 3:已暂停 4:已退出 5:已过期',
    pause_reason VARCHAR(200) NOT NULL DEFAULT '' COMMENT '暂停原因，用户暂停训练营的原因',
    pause_date DATE NULL COMMENT '暂停日期',
    resume_date DATE NULL COMMENT '恢复日期',

    -- ==================== 完成信息 ====================
    completion_score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '完成得分，0.00-100.00，训练营完成质量评分',
    completion_certificate VARCHAR(255) NOT NULL DEFAULT '' COMMENT '完成证书URL，训练营完成后的证书图片',

    -- ==================== 支付信息（如果是付费训练营） ====================
    payment_status TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态 0:无需支付 1:待支付 2:已支付 3:已退款',
    payment_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
    payment_time TIMESTAMP NULL COMMENT '支付时间',

    -- ==================== 统计信息 ====================
    total_checkins INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总打卡次数',
    consecutive_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '连续打卡天数',
    total_study_minutes INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总学习时长（分钟）',

    -- ==================== 评价信息 ====================
    rating TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户评分，1-5分，用户对训练营的评价',
    review_text TEXT COMMENT '评价内容，用户的详细评价',
    review_date DATE NULL COMMENT '评价日期',

    -- ==================== 时间记录 ====================
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- ==================== 索引设计 ====================
    INDEX idx_camp_user (camp_id, user_id) COMMENT '训练营用户索引',
    INDEX idx_child_status (child_id, participation_status) COMMENT '孩子参与状态索引',
    INDEX idx_participation_date (participation_date) COMMENT '参与日期索引',
    INDEX idx_status_progress (participation_status, progress_percentage) COMMENT '状态进度索引',

    -- ==================== 外键约束 ====================
    FOREIGN KEY (camp_id) REFERENCES training_camps(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE CASCADE,

    -- ==================== 唯一约束 ====================
    -- 同一个孩子不能重复参与同一个训练营
    UNIQUE KEY uk_camp_child (camp_id, child_id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户训练营参与记录表：记录用户参与训练营的完整生命周期，包含进度跟踪和状态管理';

-- =====================================================
-- 初始化数据示例
-- =====================================================

-- =====================================================
-- 插入不同类型的训练营数据
-- =====================================================

-- 1. 免费基础训练营 - 21天跳绳养成计划
INSERT INTO training_camps (
    title, subtitle, hero_number, hero_text, pain_solution,
    duration_days, daily_minutes, difficulty_level, age_group,
    price, original_price, is_free,
    key_benefits, promises, tags,
    share_title, share_desc, share_image,
    wechat_helper, wechat_group_benefits,
    total_participants, average_rating, total_reviews,
    status, is_featured, sort_order
) VALUES (
    '21天跳绳养成计划',
    '让孩子从不会跳到连续100个',
    '0 → 100个',
    '21天技能飞跃',
    '解决不会教、没方法、没效果的困扰',
    21, 15, 1, '3-12岁',
    0.00, 99.00, 1,
    '[{"icon":"🎯","text":"掌握跳绳技能"},{"icon":"💪","text":"养成运动习惯"},{"icon":"📈","text":"提升身体协调"},{"icon":"🏆","text":"建立自信心"}]',
    '[{"text":"完全免费"},{"text":"随时可退"},{"text":"专业指导"},{"text":"社群支持"}]',
    '["保姆级教程","社群交流","零基础","免费体验"]',
    '21天跳绳养成计划 - 专业指导，21天见效果',
    '让孩子从不会跳到连续100个，专业教练指导，完全免费',
    '/images/camps/21day-basic-share.jpg',
    'jump_helper_2024',
    '["个性化训练计划","1000+家长交流经验","每日训练提醒","专业进度分析"]',
    1234, 4.8, 567,
    1, 1, 100
);

-- 2. 付费进阶训练营 - 暑假跳绳追高计划
INSERT INTO training_camps (
    title, subtitle, hero_number, hero_text, pain_solution,
    duration_days, daily_minutes, difficulty_level, age_group,
    price, original_price, is_free,
    key_benefits, promises, tags,
    share_title, share_desc, share_image,
    wechat_helper, wechat_group_benefits,
    total_participants, average_rating, total_reviews,
    status, is_featured, sort_order
) VALUES (
    '暑假跳绳追高计划',
    '科学训练助力孩子长高5-8cm',
    '5-8cm',
    '暑假黄金增高期',
    '抓住暑假黄金期，科学运动促进身高增长',
    30, 25, 2, '6-12岁',
    99.00, 199.00, 0,
    '[{"icon":"📏","text":"促进身高增长"},{"icon":"🏃","text":"提升运动能力"},{"icon":"💪","text":"增强体质"},{"icon":"🧠","text":"提高专注力"}]',
    '[{"text":"专业教练指导"},{"text":"科学训练计划"},{"text":"效果保证"},{"text":"7天无理由退款"}]',
    '["科学增高","专业指导","暑假专属","效果保证"]',
    '暑假跳绳追高计划 - 科学训练助力孩子长高',
    '抓住暑假黄金期，30天科学跳绳训练，助力孩子长高5-8cm',
    '/images/camps/summer-height-share.jpg',
    'jump_coach_pro',
    '["专属增高方案","营养指导建议","身高监测工具","专业教练答疑"]',
    456, 4.9, 234,
    1, 1, 90
);

-- 3. 即将推出 - 冲刺200/分钟挑战营
INSERT INTO training_camps (
    title, subtitle, hero_number, hero_text, pain_solution,
    duration_days, daily_minutes, difficulty_level, age_group,
    price, original_price, is_free,
    key_benefits, promises, tags,
    share_title, share_desc, share_image,
    wechat_helper, wechat_group_benefits,
    total_participants, average_rating, total_reviews,
    status, is_featured, is_coming_soon, sort_order
) VALUES (
    '冲刺200/分钟挑战营',
    '从基础到高手，挑战跳绳极限',
    '200次/分钟',
    '极限挑战',
    '突破瓶颈，从普通到专业的跳绳高手之路',
    14, 30, 4, '8-15岁',
    199.00, 299.00, 0,
    '[{"icon":"⚡","text":"极速跳绳技巧"},{"icon":"🏆","text":"挑战个人极限"},{"icon":"🎖️","text":"获得专业认证"},{"icon":"👑","text":"成为跳绳高手"}]',
    '[{"text":"专业高级教练"},{"text":"个性化训练"},{"text":"技术突破保证"},{"text":"挑战成功奖励"}]',
    '["高级挑战","专业认证","极限突破","即将推出"]',
    '冲刺200/分钟挑战营 - 挑战跳绳极限',
    '专业训练营，挑战200次/分钟极限速度，成为跳绳高手',
    '/images/camps/speed-challenge-share.jpg',
    'jump_master_2024',
    '["高级训练计划","技术分析指导","挑战赛参与资格","专业认证证书"]',
    0, 0.0, 0,
    1, 0, 1, 80
);

-- 4. 即将推出 - 花样跳绳艺术营
INSERT INTO training_camps (
    title, subtitle, hero_number, hero_text, pain_solution,
    duration_days, daily_minutes, difficulty_level, age_group,
    price, original_price, is_free,
    key_benefits, promises, tags,
    share_title, share_desc, share_image,
    wechat_helper, wechat_group_benefits,
    total_participants, average_rating, total_reviews,
    status, is_featured, is_coming_soon, sort_order
) VALUES (
    '花样跳绳艺术营',
    '学会10种花样跳绳，成为校园明星',
    '10种花样',
    '艺术与运动结合',
    '让跳绳变成艺术，培养孩子的创造力和表现力',
    21, 20, 3, '7-14岁',
    149.00, 249.00, 0,
    '[{"icon":"🎨","text":"学会花样技巧"},{"icon":"⭐","text":"成为校园明星"},{"icon":"🎭","text":"培养艺术感"},{"icon":"🤸","text":"提升协调性"}]',
    '[{"text":"艺术级教学"},{"text":"创意花样设计"},{"text":"表演指导"},{"text":"才艺展示机会"}]',
    '["花样跳绳","艺术培养","创意表演","即将推出"]',
    '花样跳绳艺术营 - 让跳绳变成艺术',
    '学会10种花样跳绳技巧，培养孩子艺术感和创造力',
    '/images/camps/artistic-rope-share.jpg',
    'jump_artist_2024',
    '["花样技巧教学","创意设计指导","表演机会推荐","艺术作品展示"]',
    0, 0.0, 0,
    1, 0, 1, 70
);

-- 5. 特殊训练营 - 亲子跳绳互动营（未来规划）
INSERT INTO training_camps (
    title, subtitle, hero_number, hero_text, pain_solution,
    duration_days, daily_minutes, difficulty_level, age_group,
    price, original_price, is_free,
    key_benefits, promises, tags,
    share_title, share_desc, share_image,
    wechat_helper, wechat_group_benefits,
    total_participants, average_rating, total_reviews,
    status, is_featured, is_coming_soon, sort_order
) VALUES (
    '亲子跳绳互动营',
    '家长和孩子一起运动，增进亲子关系',
    '亲子互动',
    '家庭运动时光',
    '工作忙碌缺少陪伴？通过跳绳增进亲子关系',
    14, 20, 1, '全家庭',
    79.00, 129.00, 0,
    '[{"icon":"👨‍👩‍👧‍👦","text":"增进亲子关系"},{"icon":"💝","text":"共同成长体验"},{"icon":"🏠","text":"家庭运动习惯"},{"icon":"😊","text":"快乐亲子时光"}]',
    '[{"text":"亲子互动设计"},{"text":"家庭运动指导"},{"text":"关系改善保证"},{"text":"温馨回忆创造"}]',
    '["亲子互动","家庭运动","关系增进","即将推出"]',
    '亲子跳绳互动营 - 家长孩子一起运动',
    '通过跳绳运动增进亲子关系，创造美好家庭时光',
    '/images/camps/family-rope-share.jpg',
    'family_jump_2024',
    '["亲子互动方案","家庭运动计划","关系改善指导","温馨时光记录"]',
    0, 0.0, 0,
    2, 0, 1, 60
);

-- =====================================================
-- 插入视频集合数据
-- =====================================================

-- 1. 21天跳绳基础教程视频集合
INSERT INTO video_collections (
    name, description, cover_image, collection_type, total_videos, total_duration, status
) VALUES (
    '21天跳绳基础教程',
    '从零基础到熟练掌握，21天系统化跳绳教学视频集合',
    '/images/collections/21day-basic-cover.jpg',
    1, 8, 1800, 1
);

-- 2. 暑假追高专业训练视频集合
INSERT INTO video_collections (
    name, description, cover_image, collection_type, total_videos, total_duration, status
) VALUES (
    '暑假追高专业训练',
    '科学的增高训练视频，结合跳绳和拉伸运动',
    '/images/collections/summer-height-cover.jpg',
    1, 12, 2400, 1
);

-- 3. 高级速度挑战视频集合
INSERT INTO video_collections (
    name, description, cover_image, collection_type, total_videos, total_duration, status
) VALUES (
    '高级速度挑战训练',
    '专业级跳绳速度训练，挑战200次/分钟极限',
    '/images/collections/speed-challenge-cover.jpg',
    1, 10, 2100, 1
);

-- =====================================================
-- 更新训练营关联视频集合
-- =====================================================

-- 更新训练营的视频集合关联
UPDATE training_camps SET video_collection_id = 1, featured_video_id = 1 WHERE title = '21天跳绳养成计划';
UPDATE training_camps SET video_collection_id = 2, featured_video_id = 5 WHERE title = '暑假跳绳追高计划';
UPDATE training_camps SET video_collection_id = 3, featured_video_id = 8 WHERE title = '冲刺200/分钟挑战营';

/*
=====================================================
设计说明和扩展建议
=====================================================

1. 设计理念：
   - 基于页面实际需求设计，避免过度设计
   - 使用JSON字段存储灵活配置，便于扩展
   - 预留扩展字段，支持渐进式演进

2. 核心特点：
   - 满足当前2-3个训练营的需求
   - 支持页面的完全数据驱动渲染
   - 社交功能配置简单实用
   - 视频资源可复用

3. 扩展路径：
   - 需要更多差异化时，可以增加page_template字段
   - 需要A/B测试时，可以增加page_configs表
   - 需要用户参与记录时，可以增加user_participations表
   - 需要评价系统时，可以增加reviews表

4. API设计建议：
   GET /api/v1/camps/{id}/detail
   - 返回训练营完整信息
   - 包含视频集合数据
   - 支持个性化推荐

5. 前端使用：
   - 页面完全由数据驱动
   - JSON配置直接用于页面渲染
   - 支持不同训练营的差异化展示
*/
