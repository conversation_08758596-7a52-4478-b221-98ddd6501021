-- =====================================================
-- 打卡系统表设计
-- =====================================================
-- 模块: 打卡系统 (Check-in System)
-- 包含表: checkin_records
-- 功能: 记录用户打卡数据、成绩统计、历史追踪
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 打卡记录表 (checkin_records)
-- =====================================================
-- 用途: 存储用户每日打卡的详细记录和成绩数据
-- 业务场景: 打卡提交、历史查询、成绩统计、排行榜生成
-- 设计原则: 支持丰富的打卡内容，确保数据完整性和查询性能
-- =====================================================

CREATE TABLE checkin_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '打卡记录ID，主键自增，全平台唯一标识',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识打卡的孩子档案',
    
    -- 必填的打卡内容
    practice_duration INT UNSIGNED DEFAULT 0 COMMENT '练习时长（分钟），必填字段，用于基础积分计算',
    
    -- 可选的跳绳成绩
    score_1min INT UNSIGNED DEFAULT 0 COMMENT '一分钟跳绳成绩（个数），可选填写，用于技能评估和排行榜',
    score_continuous INT UNSIGNED DEFAULT 0 COMMENT '连续跳绳成绩（个数），可选填写，用于耐力评估',
    
    -- 可选的分享内容
    video_id VARCHAR(100) DEFAULT '' COMMENT '视频号视频ID，用户发布到微信视频号的视频标识',
    moments_screenshot VARCHAR(255) DEFAULT '' COMMENT '朋友圈截图URL，用户分享到朋友圈的截图图片地址',
    experience_text TEXT COMMENT '经验感悟文字，用户分享的心得体会，最多200字',
    
    -- 打卡数据验证和状态
    is_valid TINYINT UNSIGNED DEFAULT 1 COMMENT '数据有效性 0:无效 1:有效，用于过滤异常数据',
    audit_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:待审核 2:审核通过 3:审核拒绝，内容审核使用',
    audit_reason VARCHAR(200) DEFAULT '' COMMENT '审核原因，审核拒绝时的具体原因说明',
    
    -- 积分和奖励记录
    base_points INT UNSIGNED DEFAULT 0 COMMENT '基础积分，根据练习时长计算的基础分数',
    bonus_points INT UNSIGNED DEFAULT 0 COMMENT '奖励积分，额外内容（视频、截图等）获得的奖励分数',
    total_points INT UNSIGNED DEFAULT 0 COMMENT '总积分，base_points + bonus_points，冗余字段提升查询性能',
    
    -- 打卡时间信息
    checkin_date DATE NOT NULL COMMENT '打卡日期，格式YYYY-MM-DD，用于日历展示和连续天数计算',
    checkin_time TIME DEFAULT '00:00:00' COMMENT '打卡时间，格式HH:MM:SS，记录具体打卡时刻',
    
    -- 地理位置信息（可选）
    location_province VARCHAR(20) DEFAULT '' COMMENT '打卡省份，用于地域统计和本地化推荐',
    location_city VARCHAR(20) DEFAULT '' COMMENT '打卡城市，用于同城用户互动',
    
    -- 设备和环境信息
    device_info VARCHAR(100) DEFAULT '' COMMENT '设备信息，记录打卡时使用的设备类型',
    weather VARCHAR(20) DEFAULT '' COMMENT '天气情况，可选记录，用于数据分析',
    
    -- 社交互动统计（冗余字段）
    like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数量，冗余字段，提升社交功能查询性能',
    comment_count INT UNSIGNED DEFAULT 0 COMMENT '评论数量，冗余字段，用于热门内容排序',
    share_count INT UNSIGNED DEFAULT 0 COMMENT '分享次数，记录被分享的次数',
    
    -- 记录状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '记录状态 1:正常 2:隐藏 3:删除，用于内容管理',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录打卡提交的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 孩子关联索引
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子所有打卡记录时使用',

    -- 打卡日期相关索引
    UNIQUE KEY uk_child_date (child_id, checkin_date) COMMENT '唯一约束：同一孩子每天只能打卡一次',
    INDEX idx_checkin_date (checkin_date) COMMENT '打卡日期索引：按日期范围查询打卡记录',
    INDEX idx_date_status (checkin_date, status) COMMENT '日期状态复合索引：查询特定日期有效记录',
    
    -- 成绩排行索引
    INDEX idx_score_1min (score_1min DESC) COMMENT '一分钟成绩排行索引：生成成绩排行榜时使用',
    INDEX idx_score_continuous (score_continuous DESC) COMMENT '连续跳成绩排行索引：耐力排行榜时使用',
    INDEX idx_practice_duration (practice_duration DESC) COMMENT '练习时长排行索引：时长排行榜时使用',
    
    -- 积分相关索引
    INDEX idx_total_points (total_points DESC) COMMENT '总积分排行索引：积分排行榜时使用',
    INDEX idx_base_points (base_points) COMMENT '基础积分索引：积分统计分析时使用',
    
    -- 审核状态索引
    INDEX idx_audit_status (audit_status) COMMENT '审核状态索引：内容审核管理时使用',
    INDEX idx_valid_status (is_valid, status) COMMENT '有效状态复合索引：查询有效记录时使用',
    
    -- 地域索引
    INDEX idx_location (location_province, location_city) COMMENT '地域复合索引：地域化统计和推荐时使用',
    
    -- 社交互动索引
    INDEX idx_like_count (like_count DESC) COMMENT '点赞数排序索引：热门内容排序时使用',
    INDEX idx_comment_count (comment_count DESC) COMMENT '评论数排序索引：活跃内容排序时使用',
    
    -- 时间范围索引
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按时间范围查询记录时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_checkin_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='打卡记录表：存储用户每日打卡详细数据，支持丰富内容记录和社交互动，包含积分计算和排行榜统计';

-- =====================================================
-- 分区策略（大表优化）
-- =====================================================
-- 按年份分区，提升大数据量下的查询性能
-- ALTER TABLE checkin_records 
-- PARTITION BY RANGE (YEAR(checkin_date)) (
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p2026 VALUES LESS THAN (2027),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询用户某个孩子的打卡历史
-- SELECT * FROM checkin_records 
-- WHERE user_id = ? AND child_id = ? AND status = 1 
-- ORDER BY checkin_date DESC LIMIT 30;

-- 查询某日期范围内的打卡记录
-- SELECT * FROM checkin_records 
-- WHERE checkin_date BETWEEN ? AND ? AND status = 1 AND is_valid = 1
-- ORDER BY total_points DESC;

-- 生成一分钟成绩排行榜
-- SELECT cr.*, c.nickname, c.avatar FROM checkin_records cr
-- JOIN children c ON cr.child_id = c.id
-- WHERE cr.score_1min > 0 AND cr.status = 1 AND cr.is_valid = 1
-- AND c.show_in_leaderboard = 1 AND c.privacy_level <= 2
-- ORDER BY cr.score_1min DESC LIMIT 100;

-- 统计用户连续打卡天数
-- SELECT COUNT(*) as continuous_days FROM checkin_records 
-- WHERE user_id = ? AND child_id = ? AND status = 1
-- AND checkin_date >= (
--     SELECT DATE_SUB(MAX(checkin_date), INTERVAL 30 DAY) 
--     FROM checkin_records 
--     WHERE user_id = ? AND child_id = ?
-- );

-- 查询热门打卡内容（社交功能）
-- SELECT * FROM checkin_records 
-- WHERE status = 1 AND is_valid = 1 AND audit_status = 2
-- AND (like_count > 10 OR comment_count > 5)
-- ORDER BY (like_count + comment_count * 2) DESC LIMIT 50;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 打卡限制规则：
   - 每个用户每个孩子每天只能打卡一次（唯一约束保证）
   - 练习时长为必填项，其他内容为可选
   - 支持丰富的打卡内容：成绩、视频、截图、感悟

2. 积分计算规则：
   - 基础积分：根据练习时长计算（10分/次）
   - 奖励积分：视频分享+15分，朋友圈截图+10分，经验感悟+8分
   - 完整打卡奖励：所有内容都填写额外+10分
   - 连续打卡奖励：连续7天+50分（在业务逻辑中计算）

3. 数据验证和审核：
   - 自动数据验证：跳绳成绩合理性检查
   - 内容审核：文字和图片内容的人工/自动审核
   - 异常数据标记：明显不合理的数据标记为无效

4. 社交互动支持：
   - 冗余存储点赞、评论、分享数量
   - 支持热门内容排序和推荐
   - 隐私保护：用户可选择隐藏打卡记录

5. 性能优化设计：
   - 合理的索引设计支持各种查询场景
   - 冗余字段减少复杂计算
   - 分区策略应对大数据量
   - 外键约束保证数据一致性

6. 扩展性考虑：
   - 支持地理位置记录
   - 设备信息记录用于数据分析
   - 天气信息等环境因素记录
   - JSON字段可扩展更多自定义数据
*/
