-- =====================================================
-- 系统任务表设计
-- =====================================================
-- 模块: 系统任务 (System Tasks)
-- 包含表: system_tasks, user_task_participations
-- 功能: 平台官方任务、用户参与记录、任务完成状态跟踪
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 系统任务表 (system_tasks)
-- =====================================================
-- 用途: 定义平台发布的官方任务
-- 业务场景: 任务管理、用户激励、活动运营
-- 设计原则: 灵活的任务配置，支持多种任务类型和完成条件
-- =====================================================

CREATE TABLE system_tasks (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID，主键自增，全平台唯一标识',
    
    -- 任务基本信息
    title VARCHAR(100) DEFAULT '' COMMENT '任务标题，如"连续打卡7天"、"完成10个跳绳视频"',
    description TEXT COMMENT '任务描述，详细说明任务内容、完成条件和奖励',
    icon VARCHAR(255) DEFAULT '' COMMENT '任务图标URL，用于展示的图标图片地址',
    banner_image VARCHAR(255) DEFAULT '' COMMENT '任务横幅图URL，任务详情页的横幅图片',
    
    -- 任务类型和分类
    task_type TINYINT UNSIGNED DEFAULT 1 COMMENT '任务类型 1:打卡任务 2:学习任务 3:社交任务 4:成长任务 5:活动任务',
    task_category TINYINT UNSIGNED DEFAULT 1 COMMENT '任务分类 1:日常任务 2:周任务 3:月任务 4:节日任务 5:挑战任务',
    difficulty_level TINYINT UNSIGNED DEFAULT 1 COMMENT '任务难度 1:简单 2:普通 3:困难 4:专家 5:大师，任务完成难度',
    
    -- 完成条件配置
    completion_type TINYINT UNSIGNED DEFAULT 1 COMMENT '完成条件类型 1:次数达成 2:连续天数 3:累计时长 4:分数达成 5:复合条件',
    target_value INT UNSIGNED DEFAULT 1 COMMENT '目标数值，如打卡次数、连续天数、累计时长等',
    target_unit VARCHAR(20) DEFAULT '' COMMENT '目标单位，如"次"、"天"、"分钟"、"分"',
    completion_config JSON COMMENT '完成条件配置JSON，复杂条件的详细配置',
    
    -- 任务周期
    cycle_type TINYINT UNSIGNED DEFAULT 1 COMMENT '任务周期 1:一次性 2:每日 3:每周 4:每月 5:自定义周期',
    cycle_config JSON COMMENT '周期配置JSON，自定义周期的详细配置',
    
    -- 参与条件
    min_age TINYINT UNSIGNED DEFAULT 0 COMMENT '最小年龄限制，0表示不限制',
    max_age TINYINT UNSIGNED DEFAULT 100 COMMENT '最大年龄限制，100表示不限制',
    required_level TINYINT UNSIGNED DEFAULT 0 COMMENT '要求用户等级，0表示不限制',
    prerequisite_tasks JSON COMMENT '前置任务JSON，需要完成的前置任务ID列表',
    
    -- 奖励配置
    reward_type TINYINT UNSIGNED DEFAULT 1 COMMENT '奖励类型 1:积分 2:虚拟奖励 3:积分+奖励 4:无奖励',
    reward_points INT UNSIGNED DEFAULT 0 COMMENT '奖励积分，完成任务获得的积分数量',
    reward_items JSON COMMENT '奖励物品JSON，虚拟奖励的详细配置',
    bonus_config JSON COMMENT '额外奖励配置JSON，如连续完成奖励、首次完成奖励等',
    
    -- 任务限制
    max_participants INT UNSIGNED DEFAULT 0 COMMENT '最大参与人数，0表示不限制',
    current_participants INT UNSIGNED DEFAULT 0 COMMENT '当前参与人数，已参与任务的用户数量',
    max_completions INT UNSIGNED DEFAULT 0 COMMENT '最大完成次数，每个用户最多完成次数，0表示不限制',
    
    -- 任务时间
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '任务开始时间，任务开放参与的开始时间',
    end_time TIMESTAMP NULL COMMENT '任务结束时间，任务停止参与的结束时间，NULL表示永不过期',
    
    -- 任务状态
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '任务状态 1:未开始 2:进行中 3:已结束 4:已暂停 5:已取消',
    is_featured TINYINT UNSIGNED DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐，推荐任务会优先展示',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重，数值越大排序越靠前',
    
    -- 统计信息（冗余字段）
    total_participations BIGINT UNSIGNED DEFAULT 0 COMMENT '总参与次数，所有用户参与任务的总次数',
    total_completions BIGINT UNSIGNED DEFAULT 0 COMMENT '总完成次数，所有用户完成任务的总次数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比，0.00-100.00，任务完成率统计',
    
    -- 创建者信息
    created_by_admin_id BIGINT UNSIGNED DEFAULT 0 COMMENT '创建管理员ID，创建任务的管理员用户ID',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，任务创建的时间戳',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，任务信息最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 任务类型和分类索引
    INDEX idx_task_type (task_type) COMMENT '任务类型索引：按类型查询任务时使用',
    INDEX idx_task_category (task_category) COMMENT '任务分类索引：按分类查询任务时使用',
    INDEX idx_type_category (task_type, task_category) COMMENT '类型分类复合索引：分类筛选时使用',
    
    -- 难度和条件索引
    INDEX idx_difficulty_level (difficulty_level) COMMENT '难度等级索引：按难度筛选任务时使用',
    INDEX idx_completion_type (completion_type) COMMENT '完成条件类型索引：按条件类型查询时使用',
    
    -- 任务周期索引
    INDEX idx_cycle_type (cycle_type) COMMENT '任务周期索引：按周期类型查询时使用',
    
    -- 状态和推荐索引
    INDEX idx_status (status) COMMENT '任务状态索引：查询有效任务时使用',
    INDEX idx_featured_status (is_featured, status) COMMENT '推荐状态复合索引：查询推荐任务时使用',
    
    -- 时间范围索引
    INDEX idx_time_range (start_time, end_time) COMMENT '时间范围索引：查询有效期内任务时使用',
    INDEX idx_start_time (start_time) COMMENT '开始时间索引：任务调度时使用',
    INDEX idx_end_time (end_time) COMMENT '结束时间索引：任务清理时使用',
    
    -- 排序索引
    INDEX idx_sort_order (sort_order DESC) COMMENT '排序权重索引：任务排序展示时使用',
    INDEX idx_completion_rate (completion_rate DESC) COMMENT '完成率排序索引：按完成率排序时使用',
    
    -- 参与限制索引
    INDEX idx_participants (current_participants, max_participants) COMMENT '参与人数索引：检查参与限制时使用',
    
    -- 创建者索引
    INDEX idx_created_by_admin (created_by_admin_id) COMMENT '创建管理员索引：查询管理员创建的任务时使用',
    
    -- 时间记录索引
    INDEX idx_created_at (created_at DESC) COMMENT '创建时间排序索引：按创建时间排序任务时使用'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='系统任务表：定义平台发布的官方任务，支持多种任务类型、完成条件和奖励机制，包含详细的任务配置和统计信息';

-- =====================================================
-- 孩子任务参与表 (child_task_participations)
-- =====================================================
-- 用途: 记录孩子参与系统任务的详细信息
-- 业务场景: 任务进度跟踪、完成状态管理、奖励发放
-- 设计原则: 详细记录任务进度，支持复杂的完成条件判断
-- =====================================================

CREATE TABLE child_task_participations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '参与记录ID，主键自增，全平台唯一标识',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识参与任务的孩子档案',
    task_id BIGINT UNSIGNED DEFAULT 0 COMMENT '任务ID，关联system_tasks.id，标识参与的任务',
    
    -- 参与状态
    participation_status TINYINT UNSIGNED DEFAULT 1 COMMENT '参与状态 1:进行中 2:已完成 3:已放弃 4:已过期',
    
    -- 进度信息
    current_progress INT UNSIGNED DEFAULT 0 COMMENT '当前进度，已完成的数量或进度值',
    target_progress INT UNSIGNED DEFAULT 0 COMMENT '目标进度，需要达到的目标值（冗余字段）',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比，0.00-100.00，任务完成进度',
    
    -- 详细进度数据
    progress_data JSON COMMENT '进度详细数据JSON，复杂任务的详细进度信息',
    milestone_data JSON COMMENT '里程碑数据JSON，记录关键进度节点',
    
    -- 完成信息
    completed_at TIMESTAMP NULL COMMENT '完成时间，任务完成的时间戳，NULL表示未完成',
    completion_proof JSON COMMENT '完成证明JSON，任务完成的证明数据（如截图、视频等）',
    
    -- 奖励信息
    reward_status TINYINT UNSIGNED DEFAULT 0 COMMENT '奖励状态 0:未发放 1:已发放 2:发放失败',
    reward_points INT UNSIGNED DEFAULT 0 COMMENT '获得积分，完成任务实际获得的积分（冗余字段）',
    reward_items JSON COMMENT '获得奖励JSON，实际获得的虚拟奖励详情',
    reward_granted_at TIMESTAMP NULL COMMENT '奖励发放时间，奖励发放的时间戳',
    
    -- 任务周期信息
    cycle_period VARCHAR(50) DEFAULT '' COMMENT '任务周期标识，如"2025-01-W1"、"2025-01"，用于周期性任务',
    attempt_count INT UNSIGNED DEFAULT 1 COMMENT '尝试次数，用户参与该任务的次数',
    
    -- 连续性数据（用于连续任务）
    consecutive_days INT UNSIGNED DEFAULT 0 COMMENT '连续天数，连续完成任务的天数',
    last_activity_date DATE NULL COMMENT '最后活动日期，最近一次任务活动的日期',
    streak_broken_count INT UNSIGNED DEFAULT 0 COMMENT '中断次数，连续任务中断的次数',
    
    -- 时间记录
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间，用户开始参与任务的时间戳',
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间，进度最后更新的时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 孩子和任务关联索引
    UNIQUE KEY uk_child_task_cycle (child_id, task_id, cycle_period)
        COMMENT '唯一约束：同一孩子在同一周期内只能参与同一任务一次',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子参与的任务时使用',
    INDEX idx_task_id (task_id) COMMENT '任务ID索引：查询任务参与情况时使用',
    
    -- 参与状态索引
    INDEX idx_participation_status (participation_status) COMMENT '参与状态索引：按状态查询任务参与时使用',
    INDEX idx_task_status (task_id, participation_status) COMMENT '任务状态复合索引：统计任务参与状态时使用',
    
    -- 进度索引
    INDEX idx_progress_percentage (progress_percentage) COMMENT '进度百分比索引：按进度查询时使用',
    INDEX idx_current_progress (current_progress) COMMENT '当前进度索引：进度统计时使用',
    
    -- 完成状态索引
    INDEX idx_completed_at (completed_at) COMMENT '完成时间索引：按完成时间查询时使用',
    INDEX idx_completion_status (participation_status, completed_at) COMMENT '完成状态复合索引：查询已完成任务时使用',
    
    -- 奖励状态索引
    INDEX idx_reward_status (reward_status) COMMENT '奖励状态索引：奖励发放管理时使用',
    INDEX idx_reward_granted (reward_granted_at) COMMENT '奖励发放时间索引：奖励统计时使用',
    
    -- 周期索引
    INDEX idx_cycle_period (cycle_period) COMMENT '任务周期索引：周期性任务管理时使用',
    INDEX idx_attempt_count (attempt_count) COMMENT '尝试次数索引：多次参与统计时使用',
    
    -- 连续性索引
    INDEX idx_consecutive_days (consecutive_days DESC) COMMENT '连续天数排序索引：连续任务排行时使用',
    INDEX idx_last_activity_date (last_activity_date) COMMENT '最后活动日期索引：连续性计算时使用',
    
    -- 时间索引
    INDEX idx_started_at (started_at) COMMENT '开始时间索引：按参与时间查询时使用',
    INDEX idx_last_updated_at (last_updated_at DESC) COMMENT '最后更新时间索引：最近活动查询时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_child_task_participations_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE,

    CONSTRAINT fk_user_task_participations_task_id
        FOREIGN KEY (task_id) REFERENCES system_tasks(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='用户任务参与表：记录用户参与系统任务的详细信息，支持复杂的进度跟踪和奖励管理，包含连续性任务的特殊处理';

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询用户当前进行中的任务
-- SELECT st.*, utp.current_progress, utp.target_progress, utp.progress_percentage 
-- FROM system_tasks st
-- JOIN user_task_participations utp ON st.id = utp.task_id
-- WHERE utp.user_id = ? AND utp.child_id = ? AND utp.participation_status = 1
-- AND st.status = 2 AND st.start_time <= NOW() AND st.end_time >= NOW()
-- ORDER BY utp.last_updated_at DESC;

-- 查询可参与的任务列表
-- SELECT * FROM system_tasks 
-- WHERE status = 2 AND start_time <= NOW() AND end_time >= NOW()
-- AND (max_participants = 0 OR current_participants < max_participants)
-- AND id NOT IN (
--     SELECT task_id FROM user_task_participations 
--     WHERE user_id = ? AND child_id = ? AND cycle_period = ?
-- )
-- ORDER BY is_featured DESC, sort_order DESC, created_at DESC;

-- 查询任务完成统计
-- SELECT 
--     COUNT(*) as total_participants,
--     COUNT(CASE WHEN participation_status = 2 THEN 1 END) as completed_count,
--     AVG(progress_percentage) as avg_progress
-- FROM user_task_participations 
-- WHERE task_id = ?;

-- 查询用户任务完成历史
-- SELECT st.title, utp.completed_at, utp.reward_points, utp.consecutive_days
-- FROM system_tasks st
-- JOIN user_task_participations utp ON st.id = utp.task_id
-- WHERE utp.user_id = ? AND utp.child_id = ? AND utp.participation_status = 2
-- ORDER BY utp.completed_at DESC LIMIT 50;

-- 查询连续任务排行榜
-- SELECT u.nickname, c.nickname as child_name, utp.consecutive_days
-- FROM user_task_participations utp
-- JOIN users u ON utp.user_id = u.id
-- JOIN children c ON utp.child_id = c.id
-- WHERE utp.task_id = ? AND utp.participation_status IN (1,2)
-- AND c.show_in_leaderboard = 1 AND c.privacy_level <= 2
-- ORDER BY utp.consecutive_days DESC LIMIT 100;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 系统任务设计原则：
   - 支持多种任务类型：打卡、学习、社交、成长、活动
   - 灵活的完成条件：次数、连续天数、累计时长、分数等
   - 丰富的奖励机制：积分、虚拟奖励、额外奖励
   - 完整的任务生命周期管理

2. 任务参与机制：
   - 支持年龄限制和等级要求
   - 前置任务依赖关系
   - 参与人数限制和完成次数限制
   - 周期性任务的重复参与

3. 进度跟踪系统：
   - 实时进度更新和百分比计算
   - 复杂任务的详细进度数据存储
   - 里程碑节点记录
   - 连续性任务的特殊处理

4. 奖励发放机制：
   - 任务完成后自动发放奖励
   - 奖励发放状态跟踪
   - 失败重试机制
   - 额外奖励和连续奖励

5. 数据统计分析：
   - 任务参与度和完成率统计
   - 用户行为分析数据
   - 任务效果评估指标
   - 运营数据支持

6. 性能优化考虑：
   - 合理的索引设计支持各种查询
   - 冗余字段减少复杂计算
   - 分区策略应对大数据量
   - 定时任务处理过期数据

7. 数据一致性保证：
   - 外键约束保证数据完整性
   - 唯一约束防止重复参与
   - 事务处理保证奖励发放一致性
   - 定期数据校验和清理
*/
