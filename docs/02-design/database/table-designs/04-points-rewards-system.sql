-- =====================================================
-- 积分和虚拟奖励系统表设计
-- =====================================================
-- 模块: 积分和虚拟奖励系统 (Points and Virtual Rewards System)
-- 包含表: user_points, point_records, rewards, user_rewards
-- 功能: 积分管理、奖励发放、激励机制
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 孩子积分表 (child_points)
-- =====================================================
-- 用途: 记录每个孩子的积分余额和统计信息
-- 业务场景: 积分查询、排行榜生成、积分消费验证
-- 设计原则: 实时准确的积分余额，支持多维度统计
-- =====================================================

CREATE TABLE child_points (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，主键自增，全平台唯一标识',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识积分所属孩子档案',
    
    -- 积分余额
    current_points BIGINT UNSIGNED DEFAULT 0 COMMENT '当前积分余额，用户可用的积分总数',
    
    -- 积分统计
    total_earned BIGINT UNSIGNED DEFAULT 0 COMMENT '累计获得积分，历史获得的积分总和',
    total_spent BIGINT UNSIGNED DEFAULT 0 COMMENT '累计消费积分，历史消费的积分总和',
    
    -- 分类积分统计
    checkin_points BIGINT UNSIGNED DEFAULT 0 COMMENT '打卡获得积分，通过打卡活动获得的积分总数',
    task_points BIGINT UNSIGNED DEFAULT 0 COMMENT '任务获得积分，完成任务获得的积分总数',
    social_points BIGINT UNSIGNED DEFAULT 0 COMMENT '社交获得积分，社交互动获得的积分总数',
    bonus_points BIGINT UNSIGNED DEFAULT 0 COMMENT '奖励积分，额外奖励获得的积分总数',
    
    -- 等级信息
    current_level TINYINT UNSIGNED DEFAULT 1 COMMENT '当前等级，根据积分计算的用户等级 1-10级',
    level_progress INT UNSIGNED DEFAULT 0 COMMENT '等级进度，距离下一等级还需要的积分数',
    
    -- 连续记录
    continuous_days INT UNSIGNED DEFAULT 0 COMMENT '连续获得积分天数，用于连续激励',
    last_earn_date DATE DEFAULT '1900-01-01' COMMENT '最后获得积分日期，用于连续天数计算',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录积分账户建立时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录积分最后变动时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 孩子关联索引
    UNIQUE KEY uk_child_id (child_id) COMMENT '唯一约束：每个孩子只能有一条积分记录',
    
    -- 积分排行索引
    INDEX idx_current_points (current_points DESC) COMMENT '当前积分排行索引：积分排行榜时使用',
    INDEX idx_total_earned (total_earned DESC) COMMENT '累计积分排行索引：历史积分排行时使用',
    
    -- 等级索引
    INDEX idx_current_level (current_level) COMMENT '等级索引：按等级查询和统计时使用',
    
    -- 连续天数索引
    INDEX idx_continuous_days (continuous_days DESC) COMMENT '连续天数排行索引：连续激励排行时使用',
    
    -- 时间索引
    INDEX idx_last_earn_date (last_earn_date) COMMENT '最后获得积分日期索引：连续天数计算时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_child_points_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='孩子积分表：记录孩子积分余额和统计信息，支持多维度积分统计和等级计算';

-- =====================================================
-- 积分变动记录表 (point_records)
-- =====================================================
-- 用途: 记录所有积分变动的详细流水
-- 业务场景: 积分流水查询、数据审计、异常排查
-- 设计原则: 完整记录每笔积分变动，支持数据追溯
-- =====================================================

CREATE TABLE point_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，主键自增，全平台唯一标识',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识积分变动的孩子档案',
    
    -- 积分变动信息
    change_type TINYINT UNSIGNED DEFAULT 1 COMMENT '变动类型 1:获得 2:消费 3:过期 4:调整，标识积分变动原因',
    change_amount BIGINT DEFAULT 0 COMMENT '变动数量，正数表示增加，负数表示减少',
    balance_before BIGINT UNSIGNED DEFAULT 0 COMMENT '变动前余额，记录积分变动前的余额',
    balance_after BIGINT UNSIGNED DEFAULT 0 COMMENT '变动后余额，记录积分变动后的余额',
    
    -- 变动来源
    source_type TINYINT UNSIGNED DEFAULT 1 COMMENT '来源类型 1:打卡 2:任务 3:社交 4:奖励 5:消费 6:管理员调整',
    source_id BIGINT UNSIGNED DEFAULT 0 COMMENT '来源ID，关联具体的打卡记录、任务记录等',
    
    -- 描述信息
    title VARCHAR(100) DEFAULT '' COMMENT '变动标题，简短描述积分变动原因',
    description VARCHAR(500) DEFAULT '' COMMENT '详细描述，积分变动的具体说明',
    
    -- 过期信息
    expire_date DATE NULL COMMENT '过期日期，积分的有效期，NULL表示不过期',
    
    -- 管理信息
    admin_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '管理员ID，人工调整积分时记录操作管理员',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录积分变动发生的时间戳',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 孩子关联索引
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子积分流水时使用',
    
    -- 变动类型和来源索引
    INDEX idx_change_type (change_type) COMMENT '变动类型索引：按类型统计积分变动时使用',
    INDEX idx_source_type (source_type) COMMENT '来源类型索引：按来源统计积分获得时使用',
    INDEX idx_source (source_type, source_id) COMMENT '来源复合索引：查询特定来源的积分记录时使用',
    
    -- 时间范围索引
    INDEX idx_created_at (created_at) COMMENT '创建时间索引：按时间范围查询积分流水时使用',
    INDEX idx_expire_date (expire_date) COMMENT '过期日期索引：处理积分过期时使用',
    
    -- 管理员操作索引
    INDEX idx_admin_user (admin_user_id) COMMENT '管理员索引：查询管理员操作记录时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_point_records_child_id
        FOREIGN KEY (child_id) REFERENCES children(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='积分变动记录表：记录所有积分变动的详细流水，支持数据审计和异常排查';

-- =====================================================
-- 虚拟奖励表 (rewards)
-- =====================================================
-- 用途: 定义系统中的各种虚拟奖励
-- 业务场景: 奖励配置、奖励展示、兑换管理
-- 设计原则: 灵活的奖励配置，支持多种奖励类型
-- =====================================================

CREATE TABLE rewards (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '奖励ID，主键自增，全平台唯一标识',
    
    -- 奖励基本信息
    name VARCHAR(100) DEFAULT '' COMMENT '奖励名称，如"跳绳小达人徽章"、"连续打卡7天奖杯"',
    description TEXT COMMENT '奖励描述，详细说明奖励的含义和获得条件',
    icon VARCHAR(255) DEFAULT '' COMMENT '奖励图标URL，用于展示的图标图片地址',
    
    -- 奖励类型和等级
    reward_type TINYINT UNSIGNED DEFAULT 1 COMMENT '奖励类型 1:徽章 2:奖杯 3:称号 4:道具 5:特权，不同类型的虚拟奖励',
    reward_level TINYINT UNSIGNED DEFAULT 1 COMMENT '奖励等级 1-5级，表示奖励的珍贵程度',
    
    -- 获得条件
    condition_type TINYINT UNSIGNED DEFAULT 1 COMMENT '获得条件类型 1:积分达到 2:连续打卡 3:成绩达到 4:任务完成',
    condition_value INT UNSIGNED DEFAULT 0 COMMENT '条件数值，如积分数量、连续天数、成绩分数等',
    condition_description VARCHAR(200) DEFAULT '' COMMENT '条件描述，详细说明获得条件',
    
    -- 奖励价值
    point_value INT UNSIGNED DEFAULT 0 COMMENT '积分价值，该奖励相当于多少积分价值',
    rarity_score INT UNSIGNED DEFAULT 0 COMMENT '稀有度评分，用于奖励排序和展示',
    
    -- 奖励状态
    is_active TINYINT UNSIGNED DEFAULT 1 COMMENT '是否启用 0:禁用 1:启用，控制奖励是否可获得',
    is_limited TINYINT UNSIGNED DEFAULT 0 COMMENT '是否限量 0:不限量 1:限量，限量奖励更珍贵',
    total_limit INT UNSIGNED DEFAULT 0 COMMENT '总限量数，限量奖励的总发放数量',
    current_count INT UNSIGNED DEFAULT 0 COMMENT '当前已发放数量，已经发放的奖励数量',
    
    -- 时间限制
    start_date DATE DEFAULT '1900-01-01' COMMENT '开始日期，奖励开始发放的日期',
    end_date DATE DEFAULT '2099-12-31' COMMENT '结束日期，奖励停止发放的日期',
    
    -- 时间记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录奖励配置创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录奖励配置最后修改时间',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 奖励类型和等级索引
    INDEX idx_reward_type (reward_type) COMMENT '奖励类型索引：按类型查询奖励时使用',
    INDEX idx_reward_level (reward_level) COMMENT '奖励等级索引：按等级查询奖励时使用',
    INDEX idx_type_level (reward_type, reward_level) COMMENT '类型等级复合索引：分类分级查询时使用',
    
    -- 获得条件索引
    INDEX idx_condition_type (condition_type) COMMENT '条件类型索引：按条件类型查询奖励时使用',
    INDEX idx_condition_value (condition_value) COMMENT '条件数值索引：按条件数值排序时使用',
    
    -- 状态索引
    INDEX idx_is_active (is_active) COMMENT '启用状态索引：查询可用奖励时使用',
    INDEX idx_is_limited (is_limited) COMMENT '限量状态索引：查询限量奖励时使用',
    
    -- 稀有度索引
    INDEX idx_rarity_score (rarity_score DESC) COMMENT '稀有度排序索引：按稀有度排序奖励时使用',
    
    -- 时间范围索引
    INDEX idx_date_range (start_date, end_date) COMMENT '时间范围索引：查询有效期内奖励时使用'
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='虚拟奖励表：定义系统中的各种虚拟奖励，支持多种奖励类型和获得条件配置';

-- =====================================================
-- 用户奖励表 (user_rewards)
-- =====================================================
-- 用途: 记录用户获得的虚拟奖励
-- 业务场景: 奖励发放、奖励展示、成就系统
-- 设计原则: 记录奖励获得历史，支持奖励展示
-- =====================================================

CREATE TABLE user_rewards (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，主键自增，全平台唯一标识',
    user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id，标识获得奖励的用户',
    child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id，标识获得奖励的孩子档案',
    reward_id BIGINT UNSIGNED DEFAULT 0 COMMENT '奖励ID，关联rewards.id，标识获得的具体奖励',
    
    -- 获得信息
    source_type TINYINT UNSIGNED DEFAULT 1 COMMENT '获得来源类型 1:自动获得 2:管理员发放 3:活动奖励',
    source_id BIGINT UNSIGNED DEFAULT 0 COMMENT '来源ID，关联具体的打卡记录、任务记录等',
    source_description VARCHAR(200) DEFAULT '' COMMENT '获得原因描述，详细说明获得奖励的原因',
    
    -- 奖励状态
    is_displayed TINYINT UNSIGNED DEFAULT 1 COMMENT '是否展示 0:不展示 1:展示，用户可选择是否在个人资料中展示',
    display_order INT UNSIGNED DEFAULT 0 COMMENT '展示顺序，用户自定义的奖励展示顺序',
    
    -- 管理信息
    admin_user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '管理员ID，人工发放奖励时记录操作管理员',
    
    -- 时间记录
    obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间，记录用户获得奖励的时间戳',
    
    -- =====================================================
    -- 索引设计
    -- =====================================================
    
    -- 用户和孩子关联索引
    INDEX idx_user_id (user_id) COMMENT '用户ID索引：查询用户所有奖励时使用',
    INDEX idx_child_id (child_id) COMMENT '孩子ID索引：查询孩子所有奖励时使用',
    INDEX idx_user_child (user_id, child_id) COMMENT '用户孩子复合索引：查询特定用户特定孩子的奖励',
    
    -- 奖励关联索引
    INDEX idx_reward_id (reward_id) COMMENT '奖励ID索引：查询特定奖励的获得记录时使用',
    
    -- 展示相关索引
    INDEX idx_displayed (is_displayed) COMMENT '展示状态索引：查询展示的奖励时使用',
    INDEX idx_display_order (user_id, child_id, display_order) COMMENT '展示顺序索引：按用户自定义顺序展示奖励',
    
    -- 来源索引
    INDEX idx_source (source_type, source_id) COMMENT '来源复合索引：查询特定来源的奖励记录时使用',
    
    -- 时间索引
    INDEX idx_obtained_at (obtained_at) COMMENT '获得时间索引：按时间查询奖励记录时使用',
    
    -- 管理员操作索引
    INDEX idx_admin_user (admin_user_id) COMMENT '管理员索引：查询管理员发放记录时使用',
    
    -- =====================================================
    -- 外键约束
    -- =====================================================
    
    CONSTRAINT fk_user_rewards_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    CONSTRAINT fk_user_rewards_child_id 
        FOREIGN KEY (child_id) REFERENCES children(id) 
        ON DELETE CASCADE ON UPDATE CASCADE,
        
    CONSTRAINT fk_user_rewards_reward_id 
        FOREIGN KEY (reward_id) REFERENCES rewards(id) 
        ON DELETE CASCADE ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='用户奖励表：记录用户获得的虚拟奖励，支持奖励展示和成就系统';

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询用户当前积分余额
-- SELECT current_points, current_level FROM user_points 
-- WHERE user_id = ? AND child_id = ?;

-- 查询用户积分流水（分页）
-- SELECT * FROM point_records 
-- WHERE user_id = ? AND child_id = ? 
-- ORDER BY created_at DESC LIMIT 20 OFFSET 0;

-- 积分排行榜查询
-- SELECT up.*, c.nickname, c.avatar FROM user_points up
-- JOIN children c ON up.child_id = c.id
-- WHERE c.show_in_leaderboard = 1 AND c.privacy_level <= 2
-- ORDER BY up.current_points DESC LIMIT 100;

-- 查询用户获得的奖励
-- SELECT ur.*, r.name, r.icon, r.reward_type FROM user_rewards ur
-- JOIN rewards r ON ur.reward_id = r.id
-- WHERE ur.user_id = ? AND ur.child_id = ? AND ur.is_displayed = 1
-- ORDER BY ur.display_order, ur.obtained_at DESC;

-- 查询可获得的奖励
-- SELECT * FROM rewards 
-- WHERE is_active = 1 AND start_date <= CURDATE() AND end_date >= CURDATE()
-- AND (is_limited = 0 OR current_count < total_limit)
-- ORDER BY reward_level DESC, rarity_score DESC;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 积分系统规则：
   - 积分余额实时更新，确保数据准确性
   - 所有积分变动都有详细流水记录
   - 支持积分过期机制（可配置）
   - 积分等级自动计算，用于用户激励

2. 奖励系统规则：
   - 虚拟奖励包括徽章、奖杯、称号等多种类型
   - 支持自动发放和人工发放两种方式
   - 限量奖励增加稀有性和收集价值
   - 用户可自定义奖励展示顺序

3. 激励机制设计：
   - 多维度积分统计，了解用户行为偏好
   - 连续激励机制，鼓励用户持续参与
   - 等级系统提供长期目标
   - 稀有奖励增加用户粘性

4. 数据一致性保证：
   - 积分变动使用事务确保一致性
   - 冗余统计字段定期校验
   - 外键约束保证数据完整性
   - 详细的操作日志支持数据审计
*/
