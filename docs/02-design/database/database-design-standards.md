# 数据库设计规范

## 📋 文档信息
- **项目名称**：kids-platform 儿童跳绳学习平台
- **创建时间**：2025-07-05
- **版本**：v1.0
- **状态**：✅ 规范制定完成

## 🎯 设计原则

### 1. 核心原则
- **一致性**：命名规范、数据类型、约束规则保持一致
- **可扩展性**：预留扩展字段，支持业务发展
- **性能优化**：合理设计索引，优化查询性能
- **数据完整性**：确保数据准确性和一致性
- **安全性**：敏感数据加密，访问权限控制

### 2. 数据存储策略
```yaml
MySQL (95%业务数据):
  - 用户系统、积分系统、任务系统
  - 打卡系统、社交系统、内容系统
  - 排行榜系统、家庭共享系统

MongoDB (日志存储+统计分析):
  - 系统日志、用户行为日志
  - 审计日志、统计分析数据
  - 监控数据、性能指标

Redis (缓存和会话):
  - 用户会话、热点数据缓存
  - 排行榜缓存、分布式锁
```

## 📝 命名规范

### 1. 数据库命名
```sql
-- 数据库名称：项目名_环境
kids_platform_dev      -- 开发环境
kids_platform_test     -- 测试环境
kids_platform_prod     -- 生产环境
```

### 2. 表命名规范
```sql
-- 表名：小写字母+下划线，复数形式
users                   -- 用户表
children                -- 孩子档案表
checkin_records        -- 打卡记录表
family_members         -- 家庭成员表
system_tasks           -- 系统任务表

-- 关联表：主表_关联表
user_points            -- 用户积分表
album_videos           -- 专辑视频关联表
task_participations    -- 任务参与记录表
```

### 3. 字段命名规范
```sql
-- 主键：id (bigint, auto_increment)
id BIGINT PRIMARY KEY AUTO_INCREMENT

-- 外键：关联表名_id
user_id BIGINT          -- 关联用户表
child_id BIGINT         -- 关联孩子表
family_id BIGINT        -- 关联家庭表

-- 时间字段：统一后缀
created_at TIMESTAMP    -- 创建时间
updated_at TIMESTAMP    -- 更新时间
deleted_at TIMESTAMP    -- 软删除时间
started_at TIMESTAMP    -- 开始时间
ended_at TIMESTAMP      -- 结束时间

-- 状态字段：status 或 具体状态名
status TINYINT          -- 通用状态
audit_status TINYINT    -- 审核状态
task_status TINYINT     -- 任务状态

-- 计数字段：_count 后缀
view_count INT          -- 观看次数
like_count INT          -- 点赞次数
comment_count INT       -- 评论次数

-- 布尔字段：is_ 前缀
is_active TINYINT(1)    -- 是否激活
is_public TINYINT(1)    -- 是否公开
is_deleted TINYINT(1)   -- 是否删除
```

### 4. 索引命名规范
```sql
-- 主键索引：PRIMARY
PRIMARY KEY (id)

-- 唯一索引：uk_表名_字段名
UNIQUE KEY uk_users_openid (openid)
UNIQUE KEY uk_families_invite_code (invite_code)

-- 普通索引：idx_表名_字段名
KEY idx_checkin_records_user_id (user_id)
KEY idx_checkin_records_created_at (created_at)

-- 复合索引：idx_表名_字段1_字段2
KEY idx_task_participations_user_task (user_id, task_id)
KEY idx_checkin_records_user_date (user_id, checkin_date)
```

## 🗃️ 数据类型规范

### 1. 数值类型选择决策树
```sql
-- =====================================================
-- ID字段选择策略
-- =====================================================
-- 用户相关表：预期用户量 > 100万 → BIGINT UNSIGNED
id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT  -- users, children, checkin_records

-- 配置类表：记录数 < 10万 → INT UNSIGNED
id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT     -- system_configs, categories

-- 日志类表：高频写入，预期大量数据 → BIGINT UNSIGNED
id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT  -- operation_logs, access_logs

-- =====================================================
-- 计数字段选择策略
-- =====================================================
-- 观看次数：可能达到千万级 → INT UNSIGNED (42亿够用)
view_count INT UNSIGNED DEFAULT 0 COMMENT '观看次数'
like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞次数'

-- 积分总数：可能累积很大 → BIGINT UNSIGNED
total_points BIGINT UNSIGNED DEFAULT 0 COMMENT '总积分'
file_size BIGINT UNSIGNED DEFAULT 0 COMMENT '文件大小(字节)'

-- 状态枚举：选项 < 255个 → TINYINT UNSIGNED
status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态 1:正常 2:禁用'
difficulty TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1-5'
age TINYINT UNSIGNED DEFAULT 0 COMMENT '年龄'

-- =====================================================
-- 字符串长度建议
-- =====================================================
-- 用户昵称：VARCHAR(50)   -- 微信昵称最长64字符，预留空间
nickname VARCHAR(50) DEFAULT '' COMMENT '用户昵称'

-- 手机号：VARCHAR(20)     -- 国际号码格式
phone VARCHAR(20) DEFAULT '' COMMENT '手机号'

-- 邮箱：VARCHAR(100)      -- 常见邮箱长度
email VARCHAR(100) DEFAULT '' COMMENT '邮箱地址'

-- 标题：VARCHAR(200)      -- 适合大部分标题
title VARCHAR(200) DEFAULT '' COMMENT '标题'

-- URL：VARCHAR(500)       -- 适合大部分URL
video_url VARCHAR(500) DEFAULT '' COMMENT '视频URL'

-- 小数：DECIMAL (精确小数)
completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率 0.00-100.00'
score DECIMAL(5,2) DEFAULT 0.00 COMMENT '分数'

-- IP地址：INT UNSIGNED (节省空间，使用函数转换)
ip_address INT UNSIGNED DEFAULT 0 COMMENT 'IP地址(INET_ATON转换)'
```

### 2. 字符串类型（避免NULL，设置合理默认值）
```sql
-- 短字符串：VARCHAR (设置默认空字符串)
nickname VARCHAR(50) DEFAULT '' COMMENT '用户昵称'
phone VARCHAR(20) DEFAULT '' COMMENT '手机号'
invite_code VARCHAR(32) DEFAULT '' COMMENT '邀请码'
title VARCHAR(200) DEFAULT '' COMMENT '标题'

-- 长字符串：TEXT (MySQL中TEXT不能设置默认值，查询时用COALESCE处理)
description TEXT COMMENT '描述'
content TEXT COMMENT '内容'
-- 查询时：SELECT COALESCE(description, '') as description

-- 超长字符串：LONGTEXT
rich_content LONGTEXT COMMENT '富文本内容'

-- 固定长度：CHAR (微信ID等固定长度字符串)
openid CHAR(28) DEFAULT '' COMMENT '微信openid'
unionid CHAR(29) DEFAULT '' COMMENT '微信unionid'

-- 枚举替代：使用整数+注释，而非ENUM类型
-- ❌ 不推荐：task_type ENUM('system', 'team')
-- ✅ 推荐：task_type TINYINT UNSIGNED DEFAULT 1 COMMENT '任务类型 1:系统任务 2:组队任务'
```

### 3. 时间类型（平衡NULL值使用）
```sql
-- =====================================================
-- 时间字段设计策略
-- =====================================================

-- 必需时间字段：避免NULL，使用默认值
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

-- 软删除字段：使用NULL值表示未删除（推荐方案）
deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除'

-- 真正可选的时间字段：允许合理使用NULL
last_login_at TIMESTAMP NULL COMMENT '最后登录时间，NULL表示从未登录'
completed_at TIMESTAMP NULL COMMENT '完成时间，NULL表示未完成'

-- 业务日期字段：使用默认值
checkin_date DATE NOT NULL COMMENT '打卡日期'
birth_date DATE DEFAULT '1970-01-01' COMMENT '出生日期，1970-01-01表示未设置'

-- 时间范围字段
started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间'
ended_at TIMESTAMP NULL COMMENT '结束时间，NULL表示进行中'
```

### 4. JSON类型
```sql
-- 配置信息：JSON
settings JSON                       -- 用户设置
metadata JSON                       -- 元数据
permissions JSON                    -- 权限配置
tags JSON                          -- 标签数组
```

## ⚡ 性能优化最佳实践

### 1. 避免NULL值的核心原则
```sql
-- ❌ 问题：NULL值的问题
-- 1. 索引效率低：NULL值不会被包含在索引中
-- 2. 查询复杂：需要使用IS NULL/IS NOT NULL
-- 3. 存储开销：需要额外的空间标记NULL
-- 4. 三值逻辑：NULL的比较结果既不是TRUE也不是FALSE

-- ✅ 解决方案：使用有意义的默认值
nickname VARCHAR(50) DEFAULT '' COMMENT '昵称，空字符串表示未设置'
age TINYINT UNSIGNED DEFAULT 0 COMMENT '年龄，0表示未知'
last_login_at TIMESTAMP DEFAULT '1970-01-01 00:00:01' COMMENT '最后登录，特殊值表示从未登录'
```

### 2. 数据类型选择策略
```sql
-- 原则：够用就好，优先选择小数据类型

-- ID字段选择策略
user_id BIGINT UNSIGNED     -- 用户ID：预期大量增长
config_id INT UNSIGNED      -- 配置ID：数量有限
log_id BIGINT UNSIGNED      -- 日志ID：可能大量增长

-- 计数字段选择
view_count INT UNSIGNED DEFAULT 0      -- 观看次数：最大42亿够用
like_count INT UNSIGNED DEFAULT 0      -- 点赞次数：最大42亿够用
total_points BIGINT UNSIGNED DEFAULT 0 -- 总积分：可能累积很大

-- 状态枚举选择
status TINYINT UNSIGNED DEFAULT 1      -- 状态：0-255个状态够用
priority TINYINT UNSIGNED DEFAULT 5    -- 优先级：1-10级别
```

### 3. 单表字段控制
```sql
-- 原则：单表字段建议控制在20个以内
-- 超过20个字段考虑垂直分表

-- ✅ 主表：核心字段
CREATE TABLE users (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    openid CHAR(28) DEFAULT '' COMMENT '微信openid',
    nickname VARCHAR(50) DEFAULT '' COMMENT '昵称',
    avatar VARCHAR(255) DEFAULT '' COMMENT '头像URL',
    phone VARCHAR(20) DEFAULT '' COMMENT '手机号',
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    -- 总共8个核心字段
);

-- ✅ 扩展表：详细信息
CREATE TABLE user_profiles (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    real_name VARCHAR(50) DEFAULT '' COMMENT '真实姓名',
    gender TINYINT UNSIGNED DEFAULT 0 COMMENT '性别',
    birth_date DATE DEFAULT '1970-01-01' COMMENT '出生日期',
    province VARCHAR(50) DEFAULT '' COMMENT '省份',
    city VARCHAR(50) DEFAULT '' COMMENT '城市',
    -- 扩展信息字段...
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 4. IP地址存储优化
```sql
-- ✅ 使用INT UNSIGNED存储IPv4地址
CREATE TABLE access_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED DEFAULT 0,
    ip_address INT UNSIGNED DEFAULT 0 COMMENT 'IP地址(整型)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 转换函数使用
INSERT INTO access_logs (user_id, ip_address)
VALUES (123, INET_ATON('***********'));

SELECT user_id, INET_NTOA(ip_address) as ip
FROM access_logs;
```

## 🔧 表结构设计规范

### 1. 基础字段（遵循最佳实践）
```sql
-- 每个表必须包含的基础字段
CREATE TABLE example_table (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 业务字段示例（避免NULL，使用合适的数据类型）
    name VARCHAR(100) DEFAULT '' COMMENT '名称',
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
    sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重',

    -- 索引设计
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='示例表';
```

### 2. 软删除设计（推荐NULL值方案）
```sql
-- 软删除字段设计（推荐方案）
deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',
deleted_by BIGINT UNSIGNED NULL COMMENT '删除操作人ID，NULL表示未删除',

-- 软删除索引优化
INDEX idx_active_records (deleted_at, created_at) COMMENT '活跃记录查询优化',
INDEX idx_deleted_records (deleted_at DESC) COMMENT '已删除记录查询',

-- 查询示例
-- 查询未删除记录：WHERE deleted_at IS NULL
-- 查询已删除记录：WHERE deleted_at IS NOT NULL
-- 按删除时间排序：WHERE deleted_at IS NOT NULL ORDER BY deleted_at DESC
```

### 3. 状态字段设计（使用UNSIGNED扩大范围）
```sql
-- 状态字段使用 TINYINT UNSIGNED，范围0-255
status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态 1:正常 2:禁用 3:删除',
audit_status TINYINT UNSIGNED DEFAULT 1 COMMENT '审核状态 1:待审核 2:通过 3:拒绝',
task_status TINYINT UNSIGNED DEFAULT 1 COMMENT '任务状态 1:进行中 2:已完成 3:已取消',

-- 优先级、等级等字段
priority TINYINT UNSIGNED DEFAULT 5 COMMENT '优先级 1-10',
difficulty TINYINT UNSIGNED DEFAULT 1 COMMENT '难度等级 1-5',
user_level TINYINT UNSIGNED DEFAULT 1 COMMENT '用户等级 1-100'
```

### 4. 扩展字段设计（避免NULL，合理默认值）
```sql
-- 预留扩展字段
extra_data JSON COMMENT '扩展数据(JSON格式)',
remark VARCHAR(500) DEFAULT '' COMMENT '备注',
sort_order INT UNSIGNED DEFAULT 0 COMMENT '排序权重',
tags JSON COMMENT '标签数组',

-- 统计冗余字段（提升查询性能）
total_count INT UNSIGNED DEFAULT 0 COMMENT '总数统计',
hot_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '热度分数'
```

## 📊 索引设计规范

### 1. 索引设计决策矩阵

| 查询频率 | 数据量 | 索引建议 | 示例场景 |
|----------|--------|----------|----------|
| 高频 | 大量 | 必须建索引 | user_id, created_at |
| 高频 | 少量 | 可选索引 | status, type |
| 低频 | 大量 | 谨慎建索引 | description全文搜索 |
| 低频 | 少量 | 不建索引 | 配置表字段 |

### 2. 复合索引设计原则
```sql
-- =====================================================
-- 复合索引设计策略
-- =====================================================

-- 1. 区分度高的字段在前
-- ✅ 推荐：user_id区分度高，在前
KEY idx_user_status (user_id, status)

-- ❌ 不推荐：status区分度低，在前
KEY idx_status_user (status, user_id)

-- 2. 范围查询字段在后
-- ✅ 推荐：时间范围查询在后
KEY idx_user_created (user_id, created_at)

-- 3. 覆盖索引优化：包含SELECT字段避免回表
KEY idx_user_status_created (user_id, status, created_at)
-- 可以覆盖查询：SELECT status, created_at FROM table WHERE user_id = ?
```

### 3. 索引类型和使用场景
```sql
-- 主键索引：每个表必须有
id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT

-- 唯一索引：业务唯一性约束
UNIQUE KEY uk_users_openid (openid) COMMENT '微信openid唯一约束',
UNIQUE KEY uk_checkin_child_date (child_id, checkin_date) COMMENT '每日打卡唯一约束',

-- 普通索引：高频查询字段
INDEX idx_user_id (user_id) COMMENT '用户查询索引',
INDEX idx_created_at (created_at) COMMENT '时间范围查询索引',
INDEX idx_status (status) COMMENT '状态筛选索引'
```

## 🔒 约束和完整性

### 1. 外键约束
```sql
-- 重要关联关系使用外键约束
CONSTRAINT fk_checkin_records_user_id 
    FOREIGN KEY (user_id) REFERENCES users(id) 
    ON DELETE CASCADE ON UPDATE CASCADE,

CONSTRAINT fk_family_members_family_id 
    FOREIGN KEY (family_id) REFERENCES families(id) 
    ON DELETE CASCADE ON UPDATE CASCADE
```

### 2. 检查约束
```sql
-- 数值范围检查
CONSTRAINT chk_age CHECK (age >= 0 AND age <= 150),
CONSTRAINT chk_difficulty CHECK (difficulty >= 1 AND difficulty <= 5),
CONSTRAINT chk_completion_rate CHECK (completion_rate >= 0 AND completion_rate <= 1)
```

### 3. 默认值设置
```sql
-- 合理设置默认值
status TINYINT DEFAULT 1,
is_deleted TINYINT(1) DEFAULT 0,
view_count INT DEFAULT 0,
like_count INT DEFAULT 0,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```

## 🚀 性能优化规范

### 1. 表引擎选择
```sql
-- 事务表使用 InnoDB
ENGINE=InnoDB

-- 字符集使用 utf8mb4
DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

### 2. 分区策略
```sql
-- 大表按时间分区
CREATE TABLE checkin_records (
    -- 字段定义...
) ENGINE=InnoDB
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 读写分离准备
```sql
-- 查询优化字段
last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

-- 统计字段冗余存储
total_checkins INT DEFAULT 0 COMMENT '总打卡次数',
total_points BIGINT DEFAULT 0 COMMENT '总积分'
```

---

## 📋 注释规范（简化版）

### 1. 注释分层策略
```sql
-- =====================================================
-- 注释内容分层原则
-- =====================================================

-- 表级注释：业务用途 + 核心关系
COMMENT='用户孩子关联表：支持多用户管理同一孩子'

-- 字段注释：核心信息简洁明了
user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id',
status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态 1:正常 2:禁用 3:删除',

-- 索引注释：使用场景
INDEX idx_user_id (user_id) COMMENT '用户查询索引',
UNIQUE KEY uk_user_child (user_id, child_id) COMMENT '防重复约束',

-- 详细业务说明放在设计文档中，不在SQL中冗余
```

### 2. 字段注释标准格式
```sql
-- 关联字段：字段含义 + 关联关系
user_id BIGINT UNSIGNED DEFAULT 0 COMMENT '用户ID，关联users.id',
child_id BIGINT UNSIGNED DEFAULT 0 COMMENT '孩子ID，关联children.id',

-- 枚举字段：字段含义 + 枚举值
status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态 1:正常 2:禁用 3:删除',
difficulty TINYINT UNSIGNED DEFAULT 1 COMMENT '难度 1-5级',

-- 计数字段：字段含义 + 单位
duration INT UNSIGNED DEFAULT 0 COMMENT '时长（秒）',
file_size BIGINT UNSIGNED DEFAULT 0 COMMENT '文件大小（字节）',

-- 时间字段：字段含义 + NULL值说明
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除'
```

### 3. 索引注释简化格式
```sql
-- 单字段索引：查询用途
INDEX idx_user_id (user_id) COMMENT '用户查询',
INDEX idx_created_at (created_at) COMMENT '时间查询',

-- 复合索引：主要查询场景
INDEX idx_user_status (user_id, status) COMMENT '用户状态查询',
INDEX idx_child_date (child_id, checkin_date) COMMENT '孩子打卡查询',

-- 唯一索引：约束说明
UNIQUE KEY uk_openid (openid) COMMENT '微信ID唯一',
UNIQUE KEY uk_child_date (child_id, checkin_date) COMMENT '每日打卡唯一'
```

### 5. 完整表设计模板
```sql
-- =====================================================
-- 表名和用途说明
-- =====================================================
-- 表名: table_name
-- 用途: 表的业务用途和核心功能
-- 业务场景: 具体的使用场景描述
-- 设计原则: 设计时遵循的原则和考虑
-- 创建时间: YYYY-MM-DD
-- =====================================================

CREATE TABLE table_name (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID，自增长，唯一标识记录',
    field_name VARCHAR(50) DEFAULT '' COMMENT '字段的完整业务含义和使用说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，记录首次插入时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间，记录最后修改时间',
    UNIQUE KEY uk_field (field_name) COMMENT '唯一索引：业务唯一性约束说明',
    INDEX idx_field (field_name) COMMENT '普通索引：查询场景说明',
    CONSTRAINT fk_table_field
        FOREIGN KEY (field_id) REFERENCES other_table(id)
        ON DELETE CASCADE ON UPDATE CASCADE
        COMMENT '外键约束：关联关系和级联操作说明'

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='表的完整描述：用途、关系、业务规则';

-- =====================================================
-- 常用查询示例
-- =====================================================
-- 提供典型查询SQL和使用场景说明

-- =====================================================
-- 业务规则说明
-- =====================================================
-- 详细的业务逻辑、约束规则、扩展考虑
```

## �📋 设计检查清单

### 表设计检查
- [ ] 表名符合命名规范
- [ ] 包含基础字段 (id, created_at, updated_at)
- [ ] 字段类型选择合适
- [ ] ✅ 添加详细的表注释、字段注释、索引注释、约束注释
- [ ] 设置合理的默认值
- [ ] 提供常用查询示例
- [ ] 编写业务规则说明文档

### 索引检查
- [ ] 主键索引存在
- [ ] 外键字段有索引
- [ ] 高频查询字段有索引
- [ ] 复合索引顺序合理
- [ ] 避免过多索引影响写入性能

### 约束检查
- [ ] 重要关联关系有外键约束
- [ ] 数值字段有范围检查
- [ ] 唯一性约束正确设置
- [ ] 非空约束合理设置

## 🔐 安全设计规范

### 1. 敏感数据处理
```sql
-- 敏感字段加密存储
phone_encrypted VARCHAR(255) COMMENT '加密手机号',
id_card_encrypted VARCHAR(255) COMMENT '加密身份证号',

-- 脱敏字段用于显示
phone_masked VARCHAR(20) COMMENT '脱敏手机号 138****1234',
real_name_masked VARCHAR(50) COMMENT '脱敏真实姓名 张*明'
```

### 2. 访问控制
```sql
-- 数据权限字段
owner_id BIGINT COMMENT '数据所有者ID',
visibility TINYINT DEFAULT 1 COMMENT '可见性 1:公开 2:好友 3:私有',
access_level TINYINT DEFAULT 1 COMMENT '访问级别 1:普通 2:VIP 3:管理员'
```

### 3. 审计日志
```sql
-- 操作记录表
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '操作用户ID',
    operation_type VARCHAR(50) COMMENT '操作类型',
    table_name VARCHAR(50) COMMENT '操作表名',
    record_id BIGINT COMMENT '操作记录ID',
    old_values JSON COMMENT '修改前数据',
    new_values JSON COMMENT '修改后数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 数据统计规范

### 1. 统计表设计
```sql
-- 按日统计
CREATE TABLE daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE COMMENT '统计日期',
    metric_type VARCHAR(50) COMMENT '指标类型',
    metric_value BIGINT COMMENT '指标值',
    dimensions JSON COMMENT '维度信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_date_type (stat_date, metric_type)
);

-- 实时统计缓存表
CREATE TABLE realtime_counters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    counter_key VARCHAR(100) COMMENT '计数器键',
    counter_value BIGINT DEFAULT 0 COMMENT '计数值',
    expire_at TIMESTAMP COMMENT '过期时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_counter_key (counter_key)
);
```

### 2. 冗余统计字段
```sql
-- 在主表中冗余统计数据，提升查询性能
ALTER TABLE users ADD COLUMN (
    total_checkins INT DEFAULT 0 COMMENT '总打卡次数',
    total_points BIGINT DEFAULT 0 COMMENT '总积分',
    current_streak INT DEFAULT 0 COMMENT '当前连续打卡天数',
    max_streak INT DEFAULT 0 COMMENT '最大连续打卡天数',
    last_checkin_date DATE COMMENT '最后打卡日期'
);
```

## 🔄 数据迁移规范

### 1. 版本控制
```sql
-- 数据库版本表
CREATE TABLE schema_migrations (
    version VARCHAR(20) PRIMARY KEY COMMENT '版本号',
    description VARCHAR(255) COMMENT '变更描述',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间'
);

-- 插入版本记录
INSERT INTO schema_migrations (version, description)
VALUES ('20250105_001', '创建用户表和基础索引');
```

### 2. 迁移脚本规范
```sql
-- 迁移脚本模板
-- Migration: 20250105_002_add_family_sharing
-- Description: 添加家庭共享功能相关表

-- 前置检查
SELECT COUNT(*) FROM information_schema.tables
WHERE table_schema = 'kids_platform' AND table_name = 'families';

-- 执行变更
CREATE TABLE families (
    -- 表结构定义...
);

-- 后置验证
SELECT COUNT(*) FROM families;

-- 记录版本
INSERT INTO schema_migrations (version, description)
VALUES ('20250105_002', '添加家庭共享功能相关表');
```

### 3. 回滚策略
```sql
-- 每个迁移都要准备回滚脚本
-- Rollback: 20250105_002_add_family_sharing

-- 删除新增表
DROP TABLE IF EXISTS families;
DROP TABLE IF EXISTS family_members;

-- 删除版本记录
DELETE FROM schema_migrations WHERE version = '20250105_002';
```

## 🎯 业务规则约束分层策略

### 1. 约束分层原则

| 约束层级 | 适用场景 | 优势 | 示例 |
|----------|----------|------|------|
| **数据库层** | 数据完整性、简单业务规则 | 强制执行、性能好 | 外键约束、唯一约束 |
| **应用层** | 复杂业务逻辑、动态规则 | 灵活处理、易变更 | 积分计算、权限检查 |

### 2. 数据库层约束（强制执行）
```sql
-- 数据完整性约束
UNIQUE KEY uk_checkin_child_date (child_id, checkin_date) COMMENT '每日打卡唯一',
UNIQUE KEY uk_user_openid (openid) COMMENT '微信ID唯一',

-- 数据格式约束
CONSTRAINT chk_age CHECK (age >= 0 AND age <= 150) COMMENT '年龄范围检查',
CONSTRAINT chk_difficulty CHECK (difficulty >= 1 AND difficulty <= 5) COMMENT '难度等级检查',

-- 基础业务规则
CONSTRAINT chk_points CHECK (points >= 0) COMMENT '积分非负检查'
```

### 3. 应用层约束（灵活处理）
```sql
-- 复杂业务逻辑（在应用代码中实现）
-- 1. 积分计算规则：根据打卡时长、连续天数等动态计算
-- 2. 权限检查：用户是否有权限管理某个孩子
-- 3. 活动规则：训练营参与条件、优惠规则等
-- 4. 跨表业务约束：如用户等级与功能权限的关系

-- 示例：应用层检查逻辑
-- if (user.level < 3 && camp.difficulty > 2) {
--     throw new Error("用户等级不足，无法参与高难度训练营");
-- }
```

### 2. 数据一致性检查
```sql
-- 定期数据一致性检查脚本
-- 检查积分统计是否一致
SELECT u.id, u.total_points,
       COALESCE(SUM(p.points), 0) as calculated_points
FROM users u
LEFT JOIN user_points p ON u.id = p.user_id
GROUP BY u.id
HAVING u.total_points != calculated_points;

-- 检查打卡统计是否一致
SELECT u.id, u.total_checkins,
       COUNT(c.id) as calculated_checkins
FROM users u
LEFT JOIN checkin_records c ON u.id = c.user_id
GROUP BY u.id
HAVING u.total_checkins != calculated_checkins;
```

## 📊 监控和告警

### 1. 性能监控
```sql
-- 慢查询监控表
CREATE TABLE slow_query_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    query_time DECIMAL(10,6) COMMENT '查询时间(秒)',
    lock_time DECIMAL(10,6) COMMENT '锁等待时间(秒)',
    rows_sent INT COMMENT '返回行数',
    rows_examined INT COMMENT '扫描行数',
    sql_text TEXT COMMENT 'SQL语句',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 数据质量监控
```sql
-- 数据质量检查规则
CREATE TABLE data_quality_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) COMMENT '规则名称',
    table_name VARCHAR(50) COMMENT '检查表名',
    check_sql TEXT COMMENT '检查SQL',
    threshold_value INT COMMENT '阈值',
    alert_level TINYINT COMMENT '告警级别 1:警告 2:错误 3:严重',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入检查规则示例
INSERT INTO data_quality_rules (rule_name, table_name, check_sql, threshold_value, alert_level) VALUES
('用户表空昵称检查', 'users', 'SELECT COUNT(*) FROM users WHERE nickname IS NULL OR nickname = ""', 0, 2),
('打卡记录异常时长检查', 'checkin_records', 'SELECT COUNT(*) FROM checkin_records WHERE duration > 3600 OR duration < 0', 0, 1);
```

---

## 🔄 更新记录
- 2025-07-05：创建数据库设计规范文档，包含命名规范、数据类型、索引设计、安全规范、统计规范、迁移规范等
- 2025-07-05：融入性能优化最佳实践：避免NULL值、优先使用小数据类型、UNSIGNED扩大范围、整型存储IP、控制单表字段数量等
- 2024-01-08：优化规范实用性
  - 简化注释要求，提高开发效率
  - 平衡NULL值使用策略，允许合理使用NULL
  - 完善数据类型选择决策树
  - 优化索引设计策略，增加决策矩阵
  - 增加业务规则分层策略
- 待续：补充分库分表和读写分离详细规范
