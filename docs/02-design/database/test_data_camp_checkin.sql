-- =====================================================
-- 训练营打卡系统测试数据
-- =====================================================

-- 清理现有测试数据（可选）
-- DELETE FROM camp_checkin_dates WHERE participation_id IN (1, 2, 3);
-- DELETE FROM user_camp_participations WHERE id IN (1, 2, 3);

-- =====================================================
-- 1. 用户训练营参与记录测试数据
-- =====================================================

-- 参与记录1：21天跳绳挑战 - 进行中（第14天）
INSERT INTO user_camp_participations (
    id, camp_id, user_id, child_id, participation_status, participation_date,
    current_day, progress_percentage, total_checkins, consecutive_days,
    total_study_minutes, rating, review_text,
    -- 新增冗余字段
    camp_title, camp_subtitle, total_checkin_days, completed_checkin_days,
    makeup_used_count, makeup_total_count,
    created_at, updated_at
) VALUES (
    1, 1, 1, 1, 1, '2024-01-15',
    14, 66.67, 12, 10,
    420, 0, NULL,
    -- 冗余字段值
    '21天跳绳挑战', '从零基础到连续跳绳300个', 21, 12,
    1, 3,
    '2024-01-15 08:00:00', '2024-01-28 20:30:00'
);

-- 参与记录2：30天阅读计划 - 已完成
INSERT INTO user_camp_participations (
    id, camp_id, user_id, child_id, participation_status, participation_date,
    current_day, progress_percentage, total_checkins, consecutive_days,
    total_study_minutes, rating, review_text,
    -- 新增冗余字段
    camp_title, camp_subtitle, total_checkin_days, completed_checkin_days,
    makeup_used_count, makeup_total_count,
    created_at, updated_at
) VALUES (
    2, 2, 1, 1, 2, '2023-12-01',
    30, 100.00, 28, 25,
    900, 5, '非常棒的阅读训练营，孩子养成了良好的阅读习惯！',
    -- 冗余字段值
    '30天阅读计划', '培养孩子每日阅读好习惯', 30, 28,
    2, 3,
    '2023-12-01 08:00:00', '2023-12-30 22:15:00'
);

-- 参与记录3：14天数学思维 - 进行中（第7天）
INSERT INTO user_camp_participations (
    id, camp_id, user_id, child_id, participation_status, participation_date,
    current_day, progress_percentage, total_checkins, consecutive_days,
    total_study_minutes, rating, review_text,
    -- 新增冗余字段
    camp_title, camp_subtitle, total_checkin_days, completed_checkin_days,
    makeup_used_count, makeup_total_count,
    created_at, updated_at
) VALUES (
    3, 3, 2, 2, 1, '2024-01-22',
    7, 50.00, 6, 5,
    210, 0, NULL,
    -- 冗余字段值
    '14天数学思维训练', '提升逻辑思维和计算能力', 14, 6,
    0, 3,
    '2024-01-22 09:00:00', '2024-01-28 19:45:00'
);

-- =====================================================
-- 2. 训练营打卡日期测试数据
-- =====================================================

-- 参与记录1的打卡日期数据（21天跳绳挑战）
-- 第1-10天：已完成打卡
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 1, '2024-01-15', 1, 1, '2024-01-15 08:00:00', '2024-01-15 18:30:00'),
(1, 2, '2024-01-16', 1, 1, '2024-01-15 08:00:00', '2024-01-16 19:15:00'),
(1, 3, '2024-01-17', 1, 1, '2024-01-15 08:00:00', '2024-01-17 20:00:00'),
(1, 4, '2024-01-18', 1, 1, '2024-01-15 08:00:00', '2024-01-18 18:45:00'),
(1, 5, '2024-01-19', 1, 1, '2024-01-15 08:00:00', '2024-01-19 19:30:00'),
(1, 6, '2024-01-20', 1, 1, '2024-01-15 08:00:00', '2024-01-20 17:20:00'),
(1, 7, '2024-01-21', 1, 1, '2024-01-15 08:00:00', '2024-01-21 18:10:00'),
(1, 8, '2024-01-22', 1, 1, '2024-01-15 08:00:00', '2024-01-22 19:45:00'),
(1, 9, '2024-01-23', 1, 1, '2024-01-15 08:00:00', '2024-01-23 20:15:00'),
(1, 10, '2024-01-24', 1, 1, '2024-01-15 08:00:00', '2024-01-24 18:50:00');

-- 第11天：漏打卡，后来补卡
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 11, '2024-01-25', 2, 1, '2024-01-15 08:00:00', '2024-01-26 10:30:00');

-- 第12天：正常打卡
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 12, '2024-01-26', 1, 1, '2024-01-15 08:00:00', '2024-01-26 19:20:00');

-- 第13天：漏打卡（未补卡）
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 13, '2024-01-27', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00');

-- 第14天：今日待打卡
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 14, '2024-01-28', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00');

-- 第15-21天：未来日期
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(1, 15, '2024-01-29', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 16, '2024-01-30', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 17, '2024-01-31', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 18, '2024-02-01', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 19, '2024-02-02', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 20, '2024-02-03', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00'),
(1, 21, '2024-02-04', 0, 1, '2024-01-15 08:00:00', '2024-01-15 08:00:00');

-- 参与记录2的打卡日期数据（30天阅读计划 - 已完成）
-- 只插入部分关键数据作为示例
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(2, 1, '2023-12-01', 1, 1, '2023-12-01 08:00:00', '2023-12-01 20:00:00'),
(2, 2, '2023-12-02', 1, 1, '2023-12-01 08:00:00', '2023-12-02 19:30:00'),
(2, 3, '2023-12-03', 1, 1, '2023-12-01 08:00:00', '2023-12-03 18:45:00'),
-- 第15天：漏打卡，后补卡
(2, 15, '2023-12-15', 2, 1, '2023-12-01 08:00:00', '2023-12-16 10:15:00'),
-- 第22天：跳过（生病）
(2, 22, '2023-12-22', 3, 1, '2023-12-01 08:00:00', '2023-12-22 09:00:00'),
-- 第28天：补卡
(2, 28, '2023-12-28', 2, 1, '2023-12-01 08:00:00', '2023-12-29 11:30:00'),
(2, 29, '2023-12-29', 1, 1, '2023-12-01 08:00:00', '2023-12-29 19:20:00'),
(2, 30, '2023-12-30', 1, 1, '2023-12-01 08:00:00', '2023-12-30 20:45:00');

-- 参与记录3的打卡日期数据（14天数学思维 - 进行中）
INSERT INTO camp_checkin_dates (participation_id, day_number, checkin_date, status, date_type, created_at, updated_at) VALUES
(3, 1, '2024-01-22', 1, 1, '2024-01-22 09:00:00', '2024-01-22 20:30:00'),
(3, 2, '2024-01-23', 1, 1, '2024-01-22 09:00:00', '2024-01-23 19:15:00'),
(3, 3, '2024-01-24', 1, 1, '2024-01-22 09:00:00', '2024-01-24 18:45:00'),
(3, 4, '2024-01-25', 1, 1, '2024-01-22 09:00:00', '2024-01-25 20:00:00'),
(3, 5, '2024-01-26', 1, 1, '2024-01-22 09:00:00', '2024-01-26 19:30:00'),
(3, 6, '2024-01-27', 1, 1, '2024-01-22 09:00:00', '2024-01-27 18:20:00'),
-- 第7天：漏打卡
(3, 7, '2024-01-28', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
-- 第8-14天：未来日期
(3, 8, '2024-01-29', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 9, '2024-01-30', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 10, '2024-01-31', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 11, '2024-02-01', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 12, '2024-02-02', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 13, '2024-02-03', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00'),
(3, 14, '2024-02-04', 0, 1, '2024-01-22 09:00:00', '2024-01-22 09:00:00');

-- =====================================================
-- 3. 数据验证查询
-- =====================================================

-- 验证参与记录数据
-- SELECT * FROM user_camp_participations WHERE id IN (1, 2, 3);

-- 验证打卡日期数据
-- SELECT * FROM camp_checkin_dates WHERE participation_id IN (1, 2, 3) ORDER BY participation_id, day_number;

-- 验证统计数据一致性
-- SELECT 
--     p.id,
--     p.camp_title,
--     p.completed_checkin_days,
--     p.makeup_used_count,
--     COUNT(CASE WHEN c.status = 1 THEN 1 END) as actual_completed,
--     COUNT(CASE WHEN c.status = 2 THEN 1 END) as actual_makeup
-- FROM user_camp_participations p
-- LEFT JOIN camp_checkin_dates c ON p.id = c.participation_id
-- WHERE p.id IN (1, 2, 3)
-- GROUP BY p.id;
