# 视频内容和训练营系统设计执行文档

## 📋 文档概述

本文档是基于2025年1月26日讨论的视频内容管理和训练营系统的完整设计方案，包含字段命名规范、数据库设计策略、业务架构和实施计划。

## 🎯 核心决策

### 1. API版本策略
- **保持API版本为 `v1`**
- 不引入版本控制复杂性
- 直接在现有版本基础上优化

### 2. 需求以实际页面为准
- **以前端页面实际功能为设计依据**
- 忽略过时的文档需求
- 重点关注页面：
  - 首页训练营列表 (`/pages/home`)
  - 训练营详情页 (`/pages/camp-detail`)
  - 成长页面 (`/pages/growth`)
  - 打卡页面 (`/pages/checkin`)

### 3. 数据库设计原则
- **遵循数据库设计最佳实践**
- 规范化设计，避免数据冗余
- 合理使用索引，考虑查询性能
- 支持软删除，保证数据安全

## 🏗️ 字段命名统一规范

### 强制规则

#### 1. 数据库字段：snake_case（下划线命名）
```sql
CREATE TABLE training_camps (
    total_days INT UNSIGNED DEFAULT 21,
    current_day INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 前端数据字段：与数据库完全一致
```javascript
const campData = {
    total_days: 21,
    current_day: 14,
    created_at: '2025-01-01'
};
```

#### 3. 显示格式化：使用computed属性
```javascript
const displayData = {
    ...campData,
    get totalDaysDisplay() { return `${this.total_days}天` },
    get progressDisplay() { return `${this.current_day}/${this.total_days}` }
};
```

#### 4. API响应：不做字段转换，保持数据库字段
```javascript
// ❌ 禁止
{ totalDays: response.total_days }

// ✅ 正确
{ total_days: response.total_days }
```

### 执行原则
- **数据库字段为基准**：前端必须强制对应数据库字段
- **前端改动优先**：前端字段调整比数据库字段调整成本更低
- **统一性优先**：保持整个系统字段命名的一致性

## 📊 数据库表设计策略

### 设计流程
1. **完整表结构设计优先**：先设计所有核心表，避免后期重复修改
2. **基于实际页面功能**：以前端页面实际需求为准，不参考过时文档
3. **整合现有设计**：保留table-designs/02-08文件，整合到新架构中
4. **字段映射确认**：确保每个数据库字段都有对应的前端使用场景

### 核心表结构规划

#### 基础表（已有，微调字段命名）
- `users` - 用户表
- `children` - 孩子表  
- `user_children` - 用户孩子关联表

#### 内容表（新增，支持视频集合架构）
- `video_categories` - 视频分类表
- `videos` - 视频表（添加subtitle字段）
- `video_collections` - 视频集合表（添加subtitle字段）
- `collection_videos` - 集合视频关联表

#### 训练营表（基于09-training-camps-simple.sql + 视频集合）
- `training_camps` - 训练营表（添加subtitle字段）
- `camp_collections` - 训练营集合关联表

#### 业务表（整合02-08文件设计）
- `user_camp_participations` - 用户训练营参与表
- `checkins` - 打卡记录表（基于02-checkin-system.sql）
- `points_records` - 积分记录表（基于04-points-rewards-system.sql）
- `badges` - 勋章表
- `user_badges` - 用户勋章关联表
- `family_contracts` - 家庭荣誉契约表
- `likes` - 点赞表（基于03-social-system.sql）
- `comments` - 评论表（基于03-social-system.sql）

### 现有文件处理策略
- **保留并整合**：02、03、04、05（核心业务功能）
- **重新设计**：06（整合到新的视频集合架构）
- **评估后决定**：07、08（看是否与训练营功能重复）

## 🎯 核心业务架构

### 视频集合三层架构
```
videos (单个视频)
  ↓
video_collections (视频集合)
  ↓  
training_camps (训练营)
```

### 付费模式设计
- **视频集合级别**：前N集免费，后续付费
- **训练营级别**：可包含免费和付费集合
- **灵活配置**：支持完全免费、部分免费、完全付费

### 用户业务流程
```
用户注册 → 选择孩子 → 浏览训练营 → 参加训练营 → 观看视频 → 完成打卡 → 获得积分/勋章 → 家庭荣誉契约
```

## 🔧 执行检查机制

### 自动检查工具
```javascript
// 字段命名检查器
const fieldChecker = {
    checkNaming(data, source) {
        const issues = [];
        Object.keys(data).forEach(key => {
            if (!/^[a-z][a-z0-9_]*$/.test(key)) {
                issues.push(`${source}: ${key} 不符合snake_case规范`);
            }
        });
        return issues;
    }
};
```

### 检查清单
- [ ] 新增字段使用下划线命名
- [ ] API响应字段与数据库一致
- [ ] 前端数据绑定使用正确字段名
- [ ] 显示格式化使用computed属性
- [ ] 组件props遵循命名规范

### 检查时机
- **开发时**：手动运行检查工具
- **代码审查时**：AI自动检查
- **新增API时**：强制检查

## 📋 实施优先级

### P0 - 立即执行
1. **字段命名统一**：修改现有不一致字段
2. **完整数据库设计**：基于实际页面需求设计所有表
3. **建立检查机制**：防止后续开发违反规范

### P1 - 分模块实施
1. **视频内容管理**：实现视频和视频集合功能
2. **训练营系统**：实现训练营管理和用户参与
3. **打卡系统**：实现完整的打卡流程
4. **积分勋章**：实现奖励机制
5. **家庭荣誉契约**：实现核心差异化功能

## 🎯 关键原则

1. **数据库字段为基准**：前端必须强制对应数据库字段
2. **实际页面为准**：忽略过时文档，以前端实际功能为设计依据
3. **完整设计优先**：先完成所有表结构设计，再分模块实施
4. **规范强制执行**：建立检查机制，防止规范执行遗忘
5. **最佳实践遵循**：数据库设计遵循行业最佳实践

## 📝 后续执行

基于本文档，后续执行步骤：

1. **新开聊天窗口**
2. **按照本文档和AI记忆执行具体实施**
3. **优先级顺序**：字段统一 → 数据库设计 → 分模块开发

---

**文档创建时间**：2025年1月26日  
**最后更新时间**：2025年1月26日  
**文档状态**：执行中
