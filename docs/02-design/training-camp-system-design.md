# 训练营系统设计文档

## 📋 项目概述

### 业务背景
- **项目性质**：儿童多品类学习平台的训练营功能模块
- **目标用户**：27-40岁家长，孩子为K-6年级（3-12岁）
- **商业模式**：免费引流 + 付费进阶的训练营体系
- **当前规模**：2-3个训练营，不频繁变化

### 设计理念
- **极简优先**：避免过度设计，满足当前需求
- **数据驱动**：页面完全由数据库数据驱动渲染
- **渐进增强**：需要时再扩展，支持未来发展
- **用户体验**：保持原有简洁风格，重点突出

## 🗄️ 数据库设计

### 核心表结构

#### 1. training_camps（训练营主表）
```sql
-- 存储训练营的基本信息和配置，支持页面动态渲染
CREATE TABLE training_camps (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    
    -- 页面展示基本信息
    title VARCHAR(200) NOT NULL DEFAULT '',           -- 训练营标题
    subtitle VARCHAR(300) NOT NULL DEFAULT '',        -- 训练营副标题
    hero_number VARCHAR(50) NOT NULL DEFAULT '',      -- 核心数字展示
    hero_text VARCHAR(100) NOT NULL DEFAULT '',       -- 核心文案展示
    pain_solution VARCHAR(200) NOT NULL DEFAULT '',   -- 解决痛点描述
    
    -- 训练配置信息
    duration_days INT UNSIGNED NOT NULL DEFAULT 21,   -- 训练营总天数
    daily_minutes INT UNSIGNED NOT NULL DEFAULT 15,   -- 每日训练时长
    difficulty_level TINYINT UNSIGNED NOT NULL DEFAULT 1, -- 难度等级
    age_group VARCHAR(20) NOT NULL DEFAULT '',        -- 适用年龄段
    
    -- 商业信息
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,        -- 价格
    original_price DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 原价
    is_free TINYINT UNSIGNED NOT NULL DEFAULT 1,      -- 是否免费
    
    -- 视频关联
    video_collection_id BIGINT UNSIGNED NOT NULL DEFAULT 0, -- 视频集合ID
    featured_video_id BIGINT UNSIGNED NOT NULL DEFAULT 0,   -- 重点视频ID
    
    -- JSON配置数据
    key_benefits JSON,        -- 核心收益点
    promises JSON,           -- 服务承诺
    tags JSON,              -- 训练营标签
    
    -- 社交功能配置
    share_title VARCHAR(200) NOT NULL DEFAULT '',     -- 分享标题
    share_desc VARCHAR(300) NOT NULL DEFAULT '',      -- 分享描述
    wechat_helper VARCHAR(100) NOT NULL DEFAULT '',   -- 微信助手号
    wechat_group_benefits JSON,                       -- 加群收益描述
    
    -- 统计信息
    total_participants INT UNSIGNED NOT NULL DEFAULT 0, -- 总参与人数
    average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00,  -- 平均评分
    total_reviews INT UNSIGNED NOT NULL DEFAULT 0,      -- 评价总数
    
    -- 状态管理
    status TINYINT UNSIGNED NOT NULL DEFAULT 1,         -- 训练营状态
    is_featured TINYINT UNSIGNED NOT NULL DEFAULT 0,    -- 是否推荐
    is_coming_soon TINYINT UNSIGNED NOT NULL DEFAULT 0, -- 是否即将推出
    sort_order INT UNSIGNED NOT NULL DEFAULT 0,         -- 排序权重
    
    -- 时间记录
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### 2. user_camp_participations（用户参与记录表）
```sql
-- 记录用户参与训练营的状态和进度
CREATE TABLE user_camp_participations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0,       -- 训练营ID
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0,       -- 用户ID
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0,      -- 孩子ID
    
    -- 参与信息
    participation_date DATE NOT NULL,                  -- 参与日期
    start_date DATE NULL,                             -- 实际开始日期
    expected_end_date DATE NULL,                      -- 预期结束日期
    
    -- 进度信息
    current_day INT UNSIGNED NOT NULL DEFAULT 1,      -- 当前训练天数
    completed_videos INT UNSIGNED NOT NULL DEFAULT 0, -- 已完成视频数
    progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00, -- 完成进度
    
    -- 状态管理
    participation_status TINYINT UNSIGNED NOT NULL DEFAULT 1, -- 参与状态
    
    -- 支付信息
    payment_status TINYINT UNSIGNED NOT NULL DEFAULT 0, -- 支付状态
    payment_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00, -- 支付金额
    
    -- 统计信息
    total_checkins INT UNSIGNED NOT NULL DEFAULT 0,     -- 总打卡次数
    total_study_minutes INT UNSIGNED NOT NULL DEFAULT 0, -- 总学习时长
    
    -- 评价信息
    rating TINYINT UNSIGNED NOT NULL DEFAULT 0,         -- 用户评分
    review_text TEXT,                                   -- 评价内容
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. video_collections（视频集合表）
```sql
-- 管理视频的集合，支持训练营关联和视频复用
CREATE TABLE video_collections (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL DEFAULT '',            -- 集合名称
    description TEXT,                                 -- 集合描述
    collection_type TINYINT UNSIGNED NOT NULL DEFAULT 1, -- 集合类型
    total_videos INT UNSIGNED NOT NULL DEFAULT 0,     -- 视频总数
    status TINYINT UNSIGNED NOT NULL DEFAULT 1,       -- 集合状态
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 训练营类型设计

#### 当前训练营类型
1. **免费基础训练营**
   - 21天跳绳养成计划（0元）
   - 目标：用户引流，建立习惯

2. **付费进阶训练营**
   - 暑假跳绳追高计划（99元）
   - 目标：商业变现，高价值服务

#### 未来规划训练营
3. **高级挑战训练营**
   - 冲刺200/分钟挑战营（199元）
   - 目标：专业用户，极限挑战

4. **创意培养训练营**
   - 花样跳绳艺术营（149元）
   - 目标：艺术培养，差异化服务

5. **家庭互动训练营**
   - 亲子跳绳互动营（79元）
   - 目标：家庭市场，关系增进

## 📱 前端页面设计

### 页面结构（极简版）
```
┌─────────────────────────────────────┐
│ 1. 顶部标题区域                      │
│    - title, subtitle                │
│    - hero_number, hero_text         │
├─────────────────────────────────────┤
│ 2. 核心价值区域                      │
│    - pain_solution                  │
│    - key_benefits (JSON)            │
├─────────────────────────────────────┤
│ 3. 关键视频区域                      │
│    - featured_video                 │
│    - video_collection               │
├─────────────────────────────────────┤
│ 4. 操作说明区域                      │
│    - promises (JSON)                │
├─────────────────────────────────────┤
│ 5. 社交功能区域 🆕                   │
│    - 分享好友按钮                    │
│    - 加入微信群按钮                  │
├─────────────────────────────────────┤
│ 6. 行动按钮区域                      │
│    - 主要CTA按钮                    │
└─────────────────────────────────────┘
```

### 社交功能设计
#### 分享功能
- **触发方式**：点击"分享好友"按钮
- **实现方式**：微信原生分享（open-type="share"）
- **配置数据**：share_title, share_desc, share_image

#### 微信群功能
- **触发方式**：点击"加入专属训练交流群"按钮
- **实现方式**：弹窗展示微信号和加群收益，一键复制
- **配置数据**：wechat_helper, wechat_group_benefits

### 数据驱动渲染
```javascript
// API响应数据结构
{
  "campInfo": {
    "id": 1,
    "title": "21天跳绳养成计划",
    "subtitle": "让孩子从不会跳到连续100个",
    "heroNumber": "0 → 100个",
    "heroText": "21天技能飞跃",
    "painSolution": "解决不会教、没方法、没效果的困扰",
    "keyBenefits": [
      {"icon": "🎯", "text": "掌握跳绳技能"},
      {"icon": "💪", "text": "养成运动习惯"}
    ],
    "promises": [
      {"text": "完全免费"},
      {"text": "随时可退"}
    ],
    "shareTitle": "21天跳绳养成计划 - 专业指导，21天见效果",
    "wechatHelper": "jump_helper_2024",
    "wechatGroupBenefits": [
      "个性化训练计划",
      "1000+家长交流经验"
    ]
  },
  "videoCollection": {
    "id": 1,
    "name": "21天跳绳基础教程",
    "videos": [...]
  }
}
```

## 🚀 技术实现

### 后端API设计
```
GET /api/v1/camps/{campId}/detail
- 获取训练营详情和视频集合数据
- 支持个性化推荐

POST /api/v1/camps/{campId}/join
- 用户参与训练营
- 支持免费和付费流程

GET /api/v1/camps/list
- 获取训练营列表
- 支持状态筛选和排序
```

### 前端实现要点
1. **数据驱动渲染**：页面完全由API数据驱动
2. **JSON配置解析**：前端解析JSON配置数据
3. **社交功能集成**：微信分享和群功能
4. **状态管理**：用户参与状态跟踪

## 📈 扩展规划

### 短期扩展（3-6个月）
- 增加用户参与流程API
- 完善数据统计和分析
- 优化社交功能转化效果

### 中期扩展（6-12个月）
- 支持更多训练营类型
- 增加页面模板配置
- 实现A/B测试功能

### 长期扩展（1年以上）
- 个性化推荐算法
- 社群运营工具
- 多媒体内容支持

## 💡 设计优势

1. **满足当前需求**：完美支持2-3个训练营的差异化展示
2. **开发成本低**：基于现有页面结构，最小改动
3. **易于维护**：数据驱动，配置简单
4. **预留扩展性**：JSON字段和渐进式设计支持未来扩展
5. **用户体验好**：保持原有简洁风格，社交功能实用

这个设计方案既解决了当前的业务需求，又为未来的发展预留了充足的扩展空间，是一个平衡实用性和扩展性的优秀方案。
