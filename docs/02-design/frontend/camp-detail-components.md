# 训练营详细页面设计 - 极简版

## 📋 设计原则

1. **极简优先**：保持页面简洁，避免过度设计
2. **数据驱动**：页面内容完全由数据库数据驱动渲染
3. **渐进增强**：满足当前需求，需要时再扩展
4. **用户体验**：重点突出，交互简洁明了

## 🎯 设计理念调整

**从复杂组件化 → 极简数据驱动**
- 原方案：复杂的组件化架构，支持多种模板和A/B测试
- 新方案：极简设计，数据驱动，保持原有页面风格
- 调整原因：当前只有2-3个训练营，不需要过度设计

## 📱 页面结构设计

### 当前页面结构（极简版）
```
┌─────────────────────────────────────┐
│ 1. 顶部标题区域 (header-section)      │
│    - 训练营标题、副标题               │
│    - 核心数字展示、核心文案           │
├─────────────────────────────────────┤
│ 2. 核心价值区域 (core-value-section)  │
│    - 解决痛点描述                    │
│    - 核心收益点列表                  │
├─────────────────────────────────────┤
│ 3. 关键视频区域 (video-section)       │
│    - 重点推荐视频                    │
│    - 视频列表展示                    │
├─────────────────────────────────────┤
│ 4. 操作说明区域 (operation-section)   │
│    - 简化操作步骤                    │
│    - 服务承诺                       │
├─────────────────────────────────────┤
│ 5. 社交功能区域 (social-buttons) 🆕   │
│    - 分享好友按钮                    │
│    - 加入微信群按钮                  │
├─────────────────────────────────────┤
│ 6. 行动按钮区域 (action-section)      │
│    - 主要行动按钮                    │
│    - 紧急感营造                     │
└─────────────────────────────────────┘
```

### 核心数据结构
```javascript
// 训练营页面数据结构
{
  // 基本信息
  "title": "21天跳绳养成计划",
  "subtitle": "让孩子从不会跳到连续100个",
  "heroNumber": "0 → 100个",
  "heroText": "21天技能飞跃",
  "painSolution": "解决不会教、没方法、没效果的困扰",

  // 配置数据（JSON字段）
  "keyBenefits": [
    {"icon": "🎯", "text": "掌握跳绳技能"},
    {"icon": "💪", "text": "养成运动习惯"}
  ],
  "promises": [
    {"text": "完全免费"},
    {"text": "随时可退"},
    {"text": "专业指导"}
  ],
  "tags": ["保姆级教程", "社群交流", "零基础"],

  // 社交功能配置
  "shareTitle": "21天跳绳养成计划 - 专业指导，21天见效果",
  "shareDesc": "让孩子从不会跳到连续100个，专业教练指导",
  "wechatHelper": "jump_helper_2024",
  "wechatGroupBenefits": [
    "个性化训练计划",
    "1000+家长交流经验",
    "每日训练提醒",
    "专业进度分析"
  ],

  // 视频数据
  "videoCollection": {
    "id": 1,
    "name": "21天跳绳基础教程",
    "videos": [...]
  },
  "featuredVideo": {
    "id": 1,
    "title": "跳绳基础到进阶完整教程"
  }
}
```

## 📱 页面模板类型

### 1. 标准模板 (Standard Template)
**适用**：大部分训练营
**布局**：Header(25%) + Value(30%) + Content(25%) + Action(20%)

### 2. 营销模板 (Marketing Template)  
**适用**：付费训练营、限时活动
**布局**：Header(20%) + Value(35%) + SocialProof(25%) + Action(20%)

### 3. 简约模板 (Minimal Template)
**适用**：免费训练营、新用户引导
**布局**：Header(30%) + Content(40%) + Action(30%)

### 4. 社交模板 (Social Template)
**适用**：团队训练营、社群活动
**布局**：Header(20%) + Value(25%) + SocialProof(35%) + Action(20%)

## 🔧 技术实现

### 1. 数据结构
```javascript
// 训练营详情页面数据结构
{
  "campInfo": {
    "id": 1,
    "title": "21天跳绳养成计划",
    "type": "basic", // basic|advanced|premium
    "difficulty": 1, // 1-5
    "price": 0,
    "status": "active"
  },
  "pageConfig": {
    "template": "standard",
    "version": "v1.0",
    "components": {
      "header": {...},
      "value": {...},
      "content": {...},
      "action": {...},
      "socialProof": {...}
    }
  },
  "userContext": {
    "hasChild": true,
    "childAge": 8,
    "isLoggedIn": true,
    "hasParticipated": false
  }
}
```

### 2. 组件注册
```javascript
// 在页面中注册组件
{
  "usingComponents": {
    "camp-header": "/components/camp/camp-header/camp-header",
    "camp-value": "/components/camp/camp-value/camp-value",
    "camp-content": "/components/camp/camp-content/camp-content",
    "camp-action": "/components/camp/camp-action/camp-action",
    "camp-social-proof": "/components/camp/camp-social-proof/camp-social-proof"
  }
}
```

### 3. 页面逻辑
```javascript
Page({
  data: {
    campData: null,
    pageConfig: null,
    loading: true
  },

  onLoad(options) {
    const campId = options.id;
    this.loadCampDetail(campId);
  },

  async loadCampDetail(campId) {
    try {
      // 获取训练营详情和页面配置
      const response = await campAPI.getCampDetail(campId);
      
      this.setData({
        campData: response.campInfo,
        pageConfig: response.pageConfig,
        loading: false
      });
    } catch (error) {
      console.error('加载训练营详情失败:', error);
      this.showError();
    }
  },

  // 参与训练营
  async onJoinCamp() {
    const { campData } = this.data;
    
    // 根据训练营类型执行不同逻辑
    if (campData.price > 0) {
      // 付费训练营：跳转支付
      this.goToPayment();
    } else {
      // 免费训练营：直接参与
      await this.joinFreeCamp();
    }
  }
});
```

## 🎯 个性化推荐

### 1. 基于用户画像
- **新用户**：推荐免费基础训练营
- **活跃用户**：推荐进阶训练营
- **付费用户**：推荐高价值专业训练营

### 2. 基于孩子信息
- **年龄适配**：根据孩子年龄推荐合适难度
- **能力评估**：根据历史表现推荐进阶内容
- **兴趣偏好**：根据参与历史推荐相关训练营

### 3. 基于行为数据
- **浏览行为**：记录用户关注的训练营类型
- **参与历史**：分析完成率和满意度
- **社交互动**：考虑用户的社交偏好

## 📊 A/B测试支持

### 1. 测试维度
- **页面布局**：不同模板的转化效果
- **文案优化**：不同价值主张的吸引力
- **按钮设计**：不同按钮样式的点击率
- **价格策略**：不同定价的接受度

### 2. 测试指标
- **浏览转化率**：页面浏览到参与的转化
- **完成率**：参与到完成的转化
- **满意度**：用户评价和推荐度
- **复购率**：用户参与多个训练营的比例

这种组件化设计可以：
1. **提高开发效率**：组件复用，减少重复开发
2. **支持快速迭代**：通过配置调整页面，无需修改代码
3. **数据驱动优化**：基于数据调整页面配置
4. **个性化体验**：根据用户特征展示不同内容
