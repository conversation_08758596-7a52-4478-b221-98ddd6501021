# API 接口文档

## 概述

本目录包含儿童多品类学习平台的完整API接口规范，采用OpenAPI 3.0标准编写。这份API文档作为项目的**"单一事实来源（Single Source of Truth）"**，用于：

- 前后端开发的接口约定
- 自动生成客户端代码
- 自动生成服务端路由骨架
- 自动生成测试用例
- API文档展示和交互测试

## 文档结构

```
docs/02-design/api/
├── openapi.yaml                 # 主API规范文件
├── README.md                   # 本文档
├── components/                 # OpenAPI可复用组件
│   ├── schemas/               # 数据模型定义
│   ├── parameters/            # 通用参数定义
│   ├── responses/             # 标准响应模板
│   └── examples/              # 示例数据
├── modules/                   # 按功能模块分组的API规范
│   ├── auth.yaml             # 认证授权API
│   ├── users.yaml            # 用户管理API
│   ├── checkins.yaml         # 打卡系统API
│   ├── social.yaml           # 社交互动API
│   ├── content.yaml          # 内容管理API
│   ├── tasks.yaml            # 任务系统API
│   └── leaderboard.yaml      # 排行榜API
└── tools/                    # 工具和脚本
    ├── generate.sh           # 代码生成脚本
    └── validate.sh           # 规范验证脚本
```

## API设计原则

### 1. RESTful设计
- 使用标准HTTP方法：GET、POST、PUT、DELETE
- 资源导向的URL设计
- 合理的HTTP状态码使用
- 统一的错误响应格式

### 2. 版本管理
- API版本通过URL路径管理：`/api/v1/`
- 向后兼容原则
- 废弃API的明确迁移路径

### 3. 安全性
- JWT Token认证
- 接口权限控制
- 敏感数据加密传输
- 请求频率限制

### 4. 数据格式
- 统一使用JSON格式
- 标准化的请求/响应结构
- 详细的字段验证规则
- 国际化支持

## 核心功能模块

### P0 核心功能
1. **认证授权** (`auth.yaml`)
   - 用户注册、登录、登出
   - Token刷新和验证
   - 微信小程序授权

2. **用户管理** (`users.yaml`)
   - 用户信息管理
   - 孩子档案管理
   - 用户孩子关联管理

3. **打卡系统** (`checkins.yaml`)
   - 创建打卡记录
   - 查询打卡历史
   - 媒体文件上传

4. **社交互动** (`social.yaml`)
   - 点赞功能
   - 评论系统
   - 社区互动

5. **内容管理** (`content.yaml`)
   - 视频内容浏览
   - 播放列表管理
   - 个性化推荐

6. **任务系统** (`tasks.yaml`)
   - 系统任务参与
   - 团队任务管理
   - 任务进度跟踪

7. **排行榜** (`leaderboard.yaml`)
   - 各类排行榜查询
   - 排名历史记录

## 使用指南

### 1. 查看API文档
```bash
# 使用Swagger UI查看文档
npx swagger-ui-serve openapi.yaml

# 或使用在线编辑器
# 访问 https://editor.swagger.io/ 并导入 openapi.yaml
```

### 2. 验证API规范
```bash
# 验证OpenAPI规范格式
./tools/validate.sh

# 或使用swagger-codegen验证
swagger-codegen validate -i openapi.yaml
```

### 3. 生成代码
```bash
# 生成Go服务端代码
./tools/generate.sh server go

# 生成TypeScript客户端代码
./tools/generate.sh client typescript

# 生成测试用例
./tools/generate.sh tests
```

## 开发流程

### API-First开发流程
1. **设计阶段**: 在 `modules/` 中编写API规范
2. **评审阶段**: 团队评审API设计
3. **生成阶段**: 自动生成代码骨架
4. **实现阶段**: 实现业务逻辑
5. **测试阶段**: 基于规范进行测试
6. **部署阶段**: 发布API和文档

### 变更管理流程
1. **需求分析**: 确定API变更需求
2. **影响评估**: 评估对现有系统的影响
3. **规范更新**: 更新OpenAPI规范文件
4. **代码评审**: 进行技术评审
5. **版本发布**: 按语义化版本发布
6. **文档同步**: 更新相关文档

## 工具和集成

### 自动化工具链
项目提供了完整的自动化工具链，位于 `tools/` 目录：

#### 1. 验证工具 (`tools/validate.sh`)
验证API规范的语法和结构正确性：

```bash
# 验证API规范
./tools/validate.sh --validate

# 检查依赖工具
./tools/validate.sh --check

# 启动文档预览服务器
./tools/validate.sh --preview

# 执行完整验证和生成
./tools/validate.sh --all
```

#### 2. 代码生成工具 (`tools/generate.sh`)
基于OpenAPI规范自动生成代码：

```bash
# 生成所有代码和文档
./tools/generate.sh --all

# 仅生成服务端代码
./tools/generate.sh --server

# 生成所有客户端代码
./tools/generate.sh --clients

# 生成特定语言客户端
./tools/generate.sh --go        # Go客户端
./tools/generate.sh --js        # JavaScript客户端
./tools/generate.sh --ts        # TypeScript客户端
./tools/generate.sh --python    # Python客户端

# 生成API文档
./tools/generate.sh --docs

# 生成Postman集合
./tools/generate.sh --postman

# 清理生成的文件
./tools/generate.sh --clean
```

#### 3. API测试工具 (`tools/test.sh`)
自动化API端点测试：

```bash
# 检查API服务状态
./tools/test.sh --check

# 运行完整API测试套件
./tools/test.sh --test

# 指定API基础URL
./tools/test.sh --url http://localhost:8080/v1 --test

# 使用环境变量
API_BASE_URL=http://localhost:8080/v1 ./tools/test.sh --test
```

### 生成的代码结构

#### 服务端代码
- **位置**: `internal/generated/api/`
- **框架**: Go + Gin
- **包含**: 路由处理器、数据模型、API接口定义

#### 客户端代码
- **Go客户端**: `generated/clients/go/`
- **JavaScript客户端**: `generated/clients/javascript/`
- **TypeScript客户端**: `generated/clients/typescript/`
- **Python客户端**: `generated/clients/python/`

#### 文档
- **HTML文档**: `generated/docs/html/` - 交互式API文档
- **Markdown文档**: `generated/docs/markdown/` - 项目集成文档
- **Postman集合**: `generated/postman/` - API测试集合

### 推荐工具
- **编辑器**: VS Code + OpenAPI扩展
- **文档展示**: Swagger UI / Redoc
- **代码生成**: OpenAPI Generator
- **测试工具**: Postman / Insomnia
- **Mock服务**: Prism / WireMock

### CI/CD集成
在CI/CD流水线中集成API文档验证：

```yaml
# .github/workflows/api-docs.yml
name: API Documentation
on: [push, pull_request]
jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Validate API Specification
        run: ./docs/02-design/api/tools/validate.sh --validate
      - name: Generate Documentation
        run: ./docs/02-design/api/tools/generate.sh --docs
```

### 开发工作流
开发过程中的常用命令：

```bash
# 修改API规范后验证
./tools/validate.sh --validate

# 重新生成代码
./tools/generate.sh --server --clients

# 启动API服务后测试
./tools/test.sh --test

# 预览文档
./tools/validate.sh --preview
```

## 数据模型映射

API数据模型基于数据库表设计，主要映射关系：

| 数据库表 | API模型 | 说明 |
|----------|---------|------|
| users | User | 用户基础信息 |
| children | Child | 孩子档案信息 |
| checkin_records | CheckinRecord | 打卡记录 |
| videos | Video | 视频内容 |
| system_tasks | SystemTask | 系统任务 |
| team_tasks | TeamTask | 团队任务 |

详细的数据模型定义请参考 `components/schemas/` 目录。

## 版本历史

| 版本 | 日期 | 变更说明 |
|------|------|----------|
| 1.0.0 | 2025-07-05 | 初始版本，包含P0核心功能API |
| 1.0.0 | 2025-07-06 | **完整版本发布** - 新增内容管理、任务系统、社交互动、排行榜模块，共40个API端点 |

## 项目状态

🎉 **API文档设计阶段已完成！**

**完成情况**:
- ✅ 7个功能模块全部完成
- ✅ 40个API端点定义完整
- ✅ 15+个数据模型定义
- ✅ 完整的组件复用体系
- ✅ 自动化验证和生成工具

**下一步**: 开始Go后端API实现

详细完成情况请查看 [API完成总结](./API_COMPLETION_SUMMARY.md)。

## 联系方式

如有API相关问题，请联系：
- 技术负责人：[待定]
- 开发团队：[待定]
- 文档维护：AI Assistant

---

**注意**: 本API文档是项目的核心技术文档，任何变更都需要经过严格的评审流程。请确保在修改前充分了解变更的影响范围。
