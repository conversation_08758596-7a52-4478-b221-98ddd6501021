openapi: 3.0.3
info:
  title: 儿童多品类学习平台API
  description: |
    儿童多品类学习平台的完整API接口规范。

    ## 功能特性
    - 支持跳绳等多品类学习内容
    - 视频打卡和社交互动
    - 积分奖励和排行榜系统
    - 系统任务和团队任务
    - 个性化内容推荐

    ## 认证方式
    使用JWT Token进行身份认证，需要在请求头中包含：
    ```
    Authorization: Bearer <your-jwt-token>
    ```

    ## 错误处理
    所有错误响应都遵循统一格式，包含错误码、错误信息和详细描述。

    ## 版本管理
    API采用语义化版本管理，当前版本为v1。

  version: 1.0.0
  contact:
    name: 开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.kids-platform.com/v1
    description: 生产环境
  - url: https://api-staging.kids-platform.com/v1
    description: 测试环境
  - url: http://localhost:8080/v1
    description: 本地开发环境

# 全局安全配置
security:
  - BearerAuth: []

paths:
  # 认证模块
  /auth/register:
    $ref: "./modules/auth.yaml#/paths/~1auth~1register"
  /auth/login:
    $ref: "./modules/auth.yaml#/paths/~1auth~1login"
  /auth/wechat:
    $ref: "./modules/auth.yaml#/paths/~1auth~1wechat"
  /auth/refresh:
    $ref: "./modules/auth.yaml#/paths/~1auth~1refresh"
  /auth/logout:
    $ref: "./modules/auth.yaml#/paths/~1auth~1logout"

  # 用户管理模块
  /users/profile:
    $ref: "./modules/users.yaml#/paths/~1users~1profile"

  # 孩子管理模块
  /children:
    $ref: "./modules/children.yaml#/paths/~1children"
  /children/current:
    $ref: "./modules/children.yaml#/paths/~1children~1current"
  /children/{id}:
    $ref: "./modules/children.yaml#/paths/~1children~1{id}"
  /children/{id}/select:
    $ref: "./modules/children.yaml#/paths/~1children~1{id}~1select"

  # 打卡系统模块
  /checkins:
    $ref: "./modules/checkins.yaml#/paths/~1checkins"
  /checkins/{checkinId}:
    $ref: "./modules/checkins.yaml#/paths/~1checkins~1{checkinId}"
  /checkins/{checkinId}/media:
    $ref: "./modules/checkins.yaml#/paths/~1checkins~1{checkinId}~1media"
  /checkins/stats:
    $ref: "./modules/checkins.yaml#/paths/~1checkins~1stats"

  # 内容管理和推荐模块
  /content/videos:
    $ref: "./modules/content.yaml#/paths/~1content~1videos"
  /content/videos/{videoId}:
    $ref: "./modules/content.yaml#/paths/~1content~1videos~1{videoId}"
  /content/playlists:
    $ref: "./modules/content.yaml#/paths/~1content~1playlists"
  /content/recommendations:
    $ref: "./modules/content.yaml#/paths/~1content~1recommendations"

  # 任务系统模块
  /tasks/system:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1system"
  /tasks/system/{taskId}:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1system~1{taskId}"
  /tasks/system/{taskId}/join:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1system~1{taskId}~1join"
  /tasks/system/{taskId}/quit:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1system~1{taskId}~1quit"
  /tasks/team:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1team"
  /tasks/team/{taskId}:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1team~1{taskId}"
  /tasks/team/{taskId}/join:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1team~1{taskId}~1join"
  /tasks/team/join-by-code:
    $ref: "./modules/tasks.yaml#/paths/~1tasks~1team~1join-by-code"

  # 社交互动模块
  /social/likes:
    $ref: "./modules/social.yaml#/paths/~1social~1likes"
  /social/comments:
    $ref: "./modules/social.yaml#/paths/~1social~1comments"
  /social/comments/{commentId}:
    $ref: "./modules/social.yaml#/paths/~1social~1comments~1{commentId}"
  /social/follows:
    $ref: "./modules/social.yaml#/paths/~1social~1follows"
  /social/shares:
    $ref: "./modules/social.yaml#/paths/~1social~1shares"
  /social/timeline:
    $ref: "./modules/social.yaml#/paths/~1social~1timeline"

  # 排行榜模块
  /leaderboard/personal:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1personal"
  /leaderboard/teams:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1teams"
  /leaderboard/categories/{category}:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1categories~1{category}"
  /leaderboard/achievements:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1achievements"
  /leaderboard/history:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1history"
  /leaderboard/stats:
    $ref: "./modules/leaderboard.yaml#/paths/~1leaderboard~1stats"

components:
  # 安全方案
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Token认证，格式：Bearer <token>

        Token包含用户身份信息，有效期为24小时。
        可通过 /auth/refresh 接口刷新Token。

  # 通用响应格式
  responses:
    SuccessResponse:
      $ref: "./components/responses/common.yaml#/SuccessResponse"
    CreatedResponse:
      $ref: "./components/responses/common.yaml#/CreatedResponse"
    NoContentResponse:
      $ref: "./components/responses/common.yaml#/NoContentResponse"
    BadRequest:
      $ref: "./components/responses/common.yaml#/BadRequest"
    Unauthorized:
      $ref: "./components/responses/common.yaml#/Unauthorized"
    Forbidden:
      $ref: "./components/responses/common.yaml#/Forbidden"
    NotFound:
      $ref: "./components/responses/common.yaml#/NotFound"
    Conflict:
      $ref: "./components/responses/common.yaml#/Conflict"
    UnprocessableEntity:
      $ref: "./components/responses/common.yaml#/UnprocessableEntity"
    TooManyRequests:
      $ref: "./components/responses/common.yaml#/TooManyRequests"
    InternalServerError:
      $ref: "./components/responses/common.yaml#/InternalServerError"
    ServiceUnavailable:
      $ref: "./components/responses/common.yaml#/ServiceUnavailable"
    PaginatedResponse:
      $ref: "./components/responses/common.yaml#/PaginatedResponse"
    FileUploadResponse:
      $ref: "./components/responses/common.yaml#/FileUploadResponse"

  # 通用数据模型
  schemas:
    # 基础响应格式
    SuccessResponse:
      type: object
      required:
        - code
        - message
        - data
        - timestamp
      properties:
        code:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "操作成功"
          description: 响应消息
        data:
          type: object
          description: 响应数据，具体结构根据接口而定
        timestamp:
          type: string
          format: date-time
          example: "2025-07-05T10:30:00Z"
          description: 响应时间戳

    ErrorResponse:
      type: object
      required:
        - code
        - message
        - timestamp
      properties:
        code:
          type: integer
          example: 400
          description: 错误状态码
        message:
          type: string
          example: "请求参数错误"
          description: 错误消息
        details:
          type: string
          example: "缺少必需参数：phone"
          description: 错误详细信息
        timestamp:
          type: string
          format: date-time
          example: "2025-07-05T10:30:00Z"
          description: 错误发生时间

    # 分页响应格式
    PaginatedResponse:
      type: object
      required:
        - code
        - message
        - data
        - pagination
        - timestamp
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "查询成功"
        data:
          type: array
          items:
            type: object
          description: 分页数据列表
        pagination:
          $ref: "#/components/schemas/Pagination"
        timestamp:
          type: string
          format: date-time

    Pagination:
      type: object
      required:
        - page
        - size
        - total
        - totalPages
      properties:
        page:
          type: integer
          minimum: 1
          example: 1
          description: 当前页码
        size:
          type: integer
          minimum: 1
          maximum: 100
          example: 20
          description: 每页数量
        total:
          type: integer
          minimum: 0
          example: 150
          description: 总记录数
        totalPages:
          type: integer
          minimum: 0
          example: 8
          description: 总页数

    # 业务实体模型引用
    User:
      $ref: "./components/schemas/common.yaml#/User"
    Child:
      $ref: "./components/schemas/common.yaml#/Child"
    CheckinRecord:
      $ref: "./components/schemas/common.yaml#/CheckinRecord"
    Video:
      $ref: "./components/schemas/common.yaml#/Video"
    Playlist:
      $ref: "./components/schemas/common.yaml#/Playlist"
    SystemTask:
      $ref: "./components/schemas/common.yaml#/SystemTask"
    TeamTask:
      $ref: "./components/schemas/common.yaml#/TeamTask"
    Comment:
      $ref: "./components/schemas/common.yaml#/Comment"

  # 通用参数
  parameters:
    PageParam:
      $ref: "./components/parameters/common.yaml#/PageParam"
    SizeParam:
      $ref: "./components/parameters/common.yaml#/SizeParam"
    SortByParam:
      $ref: "./components/parameters/common.yaml#/SortByParam"
    SortOrderParam:
      $ref: "./components/parameters/common.yaml#/SortOrderParam"
    UserIdParam:
      $ref: "./components/parameters/common.yaml#/UserIdParam"
    ChildIdParam:
      $ref: "./components/parameters/common.yaml#/ChildIdParam"
    CheckinIdParam:
      $ref: "./components/parameters/common.yaml#/CheckinIdParam"
    VideoIdParam:
      $ref: "./components/parameters/common.yaml#/VideoIdParam"
    TaskIdParam:
      $ref: "./components/parameters/common.yaml#/TaskIdParam"
    CommentIdParam:
      $ref: "./components/parameters/common.yaml#/CommentIdParam"
    PlaylistIdParam:
      $ref: "./components/parameters/common.yaml#/PlaylistIdParam"
    StartDateParam:
      $ref: "./components/parameters/common.yaml#/StartDateParam"
    EndDateParam:
      $ref: "./components/parameters/common.yaml#/EndDateParam"
    StatusParam:
      $ref: "./components/parameters/common.yaml#/StatusParam"
    KeywordParam:
      $ref: "./components/parameters/common.yaml#/KeywordParam"
    CategoryIdParam:
      $ref: "./components/parameters/common.yaml#/CategoryIdParam"
    DifficultyParam:
      $ref: "./components/parameters/common.yaml#/DifficultyParam"
    AgeRangeParam:
      $ref: "./components/parameters/common.yaml#/AgeRangeParam"
    TagsParam:
      $ref: "./components/parameters/common.yaml#/TagsParam"

# 标签分组
tags:
  - name: Authentication
    description: 用户认证和授权相关接口
  - name: User Management
    description: 用户信息和孩子档案管理
  - name: Check-in System
    description: 视频打卡和记录管理
  - name: Content Management
    description: 视频内容、播放列表和推荐系统
  - name: Task System
    description: 系统任务和团队任务管理
  - name: Social Features
    description: 点赞、评论、关注、分享等社交功能
  - name: Leaderboard
    description: 个人排行、团队排行、成就排行等
