#!/bin/bash

# API代码生成脚本
# 基于OpenAPI规范自动生成服务端和客户端代码

set -e

# 设置Java环境 (OpenAPI Generator需要Java 11+)
export JAVA_HOME=/usr/local/opt/openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(cd "$API_DIR/../../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
OPENAPI_SPEC="$API_DIR/openapi.yaml"
OUTPUT_DIR="$API_DIR/generated"
SERVER_OUTPUT_DIR="$PROJECT_ROOT/internal/generated"
CLIENT_OUTPUT_DIR="$OUTPUT_DIR/clients"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v openapi-generator &> /dev/null; then
        log_error "OpenAPI Generator未安装"
        log_info "安装方法："
        echo "  # 使用Homebrew"
        echo "  brew install openapi-generator"
        echo ""
        echo "  # 或使用npm"
        echo "  npm install -g @openapitools/openapi-generator-cli"
        exit 1
    fi
    
    if [ ! -f "$OPENAPI_SPEC" ]; then
        log_error "OpenAPI规范文件不存在: $OPENAPI_SPEC"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 生成Go服务端代码
generate_go_server() {
    log_info "生成Go服务端代码..."
    
    local output_dir="$SERVER_OUTPUT_DIR/api"
    mkdir -p "$output_dir"
    
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g go-gin-server \
        -o "$output_dir" \
        --additional-properties=packageName=api,packageVersion=1.0.0,sourceFolder=src \
        --global-property=models,apis,supportingFiles
    
    log_success "Go服务端代码生成完成: $output_dir"
}

# 生成Go客户端代码
generate_go_client() {
    log_info "生成Go客户端代码..."
    
    local output_dir="$CLIENT_OUTPUT_DIR/go"
    mkdir -p "$output_dir"
    
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g go \
        -o "$output_dir" \
        --additional-properties=packageName=kidsplatform,packageVersion=1.0.0,isGoSubmodule=true
    
    log_success "Go客户端代码生成完成: $output_dir"
}

# 生成JavaScript客户端代码
generate_js_client() {
    log_info "生成JavaScript客户端代码..."
    
    local output_dir="$CLIENT_OUTPUT_DIR/javascript"
    mkdir -p "$output_dir"
    
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g javascript \
        -o "$output_dir" \
        --additional-properties=projectName=kids-platform-api,projectVersion=1.0.0,usePromises=true
    
    log_success "JavaScript客户端代码生成完成: $output_dir"
}

# 生成TypeScript客户端代码
generate_ts_client() {
    log_info "生成TypeScript客户端代码..."
    
    local output_dir="$CLIENT_OUTPUT_DIR/typescript"
    mkdir -p "$output_dir"
    
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g typescript-axios \
        -o "$output_dir" \
        --additional-properties=npmName=kids-platform-api,npmVersion=1.0.0,supportsES6=true
    
    log_success "TypeScript客户端代码生成完成: $output_dir"
}

# 生成Python客户端代码
generate_python_client() {
    log_info "生成Python客户端代码..."
    
    local output_dir="$CLIENT_OUTPUT_DIR/python"
    mkdir -p "$output_dir"
    
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g python \
        -o "$output_dir" \
        --additional-properties=packageName=kids_platform_api,packageVersion=1.0.0,projectName=kids-platform-api
    
    log_success "Python客户端代码生成完成: $output_dir"
}

# 生成API文档
generate_docs() {
    log_info "生成API文档..."
    
    local output_dir="$OUTPUT_DIR/docs"
    mkdir -p "$output_dir"
    
    # 生成HTML文档
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g html2 \
        -o "$output_dir/html"
    
    # 生成Markdown文档
    openapi-generator generate \
        -i "$OPENAPI_SPEC" \
        -g markdown \
        -o "$output_dir/markdown"
    
    log_success "API文档生成完成: $output_dir"
}

# 生成Postman集合
generate_postman() {
    log_info "生成Postman集合..."
    
    local output_dir="$OUTPUT_DIR/postman"
    mkdir -p "$output_dir"
    
    # 转换为Postman集合格式
    if command -v postman-to-openapi &> /dev/null; then
        openapi-to-postman -s "$OPENAPI_SPEC" -o "$output_dir/kids-platform-api.postman_collection.json"
        log_success "Postman集合生成完成: $output_dir"
    else
        log_warning "postman-to-openapi未安装，跳过Postman集合生成"
        log_info "安装方法: npm install -g openapi-to-postman"
    fi
}

# 清理生成的文件
clean_generated() {
    log_info "清理生成的文件..."
    
    if [ -d "$OUTPUT_DIR" ]; then
        rm -rf "$OUTPUT_DIR"
        log_success "已清理客户端生成文件"
    fi
    
    if [ -d "$SERVER_OUTPUT_DIR" ]; then
        rm -rf "$SERVER_OUTPUT_DIR"
        log_success "已清理服务端生成文件"
    fi
}

# 创建生成报告
create_report() {
    log_info "创建生成报告..."
    
    local report_file="$OUTPUT_DIR/generation-report.md"
    mkdir -p "$(dirname "$report_file")"
    
    cat > "$report_file" << EOF
# API代码生成报告

生成时间: $(date)
OpenAPI规范: $(basename "$OPENAPI_SPEC")

## 生成的组件

### 服务端代码
- **Go Gin服务端**: \`$SERVER_OUTPUT_DIR/api\`
  - 包含路由处理器、数据模型和API接口定义
  - 基于Gin框架的HTTP服务器实现

### 客户端代码
- **Go客户端**: \`$CLIENT_OUTPUT_DIR/go\`
  - 类型安全的Go客户端库
  - 支持所有API端点和数据模型

- **JavaScript客户端**: \`$CLIENT_OUTPUT_DIR/javascript\`
  - 基于Promise的JavaScript客户端
  - 适用于Node.js和浏览器环境

- **TypeScript客户端**: \`$CLIENT_OUTPUT_DIR/typescript\`
  - 完整类型定义的TypeScript客户端
  - 基于Axios的HTTP客户端

- **Python客户端**: \`$CLIENT_OUTPUT_DIR/python\`
  - Python客户端库
  - 支持同步和异步调用

### 文档
- **HTML文档**: \`$OUTPUT_DIR/docs/html\`
  - 交互式API文档
  - 可直接在浏览器中查看

- **Markdown文档**: \`$OUTPUT_DIR/docs/markdown\`
  - Markdown格式的API文档
  - 适合集成到项目文档中

### 测试工具
- **Postman集合**: \`$OUTPUT_DIR/postman\`
  - 可导入Postman的API测试集合
  - 包含所有端点的示例请求

## 使用说明

### 服务端集成
1. 将生成的Go服务端代码集成到项目中
2. 实现具体的业务逻辑处理器
3. 配置路由和中间件

### 客户端使用
1. 根据需要选择对应语言的客户端
2. 安装依赖包
3. 配置API基础URL和认证信息
4. 调用API方法

### 文档查看
1. 打开HTML文档查看交互式API文档
2. 使用Markdown文档集成到项目文档中
3. 导入Postman集合进行API测试

## 注意事项
- 生成的代码仅包含API接口定义和基础实现
- 需要根据实际业务需求实现具体逻辑
- 建议定期重新生成以保持与API规范同步
EOF

    log_success "生成报告已创建: $report_file"
}

# 显示帮助信息
show_help() {
    echo "API代码生成工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -s, --server        生成服务端代码"
    echo "  -c, --clients       生成所有客户端代码"
    echo "  -g, --go            生成Go客户端代码"
    echo "  -j, --js            生成JavaScript客户端代码"
    echo "  -t, --ts            生成TypeScript客户端代码"
    echo "  -p, --python        生成Python客户端代码"
    echo "  -d, --docs          生成API文档"
    echo "  -m, --postman       生成Postman集合"
    echo "  -a, --all           生成所有代码和文档"
    echo "  --clean             清理生成的文件"
    echo ""
    echo "示例:"
    echo "  $0 --server         # 仅生成服务端代码"
    echo "  $0 --clients        # 生成所有客户端代码"
    echo "  $0 --all            # 生成所有代码和文档"
    echo "  $0 --clean          # 清理生成的文件"
}

# 主函数
main() {
    local actions=()
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--server)
                actions+=("server")
                shift
                ;;
            -c|--clients)
                actions+=("go_client" "js_client" "ts_client" "python_client")
                shift
                ;;
            -g|--go)
                actions+=("go_client")
                shift
                ;;
            -j|--js)
                actions+=("js_client")
                shift
                ;;
            -t|--ts)
                actions+=("ts_client")
                shift
                ;;
            -p|--python)
                actions+=("python_client")
                shift
                ;;
            -d|--docs)
                actions+=("docs")
                shift
                ;;
            -m|--postman)
                actions+=("postman")
                shift
                ;;
            -a|--all)
                actions+=("server" "go_client" "js_client" "ts_client" "python_client" "docs" "postman")
                shift
                ;;
            --clean)
                clean_generated
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定操作，默认生成所有
    if [ ${#actions[@]} -eq 0 ]; then
        actions+=("server" "go_client" "js_client" "ts_client" "python_client" "docs")
    fi
    
    # 检查依赖
    check_dependencies
    
    # 执行操作
    for action in "${actions[@]}"; do
        case $action in
            server)
                generate_go_server
                ;;
            go_client)
                generate_go_client
                ;;
            js_client)
                generate_js_client
                ;;
            ts_client)
                generate_ts_client
                ;;
            python_client)
                generate_python_client
                ;;
            docs)
                generate_docs
                ;;
            postman)
                generate_postman
                ;;
        esac
    done
    
    # 创建报告
    create_report
    
    log_success "代码生成完成！"
    log_info "查看生成报告: $OUTPUT_DIR/generation-report.md"
}

# 运行主函数
main "$@"
