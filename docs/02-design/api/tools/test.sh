#!/bin/bash

# API测试脚本
# 基于OpenAPI规范进行API端点测试

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(cd "$API_DIR/../../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_BASE_URL="${API_BASE_URL:-http://localhost:8080/v1}"
TEST_OUTPUT_DIR="$API_DIR/test-results"
OPENAPI_SPEC="$API_DIR/openapi.yaml"

# 测试数据
TEST_PHONE="13800138000"
TEST_NICKNAME="测试用户"
TEST_PASSWORD="test123456"
ACCESS_TOKEN=""

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# HTTP请求函数
http_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local headers="$4"
    
    local url="$API_BASE_URL$endpoint"
    local curl_args=("-s" "-w" "%{http_code}" "-X" "$method")
    
    if [ -n "$headers" ]; then
        while IFS= read -r header; do
            curl_args+=("-H" "$header")
        done <<< "$headers"
    fi
    
    if [ -n "$data" ]; then
        curl_args+=("-d" "$data")
    fi
    
    curl_args+=("$url")
    
    curl "${curl_args[@]}"
}

# 解析HTTP响应
parse_response() {
    local response="$1"
    local http_code="${response: -3}"
    local body="${response%???}"
    
    echo "HTTP_CODE:$http_code"
    echo "BODY:$body"
}

# 测试用户注册
test_user_registration() {
    log_info "测试用户注册..."
    
    local data='{
        "phone": "'$TEST_PHONE'",
        "verificationCode": "123456",
        "nickname": "'$TEST_NICKNAME'",
        "password": "'$TEST_PASSWORD'"
    }'
    
    local headers="Content-Type: application/json"
    local response=$(http_request "POST" "/auth/register" "$data" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "201" ] || [ "$http_code" = "409" ]; then
        log_success "用户注册测试通过 (HTTP $http_code)"
        return 0
    else
        log_error "用户注册测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试用户登录
test_user_login() {
    log_info "测试用户登录..."
    
    local data='{
        "phone": "'$TEST_PHONE'",
        "password": "'$TEST_PASSWORD'"
    }'
    
    local headers="Content-Type: application/json"
    local response=$(http_request "POST" "/auth/login" "$data" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "200" ]; then
        # 提取访问令牌
        ACCESS_TOKEN=$(echo "$body" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$ACCESS_TOKEN" ]; then
            log_success "用户登录测试通过，获得访问令牌"
            return 0
        else
            log_error "用户登录测试失败：无法获取访问令牌"
            return 1
        fi
    else
        log_error "用户登录测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试获取用户信息
test_get_user_profile() {
    log_info "测试获取用户信息..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local headers="Authorization: Bearer $ACCESS_TOKEN"
    local response=$(http_request "GET" "/users/profile" "" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "200" ]; then
        log_success "获取用户信息测试通过"
        return 0
    else
        log_error "获取用户信息测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试创建孩子档案
test_create_child() {
    log_info "测试创建孩子档案..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local data='{
        "name": "小明",
        "gender": 1,
        "birthDate": "2015-06-01",
        "grade": "小学一年级"
    }'
    
    local headers="Content-Type: application/json
Authorization: Bearer $ACCESS_TOKEN"
    
    local response=$(http_request "POST" "/users/children" "$data" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "201" ]; then
        log_success "创建孩子档案测试通过"
        return 0
    else
        log_error "创建孩子档案测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试获取孩子列表
test_get_children() {
    log_info "测试获取孩子列表..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local headers="Authorization: Bearer $ACCESS_TOKEN"
    local response=$(http_request "GET" "/users/children" "" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "200" ]; then
        log_success "获取孩子列表测试通过"
        return 0
    else
        log_error "获取孩子列表测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试创建打卡记录
test_create_checkin() {
    log_info "测试创建打卡记录..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local data='{
        "childId": 1,
        "category": "跳绳",
        "videoUrl": "https://example.com/video.mp4",
        "thumbnailUrl": "https://example.com/thumb.jpg",
        "description": "今天跳了100个",
        "duration": 60,
        "count": 100
    }'
    
    local headers="Content-Type: application/json
Authorization: Bearer $ACCESS_TOKEN"
    
    local response=$(http_request "POST" "/checkins" "$data" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "201" ]; then
        log_success "创建打卡记录测试通过"
        return 0
    else
        log_error "创建打卡记录测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试获取打卡记录列表
test_get_checkins() {
    log_info "测试获取打卡记录列表..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local headers="Authorization: Bearer $ACCESS_TOKEN"
    local response=$(http_request "GET" "/checkins?page=1&size=10" "" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "200" ]; then
        log_success "获取打卡记录列表测试通过"
        return 0
    else
        log_error "获取打卡记录列表测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 测试用户登出
test_user_logout() {
    log_info "测试用户登出..."
    
    if [ -z "$ACCESS_TOKEN" ]; then
        log_error "缺少访问令牌，请先登录"
        return 1
    fi
    
    local headers="Authorization: Bearer $ACCESS_TOKEN"
    local response=$(http_request "POST" "/auth/logout" "" "$headers")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    local body=$(echo "$parsed" | grep "BODY:" | cut -d: -f2-)
    
    if [ "$http_code" = "200" ]; then
        log_success "用户登出测试通过"
        ACCESS_TOKEN=""
        return 0
    else
        log_error "用户登出测试失败 (HTTP $http_code): $body"
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    log_info "开始API端点测试..."
    log_info "API基础URL: $API_BASE_URL"
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 测试列表
    local tests=(
        "test_user_registration"
        "test_user_login"
        "test_get_user_profile"
        "test_create_child"
        "test_get_children"
        "test_create_checkin"
        "test_get_checkins"
        "test_user_logout"
    )
    
    # 创建测试结果目录
    mkdir -p "$TEST_OUTPUT_DIR"
    local report_file="$TEST_OUTPUT_DIR/test-report-$(date +%Y%m%d-%H%M%S).md"
    
    # 开始测试报告
    cat > "$report_file" << EOF
# API测试报告

测试时间: $(date)
API基础URL: $API_BASE_URL
测试用户: $TEST_PHONE

## 测试结果

EOF
    
    # 执行测试
    for test_func in "${tests[@]}"; do
        ((total_tests++))
        echo "## 测试 $((total_tests)): $test_func" >> "$report_file"
        
        if $test_func; then
            ((passed_tests++))
            echo "✅ **通过**" >> "$report_file"
        else
            ((failed_tests++))
            echo "❌ **失败**" >> "$report_file"
        fi
        echo "" >> "$report_file"
        
        # 短暂延迟避免请求过快
        sleep 1
    done
    
    # 测试总结
    cat >> "$report_file" << EOF
## 测试总结

- 总测试数: $total_tests
- 通过测试: $passed_tests
- 失败测试: $failed_tests
- 成功率: $(( passed_tests * 100 / total_tests ))%

EOF
    
    # 显示结果
    echo ""
    log_info "测试完成！"
    log_info "总测试数: $total_tests"
    log_success "通过测试: $passed_tests"
    if [ $failed_tests -gt 0 ]; then
        log_error "失败测试: $failed_tests"
    fi
    log_info "测试报告: $report_file"
    
    return $failed_tests
}

# 检查API服务状态
check_api_status() {
    log_info "检查API服务状态..."
    
    local response=$(http_request "GET" "/health" "" "")
    local parsed=$(parse_response "$response")
    local http_code=$(echo "$parsed" | grep "HTTP_CODE:" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        log_success "API服务正常运行"
        return 0
    else
        log_error "API服务不可用 (HTTP $http_code)"
        log_info "请确保API服务已启动并运行在 $API_BASE_URL"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "API测试工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --check         检查API服务状态"
    echo "  -t, --test          运行所有测试"
    echo "  -u, --url URL       设置API基础URL (默认: $API_BASE_URL)"
    echo ""
    echo "环境变量:"
    echo "  API_BASE_URL        API基础URL"
    echo ""
    echo "示例:"
    echo "  $0 --check          # 检查API服务状态"
    echo "  $0 --test           # 运行所有测试"
    echo "  $0 -u http://localhost:8080/v1 --test  # 指定URL运行测试"
}

# 主函数
main() {
    local action="test"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                action="check"
                shift
                ;;
            -t|--test)
                action="test"
                shift
                ;;
            -u|--url)
                API_BASE_URL="$2"
                shift 2
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查curl命令
    if ! command -v curl &> /dev/null; then
        log_error "curl命令未找到，请安装curl"
        exit 1
    fi
    
    # 执行相应操作
    case $action in
        check)
            check_api_status
            ;;
        test)
            if check_api_status; then
                run_all_tests
            else
                exit 1
            fi
            ;;
    esac
}

# 运行主函数
main "$@"
