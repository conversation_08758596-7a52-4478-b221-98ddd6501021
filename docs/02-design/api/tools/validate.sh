#!/bin/bash

# API文档验证脚本
# 用于验证OpenAPI规范的语法和结构正确性

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(cd "$API_DIR/../../.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    # 检查swagger-codegen
    if ! command -v swagger-codegen &> /dev/null; then
        missing_tools+=("swagger-codegen")
    fi
    
    # 检查openapi-generator
    if ! command -v openapi-generator &> /dev/null; then
        missing_tools+=("openapi-generator")
    fi
    
    # 检查Node.js和npm（用于swagger-ui-serve）
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_warning "以下工具未安装，部分功能可能不可用："
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        log_info "安装建议："
        echo "  # 安装OpenAPI Generator"
        echo "  brew install openapi-generator"
        echo ""
        echo "  # 或使用npm安装"
        echo "  npm install -g @openapitools/openapi-generator-cli"
        echo ""
        echo "  # 安装Swagger UI服务器"
        echo "  npm install -g swagger-ui-serve"
        echo ""
    else
        log_success "所有依赖工具已安装"
    fi
}

# 验证YAML语法
validate_yaml_syntax() {
    log_info "验证YAML语法..."
    
    local yaml_files=(
        "$API_DIR/openapi.yaml"
        "$API_DIR/modules/auth.yaml"
        "$API_DIR/modules/users.yaml"
        "$API_DIR/modules/checkins.yaml"
        "$API_DIR/modules/content.yaml"
        "$API_DIR/modules/tasks.yaml"
        "$API_DIR/modules/social.yaml"
        "$API_DIR/modules/leaderboard.yaml"
        "$API_DIR/components/schemas/common.yaml"
        "$API_DIR/components/parameters/common.yaml"
        "$API_DIR/components/responses/common.yaml"
    )
    
    local syntax_errors=0
    
    for file in "${yaml_files[@]}"; do
        if [ -f "$file" ]; then
            if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
                log_success "✓ $(basename "$file")"
            else
                log_error "✗ $(basename "$file") - YAML语法错误"
                ((syntax_errors++))
            fi
        else
            log_warning "文件不存在: $(basename "$file")"
        fi
    done
    
    if [ $syntax_errors -eq 0 ]; then
        log_success "所有YAML文件语法正确"
    else
        log_error "发现 $syntax_errors 个YAML语法错误"
        return 1
    fi
}

# 验证OpenAPI规范
validate_openapi_spec() {
    log_info "验证OpenAPI规范..."
    
    local main_spec="$API_DIR/openapi.yaml"
    
    if [ ! -f "$main_spec" ]; then
        log_error "主规范文件不存在: $main_spec"
        return 1
    fi
    
    # 使用openapi-generator验证
    if command -v openapi-generator &> /dev/null; then
        log_info "使用OpenAPI Generator验证规范..."
        if openapi-generator validate -i "$main_spec" 2>/dev/null; then
            log_success "OpenAPI规范验证通过"
        else
            log_error "OpenAPI规范验证失败"
            log_info "详细错误信息："
            openapi-generator validate -i "$main_spec"
            return 1
        fi
    else
        log_warning "OpenAPI Generator未安装，跳过规范验证"
    fi
}

# 检查文件引用
check_file_references() {
    log_info "检查文件引用..."
    
    local main_spec="$API_DIR/openapi.yaml"
    local ref_errors=0
    
    # 提取所有$ref引用
    local refs=$(grep -r '\$ref:' "$API_DIR" | grep -v '\.git' | sed 's/.*\$ref: *["\x27]\([^"\x27]*\)["\x27].*/\1/')
    
    for ref in $refs; do
        # 跳过内部引用（以#开头）
        if [[ "$ref" == \#* ]]; then
            continue
        fi
        
        # 解析文件路径
        local file_path="${ref%%#*}"
        local full_path="$API_DIR/$file_path"
        
        if [ ! -f "$full_path" ]; then
            log_error "引用的文件不存在: $file_path"
            ((ref_errors++))
        fi
    done
    
    if [ $ref_errors -eq 0 ]; then
        log_success "所有文件引用正确"
    else
        log_error "发现 $ref_errors 个文件引用错误"
        return 1
    fi
}

# 生成文档预览
generate_preview() {
    log_info "生成文档预览..."
    
    local main_spec="$API_DIR/openapi.yaml"
    local output_dir="$API_DIR/preview"
    
    # 创建预览目录
    mkdir -p "$output_dir"
    
    # 使用swagger-ui-serve生成预览
    if command -v swagger-ui-serve &> /dev/null; then
        log_info "启动Swagger UI预览服务器..."
        log_info "访问地址: http://localhost:3000"
        log_info "按 Ctrl+C 停止服务器"
        swagger-ui-serve "$main_spec" -p 3000
    else
        log_warning "swagger-ui-serve未安装，无法启动预览服务器"
        log_info "可以手动安装: npm install -g swagger-ui-serve"
    fi
}

# 生成客户端代码示例
generate_client_samples() {
    log_info "生成客户端代码示例..."
    
    local main_spec="$API_DIR/openapi.yaml"
    local output_dir="$API_DIR/generated"
    
    if ! command -v openapi-generator &> /dev/null; then
        log_warning "OpenAPI Generator未安装，跳过代码生成"
        return 0
    fi
    
    # 创建输出目录
    mkdir -p "$output_dir"
    
    # 生成JavaScript客户端
    log_info "生成JavaScript客户端..."
    openapi-generator generate \
        -i "$main_spec" \
        -g javascript \
        -o "$output_dir/javascript-client" \
        --additional-properties=projectName=kids-platform-api,projectVersion=1.0.0
    
    # 生成Go客户端
    log_info "生成Go客户端..."
    openapi-generator generate \
        -i "$main_spec" \
        -g go \
        -o "$output_dir/go-client" \
        --additional-properties=packageName=kidsplatform,packageVersion=1.0.0
    
    log_success "客户端代码生成完成，输出目录: $output_dir"
}

# 显示帮助信息
show_help() {
    echo "API文档验证工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --check         检查依赖工具"
    echo "  -v, --validate      验证API规范"
    echo "  -p, --preview       生成文档预览"
    echo "  -g, --generate      生成客户端代码"
    echo "  -a, --all           执行所有验证和生成操作"
    echo ""
    echo "示例:"
    echo "  $0 --validate       # 仅验证API规范"
    echo "  $0 --preview        # 启动预览服务器"
    echo "  $0 --all            # 执行完整验证和生成"
}

# 主函数
main() {
    local action="validate"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                action="check"
                shift
                ;;
            -v|--validate)
                action="validate"
                shift
                ;;
            -p|--preview)
                action="preview"
                shift
                ;;
            -g|--generate)
                action="generate"
                shift
                ;;
            -a|--all)
                action="all"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行相应操作
    case $action in
        check)
            check_dependencies
            ;;
        validate)
            validate_yaml_syntax
            validate_openapi_spec
            check_file_references
            ;;
        preview)
            generate_preview
            ;;
        generate)
            generate_client_samples
            ;;
        all)
            check_dependencies
            validate_yaml_syntax
            validate_openapi_spec
            check_file_references
            generate_client_samples
            log_success "所有操作完成"
            ;;
    esac
}

# 运行主函数
main "$@"
