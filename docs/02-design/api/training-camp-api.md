# 训练营系统 API 接口设计

## 📋 接口概览

### 基础信息
- **Base URL**: `/api/v1`
- **认证方式**: JWT Token
- **响应格式**: JSON
- **字符编码**: UTF-8

### 错误码规范
```json
{
  "code": 0,        // 0:成功, >0:业务错误, <0:系统错误
  "message": "操作成功",
  "data": {}
}
```

## 🎯 核心接口

### 1. 获取训练营详情

#### 接口信息
- **URL**: `GET /api/v1/camps/{campId}/detail`
- **描述**: 获取训练营的完整详情信息，包含视频集合数据
- **权限**: 无需登录（公开接口）

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| campId | int | 是 | 训练营ID |
| childId | int | 否 | 孩子ID，用于个性化推荐 |

#### 请求示例
```
GET /api/v1/camps/1/detail?childId=123
```

#### 响应示例
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "campInfo": {
      "id": 1,
      "title": "21天跳绳养成计划",
      "subtitle": "让孩子从不会跳到连续100个",
      "heroNumber": "0 → 100个",
      "heroText": "21天技能飞跃",
      "painSolution": "解决不会教、没方法、没效果的困扰",
      "durationDays": 21,
      "dailyMinutes": 15,
      "difficultyLevel": 1,
      "ageGroup": "3-12岁",
      "price": 0.00,
      "originalPrice": 99.00,
      "isFree": true,
      "keyBenefits": [
        {"icon": "🎯", "text": "掌握跳绳技能"},
        {"icon": "💪", "text": "养成运动习惯"},
        {"icon": "📈", "text": "提升身体协调"},
        {"icon": "🏆", "text": "建立自信心"}
      ],
      "promises": [
        {"text": "完全免费"},
        {"text": "随时可退"},
        {"text": "专业指导"},
        {"text": "社群支持"}
      ],
      "tags": ["保姆级教程", "社群交流", "零基础", "免费体验"],
      "shareTitle": "21天跳绳养成计划 - 专业指导，21天见效果",
      "shareDesc": "让孩子从不会跳到连续100个，专业教练指导，完全免费",
      "shareImage": "/images/camps/21day-basic-share.jpg",
      "wechatHelper": "jump_helper_2024",
      "wechatGroupBenefits": [
        "个性化训练计划",
        "1000+家长交流经验",
        "每日训练提醒",
        "专业进度分析"
      ],
      "totalParticipants": 1234,
      "averageRating": 4.8,
      "totalReviews": 567,
      "status": 1,
      "isFeatured": true,
      "isComingSoon": false
    },
    "videoCollection": {
      "id": 1,
      "name": "21天跳绳基础教程",
      "description": "从零基础到熟练掌握，21天系统化跳绳教学视频集合",
      "totalVideos": 8,
      "totalDuration": 1800,
      "videos": [
        {
          "id": 1,
          "title": "跳绳基础到进阶完整教程",
          "customSubtitle": "正确握绳姿势与站立方法",
          "duration": 200,
          "thumbnail": "/images/videos/basic-tutorial-thumb.jpg",
          "sortOrder": 1,
          "unlockDay": 1,
          "isRequired": true
        }
      ]
    },
    "featuredVideo": {
      "id": 1,
      "title": "跳绳基础到进阶完整教程",
      "duration": 200,
      "thumbnail": "/images/videos/basic-tutorial-thumb.jpg"
    },
    "userContext": {
      "hasParticipated": false,
      "canParticipate": true,
      "recommendReason": "适合您孩子的年龄段"
    }
  }
}
```

### 2. 获取训练营列表

#### 接口信息
- **URL**: `GET /api/v1/camps/list`
- **描述**: 获取训练营列表，支持筛选和排序
- **权限**: 无需登录（公开接口）

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| status | int | 否 | 训练营状态筛选 1:正常 2:暂停 |
| isFeatured | bool | 否 | 是否只显示推荐训练营 |
| isComingSoon | bool | 否 | 是否只显示即将推出 |
| difficultyLevel | int | 否 | 难度等级筛选 1-5 |
| isFree | bool | 否 | 是否只显示免费训练营 |
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |

#### 响应示例
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "21天跳绳养成计划",
        "subtitle": "让孩子从不会跳到连续100个",
        "heroNumber": "0 → 100个",
        "price": 0.00,
        "originalPrice": 99.00,
        "isFree": true,
        "tags": ["保姆级教程", "社群交流", "零基础"],
        "totalParticipants": 1234,
        "averageRating": 4.8,
        "difficultyLevel": 1,
        "ageGroup": "3-12岁",
        "isFeatured": true,
        "isComingSoon": false
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 3. 参与训练营

#### 接口信息
- **URL**: `POST /api/v1/camps/{campId}/join`
- **描述**: 用户参与训练营，支持免费和付费流程
- **权限**: 需要登录

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| campId | int | 是 | 训练营ID |
| childId | int | 是 | 孩子ID |
| paymentMethod | string | 否 | 支付方式，付费训练营必填 |

#### 请求示例
```json
{
  "childId": 123,
  "paymentMethod": "wechat"
}
```

#### 响应示例
```json
{
  "code": 0,
  "message": "参与成功",
  "data": {
    "participationId": 456,
    "campId": 1,
    "childId": 123,
    "participationDate": "2024-01-15",
    "expectedEndDate": "2024-02-04",
    "participationStatus": 1,
    "paymentInfo": {
      "needPayment": false,
      "amount": 0.00,
      "paymentUrl": null
    },
    "nextStep": {
      "action": "start_training",
      "message": "恭喜您成功参与训练营！现在可以开始第一天的训练了。",
      "redirectUrl": "/pages/training/training?campId=1&day=1"
    }
  }
}
```

### 4. 获取用户参与记录

#### 接口信息
- **URL**: `GET /api/v1/camps/my-participations`
- **描述**: 获取当前用户的训练营参与记录
- **权限**: 需要登录

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| childId | int | 否 | 孩子ID，不传则返回所有孩子的记录 |
| status | int | 否 | 参与状态筛选 |
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |

#### 响应示例
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 456,
        "campId": 1,
        "campTitle": "21天跳绳养成计划",
        "childId": 123,
        "childName": "小明",
        "participationDate": "2024-01-15",
        "currentDay": 5,
        "progressPercentage": 23.81,
        "participationStatus": 1,
        "totalCheckins": 4,
        "consecutiveDays": 2,
        "canContinue": true
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 3,
      "totalPages": 1
    }
  }
}
```

## 🔧 状态码说明

### 业务状态码
| 状态码 | 描述 |
|--------|------|
| 0 | 操作成功 |
| 1001 | 训练营不存在 |
| 1002 | 训练营已下线 |
| 1003 | 用户已参与该训练营 |
| 1004 | 孩子年龄不符合要求 |
| 1005 | 支付信息有误 |
| 1006 | 训练营人数已满 |

### 系统状态码
| 状态码 | 描述 |
|--------|------|
| -1 | 系统错误 |
| -2 | 参数错误 |
| -3 | 认证失败 |
| -4 | 权限不足 |

## 📊 数据字典

### 训练营状态 (status)
- 1: 正常
- 2: 暂停
- 3: 下线

### 参与状态 (participation_status)
- 1: 进行中
- 2: 已完成
- 3: 已暂停
- 4: 已退出
- 5: 已过期

### 支付状态 (payment_status)
- 0: 无需支付
- 1: 待支付
- 2: 已支付
- 3: 已退款

### 难度等级 (difficulty_level)
- 1: 新手
- 2: 初级
- 3: 中级
- 4: 高级
- 5: 专业

## 🚀 使用示例

### 小程序端调用示例
```javascript
// 获取训练营详情
async function getCampDetail(campId, childId) {
  try {
    const response = await wx.request({
      url: `${API_BASE_URL}/camps/${campId}/detail`,
      method: 'GET',
      data: { childId },
      header: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('获取训练营详情失败:', error);
    throw error;
  }
}

// 参与训练营
async function joinCamp(campId, childId, paymentMethod) {
  try {
    const response = await wx.request({
      url: `${API_BASE_URL}/camps/${campId}/join`,
      method: 'POST',
      data: { childId, paymentMethod },
      header: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 0) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error('参与训练营失败:', error);
    throw error;
  }
}
```

这套API设计完整支持训练营系统的核心功能，接口设计简洁明了，易于前端调用和后端实现。
