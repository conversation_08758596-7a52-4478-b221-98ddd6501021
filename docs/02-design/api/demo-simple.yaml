openapi: 3.0.3
info:
  title: 儿童多品类学习平台 API (演示版)
  description: |
    儿童多品类学习平台的RESTful API接口规范 - 简化演示版本
    
    ## 功能概述
    - 用户认证和授权
    - 用户信息管理
    - 打卡系统
    
  version: 1.0.0
  contact:
    name: 开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.kids-platform.com/v1
    description: 生产环境
  - url: https://api-staging.kids-platform.com/v1
    description: 测试环境
  - url: http://localhost:8080/v1
    description: 本地开发环境

security:
  - BearerAuth: []

paths:
  # 认证模块
  /auth/register:
    post:
      tags:
        - 认证授权
      summary: 用户注册
      description: 新用户注册接口，支持手机号注册
      operationId: registerUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - nickname
                - password
              properties:
                phone:
                  type: string
                  pattern: '^1[3-9]\d{9}$'
                  description: 手机号
                  example: "13800138000"
                nickname:
                  type: string
                  minLength: 2
                  maxLength: 20
                  description: 用户昵称
                  example: "小明妈妈"
                password:
                  type: string
                  minLength: 6
                  maxLength: 20
                  description: 密码
                  example: "password123"
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /auth/login:
    post:
      tags:
        - 认证授权
      summary: 用户登录
      description: 用户登录接口，支持手机号+密码登录
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - password
              properties:
                phone:
                  type: string
                  pattern: '^1[3-9]\d{9}$'
                  description: 手机号
                  example: "13800138000"
                password:
                  type: string
                  description: 密码
                  example: "password123"
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # 用户管理模块
  /users/profile:
    get:
      tags:
        - 用户管理
      summary: 获取用户信息
      description: 获取当前登录用户的详细信息
      operationId: getUserProfile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # 打卡系统模块
  /checkins:
    get:
      tags:
        - 打卡系统
      summary: 获取打卡记录
      description: 获取用户的打卡记录列表
      operationId: getCheckins
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckinList'
        '401':
          $ref: '#/components/responses/Unauthorized'

    post:
      tags:
        - 打卡系统
      summary: 创建打卡记录
      description: 用户提交新的打卡记录
      operationId: createCheckin
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCheckinRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Checkin'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  # 安全方案
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT认证，格式：Bearer <token>

  # 数据模型
  schemas:
    # 认证响应
    AuthResponse:
      type: object
      required:
        - code
        - message
        - data
        - timestamp
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "登录成功"
        data:
          type: object
          required:
            - token
            - refreshToken
            - user
          properties:
            token:
              type: string
              description: JWT访问令牌
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            refreshToken:
              type: string
              description: 刷新令牌
              example: "refresh_token_example"
            user:
              $ref: '#/components/schemas/UserProfile'
        timestamp:
          type: string
          format: date-time
          example: "2025-07-05T10:30:00Z"

    # 用户信息
    UserProfile:
      type: object
      required:
        - id
        - phone
        - nickname
        - createdAt
      properties:
        id:
          type: integer
          description: 用户ID
          example: 12345
        phone:
          type: string
          description: 手机号
          example: "13800138000"
        nickname:
          type: string
          description: 用户昵称
          example: "小明妈妈"
        avatar:
          type: string
          description: 头像URL
          example: "https://example.com/avatar.jpg"
        createdAt:
          type: string
          format: date-time
          description: 注册时间
          example: "2025-07-05T10:30:00Z"

    # 打卡记录
    Checkin:
      type: object
      required:
        - id
        - userId
        - category
        - content
        - createdAt
      properties:
        id:
          type: integer
          description: 打卡记录ID
          example: 67890
        userId:
          type: integer
          description: 用户ID
          example: 12345
        category:
          type: string
          enum: [jump_rope, magic, chinese_studies, stories, calligraphy, math]
          description: 打卡类别
          example: "jump_rope"
        content:
          type: string
          description: 打卡内容描述
          example: "今天跳绳100个"
        videoUrl:
          type: string
          description: 打卡视频URL
          example: "https://example.com/video.mp4"
        createdAt:
          type: string
          format: date-time
          description: 打卡时间
          example: "2025-07-05T10:30:00Z"

    # 创建打卡请求
    CreateCheckinRequest:
      type: object
      required:
        - category
        - content
      properties:
        category:
          type: string
          enum: [jump_rope, magic, chinese_studies, stories, calligraphy, math]
          description: 打卡类别
          example: "jump_rope"
        content:
          type: string
          description: 打卡内容描述
          example: "今天跳绳100个"
        videoUrl:
          type: string
          description: 打卡视频URL
          example: "https://example.com/video.mp4"

    # 打卡记录列表
    CheckinList:
      type: object
      required:
        - code
        - message
        - data
        - timestamp
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "获取成功"
        data:
          type: object
          required:
            - items
            - pagination
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/Checkin'
            pagination:
              type: object
              required:
                - page
                - limit
                - total
                - totalPages
              properties:
                page:
                  type: integer
                  example: 1
                limit:
                  type: integer
                  example: 20
                total:
                  type: integer
                  example: 100
                totalPages:
                  type: integer
                  example: 5
        timestamp:
          type: string
          format: date-time
          example: "2025-07-05T10:30:00Z"

    # 错误响应
    ErrorResponse:
      type: object
      required:
        - code
        - message
        - timestamp
      properties:
        code:
          type: integer
          description: 错误代码
          example: 400
        message:
          type: string
          description: 错误信息
          example: "参数错误"
        details:
          type: string
          description: 详细错误信息
          example: "手机号格式不正确"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
          example: "2025-07-05T10:30:00Z"

  # 通用响应
  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: "请求参数错误"
            details: "手机号格式不正确"
            timestamp: "2025-07-05T10:30:00Z"

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: "未授权访问"
            details: "请先登录"
            timestamp: "2025-07-05T10:30:00Z"

    Conflict:
      description: 资源冲突
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 409
            message: "手机号已注册"
            details: "该手机号已被其他用户注册，请直接登录"
            timestamp: "2025-07-05T10:30:00Z"

# 标签定义
tags:
  - name: 认证授权
    description: 用户认证和授权相关接口
  - name: 用户管理
    description: 用户信息管理相关接口
  - name: 打卡系统
    description: 打卡记录管理相关接口
