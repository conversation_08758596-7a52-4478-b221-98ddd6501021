# 通用数据模型定义
# 基于数据库表设计的API数据模型

# 用户相关模型
User:
  type: object
  required:
    - id
    - phone
    - nickname
    - status
    - created_at
  properties:
    id:
      type: integer
      format: int64
      example: 12345
      description: 用户ID，全局唯一标识
    phone:
      type: string
      pattern: '^1[3-9]\d{9}$'
      example: "13800138000"
      description: 手机号码，用于登录和验证
    nickname:
      type: string
      maxLength: 50
      example: "小明妈妈"
      description: 用户昵称
    avatar:
      type: string
      format: uri
      example: "https://cdn.kids-platform.com/avatars/user123.jpg"
      description: 用户头像URL
    gender:
      type: integer
      enum: [0, 1, 2]
      example: 1
      description: 性别 0:未知 1:男 2:女
    birth_date:
      type: string
      format: date
      example: "1985-06-15"
      description: 出生日期
    location:
      type: string
      maxLength: 100
      example: "北京市朝阳区"
      description: 所在地区
    wechat_openid:
      type: string
      maxLength: 100
      example: "wx_openid_123456"
      description: 微信OpenID
    wechat_unionid:
      type: string
      maxLength: 100
      example: "wx_unionid_123456"
      description: 微信UnionID
    status:
      type: integer
      enum: [1, 2, 3]
      example: 1
      description: 用户状态 1:正常 2:禁用 3:已删除
    created_at:
      type: string
      format: date-time
      example: "2025-01-01T10:30:00Z"
      description: 注册时间
    updated_at:
      type: string
      format: date-time
      example: "2025-07-05T10:30:00Z"
      description: 最后更新时间

# 孩子档案模型
Child:
  type: object
  required:
    - id
    - nickname
    - birth_date
    - gender
    - status
    - created_at
  properties:
    id:
      type: integer
      format: int64
      example: 67890
      description: 孩子ID，全局唯一标识
    nickname:
      type: string
      maxLength: 50
      example: "小明"
      description: 孩子昵称
    real_name:
      type: string
      maxLength: 50
      example: "张小明"
      description: 孩子真实姓名
    avatar:
      type: string
      format: uri
      example: "https://cdn.kids-platform.com/avatars/child123.jpg"
      description: 孩子头像URL
    birth_date:
      type: string
      format: date
      example: "2015-03-20"
      description: 出生日期
    gender:
      type: integer
      enum: [1, 2]
      example: 1
      description: 性别 1:男 2:女
    height:
      type: number
      format: float
      minimum: 0
      maximum: 300
      example: 120.5
      description: 身高（厘米）
    weight:
      type: number
      format: float
      minimum: 0
      maximum: 200
      example: 25.8
      description: 体重（公斤）
    grade:
      type: string
      maxLength: 20
      example: "三年级"
      description: 年级
    school:
      type: string
      maxLength: 100
      example: "北京市第一小学"
      description: 学校名称
    interests:
      type: array
      items:
        type: string
      example: ["跳绳", "篮球", "画画"]
      description: 兴趣爱好标签
    privacy_level:
      type: integer
      enum: [1, 2, 3]
      example: 2
      description: 隐私级别 1:公开 2:好友可见 3:仅自己可见
    status:
      type: integer
      enum: [1, 2, 3]
      example: 1
      description: 档案状态 1:正常 2:禁用 3:已删除
    created_at:
      type: string
      format: date-time
      example: "2025-01-01T10:30:00Z"
      description: 创建时间
    updated_at:
      type: string
      format: date-time
      example: "2025-07-05T10:30:00Z"
      description: 最后更新时间

# 打卡记录模型
CheckinRecord:
  type: object
  required:
    - id
    - user_id
    - child_id
    - checkin_date
    - checkin_type
    - status
    - created_at
  properties:
    id:
      type: integer
      format: int64
      example: 98765
      description: 打卡记录ID
    userId:
      type: integer
      format: int64
      example: 12345
      description: 用户ID
    childId:
      type: integer
      format: int64
      example: 67890
      description: 孩子ID
    checkinDate:
      type: string
      format: date
      example: "2025-07-05"
      description: 打卡日期
    checkinType:
      type: integer
      enum: [1, 2, 3, 4]
      example: 1
      description: 打卡类型 1:视频打卡 2:图片打卡 3:文字打卡 4:数据打卡
    title:
      type: string
      maxLength: 200
      example: "今天跳绳300个！"
      description: 打卡标题
    content:
      type: string
      maxLength: 1000
      example: "今天天气很好，在小区里跳绳，感觉越来越熟练了！"
      description: 打卡内容描述
    videoUrl:
      type: string
      format: uri
      example: "https://video.weixin.qq.com/wxv_123456"
      description: 打卡视频URL
    imageUrls:
      type: array
      items:
        type: string
        format: uri
      example: ["https://cdn.kids-platform.com/images/checkin1.jpg"]
      description: 打卡图片URLs
    exerciseDuration:
      type: integer
      minimum: 0
      example: 15
      description: 运动时长（分钟）
    exerciseCount:
      type: integer
      minimum: 0
      example: 300
      description: 运动次数（如跳绳次数）
    score:
      type: number
      format: float
      minimum: 0
      maximum: 100
      example: 85.5
      description: 打卡得分
    location:
      type: string
      maxLength: 100
      example: "北京市朝阳区某小区"
      description: 打卡地点
    shareToMoments:
      type: boolean
      example: true
      description: 是否分享到朋友圈
    momentsImageUrl:
      type: string
      format: uri
      example: "https://cdn.kids-platform.com/moments/screenshot123.jpg"
      description: 朋友圈分享截图URL
    likeCount:
      type: integer
      minimum: 0
      example: 15
      description: 点赞数量
    commentCount:
      type: integer
      minimum: 0
      example: 8
      description: 评论数量
    status:
      type: integer
      enum: [1, 2, 3, 4]
      example: 1
      description: 打卡状态 1:正常 2:已删除 3:被举报 4:审核中
    createdAt:
      type: string
      format: date-time
      example: "2025-07-05T10:30:00Z"
      description: 创建时间
    updatedAt:
      type: string
      format: date-time
      example: "2025-07-05T10:30:00Z"
      description: 更新时间

# 视频内容模型
Video:
  type: object
  required:
    - id
    - title
    - videoUrl
    - categoryId
    - status
    - createdAt
  properties:
    id:
      type: integer
      format: int64
      example: 54321
      description: 视频ID
    title:
      type: string
      maxLength: 200
      example: "跳绳基础教学 - 正摇跳"
      description: 视频标题
    description:
      type: string
      maxLength: 1000
      example: "适合初学者的跳绳基础动作教学，学会正摇跳的正确姿势"
      description: 视频描述
    videoUrl:
      type: string
      format: uri
      example: "https://video.weixin.qq.com/wxv_teaching_123"
      description: 视频播放URL
    thumbnailUrl:
      type: string
      format: uri
      example: "https://cdn.kids-platform.com/thumbnails/video123.jpg"
      description: 视频缩略图URL
    categoryId:
      type: integer
      format: int64
      example: 1001
      description: 视频分类ID
    tags:
      type: array
      items:
        type: string
      example: ["跳绳", "基础教学", "正摇跳"]
      description: 视频标签
    duration:
      type: integer
      minimum: 0
      example: 180
      description: 视频时长（秒）
    difficulty:
      type: integer
      enum: [1, 2, 3, 4, 5]
      example: 2
      description: 难度等级 1:入门 2:初级 3:中级 4:高级 5:专业
    ageRange:
      type: string
      example: "6-12"
      description: 适合年龄范围
    viewCount:
      type: integer
      minimum: 0
      example: 1250
      description: 播放次数
    likeCount:
      type: integer
      minimum: 0
      example: 89
      description: 点赞数量
    qualityScore:
      type: number
      format: float
      minimum: 0
      maximum: 100
      example: 92.5
      description: 内容质量评分
    status:
      type: integer
      enum: [1, 2, 3, 4]
      example: 1
      description: 视频状态 1:正常 2:下架 3:审核中 4:已删除
    createdAt:
      type: string
      format: date-time
      example: "2025-01-01T10:30:00Z"
      description: 创建时间
    updatedAt:
      type: string
      format: date-time
      example: "2025-07-05T10:30:00Z"
      description: 更新时间
