# 通用响应定义

# 成功响应
SuccessResponse:
  description: 操作成功
  content:
    application/json:
      schema:
        type: object
        required:
          - code
          - message
          - data
          - timestamp
        properties:
          code:
            type: integer
            example: 200
            description: 响应状态码
          message:
            type: string
            example: "操作成功"
            description: 响应消息
          data:
            type: object
            description: 响应数据
          timestamp:
            type: string
            format: date-time
            example: "2025-07-05T10:30:00Z"
            description: 响应时间戳

# 创建成功响应
CreatedResponse:
  description: 创建成功
  content:
    application/json:
      schema:
        type: object
        required:
          - code
          - message
          - data
          - timestamp
        properties:
          code:
            type: integer
            example: 201
            description: 响应状态码
          message:
            type: string
            example: "创建成功"
            description: 响应消息
          data:
            type: object
            description: 创建的资源数据
          timestamp:
            type: string
            format: date-time
            example: "2025-07-05T10:30:00Z"
            description: 响应时间戳

# 无内容响应
NoContentResponse:
  description: 操作成功，无返回内容
  content:
    application/json:
      schema:
        type: object
        required:
          - code
          - message
          - timestamp
        properties:
          code:
            type: integer
            example: 204
            description: 响应状态码
          message:
            type: string
            example: "操作成功"
            description: 响应消息
          timestamp:
            type: string
            format: date-time
            example: "2025-07-05T10:30:00Z"
            description: 响应时间戳

# 错误响应
BadRequest:
  description: 请求参数错误
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        validation_error:
          summary: 参数验证错误
          value:
            code: 400
            message: "请求参数错误"
            details: "nickname字段不能为空"
            timestamp: "2025-07-05T10:30:00Z"
        format_error:
          summary: 格式错误
          value:
            code: 400
            message: "请求格式错误"
            details: "JSON格式不正确"
            timestamp: "2025-07-05T10:30:00Z"

Unauthorized:
  description: 未授权访问
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        token_missing:
          summary: Token缺失
          value:
            code: 401
            message: "未授权访问"
            details: "请求头中缺少Authorization字段"
            timestamp: "2025-07-05T10:30:00Z"
        token_invalid:
          summary: Token无效
          value:
            code: 401
            message: "未授权访问"
            details: "Token已过期或无效"
            timestamp: "2025-07-05T10:30:00Z"
        token_expired:
          summary: Token过期
          value:
            code: 401
            message: "未授权访问"
            details: "Token已过期，请重新登录"
            timestamp: "2025-07-05T10:30:00Z"

Forbidden:
  description: 禁止访问
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        permission_denied:
          summary: 权限不足
          value:
            code: 403
            message: "禁止访问"
            details: "您没有权限执行此操作"
            timestamp: "2025-07-05T10:30:00Z"
        resource_forbidden:
          summary: 资源禁止访问
          value:
            code: 403
            message: "禁止访问"
            details: "该资源不允许访问"
            timestamp: "2025-07-05T10:30:00Z"

NotFound:
  description: 资源不存在
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        resource_not_found:
          summary: 资源未找到
          value:
            code: 404
            message: "资源不存在"
            details: "请求的资源未找到"
            timestamp: "2025-07-05T10:30:00Z"
        user_not_found:
          summary: 用户不存在
          value:
            code: 404
            message: "用户不存在"
            details: "指定的用户ID不存在"
            timestamp: "2025-07-05T10:30:00Z"

Conflict:
  description: 资源冲突
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        duplicate_resource:
          summary: 资源重复
          value:
            code: 409
            message: "资源冲突"
            details: "该资源已存在"
            timestamp: "2025-07-05T10:30:00Z"
        business_conflict:
          summary: 业务冲突
          value:
            code: 409
            message: "业务冲突"
            details: "当前操作与业务规则冲突"
            timestamp: "2025-07-05T10:30:00Z"

UnprocessableEntity:
  description: 请求格式正确但语义错误
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        business_validation:
          summary: 业务验证失败
          value:
            code: 422
            message: "请求无法处理"
            details: "业务规则验证失败"
            timestamp: "2025-07-05T10:30:00Z"

TooManyRequests:
  description: 请求过于频繁
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        rate_limit:
          summary: 频率限制
          value:
            code: 429
            message: "请求过于频繁"
            details: "请求频率超过限制，请稍后再试"
            timestamp: "2025-07-05T10:30:00Z"
    headers:
      Retry-After:
        description: 建议重试的秒数
        schema:
          type: integer
          example: 60

InternalServerError:
  description: 服务器内部错误
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        server_error:
          summary: 服务器错误
          value:
            code: 500
            message: "服务器内部错误"
            details: "服务器遇到了一个错误，请稍后重试"
            timestamp: "2025-07-05T10:30:00Z"

ServiceUnavailable:
  description: 服务不可用
  content:
    application/json:
      schema:
        $ref: '../schemas/common.yaml#/ErrorResponse'
      examples:
        maintenance:
          summary: 系统维护
          value:
            code: 503
            message: "服务不可用"
            details: "系统正在维护中，请稍后再试"
            timestamp: "2025-07-05T10:30:00Z"
    headers:
      Retry-After:
        description: 建议重试的秒数
        schema:
          type: integer
          example: 3600

# 分页响应
PaginatedResponse:
  description: 分页数据响应
  content:
    application/json:
      schema:
        type: object
        required:
          - code
          - message
          - data
          - pagination
          - timestamp
        properties:
          code:
            type: integer
            example: 200
            description: 响应状态码
          message:
            type: string
            example: "获取数据成功"
            description: 响应消息
          data:
            type: array
            items:
              type: object
            description: 数据列表
          pagination:
            $ref: '../schemas/common.yaml#/Pagination'
          timestamp:
            type: string
            format: date-time
            example: "2025-07-05T10:30:00Z"
            description: 响应时间戳

# 文件上传响应
FileUploadResponse:
  description: 文件上传成功
  content:
    application/json:
      schema:
        type: object
        required:
          - code
          - message
          - data
          - timestamp
        properties:
          code:
            type: integer
            example: 200
            description: 响应状态码
          message:
            type: string
            example: "文件上传成功"
            description: 响应消息
          data:
            type: object
            required:
              - fileUrl
              - fileSize
              - fileType
            properties:
              fileUrl:
                type: string
                format: uri
                example: "https://cdn.kids-platform.com/uploads/file_123456.jpg"
                description: 文件访问URL
              thumbnailUrl:
                type: string
                format: uri
                example: "https://cdn.kids-platform.com/thumbnails/file_123456_thumb.jpg"
                description: 缩略图URL（图片文件）
              fileSize:
                type: integer
                example: 1024000
                description: 文件大小（字节）
              fileType:
                type: string
                example: "image/jpeg"
                description: 文件MIME类型
              fileName:
                type: string
                example: "example.jpg"
                description: 原始文件名
              duration:
                type: integer
                example: 30
                description: 视频时长（秒，视频文件）
          timestamp:
            type: string
            format: date-time
            example: "2025-07-05T10:30:00Z"
            description: 响应时间戳
