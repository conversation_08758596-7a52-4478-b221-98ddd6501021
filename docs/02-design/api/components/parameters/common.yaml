# 通用参数定义

# 分页参数
PageParam:
  name: page
  in: query
  description: 页码，从1开始
  required: false
  schema:
    type: integer
    minimum: 1
    default: 1
    example: 1

SizeParam:
  name: size
  in: query
  description: 每页数量，最大100
  required: false
  schema:
    type: integer
    minimum: 1
    maximum: 100
    default: 20
    example: 20

# 排序参数
SortByParam:
  name: sortBy
  in: query
  description: 排序字段
  required: false
  schema:
    type: string
    default: "createdAt"
    example: "createdAt"

SortOrderParam:
  name: sortOrder
  in: query
  description: 排序方向
  required: false
  schema:
    type: string
    enum: ["asc", "desc"]
    default: "desc"
    example: "desc"

# 实体ID参数
UserIdParam:
  name: userId
  in: path
  description: 用户ID
  required: true
  schema:
    type: integer
    format: int64
    example: 12345

ChildIdParam:
  name: childId
  in: path
  description: 孩子ID
  required: true
  schema:
    type: integer
    format: int64
    example: 67890

CheckinIdParam:
  name: checkinId
  in: path
  description: 打卡记录ID
  required: true
  schema:
    type: integer
    format: int64
    example: 98765

VideoIdParam:
  name: videoId
  in: path
  description: 视频ID
  required: true
  schema:
    type: integer
    format: int64
    example: 54321

TaskIdParam:
  name: taskId
  in: path
  description: 任务ID
  required: true
  schema:
    type: integer
    format: int64
    example: 11111

# 日期范围参数
StartDateParam:
  name: startDate
  in: query
  description: 开始日期
  required: false
  schema:
    type: string
    format: date
    example: "2025-07-01"

EndDateParam:
  name: endDate
  in: query
  description: 结束日期
  required: false
  schema:
    type: string
    format: date
    example: "2025-07-05"

# 状态筛选参数
StatusParam:
  name: status
  in: query
  description: 状态筛选
  required: false
  schema:
    type: integer
    enum: [1, 2, 3]
    default: 1
    example: 1

# 搜索参数
KeywordParam:
  name: keyword
  in: query
  description: 搜索关键词
  required: false
  schema:
    type: string
    maxLength: 100
    example: "跳绳"

# 分类参数
CategoryIdParam:
  name: categoryId
  in: query
  description: 分类ID筛选
  required: false
  schema:
    type: integer
    format: int64
    example: 1001

# 难度参数
DifficultyParam:
  name: difficulty
  in: query
  description: 难度等级筛选
  required: false
  schema:
    type: integer
    enum: [1, 2, 3, 4, 5]
    example: 2

# 年龄范围参数
AgeRangeParam:
  name: ageRange
  in: query
  description: 适合年龄范围筛选
  required: false
  schema:
    type: string
    pattern: '^\d+-\d+$'
    example: "6-12"

# 标签参数
TagsParam:
  name: tags
  in: query
  description: 标签筛选，多个标签用逗号分隔
  required: false
  schema:
    type: string
    example: "跳绳,基础教学"

# 地理位置参数
LocationParam:
  name: location
  in: query
  description: 地理位置筛选
  required: false
  schema:
    type: string
    maxLength: 100
    example: "北京市朝阳区"

# 时间范围参数（用于统计查询）
TimeRangeParam:
  name: timeRange
  in: query
  description: 时间范围
  required: false
  schema:
    type: string
    enum: ["today", "week", "month", "year", "all"]
    default: "week"
    example: "week"

# 数据类型参数
DataTypeParam:
  name: dataType
  in: query
  description: 数据类型
  required: false
  schema:
    type: string
    enum: ["json", "csv", "excel"]
    default: "json"
    example: "json"

# 包含关联数据参数
IncludeParam:
  name: include
  in: query
  description: 包含的关联数据，多个用逗号分隔
  required: false
  schema:
    type: string
    example: "childInfo,userInfo,comments"

# 字段筛选参数
FieldsParam:
  name: fields
  in: query
  description: 返回的字段列表，多个用逗号分隔
  required: false
  schema:
    type: string
    example: "id,title,createdAt"

# 语言参数
LanguageParam:
  name: lang
  in: query
  description: 语言代码
  required: false
  schema:
    type: string
    enum: ["zh-CN", "zh-TW", "en-US"]
    default: "zh-CN"
    example: "zh-CN"

# 设备信息参数
DeviceTypeParam:
  name: deviceType
  in: query
  description: 设备类型
  required: false
  schema:
    type: string
    enum: ["wechat", "ios", "android", "web"]
    example: "wechat"

# 版本参数
VersionParam:
  name: version
  in: query
  description: API版本
  required: false
  schema:
    type: string
    pattern: '^\d+\.\d+\.\d+$'
    example: "1.0.0"

# 缓存控制参数
CacheParam:
  name: cache
  in: query
  description: 是否使用缓存
  required: false
  schema:
    type: boolean
    default: true
    example: true

# 调试参数
DebugParam:
  name: debug
  in: query
  description: 是否开启调试模式
  required: false
  schema:
    type: boolean
    default: false
    example: false
