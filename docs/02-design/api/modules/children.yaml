# 孩子管理模块API规范

paths:
  /children:
    get:
      tags:
        - 孩子管理
      summary: 获取孩子列表
      description: |
        获取当前用户管理的所有孩子列表。

        ## 业务规则
        - 返回用户有权限管理的所有孩子
        - 包含孩子的基础信息和统计数据
        - 自动过滤已软删除的孩子
        - 按创建时间倒序排列
      operationId: getChildrenList
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: limit
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
      responses:
        "200":
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取孩子列表成功"
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/ChildrenResponse"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

    post:
      tags:
        - 孩子管理
      summary: 创建孩子档案
      description: |
        为当前用户创建新的孩子档案。

        ## 业务规则
        - 自动建立用户与孩子的管理关系
        - 如果用户当前没有选择孩子，自动选择新创建的孩子
        - 支持设置与孩子的关系（爸爸、妈妈等）
        - 孩子姓名必填，其他信息可选
      operationId: createChild
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChildrenCreateRequest"
      responses:
        "200":
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "孩子档案创建成功"
                  data:
                    $ref: "#/components/schemas/ChildrenResponse"
        "400":
          $ref: "../openapi.yaml#/components/responses/BadRequest"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

  /children/current:
    get:
      tags:
        - 孩子管理
      summary: 获取当前选择的孩子
      description: |
        获取用户当前选择的孩子信息。

        ## 业务规则
        - 返回用户current_child_id对应的孩子信息
        - 如果用户未选择孩子或孩子不存在，返回404
        - 只能获取用户有权限管理的孩子
      operationId: getCurrentChild
      security:
        - BearerAuth: []
      responses:
        "200":
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取当前孩子成功"
                  data:
                    $ref: "#/components/schemas/ChildrenResponse"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "404":
          $ref: "../openapi.yaml#/components/responses/NotFound"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

  /children/{id}:
    get:
      tags:
        - 孩子管理
      summary: 获取孩子详情
      description: |
        获取指定孩子的详细信息。

        ## 业务规则
        - 只能获取用户有权限管理的孩子
        - 返回完整的孩子档案信息
        - 包含统计数据和学习记录
      operationId: getChildById
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: 孩子ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123
      responses:
        "200":
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取孩子信息成功"
                  data:
                    $ref: "#/components/schemas/ChildrenResponse"
        "400":
          $ref: "../openapi.yaml#/components/responses/BadRequest"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "403":
          $ref: "../openapi.yaml#/components/responses/Forbidden"
        "404":
          $ref: "../openapi.yaml#/components/responses/NotFound"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

    put:
      tags:
        - 孩子管理
      summary: 更新孩子信息
      description: |
        更新指定孩子的信息。

        ## 业务规则
        - 只能更新用户有权限管理的孩子
        - 支持部分字段更新
        - 姓名不能为空
        - 隐私设置和排行榜参与可以随时调整
      operationId: updateChild
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: 孩子ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChildrenUpdateRequest"
      responses:
        "200":
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "孩子信息更新成功"
                  data:
                    $ref: "#/components/schemas/ChildrenResponse"
        "400":
          $ref: "../openapi.yaml#/components/responses/BadRequest"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "403":
          $ref: "../openapi.yaml#/components/responses/Forbidden"
        "404":
          $ref: "../openapi.yaml#/components/responses/NotFound"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

    delete:
      tags:
        - 孩子管理
      summary: 删除孩子档案
      description: |
        软删除指定的孩子档案。

        ## 业务规则
        - 只能删除用户有权限管理的孩子
        - 执行软删除，不会物理删除数据
        - 如果是当前选择的孩子，会自动重置用户的current_child_id为0
        - 同时软删除相关的用户孩子关联记录
      operationId: deleteChild
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: 孩子ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123
      responses:
        "200":
          description: 删除成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "孩子档案删除成功"
        "400":
          $ref: "../openapi.yaml#/components/responses/BadRequest"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "403":
          $ref: "../openapi.yaml#/components/responses/Forbidden"
        "404":
          $ref: "../openapi.yaml#/components/responses/NotFound"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

  /children/{id}/select:
    post:
      tags:
        - 孩子管理
      summary: 选择当前孩子
      description: |
        设置用户当前选择的孩子。

        ## 业务规则
        - 只能选择用户有权限管理的孩子
        - 更新用户的current_child_id字段
        - 影响后续所有需要孩子信息的业务操作
        - 可以重复选择同一个孩子
      operationId: selectCurrentChild
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: 孩子ID
          required: true
          schema:
            type: integer
            format: int64
            example: 123
      responses:
        "200":
          description: 选择成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "当前孩子选择成功"
        "400":
          $ref: "../openapi.yaml#/components/responses/BadRequest"
        "401":
          $ref: "../openapi.yaml#/components/responses/Unauthorized"
        "403":
          $ref: "../openapi.yaml#/components/responses/Forbidden"
        "404":
          $ref: "../openapi.yaml#/components/responses/NotFound"
        "500":
          $ref: "../openapi.yaml#/components/responses/InternalServerError"

components:
  schemas:
    ChildrenResponse:
      type: object
      required:
        - id
        - name
        - gender
        - age
        - status
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
          example: 123
          description: 孩子ID
        name:
          type: string
          maxLength: 50
          example: "小明"
          description: 孩子姓名
        nickname:
          type: string
          maxLength: 50
          example: "明明"
          description: 孩子昵称
        gender:
          type: integer
          enum: [0, 1, 2]
          example: 1
          description: 性别 0:未知 1:男 2:女
        age:
          type: integer
          minimum: 0
          maximum: 18
          example: 8
          description: 年龄
        birth_date:
          type: string
          format: date
          example: "2015-06-15"
          description: 出生日期
        school:
          type: string
          maxLength: 100
          example: "北京市第一小学"
          description: 学校名称
        grade:
          type: string
          maxLength: 20
          example: "三年级2班"
          description: 年级班级
        province:
          type: string
          maxLength: 20
          example: "北京"
          description: 省份
        city:
          type: string
          maxLength: 20
          example: "北京市"
          description: 城市
        avatar:
          type: string
          format: uri
          example: "https://cdn.example.com/avatar.jpg"
          description: 头像URL
        skill_level:
          type: integer
          enum: [1, 2, 3, 4, 5]
          example: 2
          description: 技能水平 1:新手 2:初级 3:中级 4:高级 5:专业
        best_score_1min:
          type: integer
          minimum: 0
          example: 120
          description: 一分钟最佳成绩
        best_score_continuous:
          type: integer
          minimum: 0
          example: 300
          description: 连续跳最佳成绩
        preferred_difficulty:
          type: integer
          enum: [1, 2, 3, 4, 5]
          example: 2
          description: 偏好难度 1-5级
        learning_goals:
          type: string
          example: '["提高耐力","学会花式跳法"]'
          description: 学习目标JSON
        privacy_level:
          type: integer
          enum: [1, 2, 3]
          example: 2
          description: 隐私级别 1:公开 2:好友可见 3:仅自己
        show_in_leaderboard:
          type: integer
          enum: [0, 1]
          example: 1
          description: 是否参与排行榜 0:不参与 1:参与
        total_checkins:
          type: integer
          minimum: 0
          example: 45
          description: 总打卡次数
        total_points:
          type: integer
          format: int64
          minimum: 0
          example: 1250
          description: 总积分
        continuous_days:
          type: integer
          minimum: 0
          example: 7
          description: 连续打卡天数
        last_checkin_date:
          type: string
          format: date
          example: "2025-07-14"
          description: 最后打卡日期
        status:
          type: integer
          enum: [1, 2, 3]
          example: 1
          description: 档案状态 1:正常 2:暂停 3:删除
        created_at:
          type: string
          format: date-time
          example: "2025-07-01T08:00:00Z"
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          example: "2025-07-15T10:30:00Z"
          description: 更新时间
        is_current:
          type: boolean
          example: true
          description: 是否为当前选择的孩子
        relation:
          type: string
          maxLength: 20
          example: "爸爸"
          description: 与当前用户的关系

    ChildrenCreateRequest:
      type: object
      required:
        - name
        - relation
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          example: "小明"
          description: 孩子姓名
        nickname:
          type: string
          maxLength: 50
          example: "明明"
          description: 孩子昵称
        gender:
          type: integer
          enum: [0, 1, 2]
          example: 1
          description: 性别 0:未知 1:男 2:女
        birth_date:
          type: string
          format: date
          example: "2015-06-15"
          description: 出生日期 YYYY-MM-DD
        school:
          type: string
          maxLength: 100
          example: "北京市第一小学"
          description: 学校名称
        grade:
          type: string
          maxLength: 20
          example: "三年级2班"
          description: 年级班级
        province:
          type: string
          maxLength: 20
          example: "北京"
          description: 省份
        city:
          type: string
          maxLength: 20
          example: "北京市"
          description: 城市
        avatar:
          type: string
          format: uri
          example: "https://cdn.example.com/avatar.jpg"
          description: 头像URL
        relation:
          type: string
          minLength: 1
          maxLength: 20
          example: "爸爸"
          description: 与孩子的关系

    ChildrenUpdateRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 50
          example: "小明"
          description: 孩子姓名
        nickname:
          type: string
          maxLength: 50
          example: "明明"
          description: 孩子昵称
        gender:
          type: integer
          enum: [0, 1, 2]
          example: 1
          description: 性别 0:未知 1:男 2:女
        birth_date:
          type: string
          format: date
          example: "2015-06-15"
          description: 出生日期 YYYY-MM-DD
        school:
          type: string
          maxLength: 100
          example: "北京市第一小学"
          description: 学校名称
        grade:
          type: string
          maxLength: 20
          example: "三年级2班"
          description: 年级班级
        province:
          type: string
          maxLength: 20
          example: "北京"
          description: 省份
        city:
          type: string
          maxLength: 20
          example: "北京市"
          description: 城市
        avatar:
          type: string
          format: uri
          example: "https://cdn.example.com/avatar.jpg"
          description: 头像URL
        preferred_difficulty:
          type: integer
          enum: [1, 2, 3, 4, 5]
          example: 2
          description: 偏好难度 1-5级
        privacy_level:
          type: integer
          enum: [1, 2, 3]
          example: 2
          description: 隐私级别 1:公开 2:好友可见 3:仅自己
        show_in_leaderboard:
          type: integer
          enum: [0, 1]
          example: 1
          description: 是否参与排行榜 0:不参与 1:参与
