# 认证授权模块API规范

paths:
  /auth/register:
    post:
      tags:
        - 认证授权
      summary: 用户注册
      description: |
        新用户注册接口，支持手机号注册。
        
        ## 业务规则
        - 手机号必须是有效的中国大陆手机号
        - 验证码有效期为5分钟
        - 同一手机号24小时内最多发送5次验证码
        - 注册成功后自动登录，返回JWT Token
      operationId: registerUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - verificationCode
                - nickname
              properties:
                phone:
                  type: string
                  pattern: '^1[3-9]\d{9}$'
                  example: "13800138000"
                  description: 手机号码
                verificationCode:
                  type: string
                  pattern: '^\d{6}$'
                  example: "123456"
                  description: 短信验证码
                nickname:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "小明妈妈"
                  description: 用户昵称
                avatar:
                  type: string
                  format: uri
                  example: "https://cdn.kids-platform.com/avatars/default.jpg"
                  description: 用户头像URL（可选）
                inviteCode:
                  type: string
                  maxLength: 20
                  example: "INVITE123"
                  description: 邀请码（可选）
      responses:
        '200':
          description: 注册成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "注册成功"
                  data:
                    type: object
                    required:
                      - user
                      - token
                      - refreshToken
                    properties:
                      user:
                        $ref: '../components/schemas/common.yaml#/User'
                      token:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: JWT访问令牌，有效期24小时
                      refreshToken:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: 刷新令牌，有效期30天
                      expiresIn:
                        type: integer
                        example: 86400
                        description: Token有效期（秒）
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '409':
          description: 手机号已注册
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 409
                message: "手机号已注册"
                details: "该手机号已被其他用户注册，请直接登录"
                timestamp: "2025-07-05T10:30:00Z"

  /auth/login:
    post:
      tags:
        - 认证授权
      summary: 用户登录
      description: |
        用户登录接口，支持手机号+验证码登录。
        
        ## 业务规则
        - 支持手机号+验证码登录
        - 登录成功返回JWT Token和用户信息
        - Token有效期24小时，可通过refresh接口刷新
        - 连续登录失败5次将锁定账户1小时
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - phone
                - verificationCode
              properties:
                phone:
                  type: string
                  pattern: '^1[3-9]\d{9}$'
                  example: "13800138000"
                  description: 手机号码
                verificationCode:
                  type: string
                  pattern: '^\d{6}$'
                  example: "123456"
                  description: 短信验证码
                deviceInfo:
                  type: object
                  properties:
                    deviceId:
                      type: string
                      example: "device_123456"
                      description: 设备唯一标识
                    platform:
                      type: string
                      enum: ["wechat", "ios", "android", "web"]
                      example: "wechat"
                      description: 登录平台
                    version:
                      type: string
                      example: "1.0.0"
                      description: 应用版本
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "登录成功"
                  data:
                    type: object
                    required:
                      - user
                      - token
                      - refreshToken
                    properties:
                      user:
                        $ref: '../components/schemas/common.yaml#/User'
                      token:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: JWT访问令牌
                      refreshToken:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: 刷新令牌
                      expiresIn:
                        type: integer
                        example: 86400
                        description: Token有效期（秒）
                      isFirstLogin:
                        type: boolean
                        example: false
                        description: 是否首次登录
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          description: 登录失败
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "登录失败"
                details: "验证码错误或已过期"
                timestamp: "2025-07-05T10:30:00Z"
        '423':
          description: 账户被锁定
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 423
                message: "账户被锁定"
                details: "登录失败次数过多，账户已被锁定1小时"
                timestamp: "2025-07-05T10:30:00Z"

  /auth/logout:
    post:
      tags:
        - 认证授权
      summary: 用户登出
      description: |
        用户登出接口，将Token加入黑名单。
        
        ## 业务规则
        - 登出后Token立即失效
        - 清除服务端会话信息
        - 建议客户端同时清除本地存储的Token
      operationId: logoutUser
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/SuccessResponse'
              example:
                code: 200
                message: "登出成功"
                data: {}
                timestamp: "2025-07-05T10:30:00Z"
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'

  /auth/refresh:
    post:
      tags:
        - 认证授权
      summary: 刷新Token
      description: |
        使用刷新令牌获取新的访问令牌。
        
        ## 业务规则
        - 刷新令牌有效期30天
        - 每次刷新会生成新的访问令牌和刷新令牌
        - 旧的刷新令牌立即失效
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  description: 刷新令牌
      responses:
        '200':
          description: 刷新成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "Token刷新成功"
                  data:
                    type: object
                    required:
                      - token
                      - refreshToken
                    properties:
                      token:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: 新的访问令牌
                      refreshToken:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: 新的刷新令牌
                      expiresIn:
                        type: integer
                        example: 86400
                        description: Token有效期（秒）
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          description: 刷新令牌无效
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "刷新令牌无效"
                details: "刷新令牌已过期或不存在"
                timestamp: "2025-07-05T10:30:00Z"

  /auth/wechat:
    post:
      tags:
        - 认证授权
      summary: 微信小程序登录
      description: |
        微信小程序登录接口，使用微信授权码登录。
        
        ## 业务规则
        - 使用微信小程序wx.login()获取的code
        - 首次登录自动创建用户账户
        - 绑定微信OpenID和UnionID
        - 支持手机号授权绑定
      operationId: wechatLogin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - code
              properties:
                code:
                  type: string
                  example: "wx_code_123456"
                  description: 微信授权码
                encryptedData:
                  type: string
                  example: "encrypted_user_info"
                  description: 加密的用户信息（可选）
                iv:
                  type: string
                  example: "iv_string"
                  description: 初始向量（可选）
                phoneCode:
                  type: string
                  example: "phone_code_123"
                  description: 手机号授权码（可选）
      responses:
        '200':
          description: 微信登录成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "微信登录成功"
                  data:
                    type: object
                    required:
                      - user
                      - token
                      - refreshToken
                    properties:
                      user:
                        $ref: '../components/schemas/common.yaml#/User'
                      token:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: JWT访问令牌
                      refreshToken:
                        type: string
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        description: 刷新令牌
                      expiresIn:
                        type: integer
                        example: 86400
                        description: Token有效期（秒）
                      isNewUser:
                        type: boolean
                        example: true
                        description: 是否为新用户
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          description: 微信授权失败
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "微信授权失败"
                details: "无效的微信授权码"
                timestamp: "2025-07-05T10:30:00Z"
