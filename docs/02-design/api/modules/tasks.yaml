# 任务系统API模块
# 包含系统任务和团队任务的创建、参与和管理

# 系统任务管理
/tasks/system:
  get:
    tags:
      - Task System
    summary: 获取系统任务列表
    description: |
      获取可参与的系统任务列表，支持分类和状态筛选。
      
      业务规则：
      - 显示当前可参与的系统任务
      - 支持按分类、难度、状态筛选
      - 显示任务参与人数和奖励信息
      - 用户最多同时参与3个任务
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: category
        in: query
        description: 任务分类
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学, 综合]
      - name: difficulty
        in: query
        description: 任务难度
        schema:
          type: integer
          enum: [1, 2, 3, 4, 5]
          description: 1:入门 2:初级 3:中级 4:高级 5:专家
      - name: status
        in: query
        description: 任务状态
        schema:
          type: integer
          enum: [1, 2, 3]
          description: 1:进行中 2:即将开始 3:已结束
      - name: duration
        in: query
        description: 任务时长筛选
        schema:
          type: string
          enum: [short, medium, long]
          description: short:1-7天, medium:8-30天, long:>30天
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        tasks:
                          type: array
                          items:
                            $ref: '../components/schemas/common.yaml#/SystemTask'
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        userParticipatingCount:
                          type: integer
                          description: 用户当前参与的任务数量
            examples:
              success:
                summary: 成功获取系统任务列表
                value:
                  code: 200
                  message: "获取成功"
                  data:
                    tasks:
                      - id: 1
                        title: "21天跳绳挑战"
                        description: "连续21天每天跳绳，培养运动习惯"
                        category: "跳绳"
                        difficulty: 2
                        duration: 21
                        startDate: "2025-07-01T00:00:00Z"
                        endDate: "2025-07-21T23:59:59Z"
                        status: 1
                        participantCount: 1250
                        maxParticipants: 5000
                        rewards:
                          points: 500
                          badge: "跳绳达人"
                        requirements:
                          minCheckins: 21
                          minDuration: 300
                        isParticipating: false
                    pagination:
                      page: 1
                      size: 20
                      total: 45
                      totalPages: 3
                    userParticipatingCount: 2
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

/tasks/system/{taskId}:
  get:
    tags:
      - Task System
    summary: 获取系统任务详情
    description: |
      获取指定系统任务的详细信息，包括参与条件和进度统计。
      
      业务规则：
      - 显示任务详细描述和要求
      - 显示用户参与状态和进度
      - 显示排行榜前10名
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        task:
                          $ref: '../components/schemas/common.yaml#/SystemTask'
                        userProgress:
                          type: object
                          properties:
                            isParticipating:
                              type: boolean
                            joinDate:
                              type: string
                              format: date-time
                            checkinCount:
                              type: integer
                            completionRate:
                              type: number
                              format: float
                            currentRank:
                              type: integer
                        leaderboard:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                              user:
                                $ref: '../components/schemas/common.yaml#/User'
                              checkinCount:
                                type: integer
                              completionRate:
                                type: number
                                format: float
                          maxItems: 10
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

/tasks/system/{taskId}/join:
  post:
    tags:
      - Task System
    summary: 参与系统任务
    description: |
      用户参与指定的系统任务。
      
      业务规则：
      - 用户最多同时参与3个任务
      - 任务必须在开始时间前或进行中
      - 不能重复参与同一任务
      - 检查任务参与条件
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [childId]
            properties:
              childId:
                type: integer
                description: 参与任务的孩子ID
              motivation:
                type: string
                maxLength: 200
                description: 参与动机或目标
    responses:
      '201':
        description: 参与成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        participationId:
                          type: integer
                          description: 参与记录ID
                        joinDate:
                          type: string
                          format: date-time
                        task:
                          $ref: '../components/schemas/common.yaml#/SystemTask'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '409':
        description: 参与条件不满足
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
            examples:
              already_participating:
                summary: 已参与该任务
                value:
                  code: 409
                  message: "参与失败"
                  details: "您已经参与了这个任务"
              max_tasks_reached:
                summary: 达到最大参与数量
                value:
                  code: 409
                  message: "参与失败"
                  details: "您最多只能同时参与3个任务"
    security:
      - BearerAuth: []

/tasks/system/{taskId}/quit:
  post:
    tags:
      - Task System
    summary: 退出系统任务
    description: |
      用户退出已参与的系统任务。
      
      业务规则：
      - 只能退出正在参与的任务
      - 退出后不能重新参与同一任务
      - 已获得的进度和奖励保留
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    responses:
      '200':
        $ref: '../components/responses/common.yaml#/SuccessResponse'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

# 团队任务管理
/tasks/team:
  get:
    tags:
      - Task System
    summary: 获取团队任务列表
    description: |
      获取团队任务列表，包括用户创建的和参与的任务。
      
      业务规则：
      - 显示用户创建的团队任务
      - 显示用户参与的团队任务
      - 支持按状态和分类筛选
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: type
        in: query
        description: 任务类型
        schema:
          type: string
          enum: [created, joined, available]
          default: available
          description: created:我创建的, joined:我参与的, available:可参与的
      - name: category
        in: query
        description: 任务分类
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学, 综合]
      - name: status
        in: query
        description: 任务状态
        schema:
          type: integer
          enum: [1, 2, 3, 4]
          description: 1:招募中 2:进行中 3:已完成 4:已取消
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        tasks:
                          type: array
                          items:
                            $ref: '../components/schemas/common.yaml#/TeamTask'
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

  post:
    tags:
      - Task System
    summary: 创建团队任务
    description: |
      用户创建新的团队任务，邀请其他用户参与。
      
      业务规则：
      - 认证用户可创建团队任务
      - 任务创建者自动成为队长
      - 设置任务目标和奖励机制
      - 支持邀请码分享
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [title, category, startDate, endDate, maxMembers]
            properties:
              title:
                type: string
                maxLength: 50
                description: 任务标题
              description:
                type: string
                maxLength: 500
                description: 任务描述
              category:
                type: string
                enum: [跳绳, 魔术, 国学, 故事, 书法, 数学, 综合]
                description: 任务分类
              startDate:
                type: string
                format: date-time
                description: 开始时间
              endDate:
                type: string
                format: date-time
                description: 结束时间
              maxMembers:
                type: integer
                minimum: 2
                maximum: 50
                description: 最大成员数
              requirements:
                type: object
                properties:
                  minCheckins:
                    type: integer
                    minimum: 1
                    description: 最少打卡次数
                  minDuration:
                    type: integer
                    minimum: 60
                    description: 最少练习时长（秒）
                description: 任务要求
              isPublic:
                type: boolean
                default: true
                description: 是否公开（可通过搜索找到）
              inviteCode:
                type: string
                pattern: '^[A-Z0-9]{6}$'
                description: 自定义邀请码（6位大写字母数字）
    responses:
      '201':
        description: 创建成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        task:
                          $ref: '../components/schemas/common.yaml#/TeamTask'
                        inviteCode:
                          type: string
                          description: 邀请码
                        inviteUrl:
                          type: string
                          description: 邀请链接
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '409':
        description: 邀请码已存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

/tasks/team/{taskId}:
  get:
    tags:
      - Task System
    summary: 获取团队任务详情
    description: |
      获取指定团队任务的详细信息和成员列表。
      
      业务规则：
      - 显示任务详情和进度统计
      - 显示成员列表和各自进度
      - 任务创建者可查看管理信息
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        task:
                          $ref: '../components/schemas/common.yaml#/TeamTask'
                        members:
                          type: array
                          items:
                            type: object
                            properties:
                              user:
                                $ref: '../components/schemas/common.yaml#/User'
                              joinDate:
                                type: string
                                format: date-time
                              role:
                                type: string
                                enum: [leader, member]
                              progress:
                                type: object
                                properties:
                                  checkinCount:
                                    type: integer
                                  totalDuration:
                                    type: integer
                                  completionRate:
                                    type: number
                                    format: float
                        teamProgress:
                          type: object
                          properties:
                            totalCheckins:
                              type: integer
                            averageCompletion:
                              type: number
                              format: float
                            activeMembers:
                              type: integer
                        isCreator:
                          type: boolean
                          description: 当前用户是否为创建者
                        isMember:
                          type: boolean
                          description: 当前用户是否为成员
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

  put:
    tags:
      - Task System
    summary: 更新团队任务
    description: |
      更新团队任务信息（仅任务创建者）。
      
      业务规则：
      - 仅任务创建者可更新
      - 任务开始后不能修改核心参数
      - 可以更新描述和成员上限
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                maxLength: 50
              description:
                type: string
                maxLength: 500
              maxMembers:
                type: integer
                minimum: 2
                maximum: 50
              isPublic:
                type: boolean
    responses:
      '200':
        description: 更新成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/TeamTask'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

  delete:
    tags:
      - Task System
    summary: 取消团队任务
    description: |
      取消团队任务（仅任务创建者，且任务未开始）。
      
      业务规则：
      - 仅任务创建者可取消
      - 只能取消未开始的任务
      - 通知所有已加入的成员
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    responses:
      '200':
        $ref: '../components/responses/common.yaml#/SuccessResponse'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
      '409':
        description: 任务状态不允许取消
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

/tasks/team/{taskId}/join:
  post:
    tags:
      - Task System
    summary: 加入团队任务
    description: |
      用户加入指定的团队任务。
      
      业务规则：
      - 任务必须在招募中状态
      - 不能超过最大成员数
      - 不能重复加入同一任务
    parameters:
      - $ref: '../components/parameters/common.yaml#/TaskIdParam'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [childId]
            properties:
              childId:
                type: integer
                description: 参与任务的孩子ID
              inviteCode:
                type: string
                description: 邀请码（私有任务必需）
    responses:
      '201':
        description: 加入成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        membershipId:
                          type: integer
                        joinDate:
                          type: string
                          format: date-time
                        task:
                          $ref: '../components/schemas/common.yaml#/TeamTask'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        description: 需要邀请码或任务已满
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
      '409':
        description: 已加入该任务
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

/tasks/team/join-by-code:
  post:
    tags:
      - Task System
    summary: 通过邀请码加入团队任务
    description: |
      用户通过邀请码加入团队任务。
      
      业务规则：
      - 验证邀请码有效性
      - 检查任务状态和成员限制
      - 自动加入对应任务
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [inviteCode, childId]
            properties:
              inviteCode:
                type: string
                pattern: '^[A-Z0-9]{6}$'
                description: 6位邀请码
              childId:
                type: integer
                description: 参与任务的孩子ID
    responses:
      '201':
        description: 加入成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        task:
                          $ref: '../components/schemas/common.yaml#/TeamTask'
                        membershipId:
                          type: integer
                        joinDate:
                          type: string
                          format: date-time
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        description: 邀请码无效
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
      '409':
        description: 加入条件不满足
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []
