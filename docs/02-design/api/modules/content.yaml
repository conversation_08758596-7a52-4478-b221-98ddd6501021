# 内容管理和推荐系统API模块
# 包含教学视频、用户分享视频、播放列表和推荐算法

# 视频内容管理
/content/videos:
  get:
    tags:
      - Content Management
    summary: 获取视频列表
    description: |
      获取教学视频和用户分享视频列表，支持分类筛选、搜索和排序。
      
      业务规则：
      - 支持按分类、难度等级、时长等条件筛选
      - 支持关键词搜索（标题、描述、标签）
      - 支持多种排序方式（最新、最热、推荐）
      - 分页返回结果，默认每页20条
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: category
        in: query
        description: 视频分类
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
      - name: level
        in: query
        description: 难度等级
        schema:
          type: integer
          minimum: 1
          maximum: 5
      - name: type
        in: query
        description: 视频类型
        schema:
          type: integer
          enum: [1, 2]
          description: 1:教学视频 2:用户分享
      - name: keyword
        in: query
        description: 搜索关键词
        schema:
          type: string
          maxLength: 50
      - name: sortBy
        in: query
        description: 排序方式
        schema:
          type: string
          enum: [latest, popular, recommended]
          default: latest
      - name: duration
        in: query
        description: 视频时长筛选（秒）
        schema:
          type: string
          enum: [short, medium, long]
          description: short:<60s, medium:60-300s, long:>300s
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        videos:
                          type: array
                          items:
                            $ref: '../components/schemas/common.yaml#/Video'
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
            examples:
              success:
                summary: 成功获取视频列表
                value:
                  code: 200
                  message: "获取成功"
                  data:
                    videos:
                      - id: 1
                        title: "跳绳基础教学"
                        description: "适合初学者的跳绳基础动作教学"
                        category: "跳绳"
                        level: 1
                        type: 1
                        duration: 180
                        thumbnailUrl: "https://example.com/thumb1.jpg"
                        videoUrl: "https://example.com/video1.mp4"
                        viewCount: 1250
                        likeCount: 89
                        createdAt: "2025-07-05T10:00:00Z"
                    pagination:
                      page: 1
                      size: 20
                      total: 156
                      totalPages: 8
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

  post:
    tags:
      - Content Management
    summary: 上传视频内容
    description: |
      上传新的视频内容（仅管理员或内容创作者）。
      
      业务规则：
      - 仅管理员可上传教学视频
      - 认证用户可上传分享视频
      - 视频文件大小限制100MB
      - 支持的格式：MP4, MOV, AVI
      - 自动生成缩略图
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            required: [title, category, level, type, videoFile]
            properties:
              title:
                type: string
                maxLength: 100
                description: 视频标题
              description:
                type: string
                maxLength: 500
                description: 视频描述
              category:
                type: string
                enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
                description: 视频分类
              level:
                type: integer
                minimum: 1
                maximum: 5
                description: 难度等级
              type:
                type: integer
                enum: [1, 2]
                description: 视频类型 1:教学视频 2:用户分享
              tags:
                type: string
                maxLength: 200
                description: 标签，逗号分隔
              videoFile:
                type: string
                format: binary
                description: 视频文件
              thumbnailFile:
                type: string
                format: binary
                description: 缩略图文件（可选，不提供则自动生成）
    responses:
      '201':
        description: 上传成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/Video'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '413':
        description: 文件过大
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
            example:
              code: 413
              message: "文件大小超过限制"
              details: "视频文件不能超过100MB"
    security:
      - BearerAuth: []

/content/videos/{videoId}:
  get:
    tags:
      - Content Management
    summary: 获取视频详情
    description: |
      获取指定视频的详细信息，包括播放统计和相关推荐。
      
      业务规则：
      - 增加视频浏览次数
      - 记录用户观看历史
      - 返回相关推荐视频
    parameters:
      - $ref: '../components/parameters/common.yaml#/VideoIdParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        video:
                          $ref: '../components/schemas/common.yaml#/Video'
                        relatedVideos:
                          type: array
                          items:
                            $ref: '../components/schemas/common.yaml#/Video'
                          maxItems: 10
                        isLiked:
                          type: boolean
                          description: 当前用户是否已点赞
                        isFavorited:
                          type: boolean
                          description: 当前用户是否已收藏
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

  put:
    tags:
      - Content Management
    summary: 更新视频信息
    description: |
      更新视频的基本信息（仅视频创建者或管理员）。
      
      业务规则：
      - 视频创建者可编辑自己的视频
      - 管理员可编辑所有视频
      - 不能修改视频文件本身
    parameters:
      - $ref: '../components/parameters/common.yaml#/VideoIdParam'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                maxLength: 100
              description:
                type: string
                maxLength: 500
              category:
                type: string
                enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
              level:
                type: integer
                minimum: 1
                maximum: 5
              tags:
                type: string
                maxLength: 200
    responses:
      '200':
        description: 更新成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/Video'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

  delete:
    tags:
      - Content Management
    summary: 删除视频
    description: |
      删除指定视频（软删除，仅管理员）。
      
      业务规则：
      - 仅管理员可删除视频
      - 执行软删除，保留数据用于统计
      - 删除后用户无法访问
    parameters:
      - $ref: '../components/parameters/common.yaml#/VideoIdParam'
    responses:
      '200':
        $ref: '../components/responses/common.yaml#/SuccessResponse'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

# 播放列表管理
/content/playlists:
  get:
    tags:
      - Content Management
    summary: 获取播放列表
    description: |
      获取播放列表，包括系统推荐和用户创建的播放列表。
      
      业务规则：
      - 支持按分类和创建者筛选
      - 系统播放列表对所有用户可见
      - 用户播放列表根据隐私设置显示
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: category
        in: query
        description: 播放列表分类
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
      - name: type
        in: query
        description: 播放列表类型
        schema:
          type: integer
          enum: [1, 2]
          description: 1:系统推荐 2:用户创建
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        playlists:
                          type: array
                          items:
                            $ref: '../components/schemas/common.yaml#/Playlist'
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

  post:
    tags:
      - Content Management
    summary: 创建播放列表
    description: |
      创建新的播放列表。
      
      业务规则：
      - 认证用户可创建个人播放列表
      - 管理员可创建系统推荐播放列表
      - 播放列表名称不能重复
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [name, category]
            properties:
              name:
                type: string
                maxLength: 50
                description: 播放列表名称
              description:
                type: string
                maxLength: 200
                description: 播放列表描述
              category:
                type: string
                enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
                description: 播放列表分类
              isPublic:
                type: boolean
                default: true
                description: 是否公开
              videoIds:
                type: array
                items:
                  type: integer
                maxItems: 100
                description: 初始视频ID列表
    responses:
      '201':
        description: 创建成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/Playlist'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '409':
        description: 播放列表名称已存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

# 内容推荐
/content/recommendations:
  get:
    tags:
      - Content Management
    summary: 获取个性化推荐
    description: |
      基于用户行为和偏好获取个性化内容推荐。
      
      推荐算法：
      - 基于用户观看历史的协同过滤
      - 基于内容特征的相似度推荐
      - 考虑用户孩子的年龄和兴趣
      - 热门内容和新内容的平衡
    parameters:
      - name: type
        in: query
        description: 推荐类型
        schema:
          type: string
          enum: [personal, trending, new, category]
          default: personal
      - name: category
        in: query
        description: 指定分类推荐（当type=category时）
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
      - name: limit
        in: query
        description: 推荐数量限制
        schema:
          type: integer
          minimum: 1
          maximum: 50
          default: 20
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        recommendations:
                          type: array
                          items:
                            type: object
                            properties:
                              video:
                                $ref: '../components/schemas/common.yaml#/Video'
                              score:
                                type: number
                                format: float
                                description: 推荐分数
                              reason:
                                type: string
                                description: 推荐理由
                        algorithm:
                          type: string
                          description: 使用的推荐算法
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []
