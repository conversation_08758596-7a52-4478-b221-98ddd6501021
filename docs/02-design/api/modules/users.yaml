# 用户管理模块API规范

paths:
  /users/profile:
    get:
      tags:
        - 用户管理
      summary: 获取用户信息
      description: |
        获取当前登录用户的详细信息。
        
        ## 业务规则
        - 返回用户基础信息和统计数据
        - 隐私信息根据用户设置进行过滤
        - 包含用户的积分、等级等扩展信息
      operationId: getUserProfile
      security:
        - BearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取用户信息成功"
                  data:
                    allOf:
                      - $ref: '../components/schemas/common.yaml#/User'
                      - type: object
                        properties:
                          totalPoints:
                            type: integer
                            example: 1250
                            description: 总积分
                          level:
                            type: integer
                            example: 5
                            description: 用户等级
                          checkinDays:
                            type: integer
                            example: 45
                            description: 累计打卡天数
                          consecutiveDays:
                            type: integer
                            example: 7
                            description: 连续打卡天数
                          childrenCount:
                            type: integer
                            example: 2
                            description: 管理的孩子数量
                  timestamp:
                    type: string
                    format: date-time
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'

    put:
      tags:
        - 用户管理
      summary: 更新用户信息
      description: |
        更新当前登录用户的基础信息。
        
        ## 业务规则
        - 只能更新允许修改的字段
        - 手机号修改需要验证码确认
        - 头像上传需要先调用文件上传接口
      operationId: updateUserProfile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "小明妈妈"
                  description: 用户昵称
                avatar:
                  type: string
                  format: uri
                  example: "https://cdn.kids-platform.com/avatars/user123.jpg"
                  description: 用户头像URL
                gender:
                  type: integer
                  enum: [0, 1, 2]
                  example: 2
                  description: 性别 0:未知 1:男 2:女
                birthDate:
                  type: string
                  format: date
                  example: "1985-06-15"
                  description: 出生日期
                location:
                  type: string
                  maxLength: 100
                  example: "北京市朝阳区"
                  description: 所在地区
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "用户信息更新成功"
                  data:
                    $ref: '../components/schemas/common.yaml#/User'
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'

  /users/children:
    get:
      tags:
        - 用户管理
      summary: 获取孩子列表
      description: |
        获取当前用户管理的所有孩子档案列表。
        
        ## 业务规则
        - 返回用户有权限管理的所有孩子
        - 包含孩子的基础信息和统计数据
        - 支持按状态筛选
      operationId: getUserChildren
      security:
        - BearerAuth: []
      parameters:
        - name: status
          in: query
          description: 筛选状态
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            default: 1
            example: 1
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取孩子列表成功"
                  data:
                    type: array
                    items:
                      allOf:
                        - $ref: '../components/schemas/common.yaml#/Child'
                        - type: object
                          properties:
                            relationship:
                              type: string
                              example: "父子"
                              description: 与孩子的关系
                            totalCheckins:
                              type: integer
                              example: 30
                              description: 总打卡次数
                            consecutiveDays:
                              type: integer
                              example: 5
                              description: 连续打卡天数
                            lastCheckinDate:
                              type: string
                              format: date
                              example: "2025-07-04"
                              description: 最后打卡日期
                            age:
                              type: integer
                              example: 8
                              description: 年龄（自动计算）
                  timestamp:
                    type: string
                    format: date-time
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'

    post:
      tags:
        - 用户管理
      summary: 创建孩子档案
      description: |
        为当前用户创建新的孩子档案。
        
        ## 业务规则
        - 每个用户最多可以管理10个孩子档案
        - 孩子昵称在用户范围内不能重复
        - 创建后自动建立用户-孩子关联关系
      operationId: createChild
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - nickname
                - birthDate
                - gender
              properties:
                nickname:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "小明"
                  description: 孩子昵称
                realName:
                  type: string
                  maxLength: 50
                  example: "张小明"
                  description: 孩子真实姓名（可选）
                avatar:
                  type: string
                  format: uri
                  example: "https://cdn.kids-platform.com/avatars/child123.jpg"
                  description: 孩子头像URL（可选）
                birthDate:
                  type: string
                  format: date
                  example: "2015-03-20"
                  description: 出生日期
                gender:
                  type: integer
                  enum: [1, 2]
                  example: 1
                  description: 性别 1:男 2:女
                height:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 300
                  example: 120.5
                  description: 身高（厘米）
                weight:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 200
                  example: 25.8
                  description: 体重（公斤）
                grade:
                  type: string
                  maxLength: 20
                  example: "三年级"
                  description: 年级
                school:
                  type: string
                  maxLength: 100
                  example: "北京市第一小学"
                  description: 学校名称
                interests:
                  type: array
                  items:
                    type: string
                  example: ["跳绳", "篮球", "画画"]
                  description: 兴趣爱好标签
                relationship:
                  type: string
                  maxLength: 20
                  example: "父子"
                  description: 与孩子的关系
                privacyLevel:
                  type: integer
                  enum: [1, 2, 3]
                  default: 2
                  example: 2
                  description: 隐私级别 1:公开 2:好友可见 3:仅自己可见
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: "孩子档案创建成功"
                  data:
                    $ref: '../components/schemas/common.yaml#/Child'
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '409':
          description: 孩子昵称已存在
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 409
                message: "孩子昵称已存在"
                details: "您已经有一个叫'小明'的孩子档案"
                timestamp: "2025-07-05T10:30:00Z"

  /users/children/{childId}:
    get:
      tags:
        - 用户管理
      summary: 获取孩子详情
      description: |
        获取指定孩子的详细信息。
        
        ## 业务规则
        - 只能查看有权限管理的孩子信息
        - 返回孩子的完整档案和统计数据
        - 根据隐私设置过滤敏感信息
      operationId: getChildDetail
      security:
        - BearerAuth: []
      parameters:
        - $ref: '../openapi.yaml#/components/parameters/ChildIdParam'
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取孩子信息成功"
                  data:
                    allOf:
                      - $ref: '../components/schemas/common.yaml#/Child'
                      - type: object
                        properties:
                          relationship:
                            type: string
                            example: "父子"
                            description: 与孩子的关系
                          totalCheckins:
                            type: integer
                            example: 30
                            description: 总打卡次数
                          consecutiveDays:
                            type: integer
                            example: 5
                            description: 连续打卡天数
                          totalPoints:
                            type: integer
                            example: 850
                            description: 总积分
                          level:
                            type: integer
                            example: 3
                            description: 等级
                          achievements:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  example: 1001
                                name:
                                  type: string
                                  example: "连续打卡7天"
                                icon:
                                  type: string
                                  example: "https://cdn.kids-platform.com/achievements/7days.png"
                                obtainedAt:
                                  type: string
                                  format: date-time
                            description: 获得的成就列表
                          age:
                            type: integer
                            example: 8
                            description: 年龄（自动计算）
                  timestamp:
                    type: string
                    format: date-time
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../openapi.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'

    put:
      tags:
        - 用户管理
      summary: 更新孩子信息
      description: |
        更新指定孩子的档案信息。
        
        ## 业务规则
        - 只能更新有权限管理的孩子信息
        - 部分字段（如出生日期）修改有限制
        - 更新后会记录修改日志
      operationId: updateChild
      security:
        - BearerAuth: []
      parameters:
        - $ref: '../openapi.yaml#/components/parameters/ChildIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "小明"
                  description: 孩子昵称
                avatar:
                  type: string
                  format: uri
                  example: "https://cdn.kids-platform.com/avatars/child123.jpg"
                  description: 孩子头像URL
                height:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 300
                  example: 125.0
                  description: 身高（厘米）
                weight:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 200
                  example: 28.5
                  description: 体重（公斤）
                grade:
                  type: string
                  maxLength: 20
                  example: "四年级"
                  description: 年级
                school:
                  type: string
                  maxLength: 100
                  example: "北京市第一小学"
                  description: 学校名称
                interests:
                  type: array
                  items:
                    type: string
                  example: ["跳绳", "篮球", "画画", "编程"]
                  description: 兴趣爱好标签
                privacyLevel:
                  type: integer
                  enum: [1, 2, 3]
                  example: 2
                  description: 隐私级别 1:公开 2:好友可见 3:仅自己可见
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "孩子信息更新成功"
                  data:
                    $ref: '../components/schemas/common.yaml#/Child'
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../openapi.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'

    delete:
      tags:
        - 用户管理
      summary: 删除孩子档案
      description: |
        删除指定的孩子档案（软删除）。
        
        ## 业务规则
        - 只能删除有权限管理的孩子档案
        - 执行软删除，数据不会真正删除
        - 删除后相关的打卡记录等数据会被隐藏
        - 30天内可以恢复删除的档案
      operationId: deleteChild
      security:
        - BearerAuth: []
      parameters:
        - $ref: '../openapi.yaml#/components/parameters/ChildIdParam'
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/SuccessResponse'
              example:
                code: 200
                message: "孩子档案删除成功"
                data: {}
                timestamp: "2025-07-05T10:30:00Z"
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../openapi.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'
