# 排行榜系统API模块
# 包含个人排名、团队排行、分类排行等功能

# 个人排行榜
/leaderboard/personal:
  get:
    tags:
      - Leaderboard
    summary: 获取个人排行榜
    description: |
      获取个人积分和成就排行榜。
      
      业务规则：
      - 支持全站排行和分类排行
      - 支持不同时间周期的排行
      - 显示用户当前排名和周围排名
      - 实时更新排行数据
    parameters:
      - name: category
        in: query
        description: 排行榜分类
        schema:
          type: string
          enum: [all, 跳绳, 魔术, 国学, 故事, 书法, 数学]
          default: all
      - name: period
        in: query
        description: 排行周期
        schema:
          type: string
          enum: [daily, weekly, monthly, all_time]
          default: weekly
      - name: metric
        in: query
        description: 排行指标
        schema:
          type: string
          enum: [points, checkins, duration, achievements]
          default: points
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: showMyRank
        in: query
        description: 是否显示我的排名
        schema:
          type: boolean
          default: true
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        rankings:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                                description: 排名
                              user:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                  nickname:
                                    type: string
                                  avatar:
                                    type: string
                                  level:
                                    type: integer
                              score:
                                type: integer
                                description: 积分/指标值
                              change:
                                type: integer
                                description: 排名变化（正数上升，负数下降）
                              badge:
                                type: string
                                description: 特殊徽章
                              isCurrentUser:
                                type: boolean
                                description: 是否为当前用户
                        myRanking:
                          type: object
                          properties:
                            rank:
                              type: integer
                            score:
                              type: integer
                            change:
                              type: integer
                            percentile:
                              type: number
                              format: float
                              description: 百分位数
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        statistics:
                          type: object
                          properties:
                            totalParticipants:
                              type: integer
                            averageScore:
                              type: number
                              format: float
                            topScore:
                              type: integer
                            updateTime:
                              type: string
                              format: date-time
            examples:
              success:
                summary: 成功获取个人排行榜
                value:
                  code: 200
                  message: "获取成功"
                  data:
                    rankings:
                      - rank: 1
                        user:
                          id: 123
                          nickname: "跳绳小达人"
                          avatar: "https://example.com/avatar1.jpg"
                          level: 5
                        score: 2580
                        change: 2
                        badge: "周冠军"
                        isCurrentUser: false
                      - rank: 2
                        user:
                          id: 456
                          nickname: "运动小健将"
                          avatar: "https://example.com/avatar2.jpg"
                          level: 4
                        score: 2450
                        change: -1
                        badge: null
                        isCurrentUser: false
                    myRanking:
                      rank: 15
                      score: 1680
                      change: 3
                      percentile: 85.2
                    pagination:
                      page: 1
                      size: 20
                      total: 1250
                      totalPages: 63
                    statistics:
                      totalParticipants: 1250
                      averageScore: 856.5
                      topScore: 2580
                      updateTime: "2025-07-06T10:30:00Z"
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 团队排行榜
/leaderboard/teams:
  get:
    tags:
      - Leaderboard
    summary: 获取团队排行榜
    description: |
      获取团队任务的排行榜数据。
      
      业务规则：
      - 显示团队总积分和平均积分排行
      - 支持按任务分类筛选
      - 显示团队成员贡献度
      - 实时更新团队排名
    parameters:
      - name: category
        in: query
        description: 任务分类
        schema:
          type: string
          enum: [all, 跳绳, 魔术, 国学, 故事, 书法, 数学, 综合]
          default: all
      - name: period
        in: query
        description: 排行周期
        schema:
          type: string
          enum: [current, weekly, monthly, all_time]
          default: current
      - name: metric
        in: query
        description: 排行指标
        schema:
          type: string
          enum: [total_score, average_score, completion_rate, active_members]
          default: total_score
      - name: status
        in: query
        description: 团队状态
        schema:
          type: integer
          enum: [1, 2, 3]
          description: 1:招募中 2:进行中 3:已完成
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        rankings:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                              team:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                  name:
                                    type: string
                                  category:
                                    type: string
                                  memberCount:
                                    type: integer
                                  leader:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                      nickname:
                                        type: string
                                      avatar:
                                        type: string
                              score:
                                type: number
                                format: float
                                description: 团队得分
                              completionRate:
                                type: number
                                format: float
                                description: 完成率
                              activeMembers:
                                type: integer
                                description: 活跃成员数
                              change:
                                type: integer
                                description: 排名变化
                              isMyTeam:
                                type: boolean
                                description: 是否为当前用户的团队
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        myTeams:
                          type: array
                          items:
                            type: object
                            properties:
                              teamId:
                                type: integer
                              rank:
                                type: integer
                              score:
                                type: number
                                format: float
                          description: 我参与的团队排名
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 分类专项排行榜
/leaderboard/categories/{category}:
  get:
    tags:
      - Leaderboard
    summary: 获取分类专项排行榜
    description: |
      获取特定分类的专项排行榜，如跳绳专项排行。
      
      业务规则：
      - 显示该分类的专业排行数据
      - 包含分类特有的指标和成就
      - 支持多维度排行（技能等级、练习时长等）
      - 显示分类达人和专家用户
    parameters:
      - name: category
        in: path
        required: true
        description: 分类名称
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
      - name: metric
        in: query
        description: 排行指标
        schema:
          type: string
          enum: [skill_level, practice_time, achievements, consistency]
          default: skill_level
      - name: period
        in: query
        description: 排行周期
        schema:
          type: string
          enum: [daily, weekly, monthly, all_time]
          default: monthly
      - name: level
        in: query
        description: 技能等级筛选
        schema:
          type: integer
          minimum: 1
          maximum: 5
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        category:
                          type: string
                          description: 分类名称
                        rankings:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                              user:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                  nickname:
                                    type: string
                                  avatar:
                                    type: string
                                  categoryLevel:
                                    type: integer
                                    description: 该分类的技能等级
                              metrics:
                                type: object
                                properties:
                                  skillLevel:
                                    type: integer
                                  practiceTime:
                                    type: integer
                                    description: 总练习时长（分钟）
                                  achievementCount:
                                    type: integer
                                  consistencyDays:
                                    type: integer
                                    description: 连续练习天数
                              specialBadges:
                                type: array
                                items:
                                  type: string
                                description: 分类专属徽章
                              change:
                                type: integer
                              isCurrentUser:
                                type: boolean
                        myRanking:
                          type: object
                          properties:
                            rank:
                              type: integer
                            metrics:
                              type: object
                            nextLevelRequirement:
                              type: object
                              properties:
                                currentLevel:
                                  type: integer
                                nextLevel:
                                  type: integer
                                requirement:
                                  type: string
                                progress:
                                  type: number
                                  format: float
                        categoryStats:
                          type: object
                          properties:
                            totalUsers:
                              type: integer
                            averageLevel:
                              type: number
                              format: float
                            topPerformers:
                              type: integer
                              description: 顶级表现者数量
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        description: 分类不存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

# 成就排行榜
/leaderboard/achievements:
  get:
    tags:
      - Leaderboard
    summary: 获取成就排行榜
    description: |
      获取用户成就和徽章的排行榜。
      
      业务规则：
      - 按成就数量和稀有度排行
      - 显示特殊成就和限时徽章
      - 支持成就分类筛选
      - 展示成就获得时间线
    parameters:
      - name: type
        in: query
        description: 成就类型
        schema:
          type: string
          enum: [all, rare, recent, category_specific]
          default: all
      - name: category
        in: query
        description: 成就分类
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学, 综合, 社交]
      - name: rarity
        in: query
        description: 稀有度筛选
        schema:
          type: string
          enum: [common, rare, epic, legendary]
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        rankings:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                              user:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                  nickname:
                                    type: string
                                  avatar:
                                    type: string
                              achievements:
                                type: object
                                properties:
                                  total:
                                    type: integer
                                  rare:
                                    type: integer
                                  epic:
                                    type: integer
                                  legendary:
                                    type: integer
                              recentAchievements:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                    name:
                                      type: string
                                    icon:
                                      type: string
                                    rarity:
                                      type: string
                                    earnedAt:
                                      type: string
                                      format: date-time
                                maxItems: 3
                              achievementScore:
                                type: integer
                                description: 成就总分
                              change:
                                type: integer
                              isCurrentUser:
                                type: boolean
                        myAchievements:
                          type: object
                          properties:
                            rank:
                              type: integer
                            total:
                              type: integer
                            score:
                              type: integer
                            recentEarned:
                              type: array
                              items:
                                type: object
                              maxItems: 5
                        featuredAchievements:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                              name:
                                type: string
                              description:
                                type: string
                              icon:
                                type: string
                              rarity:
                                type: string
                              requirement:
                                type: string
                              earnedBy:
                                type: integer
                                description: 获得人数
                          maxItems: 5
                          description: 精选成就展示
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 历史排行榜
/leaderboard/history:
  get:
    tags:
      - Leaderboard
    summary: 获取历史排行榜
    description: |
      获取历史时期的排行榜数据和趋势分析。
      
      业务规则：
      - 显示过往周期的排行榜冠军
      - 提供排名趋势分析
      - 支持历史数据对比
      - 展示里程碑时刻
    parameters:
      - name: type
        in: query
        required: true
        description: 排行榜类型
        schema:
          type: string
          enum: [personal, teams, categories, achievements]
      - name: period
        in: query
        description: 历史周期
        schema:
          type: string
          enum: [last_week, last_month, last_quarter, last_year]
          default: last_month
      - name: category
        in: query
        description: 分类筛选（当type为categories时）
        schema:
          type: string
          enum: [跳绳, 魔术, 国学, 故事, 书法, 数学]
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        period:
                          type: object
                          properties:
                            startDate:
                              type: string
                              format: date
                            endDate:
                              type: string
                              format: date
                            description:
                              type: string
                        champions:
                          type: array
                          items:
                            type: object
                            properties:
                              rank:
                                type: integer
                              user:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                  nickname:
                                    type: string
                                  avatar:
                                    type: string
                              score:
                                type: integer
                              achievement:
                                type: string
                                description: 获得的特殊成就
                          maxItems: 10
                        trends:
                          type: array
                          items:
                            type: object
                            properties:
                              userId:
                                type: integer
                              nickname:
                                type: string
                              rankHistory:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    date:
                                      type: string
                                      format: date
                                    rank:
                                      type: integer
                                    score:
                                      type: integer
                              trend:
                                type: string
                                enum: [rising, falling, stable]
                        milestones:
                          type: array
                          items:
                            type: object
                            properties:
                              date:
                                type: string
                                format: date
                              event:
                                type: string
                              description:
                                type: string
                              participants:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    id:
                                      type: integer
                                    nickname:
                                      type: string
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 排行榜统计
/leaderboard/stats:
  get:
    tags:
      - Leaderboard
    summary: 获取排行榜统计数据
    description: |
      获取排行榜的整体统计数据和分析报告。
      
      业务规则：
      - 提供全站排行榜概览
      - 显示各分类参与度统计
      - 分析用户活跃度趋势
      - 生成排行榜洞察报告
    parameters:
      - name: period
        in: query
        description: 统计周期
        schema:
          type: string
          enum: [daily, weekly, monthly, quarterly, yearly]
          default: weekly
      - name: includeHistory
        in: query
        description: 是否包含历史对比
        schema:
          type: boolean
          default: false
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        overview:
                          type: object
                          properties:
                            totalParticipants:
                              type: integer
                            activeUsers:
                              type: integer
                            totalScore:
                              type: integer
                            averageScore:
                              type: number
                              format: float
                        categoryStats:
                          type: array
                          items:
                            type: object
                            properties:
                              category:
                                type: string
                              participants:
                                type: integer
                              averageScore:
                                type: number
                                format: float
                              topScore:
                                type: integer
                              growthRate:
                                type: number
                                format: float
                        trends:
                          type: object
                          properties:
                            participationTrend:
                              type: array
                              items:
                                type: object
                                properties:
                                  date:
                                    type: string
                                    format: date
                                  count:
                                    type: integer
                            scoreTrend:
                              type: array
                              items:
                                type: object
                                properties:
                                  date:
                                    type: string
                                    format: date
                                  averageScore:
                                    type: number
                                    format: float
                        insights:
                          type: array
                          items:
                            type: object
                            properties:
                              type:
                                type: string
                                enum: [growth, decline, milestone, anomaly]
                              title:
                                type: string
                              description:
                                type: string
                              impact:
                                type: string
                                enum: [high, medium, low]
                        updateTime:
                          type: string
                          format: date-time
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []
