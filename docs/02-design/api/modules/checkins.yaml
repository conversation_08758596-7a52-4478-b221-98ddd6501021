# 打卡系统模块API规范

paths:
  /checkins:
    get:
      tags:
        - 打卡系统
      summary: 获取打卡记录列表
      description: |
        获取打卡记录列表，支持多种筛选和排序方式。
        
        ## 业务规则
        - 默认返回当前用户管理的所有孩子的打卡记录
        - 支持按孩子、日期范围、打卡类型等筛选
        - 支持按时间、点赞数等排序
        - 分页返回结果
      operationId: getCheckinRecords
      security:
        - BearerAuth: []
      parameters:
        - $ref: '../openapi.yaml#/components/parameters/PageParam'
        - $ref: '../openapi.yaml#/components/parameters/SizeParam'
        - name: childId
          in: query
          description: 孩子ID筛选
          required: false
          schema:
            type: integer
            format: int64
            example: 67890
        - name: startDate
          in: query
          description: 开始日期
          required: false
          schema:
            type: string
            format: date
            example: "2025-07-01"
        - name: endDate
          in: query
          description: 结束日期
          required: false
          schema:
            type: string
            format: date
            example: "2025-07-05"
        - name: checkinType
          in: query
          description: 打卡类型筛选
          required: false
          schema:
            type: integer
            enum: [1, 2, 3, 4]
            example: 1
        - name: sortBy
          in: query
          description: 排序字段
          required: false
          schema:
            type: string
            enum: ["createdAt", "likeCount", "score"]
            default: "createdAt"
            example: "createdAt"
        - name: sortOrder
          in: query
          description: 排序方向
          required: false
          schema:
            type: string
            enum: ["asc", "desc"]
            default: "desc"
            example: "desc"
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - pagination
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取打卡记录成功"
                  data:
                    type: array
                    items:
                      allOf:
                        - $ref: '../components/schemas/common.yaml#/CheckinRecord'
                        - type: object
                          properties:
                            childInfo:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  format: int64
                                  example: 67890
                                nickname:
                                  type: string
                                  example: "小明"
                                avatar:
                                  type: string
                                  format: uri
                                  example: "https://cdn.kids-platform.com/avatars/child123.jpg"
                              description: 孩子基础信息
                            isLiked:
                              type: boolean
                              example: true
                              description: 当前用户是否已点赞
                            canEdit:
                              type: boolean
                              example: true
                              description: 是否可以编辑
                  pagination:
                    $ref: '../openapi.yaml#/components/schemas/Pagination'
                  timestamp:
                    type: string
                    format: date-time
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'

    post:
      tags:
        - 打卡系统
      summary: 创建打卡记录
      description: |
        创建新的打卡记录。
        
        ## 业务规则
        - 每个孩子每天只能创建一条打卡记录
        - 支持多种打卡类型：视频、图片、文字、数据
        - 视频打卡需要先上传视频文件
        - 可以同时分享到朋友圈
      operationId: createCheckinRecord
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - childId
                - checkinType
                - title
              properties:
                childId:
                  type: integer
                  format: int64
                  example: 67890
                  description: 孩子ID
                checkinType:
                  type: integer
                  enum: [1, 2, 3, 4]
                  example: 1
                  description: 打卡类型 1:视频打卡 2:图片打卡 3:文字打卡 4:数据打卡
                title:
                  type: string
                  minLength: 1
                  maxLength: 200
                  example: "今天跳绳300个！"
                  description: 打卡标题
                content:
                  type: string
                  maxLength: 1000
                  example: "今天天气很好，在小区里跳绳，感觉越来越熟练了！"
                  description: 打卡内容描述
                videoUrl:
                  type: string
                  format: uri
                  example: "https://video.weixin.qq.com/wxv_123456"
                  description: 打卡视频URL（视频打卡必填）
                imageUrls:
                  type: array
                  items:
                    type: string
                    format: uri
                  example: ["https://cdn.kids-platform.com/images/checkin1.jpg"]
                  description: 打卡图片URLs
                exerciseDuration:
                  type: integer
                  minimum: 0
                  example: 15
                  description: 运动时长（分钟）
                exerciseCount:
                  type: integer
                  minimum: 0
                  example: 300
                  description: 运动次数（如跳绳次数）
                score:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 100
                  example: 85.5
                  description: 打卡得分
                location:
                  type: string
                  maxLength: 100
                  example: "北京市朝阳区某小区"
                  description: 打卡地点
                shareToMoments:
                  type: boolean
                  default: false
                  example: true
                  description: 是否分享到朋友圈
                momentsTemplate:
                  type: string
                  enum: ["template1", "template2", "template3"]
                  example: "template1"
                  description: 朋友圈分享模板（分享时必填）
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: "打卡记录创建成功"
                  data:
                    allOf:
                      - $ref: '../components/schemas/common.yaml#/CheckinRecord'
                      - type: object
                        properties:
                          pointsEarned:
                            type: integer
                            example: 10
                            description: 本次打卡获得的积分
                          consecutiveDays:
                            type: integer
                            example: 8
                            description: 连续打卡天数
                          achievements:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  example: 1001
                                name:
                                  type: string
                                  example: "连续打卡7天"
                                points:
                                  type: integer
                                  example: 50
                            description: 本次打卡获得的成就
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '409':
          description: 今日已打卡
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 409
                message: "今日已打卡"
                details: "该孩子今天已经完成打卡，请明天再来"
                timestamp: "2025-07-05T10:30:00Z"

  /checkins/{checkinId}:
    get:
      tags:
        - 打卡系统
      summary: 获取打卡记录详情
      description: |
        获取指定打卡记录的详细信息。
        
        ## 业务规则
        - 返回打卡记录的完整信息
        - 包含点赞、评论等社交互动数据
        - 根据权限控制显示内容
      operationId: getCheckinDetail
      security:
        - BearerAuth: []
      parameters:
        - name: checkinId
          in: path
          description: 打卡记录ID
          required: true
          schema:
            type: integer
            format: int64
            example: 98765
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取打卡详情成功"
                  data:
                    allOf:
                      - $ref: '../components/schemas/common.yaml#/CheckinRecord'
                      - type: object
                        properties:
                          childInfo:
                            type: object
                            properties:
                              id:
                                type: integer
                                format: int64
                                example: 67890
                              nickname:
                                type: string
                                example: "小明"
                              avatar:
                                type: string
                                format: uri
                                example: "https://cdn.kids-platform.com/avatars/child123.jpg"
                              age:
                                type: integer
                                example: 8
                            description: 孩子信息
                          userInfo:
                            type: object
                            properties:
                              id:
                                type: integer
                                format: int64
                                example: 12345
                              nickname:
                                type: string
                                example: "小明妈妈"
                              avatar:
                                type: string
                                format: uri
                                example: "https://cdn.kids-platform.com/avatars/user123.jpg"
                            description: 用户信息
                          isLiked:
                            type: boolean
                            example: true
                            description: 当前用户是否已点赞
                          canEdit:
                            type: boolean
                            example: true
                            description: 是否可以编辑
                          canDelete:
                            type: boolean
                            example: true
                            description: 是否可以删除
                          recentComments:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  format: int64
                                  example: 11111
                                content:
                                  type: string
                                  example: "跳得真棒！"
                                userNickname:
                                  type: string
                                  example: "小红妈妈"
                                createdAt:
                                  type: string
                                  format: date-time
                            description: 最近的评论（最多5条）
                  timestamp:
                    type: string
                    format: date-time
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'

    put:
      tags:
        - 打卡系统
      summary: 更新打卡记录
      description: |
        更新指定的打卡记录。
        
        ## 业务规则
        - 只能更新自己创建的打卡记录
        - 打卡日期不能修改
        - 更新后会记录修改日志
      operationId: updateCheckinRecord
      security:
        - BearerAuth: []
      parameters:
        - name: checkinId
          in: path
          description: 打卡记录ID
          required: true
          schema:
            type: integer
            format: int64
            example: 98765
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  minLength: 1
                  maxLength: 200
                  example: "今天跳绳350个！"
                  description: 打卡标题
                content:
                  type: string
                  maxLength: 1000
                  example: "今天比昨天多跳了50个，进步很大！"
                  description: 打卡内容描述
                exerciseDuration:
                  type: integer
                  minimum: 0
                  example: 18
                  description: 运动时长（分钟）
                exerciseCount:
                  type: integer
                  minimum: 0
                  example: 350
                  description: 运动次数
                score:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 100
                  example: 88.0
                  description: 打卡得分
                location:
                  type: string
                  maxLength: 100
                  example: "北京市朝阳区某小区"
                  description: 打卡地点
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "打卡记录更新成功"
                  data:
                    $ref: '../components/schemas/common.yaml#/CheckinRecord'
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../openapi.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'

    delete:
      tags:
        - 打卡系统
      summary: 删除打卡记录
      description: |
        删除指定的打卡记录（软删除）。
        
        ## 业务规则
        - 只能删除自己创建的打卡记录
        - 执行软删除，数据不会真正删除
        - 删除后相关的点赞、评论等数据会被隐藏
      operationId: deleteCheckinRecord
      security:
        - BearerAuth: []
      parameters:
        - name: checkinId
          in: path
          description: 打卡记录ID
          required: true
          schema:
            type: integer
            format: int64
            example: 98765
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/SuccessResponse'
              example:
                code: 200
                message: "打卡记录删除成功"
                data: {}
                timestamp: "2025-07-05T10:30:00Z"
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '403':
          $ref: '../openapi.yaml#/components/responses/Forbidden'
        '404':
          $ref: '../openapi.yaml#/components/responses/NotFound'

  /checkins/upload:
    post:
      tags:
        - 打卡系统
      summary: 上传打卡媒体文件
      description: |
        上传打卡相关的媒体文件（图片、视频）。
        
        ## 业务规则
        - 支持图片格式：JPG、PNG、GIF
        - 支持视频格式：MP4、MOV
        - 单个文件最大50MB
        - 图片会自动压缩和生成缩略图
        - 返回文件URL用于创建打卡记录
      operationId: uploadCheckinMedia
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
                - fileType
              properties:
                file:
                  type: string
                  format: binary
                  description: 要上传的文件
                fileType:
                  type: string
                  enum: ["image", "video"]
                  example: "image"
                  description: 文件类型
                childId:
                  type: integer
                  format: int64
                  example: 67890
                  description: 关联的孩子ID（可选）
      responses:
        '200':
          description: 上传成功
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - message
                  - data
                  - timestamp
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "文件上传成功"
                  data:
                    type: object
                    required:
                      - fileUrl
                      - fileSize
                      - fileType
                    properties:
                      fileUrl:
                        type: string
                        format: uri
                        example: "https://cdn.kids-platform.com/uploads/checkin_20250705_123456.jpg"
                        description: 文件访问URL
                      thumbnailUrl:
                        type: string
                        format: uri
                        example: "https://cdn.kids-platform.com/thumbnails/checkin_20250705_123456_thumb.jpg"
                        description: 缩略图URL（图片文件）
                      fileSize:
                        type: integer
                        example: 1024000
                        description: 文件大小（字节）
                      fileType:
                        type: string
                        example: "image/jpeg"
                        description: 文件MIME类型
                      duration:
                        type: integer
                        example: 30
                        description: 视频时长（秒，视频文件）
                  timestamp:
                    type: string
                    format: date-time
        '400':
          $ref: '../openapi.yaml#/components/responses/BadRequest'
        '401':
          $ref: '../openapi.yaml#/components/responses/Unauthorized'
        '413':
          description: 文件过大
          content:
            application/json:
              schema:
                $ref: '../openapi.yaml#/components/schemas/ErrorResponse'
              example:
                code: 413
                message: "文件过大"
                details: "文件大小超过50MB限制"
                timestamp: "2025-07-05T10:30:00Z"
