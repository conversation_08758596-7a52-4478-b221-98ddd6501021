# 社交互动API模块
# 包含点赞、评论、关注、分享等社交功能

# 点赞系统
/social/likes:
  post:
    tags:
      - Social Features
    summary: 点赞/取消点赞
    description: |
      对内容进行点赞或取消点赞操作。
      
      业务规则：
      - 支持对打卡记录、视频、评论点赞
      - 重复点赞则取消点赞
      - 记录点赞历史和统计
      - 实时更新点赞数量
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [targetType, targetId]
            properties:
              targetType:
                type: string
                enum: [checkin, video, comment]
                description: 点赞目标类型
              targetId:
                type: integer
                description: 目标内容ID
    responses:
      '200':
        description: 操作成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        isLiked:
                          type: boolean
                          description: 当前点赞状态
                        likeCount:
                          type: integer
                          description: 总点赞数
                        action:
                          type: string
                          enum: [liked, unliked]
                          description: 执行的操作
            examples:
              liked:
                summary: 点赞成功
                value:
                  code: 200
                  message: "操作成功"
                  data:
                    isLiked: true
                    likeCount: 25
                    action: "liked"
              unliked:
                summary: 取消点赞成功
                value:
                  code: 200
                  message: "操作成功"
                  data:
                    isLiked: false
                    likeCount: 24
                    action: "unliked"
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        description: 目标内容不存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

  get:
    tags:
      - Social Features
    summary: 获取用户点赞历史
    description: |
      获取用户的点赞历史记录。
      
      业务规则：
      - 按时间倒序返回点赞记录
      - 支持按内容类型筛选
      - 分页返回结果
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: targetType
        in: query
        description: 筛选点赞目标类型
        schema:
          type: string
          enum: [checkin, video, comment]
      - name: startDate
        in: query
        description: 开始日期
        schema:
          type: string
          format: date
      - name: endDate
        in: query
        description: 结束日期
        schema:
          type: string
          format: date
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        likes:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                              targetType:
                                type: string
                                enum: [checkin, video, comment]
                              targetId:
                                type: integer
                              createdAt:
                                type: string
                                format: date-time
                              target:
                                type: object
                                description: 被点赞的内容摘要
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 评论系统
/social/comments:
  post:
    tags:
      - Social Features
    summary: 发表评论
    description: |
      对内容发表评论或回复其他评论。
      
      业务规则：
      - 支持对打卡记录、视频发表评论
      - 支持回复其他用户的评论
      - 评论内容审核和过滤
      - 支持@提及其他用户
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [targetType, targetId, content]
            properties:
              targetType:
                type: string
                enum: [checkin, video]
                description: 评论目标类型
              targetId:
                type: integer
                description: 目标内容ID
              content:
                type: string
                maxLength: 500
                description: 评论内容
              parentId:
                type: integer
                description: 父评论ID（回复评论时）
              mentionedUsers:
                type: array
                items:
                  type: integer
                maxItems: 10
                description: 提及的用户ID列表
    responses:
      '201':
        description: 评论成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/Comment'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        description: 评论被限制
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
            example:
              code: 403
              message: "评论失败"
              details: "您的评论包含敏感内容"
    security:
      - BearerAuth: []

  get:
    tags:
      - Social Features
    summary: 获取评论列表
    description: |
      获取指定内容的评论列表。
      
      业务规则：
      - 支持按时间或热度排序
      - 分页返回评论和回复
      - 显示评论的点赞数和回复数
      - 标记用户自己的评论
    parameters:
      - name: targetType
        in: query
        required: true
        description: 目标类型
        schema:
          type: string
          enum: [checkin, video]
      - name: targetId
        in: query
        required: true
        description: 目标ID
        schema:
          type: integer
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: sortBy
        in: query
        description: 排序方式
        schema:
          type: string
          enum: [latest, popular]
          default: latest
      - name: parentId
        in: query
        description: 父评论ID（获取回复时）
        schema:
          type: integer
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        comments:
                          type: array
                          items:
                            allOf:
                              - $ref: '../components/schemas/common.yaml#/Comment'
                              - type: object
                                properties:
                                  isOwn:
                                    type: boolean
                                    description: 是否为当前用户的评论
                                  isLiked:
                                    type: boolean
                                    description: 当前用户是否已点赞
                                  replies:
                                    type: array
                                    items:
                                      $ref: '../components/schemas/common.yaml#/Comment'
                                    maxItems: 3
                                    description: 最新3条回复
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
    security:
      - BearerAuth: []

/social/comments/{commentId}:
  put:
    tags:
      - Social Features
    summary: 编辑评论
    description: |
      编辑自己发表的评论。
      
      业务规则：
      - 只能编辑自己的评论
      - 发表后5分钟内可编辑
      - 编辑后显示编辑标记
    parameters:
      - $ref: '../components/parameters/common.yaml#/CommentIdParam'
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [content]
            properties:
              content:
                type: string
                maxLength: 500
                description: 新的评论内容
    responses:
      '200':
        description: 编辑成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      $ref: '../components/schemas/common.yaml#/Comment'
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

  delete:
    tags:
      - Social Features
    summary: 删除评论
    description: |
      删除自己发表的评论。
      
      业务规则：
      - 只能删除自己的评论
      - 管理员可删除任何评论
      - 删除评论时同时删除所有回复
    parameters:
      - $ref: '../components/parameters/common.yaml#/CommentIdParam'
    responses:
      '200':
        $ref: '../components/responses/common.yaml#/SuccessResponse'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '403':
        $ref: '../components/responses/common.yaml#/Forbidden'
      '404':
        $ref: '../components/responses/common.yaml#/NotFound'
    security:
      - BearerAuth: []

# 关注系统
/social/follows:
  post:
    tags:
      - Social Features
    summary: 关注/取消关注用户
    description: |
      关注或取消关注其他用户。
      
      业务规则：
      - 不能关注自己
      - 重复关注则取消关注
      - 关注后可看到对方动态
      - 记录关注关系和时间
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [userId]
            properties:
              userId:
                type: integer
                description: 要关注的用户ID
    responses:
      '200':
        description: 操作成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        isFollowing:
                          type: boolean
                          description: 当前关注状态
                        action:
                          type: string
                          enum: [followed, unfollowed]
                          description: 执行的操作
                        followersCount:
                          type: integer
                          description: 被关注用户的粉丝数
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        description: 用户不存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

  get:
    tags:
      - Social Features
    summary: 获取关注列表
    description: |
      获取用户的关注列表或粉丝列表。
      
      业务规则：
      - 支持获取关注的人和粉丝
      - 显示关注时间和互关状态
      - 支持搜索和排序
    parameters:
      - name: type
        in: query
        required: true
        description: 列表类型
        schema:
          type: string
          enum: [following, followers]
      - name: userId
        in: query
        description: 用户ID（不指定则为当前用户）
        schema:
          type: integer
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: keyword
        in: query
        description: 搜索关键词
        schema:
          type: string
          maxLength: 50
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        users:
                          type: array
                          items:
                            type: object
                            properties:
                              user:
                                $ref: '../components/schemas/common.yaml#/User'
                              followDate:
                                type: string
                                format: date-time
                              isMutualFollow:
                                type: boolean
                                description: 是否互相关注
                              isFollowing:
                                type: boolean
                                description: 当前用户是否关注此用户
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        summary:
                          type: object
                          properties:
                            followingCount:
                              type: integer
                            followersCount:
                              type: integer
                            mutualFollowCount:
                              type: integer
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 分享系统
/social/shares:
  post:
    tags:
      - Social Features
    summary: 分享内容
    description: |
      分享内容到微信朋友圈或其他平台。
      
      业务规则：
      - 支持分享打卡记录、视频等
      - 生成分享链接和图片
      - 记录分享统计
      - 支持自定义分享文案
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required: [targetType, targetId, platform]
            properties:
              targetType:
                type: string
                enum: [checkin, video, task]
                description: 分享目标类型
              targetId:
                type: integer
                description: 目标内容ID
              platform:
                type: string
                enum: [wechat_moments, wechat_chat, weibo, qq]
                description: 分享平台
              customText:
                type: string
                maxLength: 200
                description: 自定义分享文案
    responses:
      '201':
        description: 分享成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        shareId:
                          type: integer
                          description: 分享记录ID
                        shareUrl:
                          type: string
                          description: 分享链接
                        shareImage:
                          type: string
                          description: 分享图片URL
                        shareText:
                          type: string
                          description: 分享文案
                        qrCode:
                          type: string
                          description: 二维码图片URL
      '400':
        $ref: '../components/responses/common.yaml#/BadRequest'
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
      '404':
        description: 分享目标不存在
        content:
          application/json:
            schema:
              $ref: '../components/schemas/common.yaml#/ErrorResponse'
    security:
      - BearerAuth: []

  get:
    tags:
      - Social Features
    summary: 获取分享历史
    description: |
      获取用户的分享历史记录。
      
      业务规则：
      - 按时间倒序返回分享记录
      - 显示分享统计数据
      - 支持按平台和类型筛选
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: targetType
        in: query
        description: 筛选分享目标类型
        schema:
          type: string
          enum: [checkin, video, task]
      - name: platform
        in: query
        description: 筛选分享平台
        schema:
          type: string
          enum: [wechat_moments, wechat_chat, weibo, qq]
      - name: startDate
        in: query
        description: 开始日期
        schema:
          type: string
          format: date
      - name: endDate
        in: query
        description: 结束日期
        schema:
          type: string
          format: date
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        shares:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                              targetType:
                                type: string
                              targetId:
                                type: integer
                              platform:
                                type: string
                              shareUrl:
                                type: string
                              viewCount:
                                type: integer
                              clickCount:
                                type: integer
                              createdAt:
                                type: string
                                format: date-time
                              target:
                                type: object
                                description: 被分享的内容摘要
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        statistics:
                          type: object
                          properties:
                            totalShares:
                              type: integer
                            totalViews:
                              type: integer
                            totalClicks:
                              type: integer
                            platformStats:
                              type: object
                              additionalProperties:
                                type: integer
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []

# 动态时间线
/social/timeline:
  get:
    tags:
      - Social Features
    summary: 获取动态时间线
    description: |
      获取用户的个人动态时间线，包括关注用户的活动。
      
      业务规则：
      - 显示关注用户的打卡、评论等活动
      - 按时间倒序排列
      - 支持不同类型的动态筛选
      - 智能推荐相关内容
    parameters:
      - $ref: '../components/parameters/common.yaml#/PageParam'
      - $ref: '../components/parameters/common.yaml#/SizeParam'
      - name: type
        in: query
        description: 动态类型筛选
        schema:
          type: string
          enum: [all, checkin, comment, like, follow, task]
          default: all
      - name: timeRange
        in: query
        description: 时间范围
        schema:
          type: string
          enum: [today, week, month]
          default: week
    responses:
      '200':
        description: 获取成功
        content:
          application/json:
            schema:
              allOf:
                - $ref: '../components/responses/common.yaml#/SuccessResponse'
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        activities:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                              type:
                                type: string
                                enum: [checkin, comment, like, follow, task]
                              user:
                                $ref: '../components/schemas/common.yaml#/User'
                              content:
                                type: object
                                description: 活动相关内容
                              createdAt:
                                type: string
                                format: date-time
                              isInteracted:
                                type: boolean
                                description: 当前用户是否已互动
                        pagination:
                          $ref: '../components/schemas/common.yaml#/Pagination'
                        hasNewActivities:
                          type: boolean
                          description: 是否有新动态
      '401':
        $ref: '../components/responses/common.yaml#/Unauthorized'
    security:
      - BearerAuth: []
