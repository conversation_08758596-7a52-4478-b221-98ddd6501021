# 打卡系统业务闭环测试计划

## 测试目标
验证打卡系统业务闭环的完整性，确保所有相关表的数据一致性和业务逻辑正确性。

## 测试环境准备

### 1. 数据库迁移
```sql
-- 执行数据库表结构修改
source docs/database/migrations/add_checkin_system_fields.sql;
```

### 2. 测试数据准备
```sql
-- 确保有测试用户和孩子数据
-- 确保有训练营数据
-- 确保有用户训练营参与记录
```

## 测试用例

### 测试用例1：首次打卡
**目标**: 验证首次打卡的完整业务流程

**前置条件**:
- 用户已参与训练营
- 今日未打卡

**测试步骤**:
1. 调用打卡接口 `POST /api/checkins`
2. 验证返回结果

**预期结果**:
1. `checkin_records` 表新增记录，包含正确的 `participation_id`
2. `user_camp_participations` 表更新：
   - `total_checkins` +1
   - `current_day` +1
   - `consecutive_days` +1
   - `progress_percentage` 更新
   - `total_study_minutes` 累加
   - `last_checkin_date` 更新为今日
3. `children` 表更新：
   - `last_checkin_date` 更新为今日
   - `total_checkins` +1
   - `continuous_days` +1
4. `point_records` 表新增记录，包含正确的 `participation_id`
5. `child_points` 表更新积分统计
6. `camp_checkin_dates` 表状态更新为已打卡

### 测试用例2：连续打卡
**目标**: 验证连续打卡的逻辑正确性

**前置条件**:
- 昨日已打卡
- 今日未打卡

**测试步骤**:
1. 调用打卡接口
2. 验证连续天数计算

**预期结果**:
- `consecutive_days` 正确递增
- `max_continuous_days` 如需要则更新

### 测试用例3：补卡功能
**目标**: 验证补卡功能的正确性

**前置条件**:
- 有可补卡的日期
- 补卡次数未用完

**测试步骤**:
1. 调用补卡接口，status=2
2. 验证补卡逻辑

**预期结果**:
- 打卡记录 `status` 为 2（补卡）
- `makeup_used_count` +1
- 其他逻辑与正常打卡相同

### 测试用例4：家庭契约关联
**目标**: 验证打卡与家庭契约的关联

**前置条件**:
- 存在进行中的家庭契约
- 契约目标为打卡相关

**测试步骤**:
1. 完成打卡
2. 检查契约进度更新

**预期结果**:
- `family_contracts` 表的 `current_progress` 更新
- 如契约完成，状态变为已完成

### 测试用例5：积分系统集成
**目标**: 验证积分系统的完整性

**测试步骤**:
1. 完成打卡
2. 检查积分相关表

**预期结果**:
- `point_records` 表新增记录，包含 `participation_id`
- `child_points` 表统计数据更新
- 积分计算正确

### 测试用例6：成长轨迹记录
**目标**: 验证成长轨迹的记录

**测试步骤**:
1. 完成特殊打卡（如首次、连续等）
2. 检查成长轨迹

**预期结果**:
- `growth_tracks` 表新增相应记录
- 包含正确的 `participation_id`

## 数据一致性检查

### 检查SQL脚本
```sql
-- 检查打卡记录与参与记录的关联
SELECT cr.id, cr.participation_id, ucp.id as actual_participation_id
FROM checkin_records cr
LEFT JOIN user_camp_participations ucp ON cr.camp_id = ucp.camp_id AND cr.child_id = ucp.child_id
WHERE cr.participation_id != ucp.id;

-- 检查积分记录的关联
SELECT pr.id, pr.participation_id, cr.participation_id as checkin_participation_id
FROM point_records pr
LEFT JOIN checkin_records cr ON pr.source_id = cr.id AND pr.source_type = 1
WHERE pr.participation_id != cr.participation_id;

-- 检查最后打卡日期的一致性
SELECT ucp.id, ucp.last_checkin_date, MAX(cr.checkin_date) as actual_last_date
FROM user_camp_participations ucp
LEFT JOIN checkin_records cr ON cr.participation_id = ucp.id
GROUP BY ucp.id
HAVING ucp.last_checkin_date != MAX(cr.checkin_date);
```

## 性能测试

### 并发打卡测试
- 模拟多用户同时打卡
- 验证分布式锁的有效性
- 检查数据一致性

### 大数据量测试
- 测试大量打卡记录的查询性能
- 验证索引的有效性

## 错误场景测试

### 重复打卡
- 验证同一天重复打卡的处理
- 确保返回适当的错误信息

### 数据库异常
- 模拟数据库连接异常
- 验证事务回滚的正确性

### 服务依赖异常
- 模拟积分服务异常
- 验证主流程不受影响

## 测试工具

### API测试
- 使用 Postman 或 curl 进行接口测试
- 准备测试数据集

### 数据库测试
- 使用 SQL 脚本验证数据一致性
- 监控数据库性能

## 验收标准

1. 所有测试用例通过
2. 数据一致性检查无异常
3. 性能满足要求（响应时间 < 500ms）
4. 错误处理正确
5. 日志记录完整
