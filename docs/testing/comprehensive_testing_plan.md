# 完整测试验证计划 - 纯净的直接修改表结构方案

## 概述

本文档详细说明了在实施纯净的直接修改表结构方案后，如何进行全面的测试验证，确保系统功能正常工作。

## 测试环境准备

### 1. 数据库环境准备

#### 1.1 创建测试数据库
```sql
-- 创建测试数据库
CREATE DATABASE kids_platform_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入基础表结构
USE kids_platform_test;
SOURCE /path/to/base_schema.sql;

-- 执行迁移脚本
SOURCE /path/to/docs/database/migrations/final_clean_migration.sql;
```

#### 1.2 准备测试数据
```sql
-- 插入测试用户
INSERT INTO users (id, openid, unionid, nickname, avatar, phone, created_at, updated_at) VALUES
(1, 'test_openid_1', 'test_unionid_1', '测试家长1', '', '13800138001', NOW(), NOW()),
(2, 'test_openid_2', 'test_unionid_2', '测试家长2', '', '13800138002', NOW(), NOW());

-- 插入测试孩子
INSERT INTO children (id, user_id, name, gender, birth_date, avatar, created_at, updated_at) VALUES
(1, 1, '小明', 1, '2015-06-01', '', NOW(), NOW()),
(2, 1, '小红', 2, '2017-03-15', '', NOW(), NOW()),
(3, 2, '小刚', 1, '2016-09-20', '', NOW(), NOW());

-- 插入测试训练营
INSERT INTO training_camps (id, name, description, start_date, end_date, status, created_at, updated_at) VALUES
(1, '测试训练营1', '第一个测试训练营', '2024-01-01', '2024-12-31', 'active', NOW(), NOW()),
(2, '测试训练营2', '第二个测试训练营', '2024-02-01', '2024-12-31', 'active', NOW(), NOW());

-- 插入测试参与记录
INSERT INTO user_camp_participations (id, user_id, child_id, camp_id, status, joined_at, created_at, updated_at) VALUES
(1, 1, 1, 1, 'active', NOW(), NOW(), NOW()),
(2, 1, 2, 1, 'active', NOW(), NOW(), NOW()),
(3, 2, 3, 1, 'active', NOW(), NOW(), NOW()),
(4, 1, 1, 2, 'active', NOW(), NOW(), NOW());

-- 插入测试勋章配置
INSERT INTO medals (id, name, description, type, required_value, reward_points, icon, is_active, created_at, updated_at) VALUES
(1, '新手勋章', '完成第一次打卡', 'total_checkins', 1, 10, 'newbie.png', 1, NOW(), NOW()),
(2, '坚持勋章', '连续打卡7天', 'continuous_days', 7, 50, 'persistence.png', 1, NOW(), NOW()),
(3, '积分达人', '累计获得100积分', 'total_points', 100, 20, 'points_master.png', 1, NOW(), NOW());
```

### 2. 应用环境准备

#### 2.1 配置测试环境
```yaml
# config/test.yaml
database:
  host: localhost
  port: 3306
  username: test_user
  password: test_password
  database: kids_platform_test
  
redis:
  host: localhost
  port: 6379
  database: 1
  
log:
  level: debug
  output: stdout
```

#### 2.2 启动测试服务
```bash
# 启动测试环境
cd /path/to/project
export ENV=test
go run main.go
```

## 单元测试

### 1. Repository层测试

#### 1.1 ChildPointsRepository测试
```go
// internal/repositories/child_points_repository_test.go
package repositories

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "gorm.io/gorm"
)

type ChildPointsRepositoryTestSuite struct {
    suite.Suite
    db   *gorm.DB
    repo ChildPointsRepository
}

func (suite *ChildPointsRepositoryTestSuite) SetupSuite() {
    // 初始化测试数据库连接
    suite.db = setupTestDB()
    suite.repo = NewChildPointsRepository(suite.db)
}

func (suite *ChildPointsRepositoryTestSuite) SetupTest() {
    // 每个测试前清理数据
    suite.db.Exec("TRUNCATE TABLE child_points")
}

func (suite *ChildPointsRepositoryTestSuite) TestGetByParticipationID() {
    // 测试根据参与记录ID获取积分
    participationID := uint(1)
    
    // 创建测试数据
    childPoints := &models.ChildPoints{
        ChildID:         1,
        ParticipationID: int64(participationID),
        TotalPoints:     100,
        WeekPoints:      20,
        MonthPoints:     80,
    }
    err := suite.repo.Create(childPoints)
    assert.NoError(suite.T(), err)
    
    // 测试获取
    result, err := suite.repo.GetByParticipationID(participationID)
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), int64(participationID), result.ParticipationID)
    assert.Equal(suite.T(), int64(100), result.TotalPoints)
}

func (suite *ChildPointsRepositoryTestSuite) TestGetByCampID() {
    // 测试根据训练营ID获取所有积分记录
    campID := uint(1)
    
    // 创建多个测试数据
    for i := 1; i <= 3; i++ {
        childPoints := &models.ChildPoints{
            ChildID:         int64(i),
            ParticipationID: int64(i),
            TotalPoints:     int64(i * 10),
        }
        err := suite.repo.Create(childPoints)
        assert.NoError(suite.T(), err)
    }
    
    // 测试获取
    results, err := suite.repo.GetByCampID(campID)
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), results, 3)
}

func (suite *ChildPointsRepositoryTestSuite) TestGetRankingByCampID() {
    // 测试获取训练营排行榜
    campID := uint(1)
    
    // 创建测试数据（不同积分）
    testData := []struct {
        participationID int64
        totalPoints     int64
        weekPoints      int
    }{
        {1, 100, 30},
        {2, 200, 20},
        {3, 150, 40},
    }
    
    for _, data := range testData {
        childPoints := &models.ChildPoints{
            ChildID:         data.participationID,
            ParticipationID: data.participationID,
            TotalPoints:     data.totalPoints,
            WeekPoints:      data.weekPoints,
        }
        err := suite.repo.Create(childPoints)
        assert.NoError(suite.T(), err)
    }
    
    // 测试总积分排行榜
    ranking, err := suite.repo.GetRankingByCampID(campID, "total", 10)
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), ranking, 3)
    assert.Equal(suite.T(), int64(200), ranking[0].TotalPoints) // 最高分在前
    
    // 测试周积分排行榜
    weekRanking, err := suite.repo.GetRankingByCampID(campID, "week", 10)
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), weekRanking, 3)
    assert.Equal(suite.T(), 40, weekRanking[0].WeekPoints) // 最高周积分在前
}

func TestChildPointsRepositoryTestSuite(t *testing.T) {
    suite.Run(t, new(ChildPointsRepositoryTestSuite))
}
```

#### 1.2 ChildMedalsRepository测试
```go
// internal/repositories/child_medals_repository_test.go
package repositories

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
)

type ChildMedalsRepositoryTestSuite struct {
    suite.Suite
    db   *gorm.DB
    repo ChildMedalsRepository
}

func (suite *ChildMedalsRepositoryTestSuite) SetupSuite() {
    suite.db = setupTestDB()
    suite.repo = NewChildMedalsRepository(suite.db)
}

func (suite *ChildMedalsRepositoryTestSuite) SetupTest() {
    suite.db.Exec("TRUNCATE TABLE child_medals")
}

func (suite *ChildMedalsRepositoryTestSuite) TestGetByParticipationIDAndMedalID() {
    participationID := uint(1)
    medalID := uint(1)
    
    // 创建测试数据
    childMedal := &models.ChildMedals{
        ChildID:         1,
        ParticipationID: int64(participationID),
        MedalID:         int(medalID),
        IsUnlocked:      1,
        CurrentProgress: 10,
        TargetProgress:  10,
    }
    err := suite.repo.Create(childMedal)
    assert.NoError(suite.T(), err)
    
    // 测试获取
    result, err := suite.repo.GetByParticipationIDAndMedalID(participationID, medalID)
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), result)
    assert.Equal(suite.T(), int64(participationID), result.ParticipationID)
    assert.Equal(suite.T(), int(medalID), result.MedalID)
    assert.Equal(suite.T(), 1, result.IsUnlocked)
}

func (suite *ChildMedalsRepositoryTestSuite) TestGetUnlockedMedalsByParticipationID() {
    participationID := uint(1)
    
    // 创建测试数据（部分已解锁，部分未解锁）
    testData := []struct {
        medalID    int
        isUnlocked int
    }{
        {1, 1}, // 已解锁
        {2, 0}, // 未解锁
        {3, 1}, // 已解锁
    }
    
    for _, data := range testData {
        childMedal := &models.ChildMedals{
            ChildID:         1,
            ParticipationID: int64(participationID),
            MedalID:         data.medalID,
            IsUnlocked:      data.isUnlocked,
        }
        err := suite.repo.Create(childMedal)
        assert.NoError(suite.T(), err)
    }
    
    // 测试获取已解锁勋章
    unlockedMedals, err := suite.repo.GetUnlockedMedalsByParticipationID(participationID)
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), unlockedMedals, 2) // 只有2个已解锁
    
    for _, medal := range unlockedMedals {
        assert.Equal(suite.T(), 1, medal.IsUnlocked)
    }
}

func TestChildMedalsRepositoryTestSuite(t *testing.T) {
    suite.Run(t, new(ChildMedalsRepositoryTestSuite))
}
```

### 2. Service层测试

#### 2.1 PointsService测试
```go
// internal/services/points_service_test.go
package services

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "github.com/stretchr/testify/mock"
)

type PointsServiceTestSuite struct {
    suite.Suite
    mockPointsRepo       *MockChildPointsRepository
    mockPointRecordsRepo *MockPointRecordsRepository
    mockParticipationRepo *MockUserCampParticipationRepository
    service              PointsService
}

func (suite *PointsServiceTestSuite) SetupTest() {
    suite.mockPointsRepo = new(MockChildPointsRepository)
    suite.mockPointRecordsRepo = new(MockPointRecordsRepository)
    suite.mockParticipationRepo = new(MockUserCampParticipationRepository)
    
    suite.service = NewPointsService(
        suite.mockPointsRepo,
        suite.mockPointRecordsRepo,
        suite.mockParticipationRepo,
        nil, // 使用mock，不需要真实DB
    )
}

func (suite *PointsServiceTestSuite) TestAddPoints() {
    participationID := uint(1)
    points := 10
    reason := "测试添加积分"
    
    // 设置mock期望
    participation := &models.UserCampParticipation{
        ID:      1,
        ChildID: 1,
        CampID:  1,
    }
    suite.mockParticipationRepo.On("GetByID", participationID).Return(participation, nil)
    
    existingPoints := &models.ChildPoints{
        ID:              1,
        ChildID:         1,
        ParticipationID: int64(participationID),
        TotalPoints:     50,
        WeekPoints:      10,
        MonthPoints:     30,
    }
    suite.mockPointsRepo.On("GetByParticipationID", participationID).Return(existingPoints, nil)
    suite.mockPointsRepo.On("Update", mock.AnythingOfType("uint"), mock.AnythingOfType("*models.ChildPoints")).Return(nil)
    suite.mockPointRecordsRepo.On("Create", mock.AnythingOfType("*models.PointRecords")).Return(nil)
    
    // 执行测试
    err := suite.service.AddPoints(participationID, points, reason, "test", 0)
    
    // 验证结果
    assert.NoError(suite.T(), err)
    suite.mockPointsRepo.AssertExpectations(suite.T())
    suite.mockPointRecordsRepo.AssertExpectations(suite.T())
    suite.mockParticipationRepo.AssertExpectations(suite.T())
}

func (suite *PointsServiceTestSuite) TestGetCampRanking() {
    campID := uint(1)
    rankType := "total"
    limit := 10
    
    // 设置mock期望
    mockRanking := []*models.ChildPoints{
        {
            ChildID:         1,
            ParticipationID: 1,
            TotalPoints:     200,
            TotalCheckins:   20,
            ContinuousDays:  5,
        },
        {
            ChildID:         2,
            ParticipationID: 2,
            TotalPoints:     150,
            TotalCheckins:   15,
            ContinuousDays:  3,
        },
    }
    suite.mockPointsRepo.On("GetRankingByCampID", campID, rankType, limit).Return(mockRanking, nil)
    
    // 执行测试
    ranking, err := suite.service.GetCampRanking(campID, rankType, limit)
    
    // 验证结果
    assert.NoError(suite.T(), err)
    assert.Len(suite.T(), ranking, 2)
    assert.Equal(suite.T(), 1, ranking[0].Rank)
    assert.Equal(suite.T(), uint(1), ranking[0].ParticipationID)
    assert.Equal(suite.T(), int64(200), ranking[0].Points)
}

func TestPointsServiceTestSuite(t *testing.T) {
    suite.Run(t, new(PointsServiceTestSuite))
}
```

## 集成测试

### 1. API接口测试

#### 1.1 积分接口测试
```go
// tests/integration/points_api_test.go
package integration

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/gin-gonic/gin"
)

func TestPointsAPI(t *testing.T) {
    // 设置测试环境
    gin.SetMode(gin.TestMode)
    router := setupTestRouter()
    
    t.Run("获取参与记录积分", func(t *testing.T) {
        participationID := "1"
        
        req, _ := http.NewRequest("GET", "/api/points/participation/"+participationID, nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
        
        data := response["data"].(map[string]interface{})
        assert.NotNil(t, data["participation_id"])
        assert.NotNil(t, data["total_points"])
    })
    
    t.Run("添加积分", func(t *testing.T) {
        requestBody := map[string]interface{}{
            "participation_id": 1,
            "points":          10,
            "reason":          "测试添加积分",
            "source_type":     "test",
            "source_id":       0,
        }
        
        jsonBody, _ := json.Marshal(requestBody)
        req, _ := http.NewRequest("POST", "/api/points/add", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
    })
    
    t.Run("获取训练营排行榜", func(t *testing.T) {
        campID := "1"
        
        req, _ := http.NewRequest("GET", "/api/points/ranking/camp/"+campID+"?type=total&limit=10", nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
        
        data := response["data"].(map[string]interface{})
        ranking := data["ranking"].([]interface{})
        assert.GreaterOrEqual(t, len(ranking), 0)
    })
}
```

#### 1.2 勋章接口测试
```go
// tests/integration/medals_api_test.go
package integration

func TestMedalsAPI(t *testing.T) {
    gin.SetMode(gin.TestMode)
    router := setupTestRouter()
    
    t.Run("获取参与记录勋章", func(t *testing.T) {
        participationID := "1"
        
        req, _ := http.NewRequest("GET", "/api/medals/participation/"+participationID, nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
        
        data := response["data"].([]interface{})
        assert.GreaterOrEqual(t, len(data), 0)
    })
    
    t.Run("检查并解锁勋章", func(t *testing.T) {
        participationID := "1"
        
        req, _ := http.NewRequest("POST", "/api/medals/check/"+participationID, nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
    })
    
    t.Run("获取训练营勋章统计", func(t *testing.T) {
        campID := "1"
        
        req, _ := http.NewRequest("GET", "/api/medals/statistics/camp/"+campID, nil)
        req.Header.Set("Authorization", "Bearer "+getTestToken())
        
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.Equal(t, float64(200), response["code"])
        
        data := response["data"].(map[string]interface{})
        assert.NotNil(t, data["camp_id"])
        assert.NotNil(t, data["total_participants"])
    })
}
```

### 2. 数据库集成测试

#### 2.1 数据一致性测试
```go
// tests/integration/data_consistency_test.go
package integration

func TestDataConsistency(t *testing.T) {
    db := setupTestDB()
    
    t.Run("积分记录与参与记录关联", func(t *testing.T) {
        // 查询所有积分记录
        var childPoints []models.ChildPoints
        err := db.Find(&childPoints).Error
        assert.NoError(t, err)
        
        // 验证每个积分记录都有对应的参与记录
        for _, points := range childPoints {
            var participation models.UserCampParticipation
            err := db.Where("id = ?", points.ParticipationID).First(&participation).Error
            assert.NoError(t, err, "积分记录 %d 对应的参与记录 %d 不存在", points.ID, points.ParticipationID)
        }
    })
    
    t.Run("勋章记录与参与记录关联", func(t *testing.T) {
        // 查询所有勋章记录
        var childMedals []models.ChildMedals
        err := db.Find(&childMedals).Error
        assert.NoError(t, err)
        
        // 验证每个勋章记录都有对应的参与记录
        for _, medal := range childMedals {
            var participation models.UserCampParticipation
            err := db.Where("id = ?", medal.ParticipationID).First(&participation).Error
            assert.NoError(t, err, "勋章记录 %d 对应的参与记录 %d 不存在", medal.ID, medal.ParticipationID)
        }
    })
    
    t.Run("唯一约束验证", func(t *testing.T) {
        // 测试积分记录的唯一约束
        childPoints1 := &models.ChildPoints{
            ChildID:         1,
            ParticipationID: 1,
            TotalPoints:     100,
        }
        err := db.Create(childPoints1).Error
        assert.NoError(t, err)
        
        // 尝试创建重复的积分记录
        childPoints2 := &models.ChildPoints{
            ChildID:         1,
            ParticipationID: 1, // 相同的participation_id
            TotalPoints:     200,
        }
        err = db.Create(childPoints2).Error
        assert.Error(t, err, "应该因为唯一约束而失败")
        
        // 测试勋章记录的唯一约束
        childMedal1 := &models.ChildMedals{
            ChildID:         1,
            ParticipationID: 1,
            MedalID:         1,
            IsUnlocked:      0,
        }
        err = db.Create(childMedal1).Error
        assert.NoError(t, err)
        
        // 尝试创建重复的勋章记录
        childMedal2 := &models.ChildMedals{
            ChildID:         1,
            ParticipationID: 1, // 相同的participation_id
            MedalID:         1, // 相同的medal_id
            IsUnlocked:      1,
        }
        err = db.Create(childMedal2).Error
        assert.Error(t, err, "应该因为唯一约束而失败")
    })
}
```

## 端到端测试

### 1. 完整业务流程测试

#### 1.1 打卡流程测试
```go
// tests/e2e/checkin_flow_test.go
package e2e

func TestCheckinFlow(t *testing.T) {
    // 1. 用户登录并选择孩子
    userID := loginAndSelectChild(t)
    
    // 2. 加入训练营
    participationID := joinTrainingCamp(t, userID, 1, 1) // user_id=1, child_id=1, camp_id=1
    
    // 3. 初始化积分和勋章
    initializePointsAndMedals(t, participationID)
    
    // 4. 执行打卡
    checkinResult := performCheckin(t, participationID)
    assert.True(t, checkinResult.Success)
    
    // 5. 验证积分增加
    points := getParticipationPoints(t, participationID)
    assert.Greater(t, points.TotalPoints, int64(0))
    
    // 6. 验证勋章检查
    medals := getParticipationMedals(t, participationID)
    assert.GreaterOrEqual(t, len(medals), 1)
    
    // 7. 验证排行榜更新
    ranking := getCampRanking(t, 1, "total", 10)
    assert.GreaterOrEqual(t, len(ranking), 1)
    
    // 8. 连续打卡测试
    for i := 0; i < 7; i++ {
        performCheckin(t, participationID)
    }
    
    // 9. 验证连续打卡勋章解锁
    updatedMedals := getParticipationMedals(t, participationID)
    unlockedCount := 0
    for _, medal := range updatedMedals {
        if medal.IsUnlocked == 1 {
            unlockedCount++
        }
    }
    assert.Greater(t, unlockedCount, 0, "应该有勋章被解锁")
}
```

#### 1.2 多训练营隔离测试
```go
// tests/e2e/multi_camp_isolation_test.go
package e2e

func TestMultiCampIsolation(t *testing.T) {
    userID := uint(1)
    childID := uint(1)
    camp1ID := uint(1)
    camp2ID := uint(2)
    
    // 1. 加入两个不同的训练营
    participation1ID := joinTrainingCamp(t, userID, childID, camp1ID)
    participation2ID := joinTrainingCamp(t, userID, childID, camp2ID)
    
    // 2. 在训练营1中获得积分
    addPointsToParticipation(t, participation1ID, 100, "训练营1积分")
    
    // 3. 在训练营2中获得积分
    addPointsToParticipation(t, participation2ID, 50, "训练营2积分")
    
    // 4. 验证积分隔离
    points1 := getParticipationPoints(t, participation1ID)
    points2 := getParticipationPoints(t, participation2ID)
    
    assert.Equal(t, int64(100), points1.TotalPoints)
    assert.Equal(t, int64(50), points2.TotalPoints)
    
    // 5. 验证排行榜隔离
    ranking1 := getCampRanking(t, camp1ID, "total", 10)
    ranking2 := getCampRanking(t, camp2ID, "total", 10)
    
    // 训练营1的排行榜应该只包含训练营1的数据
    for _, rank := range ranking1 {
        participation := getParticipationByID(t, rank.ParticipationID)
        assert.Equal(t, camp1ID, participation.CampID)
    }
    
    // 训练营2的排行榜应该只包含训练营2的数据
    for _, rank := range ranking2 {
        participation := getParticipationByID(t, rank.ParticipationID)
        assert.Equal(t, camp2ID, participation.CampID)
    }
    
    // 6. 验证勋章隔离
    medals1 := getParticipationMedals(t, participation1ID)
    medals2 := getParticipationMedals(t, participation2ID)
    
    // 每个参与记录都应该有独立的勋章记录
    assert.GreaterOrEqual(t, len(medals1), 1)
    assert.GreaterOrEqual(t, len(medals2), 1)
    
    // 验证勋章记录的participation_id正确
    for _, medal := range medals1 {
        assert.Equal(t, int64(participation1ID), medal.ParticipationID)
    }
    for _, medal := range medals2 {
        assert.Equal(t, int64(participation2ID), medal.ParticipationID)
    }
}
```

## 性能测试

### 1. 数据库性能测试

#### 1.1 查询性能测试
```go
// tests/performance/query_performance_test.go
package performance

func TestQueryPerformance(t *testing.T) {
    db := setupTestDB()
    
    // 创建大量测试数据
    createLargeTestDataset(db, 10000) // 创建10000条记录
    
    t.Run("根据participation_id查询积分性能", func(t *testing.T) {
        start := time.Now()
        
        for i := 0; i < 1000; i++ {
            participationID := uint(rand.Intn(10000) + 1)
            var points models.ChildPoints
            db.Where("participation_id = ?", participationID).First(&points)
        }
        
        duration := time.Since(start)
        t.Logf("1000次查询耗时: %v, 平均每次: %v", duration, duration/1000)
        
        // 性能要求：平均每次查询不超过10ms
        assert.Less(t, duration/1000, 10*time.Millisecond)
    })
    
    t.Run("训练营排行榜查询性能", func(t *testing.T) {
        start := time.Now()
        
        for i := 0; i < 100; i++ {
            campID := uint(rand.Intn(10) + 1)
            var ranking []models.ChildPoints
            db.Table("child_points cp").
                Joins("JOIN user_camp_participations ucp ON cp.participation_id = ucp.id").
                Where("ucp.camp_id = ?", campID).
                Order("cp.total_points DESC").
                Limit(50).
                Find(&ranking)
        }
        
        duration := time.Since(start)
        t.Logf("100次排行榜查询耗时: %v, 平均每次: %v", duration, duration/100)
        
        // 性能要求：平均每次查询不超过50ms
        assert.Less(t, duration/100, 50*time.Millisecond)
    })
}
```

### 2. API性能测试

#### 2.1 并发测试
```go
// tests/performance/api_performance_test.go
package performance

func TestAPIConcurrency(t *testing.T) {
    router := setupTestRouter()
    
    t.Run("并发添加积分", func(t *testing.T) {
        concurrency := 100
        requestsPerGoroutine := 10
        
        var wg sync.WaitGroup
        var successCount int64
        var errorCount int64
        
        start := time.Now()
        
        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func(goroutineID int) {
                defer wg.Done()
                
                for j := 0; j < requestsPerGoroutine; j++ {
                    requestBody := map[string]interface{}{
                        "participation_id": 1,
                        "points":          1,
                        "reason":          fmt.Sprintf("并发测试-%d-%d", goroutineID, j),
                        "source_type":     "test",
                    }
                    
                    jsonBody, _ := json.Marshal(requestBody)
                    req, _ := http.NewRequest("POST", "/api/points/add", bytes.NewBuffer(jsonBody))
                    req.Header.Set("Content-Type", "application/json")
                    req.Header.Set("Authorization", "Bearer "+getTestToken())
                    
                    w := httptest.NewRecorder()
                    router.ServeHTTP(w, req)
                    
                    if w.Code == http.StatusOK {
                        atomic.AddInt64(&successCount, 1)
                    } else {
                        atomic.AddInt64(&errorCount, 1)
                    }
                }
            }(i)
        }
        
        wg.Wait()
        duration := time.Since(start)
        
        totalRequests := int64(concurrency * requestsPerGoroutine)
        t.Logf("并发测试结果: 总请求=%d, 成功=%d, 失败=%d, 耗时=%v", 
            totalRequests, successCount, errorCount, duration)
        
        // 验证成功率
        successRate := float64(successCount) / float64(totalRequests)
        assert.Greater(t, successRate, 0.95, "成功率应该大于95%")
        
        // 验证性能
        avgDuration := duration / time.Duration(totalRequests)
        assert.Less(t, avgDuration, 100*time.Millisecond, "平均响应时间应该小于100ms")
    })
}
```

## 测试执行计划

### 1. 测试阶段划分

#### 阶段1：单元测试（1-2天）
- [ ] Repository层测试
- [ ] Service层测试
- [ ] 工具函数测试

#### 阶段2：集成测试（2-3天）
- [ ] API接口测试
- [ ] 数据库集成测试
- [ ] 组件间集成测试

#### 阶段3：端到端测试（2-3天）
- [ ] 完整业务流程测试
- [ ] 多场景组合测试
- [ ] 边界条件测试

#### 阶段4：性能测试（1-2天）
- [ ] 数据库性能测试
- [ ] API性能测试
- [ ] 并发测试

#### 阶段5：回归测试（1天）
- [ ] 全量测试执行
- [ ] 问题修复验证
- [ ] 最终验收

### 2. 测试环境要求

#### 硬件要求
- CPU: 4核以上
- 内存: 8GB以上
- 磁盘: SSD，50GB以上可用空间

#### 软件要求
- Go 1.23.10+
- MySQL 8.0+
- Redis 6.0+
- Git

### 3. 测试数据准备

#### 基础数据
- 用户数据：100个测试用户
- 孩子数据：200个测试孩子
- 训练营数据：10个测试训练营
- 参与记录：1000条参与记录

#### 大数据集（性能测试）
- 用户数据：10000个用户
- 孩子数据：20000个孩子
- 参与记录：100000条参与记录
- 积分记录：100000条积分记录
- 勋章记录：300000条勋章记录

### 4. 成功标准

#### 功能测试
- [ ] 所有单元测试通过率 ≥ 95%
- [ ] 所有集成测试通过率 ≥ 95%
- [ ] 所有端到端测试通过率 ≥ 90%

#### 性能测试
- [ ] 单次查询响应时间 ≤ 100ms
- [ ] 排行榜查询响应时间 ≤ 200ms
- [ ] 并发处理成功率 ≥ 95%
- [ ] 系统资源使用率 ≤ 80%

#### 数据一致性
- [ ] 所有外键关系正确
- [ ] 所有唯一约束生效
- [ ] 数据隔离完全有效
- [ ] 无数据丢失或重复

### 5. 风险控制

#### 回滚准备
- [ ] 数据库备份完整
- [ ] 回滚脚本测试通过
- [ ] 应用版本回滚方案
- [ ] 监控和告警配置

#### 监控指标
- [ ] API响应时间监控
- [ ] 数据库性能监控
- [ ] 错误率监控
- [ ] 业务指标监控

## 总结

本测试计划涵盖了从单元测试到端到端测试的完整测试体系，确保纯净的直接修改表结构方案能够安全、稳定地上线。通过分阶段的测试执行和严格的成功标准，可以最大程度地降低上线风险，保证系统的稳定性和可靠性。
