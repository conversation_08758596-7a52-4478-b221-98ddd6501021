# 项目目录结构规范

## 🎯 目标
建立清晰的项目目录结构规范，确保每个文件都放在正确的位置，避免目录混乱。

## 📁 标准目录结构

```
prograrm/                           # 项目根目录
├── docs/                          # 📚 项目级文档目录
│   ├── PROJECT_DIRECTORY_STRUCTURE.md  # 本文档 - 目录结构规范
│   ├── PATH_MANAGEMENT_RULES.md        # 路径管理规范
│   ├── ARCHITECTURE.md                 # 架构设计文档 ⭐ **已创建**
│   ├── standards/                      # 📋 项目标准文档
│   │   ├── README.md                   # 标准文档导航
│   │   ├── collaboration/              # 协作标准
│   │   │   ├── collaboration-guidelines.md
│   │   │   ├── communication-principles.md
│   │   │   ├── git-workflow.md
│   │   │   └── workflow-standards.md
│   │   └── development/                # 开发标准
│   │       ├── development-standards-framework.md
│   │       ├── requirements-validation.md
│   │       ├── standards-enforcement.md
│   │       ├── standards-optimization.md
│   │       └── task-module-completion-report.md
│   ├── API_DESIGN.md                   # API设计文档 (待创建)
│   └── DEPLOYMENT.md                   # 部署文档 (待创建)
├── api/                           # 🚀 API服务主目录
│   ├── README.md                  # API服务说明
│   ├── Makefile                   # 构建和管理命令
│   ├── go.mod                     # Go模块定义
│   ├── go.sum                     # Go依赖锁定
│   ├── cmd/                       # 🎯 应用程序入口
│   │   ├── api-server/            # API服务器
│   │   └── admin-server/          # 管理后台服务器
│   ├── internal/                  # 🔒 内部业务逻辑
│   │   ├── handlers/              # HTTP处理器
│   │   │   ├── api/               # API服务处理器
│   │   │   │   ├── routes.go      # API路由注册
│   │   │   │   └── user_handlers.go # 用户处理器
│   │   │   └── admin/             # 管理后台处理器
│   │   │       └── routes.go      # Admin路由注册
│   │   ├── services/              # 业务逻辑层
│   │   │   ├── api/               # API业务逻辑
│   │   │   ├── admin/             # 管理后台业务逻辑
│   │   │   ├── shared/            # 共享业务逻辑
│   │   │   └── user_service.go    # 用户服务实现
│   │   ├── repositories/          # 数据访问层
│   │   │   └── user_repository.go # 用户数据访问
│   │   └── models/                # 数据模型
│   │       └── user.go            # 用户模型
│   ├── pkg/                       # 🔧 可复用工具包
│   │   ├── config/                # 配置管理
│   │   ├── database/              # 数据库工具
│   │   ├── cache/                 # 缓存工具 (Redis)
│   │   ├── jwt/                   # JWT认证工具
│   │   ├── logger/                # 日志工具
│   │   ├── middleware/            # 中间件系统
│   │   ├── validator/             # 参数验证器
│   │   ├── errors/                # 统一错误处理
│   │   ├── response/              # 统一响应格式
│   │   ├── health/                # 健康检查
│   │   └── version/               # 版本信息
│   ├── configs/                   # ⚙️ 配置文件
│   │   └── config.yaml            # 主配置文件
│   ├── database/                  # 🗄️ 数据库相关
│   │   └── migrations/            # 数据库迁移文件
│   ├── docs/                      # 📖 API技术文档
│   │   └── docs.go                # Swagger文档
│   ├── scripts/                   # 📜 脚本工具
│   │   ├── README.md              # 脚本说明 ⭐ **已完善**
│   │   ├── build.sh               # 构建脚本
│   │   ├── build-enhanced.sh      # 增强构建脚本
│   │   ├── start.sh               # 启动脚本
│   │   ├── stop.sh                # 停止脚本
│   │   └── tests/                 # 测试脚本目录
│   │       ├── README.md          # 测试说明
│   │       ├── run_all_tests.sh   # 主测试脚本
│   │       ├── test_api.sh        # API功能测试
│   │       └── test_enhanced_api.sh # 增强功能测试
│   ├── tools/                     # 🛠️ 开发工具
│   │   ├── README.md              # 工具说明 ⭐ **已完善**
│   │   ├── generate-module.sh     # 模块生成器 ⭐ **主力工具**
│   │   ├── mvs-check.sh           # MVS规则检查器
│   │   └── generators/            # 代码生成器套件
│   │       ├── README.md          # 生成器说明
│   │       ├── generate.sh        # 基础生成脚本
│   │       └── templates/         # 代码模板库
│   ├── bin/                       # 📦 编译输出目录
│   │   ├── api-server             # API服务器可执行文件
│   │   └── admin-server           # 管理服务器可执行文件
│   └── logs/                      # 📋 日志文件目录 (运行时生成)
├── archive/                       # 🗃️ 历史文档备份
│   └── docs/                      # 原tmp/docs内容备份
└── frontend/                      # 🎨 前端项目目录（未来扩展）
    └── ...
```

## 📋 目录用途说明

### 项目根目录级别

| 目录 | 用途 | 文件类型 |
|------|------|----------|
| `docs/` | 项目级文档、架构设计、规范文档 | `.md`, `.pdf`, 设计图 |
| `api/` | 后端API服务完整代码 | Go源码、配置、脚本 |
| `frontend/` | 前端项目（未来） | 前端框架代码 |

### API服务目录级别

| 目录 | 用途 | 文件类型 | 示例 | 状态 |
|------|------|----------|------|------|
| `cmd/` | 应用程序入口点 | `main.go` | `cmd/api-server/main.go` | ✅ 已实现 |
| `internal/` | 内部业务逻辑，不对外暴露 | `.go` | `internal/services/user_service.go` | ✅ 已实现 |
| `pkg/` | 可复用的工具包 | `.go` | `pkg/config/config.go` | ✅ 已实现 |
| `configs/` | 配置文件 | `.yaml`, `.json`, `.env` | `configs/config.yaml` | ✅ 已实现 |
| `database/` | 数据库相关文件 | `.sql`, `.go` | `database/migrations/001_users.sql` | ✅ 已实现 |
| `docs/` | API技术文档 | `.go`, `.json` | `docs/docs.go` (Swagger) | ✅ 已实现 |
| `scripts/` | 构建、部署、测试脚本 | `.sh`, `.bat` | `scripts/build.sh` | ✅ 已完善 |
| `tools/` | 开发工具和代码生成器 | `.sh`, `.go`, `.tmpl` | `tools/generate-module.sh` | ✅ 已完善 |
| `bin/` | 编译输出的可执行文件 | 二进制文件 | `bin/api-server` | ✅ 已实现 |

## 🚨 文件放置决策流程

### 在创建/移动文件前，问自己：

#### 1. 这是什么类型的文件？
- **项目级文档** → `docs/`
- **API技术文档** → `api/docs/`
- **配置文件** → `api/configs/`
- **业务代码** → `api/internal/`
- **工具代码** → `api/pkg/`
- **脚本文件** → `api/scripts/`
- **开发工具** → `api/tools/`

#### 2. 这个文件的作用域是什么？
- **整个项目** → 项目根目录下
- **API服务** → `api/` 目录下
- **特定模块** → 对应模块目录下

#### 3. 这个文件的访问权限？
- **对外暴露** → `pkg/`
- **内部使用** → `internal/`
- **配置相关** → `configs/`

## 🔍 快速查找指南

### 我要找...
- **项目架构文档** → `docs/ARCHITECTURE.md` ⭐ **已创建**
- **项目标准文档** → `docs/standards/README.md` ⭐ **已完善**
- **API接口文档** → `api/docs/` 或 Swagger UI
- **配置文件** → `api/configs/config.yaml`
- **启动脚本** → `api/scripts/start.sh`
- **测试脚本** → `api/scripts/tests/`
- **代码生成器** → `api/tools/generate-module.sh` ⭐ **主力工具**
- **MVS规则检查** → `api/tools/mvs-check.sh`
- **用户相关代码** → `api/internal/models/user.go`
- **历史文档备份** → `archive/docs/`

### 我要创建...
- **新的项目规范** → `docs/NEW_RULE.md`
- **新的API文档** → `api/docs/`
- **新的配置项** → `api/configs/config.yaml`
- **新的脚本** → `api/scripts/`
- **新的业务模型** → `api/internal/models/`
- **新的工具** → `api/tools/`

## ✅ 操作前检查清单

### 每次文件操作前：

1. **确认当前位置**
   ```bash
   pwd
   ```

2. **查看目录结构规范**
   ```bash
   # 查看本文档确认目标目录
   cat docs/PROJECT_DIRECTORY_STRUCTURE.md
   ```

3. **确认目标目录用途**
   - 这个文件应该放在哪个目录？
   - 为什么放在这个目录？
   - 是否符合目录用途？

4. **执行操作**
   ```bash
   # 使用正确的路径
   mkdir -p correct/path
   mv file correct/path/
   ```

5. **验证结果**
   ```bash
   ls -la target/directory
   ```

## 🎯 最佳实践

1. **遵循约定优于配置**：按照既定结构放置文件
2. **保持目录职责单一**：每个目录只存放特定类型的文件
3. **使用描述性命名**：目录和文件名要清晰表达用途
4. **定期整理**：发现不合理的文件位置及时调整
5. **文档先行**：不确定时先查看本规范文档

## 🔄 规范更新

当需要添加新的目录或调整结构时：
1. 更新本文档
2. 通知团队成员
3. 逐步迁移现有文件
4. 更新相关脚本和配置

这个目录结构规范将作为所有文件操作的参考标准，确保项目结构的清晰和一致性。
